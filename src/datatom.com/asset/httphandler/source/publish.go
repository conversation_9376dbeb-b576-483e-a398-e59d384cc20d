package source

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	common2 "datatom.com/tools/common/feedback"

	"datatom.com/asset/common"

	"datatom/gin.v1"
	"datatom/gin.v1/binding"

	"datatom.com/asset/httphandler/httpcode"
	"datatom.com/asset/logger"
	Mgm "datatom.com/asset/source"
	"datatom.com/tools/common/tools"
)

// DWSSave  保存专题发布详细信息（具体抽取字段信息等）
func PublishSave(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common2.DataServiceMD
	feed.SubModule = common2.SubdwsPublish
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")
	tokenStr := c.Request.Header["Authorization"][0]

	var info Mgm.DwsTBPublishInfo
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	//path 重名校验
	checked := Mgm.DepPath(info.AccessUrl, c.GetString("projectid"), info.ID)
	if !checked {
		err = errors.New("path exists")
		tools.CommonError(c, err)
		return
	}

	info.Publisher = c.GetString("username")
	info.PublisherId = c.GetString("userid")
	//todo  检查入参信息
	if info.UpdateField.StartValue != "" {
		if info.UpdateField.FieldType == "bigint" {
			info.UpdateField.StartValue = "#" + info.UpdateField.StartValue + "isint"
		} else {
			info.UpdateField.StartValue = "#" + info.UpdateField.StartValue
		}
	}

	defer func() {
		if info.Type == 1 {
			feed.Subject = common2.ObjApi
		} else if info.Type == 2 {
			feed.Subject = common2.ObjPush
		} else if info.Type == 3 {
			feed.Subject = common2.ObjOfflineFile
		}
		feed.SubjectName = info.ServerName
		if info.ID == "" {
			feed.OperateType = common2.OpTypeAdd
			feed.Detail = info.DwsTBName + "表发布" + info.ServerName + "服务，服务类型为" + common2.ObjMaps[feed.Subject]
		} else {
			feed.OperateType = common2.OpTypeUpdate
			feed.Detail = info.DwsTBName + "表编辑" + info.ServerName + "服务，服务类型为" + common2.ObjMaps[feed.Subject]
		}
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()

	// 若是库表推送需要创建对应任务
	if info.Type == 2 {
		var accessID string
		accessID, err = Mgm.PushAccessJobAdd(info, tokenStr, "")
		if err != nil {
			logger.Error.Println(err)
			if err.Error() == "表已存在" {
				c.JSON(httpcode.Success, gin.H{"code": httpcode.Conflict, "msg": "表已存在"})
				return
			} else if err.Error() == "表中存在数据，不允许修改" {
				c.JSON(httpcode.Success, gin.H{"code": httpcode.TabdataExist, "msg": "表中存在数据，不允许修改"})
				return
			}
			tools.CommonError(c, err)
			return
		}
		// 赋值accessID
		info.AccessID = accessID
	}
	//转化格式为字符串
	if info.Type == 3 && len(info.CondItems) != 0 {
		if fmt.Sprintf("%v", info.CondItems[0].Value1) != "" {
			info.CondItems[0].Value1 = fmt.Sprintf("%v", info.CondItems[0].Value1)
		}
		if fmt.Sprintf("%v", info.CondItems[0].Value2) != "" {
			info.CondItems[0].Value2 = fmt.Sprintf("%v", info.CondItems[0].Value2)
		}
	}
	if info.Type == 3 && len(info.ParamFilter) != 0 {
		if fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value1) != "" {
			info.ParamFilter[0].Condition.Item[0].Value1 = fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value1)
		}
		if fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value2) != "" {
			info.ParamFilter[0].Condition.Item[0].Value2 = fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value2)
		}
	}
	//保存发布信息，根据入参判断更新还是新建
	//新建专题发布信息
	if info.ID == "" {
		newId, err := Mgm.CreateDwsPublish(info)
		if err != nil {
			logger.Error.Println(err)
			tools.CommonError(c, err)
			return
		}
		res := gin.H{
			"code": httpcode.Success,
			"result": gin.H{
				"id": newId,
			},
		}
		c.JSON(httpcode.Success, res)
		return
	}
	//更新专题发布信息
	err = Mgm.UpdateDwsPublish(info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code": httpcode.Success,
	}
	c.JSON(httpcode.Success, res)
}

// PublishList   列举专题发布信息
func PublishList(c *gin.Context) {
	var info Mgm.DwsTBPublishInfo
	err := c.BindJSON(&info)
	if err != nil || info.DwsTBID == "" {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	publishRes, err := Mgm.ListDwsPublish(info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": publishRes,
	}
	c.JSON(httpcode.Success, res)
}

// PublishRecord  获取单条专题发布详细信息
func PublishRecord(c *gin.Context) {
	var info Mgm.DwsTBPublishInfo
	err := c.BindJSON(&info)
	if err != nil || info.ID == "" {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	publishRes, err := Mgm.GetOnePublish(info.ID)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": publishRes,
	}
	c.JSON(httpcode.Success, res)
}

// PublishDel   删除单条专题发布记录
func PublishDel(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common2.DataServiceMD
	feed.SubModule = common2.SubdwsPublish
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.DwsTBPublishInfo
	err := c.BindJSON(&info)
	if err != nil || info.ID == "" {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	tokenStr := c.Request.Header["Authorization"][0]
	userID := c.GetString("userid")
	//查询发布记录中的发布人和审核人，通知删除操作
	err = Mgm.SendMessage(info, userID, tokenStr)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	// 处理库表推送任务的关联数据删除
	// 获取发布详情，以便获取关联ID
	oldInfo, err := Mgm.GetOnePublish(info.ID)
	if err == nil && oldInfo.Type == 2 {
		if oldInfo.AccessID != "" {
			// 删除tb_access相关数据
			jobId, err := Mgm.PublishAccessDelete(oldInfo.AccessID)
			// 由于此项不影响主流程进行，不做返回
			if err == nil {
				// 删除tb_job 表相关数据
				_ = Mgm.DwsJobDelete(jobId)
			}
		}
	}

	defer func() {
		if oldInfo.Type == 1 {
			feed.Subject = common2.ObjApi
		} else if oldInfo.Type == 2 {
			feed.Subject = common2.ObjPush
		} else if oldInfo.Type == 3 {
			feed.Subject = common2.ObjOfflineFile
		}
		feed.SubjectName = oldInfo.ServerName
		feed.OperateType = common2.OpTypeDel
		feed.Detail = "删除" + oldInfo.DwsTBName + "表的" + oldInfo.ServerName + "服务，服务类型为" + common2.ObjMaps[feed.Subject]
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()

	//删除发布记录
	err = Mgm.DelOnePublish(info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code": httpcode.Success,
	}
	c.JSON(httpcode.Success, res)
}

// PublishQuery API查询
func PublishQuery(c *gin.Context) {
	startTime := time.Now()

	//tokenStr := c.Request.Header["Authorization"][0] //若为私钥，需要传用户的 token
	authed := c.GetBool("authed")
	fmt.Println("authed : ", authed)
	userID := c.GetString("userid")
	var err error

	action := c.Param("action")
	if action == "/" { //不允许访问根路由
		err = errors.New("access denied")
		CEaglesError(c, err)
		return
	}
	fmt.Println("action : ", action)

	var p Mgm.ParamReleaseDetail
	err = c.BindJSON(&p)
	if err != nil {
		logger.Error.Println(err)
		CJsonError(c, err)
		return
	}
	ip := c.ClientIP()
	canRun := common.API_QUERY_LIMIT.CheckAllMaxApiRun()
	if !canRun {
		LogicError(c, errors.New("访问次数已达流控上限，请稍后访问！"))
		return
	}
	//判断是否存在流量控制策略，存在则判断是否还能继续执行
	canRun, err = Mgm.CheckApiAccess(p.ServerID, ip)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		common.API_QUERY_LIMIT.DelOneApi()
		return
	}
	if !canRun {
		common.API_QUERY_LIMIT.DelOneApi()
		LogicError(c, errors.New("访问次数已达流控上限，请稍后访问！"))
		return
	}

	//用 serverid 查 publish_tb_info 表，用其中的 path 和接口中的 path 做校验
	var publishInfo Mgm.DwsTBPublishInfo
	publishInfo.ID = p.ServerID
	publishInfo, err = Mgm.GetOnePublish(publishInfo.ID)
	if err != nil {
		logger.Error.Println(err)
		CEaglesError(c, err)
		common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
		return
	}

	if publishInfo.CheckInfo.Status != Mgm.Checked { //未审核通过的服务不允许查询
		err = errors.New("unchecked service")
		UnkownError(c, err)
		common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
		return
	}

	//增加黑白名单过滤
	if publishInfo.ApiIpType != Mgm.IpNullCheck {
		checkBool, err := Mgm.CheckBlackOrWhite(c.ClientIP(), publishInfo.ID, publishInfo.ApiIpType)
		if err != nil {
			tools.CommonError(c, err)
			common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
			return
		}
		if !checkBool {
			LogicError(c, errors.New("无法访问服务"))
			common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
			return
		}
	}

	if publishInfo.Permission == Mgm.PerRestricted { //需鉴权的服务
		//校验 token 和用户
		if p.KeyType == "" || p.Key == "" {
			err = errors.New("Restricted service,need key and keytype")
			ParamError(c, err)
			common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
			return
		}
		checked, msg, err := Mgm.VerifyAPIKey(p.Key, p.KeyType, p.ServerID, userID)
		if err != nil {
			UnkownError(c, err)
			common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
			return
		}
		if msg != "" || !checked {
			errStr := fmt.Sprintf(`校验未通过:%s`, msg)
			UnkownError(c, errors.New(errStr))
			common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
			return
		}
	}

	//校验 path
	if "/"+publishInfo.AccessUrl != action {
		err = errors.New("path not match")
		CJsonError(c, err)
		common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
		return
	}

	//用 publish_tb_info 中的 dwsdbid 查 dws_tb_info 表，得到 sql
	dwsTableInfo, err := Mgm.GetOneDwsRecord(publishInfo.DwsTBID)
	if err != nil {
		logger.Error.Println(err)
		CEaglesError(c, err)
		common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
		return
	}

	resultMap, err := Mgm.PublishDetail(dwsTableInfo, publishInfo, p)
	if err != nil {
		logger.Error.Println(err)
		CEaglesError(c, err)
		common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
		return
	}

	//todo 添加 publish_record,考虑放到中间件中处理
	//设定一个参数，当查询正常时才添加记录 c.setstring
	//正常调用后删减掉内存计数值
	common.API_QUERY_LIMIT.DelApi(p.ServerID, ip)
	//新增一种数据返回格式
	if p.ReturnType == "json" {
		resultMap["code"] = httpcode.Success
		resultMap["message"] = "success"
		c.JSON(httpcode.Success, resultMap)
		go Mgm.LogRecordApi(c.ClientIP(), publishInfo, startTime, resultMap)
		return
	}
	//原数据返回格式
	resp := gin.H{
		"code":   httpcode.Success,
		"result": resultMap,
	}

	c.JSON(httpcode.Success, resp)
	go Mgm.LogRecordApi(c.ClientIP(), publishInfo, startTime, resp)
	return
}

/*
*
API共享服务 接口查询，与第一版区别：
1. 验证方式不同:authorization含义不同
2.部分配置需要从ES中查询，不再通过入参方式传入
*/
func PublishQueryV2(c *gin.Context) {
	startTime := time.Now()
	ip := c.ClientIP()
	var resultMap = make(map[string]interface{})
	var oneService Mgm.ServiceInfo
	var APIRequest, userID, userName, queryStr, offsetrange string
	var err error
	var flowCheck, checkBool bool
	var DataNum int
	var queryParamMap = make(map[string]interface{})
	err = c.ShouldBindBodyWith(&queryParamMap, binding.JSON)
	queryParamByte, _ := json.Marshal(queryParamMap)
	queryStr = string(queryParamByte)
	logger.Info.Println("----------publishqueryv2----1111-----:", queryStr)

	var p Mgm.ParamAPIQueryDetail
	err = c.ShouldBindBodyWith(&p, binding.JSON)
	if err != nil {
		UnkownError(c, err)
		return
	}

	defer func() {
		if err != nil {
			resultMap["code"] = httpcode.UnknowError
			resultMap["message"] = "failed"
			resultMap["reason"] = err.Error()
		} else {
			resultMap["code"] = httpcode.Success
			resultMap["message"] = "success"
		}
		c.JSON(httpcode.Success, resultMap)
		if flowCheck == false { //流量管控导致的无法访问，不记录
			var recordInfo Mgm.PublishRecord
			recordInfo.UserIP = c.ClientIP() //todo 考虑换成公网 IP
			recordInfo.UserID = userID
			recordInfo.UserName = userName
			recordInfo.APIRequest = APIRequest
			recordInfo.Type = 1
			recordInfo.ProjectID = oneService.ProjectID
			recordInfo.ServerID = oneService.ID
			recordInfo.Servername = oneService.Name
			recordInfo.DwsID = oneService.SourceTBID
			recordInfo.GroupName = p.GroupName
			recordInfo.SourceTBName = oneService.SourceTBName
			recordInfo.OffsetRange = offsetrange
			recordInfo.DataNum = DataNum
			go Mgm.LogRecordApiV3(recordInfo, startTime, resultMap)
			go Mgm.InventoryStatistic(p.ServerID, common.Call) //次数统计
		}
	}()

	if c.Request.Header["Authorization"] == nil || len(c.Request.Header["Authorization"]) == 0 { //防止没有鉴别码时 panic
		err = errors.New("用户鉴别码缺失")
		return
	}
	APIRequest = fmt.Sprintf(`
--request POST '%s' \
--header 'Authorization: %s' \
--header 'Content-Type: %s' \
-d '%s' `, c.Request.RequestURI, c.Request.Header["Authorization"][0], c.Request.Header["Content-Type"], queryStr)

	//查询服务自身信息
	oneService, err = Mgm.GetOneServiceById(p.ServerID)
	if err != nil {
		return
	}
	if oneService.SourceDataType != 0 {
		err = errors.New("路径访问错误")
		return
	}

	//获取请求头中的用户鉴别码，判断用户是否存在于DS中，并判断鉴别码是否已过期
	userAuth := c.Request.Header["Authorization"][0] //仅为用户鉴别码，和系统token不同
	userID, userName, err = Mgm.CheckUserExistByAuth(userAuth)
	if err != nil {
		return
	}

	action := c.Param("action")
	if action == "/" { //不允许访问根路由
		err = errors.New("access denied")
		return
	}
	fmt.Println("action : ", action)
	//初步校验 path
	if "/"+oneService.ApiServer.AccessUrl != strings.Trim(action, " ") {
		err = errors.New("path not match")
		return
	}

	if oneService.AuditStatus == 8 || oneService.AuditStatus < 4 { //未发布的类型值
		err = errors.New("服务未发布，无法访问")
		return
	}

	//请求参数需要详细校验，判断必填参数是否存在，判断是否有额外未配置参数请求
	err = Mgm.CheckApiParam(queryParamMap, &p, oneService)
	if err != nil {
		return
	}
	/**
	判断服务是否为公开服务，公开需要判断：服务是否发布，用户是否为本系统用户（身份鉴别）
	受限服务：需要增加检测：服务是否授权，授权是否已经过期（已上架的服务才有授权功能）
	*/

	//增加判断是否有【属于当前用户】，【通过】且【未过期】的申请，根据 applyid 判断
	var queryFields map[string]bool //申请里查询的字段，公开的服务为全部字段
	if oneService.Permission == 2 {
		queryFields, err = Mgm.CheckUserPermission(userID, oneService, p.ApplyID)
		if err != nil {
			return
		}
	} else {
		queryFields = make(map[string]bool)
		for _, i := range oneService.Fields {
			queryFields[i.Name] = true
		}
	}

	//增加黑白名单过滤
	if oneService.ApiServer.ApiIpType != Mgm.IpNullCheck {
		checkBool, err = Mgm.CheckBlackOrWhite(c.ClientIP(), oneService.ID, oneService.ApiServer.ApiIpType)
		if err != nil {
			flowCheck = true
			return
		}
		if !checkBool {
			flowCheck = true
			err = errors.New("因ip黑白名单限制无法访问服务")
			return
		}
	}
	//限制接口的并发访问数
	canRun := common.API_QUERY_LIMIT_V2.CheckAllMaxApiRunV2()
	if !canRun {
		flowCheck = true
		err = errors.New("访问次数已达流控上限，请稍后访问！")
		return
	}
	//判断是否开启流量控制策略，存在则判断是否还能继续执行
	if oneService.ApiServer.OpenFlow {
		canRun, err = Mgm.CheckApiAccessV2(oneService.ApiServer.FlowInfo, p.ServerID, ip, userID)
		if err != nil {
			flowCheck = true
			common.API_QUERY_LIMIT_V2.DelOneApiV2()
			return
		}
		if !canRun {
			flowCheck = true
			common.API_QUERY_LIMIT_V2.DelOneApiV2()
			err = errors.New("访问次数已达流控上限，请稍后访问！")
			return
		}
	}
	var resultTempMap = make(map[string]interface{})
	resultTempMap, err = Mgm.PublishDetailV2(oneService, p, queryFields)
	for k, v := range resultTempMap {
		resultMap[k] = v
	}
	DataNum = len(resultMap["data"].([]map[string]interface{}))

	//正常调用后删减掉内存计数值
	common.API_QUERY_LIMIT_V2.DelApiV2(p.ServerID, ip, userID)
}

func GetDecrypt(c *gin.Context) {
	var info Mgm.DecryptParam
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	decStr, err := Mgm.QueryDecrypt(info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": decStr,
	}
	c.JSON(httpcode.Success, res)
}

// 要求区分消息流与库表API访问的url,但本质上接口除查询外的判断逻辑一致，该接口复制于publishqueryv2
func PublishQueryStreamingV2(c *gin.Context) {
	startTime := time.Now()
	ip := c.ClientIP()
	var resultMap = make(map[string]interface{})
	var oneService Mgm.ServiceInfo
	var APIRequest, userID, userName, queryStr, offsetrange string
	var err error
	var flowCheck, checkBool bool
	var DataNum int
	var queryParamMap = make(map[string]interface{})
	err = c.ShouldBindBodyWith(&queryParamMap, binding.JSON)
	queryParamByte, _ := json.Marshal(queryParamMap)
	queryStr = string(queryParamByte)
	logger.Info.Println("----------publishquerystreamingv2----1111-----:", queryStr)

	var p Mgm.ParamAPIQueryDetail
	err = c.ShouldBindBodyWith(&p, binding.JSON)
	if err != nil {
		UnkownError(c, err)
		return
	}

	defer func() {
		if err != nil {
			resultMap["code"] = httpcode.UnknowError
			resultMap["message"] = "failed"
			resultMap["reason"] = err.Error()
		} else {
			resultMap["code"] = httpcode.Success
			resultMap["message"] = "success"
		}
		c.JSON(httpcode.Success, resultMap)
		if flowCheck == false { //流量管控导致的无法访问，不记录
			var recordInfo Mgm.PublishRecord
			recordInfo.UserIP = c.ClientIP() //todo 考虑换成公网 IP
			recordInfo.UserID = userID
			recordInfo.UserName = userName
			recordInfo.APIRequest = APIRequest
			recordInfo.Type = 1
			recordInfo.ProjectID = oneService.ProjectID
			recordInfo.ServerID = oneService.ID
			recordInfo.Servername = oneService.Name
			recordInfo.DwsID = oneService.SourceTBID
			recordInfo.GroupName = p.GroupName
			recordInfo.SourceTBName = oneService.SourceTBName
			recordInfo.OffsetRange = offsetrange
			recordInfo.DataNum = DataNum
			go Mgm.LogRecordApiV3(recordInfo, startTime, resultMap)
			go Mgm.InventoryStatistic(p.ServerID, common.Call) //次数统计
		}
	}()

	if c.Request.Header["Authorization"] == nil || len(c.Request.Header["Authorization"]) == 0 { //防止没有鉴别码时 panic
		err = errors.New("用户鉴别码缺失")
		return
	}
	APIRequest = fmt.Sprintf(`
--request POST '%s' \
--header 'Authorization: %s' \
--header 'Content-Type: %s' \
-d '%s' `, c.Request.RequestURI, c.Request.Header["Authorization"][0], c.Request.Header["Content-Type"], queryStr)

	//查询服务自身信息
	oneService, err = Mgm.GetOneServiceById(p.ServerID)
	if err != nil {
		return
	}
	if oneService.SourceDataType != 1 {
		err = errors.New("路径访问错误")
		return
	}

	//获取请求头中的用户鉴别码，判断用户是否存在于DS中，并判断鉴别码是否已过期
	userAuth := c.Request.Header["Authorization"][0] //仅为用户鉴别码，和系统token不同
	userID, userName, err = Mgm.CheckUserExistByAuth(userAuth)
	if err != nil {
		return
	}

	action := c.Param("action")
	if action == "/" { //不允许访问根路由
		err = errors.New("access denied")
		return
	}
	fmt.Println("action : ", action)
	//初步校验 path
	if "/"+oneService.ApiServer.AccessUrl != strings.Trim(action, " ") {
		err = errors.New("path not match")
		return
	}

	if oneService.AuditStatus == 8 || oneService.AuditStatus < 4 { //未发布的类型值
		err = errors.New("服务未发布，无法访问")
		return
	}

	//请求参数需要详细校验，判断必填参数是否存在，判断是否有额外未配置参数请求
	err = Mgm.CheckApiParam(queryParamMap, &p, oneService)
	if err != nil {
		return
	}
	/**
	判断服务是否为公开服务，公开需要判断：服务是否发布，用户是否为本系统用户（身份鉴别）
	受限服务：需要增加检测：服务是否授权，授权是否已经过期（已上架的服务才有授权功能）
	*/

	//增加判断是否有【属于当前用户】，【通过】且【未过期】的申请，根据 applyid 判断
	var queryFields map[string]bool //申请里查询的字段，公开的服务为全部字段
	if oneService.Permission == 2 {
		queryFields, err = Mgm.CheckUserPermission(userID, oneService, p.ApplyID)
		if err != nil {
			return
		}
	} else {
		queryFields = make(map[string]bool)
		for _, i := range oneService.Fields {
			queryFields[i.Name] = true
		}
	}

	//增加黑白名单过滤
	if oneService.ApiServer.ApiIpType != Mgm.IpNullCheck {
		checkBool, err = Mgm.CheckBlackOrWhite(c.ClientIP(), oneService.ID, oneService.ApiServer.ApiIpType)
		if err != nil {
			flowCheck = true
			return
		}
		if !checkBool {
			flowCheck = true
			err = errors.New("因ip黑白名单限制无法访问服务")
			return
		}
	}
	//限制接口的并发访问数
	canRun := common.API_QUERY_LIMIT_V2.CheckAllMaxApiRunV2()
	if !canRun {
		flowCheck = true
		err = errors.New("访问次数已达流控上限，请稍后访问！")
		return
	}
	//判断是否开启流量控制策略，存在则判断是否还能继续执行
	if oneService.ApiServer.OpenFlow {
		canRun, err = Mgm.CheckApiAccessV2(oneService.ApiServer.FlowInfo, p.ServerID, ip, userID)
		if err != nil {
			flowCheck = true
			common.API_QUERY_LIMIT_V2.DelOneApiV2()
			return
		}
		if !canRun {
			flowCheck = true
			common.API_QUERY_LIMIT_V2.DelOneApiV2()
			err = errors.New("访问次数已达流控上限，请稍后访问！")
			return
		}
	}
	if oneService.SourceDataType == 1 {
		//消息流的查询
		var maxbyte string
		if p.MaxBytes == 0 {
			maxbyte = fmt.Sprintf("%d", 1024*1024)
		} else {
			maxbyte = fmt.Sprintf("%d", p.MaxBytes*1024*1024)
		}
		temptopic := oneService.SourceTBName
		if oneService.SourceTBType == 2 {
			//资产表，需要增加一层查询，查询真实挂载的实时表名称
			temptopic, err = Mgm.QueryRealTbNameByInventory(oneService.SourceTBID)
			if err != nil {
				return
			}
		}
		resultMap["data"], DataNum, err = Mgm.PublishAPIQueryKafka(p.AccessIp, p.GroupName, p.Instance, temptopic, maxbyte, false, oneService.RowRule)
		var metadata []map[string]string
		for _, f := range oneService.Fields {
			tempMap := make(map[string]string)
			tempMap["column"] = f.Name
			tempMap["type"] = f.Type
			tempMap["comment"] = f.Comment
			metadata = append(metadata, tempMap)
		}
		resultMap["metadata"] = metadata
	}
	//正常调用后删减掉内存计数值
	common.API_QUERY_LIMIT_V2.DelApiV2(p.ServerID, ip, userID)
}

// 内部使用接口，用户查询API访问的内存限制数据
func GetApiLimitInfo(c *gin.Context) {
	result, _ := json.Marshal(common.API_QUERY_LIMIT_V2)
	res := gin.H{
		"code":   httpcode.Success,
		"result": string(result),
	}
	c.JSON(httpcode.Success, res)
	return
}

// GetPath 获取当前项目所有 path,用作去重
func GetPath(c *gin.Context) {
	projectID := c.GetString("projectid")

	resList := Mgm.GetPath(projectID)

	res := gin.H{
		"code":   httpcode.Success,
		"result": resList,
	}
	c.JSON(httpcode.Success, res)
	return

}

// 与GetPath区别：查询的底层ES表不同，结构不同
func GetPathV2(c *gin.Context) {
	projectID := c.GetString("projectid")

	resList, err := Mgm.GetPathV2(projectID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	if resList == nil {
		resList = make([]string, 0)
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": resList,
	}
	c.JSON(httpcode.Success, res)
	return
}

func DBList(c *gin.Context) {
	//var feed source.Feed
	//feed.Module = common.MetaCollect
	//feed.Subject = "元数据采集-列举抽取源"
	var err error

	projectID, exist := c.Get("projectid")
	if !exist {
		CAuthError(c, err)
		return
	}
	//列举抽取源
	dataSource, err := Mgm.GetDBList(projectID.(string))
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code":   http.StatusOK,
		"result": dataSource,
	}

	c.JSON(http.StatusOK, res)
}

func TBList(c *gin.Context) {
	var result interface{}
	var req Mgm.TBListReq

	err := c.BindJSON(&req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	result, err = Mgm.TBList(req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   http.StatusOK,
		"result": result,
	}
	c.JSON(http.StatusOK, res)
}
func NewTBList(c *gin.Context) {
	var result interface{}
	var req Mgm.TBListReq

	err := c.BindJSON(&req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	result, err = Mgm.NewTBList(req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   http.StatusOK,
		"result": result,
	}
	c.JSON(http.StatusOK, res)
}
func NewTbStruct(c *gin.Context) {
	//var result interface{}
	//var req Mgm.TBStructReq
	var req Mgm.TBListReq
	err := c.BindJSON(&req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	fieldInfo, partitionInfo, err := Mgm.NewTBStruct(req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	result := gin.H{
		"fieldinfo":      fieldInfo,
		"partitionfield": partitionInfo,
	}
	res := gin.H{
		"code":   http.StatusOK,
		"result": result,
	}
	c.JSON(http.StatusOK, res)
}
func TbStruct(c *gin.Context) {
	var result interface{}
	var req Mgm.TBListReq

	err := c.BindJSON(&req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	result, _, err = Mgm.NewTBStruct(req)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   http.StatusOK,
		"result": result,
	}
	c.JSON(http.StatusOK, res)
}

func TablePushSubmit(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common2.DataServiceMD
	feed.SubModule = common2.SubserviceAudit
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.DwsTBPublishInfo
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	// 获取专题消息,以供日志记录使用
	infoForLog, _ := Mgm.GetOnePublish(info.ID)
	defer func() {
		feed.Subject = common2.ObjPush
		feed.SubjectName = infoForLog.ServerName
		feed.OperateType = common2.OpTypeSubmit
		feed.Detail = "上线" + infoForLog.ServerName + "库表推送任务"
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()

	tokenStr := c.Request.Header["Authorization"][0]
	err = Mgm.TablePushSubmit(infoForLog, tokenStr)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": "上线成功",
	}
	c.JSON(httpcode.Success, res)
}

// TablePushUnSubmit  库表推送下线
func TablePushUnSubmit(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common2.DataServiceMD
	feed.SubModule = common2.SubserviceAudit
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.DownDwsJSON
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	// 获取专题消息,以供日志记录使用
	infoForLog, _ := Mgm.GetOnePublish(info.ID)

	defer func() {
		feed.Subject = common2.ObjPush
		feed.SubjectName = infoForLog.ServerName
		feed.OperateType = common2.OpTypeUnSubmit
		feed.Detail = "下线" + infoForLog.ServerName + "库表推送任务"
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()
	tokenStr := c.Request.Header["Authorization"][0]
	err = Mgm.TablePushUnSubmit(infoForLog, info, tokenStr)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": "下线成功",
	}
	c.JSON(httpcode.Success, res)
}

// 设置黑白名单(增，删)
func SetBlackOrWhite(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common2.DataServiceMD
	feed.SubModule = common2.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")
	var info Mgm.ParamBlackOrWhite
	err := c.BindJSON(&info)
	if err != nil || info.ApiServerId == "" {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	if info.Ip != "" {
		info.IpList = append(info.IpList, info.Ip)
	}
	//查询服务信息，用于日志
	oneService, _ := Mgm.GetOneServiceById(info.ApiServerId)
	defer func() {
		feed.Subject = common2.ObjApi
		feed.SubjectName = oneService.Name
		var temp string
		if info.Type == 1 {
			temp = "黑名单"
		} else if info.Type == 2 {
			temp = "白名单"
		}
		var temp2 string
		if len(info.IpList) != 0 {
			temp2 = strings.Join(info.IpList, ";")
		}
		if info.AddOrDel == 1 {
			feed.OperateType = common2.OpTypeAdd
			feed.Detail = oneService.Name + "服务新增" + temp2 + temp
		} else if info.AddOrDel == 2 {
			feed.OperateType = common2.OpTypeDel
			feed.Detail = oneService.Name + "服务删除" + temp2 + temp
		}

		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()
	err = Mgm.SetBlackOrWhite(info, oneService.Name, feed.ProjectID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   http.StatusOK,
		"result": "设置成功",
	}
	c.JSON(http.StatusOK, res)
}

// 列举黑白名单
func ListBlackOrWhite(c *gin.Context) {
	var info Mgm.ParamBlackOrWhite
	err := c.BindJSON(&info)
	if err != nil || info.ApiServerId == "" {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	result, err := Mgm.ListBlackOrWhite(info.ApiServerId)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   http.StatusOK,
		"result": result,
	}
	c.JSON(http.StatusOK, res)
}

// 批量修改API访问设置
func ApiAccessSet(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common2.DataServiceMD
	feed.SubModule = common2.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")
	var info Mgm.ApiAccessSet
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	if len(info.ServiceIds) == 0 {
		tools.CommonError(c, errors.New("入参API服务ID为空"))
		return
	}
	var nameList []string
	defer func() {
		feed.Subject = common2.ObjApi
		feed.SubjectName = strings.Join(nameList, ";")
		var temp2 string
		if len(nameList) != 0 {
			temp2 = strings.Join(nameList, ";")
		}
		var ipstr string
		var temp string
		if len(info.Iplist) == 0 {
			feed.OperateType = common2.OpTypeUpdate
			temp = "设置"
		} else {
			feed.OperateType = common2.OpTypeAdd
			ipstr = strings.Join(info.Iplist, ";")
			temp = "新增"
		}
		if info.ApiIpType == 1 {
			feed.Detail = temp2 + "服务" + temp + ipstr + "黑名单"
		} else if info.ApiIpType == 2 {
			feed.Detail = temp2 + "服务" + temp + ipstr + "白名单"
		} else if info.ApiIpType == 0 {
			feed.Detail = temp2 + "服务不启用黑白名单"
		}

		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()
	nameList, err = Mgm.SetApiAccess(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   http.StatusOK,
		"result": "更新成功",
	}
	c.JSON(http.StatusOK, res)
}
