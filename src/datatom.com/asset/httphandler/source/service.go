package source

import (
	"datatom.com/ants/httpdo/access"
	"datatom.com/asset/thread"
	metasource "datatom.com/metadata/source"
	"datatom/gin.v1"
	"errors"
	"fmt"

	"math/rand"
	"net/http"
	"strings"
	"time"

	"datatom.com/asset/common"
	"datatom.com/asset/httphandler/httpcode"
	"datatom.com/asset/logger"
	Mgm "datatom.com/asset/source"
	"datatom.com/metadata/httpdo/collect"
	"datatom.com/tools/common/code"
	feedcom "datatom.com/tools/common/feedback"
	"datatom.com/tools/common/tools"
	"datatom.com/tools/httpdo"
)

/*
*
发布 服务保存
1. API 服务
2. 库表推送服务
3. 离线下载服务
*/
func ServiceSave(c *gin.Context) {
	var feed feedcom.Feed
	feed.Module = feedcom.DataServiceMD
	feed.SubModule = feedcom.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var infoparam Mgm.ServiceInfoParam
	err := c.BindJSON(&infoparam)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	var info Mgm.ServiceInfo
	info = infoparam.ServiceInfo
	info.CreateUser = c.GetString("username")
	info.CreateUid = c.GetString("userid")
	info.ProjectID = c.GetString("projectid")
	tokenStr := c.Request.Header["Authorization"][0]

	info.GlobalVars = access.Change_Symbol(info.GlobalVars, 1)

	var inventoryID string
	var sourceTb metasource.MoneyTableBasicInfo
	if info.SourceTBType == 2 {
		inventoryID = info.SourceTBID
		sourcetbid, err := Mgm.QuerySourceTBIDByInventory(info.SourceTBID)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		//此处更新为资源表的ID用于后续表操作
		info.SourceTBID = sourcetbid
	}
	if info.SourceDataType == 0 {
		//获取源端表信息：表连接、字段、类型等信息
		sourceTb, err = Mgm.QuerySourceTBInfoByTBID(info.SourceTBID, "")
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		//检测所有字段是否有规则限制，有则拼接出ruleSQL
		err = Mgm.CheckFieldRuleSql(&info, sourceTb.DBType)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
	}
	if info.RowRule.RuleId != "" {
		ruleDetail, _ := Mgm.QueryDetailRule(info.RowRule.RuleId)
		info.RowRule.RuleName = ruleDetail.Rulename
		info.RowRule.RuleDesc = ruleDetail.Detailsrules
		if ruleDetail.Encryption == "AES" || ruleDetail.Encryption == "SM4_CBC" || ruleDetail.Encryption == "SM4_ECB" {
			if ruleDetail.EncryptKey == "" {
				//历史的规则无秘钥传递
				//此秘钥为之前AES加密内置秘钥，sm4的秘钥无法解析为正常的16为字符，所以同步使用这个秘钥
				ruleDetail.EncryptKey = "yyDmdpYS2tM-GazI"
			}
		}
	}

	//不同类型服务需要不同逻辑验证、处理
	if info.Type == 1 {
		// API共享服务入参检测
		err := Mgm.CheckApiServerParam(info)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		//消息流API接口需要创建三个消费组和实例的ID
		//在申请服务的时候增加值
		//if info.SourceDataType == 1 {
		//	for i := 1; i <= 3; i++ {
		//		var groupInfo Mgm.ApiKafkaStruct
		//		groupInfo.GroupName = fmt.Sprintf("%s%s%d", Mgm.BuildIDForKafkaGroup(info.SourceTBName), "group", i)
		//		groupInfo.Instance = fmt.Sprintf("%s%d", Mgm.BuildIDForKafkaGroup(info.SourceTBName), i)
		//		info.ApiServer.ApiKafka = append(info.ApiServer.ApiKafka, groupInfo)
		//	}
		//}
	} else if info.Type == 2 {
		//库表推送服务
		err := Mgm.CheckPushServerParam(&info)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		var accessID string
		accessID, err = Mgm.PushServerBuild(&info, sourceTb, tokenStr, "")
		if err != nil {
			logger.Error.Println(err)
			if err.Error() == "表已存在" {
				c.JSON(httpcode.Success, gin.H{"code": httpcode.Conflict, "msg": "表已存在"})
				return
			} else if err.Error() == "表中存在数据，不允许修改" {
				c.JSON(httpcode.Success, gin.H{"code": httpcode.TabdataExist, "msg": "表中存在数据，不允许修改"})
				return
			}
			tools.CommonError(c, err)
			return
		}
		Mgm.SetGlobalvars(info, accessID)
		err = Mgm.FixPrioritymode(&info)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		// 赋值accessID
		info.PushServer.AccessID = accessID
	} else if info.Type == 3 {
		//离线下载任务
		err := Mgm.CheckDownloadServerParam(&info)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
	} else {
		//任务类型错误
		tools.CommonError(c, errors.New("服务类型错误"))
		return
	}

	defer func() {
		feed.Subject = common.ServiceTypeMap[info.Type]
		feed.SubjectName = info.Name
		if info.ID == "" {
			feed.OperateType = feedcom.OpTypeAdd
			feed.Detail = info.SourceTBName + "表新建" + info.Name + "服务，服务类型为" + feedcom.ObjMaps[feed.Subject]
		} else {
			feed.OperateType = feedcom.OpTypeUpdate
			feed.Detail = info.SourceTBName + "表编辑" + info.Name + "服务，服务类型为" + feedcom.ObjMaps[feed.Subject]
		}
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()

	if info.SourceTBType == 2 {
		//资源表的ID需要更改为资产表ID
		info.SourceTBID = inventoryID
	}
	if info.ID == "" {
		//新增服务
		newId, err := Mgm.AddNewService(info)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		if info.Type == 1 && len(infoparam.Iplist) != 0 {
			//如果是API类型，需要判断infoparam是否有iplist入参
			Mgm.SetBlackOrWhiteV2(info.ApiServer.ApiIpType, newId, infoparam.Iplist, true)
		}
		if info.SourceDataType == 1 {
			var tempTaskType int
			if info.Type == 1 {
				tempTaskType = 11
			} else if info.Type == 2 {
				tempTaskType = 10
			}
			go Mgm.ProcessRealEsTbByService(info.SourceTBID, newId, tempTaskType, "add")
		}

		go Mgm.ServiceAddLineAge(newId)

		res := gin.H{
			"code": httpcode.Success,
			"result": gin.H{
				"id": newId,
			},
		}
		c.JSON(httpcode.Success, res)
		return
	}

	//更新服务信息
	err = Mgm.UpdateService(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	if info.Type == 1 && len(infoparam.Iplist) != 0 {
		//如果是API类型，需要判断infoparam是否有iplist入参
		Mgm.SetBlackOrWhiteV2(info.ApiServer.ApiIpType, info.ID, infoparam.Iplist, true)

	}
	if info.ApiServer.AddAppIp && info.ApiServer.ApiIpType == 2 {
		//更新服务的时候需要处理 仅限 白名单
		go Mgm.ProcessApiAppIplist(info.ID)
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": "更新成功",
	}
	go Mgm.ServiceAddLineAge(info.ID)

	if info.ApiServer.AddAppIp {
		//仅限于API服务，要求将历史通过的申请应用IP添加到白名单中
	}

	c.JSON(httpcode.Success, res)
}

func ServiceDetail(c *gin.Context) {
	var info Mgm.ServiceInfo
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	oneService, err := Mgm.GetOneServiceById(info.ID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	//处理目录名称
	res, err := Mgm.QueryCataNameByService(oneService)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	respons := gin.H{
		"code":   code.Success,
		"result": res,
	}
	c.JSON(code.Success, respons)
}

func ServiceStatus(c *gin.Context) {
	var feed feedcom.Feed
	feed.Module = feedcom.DataServiceMD
	feed.SubModule = feedcom.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")
	//发布xxx表的xxx服务，服务类型为xxx

	var info Mgm.OpServiceParam
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	userId := c.GetString("userid")
	userName := c.GetString("username")
	token := c.Request.Header["Authorization"][0]
	oneService, err := Mgm.ProcessServiceStatus(info, userId, userName, token)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	defer func() {
		feed.Subject = common.ServiceTypeMap[oneService.Type]
		feed.SubjectName = oneService.Name
		if info.OpType == 1 {
			feed.Detail = fmt.Sprintf("发布%s表的%s服务，服务类型为%s", oneService.SourceTBName, oneService.Name, feedcom.ObjMaps[feed.Subject])
			feed.OperateType = feedcom.OpTypePublish
		} else if info.OpType == 2 {
			feed.Detail = fmt.Sprintf("取消%s表的%s服务的发布，服务类型为%s", oneService.SourceTBName, oneService.Name, feedcom.ObjMaps[feed.Subject])
			feed.OperateType = feedcom.OpTypeCancel
		}
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()
	respons := gin.H{
		"code":   code.Success,
		"result": "执行成功",
	}
	c.JSON(code.Success, respons)
}

func ServiceList(c *gin.Context) {
	var info Mgm.QueryServiceParam
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	info.ProjectID = c.GetString("projectid")
	reslit, total, err := Mgm.ListAllServices(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	respons := gin.H{
		"code":   code.Success,
		"result": reslit,
		"total":  total,
	}
	c.JSON(code.Success, respons)
}

func ServiceListAllName(c *gin.Context) {
	var info Mgm.QueryServiceParam
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	info.ProjectID = c.GetString("projectid")
	reslit, total, err := Mgm.ListAllServicesName(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	respons := gin.H{
		"code":   code.Success,
		"result": reslit,
		"total":  total,
	}
	c.JSON(code.Success, respons)
}

// 移动服务目录
func MoveServiceCatalog(c *gin.Context) {
	var feed feedcom.Feed
	feed.Module = feedcom.DataServiceMD
	feed.SubModule = feedcom.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.MoveCatalogParam
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	defer func() {
		serviceNames, feedDetails := Mgm.ProcessMoveFeedInfo(info, c.GetString("projectid"))
		feed.OperateType = feedcom.OpTypeUpdate
		feed.Subject = feedcom.ObjService
		feed.SubjectName = strings.Join(serviceNames, "、")
		feed.Detail = strings.Join(feedDetails, ";")
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()

	err = Mgm.MoveServiceCatalog(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	respons := gin.H{
		"code":   code.Success,
		"result": "更新成功",
	}
	c.JSON(code.Success, respons)
}

// 删除服务，批量
func DeleteService(c *gin.Context) {
	var feed feedcom.Feed
	feed.Module = feedcom.DataServiceMD
	feed.SubModule = feedcom.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.DeleteServiceParam
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	feedList, err := Mgm.DeleteService(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	defer func() {
		if len(feedList) == 1 {
			strlist := strings.Split(feedList[0], "|")
			feed.Detail = fmt.Sprintf("删除%s表的%s服务，服务类型为%s", strlist[0], strlist[1], feedcom.ObjMaps[strlist[2]])
			feed.Subject = strlist[2]
			feed.SubjectName = strlist[1]
		} else {
			var detailList, nameList []string
			for _, v := range feedList {
				strlist := strings.Split(v, "|")
				nameList = append(nameList, strlist[1])
				detailList = append(detailList, fmt.Sprintf("删除%s表的%s服务，服务类型为%s", strlist[0], strlist[1], feedcom.ObjMaps[strlist[2]]))
			}
			feed.SubjectName = strings.Join(nameList, "、")
			feed.Subject = feedcom.ObjService
			feed.Detail = fmt.Sprintf("批量删除：%s", strings.Join(detailList, ";"))
		}
		feed.OperateType = feedcom.OpTypeDel
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()
	respons := gin.H{
		"code":   code.Success,
		"result": "删除成功",
	}
	c.JSON(code.Success, respons)
}

// 单条服务申请撤回
func RecallPublish(c *gin.Context) {
	var feed feedcom.Feed
	feed.Module = feedcom.DataServiceMD
	feed.SubModule = feedcom.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.OpServiceParam
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	if info.ID == "" || info.AuditRecordId == "" {
		tools.CommonError(c, errors.New("缺失入参"))
		return
	}
	oneService, _ := Mgm.GetOneServiceById(info.ID)
	username := c.GetString("username")
	err = Mgm.RecallPublish(info, username)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	defer func() {
		feed.Subject = common.ServiceTypeMap[oneService.Type]
		feed.SubjectName = oneService.Name
		feed.Detail = fmt.Sprintf("撤回%s表的%s服务申请审核", oneService.SourceTBName, oneService.Name)
		feed.OperateType = feedcom.OpTypeRecall
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()
	respons := gin.H{
		"code":   code.Success,
		"result": "执行成功",
	}
	c.JSON(code.Success, respons)
}

func APIQueryTest(c *gin.Context) {
	var info Mgm.ServiceInfo
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	var resultMap = make(map[string]interface{})

	if info.SourceDataType == 0 {
		var p Mgm.ParamAPIQueryDetail
		//测试接口近返回10条数据
		p.PageSize = 10

		queryFields := make(map[string]bool) //api 共享支持申请部分字段，此处对 parseFilterV2 做兼容
		for _, i := range info.Fields {
			queryFields[i.Name] = true
		}
		resultMap, err = Mgm.PublishDetailV2(info, p, queryFields)
	} else if info.SourceDataType == 1 {
		//消息流的查询
		maxbyte := fmt.Sprintf("%d", 1024*1024)
		temptopic := info.SourceTBName
		if info.SourceTBType == 2 {
			//资产表，需要增加一层查询，查询真实挂载的实时表名称
			temptopic, err = Mgm.QueryRealTbNameByInventory(info.SourceTBID)
			if err != nil {
				return
			}
		}
		GroupName := fmt.Sprintf("%sgrouptest", info.SourceTBName)
		Instance := fmt.Sprintf("%stest", info.SourceTBName)
		resultMap["data"], _, err = Mgm.PublishAPIQueryKafka("127.0.0.1", GroupName, Instance, temptopic, maxbyte, true, info.RowRule)
		var metadata []map[string]string
		for _, f := range info.Fields {
			tempMap := make(map[string]string)
			tempMap["column"] = f.Name
			tempMap["type"] = f.Type
			tempMap["comment"] = f.Comment
			metadata = append(metadata, tempMap)
		}
		resultMap["metadata"] = metadata
	}
	if err != nil {
		resultMap["code"] = httpcode.UnknowError
		resultMap["message"] = "failed"
		resultMap["reason"] = err.Error()
		c.JSON(httpcode.Success, resultMap)
		return
	}

	resultMap["code"] = httpcode.Success
	resultMap["message"] = "success"
	c.JSON(httpcode.Success, resultMap)
}

func ApiServerCount(c *gin.Context) {
	var info Mgm.QueryAPIMonitor
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	var resultMap = make(map[string]interface{})
	if info.IsTPAPI {
		tpApiInfo, err := Mgm.GetServiceAPIByID(info.ID)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		resultMap, err = Mgm.TPApiServerCount(tpApiInfo, info.OpenFlow)
		if err != nil {
			resultMap["code"] = httpcode.UnknowError
			resultMap["message"] = err.Error()
			c.JSON(httpcode.Success, resultMap)
			return
		}
	} else {
		oneService, err := Mgm.GetOneServiceById(info.ID)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		resultMap, err = Mgm.ApiServerCount(oneService, info.OpenFlow)
		if err != nil {
			resultMap["code"] = httpcode.UnknowError
			resultMap["message"] = err.Error()
			c.JSON(httpcode.Success, resultMap)
			return
		}
	}
	resultMap["code"] = httpcode.Success
	c.JSON(httpcode.Success, resultMap)
}

func ApiGroupCount(c *gin.Context) {
	var info Mgm.QueryAPIMonitor
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	oneService, err := Mgm.GetOneServiceById(info.ID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	resultList, err := Mgm.ApiGroupInfo(oneService)
	if err != nil {
		c.JSON(httpcode.Success, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": resultList,
	}
	c.JSON(httpcode.Success, res)
}

func ApiSetOffset(c *gin.Context) {
	var info Mgm.QueryAPIMonitor
	err := c.BindJSON(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	oneService, err := Mgm.GetOneServiceById(info.ID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	err = Mgm.ApiSetOffset(oneService, info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": "更新成功",
	}
	c.JSON(httpcode.Success, res)
}

func CountAllAPIService(c *gin.Context) {
	thread.QueryAllAPIService()
	c.JSON(httpcode.Success, "已触发统计")
}

func PushServerSubmit(c *gin.Context) {
	var feed feedcom.Feed
	feed.Module = feedcom.DataServiceMD
	feed.SubModule = feedcom.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.ServiceInfo
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	oneService, err := Mgm.GetOneServiceById(info.ID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	defer func() {
		feed.Subject = feedcom.ObjPush
		feed.SubjectName = oneService.Name
		feed.OperateType = feedcom.OpTypeSubmit
		feed.Detail = "上线" + oneService.Name + "库表推送任务"
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()

	tokenStr := c.Request.Header["Authorization"][0]
	if oneService.SourceDataType == 1 {
		err = Mgm.ServerPushXiaoxiSubmit(oneService, tokenStr)
	} else {
		err = Mgm.ServerPushSubmit(oneService, tokenStr)
	}
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": "上线成功",
	}
	c.JSON(httpcode.Success, res)
}

func PushServerUnSubmit(c *gin.Context) {
	var feed feedcom.Feed
	feed.Module = feedcom.DataServiceMD
	feed.SubModule = feedcom.SubServiceManage
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.Userid = c.GetString("userid")

	var info Mgm.DownDwsJSON
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	oneService, err := Mgm.GetOneServiceById(info.ID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	defer func() {
		feed.Subject = feedcom.ObjPush
		feed.SubjectName = oneService.Name
		feed.OperateType = feedcom.OpTypeUnSubmit
		feed.Detail = "下线" + oneService.Name + "库表推送任务"
		if err != nil {
			feed.Opsuccess = false
			feed.OpResult = err.Error()
		} else {
			feed.Opsuccess = true
		}
		SubmitLog(feed)
	}()

	tokenStr := c.Request.Header["Authorization"][0]
	if oneService.SourceDataType == 1 {
		err = Mgm.ServerPushXiaoxiUnSubmit(oneService, tokenStr)
	} else {
		err = Mgm.ServerPushUnSubmit(oneService, info, tokenStr)
	}
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   httpcode.Success,
		"result": "下线成功",
	}
	c.JSON(httpcode.Success, res)
}

func GetServiceFlow(c *gin.Context) {
	var info Mgm.ServiceInfo
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	oneService, err := Mgm.GetOneServiceById(info.ID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res, err := Mgm.GetServiceFlow(oneService)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	response := gin.H{
		"code":   httpcode.Success,
		"result": res,
	}
	c.JSON(httpcode.Success, response)
}

func GetServiceFlowV2(c *gin.Context) {
	var info Mgm.ServiceInfo
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	oneService, err := Mgm.GetOneServiceById(info.ID)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res, err := Mgm.GetServiceFlowV2(oneService)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	response := gin.H{
		"code":   httpcode.Success,
		"result": res,
	}
	c.JSON(httpcode.Success, response)
}

func FieldCompare(c *gin.Context) {
	var info Mgm.ServiceInfo
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}
	//如果ID不为空则直接查询已存储的服务信息
	if info.ID != "" {
		info, err = Mgm.GetOneServiceById(info.ID)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
	}
	//查询表数据
	resdata, err := Mgm.QueryFieldData(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	var strs []string
	var fieldsType []string
	for _, v := range resdata.Data {
		fmt.Println(v)
		str := httpdo.Strval(v[info.PushServer.UpdateField.FieldName])
		strs = append(strs, str)
		typeStr := collect.Distinguish(str)
		if typeStr != info.PushServer.UpdateField.FieldType {
			fieldsType = append(fieldsType, str)
		}
	}

	if len(fieldsType) > 10 {
		n := len(fieldsType)
		var arr = make([]int, 10)
		rand.Seed(time.Now().UnixNano())
		for i := 0; i < 9; i++ {
		LOOP:
			f := rand.Intn(n)
			for _, v := range arr {
				if f == v {
					goto LOOP
				}
			}
			arr[i] = f
		}
		var ff []string
		for _, v := range arr {
			ff = append(ff, fieldsType[v])
		}
		fieldsType = ff
	}
	if len(strs) == 0 {
		res := gin.H{
			"code": http.StatusOK,
			"result": gin.H{
				"comparison": false,
				"field":      []string{},
				"msg":        "无随机数据",
			},
		}
		c.JSON(http.StatusOK, res)
		return
	}
	fmt.Println("fieldsType")
	fmt.Println(fieldsType)

	if len(fieldsType) > 0 {

		res := gin.H{
			"code": http.StatusOK,
			"result": gin.H{
				"comparison": false,
				"field":      fieldsType,
			},
		}
		c.JSON(http.StatusOK, res)
	} else {
		res := gin.H{
			"code": http.StatusOK,
			"result": gin.H{
				"comparison": true,
				"field":      fieldsType,
			},
		}
		c.JSON(http.StatusOK, res)
	}
}

func QueryPushTbData(c *gin.Context) {
	var info Mgm.ServiceInfo
	err := c.BindJSON(&info)
	if err != nil {
		logger.Error.Println(err)
		tools.CommonError(c, err)
		return
	}

	//如果ID不为空则直接查询已存储的服务信息
	if info.ID != "" {
		info, err = Mgm.GetOneServiceById(info.ID)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
	}

	res, err := Mgm.QueryPushtbData(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	response := gin.H{
		"code":   httpcode.Success,
		"result": res,
	}
	c.JSON(httpcode.Success, response)
}
