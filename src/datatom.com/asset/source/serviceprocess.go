package mgm

import (
	common2 "datatom.com/metadata/common"
	escli "datatom.com/tools/common/escli"
	tools "datatom.com/tools/httpdo"
	"datatom/gin.v1"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"datatom.com/ants/httpdo"
	antshttpdo "datatom.com/ants/httpdo/access"
	antssource "datatom.com/ants/source"
	"datatom.com/asset/common"
	"datatom.com/asset/httpclient"
	"datatom.com/asset/logger"
	"datatom.com/auth/handlerpack/dtauth"
	"datatom.com/metadata/httpdo/collect"
	"datatom.com/metadata/httpdo/dbutil"
	"datatom.com/metadata/httpdo/devcenter"
	"datatom.com/metadata/httpdo/esutil"
	metasource "datatom.com/metadata/source"
	modelsource "datatom.com/modeling/source"
	comtools "datatom.com/tools/common/tools"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

// 第一个返回MAP：目录ID及对应名称
// 第二个返回MAP：目录ID及对应全路径名称
func ListAllServiceCatalogName(projectID string) (map[string]string, map[string]string, error) {
	var CatalogIDAndName = make(map[string]string)
	var nameLines = make(map[string]string)
	//查询tb_folder表中dataservice模块的目录
	var terms []map[string]interface{}
	ModuleTerm := gin.H{
		"terms": gin.H{
			"module": []string{"dataservice", "realmessage"},
		},
	}
	proTerm := gin.H{
		"term": gin.H{
			"projectid": projectID,
		},
	}
	terms = append(terms, ModuleTerm, proTerm)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 10000,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseLab, common.Tb_Folder, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, nil, err
	}
	relList := esutil.GetSource(bytes)
	//将所有数据扁平塞进[]CatalogList
	var serviceList = make([]*dtauth.CatalogList, len(relList))
	for index, v := range relList {
		temp := new(dtauth.CatalogList)
		temp.ID = gjson.Get(v, "id").String()
		temp.Name = gjson.Get(v, "name").String()
		temp.ParentID = gjson.Get(v, "fatherid").String()
		serviceList[index] = temp
		CatalogIDAndName[temp.ID] = gjson.Get(v, "name").String()
	}
	var head []*dtauth.CatalogList
	res := dtauth.HanderTree(head, serviceList)
	handleEmpty(res)
	dtauth.HandleCatalogName(res, nameLines, "")
	return CatalogIDAndName, nameLines, nil
}

// AddNewService 新增服务
func AddNewService(info ServiceInfo) (string, error) {
	info.SchTypeStr = tools.GetMaxExecutionUnit(info.PushServer.PushStrategy.Scheduletype, info.PushServer.PushStrategy.Scheduledetail)
	info.Createtime = time.Now().Format("2006-01-02 15:04:05")
	info.ModifyTime = time.Now().Format("2006-01-02 15:04:05")
	//查询ES表并更新ID

	//下次编辑默认展示已有表
	info.PushServer.NeedCreate = false
	newId, err := httpclient.EAdd(DataBase, ServiceESTB, info)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	//更新标签计数
	go CorrectTagCount(info.Tags)
	return newId, nil
}

// 根据ID查询服务信息
func GetOneServiceById(id string) (ServiceInfo, error) {
	var oneService ServiceInfo
	if id == "" {
		return oneService, errors.New("根据ID查询服务信息时，id为空")
	}
	res, err := esutil.SearchByID(DataBase, ServiceESTB, id)
	err = json.Unmarshal([]byte(res), &oneService)
	if err != nil {
		logger.Error.Println(err)
		return oneService, err
	}
	soures, err := esutil.SearchByID(DataBaseMeta, DbMetaTable, oneService.PushServer.DBId)
	oneService.PushServer.DBtype = gjson.Get(soures, "dbtype").String()
	fmt.Println("test1:", oneService.PushServer.DBtype)
	//需要根据表名修正元数据同步对应的ID，涉及库表推送表信息（schemaid,tableid）
	correctIDs(&oneService)
	fmt.Println("test2:", oneService.PushServer.DBtype)
	return oneService, nil
}

func correctIDs(serviceDetail *ServiceInfo) error {
	if serviceDetail.Type != 2 {
		//目前仅限于库表推送任务中涉及到的推送表相关ID信息修正
		return nil
	}
	var terms []interface{}
	dbid_term := gin.H{
		"term": gin.H{
			"dbid": serviceDetail.PushServer.DBId,
		},
	}
	terms = append(terms, dbid_term)

	if serviceDetail.PushServer.Schema != "" {
		schemaname_term := gin.H{
			"term": gin.H{
				"schemaname": serviceDetail.PushServer.Schema,
			},
		}
		terms = append(terms, schemaname_term)
	}

	tbname_term := gin.H{
		"term": gin.H{
			"tablename": serviceDetail.PushServer.TableName,
		},
	}
	terms = append(terms, tbname_term)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
	}

	resp, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbExtractDbInfo, body)
	if err != nil {
		logger.Error.Println("例举数据表失败:%s", err.Error())
		return err
	}
	var sour metasource.TBInfoHits1
	err = json.Unmarshal(resp, &sour)
	if err != nil {
		return err
	}
	for _, v := range sour.Hits1.Hits2 {
		serviceDetail.PushServer.SchemaID = v.TBInfo.SchemaID
		serviceDetail.PushServer.TableID = v.TBInfo.TableID
	}
	//取dbname
	var terms2 []interface{}
	dbid_term2 := gin.H{
		"term": gin.H{
			"id": serviceDetail.PushServer.DBId,
		},
	}
	terms2 = append(terms2, dbid_term2)

	body2 := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms2,
			},
		},
		"size": 1,
	}
	//strbyte, _ := json.Marshal(body)
	//fmt.Println("------strbyte-------", string(strbyte))
	resp2, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbExtractDb, body2)
	if err != nil {
		logger.Error.Println("例举数据表失败:%s", err.Error())
		return err
	}
	var sour2 metasource.DBInfoHits1
	err = json.Unmarshal(resp2, &sour2)
	if err != nil {
		return err
	}
	if sour.Hits1.Total == 0 {
		return nil
	}
	for _, v := range sour2.Hits1.Hits2 {
		serviceDetail.PushServer.DBName = v.DBInfoSource.DBName
		for _, s := range v.DBInfoSource.Schemas {
			if s.SchemaName == serviceDetail.PushServer.Schema {
				serviceDetail.PushServer.SchemaID = s.SchemaID
				serviceDetail.PushServer.DBName = v.DBInfoSource.DBName
			}
		}
	}
	return nil
}

// 处理服务所属目录名称和资源表、资产表所属目录名称
func QueryCataNameByService(oneService ServiceInfo) (OutParamServiceInfo, error) {
	var outParam OutParamServiceInfo
	outParam.ServiceInfo = oneService
	_, nameLines, _ := ListAllServiceCatalogName(oneService.ProjectID)
	outParam.CatalogName = strings.TrimPrefix(nameLines[oneService.CatalogID], "/")
	//查询资源表或者资产表所属目录
	//处理编目信息
	if oneService.SourceTBType == 1 {
		if oneService.SourceDataType == 0 {
			catlogMap, _ := devcenter.DealCatalog(oneService.ProjectID)
			outParam.TbCataName = strings.TrimPrefix(catlogMap[oneService.SourceCatalogId], "/")
		} else if oneService.SourceDataType == 1 {
			outParam.TbCataName = strings.TrimPrefix(nameLines[oneService.SourceCatalogId], "/")
		}
	} else if oneService.SourceTBType == 2 {
		//获取类目信息
		var sp modelsource.AssetInventory
		sp.InventoryID = oneService.SourceTBID
		sp.ProjectID = oneService.ProjectID
		ressp, _ := modelsource.InventoryDetail(sp)
		outParam.TbCataName = ressp.InventoryPath
	}

	//查询专题是否存在tb_job表数据，并查取maxtime
	jobid, maxtime, ranstatus, lastsuccesstime, _ := QueryTbJobByAccIDV2(oneService.PushServer.AccessID)
	outParam.JobId = jobid
	//如果修改时间大于最后一次运行时间，则输出存储的startvalue，否则从job表中获取maxtime
	if lastsuccesstime != "" {
		lastruntime, err1 := time.Parse("2006-01-02 15:04:05", lastsuccesstime)
		modifytime, err2 := time.Parse("2006-01-02 15:04:05", oneService.ModifyTime)
		if err1 == nil && err2 == nil && modifytime.Before(lastruntime) {
			outParam.PushServer.UpdateField.StartValue = maxtime
			outParam.LastRunTime = lastsuccesstime
		}
		if err1 == nil && err2 == nil && lastruntime.Before(modifytime) {
			ranstatus = false
		}
	}
	if outParam.PushServer.UpdateField.StartValue != "" {
		temp := outParam.PushServer.UpdateField.StartValue
		temp = strings.Replace(temp, "#", "", 1)
		temp = strings.Replace(temp, "isint", "", 1)
		outParam.PushServer.UpdateField.StartValue = temp
	}
	outParam.RanStatus = ranstatus
	FixPrioritymode(&oneService)
	outParam.PushServer.AdvancedConfig.Prioritymode = oneService.PushServer.AdvancedConfig.Prioritymode

	if outParam.PushServer.PushStrategy.UpDep {
		outParam.DepJobList, _ = httpdo.GetDepInfo(outParam.PushServer.PushStrategy.DepJobInfo)
	}

	outParam.GlobalVars = antshttpdo.Change_Symbol(outParam.GlobalVars, 0)

	return outParam, nil
}

// UpdateService 更新服务信息
func UpdateService(info ServiceInfo) error {
	info.SchTypeStr = tools.GetMaxExecutionUnit(info.PushServer.PushStrategy.Scheduletype, info.PushServer.PushStrategy.Scheduledetail)
	info.ModifyTime = time.Now().Format("2006-01-02 15:04:05")
	//下次编辑默认展示已有表
	info.PushServer.NeedCreate = false
	body := gin.H{
		"doc": info,
	}
	bodybytes, err := json.Marshal(body)
	uri := fmt.Sprintf("/%s/%s/%s/_update", DataBase, ServiceESTB, info.ID)
	resp, err := httpclient.Post(uri, string(bodybytes))
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	errorStr := gjson.Get(string(resp), "error").String()
	if errorStr != "" {
		return errors.New(errorStr)
	}
	return nil
}

func CorrectTagCount(tags []TagV1) {
	for _, tag := range tags {
		err := devcenter.EditTagCitations(tag.ID, 1)
		if err != nil {
			logger.Error.Printf("CorrectTagCount 修正标签引用失败:%s", err.Error())
		}
	}
}

// API服务信息校验
func CheckApiServerParam(info ServiceInfo) error {
	if info.ApiServer.AccessUrl == "" {
		return errors.New("API服务请求的accessurl不能为空！")
	}
	if ApiUrlExist(info.ApiServer.AccessUrl, info.ProjectID, info.ID) {
		return errors.New("API服务请求的accessurl已经存在，相同项目下不能存在相同的url！")
	}
	return nil
}

// 为了在不保存详细规则的前提下依然可以使用历史规则
func CheckFieldRuleSql(info *ServiceInfo, dbType string) error {
	for index, field := range info.Fields {
		if !field.Selected {
			continue
		}
		if field.Rule.RuleId != "" {
			//获取详细的规则信息，存放于参数中返回
			ruleDetail, err := QueryDetailRule(field.Rule.RuleId)
			info.Fields[index].Rule.RuleName = ruleDetail.Rulename
			info.Fields[index].Rule.RuleType = ruleDetail.Types
			info.Fields[index].Rule.RuleDesc = ruleDetail.Detailsrules
			info.Fields[index].Rule.RuleKey = ruleDetail.EncryptKey
			if ruleDetail.Encryption == "AES" || ruleDetail.Encryption == "SM4_CBC" || ruleDetail.Encryption == "SM4_ECB" {
				if ruleDetail.EncryptKey == "" {
					ruleDetail.EncryptKey = "yyDmdpYS2tM-GazI"
				}
			}
			if err != nil {
				return err
			}
			//根据规则加密拼写SQL
			ruleSql, err := GetSql(ruleDetail, field.Name, dbType)
			if err != nil {
				return err
			}
			if ruleSql != "" {
				info.Fields[index].Rule.RuleSql = ruleSql
			}
		}
	}
	return nil
}

// ApiUrlExist API共享 url 重名校验, true 为存在
func ApiUrlExist(path, projectID, ID string) bool {
	var terms []map[string]interface{}
	urlTerm := gin.H{
		"term": gin.H{
			"apiserver.accessurl": path,
		},
	}
	proTerm := gin.H{
		"term": gin.H{
			"projectid": projectID,
		},
	}
	terms = append(terms, urlTerm, proTerm)

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
				"must_not": gin.H{
					"term": gin.H{
						"id": ID,
					},
				},
			},
		},
	}

	resByte, _ := ItemSearch(DataBase, ServiceESTB, body)

	exists := comtools.GetTotal(resByte)
	if exists > 0 {
		return true
	} else {
		return false
	}
}

// 库表推送服务参数校验
func CheckPushServerParam(info *ServiceInfo) error {
	if info.SourceDataType == 1 {
		return nil
	}
	if info.PushServer.UpdateField.StartValue != "" {
		if info.PushServer.UpdateField.FieldType == "bigint" {
			info.PushServer.UpdateField.StartValue = "#" + info.PushServer.UpdateField.StartValue + "isint"
		} else {
			info.PushServer.UpdateField.StartValue = "#" + info.PushServer.UpdateField.StartValue
		}
	}
	return nil
}

// 离线下载服务参数校验
func CheckDownloadServerParam(info *ServiceInfo) error {
	if info.Type == 3 && len(info.CondItems) != 0 {
		if fmt.Sprintf("%v", info.CondItems[0].Value1) != "" {
			info.CondItems[0].Value1 = fmt.Sprintf("%v", info.CondItems[0].Value1)
		}
		if fmt.Sprintf("%v", info.CondItems[0].Value2) != "" {
			info.CondItems[0].Value2 = fmt.Sprintf("%v", info.CondItems[0].Value2)
		}
	}
	if info.Type == 3 && len(info.ParamFilter) != 0 {
		if fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value1) != "" {
			info.ParamFilter[0].Condition.Item[0].Value1 = fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value1)
		}
		if fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value2) != "" {
			info.ParamFilter[0].Condition.Item[0].Value2 = fmt.Sprintf("%v", info.ParamFilter[0].Condition.Item[0].Value2)
		}
	}
	return nil
}

func ProcessRealEsTbByService(realtbid string, taskid string, tasktype int, operate string) error {
	oldmetainfo := metasource.RealMessage{}
	oldmessage, err := escli.NewESClientCl.SearchByID(common.DataBaseLab, common.RealMessage, realtbid)
	if err != nil {
		logger.Error.Printf("根据id获取数据失败:%s", err.Error())
		return err
	}
	err = json.Unmarshal([]byte(oldmessage), &oldmetainfo)
	var tempTaskIds []metasource.TaskTypeInfo
	if operate == "add" {
		tempTaskIds = oldmetainfo.TaskIds
		tempTaskIds = append(tempTaskIds, metasource.TaskTypeInfo{
			Id:       taskid,
			TaskType: tasktype,
		})
	} else if operate == "delete" {
		for _, v := range oldmetainfo.TaskIds {
			if v.Id != taskid {
				tempTaskIds = append(tempTaskIds, v)
			}
		}
	}
	//编辑时换消息流了，需要更新新的消息流taskids信息
	doc := gin.H{
		"taskids": tempTaskIds,
	}
	//souceid是传参，新值
	err = escli.NewESClientCl.UpdByIDWithRefresh(common.DataBaseLab, common.RealMessage, "wait_for", realtbid, doc)
	if err != nil {
		logger.Error.Printf("更新es失败:%s", err.Error())
		return err
	}
	return nil
}

// 新建库表推送任务
func PushServerBuild(info *ServiceInfo, sourceTb metasource.MoneyTableBasicInfo, tokenStr, jobid string) (string, error) {
	if info.Type != common.PUSH_TYPE { //不为库表推送任务，直接返回
		return "", nil
	}
	//如果为编辑服务，判断服务推送的表是否需要删除
	//561 起编辑服务不删表
	if info.ID != "" {
		oldAccessid, err := pushServerModifyTb(*info)
		if err != nil {
			return "", err
		}
		//编辑服务时，处理完历史推送表，继续走下面逻辑
		info.PushServer.AccessID = oldAccessid
	}
	//拼接采集需要的结构体数据
	//创建目的端表信息：已有表不处理，新建表需要在目的端检测并建表
	AccInfo, err := buildAccInfoByPushService(info, sourceTb)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	//已知源端和目的端，构建datax脚本，以及新建对应的tb_access采集任务
	if len(info.PushServer.ServicePrimKey) == 0 {
		info.PushServer.ServicePrimKey = info.PushServer.PrimKey
	}
	accessId, err := buildPushServerDatax(AccInfo, tokenStr, info.PushServer.AccessID, jobid, info.PushServer.DataStrategy, info.PushServer.UpdateStrategy, info.PushServer.ServicePrimKey)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	return accessId, nil
}

func pushServerModifyTb(info ServiceInfo) (string, error) {
	var accessid string
	if info.ID != "" {
		//根据ID查询旧的服务信息
		oldService, err := GetOneServiceById(info.ID)
		if err != nil {
			return "", err
		}
		accessid = oldService.PushServer.AccessID
	}
	return accessid, nil
}

// 同步更新采集表
func SetGlobalvars(info ServiceInfo, accessid string) {
	if accessid == "" {
		return
	}
	fmt.Println("更新的id为:", accessid)
	fmt.Println("globalvars:", info.GlobalVars)
	d := gin.H{
		"globalvars": info.GlobalVars,
	}
	err := esutil.UpdByID(DataBaseLab, TbAccess, accessid, d)
	if err != nil {
		logger.Error.Printf("更新采集全局参数异常:%s", err.Error())
		return
	}
}

// 新建库表推送任务
func buildPushServerDatax(sp antssource.NewAcc, Authorization, oldAccessId, oldJobId string, datastrategy int, UpdateStrategy int, serkey []string) (string, error) {
	logger.Info.Println("buildPushServerDatax method accinfo is :", sp)
	if sp.Sourcedb.Dbtype != "mysql" && sp.Sourcedb.Dbtype != "stork" && sp.Sourcedb.Dbtype != "teryx" &&
		sp.Sourcedb.Dbtype != "postgres" && sp.Sourcedb.Dbtype != "oracle" && sp.Sourcedb.Dbtype != "greenplum" &&
		sp.Sourcedb.Dbtype != "SQL server" && sp.Sourcedb.Dbtype != common.DMDB {
		return "", fmt.Errorf("推送至未知的数据库类型:%s", sp.Sourcedb.Dbtype)
	}
	var err error

	var job antssource.AccInfo
	//根据旧的采集ID判断是否为更新
	if oldAccessId == "" {
		randid := Krand(4, 3)
		job.Id = "admin_user_" + sp.Dirname + "_" + sp.Name + string(randid) + ".datax"
	} else {
		job.Id = oldAccessId
		job.Jobid = oldJobId
	}
	sp.Id = job.Id
	job.Dirid = sp.Dirid     //服务的catalogid
	job.Dirname = sp.Dirname //无值
	job.Notetype = sp.Notetype
	job.Notename = sp.Name //无值
	job.Datecreated = time.Now().Format("2006-01-02 15:04:05")
	job.Updatetime = job.Datecreated
	job.ProjectID = sp.ProjectID
	job.GlobalSubmitInfo.PreID = sp.GlobalSubmitInfo.PreID
	job.Description = sp.Description //无值

	//580修改库表保存时调度策略信息同步更新至tb_access表
	job.GlobalSubmitInfo = sp.GlobalSubmitInfo
	job.SchTypeStr = sp.SchTypeStr

	// 修正抽取源信息
	var content string
	//taskType=4是实时抽取,暂无实现
	if sp.Tasktype != 10 {
		// 获取抽取源数据库信息
		db, err := fixExtractDbInfo(sp)
		if err != nil {
			fmt.Println(err)
			logger.Error.Println(err)
			return "", err
		}
		job.Extra_type = db.DBType
		content, err = buildAccPushCreateJson(&sp, &db, datastrategy, serkey)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		//第一种方法，替换'<', '>', '&'
		content = strings.Replace(content, "\\u003c", "<", -1)
		content = strings.Replace(content, "\\u003e", ">", -1)
		content = strings.Replace(content, "\\u0026", "&", -1)
		job.Content = content
	} else {
		//实时消息流
		sp.Sourcedb.SourceUrl, err = MessageCreateSourceUrl(sp.Id, sp.Extractdb.Id, sp.Extractdb.Table, sp.Sourcedb.StoreType, UpdateStrategy)
		var tempnil metasource.AccDbInfo
		sp.Sourcedb.SinkUrl = antshttpdo.CreateSinkUrl(&sp, &tempnil)
		sp.Sourcedb.SyncUrl = antshttpdo.CreateSyncUrl(&sp, &tempnil)
	}

	//=================================================================
	job.Submitted = false
	job.User = sp.User
	job.Username = sp.UserName
	job.Variables = sp.Variables
	job.Description = sp.Description
	job.Extractdb = sp.Extractdb
	job.Sourcedb = sp.Sourcedb
	job.Tasktype = sp.Tasktype
	job.RealInfo.Offline = sp.Offline
	job.TDCLoad = sp.TDCLoad //todo
	job.Retrynum = sp.Retrynum
	job.IsRetry = sp.IsRetry
	job.IsBan = sp.IsBan
	job.RetryInterval = sp.RetryInterval
	job.ProjectID = sp.ProjectID
	job.AdvancedConfig = sp.AdvancedConfig

	// results, _ := AccDbInfo(sp.Extractdb.Id)

	if sp.Notetype == antssource.RESOURCE_TYPE_DATAX {
		filename := common.UNSUBMITTED + "/" + job.Id + "/" + job.Id

		//暂时不支持hive落地
		//if sp.Sourcedb.Dbtype == common.HIVE || sp.Sourcedb.Dbtype == common.Inceptor {
		//	//暂时只支持单列
		//	if sp.TDCLoad == "落地抽取" {
		//		// 落地抽取提前生成datax json文件
		//		loadpath := common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
		//		err = GenerateFile(job.Content, loadpath, Authorization)
		//		if err != nil {
		//			logger.Error.Println(err)
		//			fmt.Println("=================err" + err.Error())
		//			return "", err
		//		}
		//	}
		//	//hive抽取json文件放置于hive文件
		//	filename = common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
		//	// fmt.Println("filename:", filename)
		//	// fmt.Println("first sp.Cmd:", sp.Cmd)
		//	if sp.Cmd == "" {
		//		sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s", filename)
		//		if sp.Extractdb.Createfield != "" {
		//			sp.Cmd = fmt.Sprintf("/usr/bin/sh %s", filename)
		//		} //增量是shell脚本
		//		//cmdNull=true//批量采集用到
		//	} else {
		//		sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s %s", sp.Cmd, filename)
		//		//PatchCmd = fmt.Sprintf(" %s %s", sp.Cmd, filename)  //批量采集用到
		//		if sp.Extractdb.Createfield != "" {
		//			sp.Cmd = fmt.Sprintf("/usr/bin/sh %s %s", sp.Cmd, filename)
		//		}
		//	}
		//	dbname := sp.Sourcedb.Odsdatabase
		//	path := ""
		//	//单例抽取
		//	if len(sp.Extractdb.Batchtables) == 0 {
		//		sp.Cmd, err = HiveSh(&sp, path, dbname, job.Id, Authorization)
		//		if err != nil {
		//			logger.Error.Println(err)
		//			return "", err
		//		}
		//		// fmt.Println("----单例抽取执行命令---：", sp.Cmd)
		//	}
		//}

		//---------------------------------------------------------------------------------------------------
		if sp.Sourcedb.Dbtype == "greenplum" || sp.Sourcedb.Dbtype == "postgres" || sp.Sourcedb.Dbtype == common.STORK ||
			sp.Sourcedb.Dbtype == common.TERYX || sp.Sourcedb.Dbtype == "mysql" || sp.Sourcedb.Dbtype == "oracle" ||
			sp.Sourcedb.Dbtype == "SQL server" || sp.Sourcedb.Dbtype == "tdsql" || sp.Sourcedb.Dbtype == common.DMDB {
			//是否有增量判断
			switch {
			case sp.Extractdb.Createfield == "":
				if sp.Tasktype == 4 {
					job.Content = content
					sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s", filename)
				} else {
					job.Content = StorkGetScript(content, sp.Extractdb.EngineID)
					sp.Cmd = fmt.Sprintf("python %s", filename)
				}

			case sp.Extractdb.Createfield != "":
				sp.Cmd = fmt.Sprintf("python %s", filename)
			}
		}
		realFileName := sp.Cmd

		//   hive或者hive的增量命令||  stork&teryx的批量命令
		if sp.Extractdb.Createfield != "" && sp.Sourcedb.Dbtype == "hive" {
			filename = realFileName
			sp.Cmd = fmt.Sprintf("/usr/bin/sh  %s", filename)
		}

		job.Cmd = sp.Cmd
		job.Extractdb.DBType = job.Extra_type
		logger.Info.Println("buildPushServerDatax accessjob info is :", job)
		err = esutil.AddWithAimID(common.DataBaseLab, common.TBAccess, job.Id, job)
		if err != nil {
			logger.Error.Printf("添加抽取记录失败:%s", err.Error())
			return "", err
		}

		//生成文件
		var filepath string
		if sp.Sourcedb.Dbtype == "hive" && sp.TDCLoad != "落地抽取" {
			//hive
			filepath = common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
		} else {
			//非hive
			filepath = common.UNSUBMITTED + "/" + job.Id + "/" + job.Id
		}
		if len(sp.Extractdb.Batchtables) == 0 || (sp.Sourcedb.Dbtype == "greenplum" || sp.Sourcedb.Dbtype == "postgres" ||
			sp.Sourcedb.Dbtype == common.STORK || sp.Sourcedb.Dbtype == common.TERYX || sp.Sourcedb.Dbtype == "mysql" ||
			sp.Sourcedb.Dbtype == "oracle" || sp.Sourcedb.Dbtype == "SQL server" || sp.Sourcedb.Dbtype == "tdsql" || sp.Sourcedb.Dbtype == common.DMDB) {
			err = GenerateFile(job.Content, filepath, Authorization)
			if err != nil {
				logger.Error.Println(err)
				return "", err
			}
		}

	}
	return job.Id, nil
}

// 库表推送json生成
func buildAccPushCreateJson(sp *antssource.NewAcc, db *metasource.AccDbInfo, datastrategy int, serkey []string) (string, error) {
	var templete string
	var core, setting string
	var err error
	setting = common.Setting
	//根据高级配置修改 setting

	core, setting = antshttpdo.DataxAdvancedConfig(common.SettingWithRecord, sp.Name, sp.AdvancedConfig)
	sp.AdvancedConfig.Core, sp.AdvancedConfig.Setting = core, setting

	readercreate := func(sp *antssource.NewAcc, db *metasource.AccDbInfo) string {
		reader := common.Reader
		reader, _ = sjson.Set(reader, "parameter.username", db.NormalConn.DBUser)
		reader, _ = sjson.Set(reader, "parameter.password", transPassword(db.NormalConn.DBPassword))
		if sp.Tasktype == 4 {
			reader, _ = sjson.Set(reader, "parameter.splitPk", "")
		}
		if sp.Tasktype != 4 && len(sp.Extractdb.Mainkey) != 0 {
			reader, _ = sjson.Set(reader, "parameter.splitPk", sp.Extractdb.Mainkey[0])
		}
		return reader
	}

	// writer
	writerjson, err := buildPushWriterJSON(sp, datastrategy, serkey)
	if err != nil {
		return "", err
	}
	if sp.AdvancedConfig.Prioritymode == 0 {
		if sp.Sourcedb.Dbtype == common.Stork || sp.Sourcedb.Dbtype == common.POSTGRES || sp.Sourcedb.Dbtype == common.GaussDBA {
			sp.AdvancedConfig.Prioritymode = 1
		} else if sp.Sourcedb.Dbtype == common.Teryx || sp.Sourcedb.Dbtype == common.GREENPLUM || sp.Sourcedb.Dbtype == common.GaussDB {
			if antshttpdo.HaveBianry(sp.Sourcedb.Fieldtype) {
				sp.AdvancedConfig.Prioritymode = 1
			} else {
				sp.AdvancedConfig.Prioritymode = 2
			}
		}
	}
	if db.DBType == "hive" || db.DBType == common.Inceptor {
		templete, err = CommonHive(sp, db, writerjson)
		if err != nil {
			fmt.Println("hive reader err:", err.Error())
			logger.Error.Println("hive reader err:", err.Error())
			return templete, err
		}
		return templete, nil
	}

	if db.DBType == common.ODPS {
		templete, err = CommonOdpsReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("odps reader err:", err.Error())
			logger.Error.Println("odps reader err:", err.Error())
			return templete, err
		}
		return templete, nil
	}

	if db.DBType == "db2" {
		templete, err = CommonDb2Reader(sp, db, writerjson)
		if err != nil {
			fmt.Println("db2 reader err:", err.Error())
			logger.Error.Println(err)
			return templete, err
		}
		return templete, nil
	}

	if db.DBType == common.DMDB {
		templete, err = CommonDaMengReader(sp, db, writerjson)
		if err != nil {
			logger.Error.Println("DMDB reader err:\"", err)
			return templete, err
		}
		return templete, nil
	}

	if db.DBType == "SQL server" {
		templete, err = CommonSQLServerReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("db2 reader err:", err.Error())
			logger.Error.Println(err)
			return templete, err
		}
		return templete, nil
	}
	if db.DBType == "oracle" {
		templete, err = CommonOracleReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("oracle reader err:", err.Error())
			logger.Error.Println(err)
			return templete, err
		}
		return templete, nil
	}

	if db.DBType == "mysql" || db.DBType == common.DRealDb {
		templete, err = CommonMysqlReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("mysql/drealdb reader err:", err.Error())
			logger.Error.Println(err)
			return templete, err
		}
		return templete, nil
	}

	if db.DBType == "gbase8s" {
		templete, err = CommonGbaseReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("mysql reader err:", err.Error())
			logger.Error.Println(err)
			return templete, err
		}
		return templete, nil
	}

	if db.DBType == "stork" || db.DBType == "teryx" || db.DBType == "gaussdb" || db.DBType == "postgres" ||
		db.DBType == "greenplum" || db.DBType == "gaussdba" || db.DBType == common.UXDB || db.DBType == "tdsql" {
		storkreader := readercreate(sp, db)
		storkreader, _ = sjson.Set(storkreader, "name", "postgresqlreader")
		if db.DBType == common.UXDB {
			storkreader, _ = sjson.Set(storkreader, "name", "rdbmsreader")
		}
		storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
		sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, `\`, `\\`)
		storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
		//-----------------------------------------------------------
		if sp.Extractdb.Createfield != "" {
			var istime, isdate bool
			var isint bool
			for i, v := range sp.Extractdb.Field {
				if v == sp.Extractdb.Createfield {
					if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[i]), "DATE") {
						isdate = true
					}
					if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[i]), "TIMESTAMP") {
						istime = true
					}
					if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[i]), "INT") {
						isint = true
					}
					break
				}
			}
			//抽取脏数据----------------------------------------------------------------
			var dirtydata string
			var nodirtytrans string
			var nodirtyreader string
			var nodirtytemp string
			//*第一次抽取不加入初始时间
			var firsttimetrans string
			//fmt.Println("是否支持脏数据:", sp.Extractdb.Supportdirtydata)
			//if sp.Extractdb.Supportdirtydata == true {
			//	switch sp.Extractdb.Timetype {
			//	case "yyyyMMdd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{8}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy-MM-dd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy/MM/dd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy-MM-dd HH:mm:ss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy/MM/dd HH:mm:ss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyyMMddHHmmss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{14}$' or \"%s\" is null `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	default:
			//		dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			//	}
			//	//脏数据
			//	if isint {
			//		dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			//	}
			//}
			var timetrans string
			//fmt.Println(sp.Extractdb.Timetype)
			//var starttime, endtime string
			//switch db.DBType {
			//case "greenplum", "teryx":
			//	if len(sp.Extractdb.Timetype) > 10 {
			//		starttime = `substring('${starttime}' from 1 for 8)||' '||substring('${starttime}' from 9 for 6)`
			//		endtime = `substring('${endtime}' from 1 for 8)||' '|| substring('${endtime}' from 9 for 6)`
			//	} else {
			//		starttime = `substring('${starttime}' from 1 for 8)`
			//		endtime = `substring('${endtime}' from 1 for 8)`
			//	}
			//default:
			//	if len(sp.Extractdb.Timetype) > 10 {
			//		starttime = `concat(left('${starttime}',8),' ',right('${starttime}',6))`
			//		endtime = `concat(left('${endtime}',8),' ',right('${endtime}',6))`
			//	} else {
			//		starttime = `left('${starttime}',8)`
			//		endtime = `left('${endtime}',8)`
			//	}
			//}

			switch sp.Extractdb.Timetype {
			case "yyyyMMdd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_ori, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_ori, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyyMMdd")
			case "yyyy-MM-dd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_hyphen, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_hyphen, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyy-MM-dd")
			case "yyyy/MM/dd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_slash, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_slash, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyy/MM/dd")
			case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_hyphen)
			case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_slash)
			case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_origin)
			case common.Incre_Autoid:
				timetrans = fmt.Sprintf(` \"%s\" > '${starttime}' `, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" is not null `, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_Autoid)
				storkreader, _ = sjson.Set(storkreader, "parameter.firstDataCollection", true)
			}

			if isdate {
				timetrans = fmt.Sprintf(` \"%s\" > cast('${starttime}' as date)  and \"%s\" <  cast('${endtime}' as date)       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(` \"%s\" < cast('${endtime}' as date) and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			}

			if istime {
				timetrans = fmt.Sprintf(` \"%s\" > cast('${starttime}' as timestamp)  and \"%s\" <  cast('${endtime}' as timestamp)       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(` \"%s\" < cast('${endtime}' as timestamp) and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			}

			//语句处理
			if isint {
				timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
				firsttimetrans = " 1=1 "
			}

			//加入脏数据处理
			timetrans = timetrans + dirtydata
			firsttimetrans = firsttimetrans + dirtydata
			nodirtytrans = timetrans

			if sp.Extractdb.Wherecontent != "" {
				sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "
			}
			sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
			storkreader, _ = sjson.Set(storkreader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
			increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + firsttimetrans
			storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", increWhereSql)
			//storkreader, _ = sjson.Set(storkreader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
			nodirtyIncreSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + nodirtytrans
			nodirtyreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", nodirtyIncreSql)
			//nodirtyreader, _ = sjson.Set(storkreader, "parameter.where", sp.Extractdb.Wherecontent+nodirtytrans)
			templete = fmt.Sprintf(`{
				%s
			   "job": {
					   %s
				   "content": [
					   {
						   "reader":
						   %s,
						   "writer":
						   %s
					   }
				   ]
			   }
			   }`, sp.AdvancedConfig.Core, setting, storkreader, writerjson)
			//-----------------------------
			//拿到不脏的template
			// nodirtytemp := strings.Replace(templete, dirtydata, "", 1)
			nodirtytemp = fmt.Sprintf(`{
				%s
				"job": {
						%s
					"content": [
						{
							"reader":
							%s,
							"writer":
							%s
						}
					]
				}
				}`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)

			//---------------------------------------------------

			templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)

			return templete, nil
		}

		//===============================================================
		//以下为没有增量字段
		templete = fmt.Sprintf(`{
			%s
		   "job": {
				   %s
			   "content": [
				   {
					   "reader":
						%s,
					   "writer":
						%s
				   }
			   ]
		   }
	   }`, sp.AdvancedConfig.Core, setting, storkreader, writerjson)

		return templete, nil
	}

	return templete, nil
}

func buildPushWriterJSON(sp *antssource.NewAcc, datastrategy int, serkey []string) (string, error) {
	//var err error
	var writer string
	var tableName string
	var quotes string

	switch sp.Sourcedb.Dbtype {
	case "mysql":
		writer = "mysqlwriter"
		if sp.DBVersion == "8.x" || strings.ToUpper(sp.DBVersion) == "V8" {
			writer = "mysql8writer"
		}

		quotes = "`"
		tableName = quotes + sp.Sourcedb.Table + quotes
	case "SQL server":
		writer = "sqlserverwriter"

		if sp.Extractdb.Createfield != "" {
			quotes = "\\\""
		} else {
			quotes = "\""
		}
		tableName = quotes + sp.Sourcedb.Schema + quotes + "." + quotes + sp.Sourcedb.Table + quotes
	case "stork", "postgres", "postgressql", "greenplum":
		writer = "postgresqlwriter"
		//
		if sp.Sourcedb.Dbtype == common.Teryx || sp.Sourcedb.Dbtype == common.GREENPLUM || sp.Sourcedb.Dbtype == common.GaussDB {
			writer = "gpdbwriter"

			if sp.AdvancedConfig.Prioritymode == 1 {
				writer = "postgresqlwriter"
			}
			//pgwrite支持二进制
			//if HaveBianry(sp.Sourcedb.Fieldtype) {
			//	storkwriter = common.Storkwriters
			//}
		} else {
			if sp.AdvancedConfig.Prioritymode == 2 {
				writer = "gpdbwriter"
			}
		}
		//
		if sp.Extractdb.Createfield != "" {
			quotes = "\\\""
		} else {
			quotes = "\""
		}
		tableName = quotes + sp.Sourcedb.Schema + quotes + "." + quotes + sp.Sourcedb.Table + quotes
		//fmt.Sprintf("*****************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
		//	sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema)
	case "oracle":
		writer = "oraclewriter"

		if sp.Extractdb.Createfield != "" {
			quotes = "\\\""
		} else {
			quotes = "\""
		}
		tableName = quotes + sp.Sourcedb.Schema + quotes + "." + quotes + sp.Sourcedb.Table + quotes
	case common.DMDB:
		writer = "rdbmswriter"

		if sp.Extractdb.Createfield != "" {
			quotes = "\\\""
		} else {
			quotes = "\""
		}
		tableName = quotes + sp.Sourcedb.Schema + quotes + "." + quotes + sp.Sourcedb.Table + quotes
	default:
		return "", errors.New("库表推送暂不支持此类型的数据库")
	}
	writerdatax := common.Writers

	writerdatax, _ = sjson.Set(writerdatax, "name", writer)
	writerdatax, _ = sjson.Set(writerdatax, "parameter.username", sp.Sourcedb.Odsuser)
	writerdatax, _ = sjson.Set(writerdatax, "parameter.password", transPassword(sp.Sourcedb.Odspassword))
	//if len(sp.Sourcedb.Field) != 0 {
	if len(sp.Sourcedb.UsedField) != 0 {
		for i := 0; i < len(sp.Sourcedb.UsedField); i++ {
			writerdatax, _ = sjson.Set(writerdatax, "parameter.column."+strconv.Itoa(i),
				quotes+strings.TrimRight(sp.Sourcedb.UsedField[i], "\r")+quotes)
		}
	}
	writerdatax, _ = sjson.Set(writerdatax, "parameter.connection.0.table.0", tableName)
	writerdatax, _ = sjson.Set(writerdatax, "parameter.connection.0.jdbcUrl", sp.Sourcedb.JdbcURL)
	if datastrategy == 1 {
		//增量字段为空，表示覆盖采集
		writerdatax, _ = sjson.Set(writerdatax, "parameter.preSql.0", "truncate table @table")
	} else if datastrategy == 2 {
		if sp.Extractdb.AccIncreStatus.DeleteSouTb {
			//与上线后修改-- delete冲突，修改为-- clean 判断
			writerdatax, _ = sjson.Set(writerdatax, "parameter.preSql.0", "-- clean ")
		} else {
			writerdatax, _ = sjson.Set(writerdatax, "parameter.preSql.0", "")
		}
	} else if datastrategy == 3 {
		writerdatax, _ = sjson.Set(writerdatax, "parameter.writeMode", "replace")
		if sp.Sourcedb.Dbtype != common.MYSQL {
			if len(serkey) != 0 {
				for i := 0; i < len(serkey); i++ {
					writerdatax, _ = sjson.Set(writerdatax, "parameter.replaceKeys."+strconv.Itoa(i),
						quotes+strings.TrimRight(serkey[i], "\r")+quotes)
				}
			}
		}
	}
	//oracle数据库的datax不支持--delete语法
	return writerdatax, nil
}

func QueryRealMessageByID(realTbId string) (metasource.RealMessage, error) {
	//此时先查看实时任务的字段信息，后续消息流新建功能完善后再统一方案
	var message metasource.RealMessage
	realres, err := esutil.SearchByID(common.DataBaseLab, common.RealMessage, realTbId)
	if err != nil {
		logger.Error.Printf("根据id获取数据失败:%s", err.Error())
		return message, err
	}
	err = json.Unmarshal([]byte(realres), &message)
	if err != nil {
		logger.Error.Printf("根据id获取数据失败:%s", err.Error())
		return message, err
	}
	return message, nil
}
func MessageCreateSourceUrl(pushName string, realTbId string, tbname string, StoreType bool, UpdateStrategy int) (antssource.SourceUrl, error) {
	var sourceUrl antssource.SourceUrl
	var sqltxt, fieldsql, sourcewith string
	realtbInfo, err := QueryRealMessageByID(realTbId)
	if err != nil {
		return sourceUrl, err
	}
	sqltxt = fmt.Sprintf("CREATE TABLE `%s`", tbname+"_source")

	//2、字段映射 --fieldinfo
	for i, v := range realtbInfo.FieldInfo {
		if i == 0 {
			fieldsql = fmt.Sprintf("`%s` %s", v.Name, v.FieldType)
			//firstField = v.FieldName
			continue
		}
		fieldsql = fmt.Sprintf("%s, `%s` %s", fieldsql, v.Name, v.FieldType)
	}
	if StoreType == true {
		//拉链表，需要增加两个字段
		fieldsql = fmt.Sprintf("%s, `%s` %s", fieldsql, "op", "string METADATA FROM 'value.op' VIRTUAL")
		fieldsql = fmt.Sprintf("%s, `%s` %s", fieldsql, "op_ts", "TIMESTAMP(3) METADATA FROM 'timestamp' VIRTUAL")
	}

	var mode, groupid string
	if UpdateStrategy == 1 {
		mode = "earliest-offset"
	} else if UpdateStrategy == 2 {
		mode = "latest-offset"
	}
	groupid = pushName

	var terms []interface{}
	term1 := gin.H{
		"term": gin.H{
			"enginetype": common.KAFKAType,
		},
	}
	terms = append(terms, term1)

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
	}
	res, _ := esutil.SearchByTerm(common.DataBasePlatform, common.Tb_Engine, body)
	kafkaurl := gjson.Get(string(res), "hits.hits.0._source.kafkaips").String()
	authmethod := gjson.Get(string(res), "hits.hits.0._source.authmethod").Int()
	switch authmethod {
	case 2:
		username := gjson.Get(string(res), "hits.hits.0._source.kafkaplaininfo.username").String()
		password := gjson.Get(string(res), "hits.hits.0._source.kafkaplaininfo.password").String()
		sourcewith = fmt.Sprintf(`with ('connector' = 'kafka','topic'='%s','properties.bootstrap.servers' = '%s', 'scan.startup.mode' = '%s', 'properties.group.id' = '%s', 'value.format' = 'canal-json','properties.security.protocol' = 'SASL_PLAINTEXT','properties.sasl.mechanism' = 'PLAIN', 'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";')`, realtbInfo.MessageName, kafkaurl, mode, groupid, username, password)
	case 3:
		sourcewith = fmt.Sprintf(`with ('connector' = 'kafka','topic'='%s','properties.bootstrap.servers' = '%s', 'scan.startup.mode' = '%s', 'properties.group.id' = '%s', 'value.format' = 'canal-json','properties.security.protocol' = 'SASL_PLAINTEXT','properties.sasl.mechanism' = 'GSSAPI','properties.sasl.kerberos.service.name' = 'kafka')`, realtbInfo.MessageName, kafkaurl, mode, groupid)

	default:
		sourcewith = fmt.Sprintf(`with ('connector' = 'kafka','topic'='%s','properties.bootstrap.servers' = '%s', 'scan.startup.mode' = '%s', 'properties.group.id' = '%s', 'value.format' = 'canal-json')`, realtbInfo.MessageName, kafkaurl, mode, groupid)
	}

	sqltxt = fmt.Sprintf(`%s (%s)%s;`, sqltxt, fieldsql, sourcewith)
	sourceUrl.Statement = sqltxt
	sourceUrl.ExecutionConfig.TaskName = pushName
	return sourceUrl, nil
}

// 拼接采集需要的抽取源、目的端信息
func buildAccInfoByPushService(pushinfo *ServiceInfo, tbinfo metasource.MoneyTableBasicInfo) (antssource.NewAcc, error) {
	var res antssource.NewAcc

	//采集策略信息
	res.Name = pushinfo.Name //将专题表名赋值给采集脚本作为名称
	res.UserName = pushinfo.CreateUser
	res.User = pushinfo.CreateUid
	res.Dirid = pushinfo.CatalogID
	res.ProjectID = pushinfo.ProjectID
	res.GlobalSubmitInfo.PreID = pushinfo.PushServer.PreJobs //前置任务需要同步
	res.Notetype = "datax"
	if pushinfo.SourceDataType == 0 {
		res.Tasktype = 6 //库表推送  todo 类型待确认
	} else if pushinfo.SourceDataType == 1 {
		res.Tasktype = 10 //库表消息流推送至数据库
	}

	res.IsRetry = pushinfo.PushServer.IsRetry
	res.Retrynum = pushinfo.PushServer.RetryNum
	res.IsBan = pushinfo.PushServer.IsBan
	res.RetryInterval = pushinfo.PushServer.RetryInterval
	//增量信息赋值
	res.Extractdb.Createfield = pushinfo.PushServer.UpdateField.FieldName //增量字段
	updateFieldType := strings.ToLower(pushinfo.PushServer.UpdateField.FieldType)
	if !strings.Contains(updateFieldType, "timestamp") && !strings.Contains(updateFieldType, "date") {
		res.Extractdb.Timetype = pushinfo.PushServer.UpdateField.FieldType //时间类型
	}
	if pushinfo.PushServer.Syncsourcedata == 2 {
		res.Extractdb.AccIncreStatus.CurrentIncredata = pushinfo.PushServer.UpdateField.StartValue //初始增量字段
	}
	res.Extractdb.AccIncreStatus.Synchistory = pushinfo.PushServer.Syncsourcedata != 2 //同步原表历史数据
	res.Extractdb.AccIncreStatus.DeleteSouTb = pushinfo.PushServer.Clearstoredata == 1 //清空存储表数据

	//拼接抽取源信息
	//处理抽取字段
	var extractFieldTypeMap = make(map[string]string)
	var sourceFieldMap = make(map[string]string)
	var extractFields []string
	var extractFieldTypes []string
	var targetFieldTypes []string
	var targetFields, targetFieldsComment []string
	var primkeyFields []int
	var usedFields []string
	//首先确认采集的源端字段和目的端字段
	for _, v := range pushinfo.PushServer.TableRelation.Edges {
		extractFields = append(extractFields, v.Source)
		usedFields = append(usedFields, v.Target)
		extractFieldTypeMap[v.Source] = v.Source
		sourceFieldMap[v.Target] = v.Target
		//后续消息流拼接落地表sinksql
		res.TableRelation.Edges = append(res.TableRelation.Edges, antssource.Edges{Source: v.Source, Target: v.Target})
	}
	primkeyMap := make(map[string]string)
	for _, v := range pushinfo.PushServer.PrimKey {
		primkeyMap[v] = v
		res.Extractdb.Mainkey = append(res.Extractdb.Mainkey, v)
	}
	//普通表：覆盖（必须要有主键）； 拉链表：追加
	if pushinfo.PushServer.DataStrategy == 1 {
		res.Sourcedb.StoreType = false
	} else if pushinfo.PushServer.DataStrategy == 2 {
		res.Sourcedb.StoreType = true
	}

	//获取采集对应字段的类型及注释信息
	for _, v := range pushinfo.PushServer.TableRelation.Nodes {
		if _, ok := extractFieldTypeMap[v.FieldName]; ok && v.Position == "left" {
			extractFieldTypes = append(extractFieldTypes, v.FieldType)
		}
		if v.Position == "right" {
			res.Sourcedb.FieldInfo = append(res.Sourcedb.FieldInfo, antssource.RecordFieldInfo{
				FieldName: v.FieldName,
				FieldType: v.FieldType,
			})
			targetFields = append(targetFields, v.FieldName)
			targetFieldTypes = append(targetFieldTypes, v.FieldType)
			targetFieldsComment = append(targetFieldsComment, v.Comment)
			if _, ok := primkeyMap[v.FieldName]; ok {
				primkeyFields = append(primkeyFields, 2)
			} else {
				primkeyFields = append(primkeyFields, 1)
			}
		}

	}
	if pushinfo.SourceDataType == 0 {
		res.Extractdb.EngineID = tbinfo.Engineid //已做长度校验，此处直接拿。后续用此参数查引擎信息
		res.Extractdb.DBType = tbinfo.DBType
		res.Extractdb.Dbname = tbinfo.Dbname
		res.Extractdb.Schema = tbinfo.Schame
		res.Extractdb.Table = tbinfo.TableName

		res.Extractdb.Field = extractFields
		res.Extractdb.Fieldtype = extractFieldTypes
		// 为了处理sql查询中的特殊字段名，hive对字段名加反引号，stork等直接加双引号
		var separator, schemaname string
		switch res.Extractdb.DBType {
		case common.STORK, common.TERYX, "postgres", "greenplum", common.DMDB:
			separator = "\""
			schemaname = res.Extractdb.Schema
		case common.HIVE, common.ODPS, common.Inceptor, common.DRealDb:
			separator = "`"
			schemaname = tbinfo.Dbname
		default:
			separator = "\""
			schemaname = res.Extractdb.Schema
		}
		// 通过用户在前端页面选择的表字段对应关系，生成sql
		res.Extractdb.SqlTxt = fmt.Sprintf("select %s%s%s from %s%s%s.%s%s%s ", separator, strings.Join(extractFields, separator+","+separator), separator,
			separator, schemaname, separator, separator, res.Extractdb.Table, separator)
		if pushinfo.PushServer.Wherecontent != "" {
			res.Extractdb.SqlTxt = res.Extractdb.SqlTxt + " where " + pushinfo.PushServer.Wherecontent
		}
		res.Extractdb.Wherecontent = pushinfo.PushServer.Wherecontent
	} else if pushinfo.SourceDataType == 1 {
		res.Extractdb.EngineID = "KAFKA"
		res.Extractdb.DBType = "kafka"
		res.Extractdb.Dbname = "kafka"
		res.Extractdb.Table = pushinfo.SourceTBName
		res.Extractdb.Id = pushinfo.SourceTBID
	}

	//根据推送的数据库ID查询对应的库表信息
	accInfo, err := collect.DBSearch(pushinfo.PushServer.DBId)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	//存储源(服务层)
	res.Sourcedb.Sourceid = accInfo.Id                     //存储数据源ID
	res.Sourcedb.Engineid = accInfo.Id                     //服务层引擎 ID
	res.Sourcedb.Odsdatabase = accInfo.NormalConn.Database //落地数据库名，是真实的库名而不是 schema。理论上 一个层的数据都在同一个库中
	res.Sourcedb.Dbtype = accInfo.DBType                   //数据类型
	res.Sourcedb.Schema = pushinfo.PushServer.Schema       //推送落地的 schema
	res.Sourcedb.SchemaID = pushinfo.PushServer.SchemaID
	res.Sourcedb.Table = pushinfo.PushServer.TableName //推送落地表名
	res.Sourcedb.FileType = res.FileType               //文件格式 text/orc  todo filetype
	res.Sourcedb.Field, res.Sourcedb.Fieldtype = targetFields, targetFieldTypes
	res.Sourcedb.FieldPrim = primkeyFields
	res.Sourcedb.Fieldcomment = targetFieldsComment
	res.Sourcedb.UsedField = usedFields
	res.Sourcedb.TableComment = pushinfo.PushServer.Describe
	res.Sourcedb.Odspassword = accInfo.NormalConn.DBPassword
	res.Sourcedb.Odsuser = accInfo.NormalConn.DBUser
	res.Sourcedb.Odsip = accInfo.NormalConn.DBIP
	res.Sourcedb.Odsport = accInfo.NormalConn.DBPort
	res.Sourcedb.OracleServer = accInfo.NormalConn.OracleServer
	res.Sourcedb.Oraclesid = accInfo.NormalConn.Oraclesid
	res.DBVersion = accInfo.DBVersion
	res.Sourcedb.JdbcURL = collect.URLNormalDB(accInfo)

	//mysql类型的url需要加上时区
	if res.Sourcedb.Dbtype == "mysql" {
		if strings.Contains(res.Sourcedb.JdbcURL, "?") {
			res.Sourcedb.JdbcURL = res.Sourcedb.JdbcURL + "&"
		} else {
			res.Sourcedb.JdbcURL = res.Sourcedb.JdbcURL + "?"
		}
		res.Sourcedb.JdbcURL = res.Sourcedb.JdbcURL + "serverTimezone=" + accInfo.TimeZoneInfo.TimeZone
	}

	//处理目的端表，判断是否需要新建表
	var findTableSql string
	switch accInfo.DBType {
	case "mysql":
		//res.Sourcedb.JdbcURL = fmt.Sprintf("*******************************************************************************", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.Database, accInfo.TimeZoneInfo.TimeZone)
		findTableSql = fmt.Sprintf("select count(*) as num  from information_schema.TABLES t where t.TABLE_SCHEMA ='%s' and t.TABLE_NAME ='%s'", accInfo.NormalConn.Database, pushinfo.PushServer.TableName)
		if pushinfo.SourceDataType == 1 && pushinfo.PushServer.DataStrategy == 2 {
			//targetFields, targetFieldTypes, targetFieldsComment,
			targetFields = append(targetFields, "op")
			targetFieldTypes = append(targetFieldTypes, "text")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
			targetFields = append(targetFields, "op_ts")
			targetFieldTypes = append(targetFieldTypes, "timestamp")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
		}
	case "oracle", common.DMDB:
		//if accInfo.NormalConn.Oraclesid != "" {
		//	res.Sourcedb.JdbcURL = fmt.Sprintf("**************************", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.Oraclesid)
		//} else {
		//	res.Sourcedb.JdbcURL = fmt.Sprintf("**************************", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.OracleServer)
		//}
		findTableSql = fmt.Sprintf(`SELECT count(*) as "num" from ALL_TABLES where owner='%s' and TABLE_NAME='%s'`, strings.ToUpper(pushinfo.PushServer.Schema), pushinfo.PushServer.TableName)
		if pushinfo.SourceDataType == 1 && pushinfo.PushServer.DataStrategy == 2 {
			//targetFields, targetFieldTypes, targetFieldsComment,
			targetFields = append(targetFields, "op")
			targetFieldTypes = append(targetFieldTypes, "varchar")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
			targetFields = append(targetFields, "op_ts")
			targetFieldTypes = append(targetFieldTypes, "timestamp")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
		}

	case "SQL server":
		//res.Sourcedb.JdbcURL = fmt.Sprintf(`**********************************`,
		//	accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort,
		//	accInfo.NormalConn.Database)
		findTableSql = fmt.Sprintf("select count(*) as num from sysobjects where id = object_id('%s.%s')", pushinfo.PushServer.Schema, pushinfo.PushServer.TableName)
		if pushinfo.SourceDataType == 1 && pushinfo.PushServer.DataStrategy == 2 {
			//targetFields, targetFieldTypes, targetFieldsComment,
			targetFields = append(targetFields, "op")
			targetFieldTypes = append(targetFieldTypes, "text")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
			targetFields = append(targetFields, "op_ts")
			targetFieldTypes = append(targetFieldTypes, "datetime")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
		}
	default:
		//case "stork", "postgres", "postgressql", "greenplum":
		//res.Sourcedb.JdbcURL = fmt.Sprintf("jdbc:postgresql://%s:%s/%s", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.Database)
		findTableSql = fmt.Sprintf("select count(*) as num from information_schema.tables where table_schema='%s' and table_name='%s'", pushinfo.PushServer.Schema, pushinfo.PushServer.TableName)
		if pushinfo.SourceDataType == 1 && pushinfo.PushServer.DataStrategy == 2 {
			//targetFields, targetFieldTypes, targetFieldsComment,
			targetFields = append(targetFields, "op")
			targetFieldTypes = append(targetFieldTypes, "text")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
			targetFields = append(targetFields, "op_ts")
			targetFieldTypes = append(targetFieldTypes, "timestamp")
			targetFieldsComment = append(targetFieldsComment, "实时拉链表需要")
		}
	}
	if pushinfo.PushServer.NeedCreate {
		sqlStr := CreateTableForPushService(pushinfo.PushServer.TableName, pushinfo.PushServer.Schema, accInfo.NormalConn.Database, accInfo.DBType, targetFields, targetFieldTypes, targetFieldsComment, pushinfo.PushServer.Describe, pushinfo.PushServer.PrimKey)
		logger.Info.Println("sqlStr:", sqlStr)
		newTBID, err := ExecCreateSql(accInfo, findTableSql, sqlStr, "selecttable", res)
		if err != nil {
			return res, err
		}
		pushinfo.PushServer.TableID = newTBID
	}

	//高级设置
	configByte, err := json.Marshal(pushinfo.PushServer.AdvancedConfig)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	err = json.Unmarshal(configByte, &res.AdvancedConfig)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}

	//580添加的调度策略
	res.GlobalSubmitInfo.ScheduleDetail = pushinfo.PushServer.PushStrategy.Scheduledetail
	res.GlobalSubmitInfo.ScheduleType = pushinfo.PushServer.PushStrategy.Scheduletype
	res.GlobalSubmitInfo.TimeoutDuration = pushinfo.PushServer.PushStrategy.TimeoutDuration
	res.GlobalSubmitInfo.TimeoutStop = pushinfo.PushServer.PushStrategy.TimeoutStop
	res.GlobalSubmitInfo.UpDep = pushinfo.PushServer.PushStrategy.UpDep
	res.GlobalSubmitInfo.JobLevel = pushinfo.PushServer.PushStrategy.JobLevel
	res.GlobalSubmitInfo.DepJobInfo = pushinfo.PushServer.PushStrategy.DepJobInfo
	res.GlobalSubmitInfo.CrossDep = pushinfo.PushServer.PushStrategy.CrossDep
	res.GlobalSubmitInfo.CrossForce = pushinfo.PushServer.PushStrategy.CrossForce
	res.GlobalSubmitInfo.EffectStart = pushinfo.PushServer.PushStrategy.EffectStart
	res.GlobalSubmitInfo.EffectEnd = pushinfo.PushServer.PushStrategy.EffectEnd
	res.GlobalSubmitInfo.IsStartNow = pushinfo.PushServer.PushStrategy.IsStartNow
	res.GlobalSubmitInfo.FirstStartTime = pushinfo.PushServer.PushStrategy.FirstStartTime
	res.GlobalSubmitInfo.AssignNode = pushinfo.PushServer.AssignNode
	res.GlobalSubmitInfo.ExecTimes = pushinfo.PushServer.PushStrategy.ExectTimes
	res.SchTypeStr = tools.GetMaxExecutionUnit(pushinfo.PushServer.PushStrategy.Scheduletype, pushinfo.PushServer.PushStrategy.Scheduledetail)
	if pushinfo.PushServer.DataStrategy == 1 {
		res.GlobalSubmitInfo.ExtractOnlineStrategy = 0
	} else if pushinfo.PushServer.DataStrategy == 2 && pushinfo.PushServer.UpdateStrategy == 1 {
		res.GlobalSubmitInfo.ExtractOnlineStrategy = 1
	} else if pushinfo.PushServer.UpdateStrategy == 2 {
		res.GlobalSubmitInfo.ExtractOnlineStrategy = 2
	} else if pushinfo.PushServer.DataStrategy == 3 && pushinfo.PushServer.UpdateStrategy == 1 {
		res.GlobalSubmitInfo.ExtractOnlineStrategy = 1
	} else if pushinfo.PushServer.DataStrategy == 3 && pushinfo.PushServer.UpdateStrategy == 2 {
		res.GlobalSubmitInfo.ExtractOnlineStrategy = 2
	}

	return res, nil
}

// 为库表推送建表
func CreateTableForPushService(tbname, schema, database, dbType string, field, fieldType, fieldComment []string, tbComment string, primkeys []string) string {

	var createSqlPrefix string
	var separator string
	var lenSuffix string
	var fieldStr string
	var decimalSuffix = ""
	var charSet = ""
	var comment string
	var primkeySQL string
	var primkeylist string
	switch dbType {
	case "mysql":
		createSqlPrefix = fmt.Sprintf("create table if not exists `%v` (", tbname)
		separator = "`"
		lenSuffix = "(2000)"
		decimalSuffix = "(65,30)"
		charSet = "DEFAULT CHARACTER SET = utf8mb4"
		comment = fmt.Sprintf(` comment='%s'`, tbComment)
		nullTime := " null"

		for i, v := range field {
			var fieldType = fieldType[i]
			if strings.Contains(fieldType, "varchar") {
				fieldType += lenSuffix
			}
			if fieldType == "decimal" {
				fieldType += decimalSuffix
			}
			if strings.Contains(fieldType, "time") {
				fieldType += nullTime
			}
			if fieldComment[i] != "" {
				fieldStr += separator + v + separator + "   " + fieldType + "   COMMENT '" + fieldComment[i] + "' ,"
			} else {
				fieldStr += separator + v + separator + "   " + fieldType + ","
			}
		}
		if len(primkeys) != 0 {
			for _, v := range primkeys {
				primkeylist += separator + v + separator + ","
			}
			primkeylist = strings.TrimSuffix(primkeylist, `,`)
			pks := fmt.Sprintf("%s_PK", tbname)
			primkeySQL = fmt.Sprintf(" Constraint pk_%s Primary Key(%s)", pks, primkeylist)
			return createSqlPrefix + fieldStr + primkeySQL + ")" + charSet + comment
		}
		fieldStr = strings.TrimSuffix(fieldStr, `,`)
		return createSqlPrefix + fieldStr + ")" + charSet + comment
	case "SQL server":
		createSql := fmt.Sprintf(`create table "%s"."%s" (`, schema, tbname)
		lenSuffix := "(2000)"
		decimalSuffix := "(38,10)"
		separator := `"`
		//表描述 EXEC sp_addextendedproperty 'MS_Description', 'This is my table comment.', 'SCHEMA', 'dbo', 'TABLE', 'comtest', NULL, NULL
		comment = fmt.Sprintf(`EXEC %s.sys.sp_addextendedproperty 'MS_Description', '%s', 'SCHEMA', '%s', 'TABLE', '%s', NULL, NULL ;`, database, tbComment, schema, tbname)
		var commentStr string
		for i, v := range field {
			var fieldType = fieldType[i]
			if strings.Contains(fieldType, "varchar") || strings.Contains(fieldType, "binary") {
				fieldType += lenSuffix
			}
			if fieldType == "decimal" {
				fieldType += decimalSuffix
			}

			createSql += separator + v + separator + "  " + fieldType + ","
			//字段注释
			//EXEC testing.sys.sp_addextendedproperty 'MS_Description', '注释信息', 'user', 'schema名', 'table', '表名称', 'column', '列名称';
			if fieldComment[i] != "" {
				commentStr += fmt.Sprintf("EXEC %s.sys.sp_addextendedproperty 'MS_Description','%s','schema','%s','table','%s','column','%s';",
					database, fieldComment[i], schema, tbname, v)
			}
		}
		if len(primkeys) != 0 {
			for _, v := range primkeys {
				primkeylist += separator + v + separator + ","
			}
			primkeylist = strings.TrimSuffix(primkeylist, `,`)
			pks := fmt.Sprintf("%s_PK", tbname)
			primkeySQL = fmt.Sprintf(" Constraint pk_%s Primary Key(%s)", pks, primkeylist)
			return createSql + primkeySQL + ");" + " " + commentStr + comment
		}
		createSql = strings.TrimSuffix(createSql, `,`)
		return createSql + ");" + " " + commentStr + comment
	case "oracle":
		createSqlPrefix = fmt.Sprintf("create table \"%v\".\"%v\" (", schema, tbname)
		separator = "\""
		lenSuffix = "(4000)"
		if len(primkeys) != 0 {
			for _, v := range primkeys {
				primkeylist += separator + v + separator + ","
			}
			primkeylist = strings.TrimSuffix(primkeylist, `,`)
			pks := fmt.Sprintf("%s_PK", tbname)
			primkeySQL = fmt.Sprintf(" Constraint pk_%s Primary Key(%s)", pks, primkeylist)
		}
	case "stork", "postgres", "postgresql", "greenplum", "teryx", "uxdb":
		createSqlPrefix = fmt.Sprintf("create table if not exists\"%v\".\"%v\" (", schema, tbname)
		separator = "\""
		if len(primkeys) != 0 {
			for _, v := range primkeys {
				primkeylist += separator + v + separator + ","
			}
			primkeylist = strings.TrimSuffix(primkeylist, `,`)
			pks := fmt.Sprintf("%s_PK", tbname)
			primkeySQL = fmt.Sprintf(" Constraint pk_%s Primary Key(%s)", pks, primkeylist)
		}
	case common.DMDB:
		createSqlPrefix = fmt.Sprintf(`create table "%v"."%v" (`, schema, tbname)
		separator = `"`
		comment = fmt.Sprintf(` comment on table "%s"."%s" is '%s'`, schema, tbname, tbComment)
		var commentstr string
		for i, v := range field {
			var fieldType = fieldType[i]
			if strings.Contains(fieldType, "varchar") {
				fieldType += lenSuffix
			}
			if i != len(field)-1 {
				fieldStr += separator + v + separator + "   " + fieldType + ","
			} else {
				fieldStr += separator + v + separator + "   " + fieldType
			}
			if fieldComment[i] != "" {
				commentstr = commentstr + fmt.Sprintf(`COMMENT ON COLUMN "%s"."%s"."%s" IS '%s'; `, schema, tbname, field[i], fieldComment[i])
			}
		}
		if len(primkeys) != 0 {
			for _, v := range primkeys {
				primkeylist += separator + v + separator + ","
			}
			primkeylist = strings.TrimSuffix(primkeylist, `,`)
			primkeySQL = fmt.Sprintf(" CLUSTER PRIMARY KEY(%s)", primkeylist)
			distributionSql := fmt.Sprintf(`%s(%s)`, "DISTRIBUTED BY ", primkeylist)
			return createSqlPrefix + fieldStr + "," + primkeySQL + ") " + distributionSql + "; " + commentstr + comment
		}
		return createSqlPrefix + fieldStr + ") ; " + commentstr + comment
	}
	comment = fmt.Sprintf(` comment on table "%s"."%s" is '%s'`, schema, tbname, tbComment)
	var commentstr string
	for i, v := range field {
		var fieldType = fieldType[i]
		if strings.Contains(fieldType, "varchar") {
			fieldType += lenSuffix
		}
		if i != len(field)-1 {
			fieldStr += separator + v + separator + "   " + fieldType + ","
		} else {
			fieldStr += separator + v + separator + "   " + fieldType
		}
		if fieldComment[i] != "" {
			commentstr = commentstr + fmt.Sprintf(`COMMENT ON COLUMN "%s"."%s"."%s" IS '%s'; `, schema, tbname, field[i], fieldComment[i])
		}
	}
	if len(primkeys) != 0 {
		return createSqlPrefix + fieldStr + "," + primkeySQL + ") ; " + commentstr + comment
	}
	return createSqlPrefix + fieldStr + ") ; " + commentstr + comment
}

func QuerySourceTBIDByInventory(id string) (string, error) {
	var r MoneySaveInfo
	strInfo, err := esutil.SearchByID(common.DataBaseModelAsset, common.TbAssetInventory, id)
	if err != nil {
		fmt.Printf("查询资产编目失败:%s", err.Error())
		return "", err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		fmt.Printf("解析表数据失败:%s", err.Error())
		return "", err
	}
	if r.AttachInfo.AttachType == "realmessage" {
		return r.AttachInfo.AttachRealMessageInfo.Id, nil
	}
	return r.AttachInfo.AttachTableInfo.Id, nil
}

func QueryRealTbNameByInventory(id string) (string, error) {
	var r MoneySaveInfo
	strInfo, err := esutil.SearchByID(common.DataBaseModelAsset, common.TbAssetInventory, id)
	if err != nil {
		fmt.Printf("查询资产编目失败:%s", err.Error())
		return "", err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		fmt.Printf("解析表数据失败:%s", err.Error())
		return "", err
	}
	return r.AttachInfo.AttachRealMessageInfo.MessageName, nil
}

// 根据表名词查询表信息及对应的库连接信息
func QuerySourceTBInfoByTBName(tbname string) (metasource.MoneyTableBasicInfo, error) {
	var tbbasic metasource.MoneyTableBasicInfo
	if tbname == "" {
		return tbbasic, errors.New("根据源表名词查询信息失败，表名称为空")
	}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"tbname": tbname,
					},
				},
			},
		},
		"size": 3,
	}

	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
	if err != nil {
		fmt.Printf("列举表失败:%s", err.Error())
		return tbbasic, err
	}
	relList, total := esutil.GetSourceAndTotal(bytes)
	if total == 0 {
		logger.Error.Println("根据表名查询tbmetatable未查询到：", tbname)
		return tbbasic, errors.New("根据表名未查询到源表信息")
	}
	if total > 1 {
		logger.Error.Println("根据表名查询tbmetatable出现多张表：", relList)
		return tbbasic, errors.New("根据表名查询出现多张表，无法定位")
	}
	err = json.Unmarshal([]byte(relList[0]), &tbbasic)
	if err != nil {
		fmt.Printf("解析表数据失败:%s", err.Error())
		return tbbasic, err
	}
	return tbbasic, nil
}

// 根据表ID查询表信息及对应的库连接信息
func QuerySourceTBInfoByTBID(tbId, tbName string) (metasource.MoneyTableBasicInfo, error) {
	var tbbasic metasource.MoneyTableBasicInfo
	if tbId == "" {
		return tbbasic, errors.New("根据表ID查询信息失败，表ID为空")
	}
	strinfo, err := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaTable, tbId)
	if err != nil {
		logger.Error.Println(tbId, "根据表ID查询tbmetatable失败，未获取到信息，继续根据表名查询:", tbName)
		fmt.Printf("列举表失败:%s", err.Error())
		return QuerySourceTBInfoByTBName(tbName)
		//return tbbasic, err
	}
	err = json.Unmarshal([]byte(strinfo), &tbbasic)
	if err != nil {
		fmt.Printf("解析表数据失败:%s", err.Error())
		return tbbasic, err
	}
	return tbbasic, nil
}

// 处理服务提交发布请求
func ProcessServiceStatus(info OpServiceParam, userId, userName, token string) (ServiceInfo, error) {
	//获取服务信息
	oneService, err := GetOneServiceById(info.ID)
	if err != nil {
		logger.Error.Println("ProcessServiceStatus oneService error:", err)
		return oneService, err
	}

	//构建一条审核记录
	var auditRecord AuditRecord
	auditRecord.ID = oneService.AuditRecordId
	auditRecord.UserID = userId
	auditRecord.UserName = userName
	auditRecord.ProjectID = oneService.ProjectID
	auditRecord.ObjectId = info.ID
	auditRecord.ObjectName = oneService.Name
	auditRecord.SourceType = common.FWFB_Type

	//发布操作类型：1发布，2取消发布，3上架，4下架
	if info.OpType == 1 {
		auditRecord.Type = common.FWFB_ON
	} else if info.OpType == 2 {
		auditRecord.Type = common.FWFB_OFF
	} else {
		return oneService, errors.New("目前暂未识别该操作类型")
	}
	//查询审核配置
	var auto bool
	var auditId string
	//需要新增一条工单记录
	auto, auditId, err = AddOneAuditRecord(auditRecord, token)
	if err != nil {
		logger.Error.Println("ProcessServiceOn ProcessAuditByService err: ", err)
		return oneService, err
	}

	if auto {
		//如果是自动审核，则直接修改审核通过
		if info.OpType == 1 {
			oneService.AuditStatus = 4
		} else if info.OpType == 2 {
			oneService.AuditStatus = 8
		}
	} else {
		//如果不是自动审核，
		if info.OpType == 1 {
			oneService.AuditStatus = 1
		} else if info.OpType == 2 {
			oneService.AuditStatus = 5
		}
	}
	doc := gin.H{
		"auditstatus":   oneService.AuditStatus,
		"auditrecordid": auditId,
	}
	err = esutil.UpdByID(DataBase, ServiceESTB, info.ID, doc)
	if err != nil {
		logger.Error.Printf("已通知审核，但更新服务状态失败:%s", err.Error())
		return oneService, err
	}
	return oneService, nil
}

// 审核中且还未经过任何一个环节审核的服务支持撤回
func RecallPublish(info OpServiceParam, username string) error {
	//根据审核工单ID查询
	oneRecord, err := GetOneAuditRecordByID(info.AuditRecordId)
	if err != nil {
		return err
	}
	//可以撤回:当前审核层级为1，且审核状态为0，没有开始审核
	if oneRecord.Status == 0 {
		//修改服务状态,发布类型的申请被驳回，为待发布状态，取消发布的申请被驳回，为已发布状态
		var status int
		if oneRecord.Type == common.FWFB_ON {
			status = 0
		} else if oneRecord.Type == common.FWFB_OFF {
			status = 4
		}
		doc := gin.H{
			"auditstatus":   status,
			"auditrecordid": "",
		}
		err = esutil.UpdByID(DataBase, ServiceESTB, info.ID, doc)
		if err != nil {
			logger.Error.Printf("撤回申请，但更新服务状态失败:%s", err.Error())
			return err
		}
		//撤回的工单不进行删除，需要更新工单信息
		doc = gin.H{
			"status":      4,
			"recalluser":  username,
			"currentdeep": 0,
			"updatetime":  time.Now().Format("2006-01-02 15:04:05"),
		}
		err = esutil.UpdByID(DataBase, AuditRecordESTB, info.AuditRecordId, doc)
		if err != nil {
			logger.Error.Printf("撤回申请，但更新工单状态失败:%s", err.Error())
			return err
		}
	} else {
		return errors.New("处于审核进程中,不可以撤回")
	}

	return nil
}

// 列举、查询所有符合条件的服务
func ListAllServices(info QueryServiceParam) ([]OutParamServiceInfo, int, error) {

	body := GetServicesQueryString(info)
	str, _ := json.Marshal(body)
	logger.Info.Println("ListAllServices query body:", string(str))
	bytes, err := esutil.SearchByTerm(DataBase, ServiceESTB, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, 0, err
	}
	_, nameLines, _ := ListAllServiceCatalogName(info.ProjectID)
	relList, total := esutil.GetSourceAndTotal(bytes)
	var result []OutParamServiceInfo

	//前置任务依赖判断
	////tb_access 前置查询
	//dataxMap, _ := httpdo.GetDataxMap(info.ProjectID)

	//tb_job 前置查询
	jobMap, _ := httpdo.GetJobMap(info.ProjectID)

	////推送任务
	//pushMap, _ := httpdo.GetServiceMap(info.ProjectID)
	//
	////指标任务
	//metricsMap, _ := httpdo.GetMetricsMap(info.ProjectID)

	//素材库相关
	bmap, _ := httpdo.GetBelongMap(info.ProjectID)
	logger.Info.Println("拥有素材库的任务:", bmap)

	for _, value := range relList {
		var dependTask []string
		var oneService OutParamServiceInfo
		_ = json.Unmarshal([]byte(value), &oneService)
		oneService.CatalogName = strings.TrimPrefix(nameLines[oneService.CatalogID], "/")

		if oneService.Type == 2 {
			jobid, _, ranStatus, _, _ := QueryTbJobByAccIDV2(oneService.PushServer.AccessID)
			oneService.PushServer.RanStatus = ranStatus
			oneService.JobId = jobid
		}
		//需要判断服务是否上架
		flag := QueryServerPutOn(oneService.ID)
		if flag {
			oneService.AuditStatus = 12 //标识服务上架
		}

		//前置判断

		for jobKey, preID := range jobMap {
			if preID == oneService.ID || preID == oneService.JobId {
				dependTask = append(dependTask, jobKey.Name)
			}
		}

		oneService.DependTsak = strings.Join(dependTask, "、")
		oneService.BelongMar = httpdo.GetBelongMar(bmap, oneService.JobId)

		result = append(result, oneService)
	}

	return result, total, nil
}

// 需要判断服务是否在资产处存在上架情况
func QueryServerPutOn(serverid string) bool {
	var IsPutOn bool
	var querySource = []string{"inventoryid"}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"putinfo.putserverinfo.serverid": serverid,
					},
				},
			},
		},
		"size":    1,
		"_source": querySource,
	}

	bytes, err := esutil.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Println(err)
		return IsPutOn
	}
	_, total := esutil.GetSourceAndTotal(bytes)
	if total != 0 {
		IsPutOn = true
	}
	return IsPutOn
}

func GetServicesQueryString(info QueryServiceParam) interface{} {
	//page<0,搜索全部
	if info.Page <= 0 {
		info.Page = 1
	}
	if info.Perpage <= 0 {
		info.Perpage = 100000
	}
	//bool must
	var boolm []map[string]interface{}
	if info.Match != "" {
		match := gin.H{
			"query_string": gin.H{
				"query":            "*" + info.Match + "*",
				"fields":           []string{"name", "sourcetbname"},
				"default_operator": "or",
			},
		}
		boolm = append(boolm, match)
	}
	if info.Name != "" {
		var tName = gin.H{
			"query_string": gin.H{
				"query":            "*" + info.Name + "*",
				"fields":           []string{"name"},
				"default_operator": "or",
			},
		}
		boolm = append(boolm, tName)
	}
	if info.CatalogID != "" {
		dirids, err := QueryMetaFolderID(info.CatalogID)
		if err != nil {
			logger.Error.Printf("循环获取数据源子文件夹失败:%s", err.Error())
		}
		var tCata = gin.H{
			"terms": gin.H{
				"catalogid": dirids,
			},
		}
		boolm = append(boolm, tCata)
	}
	if info.ProjectID != "" {
		var tPro = gin.H{
			"term": gin.H{
				"projectid": info.ProjectID,
			},
		}
		boolm = append(boolm, tPro)
	}
	if len(info.IDList) != 0 {
		var tID = gin.H{
			"terms": gin.H{
				"id": info.IDList,
			},
		}
		boolm = append(boolm, tID)
	}
	if len(info.Type) != 0 {
		var tType = gin.H{
			"terms": gin.H{
				"type": info.Type,
			},
		}
		boolm = append(boolm, tType)
	}
	if len(info.SourceTBType) != 0 {
		var tSourceType = gin.H{
			"terms": gin.H{
				"sourcetbtype": info.SourceTBType,
			},
		}
		boolm = append(boolm, tSourceType)
	}
	if len(info.ApiIpTypes) != 0 {
		var tApiipType = gin.H{
			"terms": gin.H{
				"apiserver.apiiptype": info.ApiIpTypes,
			},
		}
		boolm = append(boolm, tApiipType)
		//要求仅查询API服务
		var tType = gin.H{
			"term": gin.H{
				"type": 1,
			},
		}
		boolm = append(boolm, tType)
	}

	if info.SourceTBID != "" {
		var tSourceTBID = gin.H{
			"term": gin.H{
				"sourcetbid": info.SourceTBID,
			},
		}
		boolm = append(boolm, tSourceTBID)
	}
	if len(info.SourceTBIDS) != 0 {
		var tSourceTBIDs = gin.H{
			"terms": gin.H{
				"sourcetbid": info.SourceTBIDS,
			},
		}
		boolm = append(boolm, tSourceTBIDs)
	}
	if len(info.AuditStatus) != 0 {
		var tStatus = gin.H{
			"terms": gin.H{
				"auditstatus": info.AuditStatus,
			},
		}
		boolm = append(boolm, tStatus)
	}
	if info.SourceTBName != "" {
		var tTbName = gin.H{
			"query_string": gin.H{
				"query":            "*" + info.SourceTBName + "*",
				"fields":           []string{"sourcetbname"},
				"default_operator": "or",
			},
		}
		boolm = append(boolm, tTbName)
	}

	if info.SortField == "" {
		info.SortField = "modifytime"
	}
	if info.Sort == "" {
		info.Sort = "desc"
	}

	var notcheckTerms []map[string]interface{}
	if len(info.ExcludeAudit) != 0 {
		notcheckTerm1 := gin.H{
			"terms": gin.H{
				"auditstatus": info.ExcludeAudit,
			},
		}
		notcheckTerms = append(notcheckTerms, notcheckTerm1)
	}

	//由于需要兼容历史数据查询，历史的服务无sourcedatatype,无法直接用0值查询
	if len(info.SourceDataType) == 1 {
		if info.SourceDataType[0] == 0 {
			notcheckTerm2 := gin.H{
				"term": gin.H{
					"sourcedatatype": 1,
				},
			}
			notcheckTerms = append(notcheckTerms, notcheckTerm2)
		} else if info.SourceDataType[0] == 1 {
			var tSourceDataType = gin.H{
				"term": gin.H{
					"sourcedatatype": 1,
				},
			}
			boolm = append(boolm, tSourceDataType)
		}
	}

	//final query
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must":     boolm,
				"must_not": notcheckTerms,
			},
		},
		"sort": gin.H{
			info.SortField: gin.H{
				"order": info.Sort,
			},
		},
		"_source": []string{"sourcetbid", "id", "name", "sourcetbname", "sourcetbtype", "sourcedatatype", "catalogid", "type", "auditstatus", "createtime", "modifytime", "auditrecordid", "publishtime", "createuser", "tags", "describe", "apiserver.apiiptype", "pushserver.onlinestatus", "pushserver.accessid", "downloadserver.filestatuss", "downloadserver.reason", "permission"},
		"size":    info.Perpage,
		"from":    (info.Page - 1) * info.Perpage,
	}
	return body
}

func QueryMetaFolderID(id string) ([]string, error) {
	ids := []string{id}
	delid := map[string]string{id: id}
	for len(delid) != 0 {
		tempid := delid
		delid = map[string]string{}
		for _, v := range tempid {
			body := gin.H{
				"_source": []string{"id", "fatherid"},
				"query": gin.H{
					"bool": gin.H{
						"must": gin.H{
							"term": gin.H{
								"fatherid": v,
							},
						},
					},
				},
				"size": 10000,
			}
			bytes, err := esutil.SearchByTerm(common.DataBaseLab, common.Tb_Folder, body)
			if err != nil {
				logger.Error.Printf("根据prentid查询文件夹信息失败:%s", err.Error())
				return ids, err
			}
			sources := esutil.GetSource(bytes)
			for _, s := range sources {
				dirid := gjson.Get(s, "id").String()
				delid[dirid] = dirid
				ids = append(ids, dirid)
			}
		}
	}
	return ids, nil
}

// 列举所有服务的名称和目录ID
func ListAllServicesName(info QueryServiceParam) ([]map[string]string, int, error) {

	body := GetAllServiceNamesQueryString(info)
	bytes, err := esutil.SearchByTerm(DataBase, ServiceESTB, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, 0, err
	}
	relList, total := esutil.GetSourceAndTotal(bytes)
	var result []map[string]string
	for _, value := range relList {
		id := gjson.Get(value, "id").String()
		if id == "" {
			continue
		}
		var tempMap = make(map[string]string)
		tempMap["id"] = id
		tempMap["name"] = gjson.Get(value, "name").String()
		tempMap["catalogid"] = gjson.Get(value, "catalogid").String()
		result = append(result, tempMap)
	}
	return result, total, nil
}

func GetAllServiceNamesQueryString(info QueryServiceParam) interface{} {
	//bool must
	var boolm []map[string]interface{}

	if info.ProjectID != "" {
		var tPro = gin.H{
			"term": gin.H{
				"projectid": info.ProjectID,
			},
		}
		boolm = append(boolm, tPro)
	}
	if info.CatalogID != "" {
		var tCata = gin.H{
			"term": gin.H{
				"catalogid": info.CatalogID,
			},
		}
		boolm = append(boolm, tCata)
	}
	if len(info.IDList) != 0 {
		var tidlist = gin.H{
			"terms": gin.H{
				"id": info.IDList,
			},
		}
		boolm = append(boolm, tidlist)
	}

	//final query
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": boolm,
			},
		},
		"_source": []string{"id", "name", "catalogid"},
		"size":    100000,
	}
	return body
}

func ProcessMoveFeedInfo(info MoveCatalogParam, projectId string) ([]string, []string) {
	var tempQuery QueryServiceParam
	tempQuery.IDList = info.IdList
	reslit, _, err := ListAllServicesName(tempQuery)
	if err != nil {
		logger.Error.Println("ProcessMoveFeedInfo ListAllServicesName err:", err)
		return nil, nil
	}
	catalogIdAndNameMap, _, _ := ListAllServiceCatalogName(projectId)
	var serviceNames, feedDetails []string
	for _, v := range reslit {
		serviceNames = append(serviceNames, v["name"])
		feedDetails = append(feedDetails, fmt.Sprintf("%s服务从%s文件夹移动至%s文件夹", v["name"], catalogIdAndNameMap[v["catalogid"]], catalogIdAndNameMap[info.CatalogID]))
	}
	return serviceNames, feedDetails
}

func MoveServiceCatalog(param MoveCatalogParam) error {
	if param.CatalogID == "" {
		return errors.New("移动目录ID为空")
	}
	if len(param.IdList) == 0 {
		return errors.New("需要移动目录的服务IDlist为空")
	}
	var body = gin.H{
		"script": gin.H{
			"inline": fmt.Sprintf("ctx._source.catalogid = '%s'", param.CatalogID),
		},
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"terms": gin.H{
						"id": param.IdList,
					},
				},
			},
		},
	}
	byteBody, _ := json.Marshal(body)
	fmt.Println(string(byteBody))
	uri := fmt.Sprintf("/%s/%s/_update_by_query", DataBase, ServiceESTB)
	_, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

// 待发布、已驳回状态的服务支持删除。审核中、已发布的服务不支持删除
func DeleteService(param DeleteServiceParam) ([]string, error) {
	var ids []string
	var feedNeedList []string //删除xxx表的xxx服务，服务类型为xxx
	if param.Id != "" {
		ids = append(ids, param.Id)
	}
	if len(param.IdList) != 0 {
		ids = append(ids, param.IdList...)
	}
	if len(ids) == 0 {
		return nil, errors.New("删除服务入参中缺失服务ID")
	}
	var info QueryServiceParam
	info.IDList = ids
	body := GetServicesQueryString(info)
	bytes, err := esutil.SearchByTerm(DataBase, ServiceESTB, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	relList, _ := esutil.GetSourceAndTotal(bytes)
	//存储所有能删除的服务
	var deleteIds []string
	var accessIds []string
	//存储不能被删除的服务ID
	var errIds []string
	for _, value := range relList {
		id := gjson.Get(value, "id").String()
		auditStatus := gjson.Get(value, "auditstatus").Int()
		name := gjson.Get(value, "name").String()
		sourcetbid := gjson.Get(value, "sourcetbid").String()
		sourcetbName := gjson.Get(value, "sourcetbname").String()
		sourcedatatype := gjson.Get(value, "sourcedatatype").Int()
		typetemp := gjson.Get(value, "type").Int()
		accessId := gjson.Get(value, "pushserver.accessid").String()
		if auditStatus == 0 || auditStatus == 3 || auditStatus == 8 {
			deleteIds = append(deleteIds, id)
			if accessId != "" {
				accessIds = append(accessIds, accessId)
			}
		} else {
			errIds = append(errIds, id)
		}
		if sourcedatatype == 1 {
			var tempTaskType int
			if typetemp == 1 {
				tempTaskType = 11
			} else if typetemp == 2 {
				tempTaskType = 10
			}
			go ProcessRealEsTbByService(sourcetbid, id, tempTaskType, "delete")
		}
		feedNeedList = append(feedNeedList, fmt.Sprintf("%s|%s|%s", sourcetbName, name, common.ServiceTypeMap[int(typetemp)]))
	}
	if len(deleteIds) != 0 {
		for _, accessid := range accessIds {
			//需要联动删除tb_asset表和tb_job表（库表推送任务）
			// 删除tb_access相关数据
			jobId, _ := PublishAccessDelete(accessid)
			logger.Warn.Println("根据数据服务库表推送任务，删除对应tb_access表相关数据：", accessid, jobId)
			// 由于此项不影响主流程进行，不做返回
			if jobId != "" {
				// 删除tb_job 表相关数据
				_ = DwsJobDelete(jobId)
			}
		}
		err = esutil.DelByID(DataBase, ServiceESTB, deleteIds)
		if err != nil {
			logger.Error.Println(err)
			return feedNeedList, err
		}
	}
	if len(errIds) != 0 {
		return feedNeedList, errors.New("该部分服务处于审核或发布状态，不能删除。ids:" + strings.Join(errIds, ","))
	}
	return feedNeedList, nil
}

func ApiServerCount(info ServiceInfo, openflow bool) (map[string]interface{}, error) {
	//开启了服务监控，需要查询配置是否超出限制
	var count int
	var querytime1, querytime2 string
	if openflow {
		querytime1, querytime2 = GetLimitTimeV2(info.ApiServer.FlowInfo.CountType, info.ApiServer.FlowInfo.Interval, info.ApiServer.FlowInfo.IntervalUnit)
	}
	count, _ = QueryLogCountByApiId(info.ID, querytime1, querytime2)
	countByUserName, err := APICountByField(info.ID, "username", querytime1, querytime2)
	if err != nil {
		logger.Error.Println(err)
	}
	countByUserIp, err := APICountByField(info.ID, "userip", querytime1, querytime2)
	if err != nil {
		logger.Error.Println(err)
	}
	var resultMap = make(map[string]interface{})
	resultMap["openflow"] = info.ApiServer.OpenFlow
	resultMap["apiiptype"] = info.ApiServer.ApiIpType
	resultMap["flowinfo"] = info.ApiServer.FlowInfo
	resultMap["countbyuser"] = countByUserName
	resultMap["countbyip"] = countByUserIp
	resultMap["allcount"] = count
	return resultMap, nil
}

func APICountByField(serverid, field, querytime, endtime string) (map[string]int, error) {
	var terms []map[string]interface{}
	apiTerm := gin.H{
		"term": gin.H{
			"serverid": serverid,
		},
	}
	terms = append(terms, apiTerm)
	if querytime != "" {
		timeRange := gin.H{
			"range": gin.H{
				"starttime": gin.H{
					"gte": querytime,
				},
			},
		}
		terms = append(terms, timeRange)
	}
	if endtime != "" {
		timeRange := gin.H{
			"range": gin.H{
				"starttime": gin.H{
					"lte": endtime,
				},
			},
		}
		terms = append(terms, timeRange)
	}
	// 获取总量聚合
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 0,
		"aggs": gin.H{
			"all_subject": gin.H{
				"terms": gin.H{
					"field": field,
					"size":  10000,
				},
			},
		},
	}
	bytes, err := esutil.SearchByTerm(DataBase, PublishRecordTB, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}

	var aggs Aggregations
	_ = json.Unmarshal(bytes, &aggs)

	var allCountMap = make(map[string]int)
	bucketArr := aggs.Aggregation.Subjects.BucketArr
	for _, value := range bucketArr {
		allCountMap[value.Key] = value.Doc_count
	}
	return allCountMap, nil
}

func APICountByField_COPY(serverid, field, querytime, endtime string) (map[string]int, error) {
	var terms []map[string]interface{}
	apiTerm := gin.H{
		"term": gin.H{
			"serverid": serverid,
		},
	}
	terms = append(terms, apiTerm)
	if querytime != "" {
		timeRange := gin.H{
			"range": gin.H{
				"starttime": gin.H{
					"gte": querytime,
					"lte": endtime,
				},
			},
		}
		terms = append(terms, timeRange)
	}
	// 获取总量聚合
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 0,
		"aggs": gin.H{
			"all_subject": gin.H{
				"terms": gin.H{
					"field": field,
					"size":  10000,
				},
			},
		},
	}
	bytes, err := esutil.SearchByTerm(DataBase, PublishRecordTB, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}

	var aggs Aggregations
	_ = json.Unmarshal(bytes, &aggs)

	var allCountMap = make(map[string]int)
	bucketArr := aggs.Aggregation.Subjects.BucketArr
	for _, value := range bucketArr {
		allCountMap[value.Key] = value.Doc_count
	}
	return allCountMap, nil
}

func TPApiServerCount(info modelsource.ServiceApiInfo, openflow bool) (map[string]interface{}, error) {
	//开启了服务监控，需要查询配置是否超出限制
	var count int
	var querytime1, querytime2 string
	if openflow {
		querytime1, querytime2 = GetLimitTimeV2(info.ServiceSafe.FlowInfo.CountType, info.ServiceSafe.FlowInfo.Interval, info.ServiceSafe.FlowInfo.IntervalUnit)
	}
	count, _ = QueryLogCountByApiId(info.ID, querytime1, querytime2)
	countByUserName, err := APICountByField(info.ID, "username", querytime1, querytime2)
	if err != nil {
		logger.Error.Println(err)
	}
	countByUserIp, err := APICountByField(info.ID, "userip", querytime1, querytime2)
	if err != nil {
		logger.Error.Println(err)
	}
	var resultMap = make(map[string]interface{})
	resultMap["openflow"] = info.ServiceSafe.OpenFlow
	resultMap["apiiptype"] = info.ServiceSafe.ApiIpType
	resultMap["flowinfo"] = info.ServiceSafe.FlowInfo
	resultMap["countbyuser"] = countByUserName
	resultMap["countbyip"] = countByUserIp
	resultMap["allcount"] = count
	return resultMap, nil
}

func ServerPushXiaoxiSubmit(info ServiceInfo, tokenStr string) error {

	querybody := fmt.Sprintf(`{"id":"%s"}`,
		info.PushServer.AccessID)
	response, err := httpclient.PostAuthorization(httpclient.EaglesIP, common.AntsPort, "/danastudio/ants/access/realtime/submittasks", querybody, tokenStr)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	respStr := string(response)
	//fmt.Println("===========打印下采集上线接口返回信息：=====", respStr)
	respCode := gjson.Get(respStr, "code").Int()
	respErr := gjson.Get(respStr, "error").String()
	respRes := gjson.Get(respStr, "result").String()
	respMsg := gjson.Get(respStr, "msg").String()
	if respCode != 200 {
		return errors.New(respErr + " " + respRes + " " + respMsg)
	}
	//上线成功后修改service_info表中该库表推送任务的上线状态信息
	bodys := gin.H{
		"doc": gin.H{
			"pushserver": gin.H{
				"onlinestatus": true,
			},
		},
	}
	err = httpclient.EUpdate(DataBase, ServiceESTB, bodys, info.ID)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

func ServerPushSubmit(info ServiceInfo, tokenStr string) error {
	submitted, err := QuerySubmittedByAccessId(info.PushServer.AccessID)
	if err != nil {
		return err
	}
	if submitted {
		return errors.New("库表推送已上线，不能重复操作")
	}

	// 配合调用采集接口上线，根据落地策略和更新方式获取：0：覆盖存储数据，1：全量追加数据，2：增量追加数据
	var extractStrategy int
	// 库表推送使用使用自身的覆盖/追加策略，全量/增量追加策略
	if info.PushServer.DataStrategy == 1 {
		extractStrategy = 0
	} else if info.PushServer.DataStrategy == 2 && info.PushServer.UpdateStrategy == 1 {
		extractStrategy = 1
	} else if info.PushServer.UpdateStrategy == 2 {
		extractStrategy = 2
	} else if info.PushServer.DataStrategy == 3 && info.PushServer.UpdateStrategy == 1 {
		extractStrategy = 1
	} else if info.PushServer.DataStrategy == 3 && info.PushServer.UpdateStrategy == 2 {
		extractStrategy = 2
	}

	//复用采集上线接口
	//times := -1
	//if info.PushServer.PushStrategy.Scheduletype != 5 {
	//	exectimes := info.PushServer.PushStrategy.ExectTimes
	//	times = int(*exectimes) //重试次数
	//}
	//转化前置任务数组为string格式
	//tempPrejob, _ := json.Marshal(info.PushServer.PreJobs)
	querybody := fmt.Sprintf(`{"ids":["%s"],"extractonlinestrategy":%d}`,
		info.PushServer.AccessID, extractStrategy)
	response, err := httpclient.PostAuthorization(httpclient.EaglesIP, common.AntsPort, "/danastudio/ants/access/submit", querybody, tokenStr)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	respStr := string(response)
	//fmt.Println("===========打印下采集上线接口返回信息：=====", respStr)
	respCode := gjson.Get(respStr, "code").Int()
	respErr := gjson.Get(respStr, "error").String()
	respRes := gjson.Get(respStr, "result").String()
	respMsg := gjson.Get(respStr, "msg").String()
	if respCode != 200 {
		return errors.New(respErr + " " + respRes + " " + respMsg)
	}
	//上线成功后修改service_info表中该库表推送任务的上线状态信息
	bodys := gin.H{
		"doc": gin.H{
			"pushserver": gin.H{
				"onlinestatus": true,
			},
		},
	}
	err = httpclient.EUpdate(DataBase, ServiceESTB, bodys, info.ID)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

// 调用dodox/job/del删除接口
func ServerPushUnSubmit(info ServiceInfo, downJson DownDwsJSON, tokenStr string) error {
	submitted, err := QuerySubmittedByAccessId(info.PushServer.AccessID)
	if err != nil {
		return err
	}
	if !submitted {
		return errors.New("库表推送已下线，不能重复操作")
	}
	jobiId, err := QueryJobIdByAccessId(info.PushServer.AccessID)
	if err != nil {
		return err
	}
	//调用dodox/stop接口停止任务（针对如果有正在运行的任务）
	querybody := fmt.Sprintf(`{"id":"%s","stopNow":%d,"force":%v}`, jobiId, downJson.StopNow, downJson.Force)
	response, err := httpclient.PostAuthorization(httpclient.EaglesIP, common.DodoJobMPort, "/dodox/job/del", querybody, tokenStr)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	respStr := string(response)
	fmt.Println("===========打印下dodox任务下线接口返回信息：=====", respStr)
	respCode := gjson.Get(respStr, "code").Int()
	respMsg := gjson.Get(respStr, "msg").String()
	if respCode != 200 {
		return errors.New(respMsg)
	}
	bodys := gin.H{
		"doc": gin.H{
			"pushserver": gin.H{
				"onlinestatus": false,
			},
		},
	}
	err = httpclient.EUpdate(DataBase, ServiceESTB, bodys, info.ID)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

func ServerPushXiaoxiUnSubmit(info ServiceInfo, tokenStr string) error {

	querybody := fmt.Sprintf(`{"ids":["%s"],"operatetype":2,"tasktype":[10]}`, info.PushServer.AccessID)
	response, err := httpclient.PostAuthorization(httpclient.EaglesIP, common.AntsPort, "/danastudio/ants/access/realtime/operate", querybody, tokenStr)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	respStr := string(response)
	respCode := gjson.Get(respStr, "code").Int()
	if respCode != 200 {
		return errors.New(respStr)
	}
	bodys := gin.H{
		"doc": gin.H{
			"pushserver": gin.H{
				"onlinestatus": false,
			},
		},
	}
	err = httpclient.EUpdate(DataBase, ServiceESTB, bodys, info.ID)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

// 查询服务的整体流程：从新建到各项审核
func GetServiceFlow(info ServiceInfo) ([]ApplyFlow, error) {
	//获取配置
	confManager, confMap, _ := GetAuditConf("", info.ProjectID, 1)
	var res []ApplyFlow
	//服务新建：
	var newService = ApplyFlow{
		Title:    "服务新建",
		Date:     info.Createtime,
		Describe: []FlowDesc{{Key: "操作人", Value: info.CreateUser}},
	}
	newService.Status = -1
	res = append(res, newService)
	//查询工单
	var boolm []map[string]interface{}
	boolm = append(boolm, gin.H{"term": gin.H{"objectid": info.ID}})
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": boolm,
			},
		},
		"sort": gin.H{"createtime": gin.H{"order": "asc"}},
		"size": UnlimitSize,
	}
	resByte, err := esutil.SearchByTerm(DataBase, AuditRecordESTB, body)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	relList, _ := esutil.GetSourceAndTotal(resByte)

	for _, value := range relList {
		var oneRecord AuditRecord
		_ = json.Unmarshal([]byte(value), &oneRecord)

		var title string
		switch oneRecord.Type {
		case common.FWFB_ON: //资产使用-申请
			title = "服务发布"
		case common.FWFB_OFF: //资产使用-续期
			title = "服务取消发布"
		}
		//1. 申请标头
		var headItem ApplyFlow
		headItem = ApplyFlow{
			Title:    title,
			Date:     oneRecord.CreateTime,
			Describe: getFlowBasic(oneRecord.UserName, oneRecord.ID),
		}
		headItem.Status = -1

		res = append(res, headItem)

		//2.1 审核流程-自动申请
		if oneRecord.AutoCheck {
			//自动申请
			var autoCheckItem ApplyFlow
			autoCheckItem.Title = title + "审核"
			autoCheckItem.Describe = getFlowAudit("系统自动审核", "通过")
			autoCheckItem.Date = oneRecord.CreateTime
			autoCheckItem.Status = 1
			res = append(res, autoCheckItem)
			//自动审核的没有审核流程
			continue
		}
		//2.2 审核流程
		var auditItem ApplyFlow
		auditItem.Date = oneRecord.UpdateTime
		auditItem.Status = oneRecord.Status
		auditItem.Title = title + "审核"
		if oneRecord.Status == 4 { //已撤回
			auditItem.Title = title + "撤回"
			auditItem.Describe = []FlowDesc{{Key: "操作人", Value: oneRecord.RecallUser}}
			res = append(res, auditItem)
			continue
		}

		var auditChildren []FlowChild
		for _, i := range oneRecord.Detail {
			var auditChild FlowChild
			auditChild.Index = i.Index
			auditChild.Title = i.IndexDesc
			auditChild.Date = i.CheckTime
			auditChild.Status = i.SubStatus
			auditChild.Describe = getFlowAudit(i.Checker, i.Desc)
			auditChildren = append(auditChildren, auditChild)
		}
		//拼接未审核结束的流程
		if oneRecord.Status == 3 || oneRecord.Status == 0 {
			auditItem.Status = 3 //产品要求待审核和审核中都统一展示为审核中
			var auditChild FlowChild
			auditChild.Index = confMap[oneRecord.CurrentDeep].Index
			auditChild.Title = confMap[oneRecord.CurrentDeep].IndexDesc
			auditChild.Status = 0
			auditChild.Describe = getFlowAudit(strings.Join(oneRecord.CurrentChecker, ","), "")
			auditChildren = append(auditChildren, auditChild)
			for i := oneRecord.CurrentDeep + 1; i <= len(confMap); i++ {
				var auditChild FlowChild
				auditChild.Index = confMap[i].Index
				auditChild.Title = confMap[i].IndexDesc
				auditChild.Status = -1
				_, touserNames, err := QueryNextCheckUser(oneRecord, confManager, i)
				if err != nil {
					logger.Error.Println(" 审核流程查询下一个审核用户失败，err:", err)
				}
				auditChild.Describe = getFlowAudit(strings.Join(touserNames, ","), "")
				auditChildren = append(auditChildren, auditChild)
			}
		}
		//子流程中需要按照index排序
		sort.Slice(auditChildren, func(i, j int) bool {
			return auditChildren[i].Index > auditChildren[j].Index
		})
		auditItem.Children = auditChildren
		res = append(res, auditItem)
	}
	reverse(res)
	return res, nil
}

// 二选一，优先nickname
func ChooseOne(nickname, username string) string {
	if nickname == "" {
		return username
	}
	return nickname
}

// 查询服务的整体流程：从新建到各项审核
func GetServiceFlowV2(info ServiceInfo) ([]ApplyFlowV2, error) {

	//获取配置
	confManager, _, _ := GetAuditConf("", info.ProjectID, 1)
	var res []ApplyFlowV2

	//服务新建：
	_, idAndNickTempMap, idAndDepartTempMap, _ := queryUserNameByIds([]string{info.CreateUid})
	var newService = ApplyFlowV2{
		Title:  "服务新建",
		Status: -1,
		Date:   info.Createtime,
		Describe: []FlowDesc{{Key: "操作人", Value: ChooseOne(idAndNickTempMap[info.CreateUid], info.CreateUser)},
			{Key: "部门", Value: idAndDepartTempMap[info.CreateUid]}},
	}
	res = append(res, newService)
	//查询工单
	var boolm []map[string]interface{}
	boolm = append(boolm, gin.H{"term": gin.H{"objectid": info.ID}})
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": boolm,
			},
		},
		"sort": gin.H{"createtime": gin.H{"order": "asc"}},
		"size": UnlimitSize,
	}
	resByte, err := esutil.SearchByTerm(DataBase, AuditRecordESTB, body)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	relList, _ := esutil.GetSourceAndTotal(resByte)

	for _, value := range relList {
		var oneRecord AuditRecord
		_ = json.Unmarshal([]byte(value), &oneRecord)

		var title string
		switch oneRecord.Type {
		case common.FWFB_ON: //资产使用-申请
			title = "服务发布"
		case common.FWFB_OFF: //资产使用-续期
			title = "服务取消发布"
		}
		//1. 申请标头
		var headItem ApplyFlowV2
		_, idAndNickHeadMap, idAndDepartHeadMap, _ := queryUserNameByIds([]string{oneRecord.UserID})
		headItem = ApplyFlowV2{
			Title:  title,
			Status: -1,
			Date:   oneRecord.CreateTime,
			Describe: getFlowBasicV2(ChooseOne(idAndNickHeadMap[oneRecord.UserID], oneRecord.UserName),
				oneRecord.ID, idAndDepartHeadMap[oneRecord.UserID]),
		}
		res = append(res, headItem)

		var auditItem ApplyFlowV2
		auditItem.Date = oneRecord.UpdateTime
		auditItem.Status = oneRecord.Status
		auditItem.Title = title + "审核"
		//2.1 审核流程-自动申请
		if oneRecord.AutoCheck {
			//自动申请
			auditItem.Describe = getFlowAudit("系统自动审核", "通过")
			auditItem.Date = oneRecord.CreateTime
			res = append(res, auditItem)
			//自动审核的没有审核流程
			continue
		}

		//2.2 审核流程
		if oneRecord.Status == 4 { //已撤回
			auditItem.Title = title + "撤回"
			auditItem.Describe = []FlowDesc{{Key: "操作人", Value: oneRecord.RecallUser},
				{Key: "部门", Value: QueryUserDepartByName(oneRecord.RecallUser)}}
			res = append(res, auditItem)
			continue
		}
		// 获取多层级的审核详情
		tempFlowChart := BuildAuditFlowByRecord(oneRecord, confManager)
		//统一处理名称
		tempFlowChart = ProcessDetailV2(tempFlowChart)
		auditItem.AuditChart = tempFlowChart.AuditChartInfo

		res = append(res, auditItem)
	}
	reverseV2(res)
	return res, nil
}

func reverseV2(s []ApplyFlowV2) {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
}

func reverse(s []ApplyFlow) {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
}

// 根据服务信息，查询源表中的数据
func QueryFieldData(info ServiceInfo) (metasource.MGCommonRes, error) {
	var result metasource.MGCommonRes
	if info.SourceTBType == 2 {
		sourcetbid, err := QuerySourceTBIDByInventory(info.SourceTBID)
		if err != nil {
			logger.Error.Println(err)
			return result, err
		}
		info.SourceTBID = sourcetbid
	}

	//获取源端表信息：表连接、字段、类型等信息
	sourceTb, err := QuerySourceTBInfoByTBID(info.SourceTBID, info.SourceTBName)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}

	engineinfo, err := dbutil.EngineInfo(sourceTb.Engineid)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}
	var separator, schemaname string
	switch sourceTb.DBType {
	case common.STORK, common.TERYX, "postgres", "greenplum":
		separator = "\""
		schemaname = sourceTb.Schame
	case common.HIVE, common.ODPS, common.Inceptor:
		separator = "`"
		schemaname = sourceTb.Dbname
	default:
		separator = "\""
		schemaname = sourceTb.Schame
	}
	// 通过用户在前端页面选择的表字段对应关系，生成sql
	sql := fmt.Sprintf("select * from %s%s%s.%s%s%s limit 10",
		separator, schemaname, separator, separator, sourceTb.TableName, separator)
	logger.Info.Println("QueryFieldData  sql:", sql)
	resData, err := collect.MGExecuteQuerySqlV2(engineinfo, sourceTb.Dbname, sql)
	if err != nil {
		logger.Error.Println(err)
		return resData, err
	}
	return resData, nil
}

// 根据服务信息，查询源表中的数据
func QueryPushtbData(info ServiceInfo) (metasource.MGCommonRes, error) {
	var result metasource.MGCommonRes
	//根据推送的数据库ID查询对应的库表信息
	accInfo, err := collect.DBSearch(info.PushServer.DBId)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}
	//处理目的端表，判断是否需要新建表
	var selectSql string
	switch accInfo.DBType {
	case "mysql":
		selectSql = fmt.Sprintf("select * from `%s` limit 3", info.PushServer.TableName)
	case "oracle":
		selectSql = fmt.Sprintf(`SELECT * from "%s"."%s" WHERE ROWNUM<=3`, info.PushServer.Schema, info.PushServer.TableName)
	case "stork", "postgres", "teryx", "greenplum", common.DMDB:
		selectSql = fmt.Sprintf(`select * from "%s"."%s" limit 3`, info.PushServer.Schema, info.PushServer.TableName)
	case "SQL server":
		selectSql = fmt.Sprintf(`select top 3 * from "%s"."%s"`, info.PushServer.Schema, info.PushServer.TableName)
	}
	result, err = ExecQuerySql(accInfo, selectSql)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}
	return result, nil
}

// 库表推送查推送目的端的数据库类型
func FixPrioritymode(info *ServiceInfo) error {
	if info.Type == 2 && info.PushServer.AdvancedConfig.Prioritymode == 0 {
		res, err := escli.NewESClientCl.SearchByID(DataBaseMeta, DbMetaTable, info.PushServer.DBId)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
		sourcetype := gjson.Get(res, "dbtype").String()
		if sourcetype == common.Stork || sourcetype == common.TDSQL || sourcetype == common.POSTGRES || sourcetype == common.GAUSSDBA {
			info.PushServer.AdvancedConfig.Prioritymode = 1
		} else if sourcetype == common.Teryx || sourcetype == common.GREENPLUM || sourcetype == common.GaussDB {
			var arr []string
			for _, v := range info.PushServer.TableRelation.Nodes {
				if v.Position == "right" {
					arr = append(arr, v.FieldType)
				}
			}
			if antshttpdo.HaveBianry(arr) {
				info.PushServer.AdvancedConfig.Prioritymode = 1
			} else {
				info.PushServer.AdvancedConfig.Prioritymode = 2
			}
		}
	}
	return nil
}

func ListAllDownLoadServices(projectId string) ([]OutParamDownLoadService, error) {
	if projectId == "" {
		return nil, errors.New("查询项目ID为空")
	}
	var boolm []map[string]interface{}
	term1 := gin.H{
		"term": gin.H{
			"type": 3,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"projectid": projectId,
		},
	}
	boolm = append(boolm, term1, term2)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": boolm,
			},
		},
		"_source": []string{"id", "name", "downloadserver.filestatuss", "downloadserver.reason"},
		"size":    10000,
	}
	str, _ := json.Marshal(body)
	logger.Info.Println("ListAllDownLoadServices query body:", string(str))
	bytes, err := esutil.SearchByTerm(DataBase, ServiceESTB, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	relList, _ := esutil.GetSourceAndTotal(bytes)
	var result []OutParamDownLoadService
	for _, value := range relList {
		var oneDownLoadService OutParamDownLoadService
		_ = json.Unmarshal([]byte(value), &oneDownLoadService)
		result = append(result, oneDownLoadService)
	}
	return result, nil
}

func ServiceAddLineAge(id string) {

	fmt.Println("获取的id为:", id)
	if id != "" {
		fmt.Println("开始添加单表血缘:", id)
		body := gin.H{
			"id":        id,
			"addmodule": common2.Service2Lineage,
		}
		err := tools.LineagePost(body)
		if err != nil {
			fmt.Println("添加采集血缘异常:", err)
			return
		}

	}

}
