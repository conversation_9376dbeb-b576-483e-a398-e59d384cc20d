package mgm

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path"
	"sort"
	"strconv"
	"strings"
	"time"

	"datatom/gin.v1"

	"github.com/tidwall/gjson"

	"datatom.com/ants/httpdo/access"
	"datatom.com/ants/source"
	antssource "datatom.com/ants/source"
	"datatom.com/asset/common"
	"datatom.com/asset/logger"
	"datatom.com/metadata/httpdo/collect"
	"datatom.com/metadata/httpdo/dbutil"
	"datatom.com/metadata/httpdo/esutil"
	metadata "datatom.com/metadata/source"
	tools "datatom.com/tools/httpdo"
	toolsource "datatom.com/tools/source"
	"github.com/go-xorm/xorm"
	uuid "github.com/satori/go.uuid"
	"github.com/tidwall/sjson"
)

func PatchAcc(dwsInfo DWSTBInfo) (source.NewAcc, error) {
	var res source.NewAcc
	if len(dwsInfo.ParamTb) == 0 { //存放关联表或 sql 的引擎信息
		return res, errors.New("table missing")
	}
	//res.Name = dwsInfo.TbName //将专题表名赋值给采集脚本作为名称
	res.Name = dwsInfo.TbtaskName //将讲专题任务名赋给任务名
	res.ProjectID = dwsInfo.ProjectId
	res.Notetype = "datax"
	//res.Name=
	res.Tasktype = 5 //专题落地
	if dwsInfo.LandStrategy.Scheduletype != 5 {
		retryNum := dwsInfo.LandStrategy.ExectTimes
		res.Retrynum = int(*retryNum) //重试次数
	}
	res.FileType = dwsInfo.FileFormat //文件格式 text/orc
	res.IsRetry = dwsInfo.IsRetry
	res.Retrynum = dwsInfo.RetryNum
	res.IsBan = dwsInfo.IsBan
	res.RetryInterval = dwsInfo.RetryInterval

	//抽取源 只需要引擎 id 和 sql 即可得到数据
	res.Extractdb.EngineID = dwsInfo.ParamTb[0].SourceEngineId //已做长度校验，此处直接拿。后续用此参数查引擎信息
	res.Extractdb.DBType = dwsInfo.ParamTb[0].SourceDBType
	res.Extractdb.Timetype = dwsInfo.UpdateField.FieldName
	res.Extractdb.Dbname = dwsInfo.ParamTb[0].SourceDBName
	//res.Extractdb.Dbname//有可能跨库联表，故不存数据库名; schema，table，tableid 同理
	res.Extractdb.SqlTxt = dwsInfo.Sqltxt

	if dwsInfo.UpdateStrategy == 1 {
		res.Extractdb.Createfield = ""
	} else if dwsInfo.UpdateStrategy == 2 {
		res.Extractdb.Createfield = dwsInfo.UpdateField.FieldName //增量字段
		res.Extractdb.Timetype = dwsInfo.UpdateField.FieldType    //时间类型
		if dwsInfo.Syncsourcedata == 2 {
			res.Extractdb.AccIncreStatus.CurrentIncredata = dwsInfo.UpdateField.StartValue //不同步的初始值
		}
		res.Extractdb.AccIncreStatus.Synchistory = dwsInfo.Syncsourcedata != 2 //同步原表历史数据
		res.Extractdb.AccIncreStatus.DeleteSouTb = dwsInfo.Clearstoredata == 1 //清空存储表数据
	}
	//if dwsInfo.Clearstoredata == 1 {
	//	res.Extractdb.AccIncreStatus.DeleteSouTb = true
	//}
	//if dwsInfo.Syncsourcedata == 1 || dwsInfo.Syncsourcedata == 0 {
	//	res.Extractdb.AccIncreStatus.Synchistory = true
	//}

	//存储源(服务层)
	res.Sourcedb.Sourceid = dwsInfo.StoreDBId      //存储数据源ID
	res.Sourcedb.Engineid = dwsInfo.StoreEngineId  //服务层引擎 ID
	res.Sourcedb.Layerid = dwsInfo.LayerId         //服务层 层id
	res.Sourcedb.LayerName = dwsInfo.LayerName     //服务层 层名称
	res.Sourcedb.Odsdatabase = dwsInfo.StoreDBName //落地数据库名，是真实的库名而不是 schema。理论上 一个层的数据都在同一个库中
	res.Sourcedb.Dbtype = dwsInfo.StoreDBType      //数据类型
	res.Sourcedb.Schema = dwsInfo.StoreSchema      //落地的 schema
	res.Sourcedb.Table = dwsInfo.TbName            //落地表名-专题表
	//res.Sourcedb.TableID = dwsInfo.ID              //专题表 ID
	res.Sourcedb.FileType = res.FileType //文件格式 text/orc
	//res.Sourcedb.NewCreate  //service-todo 查找此字段的作用
	res.Sourcedb.Field, res.Sourcedb.Fieldtype, res.Sourcedb.Fieldcomment = PatchField(dwsInfo)
	//res.Sourcedb.fieldcomment //专题表生成时没有备注，无需处理
	err := fixSpSourcedb(&res)
	if err != nil {
		return res, err
	}
	return res, nil
}

// 查询存储源引擎相关信息
func fixSpSourcedb(res *source.NewAcc) error {
	//根据引擎ID查询连接信息
	engineinfo, err := dbutil.EngineInfo(res.Sourcedb.Engineid)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	res.Sourcedb.Odspassword = engineinfo.Password
	res.Sourcedb.Odsuser = engineinfo.Username
	res.Sourcedb.Odsip = engineinfo.IP
	res.Sourcedb.Odsport = strconv.Itoa(engineinfo.Port)
	return nil
}

// 抽取的引擎信息查询
func fixExtractDbInfo(sp source.NewAcc) (metadata.AccDbInfo, error) {
	var result metadata.AccDbInfo
	result.DBType = sp.Extractdb.DBType
	//根据引擎ID查询连接信息
	engineinfo, err := dbutil.EngineInfo(sp.Extractdb.EngineID)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}
	if result.DBType == common.HIVE || result.DBType == common.Inceptor {
		result.HiveConn.HiveURL = engineinfo.JdbcURL //TODO 可能不一致
		result.HiveConn.HiveUser = engineinfo.Username
		result.HiveConn.HivePassword = engineinfo.Password
		if engineinfo.KerberosInfo.Id != "" {
			result.HiveConn.AuthMethod = 2
		}
		result.HiveConn.KeytabFilePath = engineinfo.KerberosInfo.KeyTabPath
		result.HiveConn.Principal = engineinfo.KerberosInfo.Principal
		result.HiveConn.Krb5ConfPath = engineinfo.KerberosInfo.Krb5ConfPath
		// add driver path
		result.HiveConn.DriveInfo.DriverPath = engineinfo.DriverInfo.DriverPath
	} else if result.DBType == common.ODPS {
		result.NormalConn.DBUser = engineinfo.Username
		result.NormalConn.DBPassword = engineinfo.Password
		result.NormalConn.Database = engineinfo.OdpsProject
	} else {
		//postgres,greenplum,teryx,stork,gaussdb,gaussdba
		result.NormalConn.DBIP = engineinfo.IP
		result.NormalConn.DBPort = strconv.Itoa(engineinfo.Port)
		result.NormalConn.Database = sp.Extractdb.Dbname
		result.NormalConn.DBUser = engineinfo.Username
		result.NormalConn.DBPassword = engineinfo.Password
		result.NormalConn.RegisterWay = engineinfo.RegisterWay
		result.NormalConn.JdbcUrl = engineinfo.JdbcURL
	}
	return result, nil
}

// AccNewDatax 新建单例/批量抽取任务
func AccNewDatax(sp source.NewAcc, Authorization, oldAccessId, oldJobId string, datastrategy int) (string, error) {
	logger.Info.Println("AccNewDatax method accessinfo is :", sp)
	if sp.Sourcedb.Dbtype != common.TERYX && sp.Sourcedb.Dbtype != common.GAUSSDB && sp.Sourcedb.Dbtype != common.GAUSSDBA &&
		sp.Sourcedb.Dbtype != common.STORK && sp.Sourcedb.Dbtype != "greenplum" && sp.Sourcedb.Dbtype != "postgres" {
		return "", fmt.Errorf("未知的数据库类型:%s", sp.Sourcedb.Dbtype)
	}
	//根据engineid获取存储源信息
	var err error
	var analyzeSql string

	sp.Sourcedb.FileType = sp.FileType
	var job source.AccInfo
	job.Dirid = sp.Dirid     //无值
	job.Dirname = sp.Dirname //无值
	job.Notetype = sp.Notetype
	job.Notename = sp.Name //无值
	job.Datecreated = time.Now().Format("2006-01-02 15:04:05")
	job.Updatetime = job.Datecreated
	job.CsvNum = sp.CsvNum //无值
	job.ProjectID = sp.ProjectID
	job.Description = sp.Description //无值

	////---6 获取抽取源数据库信息 todo 抽取源  sourcedb
	db, err := fixExtractDbInfo(sp)
	if err != nil {
		fmt.Println(err)
		logger.Error.Println(err)
		return "", err
	}
	// 修正抽取源信息
	//var content, dbpath, sourcerealcontent, sinkrealcontent, producename, consumename string //删除了实时抽取用到的参数
	var content, dbpath string
	//实时抽取,暂无实现
	if sp.Tasktype == 4 {
		/* --- 暂时注释实时抽取
		//启动confluent实例服务
		err = StartConfluent(sp.Sourcedb.HiveFilePath)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		job.RealInfo.TableID = sp.TableID
		info := TopicInfo(&sp, &db, 1)
		if sp.Offline { //先离线
			//构建离线json体
			content, dbpath, err = AccCreateJson(&sp, &db)
			//构建实时 json 参数
			sourcerealcontent, producename, err = ProduceContent(&sp, &db)
			sinkrealcontent, consumename, err = ConsumeContent(&sp, &db)
			if err != nil {
				logger.Error.Println(err)
				return "", err
			}
		} else {
			sourcerealcontent, producename, err = ProduceContent(&sp, &db)
			sinkrealcontent, consumename, err = ConsumeContent(&sp, &db)
			if err != nil {
				logger.Error.Println(err)
				return "", err
			}
			fmt.Print(sinkrealcontent)
		}
		job.RealInfo.SourceRealContent = sourcerealcontent
		job.RealInfo.SinkRealContent = sinkrealcontent
		job.RealInfo.Producename = producename
		job.RealInfo.Consumename = consumename
		job.RealInfo.Topic = info.DbServerName + "." + info.TableWhitelist
		//更新es数据 --将抽取源的表更新到metadata任务列表中
		err = UpdateEsInfo(sp.Extractdb.Id, sp.Extractdb.Table, 0)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		*/
	} else {
		//单例抽取:普通单例
		if db.DBType == "csv" {
			//csv中的json构建
			//content, dbpath, err = AccCreateCsvJson(&sp, &db)
		} else {
			content, dbpath, analyzeSql, err = AccCreateJson(&sp, &db, datastrategy)
		}
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
	}
	//第一种方法，替换'<', '>', '&'
	content = strings.Replace(content, "\\u003c", "<", -1)
	content = strings.Replace(content, "\\u003e", ">", -1)
	content = strings.Replace(content, "\\u0026", "&", -1)
	job.Content = content

	//==========================================================

	//job.Content = sp.Content

	//=================================================================
	job.Submitted = false
	job.User = sp.User
	job.Variables = sp.Variables
	//job.Tags = sp.Tags
	//job.Concat = sp.Concat
	//job.Delif = sp.Delif
	job.Username = sp.UserName
	job.User = sp.User
	job.Description = sp.Description
	job.Extractdb = sp.Extractdb
	job.Sourcedb = sp.Sourcedb
	job.Tasktype = sp.Tasktype
	job.RealInfo.Offline = sp.Offline
	job.TDCLoad = sp.TDCLoad //todo
	job.Retrynum = sp.Retrynum
	job.IsRetry = sp.IsRetry
	job.IsBan = sp.IsBan
	job.RetryInterval = sp.RetryInterval
	job.ProjectID = sp.ProjectID
	job.AnalyzeSql = analyzeSql

	// results, _ := AccDbInfo(sp.Extractdb.Id)
	randid := Krand(4, 3)
	if sp.Notetype == source.RESOURCE_TYPE_DATAX {
		//根据旧的采集ID判断是否为更新
		if oldAccessId == "" {
			noSpeDirName := NoSpe(sp.Dirname)
			noSpeName := NoSpe(sp.Name)
			job.Id = "admin_user_" + noSpeDirName + "_" + noSpeName + string(randid) + ".datax"
		} else {
			job.Id = oldAccessId
			job.Jobid = oldJobId
		}
		filename := common.UNSUBMITTED + "/" + job.Id + "/" + job.Id
		//var cmdNull bool
		//var PatchCmd string

		//暂时不支持hive落地
		if sp.Sourcedb.Dbtype == common.HIVE || sp.Sourcedb.Dbtype == common.Inceptor {
			//暂时只支持单列
			if sp.TDCLoad == "落地抽取" {
				// 落地抽取提前生成datax json文件
				loadpath := common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
				err = GenerateFile(job.Content, loadpath, Authorization)
				if err != nil {
					logger.Error.Println(err)
					fmt.Println("=================err" + err.Error())
					return "", err
				}
			}
			//hive抽取json文件放置于hive文件
			filename = common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
			// fmt.Println("filename:", filename)
			// fmt.Println("first sp.Cmd:", sp.Cmd)
			if sp.Cmd == "" {
				sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s", filename)
				if sp.Extractdb.Createfield != "" {
					sp.Cmd = fmt.Sprintf("/usr/bin/sh %s", filename)
				} //增量是shell脚本
				//cmdNull=true//批量采集用到
			} else {
				sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s %s", sp.Cmd, filename)
				//PatchCmd = fmt.Sprintf(" %s %s", sp.Cmd, filename)  //批量采集用到
				if sp.Extractdb.Createfield != "" {
					sp.Cmd = fmt.Sprintf("/usr/bin/sh %s %s", sp.Cmd, filename)
				}
			}
			dbname := sp.Sourcedb.Odsdatabase
			path := dbpath
			//单例抽取
			if len(sp.Extractdb.Batchtables) == 0 {
				sp.Cmd, err = HiveSh(&sp, path, dbname, job.Id, Authorization)
				if err != nil {
					logger.Error.Println(err)
					return "", err
				}
				// fmt.Println("----单例抽取执行命令---：", sp.Cmd)
			}
		}

		//---------------------------------------------------------------------------------------------------
		//stork & teryx 的批量的脚本命令
		// if (sp.Sourcedb.Dbtype == "stork" || sp.Sourcedb.Dbtype == "teryx") && len(sp.Extractdb.Batchtables) != 0 {
		// 	sp.Cmd, err = storksh(&sp, Authorization)
		// 	if err != nil {
		// 		logger.Error.Println(err)
		// 		return "", err
		// 	}
		// }
		//stork || teryx (命令都放到一起吧)
		if sp.Sourcedb.Dbtype == "greenplum" || sp.Sourcedb.Dbtype == "postgres" || sp.Sourcedb.Dbtype == common.STORK ||
			sp.Sourcedb.Dbtype == common.TERYX || sp.Sourcedb.Dbtype == common.GAUSSDB || sp.Sourcedb.Dbtype == common.GAUSSDBA {
			//是否有增量判断
			switch {
			case sp.Extractdb.Createfield == "":
				if sp.Tasktype == 4 {
					job.Content = content
					sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s", filename)
				} else {
					job.Content = StorkGetScript(content, sp.Extractdb.EngineID)
					sp.Cmd = fmt.Sprintf("python %s", filename)
				}

			case sp.Extractdb.Createfield != "":
				sp.Cmd = fmt.Sprintf("python %s", filename)
			}
		}
		realFileName := sp.Cmd

		//   hive或者hive的增量命令||  stork&teryx的批量命令
		if sp.Extractdb.Createfield != "" && sp.Sourcedb.Dbtype == "hive" {
			filename = realFileName
			sp.Cmd = fmt.Sprintf("/usr/bin/sh  %s", filename)
		}

		job.Cmd = sp.Cmd
		job.Extra_type = db.DBType
		job.Extractdb.DBType = job.Extra_type
		logger.Info.Println("AccNewDatax accessjob info is :", job)
		err = esutil.AddWithAimID(common.DataBaseLab, common.TBAccess, job.Id, job)
		if err != nil {
			logger.Error.Printf("添加抽取记录失败:%s", err.Error())
			return "", err
		}

		//生成文件
		var filepath string
		if sp.Sourcedb.Dbtype == "hive" && sp.TDCLoad != "落地抽取" {
			//hive
			filepath = common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
		} else {
			//非hive
			filepath = common.UNSUBMITTED + "/" + job.Id + "/" + job.Id
		}
		if len(sp.Extractdb.Batchtables) == 0 || (sp.Sourcedb.Dbtype == "greenplum" || sp.Sourcedb.Dbtype == "postgres" || sp.Sourcedb.Dbtype == common.STORK || sp.Sourcedb.Dbtype == common.TERYX || sp.Sourcedb.Dbtype == common.GAUSSDB || sp.Sourcedb.Dbtype == common.GAUSSDBA) {
			err = GenerateFile(job.Content, filepath, Authorization)
			if err != nil {
				logger.Error.Println(err)
				return "", err
			}
		}

		// 任务标签次数修正
		//for _, v := range sp.Tags {
		//	devcenter.EditTagCitations(v.ID, 1)
		//}
	}
	return job.Id, nil
}

// 单例建表
func AccCreateJson(sp *source.NewAcc, db *metadata.AccDbInfo, datastrategy int) (string, string, string, error) {

	var templete string
	var setting string
	var err error

	// fmt.Println("db: ", db)

	setting = common.Setting

	readercreate := func(sp *source.NewAcc, db *metadata.AccDbInfo) string {
		reader := common.Reader
		reader, _ = sjson.Set(reader, "parameter.username", db.NormalConn.DBUser)
		reader, _ = sjson.Set(reader, "parameter.password", transPassword(db.NormalConn.DBPassword))
		if sp.Tasktype == 4 {
			reader, _ = sjson.Set(reader, "parameter.splitPk", "")
		}
		if sp.Tasktype != 4 && len(sp.Extractdb.Mainkey) != 0 {
			reader, _ = sjson.Set(reader, "parameter.splitPk", sp.Extractdb.Mainkey[0])
		}
		return reader
	}
	// writer
	writerjson, dbpath, analyzeSql, err := WriterJSON(sp, false, datastrategy)
	fmt.Println("==============writerjson:", writerjson)
	if err != nil {
		return "", "", "", err
	}

	if db.DBType == common.HIVE || db.DBType == common.Inceptor {
		templete, err = CommonHive(sp, db, writerjson)
		if err != nil {
			fmt.Println("hive reader err:", err.Error())
			logger.Error.Println("hive reader err:", err.Error())
			return templete, dbpath, analyzeSql, err
		}
		return templete, dbpath, analyzeSql, nil
	}

	if db.DBType == common.DRealDb {
		templete, err = CommonMysqlReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("mysql reader err:", err.Error())
			logger.Error.Println("mysql reader err:", err.Error())
			return templete, dbpath, analyzeSql, err
		}
		return templete, dbpath, analyzeSql, nil
	}

	if db.DBType == common.ODPS {
		templete, err = CommonOdpsReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("odps reader err:", err.Error())
			logger.Error.Println("odps reader err:", err.Error())
			return templete, dbpath, analyzeSql, err
		}
		return templete, dbpath, analyzeSql, nil
	}

	if db.DBType == common.DMDB {
		templete, err = CommonDaMengReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("DMDB reader err:", err.Error())
			logger.Error.Println("DMDB reader err:", err.Error())
			return templete, dbpath, analyzeSql, err
		}
		return templete, dbpath, analyzeSql, err
	}

	if db.DBType == "stork" || db.DBType == "teryx" || db.DBType == "gaussdb" || db.DBType == "postgres" ||
		db.DBType == "gaussdba" || db.DBType == "greenplum" || db.DBType == common.UXDB {
		var jdbcType, readerType string
		if db.DBType == common.UXDB {
			jdbcType = "uxdb"
			readerType = "rdbmsreader"
		} else {
			jdbcType = "postgresql"
			readerType = "postgresqlreader"
		}

		storkreader := readercreate(sp, db)
		storkreader, _ = sjson.Set(storkreader, "name", readerType)
		storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.jdbcUrl.0", fmt.Sprintf("jdbc:%s://%s:%s/%s", jdbcType, db.NormalConn.DBIP, db.NormalConn.DBPort, db.NormalConn.Database))
		sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, `\`, `\\`)
		storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
		//-----------------------------------------------------------
		if sp.Extractdb.Createfield != "" {
			var istime, isdate bool
			var isint bool
			for i, v := range sp.Sourcedb.Field {
				if v == sp.Extractdb.Createfield {
					if strings.Contains(strings.ToUpper(sp.Sourcedb.Fieldtype[i]), "DATE") {
						isdate = true
					}
					if strings.Contains(strings.ToUpper(sp.Sourcedb.Fieldtype[i]), "TIMESTAMP") {
						istime = true
					}
					if strings.Contains(strings.ToUpper(sp.Sourcedb.Fieldtype[i]), "INT") {
						isint = true
					}
					break
				}
			}
			//抽取脏数据----------------------------------------------------------------
			var dirtydata string
			var nodirtytrans string
			var nodirtyreader string
			var nodirtytemp string
			//*第一次抽取不加入初始时间
			var firsttimetrans string
			//fmt.Println("是否支持脏数据:", sp.Extractdb.Supportdirtydata)
			//if sp.Extractdb.Supportdirtydata == true {
			//	switch sp.Extractdb.Timetype {
			//	case "yyyyMMdd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{8}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy-MM-dd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy/MM/dd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy-MM-dd HH:mm:ss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy/MM/dd HH:mm:ss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyyMMddHHmmss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{14}$' or \"%s\" is null `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	default:
			//		dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			//	}
			//	//脏数据
			//	if isint {
			//		dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			//	}
			//}
			var timetrans string
			fmt.Println(sp.Extractdb.Timetype)
			//var starttime, endtime string
			//switch db.DBType {
			//case "greenplum", "teryx", "gaussdb":
			//	if len(sp.Extractdb.Timetype) > 10 {
			//		starttime = `substring('${starttime}' from 1 for 8)||' '||substring('${starttime}' from 9 for 6)`
			//		endtime = `substring('${endtime}' from 1 for 8)||' '|| substring('${endtime}' from 9 for 6)`
			//	} else {
			//		starttime = `substring('${starttime}' from 1 for 8)`
			//		endtime = `substring('${endtime}' from 1 for 8)`
			//	}
			//default:
			//	if len(sp.Extractdb.Timetype) > 10 {
			//		starttime = `concat(left('${starttime}',8),' ',right('${starttime}',6))`
			//		endtime = `concat(left('${endtime}',8),' ',right('${endtime}',6))`
			//	} else {
			//		starttime = `left('${starttime}',8)`
			//		endtime = `left('${endtime}',8)`
			//	}
			//}

			switch sp.Extractdb.Timetype {
			case "yyyyMMdd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_ori, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_ori, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyyMMdd")
			case "yyyy-MM-dd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_hyphen, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_hyphen, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyy-MM-dd")
			case "yyyy/MM/dd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_slash, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_slash, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyy/MM/dd")
			case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_hyphen)
			case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_slash)
			case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_origin)
			case common.Incre_Autoid:
				timetrans = fmt.Sprintf(` \"%s\" > '${starttime}'`, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" is not null `, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_Autoid)
				storkreader, _ = sjson.Set(storkreader, "parameter.firstDataCollection", true)
			}

			if isdate {
				timetrans = fmt.Sprintf(` \"%s\" > cast('${starttime}' as date)  and \"%s\" <  cast('${endtime}' as date)       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(` \"%s\" < cast('${endtime}' as date) and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			}

			if istime {
				timetrans = fmt.Sprintf(` \"%s\" > cast('${starttime}' as timestamp)  and \"%s\" <  cast('${endtime}' as timestamp)       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(` \"%s\" < cast('${endtime}' as timestamp) and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			}
			//语句处理
			if isint {
				timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
				firsttimetrans = " 1=1 "
			}
			//加入脏数据处理
			timetrans = timetrans + dirtydata
			firsttimetrans = firsttimetrans + dirtydata
			nodirtytrans = timetrans

			if sp.Extractdb.Wherecontent != "" {
				sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "
			}
			sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
			storkreader, _ = sjson.Set(storkreader, "parameter.firstDataCollection", true)
			storkreader, _ = sjson.Set(storkreader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
			increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + firsttimetrans
			storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", increWhereSql)
			//storkreader, _ = sjson.Set(storkreader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
			nodirtyIncreSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + nodirtytrans
			nodirtyreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", nodirtyIncreSql)
			//nodirtyreader, _ = sjson.Set(storkreader, "parameter.where", sp.Extractdb.Wherecontent+nodirtytrans)
			templete := fmt.Sprintf(`{
	    "job": {
			"setting":%s,
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, setting, storkreader, writerjson)
			//-----------------------------
			//拿到不脏的template
			// nodirtytemp := strings.Replace(templete, dirtydata, "", 1)
			nodirtytemp = fmt.Sprintf(`{
	       "job": {
			"setting":%s,
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, setting, nodirtyreader, writerjson)

			//---------------------------------------------------

			templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)

			return templete, dbpath, analyzeSql, nil
		}

		//===============================================================
		//以下为没有增量字段
		templete = fmt.Sprintf(`{
	    "job": {
			"setting":%s,
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, setting, storkreader, writerjson)

		return templete, dbpath, analyzeSql, nil
	}

	return templete, dbpath, analyzeSql, nil
}

func PushPatchAcc(info DwsTBPublishInfo) (source.NewAcc, []string, []string, error) {
	var res source.NewAcc
	// 获取专题相关信息，已此获取抽取源引擎相关信息
	dwsInfo, err := GetOneDwsRecord(info.DwsTBID)
	if err != nil {
		logger.Error.Println(err)
		return res, nil, nil, err
	}

	if len(dwsInfo.ParamTb) == 0 { //存放关联表或 sql 的引擎信息
		return res, nil, nil, errors.New("table missing")
	}
	res.Name = info.ServerName //将专题表名赋值给采集脚本作为名称
	res.ProjectID = dwsInfo.ProjectId
	res.Notetype = "datax"
	//res.Name=
	res.Tasktype = 6 //库表推送
	if info.PushStrategy.Scheduletype != 5 {
		retryNum := info.PushStrategy.ExectTimes
		res.Retrynum = int(*retryNum) //重试次数
	}
	res.FileType = dwsInfo.FileFormat //文件格式 text/orc
	res.IsRetry = info.IsRetry
	res.Retrynum = info.RetryNum
	res.IsBan = info.IsBan
	res.RetryInterval = info.RetryInterval

	//抽取源 只需要引擎 id 和 sql 即可得到数据
	res.Extractdb.EngineID = dwsInfo.ParamTb[0].SourceEngineId //已做长度校验，此处直接拿。后续用此参数查引擎信息
	res.Extractdb.DBType = dwsInfo.ParamTb[0].SourceDBType
	res.Extractdb.Timetype = dwsInfo.UpdateField.FieldName
	res.Extractdb.Dbname = dwsInfo.ParamTb[0].SourceDBName
	//res.Extractdb.Dbname//有可能跨库联表，故不存数据库名; schema，table，tableid 同理

	var targetFieldTypeMap = make(map[string]string)
	var SourceFieldTypeMap = make(map[string]string)
	var targetFieldCommentMap = make(map[string]string)
	// 遍历存储目的端字段类型
	for _, v := range info.TableRelation.Nodes {
		if v.Position == "right" {
			targetFieldTypeMap[v.FieldName] = v.FieldType
			targetFieldCommentMap[v.FieldName] = v.Comment
		} else {
			SourceFieldTypeMap[v.FieldName] = v.FieldType
		}
	}
	// 获取用户选择的抽取端字段
	var sourceFields []string
	var targetFields []string
	var targetFieldTypes []string
	var sourceFieldTypes []string
	var targetFieldComments []string
	for _, v := range info.TableRelation.Edges {
		sourceFields = append(sourceFields, v.Source)
		targetFields = append(targetFields, v.Target)
		// 找到该字段创建的类型保存下来
		targetType := targetFieldTypeMap[v.Target]
		targetFieldTypes = append(targetFieldTypes, targetType)
		sourceType := SourceFieldTypeMap[v.Source]
		sourceFieldTypes = append(sourceFieldTypes, sourceType)
		//获取字段注释
		targetComment := targetFieldCommentMap[v.Source]
		targetFieldComments = append(targetFieldComments, targetComment)
	}
	res.Extractdb.Fieldtype = sourceFieldTypes
	//5.1 todo
	if info.UpdateStrategy == 1 {
		res.Extractdb.Createfield = ""
	} else if info.UpdateStrategy == 2 {
		res.Extractdb.Createfield = info.UpdateField.FieldName //增量字段
		res.Extractdb.Timetype = info.UpdateField.FieldType    //时间类型
		if info.Syncsourcedata == 2 {
			res.Extractdb.AccIncreStatus.CurrentIncredata = info.UpdateField.StartValue //初始增量字段
		}
		res.Extractdb.AccIncreStatus.Synchistory = info.Syncsourcedata != 2 //同步原表历史数据
		res.Extractdb.AccIncreStatus.DeleteSouTb = info.Clearstoredata == 1 //清空存储表数据
	}
	accInfo, err := collect.DBSearch(info.DBId)
	if err != nil {
		logger.Error.Println(err)
		return res, nil, nil, err
	}
	//存储源(服务层)
	res.Sourcedb.Sourceid = accInfo.Id        //存储数据源ID
	res.Sourcedb.Engineid = accInfo.Id        //服务层引擎 ID
	res.Sourcedb.Layerid = ""                 //服务层 层id
	res.Sourcedb.LayerName = ""               //服务层 层名称
	res.Sourcedb.Odsdatabase = accInfo.DBName //落地数据库名，是真实的库名而不是 schema。理论上 一个层的数据都在同一个库中
	res.Sourcedb.Dbtype = accInfo.DBType      //数据类型
	res.Sourcedb.Schema = info.Schema         //落地的 schema
	res.Sourcedb.Table = info.TableName       //落地表名-专题表
	//res.Sourcedb.TableID = dwsInfo.ID              //专题表 ID
	res.Sourcedb.FileType = res.FileType //文件格式 text/orc
	//res.Sourcedb.NewCreate  //service-todo 查找此字段的作用
	//res.Sourcedb.Field, res.Sourcedb.Fieldtype = targetFields, targetFieldTypes
	res.Sourcedb.Field, res.Sourcedb.Fieldtype = sourceFields, sourceFieldTypes
	res.Sourcedb.Fieldcomment = targetFieldComments
	//res.Sourcedb.fieldcomment //专题表生成时没有备注，无需处理
	//err = fixSpSourcedb(&res)
	res.Sourcedb.Odspassword = accInfo.NormalConn.DBPassword
	res.Sourcedb.Odsuser = accInfo.NormalConn.DBUser
	res.Sourcedb.Odsip = accInfo.NormalConn.DBIP
	res.Sourcedb.Odsport = accInfo.NormalConn.DBPort
	res.DBVersion = accInfo.DBVersion
	res.Sourcedb.IsOpenSSL = accInfo.NormalConn.ISOpenSSL
	var findTableSql string
	var separator string
	// 为了处理sql查询中的特殊字段名，hive对字段名加反引号，stork等直接加双引号
	switch res.Extractdb.DBType {
	case common.STORK, common.TERYX, "postgres", "greenplum":
		separator = "\""
	case common.HIVE, common.ODPS, common.Inceptor:
		separator = "`"
	default:
		separator = "\""
	}

	switch accInfo.DBType {
	case "mysql":
		res.Sourcedb.JdbcURL = fmt.Sprintf("*******************************************************************************", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.Database, accInfo.TimeZoneInfo.TimeZone)
		findTableSql = fmt.Sprintf("select count(*) as num  from information_schema.TABLES t where t.TABLE_SCHEMA ='%s' and t.TABLE_NAME ='%s'", accInfo.NormalConn.Database, info.TableName)
	case "oracle":
		if accInfo.NormalConn.Oraclesid != "" {
			res.Sourcedb.JdbcURL = fmt.Sprintf("**************************", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.Oraclesid)
		} else {
			res.Sourcedb.JdbcURL = fmt.Sprintf("**************************", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.OracleServer)
		}
		findTableSql = fmt.Sprintf(`SELECT count(*) as "num" from ALL_TABLES where owner='%s' and TABLE_NAME='%s'`, strings.ToUpper(info.Schema), info.TableName)
	case "stork", "postgres", "postgressql":
		res.Sourcedb.JdbcURL = fmt.Sprintf("jdbc:postgresql://%s:%s/%s", accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort, accInfo.NormalConn.Database)
		findTableSql = fmt.Sprintf("select count(*) as num from information_schema.tables where table_schema='%s' and table_name='%s'", info.Schema, info.TableName)
	case "SQL server":
		res.Sourcedb.JdbcURL = fmt.Sprintf(`**********************************`,
			accInfo.NormalConn.DBIP, accInfo.NormalConn.DBPort,
			accInfo.NormalConn.Database)
		findTableSql = fmt.Sprintf("select count(*) as num from sysobjects where id = object_id('%s.%s')", info.Schema, info.TableName)
	}

	// 通过用户在前端页面选择的表字段对应关系，生成sql
	res.Extractdb.SqlTxt = fmt.Sprintf("select %s%s%s from (%s) a", separator, strings.Join(sourceFields, separator+","+separator), separator, dwsInfo.Sqltxt)

	if info.NeedCreate {
		sqlStr := CreateTableForTablePush(info, accInfo.DBType, accInfo.NormalConn.Database, targetFields, targetFieldTypes, targetFieldComments)
		_, err = ExecCreateSql(accInfo, findTableSql, sqlStr, "selecttable")
		if err != nil {
			return res, nil, nil, err
		}
	}
	return res, targetFields, targetFieldTypes, nil
}

// 新建库表推送任务
func PushNewDatax(sp source.NewAcc, targetFields []string, targetFieldTypes []string, publishInfo DwsTBPublishInfo, Authorization, oldJobId string) (string, error) {
	logger.Info.Println("AccNewDatax method accessinfo is :", sp)
	if sp.Sourcedb.Dbtype != "mysql" && sp.Sourcedb.Dbtype != "stork" && sp.Sourcedb.Dbtype != "postgres" &&
		sp.Sourcedb.Dbtype != "oracle" && sp.Sourcedb.Dbtype != "greenplum" && sp.Sourcedb.Dbtype != "SQL server" {
		return "", fmt.Errorf("未知的数据库类型:%s", sp.Sourcedb.Dbtype)
	}
	//5.1 todo
	//根据engineid获取存储源信息
	var err error
	sp.UserName = publishInfo.Publisher
	sp.User = publishInfo.PublisherId

	sp.Sourcedb.FileType = sp.FileType
	var job source.AccInfo
	job.Dirid = sp.Dirid     //无值
	job.Dirname = sp.Dirname //无值
	job.Notetype = sp.Notetype
	job.Notename = sp.Name //无值
	job.Datecreated = time.Now().Format("2006-01-02 15:04:05")
	job.Updatetime = job.Datecreated
	job.CsvNum = sp.CsvNum //无值
	job.ProjectID = sp.ProjectID
	job.Description = sp.Description //无值
	////---6 获取抽取源数据库信息 todo 抽取源  sourcedb
	db, err := fixExtractDbInfo(sp)
	if err != nil {
		fmt.Println(err)
		logger.Error.Println(err)
		return "", err
	}
	// 修正抽取源信息
	var content, dbpath string
	//taskType=4是实时抽取,暂无实现
	if sp.Tasktype != 4 {
		content, dbpath, err = AccPushCreateJson(&sp, targetFields, &db, publishInfo.DataStrategy)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
	}
	//第一种方法，替换'<', '>', '&'
	content = strings.Replace(content, "\\u003c", "<", -1)
	content = strings.Replace(content, "\\u003e", ">", -1)
	content = strings.Replace(content, "\\u0026", "&", -1)
	job.Content = content

	//==========================================================

	//job.Content = sp.Content

	//=================================================================
	job.Submitted = false
	job.User = sp.User
	job.Variables = sp.Variables
	//job.Tags = sp.Tags
	//job.Concat = sp.Concat
	//job.Delif = sp.Delif
	job.Username = publishInfo.Publisher
	job.User = publishInfo.PublisherId
	job.Description = sp.Description
	job.Extractdb = sp.Extractdb
	sp.Sourcedb.Field = targetFields
	sp.Sourcedb.Fieldtype = targetFieldTypes
	job.Sourcedb = sp.Sourcedb
	job.Tasktype = sp.Tasktype
	job.RealInfo.Offline = sp.Offline
	job.TDCLoad = sp.TDCLoad //todo
	job.Retrynum = sp.Retrynum
	job.IsRetry = sp.IsRetry
	job.IsBan = sp.IsBan
	job.RetryInterval = sp.RetryInterval
	job.ProjectID = sp.ProjectID

	// results, _ := AccDbInfo(sp.Extractdb.Id)
	randid := Krand(4, 3)
	if sp.Notetype == source.RESOURCE_TYPE_DATAX {
		//根据旧的采集ID判断是否为更新
		if publishInfo.AccessID == "" {
			noSpeDirName := NoSpe(sp.Dirname)
			noSpeName := NoSpe(sp.Name)
			job.Id = "admin_user_" + noSpeDirName + "_" + noSpeName + randid + ".datax"
		} else {
			job.Id = publishInfo.AccessID
			job.Jobid = oldJobId
		}
		filename := common.UNSUBMITTED + "/" + job.Id + "/" + job.Id
		//var cmdNull bool
		//var PatchCmd string

		//暂时不支持hive落地
		//if sp.Sourcedb.Dbtype == common.HIVE {
		if db.DBType == "hive" || db.DBType == common.Inceptor {
			//暂时只支持单列
			if sp.TDCLoad == "落地抽取" {
				// 落地抽取提前生成datax json文件
				loadpath := common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
				err = GenerateFile(job.Content, loadpath, Authorization)
				if err != nil {
					logger.Error.Println(err)
					fmt.Println("=================err" + err.Error())
					return "", err
				}
			}
			//hive抽取json文件放置于hive文件
			filename = common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
			// fmt.Println("filename:", filename)
			// fmt.Println("first sp.Cmd:", sp.Cmd)
			if sp.Cmd == "" {
				sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s", filename)
				if sp.Extractdb.Createfield != "" {
					sp.Cmd = fmt.Sprintf("/usr/bin/sh %s", filename)
				} //增量是shell脚本
				//cmdNull=true//批量采集用到
			} else {
				sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s %s", sp.Cmd, filename)
				//PatchCmd = fmt.Sprintf(" %s %s", sp.Cmd, filename)  //批量采集用到
				if sp.Extractdb.Createfield != "" {
					sp.Cmd = fmt.Sprintf("/usr/bin/sh %s %s", sp.Cmd, filename)
				}
			}
			dbname := sp.Sourcedb.Odsdatabase
			path := dbpath
			//单例抽取
			if len(sp.Extractdb.Batchtables) == 0 {
				sp.Cmd, err = HiveSh(&sp, path, dbname, job.Id, Authorization)
				if err != nil {
					logger.Error.Println(err)
					return "", err
				}
				// fmt.Println("----单例抽取执行命令---：", sp.Cmd)
			}
		}
		//if sp.Sourcedb.Dbtype == common.STORK || sp.Sourcedb.Dbtype == common.MYSQL || sp.Sourcedb.Dbtype == common.ORACLE {
		if db.DBType == "postgres" || db.DBType == "greenplum" || db.DBType == "stork" || db.DBType == "teryx" ||
			db.DBType == "gaussdb" || db.DBType == "odps" || db.DBType == "gaussdba" {
			//是否有增量判断
			switch {
			case sp.Extractdb.Createfield == "":
				if sp.Tasktype == 4 {
					job.Content = content
					sp.Cmd = fmt.Sprintf("python /opt/dana/datax/bin/datax.py %s", filename)
				} else {
					job.Content = StorkGetScript(content, sp.Extractdb.EngineID)
					sp.Cmd = fmt.Sprintf("python %s", filename)
				}
			case sp.Extractdb.Createfield != "":
				sp.Cmd = fmt.Sprintf("python %s", filename)
			}
		}
		realFileName := sp.Cmd
		//   hive或者hive的增量命令||  stork&teryx的批量命令
		if sp.Extractdb.Createfield != "" && (sp.Extractdb.DBType == "hive" || sp.Extractdb.DBType == common.Inceptor) {
			//if sp.Extractdb.Createfield != "" && sp.Sourcedb.Dbtype == "hive" {
			filename = realFileName
			sp.Cmd = fmt.Sprintf("/usr/bin/sh  %s", filename)
		}
		job.Cmd = sp.Cmd
		job.Extra_type = db.DBType
		job.Extractdb.DBType = job.Extra_type
		logger.Info.Println("AccNewDatax accessjob info is :", job)
		err = esutil.AddWithAimID(common.DataBaseLab, common.TBAccess, job.Id, job)
		if err != nil {
			logger.Error.Printf("添加抽取记录失败:%s", err.Error())
			return "", err
		}
		//生成文件
		var filepath string
		//if sp.Sourcedb.Dbtype == "hive" && sp.TDCLoad != "落地抽取" {
		if (sp.Extractdb.DBType == "hive" || sp.Extractdb.DBType == common.Inceptor) && sp.TDCLoad != "落地抽取" {
			//hive
			filepath = common.UNSUBMITTED + "/" + "hive" + "/" + job.Id + "/" + job.Id
		} else {
			//非hive
			filepath = common.UNSUBMITTED + "/" + job.Id + "/" + job.Id
		}
		//if len(sp.Extractdb.Batchtables) == 0 || (sp.Sourcedb.Dbtype == common.STORK || sp.Sourcedb.Dbtype == common.MYSQL || sp.Sourcedb.Dbtype == common.ORACLE) {
		if len(sp.Extractdb.Batchtables) == 0 || (sp.Extractdb.DBType == "postgres" || sp.Extractdb.DBType == "greenplum" || sp.Extractdb.DBType == "teryx" || sp.Extractdb.DBType == "stork" || sp.Extractdb.DBType == "odps") {
			//	if len(sp.Extractdb.Batchtables) == 0 || (sp.Sourcedb.Dbtype == common.STORK || sp.Sourcedb.Dbtype == common.MYSQL || sp.Sourcedb.Dbtype == common.ORACLE) {
			err = GenerateFile(job.Content, filepath, Authorization)
			if err != nil {
				logger.Error.Println(err)
				return "", err
			}
		}
	}
	return job.Id, nil
}

// 库表推送json生成
func AccPushCreateJson(sp *source.NewAcc, targetFields []string, db *metadata.AccDbInfo, datastrategy int) (string, string, error) {
	var templete string
	var setting string
	var err error

	// fmt.Println("db: ", db)

	setting = common.Setting

	readercreate := func(sp *source.NewAcc, db *metadata.AccDbInfo) string {
		reader := common.Reader
		reader, _ = sjson.Set(reader, "parameter.username", db.NormalConn.DBUser)
		reader, _ = sjson.Set(reader, "parameter.password", transPassword(db.NormalConn.DBPassword))
		if sp.Tasktype == 4 {
			reader, _ = sjson.Set(reader, "parameter.splitPk", "")
		}
		if sp.Tasktype != 4 && len(sp.Extractdb.Mainkey) != 0 {
			reader, _ = sjson.Set(reader, "parameter.splitPk", sp.Extractdb.Mainkey[0])
		}
		return reader
	}

	// writer
	writerjson, dbpath, err := PushWriterJSON(sp, targetFields, datastrategy)
	//fmt.Println("==============writerjson:", writerjson)
	if err != nil {
		return "", "", err
	}

	if db.DBType == "hive" || db.DBType == common.Inceptor {
		templete, err = CommonHive(sp, db, writerjson)
		if err != nil {
			fmt.Println("hive reader err:", err.Error())
			logger.Error.Println("hive reader err:", err.Error())
			return templete, dbpath, err
		}
		return templete, dbpath, nil
	}

	if db.DBType == common.ODPS {
		templete, err = CommonOdpsReader(sp, db, writerjson)
		if err != nil {
			fmt.Println("odps reader err:", err.Error())
			logger.Error.Println("odps reader err:", err.Error())
			return templete, dbpath, err
		}
		return templete, dbpath, nil
	}

	if db.DBType == "stork" || db.DBType == "teryx" || db.DBType == "gaussdb" || db.DBType == "postgres" ||
		db.DBType == "greenplum" || db.DBType == "gaussdba" {
		storkreader := readercreate(sp, db)
		storkreader, _ = sjson.Set(storkreader, "name", "postgresqlreader")
		storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.jdbcUrl.0", fmt.Sprintf("jdbc:postgresql://%s:%s/%s", db.NormalConn.DBIP, db.NormalConn.DBPort, db.NormalConn.Database))
		sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, `\`, `\\`)
		storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
		//-----------------------------------------------------------
		if sp.Extractdb.Createfield != "" {
			var istime, isdate bool
			var isint bool
			for i, v := range sp.Sourcedb.Field {
				if v == sp.Extractdb.Createfield {
					if strings.Contains(strings.ToUpper(sp.Sourcedb.Fieldtype[i]), "DATE") {
						isdate = true
					}
					if strings.Contains(strings.ToUpper(sp.Sourcedb.Fieldtype[i]), "TIMESTAMP") {
						istime = true
					}
					if strings.Contains(strings.ToUpper(sp.Sourcedb.Fieldtype[i]), "BIGINT") {
						isint = true
					}
					break
				}
			}
			//抽取脏数据----------------------------------------------------------------
			var dirtydata string
			var nodirtytrans string
			var nodirtyreader string
			var nodirtytemp string
			//*第一次抽取不加入初始时间
			var firsttimetrans string
			//fmt.Println("是否支持脏数据:", sp.Extractdb.Supportdirtydata)
			//if sp.Extractdb.Supportdirtydata == true {
			//	switch sp.Extractdb.Timetype {
			//	case "yyyyMMdd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{8}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy-MM-dd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy/MM/dd":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy-MM-dd HH:mm:ss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyy/MM/dd HH:mm:ss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	case "yyyyMMddHHmmss":
			//		dirtydata = fmt.Sprintf(` or \"%s\" !~ '^[0-9]{14}$' or \"%s\" is null `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			//	default:
			//		dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			//	}
			//	//脏数据
			//	if isint {
			//		dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			//	}
			//}
			var timetrans string
			//fmt.Println(sp.Extractdb.Timetype)
			//var starttime, endtime string
			//switch db.DBType {
			//case "greenplum", "teryx":
			//	if len(sp.Extractdb.Timetype) > 10 {
			//		starttime = `substring('${starttime}' from 1 for 8)||' '||substring('${starttime}' from 9 for 6)`
			//		endtime = `substring('${endtime}' from 1 for 8)||' '|| substring('${endtime}' from 9 for 6)`
			//	} else {
			//		starttime = `substring('${starttime}' from 1 for 8)`
			//		endtime = `substring('${endtime}' from 1 for 8)`
			//	}
			//default:
			//	if len(sp.Extractdb.Timetype) > 10 {
			//		starttime = `concat(left('${starttime}',8),' ',right('${starttime}',6))`
			//		endtime = `concat(left('${endtime}',8),' ',right('${endtime}',6))`
			//	} else {
			//		starttime = `left('${starttime}',8)`
			//		endtime = `left('${endtime}',8)`
			//	}
			//}

			switch sp.Extractdb.Timetype {
			case "yyyyMMdd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_ori, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_ori, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyyMMdd")
			case "yyyy-MM-dd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_hyphen, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_hyphen, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyy-MM-dd")
			case "yyyy/MM/dd":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, common.Regex_ymd_slash, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, common.Regex_ymd_slash, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", "yyyy/MM/dd")
			case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_hyphen)
			case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_slash)
			case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
				timetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}' and  \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" ~ '%s'  and   \"%s\" < '${endtime}'`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_col_origin)
			case common.Incre_Autoid:
				timetrans = fmt.Sprintf(` \"%s\" > '${starttime}' `, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(`  \"%s\" is not null `, sp.Extractdb.Createfield)
				storkreader, _ = sjson.Set(storkreader, "parameter.increColFormat", common.Incre_Autoid)
				storkreader, _ = sjson.Set(storkreader, "parameter.firstDataCollection", true)
			}

			if isdate {
				timetrans = fmt.Sprintf(` \"%s\" > cast('${starttime}' as date)  and \"%s\" <  cast('${endtime}' as date)       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(` \"%s\" < cast('${endtime}' as date) and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			}

			if istime {
				timetrans = fmt.Sprintf(` \"%s\" > cast('${starttime}' as timestamp)  and \"%s\" <  cast('${endtime}' as timestamp)       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
				firsttimetrans = fmt.Sprintf(` \"%s\" < cast('${endtime}' as timestamp) and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			}

			//语句处理
			if isint {
				timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
				firsttimetrans = " 1=1 "
			}

			//加入脏数据处理
			timetrans = timetrans + dirtydata
			firsttimetrans = firsttimetrans + dirtydata
			nodirtytrans = timetrans

			if sp.Extractdb.Wherecontent != "" {
				sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "
			}
			sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
			storkreader, _ = sjson.Set(storkreader, "parameter.firstDataCollection", true)
			storkreader, _ = sjson.Set(storkreader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
			increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + firsttimetrans
			storkreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", increWhereSql)
			//storkreader, _ = sjson.Set(storkreader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
			nodirtyIncreSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + nodirtytrans
			nodirtyreader, _ = sjson.Set(storkreader, "parameter.connection.0.querySql.0", nodirtyIncreSql)
			//nodirtyreader, _ = sjson.Set(storkreader, "parameter.where", sp.Extractdb.Wherecontent+nodirtytrans)
			templete := fmt.Sprintf(`{
	    "job": {
			"setting":%s,
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer":
					 %s
	            }
	        ]
	    }
	}`, setting, storkreader, writerjson)
			//-----------------------------
			//拿到不脏的template
			// nodirtytemp := strings.Replace(templete, dirtydata, "", 1)
			nodirtytemp = fmt.Sprintf(`{
	       "job": {
			"setting":%s,
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer":
					 %s
	            }
	        ]
	    }
	}`, setting, nodirtyreader, writerjson)

			//---------------------------------------------------

			templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)

			return templete, dbpath, nil
		}

		//===============================================================
		//以下为没有增量字段
		templete = fmt.Sprintf(`{
	    "job": {
			"setting":%s,
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer":
					 %s
	            }
	        ]
	    }
	}`, setting, storkreader, writerjson)

		return templete, dbpath, nil
	}

	return templete, dbpath, nil
}

func HiveSh(sp *source.NewAcc, way, dbname, jobid string, Authorization string) (string, error) {
	var shellPath string
	uuid := uuid.NewV4().String()
	hiveVersion := sp.Sourcedb.HiveVersion
	var err error
	var shellContent string
	//hive单例抽取
	if len(sp.Extractdb.Batchtables) == 0 {
		fmt.Println("告诉我他的cmd是什么东西")
		fmt.Println(sp.Cmd)
		execCmd := fmt.Sprintf("%s", sp.Cmd)
		if hiveVersion == common.HiveTDC {
			execCmd = sp.Cmd
			// fmt.Println("execCmd:", execCmd)
			switch sp.TDCLoad {
			case common.TDCLoadExtract:
				execCmd, err = TDCLoadCmd(sp, way)
				if err != nil {
					return "", fmt.Errorf("新建tdc落地抽取脚本失败:%v", err.Error())
				}
			default:
			}
			logger.Info.Println("execCmd:", execCmd)
		}
		// 如果是增量抽取则不删除老数据
		if sp.Extractdb.Createfield != "" {
			pre := strings.Index(sp.Cmd, " ")
			suf := strings.LastIndex(sp.Cmd, "/")
			path := sp.Cmd[pre+1 : suf]
			cmd := sp.Cmd[suf+1:]
			execCmd = fmt.Sprintf("python %s/%s  $first $alladd notruncate", path, cmd)
		}

		//单纯的cmd
		abspath := strings.Replace(strings.Replace(sp.Cmd, "python", "", 1), "/opt/dana/datax/bin/datax.py", "", 1)
		pystr := fmt.Sprintf("first=`python /etc/danastudio/gettimes.py %s`", jobid)
		if sp.Extractdb.Createfield != "" {
			//有增量的单例
			shellContent = fmt.Sprintf(`
set -e 
 
first='no' 
%s
echo $first 
alladd=-1 
#### 
%s`, pystr, execCmd)

		} else {
			if sp.Tasktype == 4 { //实时抽取
				shellContent = fmt.Sprintf(`
set -e 
 
python /opt/dana/datax/bin/datax.py %s
alladd=-1 
#### 
%s`, abspath, "")
			} else { //就是单纯的单例
				shellContent = fmt.Sprintf(`
set -e 
 
first='no' 
%s
echo $first 
if [ $first = "yes" ];then
sed -i s/append_notuse/truncate/g %s
python /opt/dana/datax/bin/datax.py %s
#sed -i s/truncate/append/g %s
else
sed -i s/append_notuse/truncate/g %s
python /opt/dana/datax/bin/datax.py %s
fi
alladd=-1 
#### 
%s`, pystr, abspath, abspath, abspath, abspath, abspath, "")
			}
		}

		// fmt.Println("shellContent:", shellContent)
		shellPath = common.UNSUBMITTED + "/" + uuid + "_" + sp.Sourcedb.Table + "_hive"
		err = GenerateFile(shellContent, shellPath, Authorization)
		if err != nil {
			return "", err
		}
		err = WriteFile(shellPath, shellContent)
		if err != nil {
			return "", err
		}
	}
	fmt.Println("shelContent:", shellContent)
	_, err = os.Stat(path.Dir(shellPath))
	if err != nil {
		os.MkdirAll(path.Dir(shellPath), os.ModePerm)
	}
	dstFile, err := os.OpenFile(shellPath, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0777)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	dstFile.WriteString(shellContent)
	defer dstFile.Close()
	hiveCmd := fmt.Sprintf(`%s`, shellPath)
	fmt.Println("cmd:", hiveCmd)
	return hiveCmd, nil
}

func StorkGetScript(temp string, extractEngineID string) string {
	temp = strings.Replace(temp, `\"`, `\\\"`, -1)
	firstcontent := temp
	secondcontent := temp
	//strings.Replace(temp, `\`, `\\\`, -1)
	template := fmt.Sprintf(
		`# -*- coding:utf-8 -*-
import os
import commands
import signal
import random
import string
import requests
import json
import time
import datetime
import sys
import re
import subprocess
import ConfigParser

forevertoken='Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'


#信号返回
def handle_SIGUSR1(signum, frame):
    exit(2)
def writefile(content):    
    ran = ''.join(random.sample(string.ascii_letters + string.digits, 6))
    path='/etc/danastudio/'+ran
    with open(path,"w") as file:
        file.write(content)
    return path
#看是否是第一次运行
def isfirst(id):
    runurl="http://127.0.0.1:10100/dodox_jobmanager_job/tb_job/_search"
    datas='{"query":{"bool":{"must":{"term":{"label.id":"%%s"}}}}}'%%(id)
    #print datas
    response=requests.post(runurl,data=datas,auth=('eagles','datatom.com'))
    code=response.status_code
    if code==200:
        z=response.text
        if json.loads(z)['hits']['total']==0:
            return "yes"

        dict1=json.loads(z)['hits']['hits'][0]['_source']
        #onlinetime=dict1['onlinetime']
        lastsuccesstime=dict1.get('lastsuccesstime', '0')
        if  lastsuccesstime != '0' :
            return "no"
        else:
            return "yes"
    else:
        print '请求信息为：'+ datas
        print '请求url为：'+ runurl
        print '判定失败，ES异常,请检查ES环境，错误信息如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)

def normalize_id(id):
    return re.sub(r'(\.datax)...$', r'\1', id)

def isclean(id):
    id=normalize_id(id)
    runurl="http://127.0.0.1:10100/dodox_jobmanager_job/tb_job/_search"
    datas='{"query":{"bool":{"must":{"term":{"label.id":"%%s"}}}}}'%%(id)
    #print datas
    response=requests.post(runurl,data=datas,auth=('eagles','datatom.com'))
    code=response.status_code
    if code==200:
        z=response.text
        dict1=json.loads(z)['hits']['hits'][0]['_source']
        #onlinetime=dict1['onlinetime']
        clean=dict1.get('accincrestatus', '0').get('isdeletesoutb',False)

        if clean == False :
            return False
        else:
            return True
    else:
        print '请求信息为：'+ datas
        print '请求url为：'+ runurl
        print '判定失败，ES异常,请检查ES环境，错误信息如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)


def getnginxport():
    config = ConfigParser.ConfigParser()
    config.read('/etc/dt.d/danastudio/danastudio-platform.conf')
    return config.get('server', 'nginxport')

def checkextract(id):
    head={'Authorization':'%%s'%%forevertoken}
    runurl="http://127.0.0.1:"+getnginxport()+"/danastudio/platform/engines/health/check"
    datas='{"engineid":"%%s"}'%%(id)
    response=requests.post(runurl,data=datas,headers=head)
    code=response.status_code
    if code==200:
        return True
    else:
        print '请求信息为：'+ datas
        print '请求url为：'+ runurl
        print '判定失败，检测抽取源端连接失败，错误信息如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)




# 实时输出但不可显示彩色，可以返回结果
def sh(command, print_msg=True):
    p = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
    lines = []
    for line in iter(p.stdout.readline, b''):
        line = line.rstrip()
        if print_msg:
            print line
        lines.append(line)
    return lines


i=sys.argv[0].split('/',-1)
id=i[len(i)-1]
print '判定任务类型为：单例抽取'
print '开始进行单例抽取任务...'
print '1.获取任务id：'
print '该任务的id为%%s'%%(id)
print '2.判定单例任务是否是第一次运行：'
first=''


content='''%s'''
#看是否是第一次
first=isfirst(id)
clean=isclean(id)

print '判断结果为：%%s'%%(first)
print '3.判定增量任务是否需要在第一次运行时清空目的表数据：'
print '判断结果为：%%s'%%(clean)


if first=="no":
    content='''%s'''
if first=="yes" and clean:
    print "清空目的表数据"
    content=content.replace("-- clean ","delete from")
else:
    content=content.replace("-- clean ","")
path=writefile(content)


extract_engineId='%s'
print '4.检测抽取源端连接是否正常：'
checkflag=checkextract(extract_engineId)
print '判断结果为：%%s'%%(checkflag)

cmd='python /opt/dana/datax/bin/datax.py %%s '%%(path) 
print '3.执行单例抽取命令:'
print '命令如下：'
print cmd
sys.stdout.flush()
#执行并删除文件
#val = commands.getoutput(cmd)
val = sh(cmd, True)
valstr = "".join(val)
os.remove(path)

if '任务总计耗时' in valstr:
    print '执行成功,详细的datax日志如上'
    #print val 
    print '单例抽取任务已执行结束，任务成功！'
else:
    print '执行失败,相关错误日志信息如上'
    #print val
    print '单例抽取任务已执行结束，但是执行失败，请检查相关配置或联系运维人员！'
    signal.signal(signal.SIGUSR1, handle_SIGUSR1)
    os.kill(os.getpid(), signal.SIGUSR1)
`, firstcontent, secondcontent, extractEngineID)
	return template
}

// WriterJSON 抽取任务构建datax writer json结构体
// stork-stork,teryx-teryx,hive-stork,gaussdb-gaussdb
func WriterJSON(sp *source.NewAcc, batch bool, datastrategy int) (string, string, string, error) {
	writerjson, dbpath := "", ""
	var err error
	var analyzeSql string
	switch sp.Sourcedb.Dbtype {
	case "stork", "teryx", "gaussdb", "greenplum", "postgres", "gaussdba", common.UXDB:
		writerjson, analyzeSql = storkWriterJSON(sp, datastrategy)
	case common.DRealDB:
		writerjson, analyzeSql = access.DRealdbWriterJSON(sp)
	case "hive":
		switch sp.Sourcedb.HiveVersion {
		case common.HiveTDC:
			if sp.TDCLoad == common.TDCLoadExtract {
				writerjson, dbpath, err = access.TDCLoadWriterJSON(sp.Sourcedb.Table)

			} else {
				writerjson, err = access.TDCWriterJSON(sp)
			}
		default:
			fmt.Println("-------------------------------hive.sp.Sourcedb.Field-----------------------------------------")
			fmt.Println(sp.Sourcedb.Field)
			fmt.Println(sp.Sourcedb.PartitionField)
			writerjson, dbpath, analyzeSql, err = access.HiveWriterJSON(sp)
			if err != nil {
				return writerjson, dbpath, analyzeSql, err
			}
		}
		if datastrategy == 1 {
			writerjson, _ = sjson.Set(writerjson, "parameter.writeMode", "truncate")
		}
	case common.Inceptor:
		writerjson, dbpath, analyzeSql, err = access.TdhWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
		if datastrategy == 1 {
			writerjson, _ = sjson.Set(writerjson, "parameter.writeMode", "truncate")
		}
	case common.DMDB:
		sp.Datastrategy = datastrategy
		writerjson, analyzeSql = access.DaMengWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
		//if datastrategy == 1 {
		//	writerjson, _ = sjson.Set(writerjson, "parameter.writeMode", "truncate")
		//}
	// case common.ODPS:
	// 	writerjson, analyzeSql, err = odpsWriterJSON(sp)
	// 	if err != nil {
	// 		return writerjson, dbpath, analyzeSql, err
	// 	}
	// case "hive":
	// 	fmt.Println("unsupportted type")
	// 	return "", "", "", errors.New("抽取落地任务暂不支持hive版本落地")
	default:
	}
	return writerjson, dbpath, analyzeSql, nil
}

func PushWriterJSON(sp *source.NewAcc, targetFields []string, datastrategy int) (string, string, error) {

	writerjson, dbpath := "", ""
	//var err error
	var writer string
	var tableName string
	var quotes string

	switch sp.Sourcedb.Dbtype {
	case "mysql":
		writer = "mysqlwriter"
		if sp.DBVersion == "8.x" || strings.ToUpper(sp.DBVersion) == "V8" {
			writer = "mysql8writer"
		}

		tableName = sp.Sourcedb.Table
		quotes = "`"
	case "SQL server":
		writer = "sqlserverwriter"

		if sp.Extractdb.Createfield != "" {
			quotes = "\\\""
		} else {
			quotes = "\""
		}
		tableName = quotes + sp.Sourcedb.Schema + quotes + "." + quotes + sp.Sourcedb.Table + quotes
	case "stork", "postgres", "postgressql":
		writer = "postgresqlwriter"

		if sp.Extractdb.Createfield != "" {
			quotes = "\\\""
		} else {
			quotes = "\""
		}
		tableName = quotes + sp.Sourcedb.Schema + quotes + "." + quotes + sp.Sourcedb.Table + quotes
		//fmt.Sprintf("*****************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
		//	sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema)
	case "oracle":
		writer = "oraclewriter"

		if sp.Extractdb.Createfield != "" {
			quotes = "\\\""
		} else {
			quotes = "\""
		}
		tableName = quotes + sp.Sourcedb.Schema + quotes + "." + quotes + sp.Sourcedb.Table + quotes
	default:
		return "", "", errors.New("库表推送暂不支持此类型的数据库")
	}
	writerjson = pushWriterJSON(sp, targetFields, writer, tableName, quotes)
	if datastrategy == 1 {
		writerjson, _ = sjson.Set(writerjson, "parameter.preSql.0", "delete from @table")
	} else if sp.Extractdb.AccIncreStatus.DeleteSouTb {
		writerjson, _ = sjson.Set(writerjson, "parameter.preSql.0", "-- clean ")
	} else {
		writerjson, _ = sjson.Set(writerjson, "parameter.preSql.0", "")
	}
	//oracle数据库的datax不支持--delete语法
	return writerjson, dbpath, nil
}

func escape(t string) string {
	return strings.Replace(t, `\`, `\\`, -1)
}

// CommonHive hive抽取源单例抽取处理
func CommonHive(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	fmt.Println("--------------------------hive 拼接")
	var templete string
	// var setting string
	// if common.ErrorLimit != 0 {
	// 	setting, _ = sjson.Set(common.Setting, "errorLimit.record", common.ErrorLimit)
	// } else {
	// 	setting = common.Setting
	// }

	var setting = sp.AdvancedConfig.Setting
	reader := common.HiveReaderV2
	username := db.HiveConn.HiveUser
	if username == "" {
		username = "hive"
	}
	password := db.HiveConn.HivePassword
	if password == "" {
		password = common.DefaultPwd
	}
	reader, _ = sjson.Set(reader, "name", "hivereader")
	reader, _ = sjson.Set(reader, "parameter.username", username)
	reader, _ = sjson.Set(reader, "parameter.password", transPassword(password))
	// hive reader 添加驱动的路径
	reader, _ = sjson.Set(reader, "parameter.driverJarPath", db.HiveConn.DriveInfo.DriverPath)
	// ---------------end------------------
	if len(sp.Extractdb.Mainkey) != 0 {
		reader, _ = sjson.Set(reader, "parameter.splitPk", sp.Extractdb.Mainkey[0])
	}
	reader, _ = sjson.Set(reader, "parameter.connection.0.jdbcUrl.0", db.HiveConn.HiveURL)
	reader, _ = sjson.Set(reader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
	if db.HiveConn.AuthMethod == 2 {
		// 带kerberos认证
		reader, _ = sjson.Set(reader, "parameter.haveKerberos", true)
		reader, _ = sjson.Set(reader, "parameter.kerberosKeytabFilePath", db.HiveConn.KeytabFilePath)
		reader, _ = sjson.Set(reader, "parameter.kerberosPrincipal", db.HiveConn.Principal)
		reader, _ = sjson.Set(reader, "parameter.krb5ConfigFilePath", db.HiveConn.Krb5ConfPath)
	}
	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
             %s
			"job": {
					%s
				"content": [
					{
						"reader": 
						 %s, 
						"writer": 
						 %s
					}
				]
			}
		}`, sp.AdvancedConfig.Core, setting, reader, writerjson)
		return templete, nil
	}
	// 增量抽取
	// 是否符合标准日期格式
	var istime, isint bool
	var z int

	if len(sp.Extractdb.Field) > 0 {
		for i, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				z = i
				break
			}
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") || strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATE") {
			istime = true
		}
		//是否是整型的增量
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") {
			isint = true
		}
	}
	// 增量字段--关键字/非标准字符处理
	sp.Extractdb.Createfield = strings.Replace(sp.Extractdb.Createfield, "`", "", -1)
	sp.Extractdb.Createfield = fmt.Sprintf("`%s`", sp.Extractdb.Createfield)
	// 脏数据处理
	var dirtydata string
	if sp.Extractdb.Supportdirtydata == true {
		// 字符串类型-增量脏数据处理
		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{8}$' or %s is null`,
				sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' or %s is null`,
				sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' or %s is null`,
				sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null`,
				sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null`,
				sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null `,
				sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		default:
			// 时间类型date/timestamp--脏数据处理
			dirtydata = fmt.Sprintf(`  or %s is null  `, sp.Extractdb.Createfield)
		}
		//脏数据
		if isint {
			dirtydata = fmt.Sprintf(`  or %s is null  `, sp.Extractdb.Createfield)
		}
	}
	// 增量处理
	// 抽取策略--不包含第一次抽取
	var timetrans string
	// 第一次抽取策略
	var firsttimetrans string
	// 变量说明(从前往后)：时间类型、任务开始时间转换、任务结束时间转换、时间正则
	// DS v5.5.2 autoid删除 任务结束时间endDate限制
	var timeType, startDate, addDate, endDate, timeExp string
	timeType = sp.Extractdb.Timetype
	// 转换变量处理
	switch timeType {
	// 字符串类型
	case "yyyyMMdd", "yyyy-MM-dd", "yyyy/MM/dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]",
		"yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]", "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
		addDate = fmt.Sprintf("%s", sp.Extractdb.Createfield)
	case common.Incre_Autoid:
		startDate = `'${starttime}'`
		addDate = fmt.Sprintf("%s", sp.Extractdb.Createfield)
		reader, _ = sjson.Set(reader, "parameter.firstDataCollection", true)
	default:
		// 时间类型
		startDate = ` cast('${starttime}' as timestamp)`
		endDate = ` cast('${endtime}' as timestamp) `
		addDate = fmt.Sprintf("%s", sp.Extractdb.Createfield)
		//addDate = fmt.Sprintf("cast(%s as timestamp) ", sp.Extractdb.Createfield)
		//startDate = fmt.Sprintf("cast('${starttime}' as timestamp)")
		//endDate = fmt.Sprintf("cast('${endtime}' as timestamp)")
		//if strings.Contains(strings.ToUpper(addFieldType), "DATE") {
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss')-86400)")
		//	endDate = fmt.Sprintf("date_format(%s, 'yyyyMMddHHmmss')", endDate)
		//}
		//case "yyyyMMdd":
		//	addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyyMMdd'), 'yyyy-MM-dd')", sp.Extractdb.Createfield)
		//	startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		//	endDate = fmt.Sprintf("date_add(%s, -1)", endDate)
		//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyyMMdd")
		//case "yyyy-MM-dd":
		//	addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy-MM-dd'), 'yyyy-MM-dd')", sp.Extractdb.Createfield)
		//	startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		//	endDate = fmt.Sprintf("date_add(%s, -1)", endDate)
		//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy-MM-dd")
		//case "yyyy/MM/dd":
		//	addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy/MM/dd'), 'yyyy-MM-dd')", sp.Extractdb.Createfield)
		//	startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		//	endDate = fmt.Sprintf("date_add(%s, -1)", endDate)
		//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy/MM/dd")
		//case "yyyy-MM-dd HH:mm:ss":
		//	addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy-MM-dd HH:mm:ss'), 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		//	startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy-MM-dd HH:mm:ss")
		//case "yyyy/MM/dd HH:mm:ss":
		//	addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy/MM/dd HH:mm:ss'), 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		//	startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy/MM/dd HH:mm:ss")
		//case "yyyyMMddHHmmss":
		//	addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyyMMddHHmmss'), 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		//	startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyyMMddHHmmss")
		//default:
		//	// 时间类型
		//	addDate = fmt.Sprintf("date_format(regexp_replace(cast(%s as string), '/', '-') , 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		//	startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyyMMddHHmmss")
		//	if strings.Contains(strings.ToUpper(addFieldType), "DATE") {
		//		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss')-86400)")
		//		endDate = fmt.Sprintf("date_format(%s, 'yyyyMMddHHmmss')", endDate)
		//	}
	}
	// 时间正则处理
	switch timeType {
	case "yyyyMMdd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{8}$' ", sp.Extractdb.Createfield)
	case "yyyy-MM-dd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyy/MM/dd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timeExp = fmt.Sprintf(` %s regexp '%s' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen))
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timeExp = fmt.Sprintf(` %s regexp '%s' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash))
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timeExp = fmt.Sprintf(` %s regexp '%s' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin))
	default:
		timeExp = fmt.Sprintf(" %s is not null", sp.Extractdb.Createfield)
	}
	var timeExpStr = timeExp + " and "
	switch timeType {
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timeType = common.Incre_col_hyphen
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timeType = common.Incre_col_slash
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timeType = common.Incre_col_origin
		//case common.Incre_Autoid: //自动识别类型，无需改动参数值
		//	timeType = common.Incre_Autoid
	}
	reader, _ = sjson.Set(reader, "parameter.increColFormat", timeType)
	if istime || timeExp == "" {
		// 时间类型不包含正则
		timeExpStr = ""
		reader, _ = sjson.Set(reader, "parameter.increColFormat", "")
	}
	if timeType == common.Incre_Autoid {
		timetrans = fmt.Sprintf(" %s %s >%s ", timeExpStr, addDate, startDate)
		firsttimetrans = fmt.Sprintf(" %s ", timeExp)
	} else {
		timetrans = fmt.Sprintf(" %s %s >%s and %s<%s  ", timeExpStr, addDate, startDate, addDate, endDate)
		firsttimetrans = fmt.Sprintf(" %s  %s<%s  ", timeExpStr, addDate, endDate)
	}
	//语句处理
	if isint {
		timetrans = fmt.Sprintf(` %s >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}

	firsttimetrans = firsttimetrans + dirtydata
	if sp.Extractdb.Wherecontent != "" {
		sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "
	}
	reader, _ = sjson.Set(reader, "parameter.firstDataCollection", true)
	reader, _ = sjson.Set(reader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
	// 第一次抽取reader
	increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + firsttimetrans
	reader, _ = sjson.Set(reader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + timetrans
	nodirtyreader, _ := sjson.Set(reader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
		%s
	   "job": {
			   %s
		   "content": [
			   {
				   "reader": 
				   %s, 
				   "writer": 
				   %s
			   }
		   ]
	   }
	   }`, sp.AdvancedConfig.Core, setting, reader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
	   %s
	   "job": {
			   %s
		   "content": [
			   {
				   "reader": 
				   %s, 
				   "writer": 
				   %s
			   }
		   ]
	   }
	   }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}

func GetCreatedata(reader, dbname, tablename, createfield, timetype, nodirtyreader, extractEngineId string) string {

	template := fmt.Sprintf(
		`# -*- coding:utf-8 -*-
import os
import commands
import signal
import random
import string
import requests
import json
import time
import datetime
import sys
import re
import ConfigParser
import subprocess


forevertoken='Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
#拿到表中的最大时间
maxtime=''
accmaxtime=''
timetp=''

#信号返回
def handle_SIGUSR1(signum, frame):
    exit(2)
def writefile(content):    
    ran = ''.join(random.sample(string.ascii_letters + string.digits, 6))
    path='/etc/danastudio/'+ran
    with open(path,"w") as file:
        file.write(content)
    return path
    


def remax(cont):
    pattern = re.compile("max value:\[[\w\W]*?\]")
    regresult = pattern.findall(cont)
    print '正则匹配的结果:'
    print regresult
    if len(regresult)!=0:
        return regresult[len(regresult)-1]
    return ''

def getnginxport():
    config = ConfigParser.ConfigParser()
    config.read('/etc/dt.d/danastudio/danastudio-platform.conf')
    return config.get('server', 'nginxport')

def setmax(id,max):
    head={'Authorization':'%%s'%%forevertoken}
    runurl="http://127.0.0.1:"+getnginxport()+"/danastudio/ants/inside/setjobmax"
    max=max.replace('#','').replace('max value:[','').replace(']','')
    datas='{"id":"%%s","maxtime":"%%s"}'%%(id,max)
    response=requests.post(runurl,data=datas,headers=head)
    code=response.status_code
    return code,response

def getmax(id):
    head={'Authorization':'%%s'%%forevertoken}
    runurl="http://127.0.0.1:"+getnginxport()+"/danastudio/ants/inside/getjobmax"
    datas='{"id":"%%s"}'%%(id)
    response=requests.post(runurl,data=datas,headers=head)
    code=response.status_code
    return code,response   

def isfirst(id):
    runurl="http://127.0.0.1:10100/dodox_jobmanager_job/tb_job/_search"
    datas='{"query":{"bool":{"must":{"term":{"label.id":"%%s"}}}}}'%%(id)
    #print datas
    response=requests.post(runurl,data=datas,auth=('eagles','datatom.com'))
    code=response.status_code
    if code==200:
        z=response.text
        dict1=json.loads(z)['hits']['hits'][0]['_source']
        #onlinetime=dict1['onlinetime']
        lastsuccesstime=dict1.get('lastsuccesstime', '0')
        if  lastsuccesstime != '0' :
            return "no"
        else:
            return "yes"
    else:
        print '请求信息为：'+ datas
        print '请求url为：'+ runurl
        print '判定失败，ES异常,请检查ES环境，错误信息如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)


def normalize_id(id):
    return re.sub(r'(\.datax)...$', r'\1', id)

def isclean(id):
    id=normalize_id(id)
    runurl="http://127.0.0.1:10100/dodox_jobmanager_job/tb_job/_search"
    datas='{"query":{"bool":{"must":{"term":{"label.id":"%%s"}}}}}'%%(id)
    #print datas
    response=requests.post(runurl,data=datas,auth=('eagles','datatom.com'))
    code=response.status_code
    if code==200:
        z=response.text
        dict1=json.loads(z)['hits']['hits'][0]['_source']
        #onlinetime=dict1['onlinetime']
        clean=dict1.get('accincrestatus', '0').get('isdeletesoutb',False)

        if clean == False :
            return False
        else:
            return True
    else:
        print '请求信息为：'+ datas
        print '请求url为：'+ runurl
        print '判定失败，ES异常,请检查ES环境，错误信息如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)


def checkextract(id):
    head={'Authorization':'%%s'%%forevertoken}
    runurl="http://127.0.0.1:"+getnginxport()+"/danastudio/platform/engines/health/check"
    datas='{"engineid":"%%s"}'%%(id)
    response=requests.post(runurl,data=datas,headers=head)
    code=response.status_code
    if code==200:
        return True
    else:
        print '请求信息为：'+ datas
        print '请求url为：'+ runurl
        print '判定失败，检测抽取源端连接失败，错误信息如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)



# 实时输出但不可显示彩色，可以返回结果
def sh(command, print_msg=True):
    p = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
    lines = []
    for line in iter(p.stdout.readline, b''):
        line = line.rstrip()
        if print_msg:
            print line
        lines.append(line)
    return lines

def get_now_format(format):
	format=format.lower()
	now_time=datetime.datetime.now()
	str_format=""
	if format=="yyyymmdd":
		str_format=now_time.strftime('%%Y%%m%%d')
	if format=="yyyy/mm/dd":
		str_format=now_time.strftime('%%Y/%%m/%%d')
	if format=="yyyy-mm-dd":
		str_format=now_time.strftime('%%Y-%%m-%%d')
	if format=="yyyymmddhhmmss[.fff]" or format=="yyyymmddhhmmss":
		str_format=now_time.strftime('%%Y%%m%%d%%H%%M%%S.%%f')[:-3]
	if format=="yyyy-mm-dd hh:mm:ss[.fff]" or format=="" or format=="yyyy-mm-dd hh:mm:ss":
		str_format=now_time.strftime('%%Y-%%m-%%d %%H:%%M:%%S.%%f')[:-3]
	if format=="yyyy/mm/dd hh:mm:ss[.fff]" or format=="yyyy/mm/dd hh:mm:ss":
		str_format=now_time.strftime('%%Y/%%m/%%d %%H:%%M:%%S.%%f')[:-3]
	if format=="timestamp" or format=="datetime":
		str_format=now_time.strftime('%%Y-%%m-%%d %%H:%%M:%%S.%%f')[:-3]
	if format=="date":
		str_format=now_time.strftime('%%Y-%%m-%%d')
	return str_format 

def timeformat(str,format):
	if str=='':
		return str
	if 'isint' in str:
		return str.replace('isint','')
	return str
	res=''
	if format=="timestamp" or format=="datetime":
		str=str.replace('-','').replace('/','').replace(':','').replace(' ','').replace('T','')
	if format=="date":
		str=str.replace('-','').replace('/','').replace(':','').replace(' ','').replace('T','')[:8]
		str=str+"000000"
	return str
	print '转换前的时间格式%%s'%%(str)
	if len(str)==8:
		return str+"000000"
	if len(str)==14:
		return str
	if len(str)>14:
		res=str[:14]
	print '转换后的时间格式%%s'%%(res)
	return res 

def timetype(id):
    runurl="http://127.0.0.1:10100/danastudio_ants_lab/tb_access/_search"
    datas='{"query":{"bool":{"must":{"term":{"id":"%%s"}}}}}'%%(id)
    #print datas
    response=requests.post(runurl,data=datas,auth=('eagles','datatom.com'))
    code=response.status_code
    if code==200:
        z=response.text
        timetype=json.loads(z)['hits']['hits'][0]['_source']['extractdb']['timetype']
        return timetype
    else:
        print '请求信息为：'+ datas
        print '请求url为：'+ runurl
        print '查询时间类型失败，ES异常,请检查ES环境，错误信息如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)



i=sys.argv[0].split('/',-1)
id=i[len(i)-1]
print '判定任务类型为：增量抽取'
print '开始进行增量抽取任务...'
print '1.获取任务id：'
print '该任务的id为%%s'%%(id)
print '2.判定增量任务是否是第一次运行：'

first=''
clean=False
alladd='stork_cl'

content='''%s'''
if len(sys.argv)==4:
    if sys.argv[3]=="truncate":
        content=content.replace("append", "truncate",1)
#看是否是第一次
if len(sys.argv)>=3:
    first=sys.argv[1]
    alladd=sys.argv[2]
    clean=isclean(id)
else:
    #print '判断是否第一次'
    first=isfirst(id)
    clean=isclean(id)
   
print '判断结果为：%%s'%%(first)
print '3.判定增量任务是否需要在第一次运行时清空目的表数据：'
print '判断结果为：%%s'%%(clean)


extract_engineId='%s'
print '4.检测抽取源端连接是否正常：'
checkflag=checkextract(extract_engineId)
print '判断结果为：%%s'%%(checkflag)





#-----------------------------------------------------------
print '5.获取增量时间和任务时间:'
code,response=getmax(id)
if code==200 and json.loads(response.text)['code']==200 :
    maxtime=json.loads(response.text)['result']['maxtime'].replace('#','').replace('max value:[','').replace(']',"")
    accmaxtime=json.loads(response.text)['result']['accmaxtime'].replace('#','').replace('max value:[','').replace(']',"")
    timetp=timetype(id)
    if timetp != "":
        print '增量时间类型为:%%s'%%(timetp)
    if len(timetp)!=len(accmaxtime):
        accmaxtime=''
    maxtime=timeformat(maxtime,timetp) 
    accmaxtime=timeformat(accmaxtime,timetp)   
    #print maxtime
    #print '--------'
    #print accmaxtime
else:
    print '调用查询最大时间接口失败,请检查ES环境,错误详情如下：'
    print response.text
    signal.signal(signal.SIGUSR1, handle_SIGUSR1)
    os.kill(os.getpid(), signal.SIGUSR1)
#-------------------------------------------------------------
#如果不是第一次运行并且maxtime不为空 
#if (first=="no" and maxtime !="") or (alladd=='-1' and accmaxtime !="" and maxtime =="")  or (alladd=='-1' and accmaxtime !=""):
#如果maxtime不为空
if maxtime !="" or (alladd=='-1' and accmaxtime !=""):
    content='''%s'''

if first=="yes" and clean:
    print "清空目的表数据"
    content=content.replace("-- clean ","delete from @table")
else:
    content=content.replace("-- clean ","")
path=writefile(content)
dbname='%s'
tablename='%s'
createfield='%s'

#endtime=datetime.datetime.now().strftime('%%Y%%m%%d%%H%%M%%S') 
endtime=get_now_format(timetp)

if (first == "yes" and maxtime != ""):
    print '首次运行，初始值数据：' +maxtime
elif maxtime != "":
    print '读取到的上次抽取增量最大时间: '+maxtime
print '目前的任务时间为: '+datetime.datetime.now().strftime('%%Y-%%m-%%d %%H:%%M:%%S') 
#print alladd
print '6.执行增量抽取命令:'


#是否第一次运行
if alladd=='-1' and accmaxtime !="":
    cmd='python /opt/dana/datax/bin/datax.py %%s -p "-Dstarttime=\'%%s\' -Dendtime=\'%%s\' "  '%%(path,accmaxtime,endtime) 
    print '增量追加:'
elif (first == "yes" and maxtime !=""):
    print '首次运行，不同步历史数据，初始数据：'+maxtime
    cmd='python /opt/dana/datax/bin/datax.py %%s -p "-Dstarttime=\'%%s\' -Dendtime=\'%%s\' "  '%%(path,maxtime,endtime) 
elif (first=="yes" or alladd=='1' or maxtime==''):
    print '首次运行,不使用最大时间'
    cmd='python /opt/dana/datax/bin/datax.py %%s -p "-Dendtime=\'%%s\' "  '%%(path,endtime) 
elif first=="no":
    cmd='python /opt/dana/datax/bin/datax.py %%s -p "-Dstarttime=\'%%s\' -Dendtime=\'%%s\' "  '%%(path,maxtime,endtime) 

if os.access("/etc/danastudio/tdctoken.csv", os.F_OK):
    tdctoken = commands.getoutput("cat /etc/danastudio/tdctoken.csv")
    cmd='python /opt/dana/datax/bin/datax.py %%s -p "-Dstarttime=\'%%s\' -Dendtime=\'%%s\' -Dtoken=\'%%s\'"  '%%(path,maxtime,endtime,tdctoken)

print '命令如下：'
print cmd
sys.stdout.flush()
#执行并删除文件
#val = commands.getoutput(cmd)
val = sh(cmd, True)
valstr = "".join(val)
os.remove(path)

print '7.任务执行结束，结果为：'
if '任务总计耗时' in valstr:
    print '执行成功,详细的datax日志如上'
    #print val 
    print '增量抽取任务已执行结束，任务成功！'
    print '开始获取并设置此次任务增量字段时间最大值:'
    max=remax(valstr)
    code,response=setmax(id,max)
    if code==200 and json.loads(response.text)['code']==200 :
        print'获取并设置最大值成功！'
    else:
        print '获取并设置最大时间接口失败,请检查ES环境,错误日志如下：'
        print response.text
        signal.signal(signal.SIGUSR1, handle_SIGUSR1)
        os.kill(os.getpid(), signal.SIGUSR1)
else:
    print '执行失败,相关错误日志信息如上'
    #print val
    print '增量抽取任务已执行结束，但是执行失败，请检查相关配置或联系运维人员！'
    signal.signal(signal.SIGUSR1, handle_SIGUSR1)
    os.kill(os.getpid(), signal.SIGUSR1)
`, reader, extractEngineId, nodirtyreader, dbname, tablename, createfield)
	//fmt.Println(template)
	template = strings.Replace(template, `"fieldEscape":"\\"`, `"fieldEscape":"\\\\"`, 2)
	return template
}

// TDCLoadCmd 新建tdc落地抽取脚本
func TDCLoadCmd(sp *source.NewAcc, way string) (string, error) {
	var execCmd string
	eninfo, err := dbutil.EngineInfo(sp.Sourcedb.Engineid)
	if err != nil {
		logger.Error.Printf("获取引擎信息失败:%s", err.Error())
		return execCmd, err
	}
	tdcommand := "cat /etc/danastudio/tdctoken.csv"
	tdctoken, err := tools.ExeCommand(tdcommand)
	if err != nil {
		return "", err
	}
	// fmt.Println("1--------------1")
	tdctoken = strings.TrimSpace(tdctoken)
	// 判断远程临时文件夹是否存在
	existcmd := fmt.Sprintf(`curl -s -X GET "http://%s:%d/webhdfs/v1/tmp/detuo?op=LISTSTATUS&guardian_access_token=%s"`,
		eninfo.IP, eninfo.HTTPFsPort, tdctoken)
	existfile, err := tools.ExeCommand(existcmd)
	if err != nil {
		return "", err
	}
	// fmt.Println("2-----------------2")
	if strings.Contains(existfile, "FileNotFoundException") {
		// 文件夹不存在则新建
		existcmd = fmt.Sprintf(`curl -s -X PUT "http://%s:%d/webhdfs/v1/tmp/detuo?op=MKDIRS&permission=777&guardian_access_token=%s"`,
			eninfo.IP, eninfo.HTTPFsPort, tdctoken)
		_, err = tools.ExeCommand(existcmd)
		if err != nil {
			return "", err
		}
	}
	// fmt.Println("30------------------3")
	// normalpath := common.TDCLoadPath + "/" + normalfile
	// 落地抽取步骤：
	// 使用curl命令完成主要操作步骤
	// 1.抽取到本地csv文件
	// 2.获取本地文件名称
	// 3.本地文件路径
	// 4.提交到远程tdc路径
	// 5.load到表中
	// 6.删除临时文件
	// fmt.Println("way:", way)
	normalfileCmd := fmt.Sprintf(`ls /opt/danastudio/ants/tdc_simple_load/ | grep "%s"`, way)
	// 1.新建远程临时文件
	longfileCmd := fmt.Sprintf(`curl -s -X PUT "http://%s:%d/webhdfs/v1/tmp/detuo/%s?op=CREATE&permission=777&guardian_access_token=%s"`,
		eninfo.IP, eninfo.HTTPFsPort, way, tdctoken)
	// 2.数据传输到远程临时文件
	filedataCmd := fmt.Sprintf(`curl -s -C - -X PUT -T $normalpath "http://%s:%d/webhdfs/v1/tmp/detuo/%s?op=CREATE&data=true&permission=777&guardian_access_token=%s" -H "Content-Type:application/octet-stream"`,
		eninfo.IP, eninfo.HTTPFsPort, way, tdctoken)
	// 3.新建临时表
	createsql := fmt.Sprintf(`create table if not exists "%s.%s" (`, sp.Sourcedb.Odsdatabase, way+"_tmp")
	fields, fieldtypes := sp.Sourcedb.Field, sp.Sourcedb.Fieldtype
	var cast toolsource.TypeCast
	cast.ExtrType = sp.Extractdb.DBType
	cast.SourceType = sp.Sourcedb.Dbtype
	cast.FieldType = fieldtypes
	fmt.Printf("TBExists--cast:%#v\n", cast)
	fieldtypes = tools.FieldTrans(cast)
	for i := 0; i < len(fields); i++ {
		tempsql := fmt.Sprintf("\"\\`%s\\`\" %s,", fields[i], fieldtypes[i])
		if i == len(fields)-1 {
			tempsql = fmt.Sprintf("\"\\`%s\\`\" %s)", fields[i], fieldtypes[i])
		}
		createsql += tempsql
	}
	createsql += "ROW FORMAT DELIMITED  FIELDS TERMINATED BY '\u0001'"

	temptableCmd := MGExecuteSqlCmd(common.SERVER_IP, eninfo.EngineID, createsql)
	// 4.load数据到临时表
	loadsql := fmt.Sprintf(`load DATA inpath '/tmp/detuo/%s'  INTO TABLE "%s.%s"`,
		way, sp.Sourcedb.Odsdatabase, way+"_tmp")
	loadtempCmd := MGExecuteSqlCmd(common.SERVER_IP, eninfo.EngineID, loadsql)
	// 5.load数据到目标表
	loadsql = fmt.Sprintf(`INSERT INTO TABLE "%s.%s" SELECT * FROM "%s.%s"`,
		sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table, sp.Sourcedb.Odsdatabase, way+"_tmp")
	loadaimCmd := MGExecuteSqlCmd(common.SERVER_IP, eninfo.EngineID, loadsql)
	// 6.删除临时文件
	loadsql = fmt.Sprintf(`drop table "%s.%s"`, sp.Sourcedb.Odsdatabase, way+"_tmp")
	deltmpTable := MGExecuteSqlCmd(common.SERVER_IP, eninfo.EngineID, loadsql)
	deltmpFile := fmt.Sprintf(`curl -s -X DELETE "http://%s:%d/webhdfs/v1/tmp/detuo/%s?op=DELETE&guardian_access_token=%s"`,
		eninfo.IP, eninfo.HTTPFsPort, way, tdctoken)
	delnormalfile := "rm -rf $normalpath"
	execCmd = fmt.Sprintf(`
echo "开始落地抽取..."
echo "开始抽取到本地文件，文件所在目录为/opt/danastudio/ants/tdc_simple_load"
dataxjob=$(%s)
echo "$dataxjob"
dataxres=$(echo $dataxjob | grep "任务总计耗时")
if [[ "$dataxres" != "" ]]
then
	echo "datax job completed successfully!"
	echo "抽取到本地文件成功"
	normalfile=$(%s)
	dirpath="/opt/danastudio/ants/tdc_simple_load/"
	normalpath=$dirpath$normalfile
	echo "本地文件路径:$normalpath"
	echo "开始新建远程临时文件..."
	longjob=$(%s)
	if [[ "$longjob" == "" ]]
	then
		echo "新建远程临时文件成功"
		echo "开始传输数据到远程临时文件..."
		echo "远程临时文件夹路径:/webhdfs/v1/tmp/detuo"
		filejob=$(%s)
		if [[ "$filejob" == "" ]]
		then
			echo "传输数据到远程临时文件成功"
			echo "开始新建临时表..."
			echo "建表语句为:\n%s"
			%s > /opt/danastudio/ants/tdc_simple_load/%s.txt
			temptable=$(cat /opt/danastudio/ants/tdc_simple_load/%s.txt)
			temptableres=$(echo $temptable | grep "NoneType")
			if [[ "$temptableres" != "" ]]
			then
				echo "新建临时表成功"
				echo "开始load数据到临时表..."
				%s > /opt/danastudio/ants/tdc_simple_load/%s.txt
				loadtemp=$(cat /opt/danastudio/ants/tdc_simple_load/%s.txt)
				loadtempres=$(echo $loadtemp | grep "NoneType")
				if [[ "$loadtempres" != "" ]]
				then
					echo "load数据到临时表成功"
					echo "开始load数据到目标表..."
					%s > /opt/danastudio/ants/tdc_simple_load/%s.txt
					loadaim=$(cat /opt/danastudio/ants/tdc_simple_load/%s.txt)
					loadaimres=$(echo $loadaim | grep "NoneType")
					if [[ "$loadaimres" != "" ]]
					then
						echo "load数据到目标表成功"
						echo "开始删除临时文件..."
						%s
						%s
						%s
						rm -rf /opt/danastudio/ants/tdc_simple_load/%s.txt
						echo "删除临时文件结束"
						echo "落地抽取执行结束"
					else
						echo "load数据到目标表失败"
						echo "删除已创建临时表、远程临时文件及本地文件并退出任务"
						%s
						%s
						%s
						rm -rf /opt/danastudio/ants/tdc_simple_load/%s.txt
						exit 1
					fi
				else
					echo "load数据到临时表失败"
					echo "删除已创建临时表、远程临时文件及本地文件并退出任务"
					%s
					%s
					%s
					rm -rf /opt/danastudio/ants/tdc_simple_load/%s.txt
					exit 1
				fi
			else
				echo "新建临时表失败"
				echo "删除已创建远程临时文件及本地文件并退出任务"
				%s
				%s
				exit 1
			fi
		else
			echo "传输数据到远程临时文件失败"
			echo "删除已创建远程临时文件及本地文件并退出任务"
			%s
			%s
			exit 1
		fi
	else
		echo "新建远程临时文件失败"
		echo "删除本地文件并退出任务"
		%s
		exit 1
	fi
else
echo "something seems failed"
echo "抽取到本地文件失败，请查看json文件是否合法"
exit 1
fi
`,
		sp.Cmd, normalfileCmd, longfileCmd, filedataCmd, createsql, temptableCmd,
		way, way, loadtempCmd, way, way, loadaimCmd, way, way, deltmpTable,
		deltmpFile, delnormalfile, way, deltmpTable, deltmpFile, delnormalfile,
		way, deltmpTable, deltmpFile, delnormalfile, way, deltmpFile, delnormalfile,
		deltmpFile, delnormalfile, delnormalfile)
	return execCmd, nil
}

func WriteFile(filePath string, content string) error {
	_, err := os.Stat(path.Dir(filePath))
	if err != nil {
		os.MkdirAll(path.Dir(filePath), os.ModePerm)
	}
	dstFile, err := os.OpenFile(filePath, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0777)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	dstFile.WriteString(content)
	defer dstFile.Close()
	return nil
}

func transPassword(ps string) string {
	if ps == "" {
		return ps
	}
	ps = strings.ReplaceAll(ps, `\`, `\\`)
	return ps
}

// storkWriterJSON 抽取任务构建datax writer json结构体 存储源：stork teryx gaussdb
func storkWriterJSON(sp *source.NewAcc, datastrategy int) (string, string) {
	storkwriter := common.Storkwriters
	var postsql, jdbcType string
	if sp.Sourcedb.Dbtype == common.UXDB {
		storkwriter = common.UXDBwriters
		jdbcType = "uxdb"
	} else {
		jdbcType = "postgresql"
	}
	// 5.0-2.4需求 gaussdb、优炫改用与teryx相同writer

	storkwriter, _ = sjson.Set(storkwriter, "parameter.username", sp.Sourcedb.Odsuser)
	storkwriter, _ = sjson.Set(storkwriter, "parameter.password", transPassword(sp.Sourcedb.Odspassword))
	switch sp.Extractdb.Createfield {
	case "":
		if len(sp.Sourcedb.Field) != 0 {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(i),
					"\""+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"\"")
			}
		}

		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.table.0",
			sp.Sourcedb.Schema+"."+"\""+sp.Sourcedb.Table+"\"")
		postsql = "ANALYZE " + "\"" + sp.Sourcedb.Schema + "\"" + "." + "\"" + sp.Sourcedb.Table + "\""
		//storkwriter, _ = sjson.Set(storkwriter, "parameter.postSql.0", postsql)
		//实时抽取只有覆盖策略
		if sp.Tasktype == 4 {
			presql := "delete from @table"
			storkwriter, _ = sjson.Set(storkwriter, "parameter.preSql.0", presql)
		}
	default:
		if len(sp.Sourcedb.Field) != 0 {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(i),
					`\"`+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+`\"`)
			}
		}
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.table.0",
			sp.Sourcedb.Schema+"."+`\"`+sp.Sourcedb.Table+`\"`)
		postsql = "ANALYZE " + `\"` + sp.Sourcedb.Schema + `\"` + "." + `\"` + sp.Sourcedb.Table + `\"`
		//storkwriter, _ = sjson.Set(storkwriter, "parameter.postSql.0", postsql)
	}
	storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
		fmt.Sprintf("jdbc:%s://%s:%s/%s?%s", jdbcType, sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
			sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))
	//postsql := fmt.Sprintf(`ANALYZE \"%s\".\"%s\"`, sp.Sourcedb.Schema, sp.Sourcedb.Table)
	//覆盖策略新增presql
	if datastrategy == 1 {
		presql := "truncate table @table"
		storkwriter, _ = sjson.Set(storkwriter, "parameter.preSql.0", presql)
	}
	return storkwriter, postsql
}

// storkWriterJSON 抽取任务构建datax writer json结构体 存储源：stork teryx
func pushWriterJSON(sp *source.NewAcc, targetFields []string, writerStr string, tableName string, quotes string) string {
	writer := common.Writers

	writer, _ = sjson.Set(writer, "name", writerStr)
	writer, _ = sjson.Set(writer, "parameter.username", sp.Sourcedb.Odsuser)
	writer, _ = sjson.Set(writer, "parameter.password", transPassword(sp.Sourcedb.Odspassword))
	//当处理hive类型推送到其他库时，需要判断是否为增量类型，如果非增量，则不需要转译密码
	if (sp.Extractdb.DBType == common.Hive || sp.Extractdb.DBType == common.Inceptor) && sp.Extractdb.Createfield == "" {
		writer, _ = sjson.Set(writer, "parameter.password", sp.Sourcedb.Odspassword)
	}

	//if len(sp.Sourcedb.Field) != 0 {
	if len(targetFields) != 0 {
		for i := 0; i < len(targetFields); i++ {
			writer, _ = sjson.Set(writer, "parameter.column."+strconv.Itoa(i),
				quotes+strings.TrimRight(targetFields[i], "\r")+quotes)
		}
	}
	writer, _ = sjson.Set(writer, "parameter.connection.0.table.0", tableName)

	if sp.Sourcedb.Dbtype == "mysql" {
		if !sp.Sourcedb.IsOpenSSL && !strings.Contains(sp.Sourcedb.JdbcURL, "useSSL") {
			if strings.Contains(sp.Sourcedb.JdbcURL, "?") {
				sp.Sourcedb.JdbcURL += "&useSSL=false"
			} else {
				sp.Sourcedb.JdbcURL += "?useSSL=false"
			}
		}
	}

	writer, _ = sjson.Set(writer, "parameter.connection.0.jdbcUrl", sp.Sourcedb.JdbcURL)
	//postsql := fmt.Sprintf(`ANALYZE \"%s\".\"%s\"`, sp.Sourcedb.Schema, sp.Sourcedb.Table)
	return writer
}

func MGExecuteSqlCmd(ip, databaseID, sql string) string {
	return fmt.Sprintf(`curl -XPOST  http://%v/danastudio/metadata/collect/executeSql --header 'Content-Type: application/json' --data '{"engineid":"%v","sql":"%v"}'`, ip, databaseID, sql)
}

// 定义一个结构体，用于map排序
type kv struct {
	Key   int
	Value FieldEdit
}

// PatchField 从专题信息中提取专题表字段和类型
func PatchField(dwsInfo DWSTBInfo) ([]string, []string, []string) { //字段名列表，字段类型列表
	var fieldList []string
	var typeList []string
	var commentList []string
	var ss []kv
	for _, i := range dwsInfo.ParamTb {
		for _, f := range i.Fields {
			if !f.IsSelected {
				continue
			}
			ss = append(ss, kv{f.FieldIndex, f})
		}
	}
	//切片排序
	sort.Slice(ss, func(i, j int) bool {
		return ss[i].Key < ss[j].Key // 升序
	})
	for _, sortf := range ss {
		if sortf.Value.ReName != "" {
			fieldList = append(fieldList, sortf.Value.ReName)
		} else {
			fieldList = append(fieldList, sortf.Value.FieldName)
		}

		if sortf.Value.TypeChange != "" {
			typeList = append(typeList, sortf.Value.TypeChange)
		} else {
			typeList = append(typeList, sortf.Value.FieldType)
		}

		commentList = append(commentList, sortf.Value.Comment)
	}

	return fieldList, typeList, commentList
}

// PatchField 从专题信息中提取专题表字段和类型
func PatchFieldToMapList(dwsInfo DWSTBInfo) []map[string]interface{} {
	//字段名列表，字段类型列表
	var ss []FieldEdit
	for _, i := range dwsInfo.ParamTb {
		for _, f := range i.Fields {
			if !f.IsSelected {
				continue
			}
			ss = append(ss, f)
		}
	}
	//切片排序
	sort.Slice(ss, func(i, j int) bool {
		return ss[i].FieldIndex < ss[j].FieldIndex // 升序
	})
	var resultList []map[string]interface{}
	for _, sortf := range ss {
		var fieldInfo = make(map[string]interface{})
		if sortf.ReName != "" {
			fieldInfo["fieldname"] = sortf.ReName
			//fieldList = append(fieldList, strings.ToLower(sortf.Value.ReName))
		} else {
			fieldInfo["fieldname"] = sortf.FieldName
			//fieldList = append(fieldList, strings.ToLower(sortf.Value.FieldName))
		}

		fieldInfo["comment"] = sortf.Comment

		if sortf.TypeChange != "" {
			fieldInfo["fieldtype"] = sortf.TypeChange
			//typeList = append(typeList, sortf.Value.TypeChange)
		} else {
			fieldInfo["fieldtype"] = sortf.FieldType
			//typeList = append(typeList, sortf.Value.FieldType)
		}
		fieldInfo["isprim"] = false
		for _, v := range dwsInfo.ParamPrimFields {
			if sortf.FieldName == v {
				fieldInfo["isprim"] = true
				continue
			}
		}
		fieldInfo["conversiontype"] = tools.ConversionType(dwsInfo.StoreDBType, fieldInfo["fieldtype"].(string))
		resultList = append(resultList, fieldInfo)
	}

	return resultList
}

//迁移到tools服务 httpdo/typecast.go文件中
//var mysqlString = []string{"text", "varchar", "char"}
//var oracleString = []string{"varchar2", "blob", "varchar", "char", "nchar", "nvarchar2", "clob", "nclob", "oid"}
//var postgresString = []string{"text", "varchar", "bit", "character", "char"}
//var mysqlBigint = []string{"bigint", "int", "smallint", "tinyint"}
//var oracleBigint = []string{"integer", "smallint"}
//var postgresBigint = []string{"bigint", "int8", "int4", "int2", "int", "integer", "smallint"}
//var mysqlDouble = []string{"decimal", "numeric", "double", "real", "float"}
//var oracleDouble = []string{"decimal", "number", "binary_double", "real", "float", "binary_float"}
//var postgresDouble = []string{"numeric", "decimal", "real", "float4", "float8"}
//var dateType = []string{"date"}
//var timeType = []string{"time"}
//var mysqlTimestampHive = []string{"datetime", "timestamp", "time"}
//var oracleTimestampHive = []string{"timestamp", "time", "date"}
//var postgresTimestampHive = []string{"timestamp", "time"}
//
//var mysqlTimestampTeryx = []string{"datetime", "timestamp"}
//var oracleTimestampTeryx = []string{"timestamp", "time", "date"}
//var postgresTimestampTeryx = []string{"timestamp"}
//
//var postgresBinary = []string{"bytea"}
//var mysqlBinary = []string{"blob"}
//var oracleBinary = []string{"blob"}
//
//func ConversionType(dbType string, fieldType string) ConversionFieldType {
//	switch dbType {
//	case "hive", common.Inceptor:
//		switch fieldType {
//		case "string":
//			return ConversionFieldType{Mysql: mysqlString, Oracle: oracleString, Postgres: postgresString, Stork: postgresString}
//		case "bigint":
//			return ConversionFieldType{Mysql: mysqlBigint, Oracle: oracleBigint, Postgres: postgresBigint, Stork: postgresBigint}
//		case "double":
//			return ConversionFieldType{Mysql: mysqlDouble, Oracle: oracleDouble, Postgres: postgresDouble, Stork: postgresDouble}
//		case "date":
//			return ConversionFieldType{Mysql: dateType, Oracle: dateType, Postgres: dateType, Stork: dateType}
//		case "timestamp":
//			return ConversionFieldType{Mysql: mysqlTimestampHive, Oracle: oracleTimestampHive, Postgres: postgresTimestampHive, Stork: postgresTimestampHive}
//		case common.TYPE_BINARY:
//			return ConversionFieldType{Mysql: mysqlBinary, Oracle: oracleBinary, Postgres: postgresBinary, Stork: postgresBinary}
//		}
//	case "stork", "teryx", "gaussdb", "gaussdba":
//		switch fieldType {
//		case "string":
//			return ConversionFieldType{Mysql: mysqlString, Oracle: oracleString, Postgres: postgresString, Stork: postgresString}
//		case "bigint":
//			return ConversionFieldType{Mysql: mysqlBigint, Oracle: oracleBigint, Postgres: postgresBigint, Stork: postgresBigint}
//		case "double":
//			return ConversionFieldType{Mysql: mysqlDouble, Oracle: oracleDouble, Postgres: postgresDouble, Stork: postgresDouble}
//		case "date":
//			return ConversionFieldType{Mysql: dateType, Oracle: dateType, Postgres: dateType, Stork: dateType}
//		case "time":
//			return ConversionFieldType{Mysql: timeType, Oracle: timeType, Postgres: timeType, Stork: timeType}
//		case "timestamp":
//			return ConversionFieldType{Mysql: mysqlTimestampTeryx, Oracle: oracleTimestampTeryx, Postgres: postgresTimestampTeryx, Stork: postgresTimestampHive}
//		case common.TYPE_BINARY:
//			return ConversionFieldType{Mysql: mysqlBinary, Oracle: oracleBinary, Postgres: postgresBinary, Stork: postgresBinary}
//		}
//	}
//	// 默认转string输出
//	return ConversionFieldType{Mysql: mysqlString, Oracle: oracleString, Postgres: postgresString, Stork: postgresString}
//}

// 为库表推送建表
func CreateTableForTablePush(info DwsTBPublishInfo, dbType, database string, field, fieldType, fieldComment []string) string {

	var createSqlPrefix string
	var separator string
	var lenSuffix string
	var fieldStr string
	var decimalSuffix = ""
	var charSet = ""
	switch dbType {
	case "mysql":
		createSqlPrefix = fmt.Sprintf("create table if not exists `%v` (", info.TableName)
		separator = "`"
		lenSuffix = "(2000)"
		decimalSuffix = "(65,30)"
		charSet = "DEFAULT CHARACTER SET = utf8"
		nullTime := " null"

		for i, v := range field {
			var fieldType = fieldType[i]
			if strings.Contains(fieldType, "varchar") {
				fieldType += lenSuffix
			}
			if fieldType == "decimal" {
				fieldType += decimalSuffix
			}
			if strings.Contains(fieldType, "time") {
				fieldType += nullTime
			}
			if fieldComment[i] != "" {
				fieldStr += separator + v + separator + "   " + fieldType + "   COMMENT '" + fieldComment[i] + "' ,"
			} else {
				fieldStr += separator + v + separator + "   " + fieldType + ","
			}
		}
		fieldStr = strings.TrimSuffix(fieldStr, `,`)
		return createSqlPrefix + fieldStr + ")" + charSet
	case "SQL server":
		createSql := fmt.Sprintf(`create table "%s"."%s" (`, info.Schema, info.TableName)
		lenSuffix := "(2000)"
		decimalSuffix := "(38,10)"
		separator := `"`
		var commentStr string
		for i, v := range field {
			var fieldType = fieldType[i]
			if strings.Contains(fieldType, "varchar") || strings.Contains(fieldType, "binary") {
				fieldType += lenSuffix
			}
			if fieldType == "decimal" {
				fieldType += decimalSuffix
			}

			createSql += separator + v + separator + "  " + fieldType + ","
			//字段注释
			//EXEC testing.sys.sp_addextendedproperty 'MS_Description', '注释信息', 'user', 'schema名', 'table', '表名称', 'column', '列名称';
			if fieldComment[i] != "" {
				commentStr += fmt.Sprintf("EXEC %s.sys.sp_addextendedproperty 'MS_Description','%s','schema','%s','table','%s','column','%s';",
					database, fieldComment[i], info.Schema, info.TableName, v)
			}
		}
		return createSql + ");" + " " + commentStr
	case "oracle":
		createSqlPrefix = fmt.Sprintf("create table \"%v\".\"%v\" (", info.Schema, info.TableName)
		separator = "\""
		lenSuffix = "(4000)"
	case "stork", "postgres", "postgresql":
		createSqlPrefix = fmt.Sprintf("create table if not exists\"%v\".\"%v\" (", info.Schema, info.TableName)
		separator = "\""
	}

	var commentstr string
	for i, v := range field {
		var fieldType = fieldType[i]
		if strings.Contains(fieldType, "varchar") {
			fieldType += lenSuffix
		}
		if i != len(field)-1 {
			fieldStr += separator + v + separator + "   " + fieldType + ","
		} else {
			fieldStr += separator + v + separator + "   " + fieldType
		}
		if fieldComment[i] != "" {
			commentstr = commentstr + fmt.Sprintf(`COMMENT ON COLUMN "%s"."%s"."%s" IS '%s'; `, info.Schema, info.TableName, field[i], fieldComment[i])
		}
	}
	return createSqlPrefix + fieldStr + ") ; " + commentstr
}

// 执行建表方法
func ExecQuerySql(dbinfo metadata.AccDbInfo, querysql string) (metadata.MGCommonRes, error) {
	var datastruct metadata.MGCommonRes

	err := collect.AddMGSource(dbinfo)
	if err != nil {
		logger.Error.Printf("ExecQuerySql 注册数据源失败:%s\n", err.Error())
		return datastruct, err
	}
	uri := fmt.Sprintf("/meta-gateway/executeQuery?datasourceId=%s", dbinfo.Id)
	data, err := collect.MGPOST(uri, querysql)
	if err != nil {
		logger.Error.Println("ExecQuerySql MG请求出错:", err)
		return datastruct, err
	}
	err = json.Unmarshal(data, &datastruct)
	if err != nil {
		logger.Error.Println("ExecQuerySql 查询表是否存在 MG请求出 错:", err)
		return datastruct, err
	}

	return datastruct, nil
}

// 执行建表方法
func ExecCreateSql(dbinfo metadata.AccDbInfo, checkSql, execSqlStr, exectype string, res ...antssource.NewAcc) (string, error) {
	var newTBID string
	err := collect.AddMGSource(dbinfo)
	if err != nil {
		logger.Error.Printf("ExecCreateSql 注册数据源失败:%s\n", err.Error())
		return newTBID, err
	}
	uri := fmt.Sprintf("/meta-gateway/executeQuery?datasourceId=%s", dbinfo.Id)
	data, err := collect.MGPOST(uri, checkSql)
	if err != nil {
		logger.Error.Println("ExecCreateSql MG请求出错:", err)
		return newTBID, err
	}
	var datastruct metadata.HivedDataMG
	err = json.Unmarshal(data, &datastruct)
	if err != nil {
		logger.Error.Println("ExecCreateSql 查询表是否存在 MG请求出 错:", err)
		return newTBID, err
	}
	if len(datastruct.Data) != 0 {
		num := gjson.Get(string(data), "data.0.num").Int()
		if num != 0 && exectype == "drop" {
			logger.Error.Println("drop execute SQL: ", checkSql)
			logger.Error.Println("sql exec result: ", string(data))
			return newTBID, errors.New("表中存在数据，不允许修改")
		} else if num != 0 && exectype == "selecttable" {
			logger.Error.Println("selecttable execute SQL: ", checkSql)
			logger.Error.Println("sql exec result: ", string(data))
			return newTBID, errors.New("表已存在")
		}
	}
	logger.Info.Println("库表推送涉及执行SQL：", execSqlStr)
	_, err = collect.MGPOST(uri, execSqlStr)
	if err != nil {
		logger.Error.Println("ExecCreateSql 建表 MG请求出错:", err)
		return newTBID, err
	}
	//添加数据源表记录
	if exectype == "selecttable" && len(res) == 1 {
		var newFields []metadata.Field
		for i := range res[0].Sourcedb.Field {
			t := metadata.Field{
				FieldName: res[0].Sourcedb.Field[i],
				FieldType: res[0].Sourcedb.Fieldtype[i],
				Comment:   res[0].Sourcedb.Fieldcomment[i],
				Index:     res[0].Sourcedb.FieldPrim[i],
			}
			newFields = append(newFields, t)
		}
		nowTime := time.Now().Local().Format("2006-01-02 15:04:05")
		newTable := metadata.TableInfo{
			TableName:  res[0].Sourcedb.Table,
			Fields:     newFields,
			DBID:       res[0].Sourcedb.Sourceid,
			TableType:  0,
			CreateTime: nowTime,
			UpdateTime: nowTime,
			SchemaID:   res[0].Sourcedb.SchemaID,
			SchemaName: res[0].Sourcedb.Schema,
			Comment:    res[0].Sourcedb.TableComment,
			CanInsert:  true,
		}
		newTBID, err = esutil.AddSingle(common.DataBaseMetaData, common.TbExtractDbInfo, "id", newTable)
		if err != nil {
			logger.Error.Println("创建数据源表出错:", err)
		}
		body := gin.H{
			"tableid": newTBID,
		}
		esutil.UpdByID(common.DataBaseMetaData, common.TbExtractDbInfo, newTBID, body)
	}

	return newTBID, nil
}

// 执行查询删表逻辑
func ExecDropSql(connectStr, dbType, selectSql, dropSql string) error {
	if dbType == "oracle" {
		tableIfNull := fmt.Sprintf("%s&sql=%s", connectStr, selectSql)
		// 通过sql判断表是否有数据，没有数据则可以进行删除操作
		i, err := tools.AccessPools(tableIfNull, common.SERVER_IP, common.PoolPort, "/pool/oracle/executeSql")
		if err != nil {
			logger.Error.Println(err)
			return err
		}
		// 表存在数据直接返回
		if i[0] != "0" {
			return errors.New("表中存在数据，不允许修改")
		}
		paramStr := fmt.Sprintf("%s&sql=%s", connectStr, dropSql)
		_, err = tools.AccessPools(paramStr, common.SERVER_IP, common.PoolPort, "/pool/oracle/executeSql")
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	} else {
		engine, err := xorm.NewEngine(dbType, connectStr)
		if err != nil {
			logger.Error.Println(err)
			return err
		}

		result, err := engine.Query(selectSql)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
		if string(result[0]["num"]) != "0" {
			return errors.New("表中存在数据，不允许修改")
		}
		_, err = engine.QueryString(dropSql)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	}
	return nil
}

// CommonOdpsReader odps抽取源单例抽取处理
func CommonOdpsReader(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	// var setting string
	// if common.ErrorLimit != 0 {
	// 	setting, _ = sjson.Set(common.Setting, "errorLimit.record", common.ErrorLimit)
	// } else {
	// 	setting = common.Setting
	// }

	var setting = sp.AdvancedConfig.Setting
	reader := common.Reader
	username := db.NormalConn.DBUser
	password := db.NormalConn.DBPassword
	reader, _ = sjson.Set(reader, "name", "rdbmsreader")
	reader, _ = sjson.Set(reader, "parameter.username", username)
	reader, _ = sjson.Set(reader, "parameter.password", transPassword(password))
	odpsUrl := fmt.Sprintf("****************************************************************", db.NormalConn.Database)
	reader, _ = sjson.Set(reader, "parameter.connection.0.jdbcUrl.0", odpsUrl)
	sql := "set odps.sql.allow.fullscan=true; " + sp.Extractdb.SqlTxt
	reader, _ = sjson.Set(reader, "parameter.connection.0.querySql.0", sql)

	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
		 %s
		"job": {
				%s
			"content": [
				{
					"reader":
					 %s,
					"writer":
					 %s
				}
			]
		}
	}`, sp.AdvancedConfig.Core, setting, reader, writerjson)
		return templete, nil
	}
	// 增量抽取
	// 是否符合标准日期格式
	var istime, isint bool
	// 增量字段类型
	var addFieldType string
	//遍历抽取字段原始类型，专题中抽取是由SQL查询，因此查找增量字段原类型需要从落地的所有字段中遍历（本质上两者一致，落地的字段就是SQL查询出来的字段）
	for i, v := range sp.Sourcedb.Field {
		// 匹配增量时间字段
		if v == sp.Extractdb.Createfield {
			addFieldType = sp.Sourcedb.Fieldtype[i]
			ftype := strings.ToUpper(sp.Sourcedb.Fieldtype[i])
			if strings.Contains(ftype, "TIMESTAMP") ||
				strings.Contains(ftype, "DATE") {
				istime = true
			}
			if strings.Contains(ftype, "INT") {
				isint = true
			}
			break
		}
	}
	// 增量字段--关键字/非标准字符处理
	sp.Extractdb.Createfield = strings.Replace(sp.Extractdb.Createfield, "`", "", -1)
	sp.Extractdb.Createfield = fmt.Sprintf("`%s`", sp.Extractdb.Createfield)
	// 脏数据处理
	//var dirtydata string
	//if sp.Extractdb.Supportdirtydata == true {
	//	// 字符串类型-增量脏数据处理
	//	switch sp.Extractdb.Timetype {
	//	case "yyyyMMdd":
	//		dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{8}$' or %s is null`,
	//			sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	//	case "yyyy-MM-dd":
	//		dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' or %s is null`,
	//			sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	//	case "yyyy/MM/dd":
	//		dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' or %s is null`,
	//			sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	//	case "yyyy-MM-dd HH:mm:ss":
	//		dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or %s is null`,
	//			sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	//	case "yyyy/MM/dd HH:mm:ss":
	//		dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' or %s is null`,
	//			sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	//	case "yyyyMMddHHmmss":
	//		dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{14}$' or %s is null `,
	//			sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	//	default:
	//		// 时间类型date/timestamp--脏数据处理
	//		dirtydata = fmt.Sprintf(`  or %s is null  `, sp.Extractdb.Createfield)
	//	}
	//}
	// 增量处理
	// 抽取策略--不包含第一次抽取
	var timetrans string
	// 第一次抽取策略
	var firsttimetrans string
	// 变量说明(从前往后)：时间类型、任务开始时间转换、任务结束时间转换、时间正则
	var timeType, startDate, endDate, addDate, timeExp string
	timeType = sp.Extractdb.Timetype
	// 转换变量处理
	switch timeType {
	// 字符串类型
	case "yyyyMMdd", "yyyy-MM-dd", "yyyy/MM/dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]",
		"yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]", "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
		addDate = fmt.Sprintf("%s", sp.Extractdb.Createfield)
	case common.Incre_Autoid:
		startDate = `'${starttime}'`
		addDate = fmt.Sprintf("%s", sp.Extractdb.Createfield)
		reader, _ = sjson.Set(reader, "parameter.firstDataCollection", true)
	//case "yyyyMMdd":
	//	addDate = fmt.Sprintf("to_date(%s,'yyyymmdd')", sp.Extractdb.Createfield)
	//	startDate = fmt.Sprintf("datetrunc(to_date('${starttime}','yyyymmddhhmiss'),'dd')")
	//	endDate = fmt.Sprintf("datetrunc(to_date('${endtime}','yyyymmddhhmiss'),'dd')")
	//	endDate = fmt.Sprintf("dateadd(%s, -1, 'dd')", endDate)
	//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyyMMdd")
	//case "yyyy-MM-dd":
	//	addDate = fmt.Sprintf("to_date(%s,'yyyy-mm-dd')", sp.Extractdb.Createfield)
	//	startDate = fmt.Sprintf("datetrunc(to_date('${starttime}','yyyymmddhhmiss'),'dd')")
	//	endDate = fmt.Sprintf("datetrunc(to_date('${endtime}','yyyymmddhhmiss'),'dd')")
	//	endDate = fmt.Sprintf("dateadd(%s, -1, 'dd')", endDate)
	//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy-MM-dd")
	//case "yyyy/MM/dd":
	//	addDate = fmt.Sprintf("to_date(%s,'yyyy/mm/dd')", sp.Extractdb.Createfield)
	//	startDate = fmt.Sprintf("datetrunc(to_date('${starttime}','yyyymmddhhmiss'),'dd')")
	//	endDate = fmt.Sprintf("datetrunc(to_date('${endtime}','yyyymmddhhmiss')','dd')")
	//	endDate = fmt.Sprintf("dateadd(%s, -1, 'dd')", endDate)
	//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy/MM/dd")
	//case "yyyy-MM-dd HH:mm:ss":
	//	addDate = fmt.Sprintf("to_date(%s,'yyyy-mm-dd hh:mi:ss')", sp.Extractdb.Createfield)
	//	startDate = fmt.Sprintf("to_date('${starttime}','yyyymmddhhmiss')")
	//	endDate = fmt.Sprintf("to_date('${endtime}','yyyymmddhhmiss')")
	//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy-MM-dd HH:mm:ss")
	//case "yyyy/MM/dd HH:mm:ss":
	//	addDate = fmt.Sprintf("to_date(%s,'yyyy/mm/dd hh:mi:ss')", sp.Extractdb.Createfield)
	//	startDate = fmt.Sprintf("to_date('${starttime}','yyyymmddhhmiss')")
	//	endDate = fmt.Sprintf("to_date('${endtime}','yyyymmddhhmiss')")
	//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyy/MM/dd HH:mm:ss")
	//case "yyyyMMddHHmmss":
	//	addDate = fmt.Sprintf("to_date(%s,'yyyymmddhhmiss')", sp.Extractdb.Createfield)
	//	startDate = fmt.Sprintf("to_date('${starttime}','yyyymmddhhmiss')")
	//	endDate = fmt.Sprintf("to_date('${endtime}','yyyymmddhhmiss')")
	//	reader, _ = sjson.Set(reader, "parameter.increColFormat", "yyyyMMddHHmmss")
	default:
		// 时间类型
		addDate = fmt.Sprintf("cast(%s as timestamp)", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("cast('${starttime}' as timestamp)")
		endDate = fmt.Sprintf("cast('${endtime}' as timestamp)")
		if strings.Contains(strings.ToUpper(addFieldType), "DATE") {
			addDate = fmt.Sprintf("cast(%s as date)", sp.Extractdb.Createfield)
			startDate = fmt.Sprintf("cast('${starttime}' as date)")
			endDate = fmt.Sprintf("cast('${endtime}' as date)")
			//endDate = fmt.Sprintf("datetrunc(to_date('${endtime}','yyyymmddhhmiss'),'dd')")
			//endDate = fmt.Sprintf("dateadd(%s, -1, 'dd')", endDate)
		}
		//// 时间类型
		//addDate = fmt.Sprintf("regexp_replace(cast(%s as string),'[^0-9]','')", sp.Extractdb.Createfield)
		//startDate = fmt.Sprintf("regexp_replace('${starttime}','[^0-9]','')")
		//endDate = fmt.Sprintf("regexp_replace('${endtime}','[^0-9]','')")
	}
	// 时间正则处理
	switch timeType {
	case "yyyyMMdd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{8}$' ", sp.Extractdb.Createfield)
	case "yyyy-MM-dd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyy/MM/dd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timeExp = fmt.Sprintf(` %s regexp '%s' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen))
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timeExp = fmt.Sprintf(` %s regexp '%s' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash))
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timeExp = fmt.Sprintf(` %s regexp '%s' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin))
	default:
		timeExp = fmt.Sprintf(` %s is not null `, sp.Extractdb.Createfield)
	}
	var timeExpStr = timeExp + " and "

	switch timeType {
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timeType = common.Incre_col_hyphen
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timeType = common.Incre_col_slash
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timeType = common.Incre_col_origin
	}
	reader, _ = sjson.Set(reader, "parameter.increColFormat", timeType)
	if istime {
		// 时间类型不包含正则
		timeExpStr = ""
		reader, _ = sjson.Set(reader, "parameter.increColFormat", "")
	}
	if timeType == common.Incre_Autoid {
		timetrans = fmt.Sprintf(" %s %s >%s ", timeExpStr, addDate, startDate)
		firsttimetrans = fmt.Sprintf(" %s ", timeExp)
	} else {
		timetrans = fmt.Sprintf(" %s %s >%s and %s<%s  ", timeExpStr, addDate, startDate, addDate, endDate)
		firsttimetrans = fmt.Sprintf(" %s  %s<%s  ", timeExpStr, addDate, endDate)
	}

	//语句处理
	if isint {
		timetrans = fmt.Sprintf(` %s >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}

	//firsttimetrans = firsttimetrans + dirtydata
	if sp.Extractdb.Wherecontent != "" {
		sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "
	}
	reader, _ = sjson.Set(reader, "parameter.firstDataCollection", true)
	reader, _ = sjson.Set(reader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
	// 第一次抽取reader
	increWhereSql := "set odps.sql.allow.fullscan=true; select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + firsttimetrans
	reader, _ = sjson.Set(reader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "set odps.sql.allow.fullscan=true; select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + timetrans
	nodirtyreader, _ := sjson.Set(reader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
        %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, reader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
       %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}

// CommonDb2Reader db2抽取源单例抽取处理
func CommonDb2Reader(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	// var setting string
	// if common.ErrorLimit != 0 {
	// 	setting, _ = sjson.Set(common.Setting, "errorLimit.record", common.ErrorLimit)
	// } else {
	// 	setting = common.Setting
	// }

	var setting = sp.AdvancedConfig.Setting
	db2reader := common.Reader
	username := db.NormalConn.DBUser
	password := db.NormalConn.DBPassword
	db2reader, _ = sjson.Set(db2reader, "name", "rdbmsreader")
	db2reader, _ = sjson.Set(db2reader, "parameter.username", username)
	db2reader, _ = sjson.Set(db2reader, "parameter.password", transPassword(password))
	db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
	//-----------------------------

	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
		 %s
		"job": {
				%s
			"content": [
				{
					"reader":
					 %s,
					"writer":
					 %s
				}
			]
		}
	}`, sp.AdvancedConfig.Core, setting, db2reader, writerjson)
		return templete, nil
	}
	//如果有增量的话

	// 增量抽取
	var z int
	var istime, isdate bool
	var timetrans string
	var isint bool

	if len(sp.Extractdb.Field) > 0 {
		for i, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				z = i
			}
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATE") {
			isdate = true
		}

		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") {
			istime = true
		}
		//是否是整型的增量
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") {
			isint = true
		}
	}

	//抽取脏数据----------------------------------------------------------------
	var dirtydata string
	//*第一次抽取不加入初始时间
	var firsttimetrans string
	if sp.Extractdb.Supportdirtydata == true {
		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=8  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=10 or \"%s\" not  like '%%-%%-%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=10 or  \"%s\" not  like '%%/%%/%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%-%%-%% %%:%%:%%' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%/%%/%% %%:%%:%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=14  or \"%s\" is null `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		default:
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
		//脏数据
		if isint {
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
	}
	//------------------------------------------------------------------------------------------------------------

	switch sp.Extractdb.Timetype {
	case "yyyyMMdd":
		timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8  and  LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8  and  LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyyMMdd")
	case "yyyy-MM-dd":
		timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=10 and \"%s\" like '%%-%%-%%'  and  LENGTH( replace(translate(\"%s\",'','-0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8 and \"%s\" like '%%-%%-%%' and  LENGTH( replace(translate(\"%s\",'','-0123456789'),' ','') )=0  and  \"%s\" < '${endtime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy-MM-dd")
	case "yyyy/MM/dd":
		timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=10 and \"%s\" like '%%/%%/%%'  and  LENGTH( replace(translate(\"%s\",'','/0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8 and \"%s\" like '%%/%%/%%' and  LENGTH( replace(translate(\"%s\",'','/0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy/MM/dd")
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%-%%-%% %%:%%:%%'   and  LENGTH( replace(translate(\"%s\",'',':-.0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%-%%-%% %%:%%:%%'  and  LENGTH( replace(translate(\"%s\",'',':-.0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.Incre_col_hyphen)
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%/%%/%% %%:%%:%%'   and  LENGTH( replace(translate(\"%s\",'',':/.0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%/%%/%% %%:%%:%%'  and  LENGTH( replace(translate(\"%s\",'',':/.0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.Incre_col_slash)
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=14    and  LENGTH( replace(translate(\"%s\",'','.0123456789'),' ','') )=0  and  \"%s\" > '${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=14   and  LENGTH( replace(translate(\"%s\",'','.0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.Incre_col_origin)
	case common.Incre_Autoid:
		timetrans = fmt.Sprintf(` \"%s\" >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` \"%s\" is not null `, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.Incre_Autoid)
	}

	if isdate || istime {
		timetrans = fmt.Sprintf(`\"%s\" >cast('${starttime}' as timestamp)  AND \"%s\" <cast('${endtime}' as timestamp)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`\"%s\" < cast('${endtime}' as timestamp)`, sp.Extractdb.Createfield)
		db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "")
	}

	if isint {
		timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}
	firsttimetrans = firsttimetrans + dirtydata
	if sp.Extractdb.Wherecontent != "" {
		sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "
	}
	db2reader, _ = sjson.Set(db2reader, "parameter.firstDataCollection", true)
	db2reader, _ = sjson.Set(db2reader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))

	// 第一次抽取reader
	sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
	increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + firsttimetrans
	db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + timetrans
	nodirtyreader, _ := sjson.Set(db2reader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
        %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, db2reader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
       %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}

func CommonDaMengReader(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	// var setting string
	// if common.ErrorLimit != 0 {
	// 	setting, _ = sjson.Set(common.Setting, "errorLimit.record", common.ErrorLimit)
	// } else {
	// 	setting = common.Setting
	// }
	var setting = sp.AdvancedConfig.Setting
	dmreader := common.Reader
	username := db.NormalConn.DBUser
	password := db.NormalConn.DBPassword
	dmreader, _ = sjson.Set(dmreader, "name", "rdbmsreader")
	dmreader, _ = sjson.Set(dmreader, "parameter.username", username)
	dmreader, _ = sjson.Set(dmreader, "parameter.password", transPassword(password))
	dmreader, _ = sjson.Set(dmreader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	dmreader, _ = sjson.Set(dmreader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
	//-----------------------------
	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
		 %s
		"job": {
				%s
			"content": [
				{
					"reader":
					 %s,
					"writer":
					 %s
				}
			]
		}
	}`, sp.AdvancedConfig.Core, setting, dmreader, writerjson)
		return templete, nil
	}
	//如果有增量的话

	var z int
	var istime, isdate bool
	var timetrans string
	var isint bool

	if len(sp.Extractdb.Field) > 0 {
		for i, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				z = i
			}
		}
		if strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "DATE" {
			isdate = true
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") || strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
			istime = true
		}
		//是否是整型的增量
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") || strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "DEC" || strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "BYTE" {
			isint = true
		}
	}

	//抽取脏数据----------------------------------------------------------------
	var dirtydata string
	//*第一次抽取不加入初始时间
	var firsttimetrans string
	if sp.Extractdb.Supportdirtydata == true {
		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{8}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		default:
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
		//脏数据
		if isint {
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
	}
	//------------------------------------------------------------------------------------------------------------

	switch sp.Extractdb.Timetype {
	case "yyyyMMdd":
		timetrans = fmt.Sprintf(`length(\"%s\")=8 and  regexp_like(\"%s\",'^[0-9]{8}$') and \"%s\" <'${endtime}' and \"%s\" >'${starttime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` length(\"%s\")=8 and  regexp_like(\"%s\",'^[0-9]{8}$') and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyyMMdd")
	case "yyyy-MM-dd":
		timetrans = fmt.Sprintf(` length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and \"%s\" < '${endtime}' and \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and \"%s\" <  '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy-MM-dd")
	case "yyyy/MM/dd":
		timetrans = fmt.Sprintf(` length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and \"%s\" < '${endtime}'  and \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy/MM/dd")
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" <'${endtime}' and \"%s\" >'${starttime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  regexp_like(\"%s\",'%s') and \"%s\" <'${endtime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.Incre_col_origin)
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(`regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' and  \"%s\" > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.Incre_col_hyphen)
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(`regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' and  \"%s\" > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.Incre_col_slash)
	case common.Incre_Autoid:
		timetrans = fmt.Sprintf(` \"%s\" >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` \"%s\" is not null `, sp.Extractdb.Createfield)
		dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.Incre_Autoid)
	}

	if isdate {
		timetrans = fmt.Sprintf(` \"%s\"  >to_timestamp('${starttime}', 'YYYY-MM-DD HH24:MI:SS')  and \"%s\" <=  to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS')       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` \"%s\" <=to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS') and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	}

	if istime {
		//2.如果增量字段是时间类型的
		timetrans = fmt.Sprintf(` \"%s\"  >to_timestamp('${starttime}', 'YYYY-MM-DD HH24:MI:SS.FF')  and \"%s\" <=  to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS.FF')       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` \"%s\" <=to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS.FF') and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	}

	if isint {
		timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}
	firsttimetrans = firsttimetrans + dirtydata
	if sp.Extractdb.Wherecontent != "" {
		sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "

	}
	dmreader, _ = sjson.Set(dmreader, "parameter.firstDataCollection", true)
	dmreader, _ = sjson.Set(dmreader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))

	// 第一次抽取reader
	sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
	increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + firsttimetrans
	dmreader, _ = sjson.Set(dmreader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + timetrans
	nodirtyreader, _ := sjson.Set(dmreader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)

	// 第一次抽取模板
	templete = fmt.Sprintf(`{
        %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, dmreader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
       %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}

// 生成SQLServer抽取json
func CommonSQLServerReader(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting

	sqlServerReader := common.Reader
	username := db.NormalConn.DBUser
	password := db.NormalConn.DBPassword
	sqlServerReader, _ = sjson.Set(sqlServerReader, "name", "sqlserverreader")
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.username", username)
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.password", transPassword(password))
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
	//-----------------------------
	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
		 %s
		"job": {
				%s
			"content": [
				{
					"reader":
					 %s,
					"writer":
					 %s
				}
			]
		}
	}`, sp.AdvancedConfig.Core, setting, sqlServerReader, writerjson)
		return templete, nil
	}
	//增量

	var z int
	var istime, isdate bool
	var timetrans string
	var isint bool
	//*第一次抽取不加入初始时间
	var firsttimetrans string

	if len(sp.Extractdb.Field) > 0 {
		for i, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				z = i
			}
		}

		if strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "DATE" {
			isdate = true
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") || strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
			istime = true
		}
		//是否是整型的增量
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") {
			isint = true
		}
	}

	//抽取脏数据----------------------------------------------------------------
	var dirtydata string
	if sp.Extractdb.Supportdirtydata == true {
		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=8  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=10 or \"%s\" not  like '%%-%%-%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=10 or  \"%s\" not  like '%%/%%/%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss":
			dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%-%%-%% %%:%%:%%' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss":
			dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%/%%/%% %%:%%:%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss":
			dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=14  or \"%s\" is null `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		default:
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
		//脏数据
		if isint {
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
	}
	//------------------------------------------------------------------------------------------------------------
	//1.如果字段是字符串类型的
	switch sp.Extractdb.Timetype {
	case "yyyyMMdd":
		timetrans = fmt.Sprintf(` LEN(\"%s\")=8 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")=8 and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyyMMdd")

	case "yyyy-MM-dd":
		timetrans = fmt.Sprintf(` LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%-%%-%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\"  like '%%-%%-%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy-MM-dd")

	case "yyyy/MM/dd":
		timetrans = fmt.Sprintf(` LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%/%%/%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\"  like '%%/%%/%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy-MM-dd")

	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(` LEN(\"%s\")>=19 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%-%%-%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

		firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")>=19  and \"%s\" < '${endtime}' and \"%s\"  like '%%-%%-%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.Incre_col_hyphen)

	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(` LEN(\"%s\")>=19 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%/%%/%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

		firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")>=19  and \"%s\" < '${endtime}' and \"%s\"  like '%%/%%/%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.Incre_col_slash)

	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timetrans = fmt.Sprintf(` LEN(\"%s\")>=14 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%/%%/%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

		firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")>=14  and \"%s\" < '${endtime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.Incre_col_origin)
	case common.Incre_Autoid:
		timetrans = fmt.Sprintf(` \"%s\" >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` \"%s\" is not null `, sp.Extractdb.Createfield)
		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.Incre_Autoid)

	}
	if isdate || istime {
		timetrans = fmt.Sprintf(` \"%s\"   >cast('${starttime}' as datetime2) and \"%s\" < cast('${endtime}' as datetime2) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  \"%s\" <cast('${endtime}' as datetime2) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
	}
	//---------------
	//语句处理
	if isint {
		timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}
	//-------------------
	firsttimetrans = firsttimetrans + dirtydata
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.firstDataCollection", true)
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, `"`, "", -1))

	// 第一次抽取reader
	sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
	increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + firsttimetrans
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + timetrans
	nodirtyreader, _ := sjson.Set(sqlServerReader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
        %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, sqlServerReader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
       %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}

// 生成Oracle抽取json
func CommonOracleReader(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting

	oraclereader := common.Reader
	username := db.NormalConn.DBUser
	password := db.NormalConn.DBPassword
	oraclereader, _ = sjson.Set(oraclereader, "name", "oraclereader")
	oraclereader, _ = sjson.Set(oraclereader, "parameter.username", username)
	oraclereader, _ = sjson.Set(oraclereader, "parameter.password", transPassword(password))
	oraclereader, _ = sjson.Set(oraclereader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	oraclereader, _ = sjson.Set(oraclereader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
	//-----------------------------
	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
		 %s
		"job": {
				%s
			"content": [
				{
					"reader":
					 %s,
					"writer":
					 %s
				}
			]
		}
	}`, sp.AdvancedConfig.Core, setting, oraclereader, writerjson)
		return templete, nil
	}
	//增量

	var z int
	var istime bool
	var timetrans string
	var isint bool
	//*第一次抽取不加入初始时间
	var firsttimetrans string

	if len(sp.Extractdb.Field) > 0 {
		for i, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				z = i
			}
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") || strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATE") {
			istime = true
		}
		//是否是整型的增量
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") || strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "NUMBER" {
			isint = true
		}
	}

	//抽取脏数据----------------------------------------------------------------
	var dirtydata string
	if sp.Extractdb.Supportdirtydata == true {

		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{8}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		default:
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
		//脏数据
		if isint {
			dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
		}
	}
	//------------------------------------------------------------------------------------------------------------
	//1.如果字段是字符串类型的
	switch sp.Extractdb.Timetype {
	case "yyyyMMdd":
		timetrans = fmt.Sprintf(`length(\"%s\")=8 and  regexp_like(\"%s\",'^[0-9]{8}$') and \"%s\" <'${endtime}' and \"%s\" >'${starttime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` length(\"%s\")=8 and  regexp_like(\"%s\",'^[0-9]{8}$') and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		oraclereader, _ = sjson.Set(oraclereader, "parameter.increColFormat", "yyyyMMdd")
	case "yyyy-MM-dd":
		timetrans = fmt.Sprintf(` length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and \"%s\" < '${endtime}' and \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and \"%s\" <  '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		oraclereader, _ = sjson.Set(oraclereader, "parameter.increColFormat", "yyyy-MM-dd")
	case "yyyy/MM/dd":
		timetrans = fmt.Sprintf(` length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and \"%s\" < '${endtime}'  and \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		oraclereader, _ = sjson.Set(oraclereader, "parameter.increColFormat", "yyyy/MM/dd")
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" <'${endtime}' and \"%s\" >'${starttime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  regexp_like(\"%s\",'%s') and \"%s\" <'${endtime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		oraclereader, _ = sjson.Set(oraclereader, "parameter.increColFormat", common.Incre_col_origin)
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(`regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' and  \"%s\" > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		oraclereader, _ = sjson.Set(oraclereader, "parameter.increColFormat", common.Incre_col_hyphen)
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(`regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' and  \"%s\" > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield, sp.Extractdb.Createfield)

		firsttimetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		oraclereader, _ = sjson.Set(oraclereader, "parameter.increColFormat", common.Incre_col_slash)
	case common.Incre_Autoid:
		timetrans = fmt.Sprintf(` \"%s\" >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` \"%s\" is not null `, sp.Extractdb.Createfield)
		oraclereader, _ = sjson.Set(oraclereader, "parameter.increColFormat", common.Incre_Autoid)

	}
	if istime {
		timetrans = fmt.Sprintf(`\"%s\">TO_TIMESTAMP('${starttime}','yyyy-mm-dd hh24:mi:ss.ff') and  \"%s\" <TO_TIMESTAMP('${endtime}','yyyy-mm-dd hh24:mi:ss.ff')`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` \"%s\" <TO_TIMESTAMP('${endtime}','yyyy-mm-dd hh24:mi:ss.ff')`, sp.Extractdb.Createfield)
	}
	//---------------
	//语句处理
	if isint {
		timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}
	//-------------------
	firsttimetrans = firsttimetrans + dirtydata
	oraclereader, _ = sjson.Set(oraclereader, "parameter.firstDataCollection", true)
	oraclereader, _ = sjson.Set(oraclereader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, `"`, "", -1))

	// 第一次抽取reader
	sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
	increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + firsttimetrans
	oraclereader, _ = sjson.Set(oraclereader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + timetrans
	nodirtyreader, _ := sjson.Set(oraclereader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
        %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, oraclereader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
       %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}

// 生成Mysql抽取json
func CommonMysqlReader(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting

	mysqlreader := common.Reader
	username := db.NormalConn.DBUser
	password := db.NormalConn.DBPassword
	if db.DBVersion == "8.x" || strings.ToUpper(db.DBVersion) == "V8" {
		mysqlreader, _ = sjson.Set(mysqlreader, "name", "mysql8reader")
	} else {
		mysqlreader, _ = sjson.Set(mysqlreader, "name", "mysqlreader")
	}
	mysqlreader, _ = sjson.Set(mysqlreader, "parameter.username", username)
	mysqlreader, _ = sjson.Set(mysqlreader, "parameter.password", transPassword(password))
	db.TMPURL = fmt.Sprintf("*******************************************************************************", db.NormalConn.DBIP, db.NormalConn.DBPort, db.NormalConn.Database, db.TimeZoneInfo.TimeZone)
	mysqlreader, _ = sjson.Set(mysqlreader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, `"`, "`")
	mysqlreader, _ = sjson.Set(mysqlreader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
	//-----------------------------
	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
		 %s
		"job": {
				%s
			"content": [
				{
					"reader":
					 %s,
					"writer":
					 %s
				}
			]
		}
	}`, sp.AdvancedConfig.Core, setting, mysqlreader, writerjson)
		return templete, nil
	}
	//增量

	var z int
	var istime, isdate bool
	var isint bool
	if len(sp.Extractdb.Field) > 0 {
		for i, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				z = i
			}
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATE") &&
			!strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
			isdate = true
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") ||
			strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
			istime = true
		}
		//是否是整型的增量
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") {
			isint = true
		}
	}
	var timetrans string
	//*第一次抽取不加入初始时间
	var firsttimetrans string
	//抽取脏数据----------------------------------------------------------------
	var dirtydata string
	if sp.Extractdb.Supportdirtydata == true {
		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{8}$' or %s is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' or %s is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' or %s is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		default:
			dirtydata = fmt.Sprintf(`  or %s is null`, sp.Extractdb.Createfield)
		}
		if isint {
			dirtydata = fmt.Sprintf(`  or %s is null`, sp.Extractdb.Createfield)
		}
	}
	//------------------------------------------------------------------------------------------------------------
	//1.如果字段是字符串类型的
	switch sp.Extractdb.Timetype {
	case "yyyyMMdd":
		timetrans = fmt.Sprintf(" %s  >'${starttime}' and %s <('${endtime}'  and %s regexp '^[0-9]{8}$'  ", sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(" %s <'${endtime}'  and %s regexp '^[0-9]{8}$'  ", sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increColFormat", "yyyyMMdd")
	case "yyyy-MM-dd":
		timetrans = fmt.Sprintf(" %s  >'${starttime}' and %s < '${endtime}'  and %s regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(" %s <'${endtime}'  and %s regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increColFormat", "yyyy-MM-dd")
	case "yyyy/MM/dd":
		timetrans = fmt.Sprintf(" %s  >'${starttime}' and %s <'${endtime}'  and %s regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf("  %s <'${endtime}' and %s regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increColFormat", "yyyy/MM/dd")
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(" %s  >'${starttime}'  and %s < '${endtime}'  and %s REGEXP '%s' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen))
		firsttimetrans = fmt.Sprintf(" %s <'${endtime}'  and %s REGEXP '%s' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen))
		mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increColFormat", common.Incre_col_hyphen)
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf("  %s  >'${starttime}' and %s < '${endtime}'  and %s REGEXP '%s' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash))
		firsttimetrans = fmt.Sprintf(" %s < '${endtime}'  and %s REGEXP '%s' ", sp.Extractdb.Createfield, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash))
		mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increColFormat", common.Incre_col_slash)
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timetrans = fmt.Sprintf(` %s  >'${starttime}' and %s  < '${endtime}' and %s regexp '%s' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin))
		firsttimetrans = fmt.Sprintf(`  %s < '${endtime}'  and %s regexp '%s'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash))
		mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increColFormat", common.Incre_col_origin)
		//default:
		//	timetrans = fmt.Sprintf(" cast(%s as datetime) >cast('${starttime}' as datetime) and cast(%s as datetime)<=cast('${endtime}' as datetime)", sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		//	firsttimetrans = fmt.Sprintf(" cast(%s as datetime)<=cast('${endtime}' as datetime)", sp.Extractdb.Createfield)
	case common.Incre_Autoid: //autoid
		timetrans = fmt.Sprintf(" %s >'${starttime}' ", sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(" %s is not null ", sp.Extractdb.Createfield)
		mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increColFormat", common.Incre_Autoid)
	}
	if isdate {
		timetrans = fmt.Sprintf(" %s  > cast('${starttime}' as date) and %s < cast('${endtime}' as date)", sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(" %s <=cast('${endtime}' as date)", sp.Extractdb.Createfield)
	}
	if istime {
		timetrans = fmt.Sprintf(" cast(%s as datetime) >cast('${starttime}' as datetime) and cast(%s as datetime)<=cast('${endtime}' as datetime)", sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(" cast(%s as datetime)<cast('${endtime}' as datetime)", sp.Extractdb.Createfield)
	}

	if isint {
		timetrans = fmt.Sprintf(" %s >${starttime} ", sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}
	//-------------------
	firsttimetrans = firsttimetrans + dirtydata
	mysqlreader, _ = sjson.Set(mysqlreader, "parameter.firstDataCollection", true)
	mysqlreader, _ = sjson.Set(mysqlreader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))

	// 第一次抽取reader
	increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + firsttimetrans
	mysqlreader, _ = sjson.Set(mysqlreader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + sp.Extractdb.Wherecontent + timetrans
	nodirtyreader, _ := sjson.Set(mysqlreader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
        %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, mysqlreader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
       %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}

// 生成Gbase8抽取json
func CommonGbaseReader(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting

	gbasereader := common.Reader
	username := db.NormalConn.DBUser
	password := db.NormalConn.DBPassword
	gbasereader, _ = sjson.Set(gbasereader, "name", "rdbmsreader")
	gbasereader, _ = sjson.Set(gbasereader, "parameter.username", username)
	gbasereader, _ = sjson.Set(gbasereader, "parameter.password", transPassword(password))
	gbasereader, _ = sjson.Set(gbasereader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	gbasereader, _ = sjson.Set(gbasereader, "parameter.connection.0.querySql.0", sp.Extractdb.SqlTxt)
	//-----------------------------
	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
             %s
            "job": {
                    %s
                "content": [
                    {
                        "reader":
                         %s,
                        "writer":
                         %s
                    }
                ]
            }
        }`, sp.AdvancedConfig.Core, setting, gbasereader, writerjson)
		return templete, nil
	}
	//增量

	var z int
	var istime bool
	var isdate bool
	var timetrans string
	var isint bool
	//*第一次抽取不加入初始时间
	var firsttimetrans string

	if len(sp.Extractdb.Field) > 0 {
		for i, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				z = i
			}
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATE") && !strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
			isdate = true
		}
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") || strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
			istime = true
		}
		//是否是整型的增量
		if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") || strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "NUMBER" {
			isint = true
		}
	}
	//抽取脏数据----------------------------------------------------------------
	var dirtydata string
	if sp.Extractdb.Supportdirtydata == true {

		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(%s,'^[0-9]{8}$')  or %s is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(%s,'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  or %s is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(%s,'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  or %s is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(%s,'%s')  or %s is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(%s,'%s')  or %s is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			dirtydata = fmt.Sprintf(` or  not  regexp_like(%s,'%s')  or %s is null `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		default:
			dirtydata = fmt.Sprintf(`  or %s is null`, sp.Extractdb.Createfield)
		}
		//脏数据
		if isint {
			dirtydata = fmt.Sprintf(`  or %s is null`, sp.Extractdb.Createfield)
		}
	}
	//------------------------------------------------------------------------------------------------------------
	//1.如果字段是字符串类型的
	switch sp.Extractdb.Timetype {
	case "yyyyMMdd":
		timetrans = fmt.Sprintf(` regexp_like(%s,'^[0-9]{8}$') and %s <'${endtime}' and %s >'${starttime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` regexp_like(%s,'^[0-9]{8}$') and %s < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		gbasereader, _ = sjson.Set(gbasereader, "parameter.increColFormat", "yyyyMMdd")
	case "yyyy-MM-dd":
		timetrans = fmt.Sprintf(`  regexp_like(%s,'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and %s < '${endtime}' and %s > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`   regexp_like(%s,'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and %s <  '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		gbasereader, _ = sjson.Set(gbasereader, "parameter.increColFormat", "yyyy-MM-dd")
	case "yyyy/MM/dd":
		timetrans = fmt.Sprintf(`  regexp_like(%s,'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and %s < '${endtime}'  and %s > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`   regexp_like(%s,'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and %s < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		gbasereader, _ = sjson.Set(gbasereader, "parameter.increColFormat", "yyyy/MM/dd")
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timetrans = fmt.Sprintf(` regexp_like(%s,'%s') and %s <'${endtime}' and %s >'${starttime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(`  regexp_like(%s,'%s') and %s <'${endtime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		gbasereader, _ = sjson.Set(gbasereader, "parameter.increColFormat", common.Incre_col_origin)
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(`regexp_like(%s,'%s') and %s <  '${endtime}' and  %s > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` regexp_like(%s,'%s') and %s <  '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		gbasereader, _ = sjson.Set(gbasereader, "parameter.increColFormat", common.Incre_col_hyphen)
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timetrans = fmt.Sprintf(`regexp_like(%s,'%s') and %s <  '${endtime}' and  %s > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` regexp_like(%s,'%s') and %s < '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		gbasereader, _ = sjson.Set(gbasereader, "parameter.increColFormat", common.Incre_col_slash)

	case common.Incre_Autoid:
		timetrans = fmt.Sprintf(` %s >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` %s is not null `, sp.Extractdb.Createfield)
		gbasereader, _ = sjson.Set(gbasereader, "parameter.increColFormat", common.Incre_Autoid)

	}
	if isdate {
		timetrans = fmt.Sprintf(`%s> (select TO_DATE('${starttime}','yyyy-mm-dd') from dual) and  %s < (select TO_DATE('${endtime}','yyyy-mm-dd') from dual)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` %s < (select TO_DATE ('${endtime}','yyyy-mm-dd') from dual)`, sp.Extractdb.Createfield)
	}
	if istime {
		timetrans = fmt.Sprintf(`%s> (select TO_DATE('${starttime}','yyyy-mm-dd hh24:mi:ss.ff1') from dual) and  %s < (select TO_DATE('${endtime}','yyyy-mm-dd hh24:mi:ss.ff1') from dual)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` %s < (select TO_DATE ('${endtime}','yyyy-mm-dd hh24:mi:ss.ff1') from dual)`, sp.Extractdb.Createfield)
	}

	if isint {
		timetrans = fmt.Sprintf(` %s > ${starttime} `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}
	//-------------------
	firsttimetrans = firsttimetrans + dirtydata
	gbasereader, _ = sjson.Set(gbasereader, "parameter.firstDataCollection", true)
	gbasereader, _ = sjson.Set(gbasereader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, `"`, "", -1))

	// 第一次抽取reader
	sp.Extractdb.SqlTxt = strings.ReplaceAll(sp.Extractdb.SqlTxt, "\"", "\\\"")
	increWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + firsttimetrans
	gbasereader, _ = sjson.Set(gbasereader, "parameter.connection.0.querySql.0", increWhereSql)
	//reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyWhereSql := "select * from (" + sp.Extractdb.SqlTxt + ") accesstb where " + strings.ReplaceAll(sp.Extractdb.Wherecontent, "\"", "\\\"") + timetrans
	nodirtyreader, _ := sjson.Set(gbasereader, "parameter.connection.0.querySql.0", nodirtyWhereSql)
	//nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
        %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, gbasereader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
       %s
       "job": {
               %s
           "content": [
               {
                   "reader":
                   %s,
                   "writer":
                   %s
               }
           ]
       }
       }`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
		sp.Sourcedb.Table, sp.Extractdb.Createfield, sp.Extractdb.Timetype, nodirtytemp, sp.Extractdb.EngineID)
	return templete, nil
}
func NoSpe(str string) string {
	var speStr []string
	speStr = append(speStr, "~", "!", " ", "|", "#", "$", "%", "^", "&", "*", "(", ")", "+", "=", "{", "}", "[", "]", "?", "<", ">", "/", ",", "`", "'", ";", ":", "！", "，", "。", "？", "￥", "……", "（", "）", "——", "【", "】", "、", "、", "：", "；", "“", "”", "‘", "’", "《", "》")
	for _, v := range speStr {
		if strings.Contains(str, v) {
			str = strings.ReplaceAll(str, v, "")
			continue
		}
	}
	return str
}
