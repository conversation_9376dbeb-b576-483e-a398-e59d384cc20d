package mgm

import (
	"bytes"
	"datatom.com/asset/logger"
	"datatom.com/metadata/httpdo/collect"
	"datatom.com/tools/common/tools"
	toolshttp "datatom.com/tools/httpclient"
	"datatom.com/tools/httpdo"
	"datatom/gin.v1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tidwall/gjson"
	"net/http"
	"strings"
	"time"
)

var KafkaRestPort = 8082

/**
请求消息流
步骤一：创建消费者
步骤二：创建消费实例
步骤三：消费数据
*/

func PublishAPIQueryKafka(accessip, consumer_group, instance, topic, max_bytes string, test bool, rowRule RuleInfo) ([]KafkaRecordsStruct, int, error) {
	if accessip == "" {
		return nil, 0, errors.New("内部访问kafkaIP为空")
	}
	err := BuildConsumer(accessip, consumer_group, instance)
	if err != nil {
		return nil, 0, err
	}
	logger.Info.Println("消息流API测试 url1正常结束   ")
	err = BuildConsumerInstance(accessip, consumer_group, instance, topic)
	if err != nil {
		return nil, 0, err
	}
	if test {
		//如果是测试，需要将offset设置为0
		InitOffset(consumer_group, instance, topic)
	}

	logger.Info.Println("消息流API测试 url2正常结束   ")
	return GetRecordsData(accessip, consumer_group, instance, max_bytes, true, rowRule)
}

// 创建消费者
func BuildConsumer(accessIp, consumer_group, instance string) error {
	url := fmt.Sprintf("/consumers/%s/", consumer_group)
	body := gin.H{
		"name":               instance,
		"format":             "binary",
		"auto.commit.enable": "false",
		"auto.offset.reset":  "earliest",
	}
	resbody, _ := json.Marshal(body)
	logger.Info.Println("消息流API测试   1url:", url, "  body : ", string(resbody))
	status, res, err := KafkaHttpRequest(accessIp, KafkaRestPort, url, "POST", string(resbody))
	if err != nil {
		logger.Error.Println("请求kafka接口报错：", accessIp, url, err.Error())
		return err
	}
	rescode := gjson.Get(string(res), "error_code").Int()
	if rescode == 40902 {
		return nil
	}
	resInstance := gjson.Get(string(res), "instance_id").String()
	if resInstance != instance {
		logger.Error.Println("请求kafka接口返回错误：", url, status, res)
		return errors.New("请求kafka创建消费者失败")
	}
	return nil
}

// 创建消费实例
func BuildConsumerInstance(accessip, consumer_group, instance, topic string) error {
	//示例/consumers/{{consumer_group}}/instances/{{instance}}/subscription
	url := fmt.Sprintf("/consumers/%s/instances/%s/subscription", consumer_group, instance)
	body := gin.H{
		"topics": []string{topic},
	}
	resbody, _ := json.Marshal(body)
	logger.Info.Println("消息流API测试   2url:", url, "  body : ", string(resbody))
	_, _, err := KafkaHttpRequest(accessip, KafkaRestPort, url, "POST", string(resbody))
	if err != nil {
		logger.Error.Println("请求kafka接口报错：", accessip, url, err.Error())
		return err
	}
	return nil
}

// kafka消息流返回的数据结构，其中value是string-json体被base64加密的结果
type KafkaRecordsStruct struct {
	Topic     string      `json:"topic"`
	Key       interface{} `json:"key"`
	Value     string      `json:"value"`
	Partition int         `json:"partition"`
	Offset    int         `json:"offset"`
}

// 消费数据
func GetRecordsData(accessip, consumer_group, instance, max_bytes string, firsttime bool, rowRule RuleInfo) ([]KafkaRecordsStruct, int, error) {
	//示例：/consumers/{{consumer_group}}/instances/{{instance}}/records
	url := fmt.Sprintf("/consumers/%s/instances/%s/records?timeout=240000&max_bytes=%s", consumer_group, instance, max_bytes)
	logger.Info.Println("消息流API测试   3url:", url)
	statusstr, res, err := KafkaHttpRequest(accessip, KafkaRestPort, url, "GET", "")
	if err != nil {
		logger.Error.Println(statusstr, " 消息流API3url 请求kafka接口报错：", accessip, url, err.Error())
		return nil, 0, err
	}
	var resList []KafkaRecordsStruct
	json.Unmarshal(res, &resList)
	logger.Info.Println(firsttime, "消息流API测试   3url成功结束：返回结果数量：", len(resList))
	if len(resList) == 0 && firsttime {
		//在首次访问时，可能出现空值
		firsttime = false
		return GetRecordsData(accessip, consumer_group, instance, max_bytes, false, rowRule)
	}

	if rowRule.RuleId != "" {
		if rowRule.RuleType == "AES" {
			for i, temp := range resList {
				tempVstr := httpdo.DecodeBase64(temp.Value)
				orgbyte, _ := json.Marshal(tempVstr)
				encrybyte, _ := tools.AesEcrypt(orgbyte, []byte(rowRule.RuleKey))
				encrystr := hex.EncodeToString(encrybyte)
				resList[i].Value = encrystr
			}
		} else if rowRule.RuleType == "SM4_ECB" {
			for i, temp := range resList {
				tempVstr := httpdo.DecodeBase64(temp.Value)
				orgbyte, _ := json.Marshal(tempVstr)
				encrystr, _ := tools.SM4EcbEncrypt(orgbyte, rowRule.RuleKey)
				resList[i].Value = encrystr
			}
		} else if rowRule.RuleType == "SM4_CBC" {
			for i, temp := range resList {
				tempVstr := httpdo.DecodeBase64(temp.Value)
				orgbyte, _ := json.Marshal(tempVstr)
				encrystr, _ := tools.SM4CbcEncrypt(orgbyte, rowRule.RuleKey)
				resList[i].Value = encrystr
			}
		} else if rowRule.RuleType == "BASE64" {
			//从消息流接口获取的数据就是base64加密的数据，所以此处无须处理
		}
	} else {
		for i, temp := range resList {
			tempVstr := httpdo.DecodeBase64(temp.Value)
			resList[i].Value = tempVstr
		}
	}

	if len(resList) != 0 {
		//提交offset
		posUri := fmt.Sprintf(`/consumers/%s/instances/%s/offsets`, consumer_group, instance)
		KafkaHttpRequest(accessip, KafkaRestPort, posUri, "POST", "")
	}
	return resList, len(resList), nil
}

func KafkaHttpRequest(ip string, port int, uri string, method string, body string) (ststus string, response []byte, err error) {

	url := fmt.Sprintf("http://%s:%d%s", ip, port, uri)
	client := &http.Client{
		Transport: &http.Transport{
			ResponseHeaderTimeout: time.Second * time.Duration(240),
		},
	}
	//client := &http.Client{}
	req, err := http.NewRequest(method, url, strings.NewReader(body))
	if err != nil {
		return "500", []byte(err.Error()), err
	}
	//req.SetBasicAuth(AuthUser, AuthPasswd)
	req.Header.Set("Content-Type", "application/vnd.kafka.jsonschema.v2+json")
	resp, err := client.Do(req)
	if err != nil {
		return "500", []byte(err.Error()), err
	}
	defer resp.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	return resp.Status, buf.Bytes(), nil
}

func BuildIDForKafkaGroup(topic string) string {
	timenow := time.Now().Format("20060102")
	timenow = timenow[2:6]
	uidStr := Krand(5, 3)
	return fmt.Sprintf("%s%s%s", topic, timenow, uidStr)
}

type XiaoxiGroupInfo struct {
	GroupName string `json:"groupname"`
	XiaoxiLiu string `json:"xiaoxiliu"`
	Offset    int64  `json:"offset"`
	Status    string `json:"status"`
}

// 消息流 API访问消息组
func ApiGroupInfo(info ServiceInfo) ([]XiaoxiGroupInfo, error) {
	var GroupInfoList []XiaoxiGroupInfo
	//	根据消费组查询,去重
	var GroupMap = make(map[string]string)
	var GroupIpMap = make(map[string]string)
	for _, v := range info.ApiServer.ApiKafka {
		GroupMap[v.GroupName] = v.Instance
		GroupIpMap[v.GroupName] = v.AccessIp
	}
	//查询kafka接口获取clusterid
	confluentip := tools.CurrentIP()
	clusterid, err := collect.GetMessageClusterId(confluentip)
	if err != nil {
		return GroupInfoList, err
	}
	topicName := info.SourceTBName
	if info.SourceTBType == 2 {
		//资产表，需要增加一层查询，查询真实挂载的实时表名称
		topicName, _ = QueryRealTbNameByInventory(info.SourceTBID)
	}

	//查询消费组
	for k, v := range GroupMap {
		var tempInfo XiaoxiGroupInfo
		tempInfo.GroupName = k
		tempInfo.XiaoxiLiu = topicName
		accessIp := GroupIpMap[k]
		if accessIp == "" {
			accessIp = "127.0.0.1"
		}

		tempUri := fmt.Sprintf(`/v3/clusters/%s/consumer-groups/%s`, clusterid, k)
		_, res, err := KafkaHttpRequest(accessIp, KafkaRestPort, tempUri, "GET", "")
		if err != nil {
			logger.Error.Println("获取state数据失败:", err.Error())
		} else {
			tempInfo.Status = gjson.Get(string(res), "state").String()
		}
		tempUri2 := fmt.Sprintf(`/consumers/%s/instances/%s/offsets`, k, v)
		posBody := gin.H{
			"partitions": []gin.H{
				{
					"topic":     topicName,
					"partition": 0,
				},
			},
		}
		posByte, _ := json.Marshal(posBody)
		_, res2, err2 := KafkaHttpRequest(accessIp, KafkaRestPort, tempUri2, "GET", string(posByte))
		if err2 != nil {
			tempInfo.Offset = -999 //查询错误，也不能展示0值
			logger.Error.Println("获取offsets数据失败:", err.Error())
		} else {
			temparr := gjson.Get(string(res2), "offsets").Array()
			if len(temparr) == 0 {
				tempInfo.Offset = -999 //获取offset为空，与前端设定枚举值
			} else {
				tempInfo.Offset = gjson.Get(string(res2), "offsets.0.offset").Int()
				tempInfo.Offset = tempInfo.Offset - 1
				if tempInfo.Offset < 0 {
					tempInfo.Offset = 0
				}
			}
		}
		GroupInfoList = append(GroupInfoList, tempInfo)
	}
	return GroupInfoList, nil
}

func ApiSetOffset(info ServiceInfo, p QueryAPIMonitor) error {
	var instance, accessIp string
	for _, v := range info.ApiServer.ApiKafka {
		if v.GroupName == p.GroupName {
			instance = v.Instance
			accessIp = v.AccessIp
		}
	}
	if accessIp == "" {
		return errors.New("查询请求IP为空！")
	}
	if instance == "" {
		return errors.New("查询用户组实例为空！")
	}
	temptopic := info.SourceTBName
	var err error
	if info.SourceTBType == 2 {
		//资产表，需要增加一层查询，查询真实挂载的实时表名称
		temptopic, err = QueryRealTbNameByInventory(info.SourceTBID)
		if err != nil {
			return err
		}
	}

	//如果消费组不活跃，重新注册才能正常处理
	err = BuildConsumer(accessIp, p.GroupName, instance)
	if err != nil {
		return err
	}
	err = BuildConsumerInstance(accessIp, p.GroupName, instance, temptopic)
	if err != nil {
		return err
	}
	Hereflag := true
Here:
	//设置offset
	posUri := fmt.Sprintf(`/consumers/%s/instances/%s/positions`, p.GroupName, instance)
	posBody := gin.H{
		"offsets": []gin.H{
			{
				"topic":     temptopic,
				"partition": 0,
				"offset":    p.Offset,
			},
		},
	}
	posByte, _ := json.Marshal(posBody)
	_, posmsg, err := KafkaHttpRequest(accessIp, KafkaRestPort, posUri, "POST", string(posByte))
	if err != nil {
		logger.Error.Println("更新offset失败:", err.Error(), string(posByte))
		return err
	}
	errStr := gjson.Get(string(posmsg), "message").String()
	if errStr != "" {
		// Illegal state: No current assignment for partition application_ss26-0
		if strings.Contains(errStr, "No current assignment for partition") {
			if Hereflag {
				//代表未消费过数据，导致未分配partition，需要消费一次
				//示例：/consumers/{{consumer_group}}/instances/{{instance}}/records
				url := fmt.Sprintf("/consumers/%s/instances/%s/records?timeout=3000&max_bytes=1024", p.GroupName, instance)
				logger.Info.Println("因消费组过期导致未分配partition 消息流API测试   3url:", url)
				KafkaHttpRequest(accessIp, KafkaRestPort, url, "GET", "")
				Hereflag = false
				goto Here
			}
		}

		err = errors.New(errStr)
		logger.Error.Println("更新offset失败:", errStr)
		return err
	}
	//提交offset
	posUri = fmt.Sprintf(`/consumers/%s/instances/%s/offsets`, p.GroupName, instance)
	_, posmsg, err = KafkaHttpRequest(accessIp, KafkaRestPort, posUri, "POST", string(posByte))
	if err != nil {
		logger.Error.Println("提交offset失败:", err.Error(), string(posByte))
		return err
	}
	errStr = gjson.Get(string(posmsg), "message").String()
	if errStr != "" {
		err = errors.New(errStr)
		logger.Error.Println("提交offset失败:", errStr)
		return err
	}

	return nil
}

func InitOffset(groupName, consumerName, topic string) error {
	thisIp := tools.CurrentIP() // 部署在DS节点，直接通过当前节点的IP。
	if thisIp == "" {
		thisIp = "127.0.0.1"
	}
	posUri := fmt.Sprintf(`/consumers/%s/instances/%s/positions`, groupName, consumerName)
	posBody := gin.H{
		"offsets": []gin.H{
			{
				"topic":     topic,
				"partition": 0,
				"offset":    0,
			},
		},
	}
	posByte, _ := json.Marshal(posBody)
	_, posmsg, err := toolshttp.RealHttpRequest(thisIp, 8082, posUri, "POST", string(posByte))
	if err != nil {
		logger.Error.Println("更新offset失败:", err.Error())
		return err
	}
	errStr := gjson.Get(string(posmsg), "message").String()
	if errStr != "" {
		err = errors.New(errStr)
		logger.Error.Println("更新offset失败:", errStr)
		return err
	}
	return nil
}
