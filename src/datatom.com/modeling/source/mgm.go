//Copyright (c) Datatom Software, Inc.(2017)
//
//Author: 祝林（<EMAIL>）
//
//Creating Time: 2018-03-08

package mgm

import (
	"bytes"
	"crypto/tls"
	"database/sql"
	"datatom/gin.v1"
	"encoding/json"
	"errors"
	"fmt"
	"golang.org/x/sync/errgroup"
	"io/ioutil"
	"net/http"
	"os/exec"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"datatom.com/ants/httpdo/folder"
	"datatom.com/metadata/httpdo/dbutil"
	"datatom.com/metadata/httpdo/esutil"
	toolscommon "datatom.com/tools/common"
	"datatom.com/tools/common/rediscli"
	httpclient2 "datatom.com/tools/httpclient"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	red "github.com/gomodule/redigo/redis"

	serveresclient "datatom.com/tools/common/escli"

	common2 "datatom.com/auth/common"

	"datatom.com/auth/handlerpack/user"
	"datatom.com/metadata/httpdo/collect"
	"datatom.com/metadata/source"
	simplejson "github.com/bitly/go-simplejson"
	_ "github.com/lib/pq"
	"github.com/tidwall/gjson"
	"github.com/uxff/gohive"

	assetcommon "datatom.com/asset/common"
	"datatom.com/modeling/common"
	"datatom.com/modeling/httpclient"
	"datatom.com/modeling/logger"
	commontools "datatom.com/tools/common/tools"
	tools "datatom.com/tools/httpdo"
)

func Build(info Table, databaseinfo DatabaseInfo, sqltxt string, sqltxt1 string, sqltxt2 string, sqltxt3 string, describe string) (string, error) {

	//在stork中创建表
	//var db *sql.DB
	if sqltxt1 != "" {
		db, err := getDBcnn(databaseinfo)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return "", err
		}
		if db != nil {
			defer db.Close()
		}
		rows, err := db.Query(sqltxt)
		if rows != nil {
			defer rows.Close()
		}
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return "", err
		}
		rows, err = db.Query(sqltxt1)
		if rows != nil {
			defer rows.Close()
		}
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt1 + "执行错误:" + err.Error())
			return "", err
		}
		rows, err = db.Query(sqltxt2)
		if rows != nil {
			defer rows.Close()
		}
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt2 + "执行错误:" + err.Error())
			return "", err
		}
		rows, err = db.Query(sqltxt3)
		if rows != nil {
			defer rows.Close()
		}
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt3 + "执行错误:" + err.Error())
			return "", err
		}

		if describe != `` {
			rows2, err := db.Query(describe)
			if rows2 != nil {
				defer rows2.Close()
			}
			if err != nil {
				logger.Error.Println("执行语句:" + describe + "执行错误:" + err.Error())
				return "", err
			}
		}

	} else {
		db, err := getDBcnn(databaseinfo)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return "", err
		}
		if db != nil {
			defer db.Close()
		}
		rows, err := db.Query(sqltxt)
		if rows != nil {
			defer rows.Close()
		}
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return "", err
		}
		if describe != `` {
			rows2, err := db.Query(describe)
			if rows2 != nil {
				defer rows2.Close()
			}
			if err != nil {
				logger.Error.Println("执行语句:" + describe + "执行错误:" + err.Error())
				return "", err
			}
		}
	}

	//创建标签
	if len(info.Tag) != 0 {
		info.TagID, _ = CreateTags(info.Tag)
	}

	//将记录写入eagles
	//byteBody, err := json.Marshal(info)
	//uri := fmt.Sprintf("/%s/%s", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.AddWithoutID(Database, NewTbAsset, info)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	info.Id, err = GetDocId(response)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}

	var body = gin.H{
		"id": info.Id,
	}
	//byteBody, _ = json.Marshal(body)
	//todo

	//id, err := mgm.GetDocId(res)
	err = serveresclient.NewESClientCl.UpdByID(Database, NewTbAsset, info.Id, body)
	//uri = fmt.Sprintf("/%s/%s/%s/_update", Database, NewTbAsset, info.Id)
	//response, err = httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	return info.Id, nil
}

func getDBcnn(info DatabaseInfo) (*sql.DB, error) {

	var db *sql.DB
	pgLink := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		info.User,
		info.Passwd,
		info.Ip,
		info.Port,
		info.Database)

	db, err := sql.Open("postgres", pgLink)
	if err != nil {
		db.Close()
		return db, err
	}

	return db, nil
}

func GetInstance(engineid string) (DatabaseInfo, error) {
	var database DataInfo
	uri := fmt.Sprintf("/danastudio/platform/engines/get")
	var body = gin.H{
		"engineid": engineid,
	}

	byteBody, _ := json.Marshal(body)
	response, err := httpclient.EaglesPost(common.CAYMAN_MANAGE_IP, common.CAYMAN_MANAGE_PORT, uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return database.DatabaseMes, err
	}

	err = json.Unmarshal(response, &database)
	if err != nil {
		logger.Error.Println(err)
		return database.DatabaseMes, err
	}

	return database.DatabaseMes, err
}

func GetClusterid() (string, error) {
	var clusterid string
	uri := fmt.Sprintf("/cluster/conf/getclusterid")
	var resp map[string]interface{}
	response, err := httpclient.EaglesPost(common.CAYMAN_MANAGE_IP, common.CAYMAN_MANAGE_PORT, uri, "")
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	var res interface{}
	err = json.Unmarshal(response, &resp)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	res = resp["result"].(map[string]interface{})["clusterid"]
	clusterid = res.(string)

	return clusterid, err
}

func SubjectCount() ([]Bucket, error) {
	body := gin.H{
		"aggs": gin.H{
			"all_subject": gin.H{
				"terms": gin.H{
					"field": "subject",
					"size":  UnlimitSize,
				},
			},
		},
	}

	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewTbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return []Bucket{}, err
	}

	var aggs Aggregations
	err = json.Unmarshal(response, &aggs)

	return aggs.Aggregation.Subjects.BucketArr, nil
}

func Search(subject string, page int, perpage int, tablename string, anothername string, databasetype string, star string, tag TagBody, userid string) ([]Info, int, error) {
	var must []map[string]interface{}
	var should []map[string]interface{}
	var sort []map[string]interface{}
	tablename = fmt.Sprintf("*%s*", tablename)
	anothername = fmt.Sprintf("*%s*", anothername)
	var orderByTime = gin.H{
		"createtime": gin.H{
			"order": "desc",
		},
	}
	sort = append(sort, orderByTime)
	if tablename != "" {
		query_tbname := gin.H{

			"query_string": gin.H{
				"default_field":            "tablename",
				"query":                    tablename,
				"lowercase_expanded_terms": false,
			},
		}
		should = append(should, query_tbname)
	}
	if anothername != "" {
		query_another := gin.H{

			"query_string": gin.H{
				"default_field":            "anothername",
				"query":                    anothername,
				"lowercase_expanded_terms": false,
			},
		}
		should = append(should, query_another)
	}
	var shouldbody = gin.H{
		"bool": gin.H{
			"should": should,
		},
	}
	must = append(must, shouldbody)
	if subject != "" {
		query_subject := gin.H{

			"term": gin.H{
				"subject": gin.H{
					"value": subject,
				},
			},
		}
		must = append(must, query_subject)
	}
	if databasetype != "" {
		query_type := gin.H{

			"term": gin.H{
				"databasetype": gin.H{
					"value": databasetype,
				},
			},
		}
		must = append(must, query_type)
	}
	if userid != "" {
		query_userid := gin.H{

			"term": gin.H{
				"userid": gin.H{
					"value": userid,
				},
			},
		}
		must = append(must, query_userid)
	}
	if star != "" {
		query_star := gin.H{

			"term": gin.H{
				"star": gin.H{
					"value": star,
				},
			},
		}
		must = append(must, query_star)
	}
	if tag.Name != "" {
		query_tag := gin.H{

			"query_string": gin.H{
				"default_field":            "tags.name",
				"query":                    tag.Name,
				"lowercase_expanded_terms": false,
			},
		}
		must = append(must, query_tag)
	}
	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
		"sort": sort,
		"size": perpage,
		"from": (page - 1) * perpage,
	}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", DataBase, TableModel)
	//	fmt.Print(string(byteBody))
	response, err := serveresclient.NewESClientCl.SearchByTerm(DataBase, TableModel, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return []Info{}, 0, err
	}

	var source HitsResult

	err = json.Unmarshal(response, &source)

	if err != nil {
		logger.Error.Println(err)
		return []Info{}, 0, err
	}
	t := source.Hits.Total
	var array []Info

	for i := 0; i < len(source.Hits.HitsT); i++ {
		array = append(array, source.Hits.HitsT[i].EaglesInfo)
	}

	return array, t, nil
}

func Get(id string) (Info, error) {

	//uri := fmt.Sprintf("/%s/%s/%s", DataBase, TableModel, id)
	response, err := serveresclient.NewESClientCl.SearchByIDResult(DataBase, TableModel, id)
	//response, err := httpclient.Get(uri, "")
	if err != nil {
		logger.Error.Println(err)
		return Info{}, err
	}

	var body SrcEagles
	err = json.Unmarshal(response, &body)
	if err != nil {
		logger.Error.Println(err)
		return Info{}, err
	}

	return body.EaglesInfo, nil
}

func GetInfo(id string) (Table, error) {

	//uri := fmt.Sprintf("/%s/%s/%s", Database, NewTbAsset, id)
	response, err := serveresclient.NewESClientCl.SearchByIDResult(DataBase, NewTbAsset, id)
	//response, err := httpclient.Get(uri, "")
	if err != nil {
		logger.Error.Println(err)
		return Table{}, err
	}

	var body SrcTable
	err = json.Unmarshal(response, &body)
	if err != nil {
		logger.Error.Println(err)
		return Table{}, err
	}

	return body.Tableinfo, nil
}

func GetUserid(id string) (string, error) {

	//uri := fmt.Sprintf("/%s/%s/%s", Database, NewTbAsset, id)
	response, err := serveresclient.NewESClientCl.SearchByIDResult(DataBase, NewTbAsset, id)
	//response, err := httpclient.Get(uri, "")
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}

	var body SrcTable
	err = json.Unmarshal(response, &body)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}

	return body.Tableinfo.Userid, nil
}

func Update(info Info) error {
	//创建标签
	if len(info.Tag) != 0 {
		info.TagID, _ = CreateTags(info.Tag)
	}

	//var body = gin.H{
	//	"doc": info,
	//}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/%s/_update", Database, NewTbAsset, info.Id)
	err := serveresclient.NewESClientCl.UpdByID(Database, NewTbAsset, info.Id, info)
	//_, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	return nil
}

func UpdateNew(info ParamUpdate) error {
	err := UpdateTable(info)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	//创建标签
	if len(info.BasicInfo.Tag) != 0 {
		info.BasicInfo.TagID, _ = CreateTags(info.BasicInfo.Tag)
	} else {
		var tmp []string
		info.BasicInfo.TagID = tmp
	}

	info.BasicInfo.UpdateTime = time.Now().Format(DateFormat)

	//var body = gin.H{
	//	"doc": info.BasicInfo,
	//}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/%s/_update", Database, NewTbAsset, info.BasicInfo.Id)
	err = serveresclient.NewESClientCl.UpdByID(Database, NewTbAsset, info.BasicInfo.Id, info.BasicInfo)
	//_, err = httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	return nil
}

func Delete(databaseinfo DatabaseInfo, id string, schemaname string, tablename string) error {
	db, err := getDBcnn(databaseinfo)
	if db != nil {
		defer db.Close()
	}
	if err != nil {
		logger.Error.Println("连接出错：", err)
		return err
	}

	sqltxt := fmt.Sprintf("DROP TABLE \"%s\".\"%s\"", schemaname, tablename)
	rows, err := db.Query(sqltxt)
	if rows != nil {
		defer rows.Close()
	}

	if err != nil {
		logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
		return errors.New("模型" + tablename + "删除失败")
	}
	//uri := fmt.Sprintf("/%s/%s/%s", Database, NewTbAsset, id)
	err = serveresclient.NewESClientCl.DelByIDWithRefresh(Database, NewTbAsset, "true", id)
	//_, err = httpclient.Delete(uri, "")
	if err != nil {
		logger.Error.Println(err)
		return errors.New("模型" + tablename + "删除失败")
	}
	return nil
}

// Columns 查看表结构
func Columns(info DatabaseInfo, schemaname string, table string) ([]map[string]interface{}, error) {

	var rowMaps []map[string]interface{}

	db, err := getDBcnn(info)
	defer db.Close()
	if err != nil {
		logger.Error.Println("连接出错：", err)
		return rowMaps, err
	}

	sqltxt := fmt.Sprintf(` Select (select description from pg_catalog.pg_description 
								where objoid=a.attrelid and objsubid=a.attnum) as descript
 								,a.attname as column ,pg_catalog.format_type(a.atttypid,a.atttypmod)  as type 
								from pg_catalog.pg_attribute a where 1=1 
								and a.attrelid=(select oid from pg_class where relname='%s' AND relnamespace =(SELECT oid FROM pg_namespace WHERE nspname ='%s')) 
								and a.attnum>0 and not a.attisdropped order by a.attnum; `,
		table, schemaname)

	rows, err := db.Query(sqltxt)
	defer rows.Close()
	if err != nil {
		logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
		return rowMaps, err
	}

	rowMaps, err = sqlrows2Maps(rows)
	if err != nil {
		logger.Error.Println("解析出错：", err)
		return rowMaps, err
	}

	return rowMaps, nil
}

// sqlrows2Maps sql查询结果rows转为maps
func sqlrows2Maps(rws *sql.Rows) ([]map[string]interface{}, error) {

	var rowMaps []map[string]interface{}

	var columns []string
	columns, err := rws.Columns()
	if err != nil {
		return rowMaps, err
	}

	values := make([]sql.RawBytes, len(columns))
	scans := make([]interface{}, len(columns))
	for i := range values {
		scans[i] = &values[i]
	}

	for rws.Next() {
		_ = rws.Scan(scans...)
		each := map[string]interface{}{}
		for i, col := range values {
			each[columns[i]] = string(col)
		}

		rowMaps = append(rowMaps, each)
	}

	return rowMaps, nil
}

// getdata 查看表数据
func GetData(info DatabaseInfo, schemaname string, table string) ([]map[string]interface{}, error) {

	var rowMaps []map[string]interface{}

	db, err := getDBcnn(info)
	defer db.Close()
	if err != nil {
		logger.Error.Println("连接出错：", err)
		return rowMaps, err
	}

	sqltxt := fmt.Sprintf(`select *
							from "%s"."%s" LIMIT 500`,
		schemaname, table)

	rows, err := db.Query(sqltxt)
	logger.Debug.Println("sqltxt : ", sqltxt)
	//	fmt.Println(sqltxt)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil {
		logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
		return rowMaps, err
	}

	rowMaps, err = sqlrows2Maps(rows)
	if err != nil {
		logger.Error.Println("解析出错：", err)
		return rowMaps, err
	}

	return rowMaps, nil
}

// 批量删除
func DeleteBulk(body string) error {

	//uri := fmt.Sprintf("/%s/%s/_delete_by_query", Database, NewTbAsset)
	err := serveresclient.NewESClientCl.DelByQuery(Database, NewTbAsset, body)
	//_, err := httpclient.Post(uri, body)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

func DropTables(body string) error {
	var databaseinfo DatabaseInfo
	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewTbAsset)
	//	fmt.Print(string(byteBody))
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewTbAsset, body)
	//response, err := httpclient.Post(uri, body)
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	var source TableHitsRes1

	err = json.Unmarshal(response, &source)

	if err != nil {
		logger.Error.Println(err)
		return err
	}
	//检测模型依赖
	for i := 0; i < len(source.Hits1.Hits2); i++ {
		str, err := GetModel(source.Hits1.Hits2[i].Table.TableName, source.Hits1.Hits2[i].Table.Databasename, source.Hits1.Hits2[i].Table.Engineid, source.Hits1.Hits2[i].Table.Layerid)
		if err != nil {
			//logger.Error.Println(err)
			return err
		}
		if str != "" {
			return errors.New(str)
		}
	}

	for i := 0; i < len(source.Hits1.Hits2); i++ {
		databaseinfo, err = GetInstance(source.Hits1.Hits2[i].Table.Engineid)
		if err != nil {
			return err
		}

		if databaseinfo.DataBaseType == "postgres" || databaseinfo.DataBaseType == "greenplum" || databaseinfo.DataBaseType == "stork" || databaseinfo.DataBaseType == "teryx" || databaseinfo.DataBaseType == common.GAUSSDB {

			tables := fmt.Sprintf("\"%s\".\"%s\"", source.Hits1.Hits2[i].Table.Schemaname, source.Hits1.Hits2[i].Table.TableName)
			tablename := fmt.Sprintf("%s", source.Hits1.Hits2[i].Table.TableName)
			databaseinfo.Database = source.Hits1.Hits2[i].Table.Databasename
			db, err := getDBcnn(databaseinfo)
			defer db.Close()
			if err != nil {
				logger.Error.Println("连接出错：", err)
				return err
			}

			sqltxt := fmt.Sprintf("DROP TABLE %s", tables)
			_, err = db.Query(sqltxt)
			if err != nil {
				logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
				return errors.New("模型" + tablename + "删除失败")
			}
		} else if databaseinfo.DataBaseType == "hive" {
			tablename := fmt.Sprintf("%s", source.Hits1.Hits2[i].Table.TableName)
			databaseinfo.Database = source.Hits1.Hits2[i].Table.Databasename
			sqltxt := fmt.Sprintf("drop table if exists %s.%s", databaseinfo.Database, tablename)

			switch databaseinfo.HiveVersion {
			case HiveTdh:
				_, err := TDHiveRes(databaseinfo.Ip, databaseinfo.Port, databaseinfo.User, databaseinfo.Passwd, sqltxt, databaseinfo.TdhKbsinfo)
				if err != nil {
					logger.Error.Println(err)
					//return errors.New("模型" + tablename + "删除失败")
				}
			case Hive12, Hive30, Hive31, "":
				err := HiveExec(databaseinfo.Ip, databaseinfo.Port, sqltxt)
				if err != nil {
					//logger.Error.Println(err)
					return errors.New("模型" + tablename + "删除失败")
				}
			case HiveHuawei:
				err := HDConnect(databaseinfo, sqltxt)
				if err != nil {
					//logger.Error.Println(err)
					return errors.New("模型刪除失败")
				}
			case HiveCDH:
				_, err := tools.CDHExecSQL(databaseinfo.Instance, sqltxt)
				if err != nil {
					logger.Error.Println(err)
					return errors.New("模型刪除失败")
				}
			case HiveTDC:
				_, err := tools.TDCRes(databaseinfo.Ip, databaseinfo.Port, sqltxt, databaseinfo.TdhKbsinfo.Token)
				if err != nil {
					logger.Error.Println(err)
					return errors.New("模型刪除失败")
				}
			}
		}
	}
	return nil
}

// 批量更新
func UpdateBulk(ids []string, userid string) error {
	var must []map[string]interface{}
	var should []map[string]interface{}
	for i := 0; i < len(ids); i++ {

		query_tbname := gin.H{

			"query_string": gin.H{
				"default_field":            "_id",
				"query":                    ids[i],
				"lowercase_expanded_terms": false,
			},
		}
		should = append(should, query_tbname)

	}
	var shouldbody = gin.H{
		"bool": gin.H{
			"should": should,
		},
	}
	must = append(must, shouldbody)
	if userid != "admin_user" {
		query_userid := gin.H{

			"term": gin.H{
				"userid": gin.H{
					"value": userid,
				},
			},
		}
		must = append(must, query_userid)
	}

	var body = gin.H{
		"script": gin.H{
			"inline": "ctx._source.star = 'STAR'",
		},
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
	}
	byteBody, _ := json.Marshal(body)
	//fmt.Println(string(byteBody))
	uri := fmt.Sprintf("/%s/%s/_update_by_query", DataBase, TableModel)
	_, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

func TagCount() ([]Bucket, error) {
	body := gin.H{
		"size": 0,
		"aggs": gin.H{
			"all_subject": gin.H{
				"terms": gin.H{
					"field": "concat",
					"order": gin.H{
						"_count": "desc",
					},
					"size": 1000,
				},
			},
		},
	}

	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewTbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return []Bucket{}, err
	}

	var aggs Aggregations
	err = json.Unmarshal(response, &aggs)

	return aggs.Aggregation.Subjects.BucketArr, nil
}

func ListInstance(clusterid string, databasetype string) ([]DatabaseInfo, error) {
	var array []DatabaseInfo
	var must []map[string]interface{}
	if databasetype != "" {
		query_another := gin.H{

			"query_string": gin.H{
				"default_field":            "type",
				"query":                    databasetype,
				"lowercase_expanded_terms": false,
			},
		}
		must = append(must, query_another)
	}
	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},

		"size": 30,
	}
	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/%scayman_instance/instance/_search", clusterid)
	var resp map[string]interface{}
	response, err := httpclient.EaglesPost(common.CAYMAN_EAGLES_IP, common.CAYMAN_EAGLES_PORT, uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}
	var res interface{}
	err = json.Unmarshal(response, &resp)
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}
	res = resp["hits"].(map[string]interface{})["hits"]
	byt, err := json.Marshal(res)
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}
	var arr []Source
	err = json.Unmarshal(byt, &arr)
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}
	for i := 0; i < len(arr); i++ {
		array = append(array, arr[i].SourceInfo)
	}
	return array, err

}

func ListIndex(ip string, port int) ([]CatResultT, error) {

	uri := fmt.Sprintf("/_cat/indices?format=json")
	response, err := httpclient.EaglesGet(ip, port, uri, "")
	if err != nil {
		logger.Error.Println(err)
		return []CatResultT{}, err
	}

	var array []CatResultT
	err = json.Unmarshal(response, &array)
	var array1 []CatResultT
	for i := 0; i < len(array); i++ {
		if (strings.HasPrefix(array[i].CatIndex, ".")) || (strings.EqualFold(array[i].CatIndex, "system")) {
			continue
		}
		array1 = append(array1, array[i])
	}

	if err != nil {
		logger.Error.Println(err)
		return []CatResultT{}, err
	}
	return array1, nil
}

func AlterTable(databaseinfo DatabaseInfo, sqltxt string) error {
	db, err := getDBcnn(databaseinfo)
	if db != nil {
		defer db.Close()
	}

	if err != nil {
		logger.Error.Println("连接出错：", err)
		return err
	}

	rows, err := db.Query(sqltxt)
	if rows != nil {
		defer rows.Close()
	}

	if err != nil {
		logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
		return err
	}

	return nil
}

func CountLayer() ([]Bucket, error) {
	body := gin.H{
		"aggs": gin.H{
			"all_subject": gin.H{
				"terms": gin.H{
					"field": "layerid",
					"size":  UnlimitSize,
				},
			},
		},
	}

	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewTbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return []Bucket{}, err
	}

	var aggs Aggregations
	err = json.Unmarshal(response, &aggs)

	return aggs.Aggregation.Subjects.BucketArr, nil
}

func SearchTable(info SearchJson, auth string) ([]Table, int, error) {
	var tableinfo []Table
	var sort []map[string]interface{}
	var must []map[string]interface{}
	sorttype := gin.H{
		"createtime": gin.H{
			"order": "desc",
		},
	}
	sort = append(sort, sorttype)

	tablename := fmt.Sprintf("*%s*", info.TableName)

	if tablename != "" {
		query_tbname := gin.H{
			"query_string": gin.H{
				"default_field":            "tablename",
				"query":                    tablename,
				"lowercase_expanded_terms": false,
			},
		}
		must = append(must, query_tbname)
	}
	if info.Layerid != "" {
		query_layerid := gin.H{
			"term": gin.H{
				"layerid": gin.H{
					"value": info.Layerid,
				},
			},
		}
		must = append(must, query_layerid)
	}
	if info.Databasename != "" {
		query_databasename := gin.H{
			"term": gin.H{
				"databasename": gin.H{
					"value": info.Databasename,
				},
			},
		}
		must = append(must, query_databasename)
	}
	if info.Subject != "" {
		query_subject := gin.H{
			"term": gin.H{
				"subject": gin.H{
					"value": info.Subject,
				},
			},
		}
		must = append(must, query_subject)
	}
	if info.Userid != "" {
		query_userid := gin.H{
			"term": gin.H{
				"userid": gin.H{
					"value": info.Userid,
				},
			},
		}
		must = append(must, query_userid)
	}
	var body gin.H
	if info.Prepage != -1 {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": must,
				},
			},
			"sort": sort,
			"size": info.Prepage,
			"from": (info.Page - 1) * info.Prepage,
		}
	} else {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": must,
				},
			},
			"sort": sort,
		}
	}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewTbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return tableinfo, 0, err
	}
	var result TableHitsRes1

	err = json.Unmarshal(response, &result)

	if err != nil {
		logger.Error.Println(err)
		return tableinfo, 0, err
	}

	t := result.Hits1.Total

	for i := 0; i < len(result.Hits1.Hits2); i++ {
		//获取标签
		result.Hits1.Hits2[i].Table.Tag, _ = GetTags(result.Hits1.Hits2[i].Table.TagID)
		// 修正生命周期
		if result.Hits1.Hits2[i].Table.Days == 0 {
			result.Hits1.Hits2[i].Table.Days = -1
		}
		// 元数据信息修正
		err = FixMetaTable(&result.Hits1.Hits2[i].Table)
		if err != nil {
			logger.Error.Println(err)
			return tableinfo, 0, err
		}
		tableinfo = append(tableinfo, result.Hits1.Hits2[i].Table) //[]Table
	}
	//添加 jobid
	var p ParamTBList
	p.Sorttype = DefaultSortType
	layerMap := LayerMap()
	doc := gin.H{
		"size": ESSize,
	}
	//byteBody, _ = json.Marshal(doc)

	//uri = fmt.Sprintf("/danastudio-asset/db_layer/_search")
	resp, err := serveresclient.NewESClientCl.SearchByTerm("/danastudio-asset", "db_layer", doc)
	//resp, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return tableinfo, 0, err
	}

	var layerList []LayerInfo
	s := gjson.Get(string(resp), "hits.hits").Array()
	for _, v := range s {
		var layer LayerInfo
		item := v.Get("_source").String()
		err = json.Unmarshal([]byte(item), &layer)
		if err != nil {
			return tableinfo, 0, err
		}
		if info.Layerid == layer.ID {
			p.Layer = layerMap[layer.ColorNum]
		}
		layerList = append(layerList, layer)
	}
	//fmt.Println("layer list : ", layerList)
	//补充 layer 信息
	if p.Layer == "" {
		p.Layer = DefaultLayer
	}
	resTbList, err := TBList(p, auth) //[]SubBasicInfo
	if err != nil {
		//logger.Error.Println(err)
		return tableinfo, 0, err
	}
	//fmt.Println("restblist : ", resTbList)
	tableinfo, _ = convertTB(tableinfo, resTbList, layerList) //[]Table,[]SubBasicInfo

	return tableinfo, t, err
}

func ListDatabase() ([]DbInfo, error) {
	var array []DbInfo
	var body = gin.H{
		"size": 10000,
	}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewDbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewDbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}

	var result DbHitsRes1
	json.Unmarshal(response, &result)
	metaList := result.Hits1.Hits2
	for _, v := range metaList {
		array = append(array, v.Db)
	}

	return array, nil
}

func SearchDatabase(layerid string) ([]DbInfo, error) {
	var array []DbInfo

	var musts []map[string]interface{}
	var must map[string]interface{}
	if layerid == "" {
		must = gin.H{
			"query_string": gin.H{
				"default_field": "layerid",
				"query":         "*",
			},
		}
		musts = append(musts, must)

	} else {
		must = gin.H{
			"term": gin.H{
				"layerid": layerid,
			},
		}
		musts = append(musts, must)
	}

	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": 10000,
	}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewDbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewDbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}

	var result DbHitsRes1
	json.Unmarshal(response, &result)
	metaList := result.Hits1.Hits2
	for _, v := range metaList {
		array = append(array, v.Db)
	}

	return array, nil
}

func ListLayer(databasename string) ([]DbInfo, error) {
	var array []DbInfo
	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"databasename": gin.H{
							"value": databasename,
						},
					},
				},
			},
		},
		"size": 10000,
	}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewDbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewDbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}

	var result DbHitsRes1
	json.Unmarshal(response, &result)
	metaList := result.Hits1.Hits2
	for _, v := range metaList {
		array = append(array, v.Db)
	}

	return array, nil
}

// 检查数据表是否已经存在
func Check(databaseinfo DatabaseInfo, sqltxt string) ([]map[string]interface{}, error) {
	var rowMaps []map[string]interface{}
	db, err := getDBcnn(databaseinfo)
	if err != nil {
		logger.Error.Println("连接出错：", err)
		return rowMaps, err
	}
	if db != nil {
		defer db.Close()
	}
	rows, err := db.Query(sqltxt)
	if err != nil {
		logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
		return rowMaps, err
	}
	if rows != nil {
		defer rows.Close()
	}
	rowMaps, err = sqlrows2Maps(rows)
	if err != nil {
		logger.Error.Println("解析出错：", err)
		return rowMaps, err
	}
	return rowMaps, nil
}

func GetModel(table, database, engineid, layerid string) (string, error) {
	var terms []map[string]interface{}
	var terms1 []map[string]interface{}
	var terms2 []map[string]interface{}

	//数据采集(单例)
	term1 := gin.H{
		"term": gin.H{
			"sourcedb.table": table,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"sourcedb.odsdatabase": database,
		},
	}
	terms = append(terms, term1, term2)

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 1000,
	}
	//byteBody, _ := json.Marshal(body)
	//uri := fmt.Sprintf("/danastudio_ants_lab/tb_access/_search")
	resp, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAnts, common.TbAccess, body)
	//resp, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	e := gjson.Get(string(resp), "hits.hits").Array()
	if len(e) != 0 {
		//model.Notename = v.Get("_source.notename").String()
		return "模型被数据采集任务依赖，不可删除", nil
	}

	//批量
	if strings.Contains(table, "ods_") {
		table = strings.Replace(table, "ods_", "", -1)
		var musts []map[string]interface{}
		term1 = gin.H{
			"term": gin.H{
				"sourcedb.odsdatabase": database,
			},
		}
		term2 = gin.H{
			"term": gin.H{
				"extractdb.batchtables": table,
			},
		}
		musts = append(musts, term1, term2)

		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": musts,
				},
			},
			"size": 1000,
		}
		//byteBody, _ = json.Marshal(body)
		//uri = fmt.Sprintf("/danastudio_ants_lab/tb_access/_search")
		resp, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAnts, common.TbAccess, body)
		//resp, err = httpclient.Post(uri, string(byteBody))
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		e = gjson.Get(string(resp), "hits.hits").Array()
		if len(e) != 0 {
			//model.Notename = v.Get("_source.notename").String()
			return "模型被数据采集任务依赖，不可删除", nil
		}
	}

	//数据治理
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": layerid,
					},
				},
			},
		},
	}
	//byteBody, _ = json.Marshal(body)
	if err != nil {
		return "", err
	}

	//uri = fmt.Sprintf("/danastudio-asset/db_layer/_search")
	resp, err = serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAsset, common.DbLayer, body)
	//resp, err = httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	layername := gjson.Get(string(resp), "hits.hits.0._source.layername").String()

	//根据分层去做判断
	if layername == "BASE治理层" && (database == "basedb" || database == "wt") {
		if strings.Contains(table, "basedb_") || strings.Contains(table, "xxn_") || strings.Contains(table, "tmp_") || strings.Contains(table, "_tmp") || strings.Contains(table, "cf_") { //固定类型的判断
			return "模型被数据治理任务依赖，不可删除", nil
		} else if database == "wt" { //wt库的判断
			if Iswttable(table) {
				return "模型被数据治理任务依赖，不可删除", nil
			}
		} else { //表名为id的判断
			body = gin.H{
				"query": gin.H{
					"bool": gin.H{
						"must": gin.H{
							"id": table,
						},
					},
				},
				"size": 1000,
			}
			//byteBody, _ = json.Marshal(body)
			//uri = fmt.Sprintf("/danastudio_ants_lab/tb_dev/_search")
			resp, err = serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAnts, common.TbDev, body)
			//resp, err = httpclient.Post(uri, string(byteBody))
			if err != nil {
				logger.Error.Println(err)
				return "", err
			}
			e = gjson.Get(string(resp), "hits.hits").Array()
			if len(e) != 0 {
				return "模型被数据治理任务依赖，不可删除", nil
			}
		}
	} else if layername == "ODS汇聚层" {
		term1 := gin.H{
			"term": gin.H{
				"tbinfo.layerid": layerid,
			},
		}
		term2 := gin.H{
			"term": gin.H{
				"tbinfo.tbname": table,
			},
		}
		term3 := gin.H{
			"term": gin.H{
				"tbinfo.dbname": database,
			},
		}
		terms1 = append(terms1, term1, term2, term3)
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": terms1,
				},
			},
			"size": 1000,
		}
		//byteBody, _ = json.Marshal(body)
		resp, err = serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAnts, common.TbDev, body)
		//uri = fmt.Sprintf("/danastudio_ants_lab/tb_dev/_search")
		//resp, err = httpclient.Post(uri, string(byteBody))
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		e = gjson.Get(string(resp), "hits.hits").Array()
		if len(e) != 0 {
			return "模型被数据治理任务依赖，不可删除", nil
		}
	} else if layername == "DWD主题层" {
		if strings.Contains(table, "dwd_") {
			table = strings.Replace(table, "dwd_", "DWD_", -1)
		}
		term1 := gin.H{
			"term": gin.H{
				"finaltable.engineid": engineid,
			},
		}
		term2 := gin.H{
			"term": gin.H{
				"finaltable.tbname": table,
			},
		}
		term3 := gin.H{
			"term": gin.H{
				"finaltable.dbname": database,
			},
		}
		terms2 = append(terms2, term1, term2, term3)
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": terms2,
				},
			},
			"size": 1000,
		}
		//byteBody, _ = json.Marshal(body)
		//uri = fmt.Sprintf("/danastudio_ants_lab/tb_dev/_search")
		resp, err = serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAnts, common.TbDev, body)
		//resp, err = httpclient.Post(uri, string(byteBody))
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		e = gjson.Get(string(resp), "hits.hits").Array()
		if len(e) != 0 {
			return "模型被数据治理任务依赖，不可删除", nil
		}

	}

	return "", nil

}

func HiveColumns(info DatabaseInfo, table string) ([]map[string]interface{}, error) {
	var sp []map[string]interface{}
	sqltxt := "desc " + "\"" + info.Database + "\"" + "." + "\"" + table + "\""

	switch info.HiveVersion {
	case HiveTdh:
		res, err := TDHiveRes(info.Ip, info.Port, info.User, info.Passwd, sqltxt, info.TdhKbsinfo)
		if err != nil {
			//logger.Error.Println(err)
			return sp, err
		}
		rec, err := ResTransform(res)
		if err != nil {
			//logger.Error.Println(err)
			return sp, err
		}

		result := ColumnToLine(rec)

		for _, v := range result {
			record := gin.H{
				"column":   v["col_name"],
				"descript": v["comment"],
				"type":     v["data_type"],
			}
			sp = append(sp, record)
		}
	case Hive12, Hive30, Hive31, "":
		sqltxt := "desc " + "`" + info.Database + "`" + "." + "`" + table + "`"
		rets, err := HiveQuery(info.Ip, info.Port, sqltxt)
		if err != nil {
			//logger.Error.Println(err)
			return sp, err
		}

		for i := 0; i < len(rets); i++ {
			data := rets[i]
			column, _ := data["col_name"]
			descript, _ := data["comment"]
			types, _ := data["data_type"]
			record := gin.H{
				"column":   column,
				"descript": descript,
				"type":     types,
			}
			sp = append(sp, record)
		}
	case HiveHuawei, HiveCDH:
		conn, err := PrestoConnect(info.Database)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return sp, err
		}
		rows, err := conn.Query(sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return sp, err
		}

		result, err := sqlrows2Maps(rows)
		if err != nil {
			logger.Error.Println("解析出错：", err)
			return result, err
		}
		for i := 0; i < len(result); i++ {
			data := result[i]
			column, _ := data["Column"]
			descript, _ := data["Comment"]
			types, _ := data["Type"]
			record := gin.H{
				"column":   column,
				"descript": descript,
				"type":     types,
			}
			sp = append(sp, record)
		}

	case HiveTDC:
		res, err := tools.TDCRes(info.Ip, info.Port, sqltxt, info.TdhKbsinfo.Token)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return sp, err
		}
		rec, err := ResTransform(res)
		if err != nil {
			//logger.Error.Println(err)
			return sp, err
		}

		result := ColumnToLine(rec)

		for _, v := range result {
			record := gin.H{
				"column":   v["col_name"],
				"type":     v["data_type"],
				"descript": v["comment"],
			}
			sp = append(sp, record)
		}
	}

	return sp, nil
}

func GetHiveData(info DatabaseInfo, table string) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	sqltxt := fmt.Sprintf("select * from \"%s\".\"%s\" limit 500", info.Database, table)

	switch info.HiveVersion {
	case HiveTdh:
		res, err := TDHiveRes(info.Ip, info.Port, info.User, info.Passwd, sqltxt, info.TdhKbsinfo)
		if err != nil {
			//logger.Error.Println(err)
			return result, err
		}
		rec, err := ResTransform(res)
		if err != nil {
			//logger.Error.Println(err)
			return result, err
		}
		result = ColumnToLine(rec)
	case Hive12, Hive30, Hive31, "":
		sqltxt = fmt.Sprintf("select * from `%s` limit 500", table)
		port := strconv.Itoa(info.Port)
		strings := info.Ip + ":" + port
		conn, err := gohive.Connect(strings, gohive.DefaultOptions)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return result, err
		}

		conn.Exec("use " + info.Database)
		res, err := conn.SimpleQuery(sqltxt)
		if err != nil {
			logger.Error.Println(err)
		}

		//获取字段名
		var fields []string
		sqltxts := "desc `" + table + "`"
		rets, err := conn.SimpleQuery(sqltxts)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxts + "执行错误:" + err.Error())
		}

		conn.Close()

		for i := 0; i < len(rets); i++ {
			data := rets[i]
			col, _ := data["col_name"]
			column := col.(string)
			fields = append(fields, column)
		}

		result = RomoveTbname(res, fields, table)

	case HiveHuawei, HiveCDH:
		conn, err := PrestoConnect(info.Database)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return result, err
		}
		rows, err := conn.Query(sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return result, err
		}

		result, err = sqlrows2Maps(rows)
		if err != nil {
			logger.Error.Println("解析出错：", err)
			return result, err
		}
	case HiveTDC:
		res, err := tools.TDCRes(info.Ip, info.Port, sqltxt, info.TdhKbsinfo.Token)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return result, err
		}
		rec, err := ResTransform(res)
		if err != nil {
			//logger.Error.Println(err)
			return result, err
		}
		result = ColumnToLine(rec)
	}

	return result, nil
}

func HiveBuild(info Table, databaseinfo DatabaseInfo, sqltxt string) (string, error) {
	//var result []map[string]interface{}
	switch databaseinfo.HiveVersion {
	case HiveTdh:
		_, err := tools.TDHiveRes(sqltxt, databaseinfo.Ip, databaseinfo.User, databaseinfo.Passwd, databaseinfo.Port)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return "", err
		}

		// _, err := TDHiveRes(databaseinfo.Ip, databaseinfo.Port, databaseinfo.User, databaseinfo.Passwd, sqltxt)
		// if err != nil {
		// 	logger.Error.Println(err)
		// 	//return "", err
		// }
	case Hive12, Hive30, Hive31, "":
		err := HiveExec(databaseinfo.Ip, databaseinfo.Port, sqltxt)
		if err != nil {
			//logger.Error.Println(err)
			return "", err
		}
	case HiveHuawei:
		err := HDConnect(databaseinfo, sqltxt)
		// rows, err := conn.Query(sqltxt)
		if err != nil {
			//logger.Error.Println(err)
			return "", err
		}
	case HiveCDH:
		_, err := tools.CDHExecSQL(info.Engineid, sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return "", err
		}
	case HiveTDC:
		//fmt.Printf("sqltxt:%#v\n", sqltxt)
		_, err := tools.TDCRes(databaseinfo.Ip, databaseinfo.Port, sqltxt, databaseinfo.TdhKbsinfo.Token)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return "", err
		}
	}

	// fmt.Printf("%#v\n", info)
	//创建标签
	if len(info.Tag) != 0 {
		info.TagID, _ = CreateTags(info.Tag)
	}

	//将记录写入eagles
	//byteBody, err := json.Marshal(info)
	//uri := fmt.Sprintf("/%s/%s", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.AddWithoutID(Database, NewTbAsset, info)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	info.Id, err = GetDocId(response)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}

	var body = gin.H{
		"id": info.Id,
	}
	//byteBody, _ = json.Marshal(body)
	//todo

	//id, err := mgm.GetDocId(res)

	//uri = fmt.Sprintf("/%s/%s/%s/_update", Database, NewTbAsset, info.Id)
	err = serveresclient.NewESClientCl.UpdByID(Database, NewTbAsset, info.Id, body)
	//response, err = httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	return info.Id, nil
}

func HiveDelete(databaseinfo DatabaseInfo, id string, tablename string) error {
	sqltxt := fmt.Sprintf("drop table if exists %s.%s", databaseinfo.Database, tablename)

	switch databaseinfo.HiveVersion {
	case HiveTdh:
		_, err := TDHiveRes(databaseinfo.Ip, databaseinfo.Port, databaseinfo.User, databaseinfo.Passwd, sqltxt, databaseinfo.TdhKbsinfo)
		if err != nil {
			logger.Error.Println(err)
			//return "", err
		}
	case Hive12, Hive30, Hive31, "":
		err := HiveExec(databaseinfo.Ip, databaseinfo.Port, sqltxt)
		if err != nil {
			//logger.Error.Println(err)
			return errors.New("模型" + tablename + "删除失败")
		}
	case HiveHuawei:
		err := HDConnect(databaseinfo, sqltxt)
		if err != nil {
			//logger.Error.Println(err)
			return errors.New("模型" + tablename + "删除失败")
		}
	case HiveCDH:
		_, err := tools.CDHExecSQL(databaseinfo.Instance, sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return errors.New("模型" + tablename + "删除失败")
		}
	case HiveTDC:
		_, err := tools.TDCRes(databaseinfo.Ip, databaseinfo.Port, sqltxt, databaseinfo.TdhKbsinfo.Token)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return errors.New("模型" + tablename + "删除失败")
		}

	}

	uri := fmt.Sprintf("/%s/%s/%s", Database, NewTbAsset, id)
	_, err := httpclient.Delete(uri, "")
	if err != nil {
		logger.Error.Println(err)
		return errors.New("模型" + tablename + "删除失败")
	}

	return nil
}

func AlterHive(databaseinfo DatabaseInfo, sqltxt string) error {
	switch databaseinfo.HiveVersion {
	case HiveTdh:
		_, err := TDHiveRes(databaseinfo.Ip, databaseinfo.Port, databaseinfo.User, databaseinfo.Passwd, sqltxt, databaseinfo.TdhKbsinfo)
		//fmt.Println(res)
		if err != nil {
			//logger.Error.Println(err)
			return err
		}
	case Hive12, Hive30, Hive31, "":
		var cmd string
		cmd = fmt.Sprintf(`python %s %s %s "%s"`, common.HIVE_BASH, databaseinfo.Instance, databaseinfo.Database, sqltxt)
		//fmt.Println(cmd)
		//command := exec.Command("/bin/bash", "-c", cmd)

		resp, err := exec.Command("/bin/sh", "-c", cmd).Output()
		res := (string(resp[:]))
		if err != nil {
			logger.Error.Println(err)
			return err
		}

		if strings.Contains(res, "Execution Error") {
			logger.Error.Println("修改表结构" + res)
			return errors.New("模型修改失败")
		}
	case HiveHuawei, HiveCDH:
		conn, err := PrestoConnect(databaseinfo.Database)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return err
		}
		_, err = conn.Query(sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return errors.New("模型修改失败")
		}
	}
	return nil
}

func AlterHiveName(databaseinfo DatabaseInfo, sqltxt string) error {
	switch databaseinfo.HiveVersion {
	case HiveTdh:
		_, err := TDHiveRes(databaseinfo.Ip, databaseinfo.Port, databaseinfo.User, databaseinfo.Passwd, sqltxt, databaseinfo.TdhKbsinfo)
		//fmt.Println(res)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	case Hive12, Hive30, Hive31, "":
		var cmd string
		cmd = fmt.Sprintf(`python %s %s %s "%s"`, common.HIVE_BASH, databaseinfo.Instance, databaseinfo.Database, sqltxt)

		resp, err := exec.Command("/bin/sh", "-c", cmd).Output()
		res := (string(resp[:]))
		if err != nil {
			logger.Error.Println("修改表结构" + res)
			return err
		}

		if strings.Contains(res, "FAILED") {
			logger.Error.Println("修改表结构" + res)
			return errors.New("修改失败")
		}
	case HiveHuawei:
		conn, err := PrestoConnect(databaseinfo.Database)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return err
		}
		_, err = conn.Query(sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return errors.New("模型修改失败")
		}
	case HiveCDH:
		_, err := tools.CDHExecSQL(databaseinfo.Instance, sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return errors.New("模型修改失败")
		}
	}
	return nil
}

func ListTable(info DbInfo) ([]Tbinfos, error) {
	var tableinfo []Tbinfos
	var rowMaps []map[string]interface{}

	db, err := Connect(info)
	defer db.Close()
	if err != nil {
		logger.Error.Println("连接出错：", err)
		return tableinfo, err
	}

	sqltxt := `
	SELECT  
		table_schema AS schemaname ,
		table_name AS tablename, 
		pg_size_pretty(pg_total_relation_size('"' || table_schema || '"."' || table_name || '"')) AS size 
	FROM 
		information_schema.tables 
	where 
		table_schema != 'pg_catalog' 
	AND 
		table_schema != 'information_schema' 
	and 
		table_schema != 'gp_toolkit' 
	and 
		table_schema != 'madlib' 
	and 
		table_type = 'BASE TABLE' 
	and 
		TABLE_NAME not like 'xxn_%'`

	// fmt.Println("sqltxt:", sqltxt)
	rows, err := db.Query(sqltxt)
	if err != nil {
		logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
		return tableinfo, err
	}

	if rows != nil {
		defer rows.Close()
		rowMaps, err = sqlrows2Maps(rows)
		if err != nil {
			logger.Error.Println("解析错误:", err)
			return tableinfo, err
		}
		tableresp2, _ := json.Marshal(rowMaps)
		err = json.Unmarshal(tableresp2, &tableinfo)
		if err != nil {
			fmt.Println("json.Unmarshal  err .....列举表结果格式化失败", err.Error())
			logger.Error.Println(err)
			return tableinfo, err
		}
		//		fmt.Println(tableinfo)
	}
	// fmt.Printf("tableinfo:%#v\n", tableinfo)
	for i := 0; i < len(tableinfo); i++ {
		tableinfo[i].LayerID = info.Layerid
	}
	return tableinfo, nil
}

func Connect(info DbInfo) (*sql.DB, error) {

	var db *sql.DB
	pgLink := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		info.User,
		info.Passwd,
		info.Ip,
		info.Port,
		info.Databasename)

	db, err := sql.Open("postgres", pgLink)
	if err != nil {
		db.Close()
		return db, err
	}

	return db, nil
}

func ListHive(info DbInfo) ([]string, error) {
	var result []string
	sqltxt := "show tables in " + info.Databasename
	databaseinfo, _ := GetInstance(info.Engineid)
	switch databaseinfo.HiveVersion {
	case HiveTdh:
		res, err := TDHiveRes(databaseinfo.Ip, databaseinfo.Port, databaseinfo.User, databaseinfo.Passwd, sqltxt, databaseinfo.TdhKbsinfo)
		if err != nil {
			//logger.Error.Println(err)
			return result, err
		}
		rec2 := make(map[string]map[string]string, 0)

		js, err := simplejson.NewJson([]byte(res))
		if err != nil {
			logger.Error.Println(err)
			return result, err
		}

		rec1, err := js.Get("0").String()
		if err != nil {
			logger.Error.Println(err)
			return result, err
		}

		err = json.Unmarshal([]byte(rec1), &rec2)
		if err != nil {
			logger.Error.Println(err)
			return result, err
		}

		for _, v := range rec2 {
			for _, r := range v {
				if strings.HasPrefix(r, "xxn_") {
					continue
				}
				result = append(result, r)
			}
		}
	case Hive12, Hive30, Hive31, "":
		var tableinfo []HiveTable
		rets, err := HiveQuery(databaseinfo.Ip, databaseinfo.Port, sqltxt)
		if err != nil {
			//logger.Error.Println(err)
			return result, err
		}
		tableresp, _ := json.Marshal(rets)
		err = json.Unmarshal(tableresp, &tableinfo)
		if err != nil {
			fmt.Println("json.Unmarshal  err .....列举表结果格式化失败", err.Error())
			logger.Error.Println(err)
			return result, err
		}
		for _, v := range tableinfo {
			if strings.HasPrefix(v.Tablename, "xxn_") {
				continue
			}
			result = append(result, v.Tablename)
		}
	case HiveHuawei, HiveCDH:
		conn, err := PrestoConnect(info.Databasename)
		if err != nil {
			logger.Error.Println("连接出错：", err)
			return result, err
		}
		rows, err := conn.Query(sqltxt)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return result, err
		}
		rets, err := sqlrows2Maps(rows)
		if err != nil {
			logger.Error.Println("解析错误:", err)
			return result, err
		}
		for i := 0; i < len(rets); i++ {
			data := rets[i]
			tablename, _ := data["Table"]
			if strings.HasPrefix(tablename.(string), "xxn_") {
				continue
			}
			result = append(result, tablename.(string))
		}
	case HiveTDC:
		res, err := tools.TDCRes(databaseinfo.Ip, databaseinfo.Port, sqltxt, databaseinfo.TdhKbsinfo.Token)
		if err != nil {
			logger.Error.Println("执行语句:" + sqltxt + "执行错误:" + err.Error())
			return result, err
		}
		rec2 := make(map[string]map[string]string, 0)

		js, err := simplejson.NewJson([]byte(res))
		if err != nil {
			logger.Error.Println(err)
			return result, err
		}

		rec1, err := js.Get("0").String()
		if err != nil {
			logger.Error.Println(err)
			return result, err
		}

		err = json.Unmarshal([]byte(rec1), &rec2)
		if err != nil {
			logger.Error.Println(err)
			return result, err
		}

		for _, v := range rec2 {
			for _, r := range v {
				if strings.HasPrefix(r, "xxn_") {
					continue
				}
				result = append(result, r)
			}
		}
	}

	return result, nil
}

func ListEstable(info DbInfo) ([]Table, error) {
	var tableinfo []Table
	var must []map[string]interface{}
	query_engineid := gin.H{
		"term": gin.H{
			"engineid": gin.H{
				"value": info.Engineid,
			},
		},
	}
	must = append(must, query_engineid)

	query_databasename := gin.H{
		"term": gin.H{
			"databasename": gin.H{
				"value": info.Databasename,
			},
		},
	}
	must = append(must, query_databasename)

	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
		"size": 100000,
	}
	//byteBody, _ := json.Marshal(body)

	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(Database, NewTbAsset, body)
	//response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return tableinfo, err
	}
	var result TableHitsRes1

	err = json.Unmarshal(response, &result)

	if err != nil {
		logger.Error.Println(err)
		return tableinfo, err
	}

	for i := 0; i < len(result.Hits1.Hits2); i++ {
		tableinfo = append(tableinfo, result.Hits1.Hits2[i].Table)
	}

	return tableinfo, err
}

func ListDevinfo(engineid string, tbname string, userid string, username string) Devinfo {
	var source Devinfo
	var terms []interface{}
	if strings.Contains(tbname, "dwd_") {
		term1 := gin.H{
			"term": gin.H{
				"finaltable.engineid": engineid,
			},
		}
		terms = append(terms, term1)
		term2 := gin.H{
			"term": gin.H{
				"finaltable.tbname": tbname,
			},
		}
		terms = append(terms, term2)

		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": terms,
				},
			},
			"size": 1000,
		}
		//byteBody, _ := json.Marshal(body)

		//uri := fmt.Sprintf("/danastudio_ants_lab/tb_dev/_search")
		resp, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAnts, common.TbDev, body)
		//resp, err := httpclient.Post(uri, string(byteBody))
		if err != nil {
			logger.Error.Println(err)
		}

		e := gjson.Get(string(resp), "hits.hits").Array()
		if len(e) != 0 {
			source.Subject = gjson.Get(string(resp), "hits.hits.0._source.finaltable.subject").String()
			source.Describe = gjson.Get(string(resp), "hits.hits.0._source.finaltable.describe").String()
			source.Userid = gjson.Get(string(resp), "hits.hits.0._source.user").String()
			source.Username = gjson.Get(string(resp), "hits.hits.0._source.username").String()
			tagid := gjson.Get(string(resp), "hits.hits.0._source.finaltable.tagid").Array()
			for _, v := range tagid {
				source.TagID = append(source.TagID, v.String())
			}
		} else {
			source.Subject = "未知"
			source.Describe = ""
			source.Userid = userid
			source.Username = username
			source.TagID = []string{}
		}

	} else if strings.Contains(tbname, "base_") {
		term1 := gin.H{
			"term": gin.H{
				"tbinfo.engineid": engineid,
			},
		}
		terms = append(terms, term1)
		term2 := gin.H{
			"term": gin.H{
				"middletable.tablename": tbname,
			},
		}
		terms = append(terms, term2)

		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": terms,
				},
			},
			"size": 1000,
		}
		//byteBody, _ := json.Marshal(body)

		//uri := fmt.Sprintf("/danastudio_ants_lab/tb_dev/_search")
		resp, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAnts, common.TbDev, body)
		//resp, err := httpclient.Post(uri, string(byteBody))
		if err != nil {
			logger.Error.Println(err)
		}

		e := gjson.Get(string(resp), "hits.hits").Array()
		if len(e) != 0 {
			source.Subject = "无主题"
			source.Describe = gjson.Get(string(resp), "hits.hits.0._source.middletable.describe").String()
			source.Userid = gjson.Get(string(resp), "hits.hits.0._source.user").String()
			source.Username = gjson.Get(string(resp), "hits.hits.0._source.username").String()
			tagid := gjson.Get(string(resp), "hits.hits.0._source.middletable.tagid").Array()
			for _, v := range tagid {
				source.TagID = append(source.TagID, v.String())
			}
		} else {
			source.Subject = "无主题"
			source.Describe = ""
			source.Userid = userid
			source.Username = username
			source.TagID = []string{}
		}

	} else {
		source.Subject = "无主题"
		source.Describe = ""
		source.Userid = userid
		source.Username = username
		source.TagID = []string{}
	}

	return source
}

func CheckTable(info CheckInfo) (string, error) {
	engineinfo, err := GetInstance(info.Engineid)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}

	engineinfo.Database = info.Dbname

	if info.Newtbname != info.Oldtbname {
		if engineinfo.DataBaseType == "greenplum" || engineinfo.DataBaseType == "postgres" || engineinfo.DataBaseType == "stork" || engineinfo.DataBaseType == "teryx" || engineinfo.DataBaseType == common.GAUSSDB {
			// 1.获取全部表信息
			db, err := getDBcnn(engineinfo)
			if err != nil {
				return "", err
			}
			defer db.Close()
			sqltxt := `
				SELECT 
						table_name AS tablename,
						table_schema AS schemaname 
				FROM 
						information_schema.tables 
				where 
						table_schema != 'pg_catalog' 
				AND 
						table_schema != 'information_schema' 
				and 
						table_schema != 'gp_toolkit' 
				and 
						table_schema != 'madlib' 
				and 
						table_type = 'BASE TABLE' 
				and 
						TABLE_NAME not like 'xxn_%'`
			rows, err := db.Query(sqltxt)
			if err != nil {
				return "", err
			}
			defer rows.Close()
			tableinfo := []Tbinfos{}
			var name, schema string
			var subinfo Tbinfos
			for rows.Next() {
				err = rows.Scan(&name, &schema)
				if err != nil {
					return "", err
				}
				subinfo.Tablename = name
				subinfo.Schemaname = schema
				tableinfo = append(tableinfo, subinfo)
			}
			// 2.对比判定是否重名
			if info.Schema == "" {
				info.Schema = "public"
			}
			for _, v := range tableinfo {
				if v.Tablename == info.Newtbname && v.Schemaname == info.Schema {
					return "存在同名表，请修改表名", nil
				}
			}
		} else if engineinfo.DataBaseType == "hive" {
			sqltxt := "show tables in " + info.Dbname
			switch engineinfo.HiveVersion {
			case HiveTdh:
				res, err := TDHiveRes(engineinfo.Ip, engineinfo.Port, engineinfo.User, engineinfo.Passwd, sqltxt, engineinfo.TdhKbsinfo)
				if err != nil {
					logger.Error.Println(err)
				}
				rec2 := make(map[string]map[string]string, 0)

				js, err := simplejson.NewJson([]byte(res))
				if err != nil {
					logger.Error.Println(err)
				}

				rec1, err := js.Get("0").String()
				if err != nil {
					logger.Error.Println(err)
				}

				err = json.Unmarshal([]byte(rec1), &rec2)
				if err != nil {
					logger.Error.Println(err)
				}

				for _, v := range rec2 {
					for _, r := range v {
						if r == info.Newtbname {
							return "存在同名表，请修改表名", nil
						}
					}
				}
			case Hive12, Hive30, Hive31, "":
				var tableinfo []HiveTable
				rets, err := HiveQuery(engineinfo.Ip, engineinfo.Port, sqltxt)
				if err != nil {
					logger.Error.Println(err)
				}
				tableresp, _ := json.Marshal(rets)
				err = json.Unmarshal(tableresp, &tableinfo)
				if err != nil {
					logger.Error.Println(err)
				}
				for _, v := range tableinfo {
					if v.Tablename == info.Newtbname {
						return "存在同名表，请修改表名", nil
					}
				}
			case HiveHuawei, HiveCDH:
				conn, err := PrestoConnect(info.Dbname)
				rows, err := conn.Query(sqltxt)
				if err != nil {
					logger.Error.Println(err)
				}
				rets, err := sqlrows2Maps(rows)
				if err != nil {
					logger.Error.Println(err)
				}
				for i := 0; i < len(rets); i++ {
					data := rets[i]
					tablename, _ := data["Table"]
					if tablename == info.Newtbname {
						return "存在同名表，请修改表名", nil
					}
				}
			case HiveTDC:
				res, err := tools.TDCRes(engineinfo.Ip, engineinfo.Port, sqltxt, engineinfo.TdhKbsinfo.Token)
				if err != nil {
					logger.Error.Println(err)
				}
				rec2 := make(map[string]map[string]string, 0)

				js, err := simplejson.NewJson([]byte(res))
				if err != nil {
					logger.Error.Println(err)
				}

				rec1, err := js.Get("0").String()
				if err != nil {
					logger.Error.Println(err)
				}

				err = json.Unmarshal([]byte(rec1), &rec2)
				if err != nil {
					logger.Error.Println(err)
				}

				for _, v := range rec2 {
					for _, r := range v {
						if r == info.Newtbname {
							return "存在同名表，请修改表名", nil
						}
					}
				}
			}
		}
	}

	return "", nil
}

func Iswttable(table string) bool {
	databaseinfo, _ := StorkInfo()

	sqltxt := "select * from public.all_wt_tables_info where tablename = '" + table + "'"

	db, err := getDBcnn(databaseinfo)
	if err != nil {
		logger.Error.Println(err)
	}
	if db != nil {
		defer db.Close()
	}
	rows, err := db.Query(sqltxt)

	if rows != nil {
		defer rows.Close()
	}

	if err != nil {
		logger.Error.Println(err)
	}

	rowMaps, err := sqlrows2Maps(rows)
	if len(rowMaps) != 0 {
		return true
	}

	return false

}

func StorkInfo() (DatabaseInfo, error) {
	var body interface{}
	var terms []interface{}
	term1 := gin.H{
		"match": gin.H{
			"enginename": "stork",
		},
	}
	term2 := gin.H{
		"match": gin.H{
			"regtype": "DANA",
		},
	}
	terms = append(terms, term1, term2)
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
	}

	//byteBody, _ := json.Marshal(body)
	//uri := fmt.Sprintf("/%s/%s/_search", "danastudio_platform", "tb_engine")
	response, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBasePlatform, "tb_engine", body)
	//response, err := httpclient.Post(uri, string(byteBody))
	var info DatabaseInfo
	if err != nil {
		logger.Error.Println(err)
		return info, err
	}

	e := gjson.Get(string(response), "hits.hits").Array()
	for _, v := range e {
		info.Ip = v.Get("_source.ip").String()
		info.Port = int(v.Get("_source.port").Int())
		info.User = v.Get("_source.username").String()
		info.Passwd = v.Get("_source.password").String()
		info.Database = v.Get("_source.initdb").String()
	}
	return info, err
}

func CatalogSave(sp AssetCatalog) error {
	nMap := make(map[string]string) //fakeid:realid
	for i, custom := range sp.CustomInfo {
		for j, pro := range custom.CustomProperties {
			if pro.ProType == "select" || pro.ProType == "checkbox" {
				for k, rated := range pro.MulitiEnumRated {
					if rated.FakeID != "" {
						newID := pro.ProSign + "_" + tools.RandomString(common.IDLength)
						nMap[rated.FakeID] = newID
						sp.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].ID = newID
						sp.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].FakeID = ""
					}
					if rated.FatherID == "" { //原枚举值存放最顶层，兼容首页管理配置
						sp.CustomInfo[i].CustomProperties[j].EnumeratedValues = append(sp.CustomInfo[i].CustomProperties[j].EnumeratedValues, rated.Value)
					}
				}

				for k, rated := range pro.MulitiEnumRated {
					if strings.Contains(rated.FatherID, "-") {
						sp.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].FatherID = nMap[rated.FatherID]
					}
				}
			}
		}
	}

	err := serveresclient.NewESClientCl.AddWithAimID(common.DataBaseModelAsset, common.TbAssetCatalog, sp.CatalogID, sp)
	if err != nil {
		logger.Error.Printf("添加资产目录模板失败:%s", err.Error())
		return err
	}
	return nil
}

func CatalogDetail(id string, isUsedCheck ...bool) (AssetCatalog, error) {
	var res AssetCatalog
	var resHit AssetCatalogHits
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"catalogid": id,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCatalog, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &resHit)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	if len(resHit.Hits1.Hits2) > 0 {
		res = resHit.Hits1.Hits2[0].AssetCatalog
	}

	/*
		PortalFilter=false & (MenuCheck || TagCheck)=true -> 新数据，以 check 为准，不处理
		PortalFilter=false & (MenuCheck || TagCheck)=false -> 老数据且为 false，不处理
		PortalFilter=true & (MenuCheck || TagCheck)=true -> 新数据，以 check 为准，不处理
		PortalFilter=true & (MenuCheck || TagCheck)=false -> 老数据，按规则兼容
		再次编辑时只要有勾选，PortalFilter 一定传 true
	*/

	//枚举值 isused 查询
	enumUsed := make(map[string]bool) //id:true
	needCheck := false

	for _, i := range isUsedCheck {
		if i {
			needCheck = true
			break
		}
	}

	if needCheck {
		isusedQuery := gin.H{
			"_source": "assetcatalog.custominfo.customproperties.mulitiselected",
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"assetcatalog.catalogid": id,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}

		resByte, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, isusedQuery)
		if err != nil {
			logger.Error.Printf("查询目录相关资产失败:%s", err.Error())
			return res, err
		}

		var enums []SimpleCatalog
		resStr := commontools.GetSource(resByte)
		err = json.Unmarshal([]byte(resStr), &enums)
		if err != nil {
			logger.Error.Printf("查询目录相关资产失败:%s", err.Error())
			return res, err
		}

		for _, i := range enums {
			for _, j := range i.AssetCatalog.Custominfo {
				for _, l := range j.Customproperties {
					for _, m := range l.Mulitiselected {
						enumUsed[m] = true
					}
				}
			}
		}
	}

	//修正门户筛选
	for i, cust := range res.CustomInfo {
		for j, pro := range cust.CustomProperties {
			if needCheck {
				for k, e := range pro.MulitiEnumRated {
					if enumUsed[e.ID] {
						res.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].IsUsed = true
					} else {
						res.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].IsUsed = false //防止前端把 true 存入
					}
				}
			}
			if !(pro.MenuCheck || pro.TagCheck) && pro.PortalFilter {
				if pro.ProSign == "ZCLM" {
					res.CustomInfo[i].CustomProperties[j].MenuCheck = true
				} else {
					res.CustomInfo[i].CustomProperties[j].TagCheck = true
				}
			}
		}
	}

	return res, nil
}

func CheckCount(body interface{}) (int, error) {
	num, err := serveresclient.NewESClientCl.SearchCount(common.DataBaseModelAsset, common.TbAssetCatalog, body)
	if err != nil {
		logger.Error.Println("查询是否存在上线模板失败:", err)
		return num, err
	}
	return num, nil
}

func CatalogUpdByID(id string, doc interface{}) error {
	err := serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetCatalog, common.RefreshWait, id, doc)
	if err != nil {
		logger.Error.Printf("数据模板信息更新至es失败:%s", err.Error())
		return err
	}
	return nil
}

func AssetInventoryAdaptation(sp AssetCatalog) error {
	//查询所有562之前的资产目录ID
	var res []AssetInventory
	var terms []gin.H
	term1 := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"assetcatalog.catalogid": sp.CatalogID,
		},
	}
	terms = append(terms, term1, term2)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return err
	}
	var response map[string]interface{}
	err = json.Unmarshal([]byte(strInfo), &response)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return err
	}
	if response["hits"] != nil {
		hits := response["hits"].(map[string]interface{})["hits"].([]interface{})
		for _, hit := range hits {
			sourceRes := hit.(map[string]interface{})["_source"]
			var r AssetInventory
			b, err := json.Marshal(sourceRes)
			if err != nil {
				return err
			}
			err = json.Unmarshal(b, &r)
			if err != nil {
				return err
			}
			res = append(res, r)
		}

	}

	//处理数据
	newAssetInventory := make(map[string]AssetInventory)
	for _, oldInventory := range res {
		newInventory := new(AssetCatalog)
		tools.DeepCopy(newInventory, sp)
		for k1, new1 := range newInventory.CustomInfo {
			for _, old1 := range oldInventory.AssetCatalog.CustomInfo {
				if new1.InfoSign == old1.InfoSign {
					for k2, new2 := range new1.CustomProperties {
						for _, old2 := range old1.CustomProperties {
							if new2.ProSign == old2.ProSign {
								newInventory.CustomInfo[k1].CustomProperties[k2].ProValue = old2.ProValue
								newInventory.CustomInfo[k1].CustomProperties[k2].ProValues = old2.ProValues
								newInventory.CustomInfo[k1].CustomProperties[k2].ProMulitiValue = old2.ProMulitiValue
								newInventory.CustomInfo[k1].CustomProperties[k2].MulitiSelected = old2.MulitiSelected
							}
						}
					}
				}
			}
		}

		oldInventory.AssetCatalog.CustomInfo = newInventory.CustomInfo
		newAssetInventory[oldInventory.InventoryID] = oldInventory
	}

	//批量更新
	var bulkbody []gin.H
	for _, v := range newAssetInventory {
		bulkbody = append(bulkbody, gin.H{
			"id":           v.InventoryID,
			"assetcatalog": v.AssetCatalog,
		})
	}

	err = httpclient.EUpdateBulk(common.DataBaseModelAsset, common.TbAssetInventory, "id", bulkbody)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	logger.Info.Println("更新完成")

	return nil
}
func AssetInventoryHeader(sp AssetCatalog) error {
	//查询资产目录版本
	var res []AssetCategoryVerion
	var terms []gin.H
	term1 := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	terms = append(terms, term1)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCatalogVersion, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return err
	}
	var response map[string]interface{}
	err = json.Unmarshal([]byte(strInfo), &response)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return err
	}
	if response["hits"] != nil {
		hits := response["hits"].(map[string]interface{})["hits"].([]interface{})
		for _, hit := range hits {
			sourceRes := hit.(map[string]interface{})["_source"]
			var r AssetCategoryVerion
			b, err := json.Marshal(sourceRes)
			if err != nil {
				return err
			}
			err = json.Unmarshal(b, &r)
			if err != nil {
				return err
			}
			res = append(res, r)
		}

	}
	headerMap := make(map[string]string)
	for _, v := range sp.CustomInfo {
		for _, v1 := range v.CustomProperties {
			headerMap[v1.ProSign] = v1.ProName
		}
	}

	//处理数据
	for k, oldAssetCategoryVerion := range res {
		for k1, v1 := range oldAssetCategoryVerion.HeaderName {
			if _, ok := headerMap[v1.OriHeaderSign]; ok {
				res[k].HeaderName[k1].OriHeaderName = headerMap[v1.OriHeaderSign]
			}
		}
	}

	//批量更新
	var bulkbody []gin.H
	for _, v := range res {
		bulkbody = append(bulkbody, gin.H{
			"assetcategoryid": v.AssetCategoryID,
			"headername":      v.HeaderName,
		})
	}

	err = httpclient.EUpdateBulk(common.DataBaseModelAsset, common.TbAssetCatalogVersion, "assetcategoryid", bulkbody)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	logger.Info.Println("更新完成")

	return nil
}
func IsGetEnable(projectid string) (err error) {
	var terms []gin.H
	term1 := gin.H{
		"term": gin.H{
			"status": 1,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"projectid": projectid,
		},
	}
	terms = append(terms, term1, term2)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": common.UnlimitSize,
	}
	num, _ := CheckCount(body)
	if num > 1 {
		return fmt.Errorf("存在多个上线模组")
	}
	return nil
}

func CheckUsed(id string) (int, error) {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"assetcatalog.catalogid": id,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}
	num, err := serveresclient.NewESClientCl.SearchCount(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Println("查询是否存在上线模板失败:", err)
		return num, err
	}
	return num, nil
}

func CatalogDelete(id string) error {
	err := serveresclient.NewESClientCl.DelByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetCatalog, "true", id)
	if err != nil {
		logger.Error.Printf("数据模板信息更新至es失败:%s", err.Error())
		return err
	}
	return nil
}

func CatalogList(sp AssetMeta) (res []AssetCatalog, err error) {
	var resHit AssetCatalogHits
	var sort []map[string]interface{}
	var run gin.H
	if sp.Sort.SortField == "updatedtime" || sp.Sort.SortField == "" {
		if sp.Sort.SortType == "asc" {
			run = gin.H{
				"updatedtime": gin.H{
					"order": "asc",
				},
			}
		} else {
			run = gin.H{
				"updatedtime": gin.H{
					"order": "desc",
				},
			}
		}
	} else if sp.Sort.SortField == "createdtime" {
		if sp.Sort.SortType == "asc" {
			run = gin.H{
				"createdtime": gin.H{
					"order": "asc",
				},
			}
		} else {
			run = gin.H{
				"createdtime": gin.H{
					"order": "desc",
				},
			}
		}
	}
	sort = append(sort, run)
	body := gin.H{
		"sort": sort,
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": sp.ProjectID,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCatalog, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &resHit)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	var oldres []AssetCatalog
	for _, v := range resHit.Hits1.Hits2 {
		oldres = append(oldres, v.AssetCatalog)
	}

	//获取使用状态
	for k, v := range oldres {
		num, _ := CheckUsed(v.CatalogID)
		if num >= 1 {
			oldres[k].IsUsed = true
		} else {
			oldres[k].IsUsed = false
		}
	}
	//排序
	var o1, o2, o3 []AssetCatalog
	for _, v := range oldres {
		_, userMap, err := user.GetUserInfo([]string{v.UserID})
		if err != nil {
			logger.Error.Println(err)
			return res, err
		}
		if _, ok := userMap[v.UserID]; ok {
			v.NickName = userMap[v.UserID].NickName
			v.UserDepartment = userMap[v.UserID].UserDepartment
		}
		if v.Status == 1 {
			o1 = append(o1, v)
		} else {
			if v.IsUsed == true {
				o2 = append(o2, v)
			} else {
				o3 = append(o3, v)
			}
		}
	}
	res = append(res, o1...)
	res = append(res, o2...)
	res = append(res, o3...)

	return res, nil
}

func CatalogSearchused(id string, listtype string) (res AssetCatalog, err error) {
	var resHit AssetCatalogHits
	var body interface{}
	var terms []interface{}
	term1 := gin.H{
		"term": gin.H{
			"status": 1,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"projectid": id,
		},
	}
	terms = append(terms, term1, term2)
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCatalog, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &resHit)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	if len(resHit.Hits1.Hits2) > 1 {
		logger.Error.Printf("启用模组查询错误，存在多个启用模组")
		return res, fmt.Errorf("启用模组查询错误，存在多个启用模组")
	} else if len(resHit.Hits1.Hits2) == 1 {
		res = resHit.Hits1.Hits2[0].AssetCatalog

		for k, v := range res.CustomInfo {
			var properties []Properties
			for k1, v1 := range v.CustomProperties {
				if v1.ProType == "select" && len(v1.EnumeratedValues) <= 0 {
					res.CustomInfo[k].CustomProperties[k1].EnumeratedValues = []string{}
				}
				if v1.ProSign == "ZCLM" {
					//如果是资产类目，需要将“页面上资产类目管理”的所有信息拼接进来
					categoryList, _ := CategoryInfoList(id)
					var mulivalues []MulitiEnum
					for _, temp := range categoryList {
						var one MulitiEnum
						one.ID = temp.CategoryID
						if temp.FatherID != id {
							one.FatherID = temp.FatherID
						}
						one.Value = temp.CategoryName
						mulivalues = append(mulivalues, one)
					}
					res.CustomInfo[k].CustomProperties[k1].MulitiEnumRated = mulivalues
					v1.MulitiEnumRated = mulivalues
				}
				if v1.ProType == "select" || v1.ProType == "checkbox" {
					v1.EnumeratedValues = []string{}
					if v1.ProSign == common.AreaSign {
						for _, i := range v1.MulitiEnumRated {
							if i.FatherID == "" {
								v1.EnumeratedValues = append(v1.EnumeratedValues, i.Value)
							}
						}
					} else {
						keys := make(map[string]bool)
						for _, i := range v1.MulitiSelected {
							keys[i] = true
						}
						for _, i := range v1.MulitiEnumRated {
							if keys[i.ID] {
								v1.EnumeratedValues = append(v1.EnumeratedValues, i.Value)
							}
						}
					}
					if listtype == "1" {
						properties = append(properties, v1)
					}
				}
			}
			if listtype == "1" {
				res.CustomInfo[k].CustomProperties = properties
			}
		}
	} else {
		return res, fmt.Errorf("未启用")
	}
	return res, nil
}

func CatalogSearchused2(id string, listtype string) (res AssetCatalog, err error) {
	var resHit AssetCatalogHits
	var body interface{}
	var terms []interface{}
	//term1 := gin.H{
	//	"term": gin.H{
	//		"isuse": true,
	//	},
	//}
	term2 := gin.H{
		"term": gin.H{
			"projectid": id,
		},
	}
	terms = append(terms, term2)
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCatalog, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &resHit)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	//if len(resHit.Hits1.Hits2) > 1 {
	//	logger.Error.Printf("已使用模组查询错误，存在多个已使用模组")
	//	return res, fmt.Errorf("已使用模组查询错误，存在多个已使用模组")
	//} else if len(resHit.Hits1.Hits2) == 1 {
	//	res = resHit.Hits1.Hits2[0].AssetCatalog
	//	for k, v := range res.CustomInfo {
	//		for k1, v1 := range v.CustomProperties {
	//			if v1.ProType == "select" && len(v1.EnumeratedValues) <= 0 {
	//				res.CustomInfo[k].CustomProperties[k1].EnumeratedValues = []string{}
	//			}
	//		}
	//	}
	//} else {
	//	return res, fmt.Errorf("未使用")
	//}
	var ist bool
	for _, v := range resHit.Hits1.Hits2 {
		num, _ := CheckUsed(v.AssetCatalog.CatalogID)
		if num >= 1 {
			res = v.AssetCatalog
			for k, v1 := range res.CustomInfo {
				var properties []Properties
				for k1, v2 := range v1.CustomProperties {
					if v2.ProSign == "ZCLM" {
						//如果是资产类目，需要将“页面上资产类目管理”的所有信息拼接进来
						categoryList, _ := CategoryInfoList(id)
						var mulivalues []MulitiEnum
						for _, temp := range categoryList {
							var one MulitiEnum
							one.ID = temp.CategoryID
							if temp.FatherID != id {
								one.FatherID = temp.FatherID
							}
							one.Value = temp.CategoryName
							mulivalues = append(mulivalues, one)
						}
						res.CustomInfo[k].CustomProperties[k1].MulitiEnumRated = mulivalues
						v2.MulitiEnumRated = mulivalues
					}
					if v2.ProType == "select" && len(v2.EnumeratedValues) <= 0 {
						res.CustomInfo[k].CustomProperties[k1].EnumeratedValues = []string{}
					}
					if listtype == "1" {
						if v2.ProType == "select" || v2.ProType == "checkbox" {
							properties = append(properties, v2)
						}
					}
				}
				if listtype == "1" {
					res.CustomInfo[k].CustomProperties = properties
				}
			}
			ist = true
			break
		}
	}

	if !ist {
		return res, fmt.Errorf("未使用")
	}

	return res, nil
}

func CatalogUpdate(sp AssetCatalog) error {
	oldCatalogInfo, err := CatalogDetail(sp.CatalogID)
	if err != nil {
		logger.Error.Printf("查询原模板信息失败:%s", err.Error())
		return err
	}

	//处理枚举值变更
	deleted := make(map[string]bool) // 标记哪些元素被删除了
	//changed := []MulitiEnum{}             // 存储被修改了的元素
	changed := make(map[string]MulitiEnum) // 存储被修改了的元素
	newPro := make(map[string]MulitiEnum)  // id:MulitiEnum

	//changedName := make(map[string]MulitiEnum)
	//deletedName := make(map[string]bool)

	nMap := make(map[string]string) //fakeid:realid
	for i, custom := range sp.CustomInfo {
		for j, pro := range custom.CustomProperties {
			if pro.ProType == "select" || pro.ProType == "checkbox" {
				for k, rated := range pro.MulitiEnumRated {
					if rated.FakeID != "" {
						newID := pro.ProSign + "_" + tools.RandomString(common.IDLength)
						nMap[rated.FakeID] = newID
						sp.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].ID = newID
						sp.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].FakeID = ""
						rated.ID = newID
					}
				}
				for k, rated := range pro.MulitiEnumRated {
					if strings.Contains(rated.FatherID, "-") {
						sp.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k].FatherID = nMap[rated.FatherID]
					}
					newPro[rated.ID] = sp.CustomInfo[i].CustomProperties[j].MulitiEnumRated[k]
				}
			}
		}
	}

	changeDetail := make(map[string]map[string]string) // proid:old:new
	deleteDetail := make(map[string]map[string]bool)   // proid:old:true

	for _, custom := range oldCatalogInfo.CustomInfo {
		for _, pro := range custom.CustomProperties {
			for _, rated := range pro.MulitiEnumRated {
				if n, ok := newPro[rated.ID]; ok {
					if n.Value != rated.Value {
						changed[n.ID] = n //存最新的
						if len(changeDetail[pro.ProID]) == 0 {
							changeDetail[pro.ProID] = make(map[string]string)
						}
						changeDetail[pro.ProID][rated.Value] = n.Value
					}
				} else {
					deleted[rated.ID] = true
					if len(deleteDetail[pro.ProID]) == 0 {
						deleteDetail[pro.ProID] = make(map[string]bool)
					}
					deleteDetail[pro.ProID][rated.Value] = true
				}
			}
		}
	}

	//c, _ := json.Marshal(changed)
	//d, _ := json.Marshal(deleted)
	//fmt.Println("changed: ", string(c))
	//fmt.Println("deleted: ", string(d))

	err = serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetCatalog, common.RefreshWait, sp.CatalogID, sp)
	if err != nil {
		logger.Error.Printf("数据模板信息更新至es失败:%s", err.Error())
		return err
	}

	//处理删除和修改的枚举值在资产中的映射
	go UpdateCatalog(changed, deleted, sp.ProjectID, sp.CatalogID)

	//更新首页配置
	go UpdateCarousel(sp, changeDetail, deleteDetail)
	return nil
}

func UpdateCarousel(sp AssetCatalog, changeDetail map[string]map[string]string, deleteDetail map[string]map[string]bool) {
	bytes, err := esutil.SearchByID(common.DataBaseModelAsset, common.TbCarousel, sp.ProjectID)
	if err != nil {
		logger.Error.Println(err)
		return
	}

	var item ProtalInfo
	err = json.Unmarshal([]byte(bytes), &item)
	if err != nil {
		logger.Error.Println(err)
		return
	}
	if item.HotArea.CatalogID != sp.CatalogID {
		return
	}
	proid := item.HotArea.ProID
	//enums := make(map[string]bool)
	var needUpdate bool
	for i := 0; i < len(item.HotArea.Cards); i++ {
		if len(deleteDetail[proid]) > 0 {
			if deleteDetail[proid][item.HotArea.Cards[i].Title] {
				needUpdate = true
				item.HotArea.Cards = append(item.HotArea.Cards[:i], item.HotArea.Cards[i+1:]...)
				item.HotArea.ShowNum--
				i--
				continue
			}
		}

		if len(changeDetail[proid]) > 0 {
			if newTitle, ok := changeDetail[proid][item.HotArea.Cards[i].Title]; ok {
				needUpdate = true
				item.HotArea.Cards[i].Title = newTitle
			}
		}

	}

	if needUpdate {
		doc := gin.H{
			"hotarea": item.HotArea,
		}

		err = esutil.UpdByID(common.DataBaseModelAsset, common.TbCarousel, sp.ProjectID, doc)
		if err != nil {
			logger.Error.Println(err)
			return
		}
	}
}

func UpdateCatalog(changed map[string]MulitiEnum, deleted map[string]bool, projectID string, catalogID string) {
	if len(changed) == 0 && len(deleted) == 0 {
		return
	}
	var effectID []string
	for i := range changed {
		effectID = append(effectID, i)
	}
	for i := range deleted {
		effectID = append(effectID, i)
	}
	body := gin.H{
		"size":    common.UnlimitSize,
		"_source": []string{"assetcatalog", "inventoryid", "dataarealist", "dataproviderlist", "datasourcesystemlist"},
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"nested": gin.H{
						"path": "assetcatalog.custominfo.customproperties",
						"query": gin.H{
							"bool": gin.H{
								"must": gin.H{
									"terms": gin.H{
										"assetcatalog.custominfo.customproperties.promulitivalue.id": effectID,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Println(err)
		return
	}
	relList, _ := esutil.GetSourceAndTotal(bytes)

	//var result []SimpleAsset
	for _, value := range relList {
		var oneRecord AssetInventory
		err = json.Unmarshal([]byte(value), &oneRecord)
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		var newArea, newSystem, newProvider []string
		areaChange := make(map[string]string)
		for i, c := range oneRecord.AssetCatalog.CustomInfo {
			for j, _ := range c.CustomProperties {
				for k := 0; k < len(oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue); k++ {
					if deleted[oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue[k].ID] {
						oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue = append(oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue[:k], oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue[k+1:]...)
						k--
						continue
					}
					if newEnum, ok := changed[oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue[k].ID]; ok {
						oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue[k] = newEnum
						if oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProSign == common.AreaSign && oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue[k].FatherID == "" {
							areaChange[oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue[k].Value] = newEnum.Value
						}
					}

					switch oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProSign {
					case common.AreaSign:
						for _, s := range oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue {
							if s.FatherID == "" {
								newArea = append(newArea, s.Value)
							}
						}
					case common.SystemSign:
						for _, s := range oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue {
							newSystem = append(newSystem, s.Value)
						}
					case common.ProviderSign:
						for _, s := range oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue {

							newProvider = append(newProvider, s.Value)
						}
					}
				}

				for l := 0; l < len(oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].MulitiSelected); l++ {

					if deleted[oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].MulitiSelected[l]] {
						oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].MulitiSelected = append(oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].MulitiSelected[:l], oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].MulitiSelected[l+1:]...)
						switch oneRecord.AssetCatalog.CustomInfo[i].CustomProperties[j].ProSign {
						case common.AreaSign:
							oneRecord.DataArea = append(oneRecord.DataArea[:l], oneRecord.DataArea[l+1:]...) //业务领域取顶层
						case common.SystemSign:
							oneRecord.DataSourceSystem = append(oneRecord.DataSourceSystem[:l], oneRecord.DataSourceSystem[l+1:]...) //来源系统取底层
						case common.ProviderSign:
							oneRecord.DataProvider = append(oneRecord.DataProvider[:l], oneRecord.DataProvider[l+1:]...) //所属部门取底层
						}
						l--
					}

				}
			}
		}

		//for k, v := range oneRecord.DataArea {
		//	if newValue, ok := areaChange[v]; ok {
		//		oneRecord.DataArea[k] = newValue
		//	}
		//}

		oneRecord.DataArea = newArea
		oneRecord.DataSourceSystem = newSystem
		oneRecord.DataProvider = newProvider

		updateDoc := gin.H{
			"assetcatalog":         oneRecord.AssetCatalog,
			"dataarealist":         oneRecord.DataArea,
			"datasourcesystemlist": oneRecord.DataSourceSystem,
			"dataproviderlist":     oneRecord.DataProvider,
		}

		err = esutil.UpdByID(common.DataBaseModelAsset, common.TbAssetInventory, oneRecord.InventoryID, updateDoc)
		if err != nil {
			logger.Error.Println(err)
			continue
		}

	}

	//更新首页配置

	return
}

func CategoryCheckSameName(sp AssetCategory) (err error) {
	var body interface{}
	var terms []interface{}
	term1 := gin.H{
		"term": gin.H{
			"categorylv": sp.CategoryLv,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"categoryname": sp.CategoryName,
		},
	}
	term3 := gin.H{
		"term": gin.H{
			"fatherid": sp.FatherID,
		},
	}
	terms = append(terms, term1, term2, term3)
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
	}
	num, err := serveresclient.NewESClientCl.SearchCount(common.DataBaseModelAsset, common.TbAssetCategory, body)
	if err != nil {
		logger.Error.Println("查询类目是否重名失败:", err)
		return err
	}

	if num >= 1 {
		return fmt.Errorf("名称已存在，请重命名")
	}
	if sp.CategoryLv > 1 {
		//资产检测
		if sp.FatherID == "" {
			return fmt.Errorf("父类目id为空")
		}
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"inventorycategory": sp.FatherID,
						},
					},
				},
			},
		}
		num2, err := serveresclient.NewESClientCl.SearchCount(common.DataBaseModelAsset, common.TbAssetCategory, body)
		if err != nil {
			logger.Error.Println("查询类目是否依赖失败:", err)
			return err
		}
		if num2 >= 1 {
			return fmt.Errorf("该类目下已存在资产，不支持新建子类目。建议在「资产编目」中先将该类目下资产移至其他类目下，再新建子类目。")
		}

	}

	return nil
}

func CategorySave(sp AssetCategory) error {
	err := serveresclient.NewESClientCl.AddWithAimID(common.DataBaseModelAsset, common.TbAssetCategory, sp.CategoryID, sp)
	if err != nil {
		logger.Error.Printf("添加资产类目失败:%s", err.Error())
		return err
	}
	return nil
}

func UpdateCategoryIDMerge(sps *Assets, categoryList []AssetCategory) (addIDs, addNames, updateIDs []string, err error) {
	//这里的updateids只是为了修改后端数据，不计入日志

	var addID, addName string
	for k, sp := range sps.Cates {
		if sp.CategoryLv == 1 && sp.CategoryID == "" {
			sps.Cates[k].CategoryID = tools.NewUuid()
			sps.Cates[k].FatherID = sp.ProjectID
			sps.Cates[k].CategoryIDMerge.CategoryID0 = sp.ProjectID
			sps.Cates[k].CategoryIDMerge.CategoryID1 = sps.Cates[k].CategoryID
			sps.Cates[k].CreatedTime = time.Now().Format("2006-01-02 15:04:05")
			sps.Cates[k].UpdatedTime = sps.Cates[k].CreatedTime
			addIDs = append(addIDs, sps.Cates[k].CategoryID)
			addNames = append(addNames, sps.Cates[k].CategoryName)
		} else {
			for _, oldInfo := range categoryList {
				if sp.CategoryID == oldInfo.CategoryID {
					if oldInfo.CategoryIDMerge.CategoryID1 == "" {
						sps.Cates[k].CategoryIDMerge.CategoryID0 = sp.ProjectID
						sps.Cates[k].CategoryIDMerge.CategoryID1 = sps.Cates[k].CategoryID
						updateIDs = append(updateIDs, sp.CategoryID)
					}
				}
			}

		}
	}
	for k, sp := range sps.Cates {
		if sp.CategoryLv == 2 && sp.CategoryID == "" {
			addID, addName, err = FromNameCheckCategoryFatherID(k, sp, sps)
			if err != nil {
				return addIDs, addNames, updateIDs, err
			}
			addIDs = append(addIDs, addID)
			addNames = append(addNames, addName)
		} else if sp.CategoryLv == 2 && sp.CategoryID != "" {
			for _, oldInfo := range categoryList {
				if sp.CategoryID == oldInfo.CategoryID {
					if oldInfo.CategoryIDMerge.CategoryID1 == "" || oldInfo.CategoryIDMerge.CategoryID2 == "" {
						_, _, err = FromNameCheckCategoryFatherID(k, sp, sps)
						updateIDs = append(updateIDs, sp.CategoryID)
					}
				}
			}

		}
	}
	for k, sp := range sps.Cates {
		if sp.CategoryLv == 3 && sp.CategoryID == "" {
			addID, addName, err = FromNameCheckCategoryFatherID(k, sp, sps)
			if err != nil {
				return addIDs, addNames, updateIDs, err
			}
			addIDs = append(addIDs, addID)
			addNames = append(addNames, addName)
		} else if sp.CategoryLv == 3 && sp.CategoryID != "" {
			for _, oldInfo := range categoryList {
				if sp.CategoryID == oldInfo.CategoryID {
					if oldInfo.CategoryIDMerge.CategoryID1 == "" || oldInfo.CategoryIDMerge.CategoryID2 == "" ||
						oldInfo.CategoryIDMerge.CategoryID3 == "" {
						_, _, err = FromNameCheckCategoryFatherID(k, sp, sps)
						updateIDs = append(updateIDs, sp.CategoryID)
					}
				}
			}
		}
	}
	for k, sp := range sps.Cates {
		if sp.CategoryLv == 4 && sp.CategoryID == "" {
			addID, addName, err = FromNameCheckCategoryFatherID(k, sp, sps)
			if err != nil {
				return addIDs, addNames, updateIDs, err
			}
			addIDs = append(addIDs, addID)
			addNames = append(addNames, addName)
		} else if sp.CategoryLv == 4 && sp.CategoryID != "" {
			for _, oldInfo := range categoryList {
				if sp.CategoryID == oldInfo.CategoryID {
					if oldInfo.CategoryIDMerge.CategoryID1 == "" || oldInfo.CategoryIDMerge.CategoryID2 == "" ||
						oldInfo.CategoryIDMerge.CategoryID3 == "" || oldInfo.CategoryIDMerge.CategoryID4 == "" {
						_, _, err = FromNameCheckCategoryFatherID(k, sp, sps)
						updateIDs = append(updateIDs, sp.CategoryID)
					}
				}
			}
		}
	}
	for k, sp := range sps.Cates {
		if sp.CategoryLv == 5 && sp.CategoryID == "" {
			addID, addName, err = FromNameCheckCategoryFatherID(k, sp, sps)
			if err != nil {
				return addIDs, addNames, updateIDs, err
			}
			addIDs = append(addIDs, addID)
			addNames = append(addNames, addName)
		} else if sp.CategoryLv == 5 && sp.CategoryID != "" {
			for _, oldInfo := range categoryList {
				if sp.CategoryID == oldInfo.CategoryID {
					if oldInfo.CategoryIDMerge.CategoryID1 == "" || oldInfo.CategoryIDMerge.CategoryID2 == "" ||
						oldInfo.CategoryIDMerge.CategoryID3 == "" || oldInfo.CategoryIDMerge.CategoryID4 == "" ||
						oldInfo.CategoryIDMerge.CategoryID5 == "" {
						_, _, err = FromNameCheckCategoryFatherID(k, sp, sps)
						updateIDs = append(updateIDs, sp.CategoryID)
					}
				}
			}
		}
	}
	return addIDs, addNames, updateIDs, nil
}

// 通过id和fakerid找到fatherid
func FromNameCheckCategoryFatherID(k int, sp AssetCategory, sps *Assets) (addID, addName string, err error) {
	var oriCategory AssetCategory
	if sp.CategoryID == "" {
		if strings.Contains(sp.FatherID, "-") {
			for _, v := range sps.Cates {
				if sp.FatherID == v.FakerID {
					sp.FatherID = v.CategoryID
					sp.CategoryIDMerge = v.CategoryIDMerge
					sps.Cates[k].FatherID = v.CategoryID
					break
				}
			}
		}

		//先检查是否同级类目存在同名
		err = CategoryCheckSameName(sp)
		if err != nil {
			return addID, addName, fmt.Errorf("存在同名类目")
		}
		sps.Cates[k].CategoryID = tools.NewUuid()
		sps.Cates[k].CreatedTime = time.Now().Format("2006-01-02 15:04:05")
		sps.Cates[k].UpdatedTime = sp.CreatedTime
	}
	oriCategory, err = CategoryInfo(sp.FatherID)
	if err != nil {
		//查询失败就是没有id，是新建给的，那就直接拿
		fmt.Printf("查询id错误，使用当前参数的信息,id:%s,fatherid:%s", sp.CategoryID, sp.FatherID)
		logger.Error.Printf("查询id错误，使用当前参数的信息,id:%s,fatherid:%s", sp.CategoryID, sp.FatherID)
		for _, v := range sps.Cates {
			if v.CategoryID == sp.FatherID {
				oriCategory.CategoryIDMerge = v.CategoryIDMerge
				break
			}
		}

	}
	/*for _, v := range sps.Cates {
		if v.CategoryID == sp.FatherID {
			oriCategory.CategoryIDMerge = v.CategoryIDMerge
			break
		}
	}*/
	sps.Cates[k].CategoryIDMerge = oriCategory.CategoryIDMerge
	switch sp.CategoryLv {
	case 2:
		sps.Cates[k].CategoryIDMerge.CategoryID2 = sps.Cates[k].CategoryID
	case 3:
		sps.Cates[k].CategoryIDMerge.CategoryID3 = sps.Cates[k].CategoryID
	case 4:
		sps.Cates[k].CategoryIDMerge.CategoryID4 = sps.Cates[k].CategoryID
	case 5:
		sps.Cates[k].CategoryIDMerge.CategoryID5 = sps.Cates[k].CategoryID

	}
	return sps.Cates[k].CategoryID, sps.Cates[k].CategoryName, nil
}

func CategoryUpdate(sp AssetCategory) error {
	var doc gin.H
	if sp.CategoryIDMerge.CategoryID0 != "" {
		doc = gin.H{
			"categoryname":     sp.CategoryName,
			"updatedtime":      time.Now().Format("2006-01-02 15:04:05"),
			"categoryencoding": sp.CategoryEncoding,
			"num":              sp.Num,
			"categoryidmerge":  sp.CategoryIDMerge,
		}
	} else {
		doc = gin.H{
			"categoryname":     sp.CategoryName,
			"categoryencoding": sp.CategoryEncoding,
			"updatedtime":      time.Now().Format("2006-01-02 15:04:05"),
			"num":              sp.Num,
			//"categoryidmerge": sp.CategoryIDMerge,
		}
	}
	err := serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetCategory, common.RefreshWait, sp.CategoryID, doc)
	if err != nil {
		logger.Error.Printf("类目信息更新至es失败:%s", err.Error())
		return err
	}
	//兼容所有的资产
	body2 := gin.H{
		"script": gin.H{
			"inline": fmt.Sprintf(`ctx._source.inventorycategory="%s";ctx._source.inventorycategoryencoding="%s";`, sp.CategoryName, sp.CategoryEncoding),
		},
		"query": gin.H{
			"term": gin.H{
				"inventorycategoryid": sp.CategoryID,
			},
		},
	}

	err = serveresclient.NewESClientCl.UpdByQuery(common.DataBaseModelAsset, common.TbAssetInventory, body2)
	if err != nil {
		logger.Error.Printf("类目信息更新资产至es失败:%s", err.Error())
		return err
	}
	return nil
}

func CategoryInfo(id string) (res AssetCategory, err error) {
	var r AssetCategoryHits
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"categoryid": id,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCategory, body)
	if err != nil {
		logger.Error.Printf("查询资产类目失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	if len(r.Hits1.Hits2) > 0 {
		res = r.Hits1.Hits2[0].AssetCatalog
	} else {
		logger.Error.Printf("此id查询失败")
		return res, fmt.Errorf("此id查询失败")
	}

	return res, nil
}

func CategoryCheckAsset(sp AssetCategory) (CanDel bool, cateIdsInfo []string, err error) {

	//查询自己及子类目的资产
	var body interface{}
	switch sp.CategoryLv {
	case 1:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"categoryidmerge.categoryid1": sp.CategoryID,
						},
					},
				},
			},
		}
	case 2:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"categoryidmerge.categoryid2": sp.CategoryID,
						},
					},
				},
			},
		}
	case 3:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"categoryidmerge.categoryid3": sp.CategoryID,
						},
					},
				},
			},
		}
	case 4:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"categoryidmerge.categoryid4": sp.CategoryID,
						},
					},
				},
			},
		}
	case 5:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"categoryidmerge.categoryid5": sp.CategoryID,
						},
					},
				},
			},
		}

	}
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"categoryidmerge.": sp.CategoryID,
					},
				},
			},
		},
	}
	var res AssetCategoryHits
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCategory, body)
	if err != nil {
		logger.Error.Printf("查询资产类目失败:%s", err.Error())
		return false, cateIdsInfo, err
	}
	err = json.Unmarshal([]byte(strInfo), &res)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return false, cateIdsInfo, err
	}

	for _, v := range res.Hits1.Hits2 {
		cateIdsInfo = append(cateIdsInfo, v.AssetCatalog.CategoryID)
	}
	for _, v := range cateIdsInfo {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"inventorycategoryid": v,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		num, err := serveresclient.NewESClientCl.SearchCount(common.DataBaseModelAsset, common.TbAssetInventory, body)
		if err != nil {
			logger.Error.Println("查询是否存在上线模板失败:", err)
			return false, cateIdsInfo, err
		}
		if num >= 1 {
			logger.Error.Println("除类目或子类目已关联资产，不允许删除")
			return false, cateIdsInfo, fmt.Errorf("删除类目或子类目已关联资产，不允许删除")
		}
	}
	return true, cateIdsInfo, nil
}

func CategoryDelete(cateIdsInfo []string) (err error) {
	for _, v := range cateIdsInfo {
		err = serveresclient.NewESClientCl.DelByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetCategory, "true", v)
		if err != nil {
			logger.Error.Println("删除类目及子类目失败:", err)
			return err
		}
	}
	return nil
}

func CategoryMove(sp AssetCategory) (err error) {
	body := gin.H{
		"father_id":         sp.FatherID,
		"category_id_merge": sp.CategoryIDMerge,
		"updated_time":      time.Now().Format("2006-01-02 15:04:05"),
	}
	err = serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbAssetCategory, sp.CategoryID, body)
	if err != nil {
		logger.Error.Println("移动类目失败:", err)
		return err
	}
	return nil
}

func CategoryDefaultExSave(lv int, id string) (err error) {
	body := gin.H{
		"defaultex": lv,
	}
	err = serveresclient.NewESClientCl.UpdByID(common.DataBaseAuth, common.TbProject, id, body)
	if err != nil {
		logger.Error.Println("保存默认展示类目等级失败:", err)
		return err
	}
	return nil
}

func CategoryInfoList(projectID string) (res []AssetCategory, err error) {

	var resHit AssetCategoryHits
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": projectID,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCategory, body)
	if err != nil {
		logger.Error.Printf("查询资产类目失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &resHit)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	for _, v := range resHit.Hits1.Hits2 {
		res = append(res, v.AssetCatalog)
	}
	return res, nil
}

func CategoryInfoDetail(id string) (res AssetCategory, err error) {
	var resHit AssetCategoryHits
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"categoryid": id,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCategory, body)
	if err != nil {
		logger.Error.Printf("查询资产类目失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &resHit)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	if len(resHit.Hits1.Hits2) > 0 {
		res = resHit.Hits1.Hits2[0].AssetCatalog
	}
	return res, nil
}

func CategoryGetIds(id string, lv int) (res []string, err error) {
	var termid gin.H
	switch lv {
	case 1:
		termid = gin.H{
			"term": gin.H{
				"categoryidmerge.categoryid1": id,
			},
		}
	case 2:
		termid = gin.H{
			"term": gin.H{
				"categoryidmerge.categoryid2": id,
			},
		}
	case 3:
		termid = gin.H{
			"term": gin.H{
				"categoryidmerge.categoryid3": id,
			},
		}
	case 4:
		termid = gin.H{
			"term": gin.H{
				"categoryidmerge.categoryid4": id,
			},
		}
	case 5:
		termid = gin.H{
			"term": gin.H{
				"categoryidmerge.categoryid5": id,
			},
		}

	}
	var resHit AssetCategoryHits
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": termid,
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCategory, body)
	if err != nil {
		logger.Error.Printf("查询资产类目失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &resHit)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	if len(resHit.Hits1.Hits2) > 0 {
		for _, v := range resHit.Hits1.Hits2 {
			res = append(res, v.AssetCatalog.CategoryID)
		}
	}
	return res, nil
}

func addDatatypeFilter(datatype string) gin.H {
	var term gin.H
	if datatype != "" {
		term = gin.H{
			"term": gin.H{
				"assetcatalog.basicinfo.assettype": datatype,
			},
		}
	}
	return term
}

func add_status_filter(module string) gin.H {
	var term gin.H
	if module != "" {
		switch module {
		case "attach":
			term = gin.H{
				"term": gin.H{
					"status": 3,
				},
			}

		case "put":
			term = gin.H{
				"term": gin.H{
					"attachinfo.attachstatus": "已挂载",
				},
			}
		case "portal":
			onlineStatus := []string{"已上架", "下架审核中"}
			term = gin.H{
				"terms": gin.H{
					"putinfo.putstatus": onlineStatus,
				},
			}

		default:
			term = gin.H{
				"term": gin.H{
					"attachinfo.attachstatus": "已挂载",
				},
			}

		}

	}
	return term
}

func CategoryInfoStats(module, datatype, changemode string, infoList []AssetCategory, userID, projectID string) ([]AssetCategory, error) {
	//----------------
	// 挂载和发布需要过滤,挂载统计已审核的资产,发布统计已挂载的资产
	//------------
	relation, err := user.GetUserRelation(userID, projectID)
	if err != nil {
		logger.Error.Println(err)
		return infoList, err
	}
	//新增:如果用户组设置了角色,那么该用户组也能搜到,所以针对用户组拓追加角色
	extraRoleids, err := user.WithGroupIDGetRole(relation.UserGroupIDS, projectID)
	if err != nil {
		logger.Error.Println("查询用户组信息异常", err)
	}
	logger.Info.Println("额外的角色信息:", extraRoleids)
	relation.RoleIDS = append(relation.RoleIDS, extraRoleids...)
	//-------------
	//统一查询，使用errgroup并发
	var (
		mu sync.Mutex
		g  = new(errgroup.Group)
	)

	for k := range infoList {
		k := k // 避免闭包问题
		g.Go(func() error {
			v := infoList[k]
			var mustTerms []gin.H
			var mustNotTerms []gin.H
			term1 := gin.H{
				"term": gin.H{
					"inventorycategoryid": v.CategoryID,
				},
			}
			term2 := gin.H{
				"term": gin.H{
					"projectid": v.ProjectID,
				},
			}
			mustTerms = append(mustTerms, term1, term2)
			if module != "" {
				mustTerms = append(mustTerms, add_status_filter(module))
			}
			if datatype != "" {
				mustTerms = append(mustTerms, addDatatypeFilter(datatype))
			}
			var body interface{}
			if module == "portal" {
				switch changemode {
				case "开放数据":
					term3 := gin.H{
						"term": gin.H{
							"assetcatalog.openinfo.opentype": "完全开放",
						},
					}
					mustTerms = append(mustTerms, term3)
					skipM := gin.H{
						"terms": gin.H{
							"assetcatalog.basicinfo.assettype": []string{assetcommon.ASSET_ZBSJ},
						},
					}
					mustNotTerms = append(mustNotTerms, skipM)
				case "共享数据":
					term3 := gin.H{
						"terms": gin.H{
							"assetcatalog.sharedinfo.sharedtype": []string{"有条件共享", "无条件共享"},
						},
					}
					mustTerms = append(mustTerms, term3)
					skipM := gin.H{
						"terms": gin.H{
							"assetcatalog.basicinfo.assettype": []string{assetcommon.ASSET_ZBSJ},
						},
					}
					mustNotTerms = append(mustNotTerms, skipM)
				}
				var should []interface{}
				tUser := gin.H{
					"term": gin.H{
						"putinfo.putcheckinfo.sharerange.userids": userID,
					},
				}
				tGroup := gin.H{
					"terms": gin.H{
						"putinfo.putcheckinfo.sharerange.usergroupids": relation.UserGroupIDS,
					},
				}
				tRole := gin.H{
					"terms": gin.H{
						"putinfo.putcheckinfo.sharerange.roleids": relation.RoleIDS,
					},
				}
				tAll := gin.H{
					"term": gin.H{
						"putinfo.putcheckinfo.sharerange.clickset": false,
					},
				}
				should = append(should, tUser, tGroup, tRole, tAll)
				for _, v := range relation.RoleIDS {
					if v == common2.ProjectRoleID {
						tAll := gin.H{
							"term": gin.H{
								"putinfo.putcheckinfo.sharerange.clickset": true,
							},
						}
						should = append(should, tAll)
						break
					}
				}
				body = gin.H{
					"query": gin.H{
						"bool": gin.H{
							"must":                 mustTerms,
							"must_not":             mustNotTerms,
							"should":               should,
							"minimum_should_match": 1,
						},
					},
					"size": common.UnlimitSize,
				}
			} else {
				body = gin.H{
					"query": gin.H{
						"bool": gin.H{
							"must":     mustTerms,
							"must_not": mustNotTerms,
						},
					},
					"size": common.UnlimitSize,
				}
			}
			strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
			if err != nil {
				return err
			}
			var resHit AssetInventoryHits
			err = json.Unmarshal([]byte(strInfo), &resHit)
			if err != nil {
				return err
			}
			var oldres []AssetInventory
			for _, v := range resHit.Hits1.Hits2 {
				if !strings.Contains(v.AssetInventory.InventoryID, "change") {
					oldres = append(oldres, v.AssetInventory)
				}
			}
			mu.Lock()
			infoList[k].Stats = len(oldres)
			infoList[k].CurrentData = len(oldres)
			mu.Unlock()
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return infoList, err
	}

	//计算子类
	for k, v := range infoList {
		if v.CategoryLv == 4 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 5 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	for k, v := range infoList {
		if v.CategoryLv == 3 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 4 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	for k, v := range infoList {
		if v.CategoryLv == 2 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 3 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	for k, v := range infoList {
		if v.CategoryLv == 1 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 2 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	if datatype == "消息流" {
		// 移除stats为0的目录及其子目录
		var newInfoList []AssetCategory
		for _, v := range infoList {
			if v.Stats > 0 {
				newInfoList = append(newInfoList, v)
			}
		}
		infoList = newInfoList
	}

	return infoList, nil
}

func CategoryInfoStatsBak(module, datatype, changemode string, infoList []AssetCategory, userID, projectID string) ([]AssetCategory, error) {
	//----------------
	// 挂载和发布需要过滤,挂载统计已审核的资产,发布统计已挂载的资产

	//-------------
	//统一查询
	var resHit AssetInventoryHits
	var body interface{}
	for k, v := range infoList {
		var mustTerms []gin.H
		var mustNotTerms []gin.H
		term1 := gin.H{
			"term": gin.H{
				"inventorycategoryid": v.CategoryID,
			},
		}
		term2 := gin.H{
			"term": gin.H{
				"projectid": v.ProjectID,
			},
		}
		mustTerms = append(mustTerms, term1, term2)
		if module != "" {
			mustTerms = append(mustTerms, add_status_filter(module))
		}

		if datatype != "" {
			mustTerms = append(mustTerms, addDatatypeFilter(datatype))
		}

		if module == "portal" {
			switch changemode {
			case "开放数据":
				term3 := gin.H{
					"term": gin.H{
						"assetcatalog.openinfo.opentype": "完全开放",
					},
				}
				mustTerms = append(mustTerms, term3)
				skipM := gin.H{
					"terms": gin.H{
						"assetcatalog.basicinfo.assettype": []string{assetcommon.ASSET_ZBSJ},
					},
				}
				mustNotTerms = append(mustNotTerms, skipM)
			case "共享数据":
				term3 := gin.H{
					"terms": gin.H{
						"assetcatalog.sharedinfo.sharedtype": []string{"有条件共享", "无条件共享"},
					},
				}
				mustTerms = append(mustTerms, term3)
				skipM := gin.H{
					"terms": gin.H{
						"assetcatalog.basicinfo.assettype": []string{assetcommon.ASSET_ZBSJ},
					},
				}
				mustNotTerms = append(mustNotTerms, skipM)
			}

			relation, err := user.GetUserRelation(userID, projectID)
			if err != nil {
				logger.Error.Println(err)
				return infoList, err
			}
			//新增:如果用户组设置了角色,那么该用户组也能搜到,所以针对用户组拓追加角色
			extraRoleids, err := user.WithGroupIDGetRole(relation.UserGroupIDS, projectID)
			if err != nil {
				logger.Error.Println("查询用户组信息异常", err)
			}
			logger.Info.Println("额外的角色信息:", extraRoleids)
			relation.RoleIDS = append(relation.RoleIDS, extraRoleids...)

			//共享范围
			var should []interface{}
			tUser := gin.H{
				"term": gin.H{
					"putinfo.putcheckinfo.sharerange.userids": userID,
				},
			}

			tGroup := gin.H{
				"terms": gin.H{
					"putinfo.putcheckinfo.sharerange.usergroupids": relation.UserGroupIDS,
				},
			}

			tRole := gin.H{
				"terms": gin.H{
					"putinfo.putcheckinfo.sharerange.roleids": relation.RoleIDS,
				},
			}

			//未限定分享范围
			tAll := gin.H{
				"term": gin.H{
					"putinfo.putcheckinfo.sharerange.clickset": false,
				},
			}
			should = append(should, tUser, tGroup, tRole, tAll)
			//如果是项目管理员,啥都能看
			for _, v := range relation.RoleIDS {
				if v == common2.ProjectRoleID {
					tAll := gin.H{
						"term": gin.H{
							"putinfo.putcheckinfo.sharerange.clickset": true,
						},
					}
					should = append(should, tAll)
					break
				}
			}

			body = gin.H{
				"query": gin.H{
					"bool": gin.H{
						"must":                 mustTerms,
						"must_not":             mustNotTerms,
						"should":               should,
						"minimum_should_match": 1,
					},
				},
				"size": common.UnlimitSize,
			}

		} else {
			body = gin.H{
				"query": gin.H{
					"bool": gin.H{
						"must":     mustTerms,
						"must_not": mustNotTerms,
					},
				},
				"size": common.UnlimitSize,
			}
		}
		strbody, _ := json.Marshal(body)
		logger.Info.Println("strbody", string(strbody))
		strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
		if err != nil {
			logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
			return infoList, nil
		}
		err = json.Unmarshal([]byte(strInfo), &resHit)
		if err != nil {
			logger.Error.Printf("解析表数据失败:%s", err.Error())
			return infoList, nil
		}
		var oldres []AssetInventory
		for _, v := range resHit.Hits1.Hits2 {
			if !strings.Contains(v.AssetInventory.InventoryID, "change") {
				oldres = append(oldres, v.AssetInventory)
			}

		}

		infoList[k].Stats = len(oldres)
		infoList[k].CurrentData = len(oldres)
	}

	//计算子类
	for k, v := range infoList {
		if v.CategoryLv == 4 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 5 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	for k, v := range infoList {
		if v.CategoryLv == 3 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 4 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	for k, v := range infoList {
		if v.CategoryLv == 2 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 3 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	for k, v := range infoList {
		if v.CategoryLv == 1 && v.Stats == 0 {
			num := 0
			for _, v1 := range infoList {
				if v1.CategoryLv == 2 && v1.FatherID == v.CategoryID {
					num += v1.Stats
				}
			}
			infoList[k].Stats = num
		}
	}
	if datatype == "消息流" {
		// 移除stats为0的目录及其子目录
		var newInfoList []AssetCategory
		for _, v := range infoList {
			if v.Stats > 0 {
				newInfoList = append(newInfoList, v)
			}
		}
		infoList = newInfoList
	}

	return infoList, nil
}

func CategoryGetDefaultEx(id string) (count int64, ids []string, err error) {
	str, err := serveresclient.NewESClientCl.SearchByID(common.DataBaseAuth, common.TbProject, id)
	if err != nil {
		logger.Error.Println("保存默认展示类目等级失败:", err)
		return count, ids, err
	}
	count = gjson.Get(string(str), "defaultex").Int()
	managerids := gjson.Get(str, "managerids").Array()
	for _, v := range managerids {
		ids = append(ids, v.String())
	}
	return count, ids, nil
}

func HandName(oldName string) (newName string) {
	newName = strings.Replace(oldName, "%", "", -1)
	newName = strings.Replace(newName, "￥", "", -1)
	newName = strings.Replace(newName, "$", "", -1)
	newName = strings.Replace(newName, "?", "", -1)
	newName = strings.Replace(newName, "/", "", -1)
	newName = strings.Replace(newName, " ", "", -1)
	newName = strings.Replace(newName, "{", "", -1)
	newName = strings.Replace(newName, "}", "", -1)
	newName = strings.Replace(newName, "[", "", -1)
	newName = strings.Replace(newName, "]", "", -1)
	newName = strings.Replace(newName, "(", "", -1)
	newName = strings.Replace(newName, ")", "", -1)
	newName = strings.Replace(newName, "（", "", -1)
	newName = strings.Replace(newName, "）", "", -1)
	newName = strings.Replace(newName, "【", "", -1)
	newName = strings.Replace(newName, "】", "", -1)
	return newName
}

func FiledToChinese(fieldInfo []source.MetaSourceFieldInfo, dbtype string) (fieldInfoNew []AssetInfo, err error) {
	var fInfo AssetInfo
	for _, v := range fieldInfo {
		//fInfo.InfoItemsName = HandName(v.Annotation)
		/*if len(fInfo.InfoItemsName) > 128 {
			fInfo.InfoItemsName = fInfo.InfoItemsName[:128]
		}
		if fInfo.InfoItemsName == "" {
			fInfo.InfoItemsName = v.Name
		}*/
		fInfo.InfoItemsName = v.Name
		fInfo.InfoItemsComment = HandName(v.Annotation)
		//switch dbtype {
		//case common.Hive, common.ODPS, common.Inceptor:
		//	switch v.FieldType {
		//	case "string", "boolean", "char", "varchar":
		//		fInfo.InfoItemsType = "字符型"
		//	case "bigint", "tinyint", "smallint", "int":
		//		fInfo.InfoItemsType = "整型"
		//	case "double", "float":
		//		fInfo.InfoItemsType = "浮点型"
		//	case "date", "timestamp", "datetime":
		//		fInfo.InfoItemsType = "时间类型"
		//	case "binary":
		//		fInfo.InfoItemsType = "二进制类型"
		//	default:
		//		fInfo.InfoItemsType = "字符型"
		//	}
		//case common.Stork, common.POSTGRES, common.Teryx, common.GREENPLUM, common.GaussDB, common.GaussDBA:
		//	switch v.FieldType {
		//	case "text", "boolean", "bool", "bit", "char", "character", "character varying", "varchar":
		//		fInfo.InfoItemsType = "字符型"
		//	case "bigint", "int", "smallint", "int2", "integer", "int4", "int8","tin":
		//		fInfo.InfoItemsType = "整型"
		//	case "numeric", "real", "float4", "double", "precision", "float8", "decimal":
		//		fInfo.InfoItemsType = "浮点型"
		//	case "date", "timestamp", "time":
		//		fInfo.InfoItemsType = "时间类型"
		//	case "bytea":
		//		fInfo.InfoItemsType = "二进制类型"
		//	default:
		//		fInfo.InfoItemsType = "字符型"
		//	}
		//case common.DMDB:
		//	switch v.FieldType {
		//	case "text", "TEXT", "varchar", "VARCHAR", "char":
		//		fInfo.InfoItemsType = "字符型"
		//	case "bigint", "BIGINT", "int":
		//		fInfo.InfoItemsType = "整型"
		//	case "numeric", "NUMERIC":
		//		fInfo.InfoItemsType = "浮点型"
		//	case "date", "DATE", "timestamp", "TIMESTAMP", "time", "TIME":
		//		fInfo.InfoItemsType = "时间类型"
		//	case "blob", "BLOB":
		//		fInfo.InfoItemsType = "二进制类型"
		//	default:
		//		fInfo.InfoItemsType = "字符型"
		//	}
		//case common.UXDB:
		//	switch v.FieldType {
		//	case "text", "TEXT", "varchar", "VARCHAR":
		//		fInfo.InfoItemsType = "字符型"
		//	case "bigint", "BIGINT":
		//		fInfo.InfoItemsType = "整型"
		//	case "numeric", "NUMERIC":
		//		fInfo.InfoItemsType = "浮点型"
		//	case "date", "DATE", "timestamp", "TIMESTAMP", "time", "TIME":
		//		fInfo.InfoItemsType = "时间类型"
		//	case "bytea", "BYTEA":
		//		fInfo.InfoItemsType = "二进制类型"
		//	default:
		//		fInfo.InfoItemsType = "字符型"
		//	}
		//}
		switch v.FieldType {
		case "text", "boolean", "bool", "bit", "char", "character", "character varying",
			"varchar", "string", "graphic", "long varchar", "vargraphic", "varchar2",
			"oid", "interval day to second", "interval year to mouth", "long", "nchar", "nvarchar2",
			"xml", "bfile":
			fInfo.InfoItemsType = "字符型"
		case "bigint", "int", "smallint", "int2", "integer", "int4", "int8", "tinyint",
			"signed binary integer", "unsigned binary integer":
			fInfo.InfoItemsType = "整型"
		case "numeric", "real", "float4", "double", "precision", "float8", "decimal", "float",
			"binary_float", "binary_double", "double precision":
			fInfo.InfoItemsType = "浮点型"
		case "date", "timestamp", "time", "datetime":
			fInfo.InfoItemsType = "时间类型"
		case "bytea", "binary", "varbinary", "blob", "longblob", "clob", "nclob", "longvarbinary":
			fInfo.InfoItemsType = "二进制类型"
		default:
			fInfo.InfoItemsType = "字符型"
		}
		fInfo.InfoItemsOriType = v.FieldType

		fieldInfoNew = append(fieldInfoNew, fInfo)
	}
	return fieldInfoNew, nil
}

func InventorySave(sp AssetInventory) error {

	//补充 promutilivalues
	HandleEnum(&sp)

	//添加部门属性  放到 HandleEnum 里

	if len(sp.DataProvider) == 0 {
		parts := strings.Split(sp.InventoryPath, "/")
		if len(parts) > 0 {
			sp.DataProvider = []string{parts[len(parts)-1]}
		} else {
			sp.DataProvider = []string{sp.InventoryPath}
		}
	}

	err := serveresclient.NewESClientCl.AddWithAimID(common.DataBaseModelAsset, common.TbAssetInventory, sp.InventoryID, sp)
	if err != nil {
		logger.Error.Printf("添加资产编目失败:%s", err.Error())
		return err
	}

	return nil
}

func HandleEnum(sp *AssetInventory) {
	for i, cust := range sp.AssetCatalog.CustomInfo {
		for j, pro := range cust.CustomProperties {

			if pro.ProSign == "ZCLM" && len(pro.MulitiSelected) == 1 {
				for _, k := range pro.MulitiEnumRated {
					if k.ID == pro.MulitiSelected[0] {
						sp.AssetCatalog.CustomInfo[i].CustomProperties[j].ProValue = k.Value
						break
					}
				}
				continue
			}
			var t []MulitiEnum
			//顶层/底层
			firstLevel := make([]string, 0)
			lastLevel := make([]string, 0)
			if len(pro.MulitiSelected) > 0 {
				t = handleEnum(pro.MulitiSelected, pro.MulitiEnumRated)
				sp.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue = t
				step := len(t) / len(pro.MulitiSelected)
				for i := range pro.MulitiSelected {
					f := step * i
					l := f + step - 1
					firstLevel = append(firstLevel, t[f].Value)
					lastLevel = append(lastLevel, t[l].Value)
				}
			} else {
				sp.AssetCatalog.CustomInfo[i].CustomProperties[j].ProMulitiValue = make([]MulitiEnum, 0)
			}
			//补充部门属性
			switch pro.ProSign {
			case common.AreaSign:
				sp.DataArea = firstLevel //业务领域取顶层
				//sp.DataArea = pro.ProValue
			case common.SystemSign:
				sp.DataSourceSystem = lastLevel //来源系统取底层
				//if pro.ProValue != "" {
				//	sp.DataSourceSystem = pro.ProValue
				//} else if len(pro.EnumeratedValues) > 0 {
				//	sp.DataSourceSystem = pro.EnumeratedValues[0]
				//}
			case common.ProviderSign:
				sp.DataProvider = lastLevel //所属部门取底层
				//sp.DataProvider = pro.ProValue
			}
		}
	}
}

func handleEnum(selected []string, rated []MulitiEnum) []MulitiEnum {
	var res []MulitiEnum
	//根据 selected 的 id，找到该枚举值在 rated 中的全链路
	m := make(map[string]MulitiEnum)
	for _, i := range rated {
		m[i.ID] = i
	}
	for _, i := range selected {
		if m[i].ID == "" {
			continue
		}
		thisEnum := i
		var t []string                //id 列表
		used := make(map[string]bool) //防止死循环
		for {
			if thisEnum == "" || used[thisEnum] {
				break
			}
			t = append(t, thisEnum)
			used[thisEnum] = true
			thisEnum = m[thisEnum].FatherID
		}
		for idx := len(t) - 1; idx >= 0; idx-- {
			res = append(res, m[t[idx]])
		}
	}
	return res
}

func InventoryUpdate(sp AssetInventory) error {
	//补充 promutilivalues
	HandleEnum(&sp)

	//添加部门属性  放到 HandleEnum 里

	if len(sp.DataProvider) == 0 {
		parts := strings.Split(sp.InventoryPath, "/")
		if len(parts) > 0 {
			sp.DataProvider = []string{parts[len(parts)-1]}
		} else {
			sp.DataProvider = []string{sp.InventoryPath}
		}
	}
	err := serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetInventory, common.RefreshWait, sp.InventoryID, sp)
	if err != nil {
		logger.Error.Printf("资产编目信息更新至es失败:%s", err.Error())
		return err
	}
	return nil
}

func GetInventoryInfo(id string) (res AssetInventory, err error) {
	var r AssetInventoryHits
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"inventoryid": id,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产编目失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	if len(r.Hits1.Hits2) > 0 {
		res = r.Hits1.Hits2[0].AssetInventory
	} else {
		logger.Error.Println("此id查询失败", id)
		return res, fmt.Errorf("此id查询失败:%s", id)
	}
	return res, nil
}

// 得到单表的所有信息
func GetAppInfo(id string) (MoneyApp, error) {
	var info MoneyApp
	res, err := serveresclient.NewESClientCl.SearchByID(common.DataBaseModelAsset, common.TbAssetApp, id)
	if err != nil {
		logger.Error.Printf("列举表失败:%s", err.Error())
		return info, err
	}
	err = json.Unmarshal([]byte(res), &info)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return info, err
	}
	return info, nil
}

// todo
func AssetDelistingCompatibility(inventoryids []string, statustype string, putuserids []string) error {
	//定义一个数组存储未处理工单
	var untreatedIDS1 []string //工单未开始status 0 ---status 8
	var untreatedIDS2 []string //工单审核中status 3 ---status 9
	//遍历所有资产，找到待上架部分

	//查询我的收藏对应信息
	{
		body_collect := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"terms": gin.H{
							"inventoryid": inventoryids,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		source, err := esutil.SearchByTerm(common.DataBaseModelAsset, common.TbCollect, body_collect)
		if err != nil {
			logger.Error.Printf("获取我的收藏数据失败:%s", err.Error())
			return err
		}

		result, _ := esutil.GetSourceAndTotal(source)

		var ids []string
		for _, v := range result {
			if statustype != "" {
				s := gjson.Get(v, "assetstatus").String()
				if statustype == "已下架" {
					ids = append(ids, gjson.Get(v, "id").String())
				}
				if statustype == "已删除" && (s == "" || s == "已下架") {
					ids = append(ids, gjson.Get(v, "id").String())
				}
				//新增上架恢复状态
				if statustype == "已上架" {
					ids = append(ids, gjson.Get(v, "id").String())
				}
			} else {
				ids = append(ids, gjson.Get(v, "id").String())
			}

		}
		var body_bulk string
		fmt.Println("ids1:", ids)
		for _, v := range ids {
			var docbytes []byte
			if statustype != "" {
				if statustype == "已上架" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "",
							"putuserids":  putuserids,
						},
					})
				} else {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": statustype,
						},
					})
				}
			} else {
				docbytes, _ = json.Marshal(gin.H{
					"doc": gin.H{
						"putuserids": putuserids,
					},
				})
			}

			body_bulk = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
				body_bulk, common.DataBaseModelAsset, common.TbCollect, v, string(docbytes))
		}
		body_bulk = fmt.Sprintf("%s\n", body_bulk)
		err = esutil.UpdateBulk(common.DataBaseModelAsset, common.TbCollect, body_bulk)
		if err != nil {
			logger.Error.Printf("更新我的收藏数据失败:%s", err.Error())
			return err
		}
	}
	//查询我的申请对应信息
	{
		body_calling := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"terms": gin.H{
							"assetid": inventoryids,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		source, err := esutil.SearchByTerm(common.DataBaseAsset, common.TableCalling, body_calling)
		if err != nil {
			logger.Error.Printf("获取我的申请数据失败:%s", err.Error())
			return err
		}

		result, _ := esutil.GetSourceAndTotal(source)

		var ids []string
		var auditids []string
		for _, v := range result {
			if statustype != "" {
				s := gjson.Get(v, "assetstatus").String()
				if statustype == "已下架" {
					ids = append(ids, gjson.Get(v, "id").String())
				}
				if statustype == "已删除" && (s == "" || s == "已下架") {
					ids = append(ids, gjson.Get(v, "id").String())
				}
				//新增上架恢复状态
				if statustype == "已上架" {
					ids = append(ids, gjson.Get(v, "id").String())
				}
			} else {
				ids = append(ids, gjson.Get(v, "id").String())
			}
			//获取所有的申请id
			auditids = append(auditids, gjson.Get(v, "auditid").String())
		}
		var body_bulk string
		for _, v := range ids {
			var docbytes []byte
			if statustype != "" {
				if statustype == "已上架" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "",
							"putuserids":  putuserids,
						},
					})
				} else {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": statustype,
						},
					})
				}
			} else {
				docbytes, _ = json.Marshal(gin.H{
					"doc": gin.H{
						"putuserids": putuserids,
					},
				})
			}
			body_bulk = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
				body_bulk, common.DataBaseAsset, common.TableCalling, v, string(docbytes))
		}
		body_bulk = fmt.Sprintf("%s\n", body_bulk)
		err = esutil.UpdateBulk(common.DataBaseAsset, common.TableCalling, body_bulk)
		if err != nil {
			logger.Error.Printf("更新我的申请数据失败:%s", err.Error())
			return err
		}
		var body_bulk2 string
		for _, v := range auditids {
			var docbytes []byte
			if statustype != "" {
				if statustype == "已上架" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "",
							"putuserids":  putuserids,
						},
					})
				} else {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": statustype,
						},
					})
				}

			} else {
				docbytes, _ = json.Marshal(gin.H{
					"doc": gin.H{
						"putuserids": putuserids,
					},
				})
			}
			body_bulk2 = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
				body_bulk2, common.DataBaseAsset, common.AuditRecordESTB, v, string(docbytes))
		}
		body_bulk2 = fmt.Sprintf("%s\n", body_bulk2)
		err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body_bulk2)
		if err != nil {
			logger.Error.Printf("更新审核工单我的申请数据失败:%s", err.Error())
			return err
		}
	}
	//查询审核记录对应信息
	{
		var should []gin.H
		term1 := gin.H{
			"terms": gin.H{
				"objectid": inventoryids,
			},
		}
		term2 := gin.H{
			"terms": gin.H{
				"assetid": inventoryids,
			},
		}
		should = append(should, term1, term2)

		body_audit := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"should":               should,
					"minimum_should_match": 1,
				},
			},
			"size": common.UnlimitSize,
		}
		source, err := esutil.SearchByTerm(common.DataBaseAsset, common.AuditRecordESTB, body_audit)
		if err != nil {
			logger.Error.Printf("获取我的审核数据失败:%s", err.Error())
			return err
		}

		result, _ := esutil.GetSourceAndTotal(source)

		var ids []string
		for _, v := range result {
			if statustype != "" {
				aduitStatus := gjson.Get(v, "status").Int()
				s := gjson.Get(v, "assetstatus").String()

				if statustype == "已下架" {
					if aduitStatus == 0 {
						untreatedIDS1 = append(untreatedIDS1, gjson.Get(v, "id").String())
					} else if aduitStatus == 3 {
						untreatedIDS2 = append(untreatedIDS2, gjson.Get(v, "id").String())
					} else { //已处理

						ids = append(ids, gjson.Get(v, "id").String())
					}
				}
				if statustype == "已删除" && (s == "" || s == "已下架") {
					if aduitStatus == 0 || aduitStatus == 3 {
						untreatedIDS1 = append(untreatedIDS1, gjson.Get(v, "id").String())
					} else {
						ids = append(ids, gjson.Get(v, "id").String())
					}
				}
				//新增上架恢复状态
				if statustype == "已上架" {
					if aduitStatus == 8 {
						untreatedIDS1 = append(untreatedIDS1, gjson.Get(v, "id").String())
					} else if aduitStatus == 9 {
						untreatedIDS2 = append(untreatedIDS2, gjson.Get(v, "id").String())
					} else { //已处理
						ids = append(ids, gjson.Get(v, "id").String())
					}
				}
			}
			//} else {
			//	ids = append(ids, gjson.Get(v, "id").String())
			//}
		}

		if len(ids) != 0 {
			var body_bulk string
			for _, v := range ids {
				var docbytes []byte
				if statustype != "" {
					if statustype == "已上架" {
						docbytes, _ = json.Marshal(gin.H{
							"doc": gin.H{
								"assetstatus": "",
							},
						})
					} else if statustype == "已删除" {
						docbytes, _ = json.Marshal(gin.H{
							"doc": gin.H{
								"assetstatus": statustype,
							},
						})

					} else {
						docbytes, _ = json.Marshal(gin.H{
							"doc": gin.H{
								"assetstatus": statustype,
							},
						})
					}
				}
				//} else {
				//	docbytes, _ = json.Marshal(gin.H{
				//		"doc": gin.H{
				//			"putuserids": putuserids,
				//		},
				//	})
				//}
				body_bulk = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
					body_bulk, common.DataBaseAsset, common.AuditRecordESTB, v, string(docbytes))
			}
			body_bulk = fmt.Sprintf("%s\n", body_bulk)
			err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body_bulk)
			if err != nil {
				logger.Error.Printf("更新我的审核数据失败:%s", err.Error())
				return err
			}
		}

		if len(untreatedIDS1) != 0 {
			var body string
			for _, i := range untreatedIDS1 {
				var docbytes []byte
				if statustype == "已上架" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "",
							"status":      0,
						},
					})
				} else if statustype == "已下架" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "已下架",
							"status":      8,
						},
					})
				} else if statustype == "已删除" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "已删除",
							"status":      8,
						},
					})
				}
				//} else if len(putuserids) > 0 {
				//	docbytes, _ = json.Marshal(gin.H{
				//		"doc": gin.H{
				//			"putuserids": putuserids,
				//		},
				//	})
				//}
				body = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
					body, common.DataBaseAsset, common.AuditRecordESTB, i, string(docbytes))
			}
			body = fmt.Sprintf("%s\n", body)
			err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body)
			if err != nil {
				logger.Error.Printf("更新我的审核数据失败:%s", err.Error())
				return err
			}
		}

		if len(untreatedIDS2) != 0 {
			var body string
			for _, i := range untreatedIDS1 {
				var docbytes []byte
				if statustype == "已上架" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "",
							//"putuserids":  putuserids,
							"status": 3,
						},
					})
				} else if statustype == "已下架" {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"assetstatus": "已下架",
							//"putuserids":  putuserids,
							"status": 9,
						},
					})
				}
				//} else if len(putuserids) > 0 {
				//	docbytes, _ = json.Marshal(gin.H{
				//		"doc": gin.H{
				//			"putuserids": putuserids,
				//		},
				//	})
				//}
				body = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
					body, common.DataBaseAsset, common.AuditRecordESTB, i, string(docbytes))

			}
			body = fmt.Sprintf("%s\n", body)
			err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body)
			if err != nil {
				logger.Error.Printf("更新我的审核数据失败:%s", err.Error())
				return err
			}
		}

	}

	//查询我的纠错对应信息
	{
		body_dataerrr := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"terms": gin.H{
							"assetid": inventoryids,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		source, err := esutil.SearchByTerm(common.DataInteractiveCommunication, common.TbDataErrorCorrection, body_dataerrr)
		if err != nil {
			logger.Error.Printf("获取我的纠错数据失败:%s", err.Error())
			return err
		}

		result, _ := esutil.GetSourceAndTotal(source)

		var ids []string
		auditids := make(map[string]string)
		removeIDS := make(map[string]string)
		delIDS := make(map[string]string)
		perIDS := make(map[string]string)
		var todo []string
		var done []string
		var projectID string

		for _, v := range result {
			projectID = gjson.Get(v, "projectid").String()
			if statustype != "" {
				s := gjson.Get(v, "assetstatus").String()
				if statustype == "已下架" {
					ids = append(ids, gjson.Get(v, "corrid").String())
					if gjson.Get(v, "processstatus").Int() == 1 || gjson.Get(v, "processstatus").Int() == 3 {
						removeIDS[gjson.Get(v, "recordid").String()] = gjson.Get(v, "corrid").String()
					}
				}
				if statustype == "已删除" && (s == "" || s == "已下架") {
					ids = append(ids, gjson.Get(v, "corrid").String())
					if gjson.Get(v, "processstatus").Int() == 1 || gjson.Get(v, "processstatus").Int() == 3 {
						delIDS[gjson.Get(v, "recordid").String()] = gjson.Get(v, "corrid").String()
					}
				}
				//新增上架恢复状态
				if statustype == "已上架" {
					ids = append(ids, gjson.Get(v, "corrid").String())
					oldstatus := gjson.Get(v, "oldstatus").Int()
					if oldstatus == 1 || oldstatus == 3 {
						auditids[gjson.Get(v, "recordid").String()] = gjson.Get(v, "corrid").String()
					}

				}
			} else { //权限变更
				ids = append(ids, gjson.Get(v, "corrid").String())
				processstatus := gjson.Get(v, "processstatus").Int()
				oldstatus := gjson.Get(v, "oldstatus").Int()
				if processstatus == 1 || processstatus == 3 {
					todo = append(todo, gjson.Get(v, "corrid").String())
				}
				if oldstatus == 1 || oldstatus == 3 {
					done = append(done, gjson.Get(v, "corrid").String())
				}
				perIDS[gjson.Get(v, "recordid").String()] = gjson.Get(v, "corrid").String()
			}

		}

		if len(ids) != 0 {
			var body_bulk string
			for _, v := range ids {
				var docbytes []byte
				if statustype != "" {
					if statustype == "已上架" {
						docbytes, _ = json.Marshal(gin.H{
							"doc": gin.H{
								"assetstatus": "",
								"putuserids":  putuserids,
							},
						})
					} else {
						docbytes, _ = json.Marshal(gin.H{
							"doc": gin.H{
								"assetstatus": statustype,
							},
						})
					}
				} else {
					docbytes, _ = json.Marshal(gin.H{
						"doc": gin.H{
							"putuserids": putuserids,
						},
					})
				}
				body_bulk = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
					body_bulk, common.DataInteractiveCommunication, common.TbDataErrorCorrection, v, string(docbytes))
			}
			body_bulk = fmt.Sprintf("%s\n", body_bulk)
			//logger.Info.Println("body_bulk：", body_bulk)

			err = esutil.UpdateBulk(common.DataInteractiveCommunication, common.TbDataErrorCorrection, body_bulk)
			if err != nil {
				logger.Error.Printf("更新我的纠错数据失败:%s", err.Error())
				return err
			}
		}

		//资产删除
		if len(delIDS) != 0 {
			var body_bulk string
			for k, v := range delIDS {

				oldInfo, err := GetCorrInfo(v)
				if err != nil {
					logger.Error.Println(err)
					return err
				}
				t := time.Now().Format("2006-01-02 15:04:05")
				if len(oldInfo.AskRecord) > 0 {
					p := oldInfo.AskRecord[len(oldInfo.AskRecord)-1]
					p.HandlingOpinions = "您好，您的纠错申请已接收，感谢您对平台的关注和建议。"
					p.ProcessTime = t
					oldInfo.AskRecord = oldInfo.AskRecord[:len(oldInfo.AskRecord)-1]
					oldInfo.AskRecord = append(oldInfo.AskRecord, p)
				}
				bodys := gin.H{
					"recordid":         k,
					"processstatus":    2,
					"handlingopinions": "您好，您的纠错申请已接收，感谢您对平台的关注和建议。",
					"processtime":      t,
					"askrecord":        oldInfo.AskRecord,
				}
				err = esutil.UpdByIDWithRefresh(common.DataInteractiveCommunication, common.TbDataErrorCorrection, "true", v, bodys)
				if err != nil {
					logger.Error.Println(err)
					return err
				}

				//aduit_record表工单处理
				docbytes, _ := json.Marshal(gin.H{
					"doc": gin.H{
						"status":      8,
						"assetstatus": "已删除",
					},
				})
				body_bulk = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
					body_bulk, common.DataBaseAsset, common.AuditRecordESTB, k, string(docbytes))

			}
			body_bulk = fmt.Sprintf("%s\n", body_bulk)
			err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body_bulk)
			if err != nil {
				logger.Error.Printf("更新我的纠错数据失败:%s", err.Error())
				return err
			}

		}

		//资产下架
		if len(removeIDS) != 0 {
			var body_bulk string
			for k, v := range removeIDS {

				oldInfo, err := GetCorrInfo(v)
				if err != nil {
					logger.Error.Println(err)
					return err
				}
				t := time.Now().Format("2006-01-02 15:04:05")
				if len(oldInfo.AskRecord) > 0 {
					p := oldInfo.AskRecord[len(oldInfo.AskRecord)-1]
					p.HandlingOpinions = "您好，您的纠错申请已接收，感谢您对平台的关注和建议。"
					p.ProcessTime = t
					oldInfo.AskRecord = oldInfo.AskRecord[:len(oldInfo.AskRecord)-1]
					oldInfo.AskRecord = append(oldInfo.AskRecord, p)
				}
				bodys := gin.H{
					"recordid":         k,
					"processstatus":    2,
					"handlingopinions": "您好，您的纠错申请已接收，感谢您对平台的关注和建议。",
					"processtime":      t,
					"askrecord":        oldInfo.AskRecord,
					"oldstatus":        oldInfo.ProcessStatus,
				}
				err = esutil.UpdByIDWithRefresh(common.DataInteractiveCommunication, common.TbDataErrorCorrection, "true", v, bodys)
				if err != nil {
					logger.Error.Println(err)
					return err
				}

				//aduit_record表工单处理
				docbytes, _ := json.Marshal(gin.H{
					"doc": gin.H{
						"status":      8,
						"assetstatus": "已下架",
					},
				})
				body_bulk = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
					body_bulk, common.DataBaseAsset, common.AuditRecordESTB, k, string(docbytes))

			}
			body_bulk = fmt.Sprintf("%s\n", body_bulk)
			err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body_bulk)
			if err != nil {
				logger.Error.Printf("更新我的纠错数据失败:%s", err.Error())
				return err
			}
		}

		//资产重新上架
		if len(auditids) != 0 {
			var body_bulk string
			for k, v := range auditids {
				oldInfo, err := GetCorrInfo(v)
				if err != nil {
					logger.Error.Println(err)
					return err
				}
				t := time.Now().Format("2006-01-02 15:04:05")
				//if len(oldInfo.AskRecord) > 0 {
				//	p := oldInfo.AskRecord[len(oldInfo.AskRecord)-1]
				//	p.HandlingOpinions = "您好，您的纠错申请已接收，感谢您对平台的关注和建议。"
				//	p.ProcessTime = t
				//	oldInfo.AskRecord = oldInfo.AskRecord[:len(oldInfo.AskRecord)-1]
				//	oldInfo.AskRecord = append(oldInfo.AskRecord, p)
				//}
				bodys := gin.H{
					"recordid":         k,
					"processstatus":    oldInfo.OldStatus,
					"handlingopinions": "",
					"processtime":      t,
				}
				err = esutil.UpdByIDWithRefresh(common.DataInteractiveCommunication, common.TbDataErrorCorrection, "true", v, bodys)
				if err != nil {
					logger.Error.Println(err)
					return err
				}

				//aduit_record表工单处理
				docbytes, _ := json.Marshal(gin.H{
					"doc": gin.H{
						"status":      0,
						"assetstatus": "",
					},
				})
				body_bulk = fmt.Sprintf(`%s
								{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
								%s`,
					body_bulk, common.DataBaseAsset, common.AuditRecordESTB, k, string(docbytes))
			}

			body_bulk = fmt.Sprintf("%s\n", body_bulk)
			err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body_bulk)
			if err != nil {
				logger.Error.Printf("更新我的纠错数据失败:%s", err.Error())
				return err
			}
		}

		//资产权限处理
		//列举所有项目管理员，项目管理员的纠错权限变更时无需处理
		var managerids []string
		if projectID != "" {
			_, managerids, _ = CategoryGetDefaultEx(projectID)
		}

		if len(todo) != 0 { //待处理变更为已处理
			for _, v := range todo {

				oldInfo, err := GetCorrInfo(v)
				if err != nil {
					logger.Error.Println(err)
					return err
				}

				if len(putuserids) > 0 {
					if tools.IsInArr(oldInfo.UserID, managerids) {
						continue
					}
					if !tools.IsInArr(oldInfo.UserID, putuserids) {
						t := time.Now().Format("2006-01-02 15:04:05")
						if len(oldInfo.AskRecord) > 0 {
							p := oldInfo.AskRecord[len(oldInfo.AskRecord)-1]
							p.HandlingOpinions = "您好，您的纠错申请已接收，感谢您对平台的关注和建议。"
							p.ProcessTime = t
							oldInfo.AskRecord = oldInfo.AskRecord[:len(oldInfo.AskRecord)-1]
							oldInfo.AskRecord = append(oldInfo.AskRecord, p)
						}
						bodys := gin.H{
							"processstatus":    2,
							"handlingopinions": "您好，您的纠错申请已接收，感谢您对平台的关注和建议。",
							"processtime":      t,
							"askrecord":        oldInfo.AskRecord,
							"oldstatus":        oldInfo.ProcessStatus,
						}
						err = esutil.UpdByIDWithRefresh(common.DataInteractiveCommunication, common.TbDataErrorCorrection, "true", v, bodys)
						if err != nil {
							logger.Error.Println(err)
							return err
						}
					}
				}
			}
		}

		if len(done) != 0 { //已处理便更为待处理
			for _, v := range done {

				oldInfo, err := GetCorrInfo(v)
				if err != nil {
					logger.Error.Println(err)
					return err
				}

				if tools.IsInArr(oldInfo.UserID, managerids) {
					continue
				}

				if len(putuserids) == 0 || tools.IsInArr(oldInfo.UserID, putuserids) {
					t := time.Now().Format("2006-01-02 15:04:05")
					//if len(oldInfo.AskRecord) > 0 {
					//	p := oldInfo.AskRecord[len(oldInfo.AskRecord)-1]
					//	p.HandlingOpinions = "您好，您的纠错申请已接收，感谢您对平台的关注和建议。"
					//	p.ProcessTime = t
					//	oldInfo.AskRecord = oldInfo.AskRecord[:len(oldInfo.AskRecord)-1]
					//	oldInfo.AskRecord = append(oldInfo.AskRecord, p)
					//}
					bodys := gin.H{
						"processstatus":    oldInfo.OldStatus,
						"handlingopinions": "",
						"processtime":      t,
					}
					err = esutil.UpdByIDWithRefresh(common.DataInteractiveCommunication, common.TbDataErrorCorrection, "true", v, bodys)
					if err != nil {
						logger.Error.Println(err)
						return err
					}
				}
			}
		}

		//if len(perIDS) != 0 {
		//	for k, _ := range perIDS {
		//		docbytes, _ := json.Marshal(gin.H{
		//			"doc": gin.H{
		//				"putuserids": putuserids,
		//			},
		//		})
		//
		//		body_bulk = fmt.Sprintf(`%s
		//						{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
		//						%s`,
		//			body_bulk, common.DataBaseAsset, common.AuditRecordESTB, k, string(docbytes))
		//	}
		//}

		//var docbytes []byte
		//if statustype != "" {
		//	docbytes, _ = json.Marshal(gin.H{
		//		"doc": gin.H{
		//			"status":       1,
		//			"autocheck":    true,
		//			"assetstatus":  "已下架",
		//			"notdisplayed": true,
		//		},
		//	})
		//} else if len(putuserids) > 0 {
		//	docbytes, _ = json.Marshal(gin.H{
		//		"doc": gin.H{
		//			"status":     1,
		//			"autocheck":  true,
		//			"putuserids": putuserids,
		//		},
		//	})
		//}
		//body_bulk = fmt.Sprintf(`%s
		//						{"update":{"_index":"%s","_type":"%s", "_id": "%s","_retry_on_conflict" : 5}}
		//						%s`,
		//	body_bulk, common.DataBaseAsset, common.AuditRecordESTB, k, string(docbytes))
		//
		//body_bulk = fmt.Sprintf("%s\n", body_bulk)
		//err = esutil.UpdateBulk(common.DataBaseAsset, common.AuditRecordESTB, body_bulk)
		//if err != nil {
		//	logger.Error.Printf("更新我的纠错数据失败:%s", err.Error())
		//	return err
		//}

	}

	return nil
}

func GetIsAutoCheck(projectid string) (isauto bool, err error) {
	//资产编目的类型是2
	p := projectid + "_2"
	fmt.Println(p)
	strInfo, err := serveresclient.NewESClientCl.SearchByID("danastudio-asset", "audit_manager", p)
	if err != nil {
		AuditManagerConfInit(projectid, 2)
		return GetIsAutoCheck(projectid)
	}
	fmt.Println(string(strInfo))
	isauto = gjson.Get(string(strInfo), "autocheck").Bool()
	fmt.Println("检测提交状态为:", isauto)
	return isauto, nil
}

func GetALLIsAutoCheck(projectid string, sign int) (isauto bool, err error) {
	//FWFB_Type = 1 //服务发布    对应申请来源：数据服务
	//ZCBM_Type = 2 //资产编目    对应申请来源：资产编目
	//ZCGZ_Type = 3 //资产挂载    对应申请来源：资产挂载
	//ZCFB_Type = 4 //资产发布    对应申请来源：资产发布
	//ZCSY_Type = 5 //资产使用    对应申请来源：数据门户
	//SJXQ_Type = 6 //数据需求    对应申请来源：数据需求
	//SJJC_Type = 7 //数据纠错    对应申请来源：数据纠错
	p := projectid + "_" + strconv.Itoa(sign)
	fmt.Println(p)
	strInfo, err := serveresclient.NewESClientCl.SearchByID("danastudio-asset", "audit_manager", p)
	if err != nil {
		AuditManagerConfInit(projectid, sign)
		return GetIsAutoCheck(projectid)
	}
	fmt.Println(string(strInfo))
	isauto = gjson.Get(string(strInfo), "autocheck").Bool()
	fmt.Println("检测提交状态为:", isauto)
	return isauto, nil
}

func AuditManagerConfInit(projectID string, ServiceType int) {
	nowTime := time.Now().Format("2006-01-02 15:04:05")
	//目前有五个服务需要配置默认审核信息,新增两个审核配置
	var info AuditManager
	info.ProjectID = projectID
	info.Type = ServiceType
	info.ID = fmt.Sprintf("%s_%d", info.ProjectID, info.Type)
	info.CreateTime = nowTime
	info.AutoCheck = true
	esutil.AddWithAimID("danastudio-asset", "audit_manager", info.ID, info)
}

func GetIsAutoCheck_Req(projectid string) (isauto bool, err error) {
	//数据需求
	p := projectid + "_6"
	fmt.Println(p)
	strInfo, err := serveresclient.NewESClientCl.SearchByID("danastudio-asset", "audit_manager", p)
	if err != nil {
		logger.Error.Printf("查询审核状态失败:%s", err.Error())
		return isauto, err
	}
	fmt.Println(string(strInfo))
	isauto = gjson.Get(string(strInfo), "autocheck").Bool()
	fmt.Println("检测提交状态为:", isauto)
	return isauto, nil
}

func GetIsAutoCheck_Corr(projectid string) (isauto bool, err error) {
	//数据纠错
	p := projectid + "_7"
	fmt.Println(p)
	strInfo, err := serveresclient.NewESClientCl.SearchByID("danastudio-asset", "audit_manager", p)
	if err != nil {
		logger.Error.Printf("查询审核状态失败:%s", err.Error())
		return isauto, err
	}
	fmt.Println(string(strInfo))
	isauto = gjson.Get(string(strInfo), "autocheck").Bool()
	fmt.Println("检测提交状态为:", isauto)
	return isauto, nil
}

func InventoryDelete(id string, m map[string]AssetInventory) error {
	err := serveresclient.NewESClientCl.DelByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetInventory, "true", id)
	if err != nil {
		logger.Error.Printf("资产编目信息删除失败:%s", err.Error())
		return err
	}

	item := m[id]
	item.DeleteTime = time.Now().Format("2006-01-02 15:04:05")
	err = serveresclient.NewESClientCl.AddWithAimID(common.DatabaseOPdata, common.TbInventoryGrave, id, item)
	if err != nil {
		logger.Error.Printf("资产删除记录添加失败:%s", err.Error())
	}
	return nil
}

func InventoryUpdateBody(id string, doc interface{}) error {
	err := serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetInventory, common.RefreshWait, id, doc)
	if err != nil {
		logger.Error.Printf("编目信息状态更新至es失败:%s", err.Error())
		return err
	}

	return nil
}

func RqeUpdateBody(id string, doc interface{}) error {
	err := serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataInteractiveCommunication, common.TbDataRequirements, common.RefreshWait, id, doc)
	if err != nil {
		logger.Error.Printf("编目信息状态更新至es失败:%s", err.Error())
		return err
	}

	return nil
}
func CorrUpdateBody(id string, doc interface{}) error {
	err := serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataInteractiveCommunication, common.TbDataErrorCorrection, common.RefreshWait, id, doc)
	if err != nil {
		logger.Error.Printf("编目信息状态更新至es失败:%s", err.Error())
		return err
	}

	return nil
}

func InventoryList(sp AssetMeta, ids []string) (res []gin.H, total int, err error) {
	var res1, res2, res3 []gin.H
	var r AssetInventoryHits
	var body interface{}
	var terms []interface{}
	var sort []map[string]interface{}
	var filters []map[string]interface{}
	var run gin.H
	var restemp []gin.H
	sp.Match = tools.ForElasticSearch(sp.Match)

	if sp.Page == 0 {
		sp.Page = 1
	}
	if sp.Perpage == 0 {
		sp.Perpage = 100000
	}
	var isFuzzy bool
	if sp.Sort.SortField == "" {
		sp.Sort.SortField = "updatedtime"
		isFuzzy = true
	}
	if sp.Sort.SortType == "asc" {
		run = gin.H{
			sp.Sort.SortField: gin.H{
				"order": "asc",
			},
		}
	} else {
		run = gin.H{
			sp.Sort.SortField: gin.H{
				"order": "desc",
			},
		}
	}

	sort = append(sort, run)

	term1 := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	term2 := gin.H{
		"terms": gin.H{
			"inventorycategoryid": ids,
		},
	}

	if len(sp.AssetMetaFilter.AssetType) != 0 {
		term3 := gin.H{
			"terms": gin.H{
				"assetcatalog.basicinfo.assettype": sp.AssetMetaFilter.AssetType,
			},
		}
		filters = append(filters, term3)
	}
	if len(sp.AssetMetaFilter.SharedMode) != 0 {
		term3 := gin.H{
			"terms": gin.H{
				"assetcatalog.sharedinfo.sharedmode": sp.AssetMetaFilter.SharedMode,
			},
		}
		filters = append(filters, term3)
	}
	if len(sp.AssetMetaFilter.SharedType) != 0 {
		term3 := gin.H{
			"terms": gin.H{
				"assetcatalog.sharedinfo.sharedtype": sp.AssetMetaFilter.SharedType,
			},
		}
		filters = append(filters, term3)
	}
	if len(sp.AssetMetaFilter.Status) != 0 {
		term3 := gin.H{
			"terms": gin.H{
				"status": sp.AssetMetaFilter.Status,
			},
		}
		filters = append(filters, term3)
	}

	var should []map[string]interface{}
	if toolscommon.ESedition == 1 {
		s1 := gin.H{
			"query_string": gin.H{
				"fields": []string{"inventoryname^100",
					"inventorycategory",
					"assetcatalog.basicinfo.assetcode", "inventorycategoryencodingpath", "inventorypath"},
				"query":                    "*" + sp.Match + "*",
				"default_operator":         "or",
				"lowercase_expanded_terms": true,
			},
		}
		s2 := gin.H{
			"multi_match": gin.H{
				"query": sp.Match,
				"fields": []string{
					"inventoryname.text_ik",
				},
				"boost": 1,
			},
		}
		s3 := gin.H{
			"multi_match": gin.H{
				"query": sp.Match,
				"fields": []string{
					"inventorycategory.text_ik",
					"assetcatalog.basicinfo.assetcode.text_ik",
					"inventorypath.text_ik",
					"inventorycategoryencodingpath.text_ik"},
				"boost": 0.5,
			},
		}
		s4 := gin.H{
			"query_string": gin.H{
				"fields": []string{
					"assetcatalog.custominfo.customproperties.provalue",
					"assetcatalog.custominfo.customproperties.provalues",
					"assetcatalog.custominfo.customproperties.mulitiselected"},
				"query": "*" + sp.Match + "*",
			},
		}
		s5 := gin.H{
			"multi_match": gin.H{
				"query": sp.Match,
				"fields": []string{
					"assetcatalog.custominfo.customproperties.provalue.text_ik",
					"assetcatalog.custominfo.customproperties.provalues.text_ik",
					"assetcatalog.custominfo.customproperties.mulitiselected.text_ik"},
				"boost": 0.5,
			},
		}

		s6 := gin.H{
			"nested": gin.H{
				"path": "assetcatalog.custominfo.customproperties",
				"query": gin.H{
					"bool": gin.H{
						"should":               []gin.H{s4, s5},
						"minimum_should_match": 1,
					},
				},
			},
		}

		should = append(should, s1, s2, s3, s6)
		if sp.InventoryCategoryID == "" {
			terms = append(terms, term1)
		} else if sp.InventoryCategoryID != "" {
			terms = append(terms, term1, term2)
		}

	} else {
		sp.Match = commontools.SpecialCharacterAdaptation(sp.Match)
		match := gin.H{
			"query_string": gin.H{
				"query":            "*" + sp.Match + "*",
				"fields":           []string{"assetcatalog.basicinfo.assetname", "assetcatalog.basicinfo.assetcode"},
				"default_operator": "or",
			},
		}

		if sp.InventoryCategoryID == "" && sp.Match == "" {
			terms = append(terms, term1)
		} else if sp.InventoryCategoryID == "" && sp.Match != "" {
			terms = append(terms, term1, match)
		} else if sp.InventoryCategoryID != "" && sp.Match == "" {
			terms = append(terms, term1, term2)
		} else if sp.InventoryCategoryID != "" && sp.Match != "" {
			terms = append(terms, term1, term2, match)
		}
	}

	if len(filters) > 0 {
		terms = append(terms, filters)
	}

	if sp.StartTime != "" && sp.EndTime != "" {
		rangeT := gin.H{
			"range": gin.H{
				"createdtime": gin.H{
					"gte": sp.StartTime,
					"lte": sp.EndTime,
				},
			},
		}
		terms = append(terms, rangeT)
	}
	if sp.NowTime != "" {
		rangeT := gin.H{
			"range": gin.H{
				"createdtime": gin.H{
					"gte": sp.NowTime,
					"lte": time.Now().Format("2006-01-02 15:04:05"),
				},
			},
		}
		terms = append(terms, rangeT)
	}

	body = gin.H{
		"sort": sort,
		"size": common.UnlimitSize,
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
	}
	if toolscommon.ESedition == 1 && sp.Match != "" {
		if isFuzzy {
			sort = []map[string]interface{}{}
		}
		body = gin.H{
			"size": common.UnlimitSize,
			"sort": sort,
			"query": gin.H{
				"bool": gin.H{
					"must":                 terms,
					"should":               should,
					"minimum_should_match": 1,
				},
			},
		}
	}
	byteBody, _ := json.Marshal(body)
	fmt.Println(string(byteBody))
	logger.Info.Println("string(byteBody):", string(byteBody))
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return res, total, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, total, err
	}
	//获取类目信息
	categoryList, err := CategoryInfoList(sp.ProjectID)
	if err != nil {
		logger.Error.Printf("获取类目信息失败:%s", err.Error())
		return res, total, err
	}

	//待提交第一，已驳回第二，然后更新倒序
	for _, v := range r.Hits1.Hits2 {
		//添加编目路径
		GetInventorypath(&v.AssetInventory, categoryList)

		if v.AssetInventory.AttachInfo.AttachStatus == "" {
			v.AssetInventory.AttachInfo.AttachStatus = "待挂载"
		}
		for k, vv := range v.AssetInventory.AssetCatalog.CustomInfo {
			if len(vv.CustomProperties) == 0 {
				v.AssetInventory.AssetCatalog.CustomInfo[k].CustomProperties = []Properties{}
			}
		}

		rr := gin.H{
			"inventoryid":                   v.AssetInventory.InventoryID,
			"inventoryname":                 v.AssetInventory.InventoryName,
			"inventorycategory":             v.AssetInventory.InventoryCategory,
			"inventorycategoryid":           v.AssetInventory.InventoryCategoryID,
			"inventorycategoryencoding":     v.AssetInventory.InventoryCategoryEncoding,
			"assetcode":                     v.AssetInventory.AssetCatalog.BasicInfo.AssetCode,
			"assettype":                     v.AssetInventory.AssetCatalog.BasicInfo.AssetType,
			"sharedtype":                    v.AssetInventory.AssetCatalog.SharedInfo.SharedType,
			"sharedmode":                    v.AssetInventory.AssetCatalog.SharedInfo.SharedMode,
			"createdtime":                   v.AssetInventory.CreatedTime,
			"updatedtime":                   v.AssetInventory.UpdatedTime,
			"status":                        v.AssetInventory.Status,
			"attachstatus":                  v.AssetInventory.AttachInfo.AttachStatus,
			"custominfo":                    v.AssetInventory.AssetCatalog.CustomInfo,
			"inventorypath":                 v.AssetInventory.InventoryPath,
			"auditstatus":                   v.AssetInventory.AuditStatus,
			"inventorycategoryencodingpath": v.AssetInventory.InventoryCategoryEncodingPath,
		}
		if !strings.Contains(v.AssetInventory.InventoryID, "change") {
			restemp = append(restemp, rr)
			switch v.AssetInventory.Status {
			case 1:
				res1 = append(res1, rr)
			case 4:
				res2 = append(res2, rr)
			default:
				res3 = append(res3, rr)
			}
		}
	}
	res = append(res, res1...)
	fmt.Println(len(res))
	res = append(res, res2...)
	fmt.Println(len(res))
	res = append(res, res3...)
	fmt.Println(len(res))

	total = len(res)

	if sp.Sort.SortType != "" {
		res = restemp
		total = len(res)
	}

	//排序后再分页
	if (sp.Page-1)*sp.Perpage >= len(res) {
		return []gin.H{}, total, nil
	} else if (sp.Page-1)*sp.Perpage < len(res) && len(res) <= sp.Page*sp.Perpage {
		return res[(sp.Page-1)*sp.Perpage : len(res)], total, nil
	} else if len(res) > sp.Page*sp.Perpage {
		return res[(sp.Page-1)*sp.Perpage : sp.Page*sp.Perpage], total, nil
	} else {
		return res, total, fmt.Errorf("分页错误")
	}
}

func InventoryDetail(sp AssetInventory) (res AssetInventory, err error) {
	var r AssetInventoryHits
	var body interface{}
	//users, err := user.GetAllUserWithProject(sp.ProjectID)
	//if err != nil {
	//	logger.Error.Println(err)
	//	return res, err
	//}

	var terms []interface{}
	term1 := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"inventoryid": sp.InventoryID,
		},
	}
	terms = append(terms, term1, term2)
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
	}

	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	if len(r.Hits1.Hits2) == 0 {
		return res, fmt.Errorf("查询结果为空")
	}
	for _, v := range r.Hits1.Hits2 {
		res = v.AssetInventory
	}
	//获取类目信息
	categoryList, err := CategoryInfoList(sp.ProjectID)
	if err != nil {
		logger.Error.Printf("获取类目信息错误:%s", err.Error())
		return res, err
	}
	//添加编目路径
	GetInventorypath(&res, categoryList)

	for k, v := range res.AssetCatalog.CustomInfo {
		if len(v.CustomProperties) == 0 {
			res.AssetCatalog.CustomInfo[k].CustomProperties = []Properties{}
		}
	}
	_, userMap, err := user.GetUserInfo([]string{res.UserID})
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	if _, ok := userMap[res.UserID]; ok {
		res.NickName = userMap[res.UserID].NickName
		res.UserDepartment = userMap[res.UserID].UserDepartment
	}
	return res, nil
}

func InventoryGetAssetCode(sp AssetInventory) (str string, err error) {
	var r AssetInventoryHits
	var body interface{}

	var terms []interface{}
	term1 := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"assetcodedate": time.Now().Format("20060102"),
		},
	}
	terms = append(terms, term1, term2)
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 10000,
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产目录模板失败:%s", err.Error())
		return str, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return str, err
	}
	timeMap := make(map[string]string)
	for _, v := range r.Hits1.Hits2 {
		timeMap[v.AssetInventory.AssetCodeNum] = v.AssetInventory.AssetCodeNum
	}
	for i := 1; i <= 9999; i++ {
		if i < 10 {
			str = fmt.Sprintf("000%d", i)
		} else if i >= 10 && i < 100 {
			str = fmt.Sprintf("00%d", i)
		} else if i >= 100 && i < 999 {
			str = fmt.Sprintf("0%d", i)
		} else {
			str = fmt.Sprintf("%d", i)
		}
		if _, ok := timeMap[str]; !ok {
			break
		}
		if i == 9999 {
			return str, fmt.Errorf("随机数已满")
		}
	}
	str = fmt.Sprintf(`%s%s`, time.Now().Format("20060102"), str)
	return str, nil
}

func FetchFixedValues(sp *AssetInventory) {
	for k1, v1 := range sp.AssetCatalog.CustomInfo {
		if v1.InfoSign == "JBXXFL" {
			for _, v2 := range v1.CustomProperties {
				if len(v2.MulitiSelected) == 1 && v2.ProValue == "" {
					for _, k := range v2.MulitiEnumRated {
						if k.ID == v2.MulitiSelected[0] {
							v2.ProValue = k.Value
						}
					}
				}
				switch v2.ProSign {
				case "ZCMC":
					sp.AssetCatalog.BasicInfo.AssetName = v2.ProValue
					sp.InventoryName = v2.ProValue
				case "ZCLM":
					sp.AssetCatalog.BasicInfo.AssetCategory = v2.ProValue
					if len(v2.MulitiSelected) == 1 {
						sp.InventoryCategoryID = v2.MulitiSelected[0]
					}
					categoryInfo, err := CategoryInfoDetail(sp.InventoryCategoryID)
					if err != nil {
						logger.Error.Println("资产编目所属类目id查询名称失败，id:", sp.InventoryCategoryID)
					}
					sp.InventoryCategory = categoryInfo.CategoryName
					sp.InventoryCategoryEncoding = categoryInfo.CategoryEncoding
				case "ZCLX":
					sp.AssetCatalog.BasicInfo.AssetType = v2.ProValue
				case "ZCBM":
					sp.AssetCatalog.BasicInfo.AssetCode = v2.ProValue
				case "ZYJJ":
					sp.AssetCatalog.BasicInfo.AssetIntroduction = v2.ProValue
				case "DATA_PROVIDER":
					sp.AssetCatalog.BasicInfo.DATAPROVIDER = v2.ProValue
				case "DATA_SOURCE_SYSTEM":
					sp.AssetCatalog.BasicInfo.DATASOURCESYSTEM = v2.ProValue
				case "DATA_AREA":
					var fact func(pro Properties, num int, id string) []string
					fmt.Println("开始执行")
					var valuefact []string
					fact = func(pro Properties, num int, id string) []string {
						var isT bool
						fmt.Println("values:", valuefact)
						fmt.Println("num", num)
						for _, k := range pro.MulitiEnumRated {
							if k.ID == id {
								fmt.Println("GETGET")
								isT = true
								valuefact = append(valuefact, k.Value)
								id = k.FatherID
							}

						}
						if !isT || num > 5 {
							return valuefact
						}
						return fact(pro, num+1, id)
					}
					if len(v2.MulitiSelected) == 1 {
						values := fact(v2, 1, v2.MulitiSelected[0])
						fmt.Println("values222:", values)
						sp.AssetCatalog.BasicInfo.DATAAREAS = []string{}
						for i := 0; i < len(values); i++ {
							sp.AssetCatalog.BasicInfo.DATAAREAS = append(sp.AssetCatalog.BasicInfo.DATAAREAS, values[len(values)-1-i])
						}
					}
				}
			}
		}
		if v1.InfoSign == "GXXXFL" {
			for _, v2 := range v1.CustomProperties {
				var tmpValues []string
				if len(v2.MulitiSelected) > 0 {
					for _, k := range v2.MulitiEnumRated {
						for _, s := range v2.MulitiSelected {
							if k.ID == s {
								v2.ProValue = k.Value
								tmpValues = append(tmpValues, k.Value)
							}
						}
					}
				}
				switch v2.ProSign {
				case "GXLX":
					sp.AssetCatalog.SharedInfo.SharedType = v2.ProValue
				case "GXTJ":
					sp.AssetCatalog.SharedInfo.SharedCondition = v2.ProValue
				case "GXFS":
					sp.AssetCatalog.SharedInfo.SharedMode = tmpValues
				}
			}
		}

		if v1.InfoSign == "KFXXFL" {
			for _, v2 := range v1.CustomProperties {
				var tmpValues []string

				if len(v2.MulitiSelected) > 0 {
					for _, k := range v2.MulitiEnumRated {
						for _, s := range v2.MulitiSelected {
							if k.ID == s {
								v2.ProValue = k.Value
								tmpValues = append(tmpValues, k.Value)
							}
						}
					}
				}

				switch v2.ProSign {
				case "KFLX":
					sp.AssetCatalog.OpenInfo.OpenType = v2.ProValue
				case "KFFS":
					sp.AssetCatalog.OpenInfo.OpenWay = tmpValues
				case "KFTJ":
					sp.AssetCatalog.OpenInfo.OpenConditions = v2.ProValue
				}
			}
		}

		for k2, v2 := range v1.CustomProperties {
			var tmpValues []string
			if len(v2.MulitiSelected) > 0 {
				for _, k := range v2.MulitiEnumRated {
					for _, s := range v2.MulitiSelected {
						if k.ID == s {
							v2.ProValue = k.Value
							tmpValues = append(tmpValues, k.Value)
						}
					}
				}
				sp.AssetCatalog.CustomInfo[k1].CustomProperties[k2].ProValues = tmpValues
			}

		}

	}
}

func IsCode(str string) bool {
	if len(str) != 12 {
		return false
	} else if str[0:8] != time.Now().Format("20060102") {
		return false
	} else {
		s, err := strconv.Atoi(str[8:12])
		if err != nil {
			return false
		} else if s >= 0 && s <= 9999 {
			return true
		} else {
			return false
		}
	}
}

func CallAuditCenter(sp AssetInventory, typenum int, sourcetype int, tokenStr string) (recordid string, err error) {
	//调用审核中心接口

	var objectType int
	switch sp.AssetCatalog.BasicInfo.AssetType {
	case "数据库":
		objectType = common.AttachTableSign
	case "数据文件":
		objectType = common.AttachFileSign
	case "数据指标":
		objectType = common.AttachMetricSign
	case "API", "第三方API":
		objectType = common.AttachTPAPISign
	case "消息流":
		objectType = common.AttachRealMessageSign
	}
	var body = gin.H{
		"objectid":   sp.InventoryID,
		"objectname": sp.InventoryName,
		"objecttype": objectType,
		"type":       typenum,
		"sourcetype": sourcetype,
	}
	bodybytes, _ := json.Marshal(body)
	fmt.Println("----------------------request body:", string(bodybytes))
	response, err := httpclient.PostAuthorization("127.0.0.1", common.AssetPort, "/danastudio/audit/addrecord", string(bodybytes), tokenStr)
	fmt.Println("----------------------response:", string(response))
	if err != nil {
		logger.Error.Println(err)
		return recordid, err
	}
	recordid = gjson.Get(string(response), "result.recordid").String()
	if recordid == "" {
		return recordid, fmt.Errorf("审核工单ID为空")
	}

	return recordid, nil
}
func CallAuditCenter_Req(sp DataRequirements, typenum int, sourcetype int, tokenStr string) (recordid string, err error) {
	//调用审核中心接口
	var body = gin.H{
		"objectid":   sp.ReqID,
		"objectname": sp.ReqName,
		"type":       typenum,
		"sourcetype": sourcetype,
	}
	bodybytes, _ := json.Marshal(body)
	fmt.Println("----------------------request body:", string(bodybytes))
	response, err := httpclient.PostAuthorization("127.0.0.1", common.AssetPort, "/danastudio/audit/addrecord", string(bodybytes), tokenStr)
	fmt.Println("----------------------response:", string(response))
	if err != nil {
		logger.Error.Println(err)
		return recordid, err
	}
	recordid = gjson.Get(string(response), "result.recordid").String()
	if recordid == "" {
		return recordid, fmt.Errorf("审核工单ID为空")
	}

	return recordid, nil
}
func CallAuditCenter_Corr(sp DataErrorCorrection, typenum int, sourcetype int, tokenStr string) (recordid string, err error) {
	//调用审核中心接口
	var body = gin.H{
		"objectid":   sp.CorrID,
		"objectname": sp.CorrName,
		"assetid":    sp.AssetID,
		"type":       typenum,
		"sourcetype": sourcetype,
	}
	bodybytes, _ := json.Marshal(body)
	fmt.Println("----------------------request body:", string(bodybytes))
	response, err := httpclient.PostAuthorization("127.0.0.1", common.AssetPort, "/danastudio/audit/addrecord", string(bodybytes), tokenStr)
	fmt.Println("----------------------response:", string(response))
	if err != nil {
		logger.Error.Println(err)
		return recordid, err
	}
	recordid = gjson.Get(string(response), "result.recordid").String()
	if recordid == "" {
		return recordid, fmt.Errorf("审核工单ID为空")
	}

	return recordid, nil
}
func CallAuditCenterRecall(id string, tokenStr string) error {
	//调用审核中心接口
	var body = gin.H{
		"id": id,
	}
	bodybytes, _ := json.Marshal(body)
	fmt.Println("----------------------request body:", string(bodybytes))
	response, err := httpclient.PostAuthorization("127.0.0.1", common.AssetPort, "/danastudio/audit/recall", string(bodybytes), tokenStr)
	fmt.Println("----------------------response:", string(response))
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}
func GetInventorypath(sp *AssetInventory, categoryList []AssetCategory) {

	type s struct {
		CategoryIDMerge  CategoryIDMerge `json:"categoryidmerge"`
		CategoryName     string          `json:"categoryname"`     //类目名称
		CategoryEncoding string          `json:"categoryencoding"` //类目编码
	}
	categoryInfo := make(map[string]s)

	for _, v := range categoryList {
		var cate2 s
		cate2.CategoryIDMerge = v.CategoryIDMerge
		cate2.CategoryName = v.CategoryName
		cate2.CategoryEncoding = v.CategoryEncoding
		categoryInfo[v.CategoryID] = cate2
	}
	//获取路径
	var path, epath string
	if _, ok := categoryInfo[sp.InventoryCategoryID]; ok {
		if categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID1 != "" {
			if _, ok := categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID1]; ok {
				path = categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID1].CategoryName
				epath = categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID1].CategoryEncoding
			}
		}
		if categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID2 != "" {
			if _, ok := categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID2]; ok {
				path = path + "/" + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID2].CategoryName
				epath = epath + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID2].CategoryEncoding
			}
		}
		if categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID3 != "" {
			if _, ok := categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID3]; ok {
				path = path + "/" + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID3].CategoryName
				epath = epath + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID3].CategoryEncoding
			}
		}
		if categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID4 != "" {
			if _, ok := categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID4]; ok {
				path = path + "/" + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID4].CategoryName
				epath = epath + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID4].CategoryEncoding
			}
		}
		if categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID5 != "" {
			if _, ok := categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID5]; ok {
				path = path + "/" + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID5].CategoryName
				epath = epath + categoryInfo[categoryInfo[sp.InventoryCategoryID].CategoryIDMerge.CategoryID5].CategoryEncoding
			}
		}

	}
	sp.InventoryPath = path
	sp.InventoryCategoryEncodingPath = epath

}

// 是否有启用的资产模板
func CheckUsingCatalog(projectID string) (bool, error) {
	body := gin.H{
		"projectid": projectID,
		"status":    1,
	}
	resByte, err := ItemQuery(common.DataBaseModelAsset, common.TbAssetCatalog, body)
	if err != nil {
		logger.Error.Println(err)
		return false, err
	}
	count := commontools.GetTotal(resByte)
	if err != nil {
		logger.Error.Println(err)
		return false, err
	}
	return count > 0, nil
}

// --562
func DataReqSave(sp DataRequirements) error {

	userStr, err := serveresclient.NewESClientCl.SearchByID(common.DataBaseAuth, common.UserESTB, sp.UserID)
	if err != nil {
		logger.Error.Printf("查询用户失败:%s", err.Error())
		return err
	}
	sp.UserDepartment = gjson.Get(userStr, "userdepartment").String()

	err = serveresclient.NewESClientCl.AddWithAimID(common.DataInteractiveCommunication, common.TbDataRequirements, sp.ReqID, sp)
	if err != nil {
		logger.Error.Printf("添加数据需求失败:%s", err.Error())
		return err
	}

	return nil
}
func DataCorrSave(sp DataErrorCorrection) error {
	var err error

	err = func() error {
		switch sp.CorrType {
		case 0:
			if sp.AssetID != "" {
				//补充资产属性
				assetStr, err := serveresclient.NewESClientCl.SearchByID(common.DataBaseModelAsset, common.TbAssetInventory, sp.AssetID)
				if err != nil {
					logger.Error.Printf("查询资产失败:%s", err.Error())
					return err
				}
				var assetInfo AssetInventory
				err = json.Unmarshal([]byte(assetStr), &assetInfo)
				if err != nil {
					logger.Error.Printf("查询资产失败:%s", err.Error())
					return err
				}
				sp.DataProvider = assetInfo.DataProvider
				sp.DataSourceSystem = assetInfo.DataSourceSystem

			}
		case 1: //我的数据-纠错，没有assetid，用 assettbname
			if sp.AssetTBName != "" {
				sourcedbList, err := DateConfGet(sp.ProjectID)
				if err != nil {
					logger.Error.Printf("查询模板失败:%s", err.Error())
					return err
				}
				//查 sourceid 和 tbname 符合的表
				sourceTBID, err := getSourcetb(sourcedbList.ID, sp.AssetTBName)
				if err != nil {
					logger.Error.Printf("查询资源表失败:%s", err.Error())
					return err
				}
				if sourceTBID == "" {
					logger.Error.Println("查询资源表为空")
					return nil
				}
				assetInfo, err := getAssetTB(sourceTBID)
				if err != nil {
					logger.Error.Printf("查询资产表失败:%s", err.Error())
					return nil
				}
				if assetInfo.InventoryID != "" {
					sp.AssetID = assetInfo.InventoryID
					sp.DataProvider = assetInfo.DataProvider
					sp.DataSourceSystem = assetInfo.DataSourceSystem
				}
			}
		default:
			err = fmt.Errorf("CorrType 错误: %d", sp.CorrType)
			return err
		}
		return nil
	}()

	if err != nil {
		logger.Error.Printf("查询资产属性失败:%s", err.Error())
		return err
	}

	userStr, err := serveresclient.NewESClientCl.SearchByID(common.DataBaseAuth, common.UserESTB, sp.UserID)
	if err != nil {
		logger.Error.Printf("查询用户失败:%s", err.Error())
		return err
	}
	sp.UserDepartment = gjson.Get(userStr, "userdepartment").String()

	err = serveresclient.NewESClientCl.AddWithAimID(common.DataInteractiveCommunication, common.TbDataErrorCorrection, sp.CorrID, sp)
	if err != nil {
		logger.Error.Printf("添加数据需求失败:%s", err.Error())
		return err
	}

	return nil
}

func DataReqList(sp DataSearchNew) (res []DataRequirements, total int, err error) {
	sp.DataType = 1

	body := GetDataBody(sp)

	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataInteractiveCommunication, common.TbDataRequirements, body)
	if err != nil {
		logger.Error.Printf("查询数据需求失败:%s", err.Error())
		return res, 0, err
	}
	var response map[string]interface{}
	err = json.Unmarshal([]byte(strInfo), &response)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, 0, err
	}
	if response["hits"] != nil {
		hits := response["hits"].(map[string]interface{})["hits"].([]interface{})
		for _, hit := range hits {
			sourceRes := hit.(map[string]interface{})["_source"]
			var r DataRequirements
			b, err := json.Marshal(sourceRes)
			if err != nil {
				return res, 0, err
			}
			err = json.Unmarshal(b, &r)
			if err != nil {
				return res, 0, err
			}
			res = append(res, r)
		}
	}

	total, err = GetDataNum(sp, 1)
	if err != nil {
		logger.Error.Println("查询total数据错误：", err)
		return res, 0, err
	}

	return res, total, nil
}

func DataCorrList(sp DataSearchNew) (res []DataErrorCorrection, total int, err error) {
	sp.DataType = 2

	body := GetDataBody(sp)

	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataInteractiveCommunication, common.TbDataErrorCorrection, body)
	if err != nil {
		logger.Error.Printf("查询数据纠错失败:%s", err.Error())
		return res, 0, err
	}
	var response map[string]interface{}
	err = json.Unmarshal([]byte(strInfo), &response)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, 0, err
	}

	if response["hits"] != nil {
		hits := response["hits"].(map[string]interface{})["hits"].([]interface{})
		for _, hit := range hits {
			sourceRes := hit.(map[string]interface{})["_source"]
			var r DataErrorCorrection
			b, err := json.Marshal(sourceRes)
			if err != nil {
				return res, 0, err
			}
			err = json.Unmarshal(b, &r)
			if err != nil {
				return res, 0, err
			}
			res = append(res, r)
		}
	}
	total, err = GetDataNum(sp, 2)
	if err != nil {
		logger.Error.Println("查询total数据错误：", err)
		return res, 0, err
	}

	return res, total, nil
}
func GetCorrInfo(id string) (res DataErrorCorrection, err error) {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"corrid": id,
					},
				},
			},
		},
	}
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataInteractiveCommunication, common.TbDataErrorCorrection, body)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Printf("查询数据纠错失败:%s", err.Error())
		}
		return res, err
	}
	var response map[string]interface{}
	err = json.Unmarshal([]byte(strInfo), &response)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}

	if response["hits"] != nil {
		hits := response["hits"].(map[string]interface{})["hits"].([]interface{})
		for _, hit := range hits {
			sourceRes := hit.(map[string]interface{})["_source"]
			b, err := json.Marshal(sourceRes)
			if err != nil {
				return res, err
			}
			err = json.Unmarshal(b, &res)
			if err != nil {
				return res, err
			}
			break
		}
	}
	return res, nil
}
func GetDataNum(sp DataSearchNew, datatype int) (total int, err error) {

	var terms []map[string]interface{}
	term := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	terms = append(terms, term)

	check := gin.H{
		"terms": gin.H{
			"processstatus": []int{2, 3},
		},
	}
	terms = append(terms, check)

	var rangeT map[string]interface{}
	if sp.StartTime != "" && sp.EndTime != "" {
		rangeT = gin.H{
			"range": gin.H{
				"processtime": gin.H{
					"gte": sp.StartTime,
					"lte": sp.EndTime,
				},
			},
		}
		terms = append(terms, rangeT)
	}
	var should []map[string]interface{}
	if sp.FiterSets.Match != "" {

		if toolscommon.ESedition == 1 {
			switch sp.DataType {
			case 1:
				s1 := gin.H{
					"query_string": gin.H{
						"fields": []string{"reqname^10",
							"reqdesc",
							"reqresponse",
							"userdepartment",
							"username"},
						"query":                    "*" + sp.FiterSets.Match + "*",
						"default_operator":         "or",
						"lowercase_expanded_terms": true,
					},
				}
				s2 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"reqname.text_ik",
						},
						"boost": 1,
					},
				}
				s3 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"reqdesc.text_ik",
							"reqresponse.text_ik",
							"userdepartment.text_ik",
							"username.text_ik"},
						"boost": 0.5,
					},
				}
				should = append(should, s1, s2, s3)
			case 2:
				s1 := gin.H{
					"query_string": gin.H{
						"fields":                   []string{"corrname^10", "assetname^10"},
						"query":                    "*" + sp.FiterSets.Match + "*",
						"default_operator":         "or",
						"lowercase_expanded_terms": true,
					},
				}
				s2 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"corrname.text_ik",
							"assetname.text_ik",
						},
						"boost": 1,
					},
				}
				s3 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"dataproviderlist.text_ik",
							"userdepartment.text_ik",
							"corrtype.text_ik",
							"handlingopinions.text_ik",
							"corrdesc.text_ik"},
						"boost": 0.5,
					},
				}
				should = append(should, s1, s2, s3)
			}

		} else {
			switch sp.DataType {
			case 1:
				sp.FiterSets.Fields = []string{"reqname", "reqdesc"}
			case 2:
				sp.FiterSets.Fields = []string{"corrname", "corrdesc", "assetname", "askrecord.corrdesc"}
			}
			match := gin.H{
				"query_string": gin.H{
					"query":                    "*" + sp.FiterSets.Match + "*",
					"fields":                   sp.FiterSets.Fields,
					"default_operator":         "or",
					"lowercase_expanded_terms": true,
				},
			}
			terms = append(terms, match)
		}

	}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": common.UnlimitSize,
	}
	if toolscommon.ESedition == 1 && sp.FiterSets.Match != "" {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must":                 terms,
					"should":               should,
					"minimum_should_match": 1,
				},
			},
			"size": common.UnlimitSize,
		}
	}

	b, _ := json.Marshal(body)
	fmt.Println(string(b))
	logger.Info.Println(string(b))

	var strInfo []byte
	switch datatype {
	case 1:
		strInfo, err = serveresclient.NewESClientCl.SearchByTerm(common.DataInteractiveCommunication, common.TbDataRequirements, body)
		if err != nil {
			logger.Error.Printf("查询数据需求失败:%s", err.Error())
			return total, err
		}
	case 2:
		strInfo, err = serveresclient.NewESClientCl.SearchByTerm(common.DataInteractiveCommunication, common.TbDataErrorCorrection, body)
		if err != nil {
			logger.Error.Printf("查询数据需求失败:%s", err.Error())
			return total, err
		}
	}

	var response map[string]interface{}
	err = json.Unmarshal([]byte(strInfo), &response)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return total, err
	}
	if response["hits"] != nil {
		total = int(response["hits"].(map[string]interface{})["total"].(float64))
	}

	return total, nil
}

func GetDataBody(sp DataSearchNew) gin.H {
	if sp.Page == 0 {
		sp.Page = 1
	}
	if sp.Perpage == 0 {
		sp.Perpage = 100000
	}
	sort := gin.H{
		"processtime": gin.H{
			"order": "desc",
		},
	}
	var terms []map[string]interface{}
	term := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	terms = append(terms, term)
	//数据纠错只区分项目不区分用户
	//term1 := gin.H{
	//	"term": gin.H{
	//		"userid": sp.UserID,
	//	},
	//}
	//terms = append(terms, term1)
	//默认查询已处理
	check := gin.H{
		"terms": gin.H{
			"processstatus": []int{2, 3},
		},
	}
	terms = append(terms, check)

	var rangeT map[string]interface{}
	if sp.StartTime != "" && sp.EndTime != "" {
		rangeT = gin.H{
			"range": gin.H{
				"processtime": gin.H{
					"gte": sp.StartTime,
					"lte": sp.EndTime,
				},
			},
		}
		terms = append(terms, rangeT)
	}
	var should []map[string]interface{}
	if sp.FiterSets.Match != "" {

		if toolscommon.ESedition == 1 {
			switch sp.DataType {
			case 1:
				s1 := gin.H{
					"query_string": gin.H{
						"fields": []string{"reqname^10",
							"reqdesc",
							"reqresponse",
							"userdepartment",
							"username"},
						"query":                    "*" + sp.FiterSets.Match + "*",
						"default_operator":         "or",
						"lowercase_expanded_terms": true,
					},
				}
				s2 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"reqname.text_ik",
						},
						"boost": 1,
					},
				}
				s3 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"reqdesc.text_ik",
							"reqresponse.text_ik",
							"userdepartment.text_ik",
							"username.text_ik"},
						"boost": 0.5,
					},
				}
				should = append(should, s1, s2, s3)
			case 2:
				s1 := gin.H{
					"query_string": gin.H{
						"fields": []string{"corrname^10", "assetname^10",
							"dataproviderlist", "userdepartment", "problemtype", "handlingopinions", "corrdesc",
							"askrecord.corrdesc", "askrecord.handlingopinions"},
						"query":                    "*" + sp.FiterSets.Match + "*",
						"default_operator":         "or",
						"lowercase_expanded_terms": true,
					},
				}
				s2 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"corrname.text_ik",

							"assetname.text_ik",
						},
						"boost": 1,
					},
				}
				s3 := gin.H{
					"multi_match": gin.H{
						"query": sp.FiterSets.Match,
						"fields": []string{
							"dataproviderlist.text_ik",
							"userdepartment.text_ik",
							"problemtype.text_ik",
							"handlingopinions.text_ik", "askrecord.corrdesc.text_ik", "askrecord.handlingopinions.text_ik",
							"corrdesc.text_ik"},
						"boost": 0.5,
					},
				}
				should = append(should, s1, s2, s3)
			}

		} else {
			switch sp.DataType {
			case 1:
				sp.FiterSets.Fields = []string{"reqname", "reqdesc"}
			case 2:
				sp.FiterSets.Fields = []string{"corrname", "corrdesc", "assetname", "askrecord.corrdesc"}
			}
			match := gin.H{
				"query_string": gin.H{
					"query":                    "*" + sp.FiterSets.Match + "*",
					"fields":                   sp.FiterSets.Fields,
					"default_operator":         "or",
					"lowercase_expanded_terms": true,
				},
			}
			terms = append(terms, match)
		}

	}
	body := gin.H{
		"sort": sort,
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": sp.Perpage,
		"from": (sp.Page - 1) * sp.Perpage,
	}
	if toolscommon.ESedition == 1 && sp.FiterSets.Match != "" {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must":                 terms,
					"should":               should,
					"minimum_should_match": 1,
				},
			},
			"size": sp.Perpage,
			"from": (sp.Page - 1) * sp.Perpage,
		}
	}
	b, _ := json.Marshal(body)
	fmt.Println(string(b))
	logger.Info.Println(string(b))
	return body
}

func GetReqDetail(id string) (res DataRequirements, err error) {
	var r DataRequirements
	strInfo, err := serveresclient.NewESClientCl.SearchByID(common.DataInteractiveCommunication, common.TbDataRequirements, id)
	if err != nil {
		logger.Error.Printf("查询失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}

	if r.UserID != "" {
		r.UserName, _ = user.GetShowUserName(r.UserID)
	}

	return r, nil
}

func GetErrorDetail(id string) (res DataErrorCorrection, err error) {
	var r DataErrorCorrection
	strInfo, err := serveresclient.NewESClientCl.SearchByID(common.DataInteractiveCommunication, common.TbDataErrorCorrection, id)
	if err != nil {
		logger.Error.Printf("查询失败:%s", err.Error())
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}

	if r.UserID != "" {
		r.UserName, _ = user.GetShowUserName(r.UserID)
	}

	return r, nil
}

func DateConfGet(projectID string) (SourcedbList, error) {
	var dataConf SourcedbList
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": projectID,
					},
				},
			},
		},
	}
	bytes, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBasePlatform, common.TbMyData, body)
	if err != nil {
		logger.Error.Println(err.Error())
		return dataConf, err
	}

	resp := serveresclient.NewESClientCl.GetSource(bytes)

	if len(resp) == 0 {
		dataConf.ConfType = "default"
	} else {
		_ = json.Unmarshal([]byte(resp[0]), &dataConf)

	}
	return dataConf, nil
}

func getSourcetb(sourceID, tbname string) (string, error) {
	tSource := gin.H{
		"term": gin.H{
			"sourceid": sourceID,
		},
	}
	tTable := gin.H{
		"term": gin.H{
			"tbname": tbname,
		},
	}
	var boolm []map[string]interface{}
	boolm = append(boolm, tSource, tTable)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": boolm,
			},
		},
	}

	bytes, err := serveresclient.NewESClientCl.SearchByTerm(common.DatabaseMetadata, common.TbSourcetb, body)
	if err != nil {
		logger.Error.Println(err.Error())
		return "", err
	}
	resp := serveresclient.NewESClientCl.GetSource(bytes)
	if len(resp) == 0 {
		return "", nil
	}
	return gjson.Get(resp[0], "id").String(), nil
}

func getAssetTB(sourceTBID string) (AssetInventory, error) {
	var res AssetInventory
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"attachinfo.attachtableinfo.id": sourceTBID,
					},
				},
			},
		},
	}

	bytes, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Println(err.Error())
		return res, err
	}

	resp := serveresclient.NewESClientCl.GetSource(bytes)
	if len(resp) == 0 {
		return res, nil
	}

	err = json.Unmarshal([]byte(resp[0]), &res)
	if err != nil {
		logger.Error.Println(err.Error())
		return res, err
	}
	return res, nil
}

func GetEnum(sp EnumParam) ([]*EnumRes, error) {
	var res []*EnumRes
	//var err error
	tbInfo, err := collect.MetaInfoByID(sp.ID)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	engineinfo, err := dbutil.EngineInfo(tbInfo.EngineID)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	sqlTxt := fmt.Sprintf("select distinct `%s` from `%s`.`%s` order by `%s`", strings.Join(sp.FieldList, "`,`"), tbInfo.SourceDb, tbInfo.TBName, strings.Join(sp.FieldList, "`,`"))
	if tbInfo.DBType != "hive" && tbInfo.DBType != common.Inceptor && tbInfo.DBType != common.ODPS {
		sqlTxt = strings.ReplaceAll(sqlTxt, "`", `"`)
	}
	resData, err := collect.MGExecuteQuerySqlV2(engineinfo, tbInfo.DbName, sqlTxt)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	tmpField := make([]string, len(sp.FieldList))
	root := &EnumRes{Value: "root"}
	for _, d := range resData.Data {
		for i, f := range sp.FieldList {
			tmpField[i] = commontools.I2S(d[f])
		}
		insertNode(root, tmpField)
	}
	res = root.Children
	return res, nil
}

func insertNode(root *EnumRes, path []string) {
	if len(path) == 0 {
		return
	}

	for _, child := range root.Children {
		if child.Value == path[0] {
			insertNode(child, path[1:])
			return
		}
	}

	newNode := &EnumRes{Value: path[0]}
	root.Children = append(root.Children, newNode)
	insertNode(newNode, path[1:])
}
func ResourceRegister(sp ResourceRegis) (string, error) {
	sp.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	ids, _ := ResourceDirID(sp.DirId)
	for i, v := range ids {
		if i == 0 {
			sp.ResourcePath = v
		} else {
			sp.ResourcePath = v + "/" + sp.ResourcePath
		}
	}
	_, err := esutil.AddSingleWithRefresh(common.DataBaseModelAsset, common.TbResource, "id", common.RefreshTrue, sp)
	if err != nil {
		logger.Error.Println(err)
		return "注册失败", err
	}
	return "注册成功", nil
}

func ZbResourceRegister(sp ResourceRegis) (string, error) {
	sp.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	ids, _ := ResourceDirID(sp.DirId)
	for i, v := range ids {
		if i == 0 {
			sp.ResourcePath = v
		} else {
			sp.ResourcePath = v + "/" + sp.ResourcePath
		}
	}
	id, err := esutil.AddSingleWithRefresh(common.DataBaseModelAsset, common.TbResource, "id", common.RefreshTrue, sp)
	if err != nil {
		logger.Error.Println(err)
		return "注册失败", err
	}
	//权限检测失败也要通过
	sp.Id = id
	ResourceStatus(sp)

	return "注册成功", nil
}

func ResourceStatus(sp ResourceRegis) (string, error) {
	var loginfo AccCheckLog
	res, _ := ResourceQuery(sp.Id)

	var body interface{}
	if sp.Type == 1 {
		//_, err := commontools.DatrixGetToken(sp.UserName, sp.Url)
		var err, err2 error
		if sp.Url == "" {
			_, _, _, err2 = commontools.DatrixLogin(sp.UserName, sp.Password, sp.HttpUrl)
			if err2 != nil {
				logger.Error.Println(err2)
				loginfo.CheckStatus = 0
				loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
				loginfo.CheckMessage = "资源库连接检测失败"
				res.CheckLogs = append(res.CheckLogs, loginfo)
				res.Status = 2
				body = gin.H{
					"checklog": res.CheckLogs,
					"status":   res.Status,
				}
				serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)
				return "不可用", err2
			}
		} else {
			if sp.HttpUrl == "" {
				_, _, _, err = commontools.DatrixLogin(sp.UserName, sp.Password, sp.Url)
				if err != nil {
					logger.Error.Println(err)
					loginfo.CheckStatus = 0
					loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
					loginfo.CheckMessage = "资源库连接检测失败"
					res.CheckLogs = append(res.CheckLogs, loginfo)
					res.Status = 2
					body = gin.H{
						"checklog": res.CheckLogs,
						"status":   res.Status,
					}
					serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)
					return "不可用", err
				}
			} else {
				_, _, _, err = commontools.DatrixLogin(sp.UserName, sp.Password, sp.Url)
				_, _, _, err2 = commontools.DatrixLogin(sp.UserName, sp.Password, sp.HttpUrl)
				if err != nil {
					logger.Error.Println(err)
					loginfo.CheckStatus = 0
					loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
					loginfo.CheckMessage = "资源库连接检测失败"
					res.CheckLogs = append(res.CheckLogs, loginfo)
					res.Status = 2
					body = gin.H{
						"checklog": res.CheckLogs,
						"status":   res.Status,
					}
					serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)
					return "不可用", err
				}
				if err2 != nil {
					logger.Error.Println(err2)
					loginfo.CheckStatus = 0
					loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
					loginfo.CheckMessage = "资源库连接检测失败"
					res.CheckLogs = append(res.CheckLogs, loginfo)
					res.Status = 2
					body = gin.H{
						"checklog": res.CheckLogs,
						"status":   res.Status,
					}
					serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)
					return "不可用", err2
				}
			}
		}

	} else {
		//指标健康查询
		_, err := CommonDSLoginVerfy(sp)
		//_, err = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.HttpUrl)
		if err != nil {
			logger.Error.Println(err)
			loginfo.CheckStatus = 0
			loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
			loginfo.CheckMessage = "资源库连接检测失败"
			res.CheckLogs = append(res.CheckLogs, loginfo)
			res.Status = 2
			body = gin.H{
				"checklog": res.CheckLogs,
				"status":   res.Status,
			}
			serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)
			return "不可用", err

		}

		if sp.ZbProjectId == "" {
			loginfo.CheckStatus = 0
			loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
			loginfo.CheckMessage = "资源库连接权限检测失败"
			res.CheckLogs = append(res.CheckLogs, loginfo)
			res.Status = 2
			body = gin.H{
				"checklog": res.CheckLogs,
				"status":   res.Status,
			}
			serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)

			if sp.IsLogCheck {
				//需要返回报错
				return "没有项目权限", errors.New("没有项目权限")
			} else {
				return "没有项目权限", nil
			}
		} else {
			hasAuth, err := GetDSMenu(DSMetricsPara{ProjectID: sp.ZbProjectId}, sp)
			if err != nil || !hasAuth {
				logger.Error.Println(err)
				loginfo.CheckStatus = 0
				loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
				loginfo.CheckMessage = "资源库连接权限检测失败"
				res.CheckLogs = append(res.CheckLogs, loginfo)
				res.Status = 2
				body = gin.H{
					"checklog": res.CheckLogs,
					"status":   res.Status,
				}
				serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)
				if sp.IsLogCheck {
					//需要返回报错
					return "没有项目权限", errors.New("没有项目权限")
				} else {
					return "没有项目权限", nil
				}
			}
		}

	}
	//更新es
	loginfo.CheckStatus = 1
	loginfo.CheckTime = time.Now().Format("2006-01-02 15:04:05")
	loginfo.CheckMessage = "资源库连接检测成功"
	res.CheckLogs = append(res.CheckLogs, loginfo)
	res.Status = 1
	body = gin.H{
		"checklog": res.CheckLogs,
		"status":   res.Status,
	}
	err := serveresclient.NewESClientCl.UpdByID(common.DataBaseModelAsset, common.TbResource, sp.Id, body)
	if err != nil {
		logger.Error.Println(err)
	}
	return "健康", nil
}

func ResourceListProject(sp ResourceRegis) (commontools.DSListProjectResp, error) {
	var proInfo commontools.DSListProjectResp
	//登录获取token信息
	var err, err2 error
	var token string
	var resp []byte
	if sp.Url == "" {
		token, err = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.HttpUrl)
		if err != nil {
			logger.Error.Println(err)
			return proInfo, err
		}
		resp, err = httpclient2.DSPostJsonWithToken(sp.HttpUrl+toolscommon.DSListUserInfo, "{}", "Bearer "+token)
		if err != nil {
			logger.Error.Println(err)
			return proInfo, err
		}
	} else {
		if sp.HttpUrl == "" {
			token, err = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.Url)
			if err != nil {
				logger.Error.Println(err)
				return proInfo, err
			}
			resp, err = httpclient2.DSPostJsonWithToken(sp.Url+toolscommon.DSListUserInfo, "{}", "Bearer "+token)
			if err != nil {
				logger.Error.Println(err)
				return proInfo, err
			}
		} else {
			token, err = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.Url)
			token, err2 = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.HttpUrl)
			if err != nil && err2 != nil {
				logger.Error.Println(err)
				return proInfo, err
			}
			resp, err = httpclient2.DSPostJsonWithToken(sp.Url+toolscommon.DSListUserInfo, "{}", "Bearer "+token)
			resp, err2 = httpclient2.DSPostJsonWithToken(sp.HttpUrl+toolscommon.DSListUserInfo, "{}", "Bearer "+token)
			if err != nil && err2 != nil {
				logger.Error.Println(err)
				return proInfo, err
			}
		}
	}
	//token, err := commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.Url)
	//if err != nil {
	//	logger.Error.Println(err)
	//	return proInfo, err
	//}
	//获取project信息

	err = json.Unmarshal(resp, &proInfo)
	if err != nil {
		logger.Error.Println(err)
		return proInfo, err
	}
	return proInfo, nil
}
func ResourceUpdate(sp ResourceRegis) (string, error) {
	ids, _ := ResourceDirID(sp.DirId)
	for i, v := range ids {
		if i == 0 {
			sp.ResourcePath = v
		} else {
			sp.ResourcePath = v + "/" + sp.ResourcePath
		}
	}
	res, err := ResourceQuery(sp.Id)
	if err != nil {
		logger.Error.Println(err)
		return "编辑失败", err
	}
	sp.CheckLogs = res.CheckLogs
	sp.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	//body := gin.H{
	//	"name":         sp.Name,
	//	"projectid":    sp.ProjectID,
	//	"url":          sp.Url,
	//	"username":     sp.UserName,
	//	"password":     sp.Password,
	//	"dirid":        sp.DirId,
	//	"desc":         sp.Desc,
	//	"status":       sp.Status,
	//	"updatetime":   sp.UpdateTime,
	//	"datrixuserid": sp.DatrixUserId,
	//}
	err = serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbResource, common.RefreshTrue, sp.Id, sp)
	if err != nil {
		logger.Error.Println(err)
		return "编辑失败", err
	}
	if sp.Type == 2 {
		token, _ := CommonDSGetCASToken(sp)
		rediscli.Exec("SETEX", sp.Id, 9*60, token)
	}
	return "编辑成功", nil
}
func ResourceList(sp ResourceSearch) (interface{}, int, error) {
	info := []gin.H{}
	var terms []interface{}
	//分页查询
	if sp.Page < 0 { //0：搜索全部
		sp.Page = 1
	}
	if sp.Perpage < 1 {
		sp.Perpage = 10
	}
	//项目id查询
	if sp.ProjectID != "" {
		projectT := gin.H{
			"term": gin.H{
				"projectid": sp.ProjectID,
			},
		}
		terms = append(terms, projectT)
	}

	if !sp.Listall {
		term1 := gin.H{
			"term": gin.H{
				"user": sp.User,
			},
		}
		terms = append(terms, term1)
	}

	//增加筛选
	if sp.Type != 0 {
		TypeT := gin.H{
			"term": gin.H{
				"type": sp.Type,
			},
		}
		terms = append(terms, TypeT)
	}

	//目录id
	if sp.Dirid != "" {
		// 循环获取子文件夹id
		dirids, err := folder.MetaDirID(sp.Dirid)
		if err != nil {
			logger.Error.Printf("循环获取抽取任务子文件夹失败:%s", err.Error())
			return info, 0, err
		}
		term2 := gin.H{
			"terms": gin.H{
				"dirid": dirids,
			},
		}
		terms = append(terms, term2)
	}
	//tags, notename, descrition 的模糊匹配
	if sp.Match != "" {
		match := gin.H{
			"query_string": gin.H{
				"query":            "*" + sp.Match + "*",
				"fields":           []string{"name", "resourcepath"},
				"default_operator": "or",
			},
		}
		terms = append(terms, match)
	}
	sortc := []gin.H{}
	var sortorder = "desc"
	if sp.Sort == 0 {
		sortorder = "asc"
	}

	switch sp.Sorttype {
	case 1: //时间降序
		sorttime := gin.H{
			"updatetime": sortorder,
		}
		sortc = append(sortc, sorttime)
	case 2: //任务名排序
		sorttask := gin.H{
			"name": sortorder,
		}
		sortc = append(sortc, sorttask)
	}
	var body gin.H
	//outfield := []string{"content", "sourcedb.fieldinfo", "realinfo"}
	if sp.Page == 0 {
		body = gin.H{
			//"_source": gin.H{
			//	"exclude": "password",
			//},
			"query": gin.H{
				"bool": gin.H{
					"must": terms,
				},
			},
			"size": 10000,
			"sort": sortc,
		}
	} else {
		//密码在最后要用，最后置空算了
		body = gin.H{
			//"_source": gin.H{
			//	"exclude": "password",
			//},
			"query": gin.H{
				"bool": gin.H{
					"must": terms,
				},
			},
			"size": sp.Perpage,
			"from": (sp.Page - 1) * sp.Perpage,
			"sort": sortc,
		}
	}
	byteBody, err := json.Marshal(body)
	if err != nil {
		return info, 0, err
	}
	fmt.Println("body:", string(byteBody))
	resp, err := serveresclient.NewESClientCl.SearchByTerm(DataBaseModelAsset, TbResource, body)
	if err != nil {
		logger.Error.Println(err)
		return info, 0, nil
	}
	var sou ResourceRegisHits
	err = json.Unmarshal(resp, &sou)
	if err != nil {
		logger.Error.Println(err)
		return info, 0, err
	}

	regis := make([]ResourceRegis, 0)
	for _, v := range sou.Hits1.Hits2 {
		sort.Slice(v.ResourceRegis.CheckLogs, func(i, j int) bool {
			return v.ResourceRegis.CheckLogs[i].CheckTime > v.ResourceRegis.CheckLogs[j].CheckTime
		})
		//查看是否被编目或者挂载使用
		var dataterms []map[string]interface{}
		term1 := gin.H{
			"term": gin.H{
				"putinfo.putserverinfo.resourceid": v.ResourceRegis.Id,
			},
		}
		term2 := gin.H{
			"term": gin.H{
				"attachinfo.attachfileinfo.resourceid": v.ResourceRegis.Id,
			},
		}

		term3 := gin.H{
			"term": gin.H{
				"attachinfo.attachmetricsinfo.resourceid": v.ResourceRegis.Id,
			},
		}

		term4 := gin.H{
			"term": gin.H{
				"attachinfo.attachtpapiinfo.resourceid": v.ResourceRegis.Id,
			},
		}

		dataterms = append(dataterms, term1, term2, term3, term4)
		body = gin.H{
			"sort": sortc,
			"size": common.UnlimitSize,
			"query": gin.H{
				"bool": gin.H{
					"should": dataterms,
				},
			},
		}

		res, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
		if err != nil {
			logger.Error.Println(err)
		}
		if gjson.Get(string(res), "hits.total").Int() != 0 {
			v.ResourceRegis.Used = 1
		} else {
			v.ResourceRegis.Used = 0
		}
		regis = append(regis, v.ResourceRegis)
	}
	//res := gin.H{
	//	"result": regis,
	//	"total":  sou.Hits1.Total,
	//}

	//对于指标的projectname重新赋值
	//赋值取消
	/*	for i, v := range regis {
		regis[i].Password = ""
		if v.Type == 2 {
			res, err := ResourceListProject(v)
			if err != nil {
				logger.Error.Println("获取指标的项目信息失败", err)
			}
			for _, oneProject := range res.Result.ProjectDetail {
				if oneProject.ProjectID == v.ZbProjectId {
					regis[i].ZbProjectName = oneProject.ProjectName
				}
			}
		}
	}*/

	return regis, sou.Hits1.Total, nil
}

func ResourceQuery(resourceID string) (ResourceRegis, error) {
	var res ResourceRegis
	resStr, err := serveresclient.NewESClientCl.SearchByID(common.DataBaseModelAsset, common.TbResource, resourceID)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	err = json.Unmarshal([]byte(resStr), &res)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	return res, nil
}

func ResourceListAll(projectID string) ([]ResourceRegis, error) {
	regis := make([]ResourceRegis, 0)
	body := gin.H{
		"_source": []string{"id", "resource", "name"},
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"terms": gin.H{
						"projectid": projectID,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}

	resp, err := serveresclient.NewESClientCl.SearchByTerm(DataBaseModelAsset, TbResource, body)
	if err != nil {
		logger.Error.Println(err)
		return regis, nil
	}
	var sou ResourceRegisHits
	err = json.Unmarshal(resp, &sou)
	if err != nil {
		logger.Error.Println(err)
		return regis, err
	}
	for _, v := range sou.Hits1.Hits2 {
		regis = append(regis, v.ResourceRegis)
	}

	return regis, nil
}

// ListLink 列举资源
func ListLink(param DatrixListParam, resource ResourceRegis) (DatrixListRes, error) {
	var res DatrixListRes
	//补充默认参数
	param.ShowDir = -1        //展示文件夹
	param.WithUserInfo = true //包含上传者信息
	param.FileType = -1       //展示全部类型
	param.IsCatalog = -1      //筛选是否编目的文件 1:是; 0:否; -1:全部展示
	if param.SortBy == "" {
		param.SortBy = "lastTime,0"
	}
	var newToken string
	var resBody []byte
	var err error
	if resource.Url == "" {
		newToken, resBody, err = commontools.DatrixPost(GetDtToken(resource.Id), resource.HttpUrl, DtListLink, resource.UserName, param)
	} else {
		if resource.HttpUrl == "" {
			newToken, resBody, err = commontools.DatrixPost(GetDtToken(resource.Id), resource.Url, DtListLink, resource.UserName, param)
		} else {
			var err2 error
			newToken, resBody, err = commontools.DatrixPost(GetDtToken(resource.Id), resource.Url, DtListLink, resource.UserName, param)
			newToken, resBody, err2 = commontools.DatrixPost(GetDtToken(resource.Id), resource.HttpUrl, DtListLink, resource.UserName, param)
			if err != nil && err2 != nil {
				logger.Error.Println(err)
				return res, err
			}
		}
	}
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	if newToken != "" {
		UpdateDtToken(resource.Id, newToken)
	}
	//fmt.Println("listlink res: ", string(resBody))
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	for i, v := range res.Result {
		res.Result[i].UpdateTime = v.Link.Update_Time
	}
	return res, nil
}

// GetDtToken 从 redis 中取 token
func GetDtToken(resourceID string) string {
	res, _ := rediscli.Exec("GET", resourceID)
	switch res.(type) {
	case string:
		return res.(string)
	case []byte:
		return string(res.([]byte))
	default:
		return ""
	}
}

// UpdateDtToken 更新 redis 中 token
func UpdateDtToken(resourceID, token string) {
	rediscli.Exec("SET", resourceID, token)
	return
}

func QueryFileListDetail(resourceID string, fileList []string) ([]AttachFileInfo, error) {
	l := len(fileList)
	res := make([]AttachFileInfo, l)
	if l == 0 {
		return res, nil
	}
	for i := 0; i < l; i++ {
		res[i].FileID = fileList[i]
		res[i].FileError = common.FileError
	}
	resource, err := ResourceQuery(resourceID)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}

	param := DatrixFileListParam{
		UserID:  resource.DatrixUserId,
		FileIDs: fileList,
	}
	tmpurl := commontools.CommonUrl(resource.Url, resource.HttpUrl)
	newToken, resBody, err := commontools.DatrixPost(GetDtToken(resource.Id), tmpurl, DtFileListInfo, resource.UserName, param)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Println(err)
		}
		return res, err
	}

	if newToken != "" {
		UpdateDtToken(resource.Id, newToken)
	}

	var resp DatrixInfoListRes
	err = json.Unmarshal(resBody, &resp)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Println(err)
		}
		return res, err
	}

	for i, j := range res {
		for _, r := range resp.Result {
			if r.FileID == j.FileID {
				if r.InTrash {
					res[i].ErrorInfo = fmt.Sprintf("file %s in trash", r.FileID)
					continue
				}
				res[i].UserInfo = r.UserInfo
				res[i].FileSize = r.FileSize
				res[i].FileType = r.FileType
				res[i].FilenameKeywordIkPinyin = r.FilenameKeywordIkPinyin
				res[i].UpdateTime = r.UpdateTime
				res[i].FileError = 0
			}
		}
	}

	return res, nil
}

func QueryFileListInfo(resourceID string, fileList map[string]struct{}) (map[string]struct{}, error) {
	res := make(map[string]struct{})
	prefix := resourceID + "_"

	var fileIds []string
	for i := range fileList {
		fileIds = append(fileIds, i)
		res[prefix+i] = struct{}{}
	}

	resource, err := ResourceQuery(resourceID)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}

	param := DatrixFileListParam{
		UserID:  resource.DatrixUserId,
		FileIDs: fileIds,
	}
	tmpurl := commontools.CommonUrl(resource.Url, resource.HttpUrl)
	newToken, resBody, err := commontools.DatrixPost(GetDtToken(resource.Id), tmpurl, DtFileListInfo, resource.UserName, param)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Println(err)
		}
		return res, err
	}

	if newToken != "" {
		UpdateDtToken(resource.Id, newToken)
	}

	var resp DatrixInfoListRes
	err = json.Unmarshal(resBody, &resp)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Println(err)
		}
		return res, err
	}

	for _, i := range resp.Result {
		if !i.InTrash {
			delete(res, prefix+i.FileID)
		}
	}
	return res, nil
}

func QueryFileInfo(param DatrixFileParam) (DatrixFileInfo, error) {
	fmt.Println("---jinlai--")
	var res DatrixFileInfo
	resource, err := ResourceQuery(param.ResourceID)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	tmpurl := commontools.CommonUrl(resource.Url, resource.HttpUrl)
	newToken, resBody, err := commontools.DatrixPost(GetDtToken(resource.Id), tmpurl, DtFileInfo, resource.UserName, param)
	fmt.Println("gettoken:", GetDtToken(resource.Id), tmpurl, resource.UserName, param)
	if err != nil {
		logger.Info.Println("1234")
		if logger.Error != nil {
			logger.Error.Println(err)
		}
		return res, err
	}

	if newToken != "" {
		UpdateDtToken(resource.Id, newToken)
	}
	//fmt.Println("FileInfo res: ", string(resBody))
	var resp DatrixInfoRes
	err = json.Unmarshal(resBody, &resp)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Println(err)
		}
		return res, err
	}
	res = resp.Result
	res.ResourceName = resource.Name
	res.ResourceID = resource.Id

	if res.InTrash {
		//已被删除
		err = fmt.Errorf("file %s in trash", res.FileID)
		return res, err
	}
	return res, nil

}

func FileInfo(param DatrixFileParam, resource ResourceRegis) (DatrixInfoRes, error) {
	var res DatrixInfoRes
	tmpurl := commontools.CommonUrl(resource.Url, resource.HttpUrl)
	newToken, resBody, err := commontools.DatrixPost(GetDtToken(resource.Id), tmpurl, DtFileInfo, resource.UserName, param)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	if newToken != "" {
		UpdateDtToken(resource.Id, newToken)
	}
	//fmt.Println("FileInfo res: ", string(resBody))
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	return res, nil
}

func GetPreview(param DatrixFileParam, resource ResourceRegis) (string, string, string, error) {
	var res, httpres, dtToken string
	dtToken, err := commontools.DatrixGetToken(resource.UserName, resource.Url, resource.HttpUrl)
	if err != nil {
		logger.Error.Println(err)
		return res, httpres, dtToken, err
	}
	if dtToken == "" {
		err = fmt.Errorf("token empty")
		logger.Error.Println(err)
		return res, httpres, dtToken, err
	}

	//http://192.168.51.205:30805/plug-in/filepreview/index.html?id=2023111510_bx5azgn969ryy_lv0.pdf&token=a655eb05ce78da3857934bf25e12a84f&userid=1550ebb882ae1b7f9543718f40e61da8
	res = fmt.Sprintf(`%s%s?id=%s&token=%s&userid=%s`, resource.Url, DtPreview, param.FileID, dtToken, resource.DatrixUserId)
	httpres = fmt.Sprintf(`%s%s?id=%s&token=%s&userid=%s`, resource.HttpUrl, DtPreview, param.FileID, dtToken, resource.DatrixUserId)

	return res, httpres, dtToken, nil
}

func GetThumbnail(param DatrixFileParam, resource ResourceRegis) (string, string, error) {
	var res, dtToken string
	dtToken, err := commontools.DatrixGetToken(resource.UserName, resource.Url, resource.HttpUrl)
	if err != nil {
		logger.Error.Println(err)
		return res, dtToken, err
	}
	if dtToken == "" {
		err = fmt.Errorf("token empty")
		logger.Error.Println(err)
		return res, dtToken, err
	}

	//http://192.168.51.205:30805/plug-in/filepreview/index.html?id=2023111510_bx5azgn969ryy_lv0.pdf&token=a655eb05ce78da3857934bf25e12a84f&userid=1550ebb882ae1b7f9543718f40e61da8
	res = fmt.Sprintf(`%s%s?fileId=%s&token=%s`, resource.Url, DtThumbnail, param.FileID, dtToken)

	return res, dtToken, nil
}

func GetToken(resource ResourceRegis) (string, error) {
	dtToken, err := commontools.DatrixGetToken(resource.UserName, resource.Url, resource.HttpUrl)
	if err != nil {
		logger.Error.Println(err)
		return dtToken, err
	}
	return dtToken, nil
}

func ResourceDelete(id string) (string, error) {
	err := serveresclient.NewESClientCl.DelByIDWithRefresh(common.DataBaseModelAsset, common.TbResource, common.RefreshTrue, id)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	return "删除成功", nil
}
func ResourceMove(info MoveInfo) error {
	updatebody := gin.H{
		"dirid":        info.Dirid,
		"resourcepath": info.ResourcePath,
	}
	err := esutil.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbResource, common.RefreshTrue, info.Id, updatebody)
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	return nil
}

func ResourceDownload(sp DownloadInfo) (*http.Response, error) {
	var res *http.Response
	var err error
	if sp.Url == "" {
		res, err = commontools.DatrixDownload("", sp.HttpUrl, sp.Username, sp.FileId, sp.Auth)
		if err != nil {
			logger.Error.Println(err)
			return res, err
		}

	} else {
		if sp.HttpUrl == "" {
			var err error
			res, err = commontools.DatrixDownload("", sp.Url, sp.Username, sp.FileId, sp.Auth)
			if err != nil {
				logger.Error.Println(err)
				return res, err
			}
			fmt.Println("urlres:", res)
		} else {
			res, err = commontools.DatrixDownload("", sp.Url, sp.Username, sp.FileId, sp.Auth)
			res2, err2 := commontools.DatrixDownload("", sp.HttpUrl, sp.Username, sp.FileId, sp.Auth)
			if err == nil || err2 == nil {
				if err == nil {
					return res, nil
				}
				if err2 == nil {
					return res2, nil
				}
			} else {
				logger.Error.Println("没有合适的url访问datrix:", err, err2)
				return res, err
			}
		}
	}
	return res, nil
	//res, err := commontools.DatrixDownload("", sp.Url, sp.Username, sp.FileId, sp.Auth)
	//if err != nil {
	//	logger.Error.Println(err)
	//	return res, err
	//}
	//var res ResourceRegis
	//resStr, err := serveresclient.NewESClientCl.SearchByID(common.DataBaseModelAsset, common.TbResource, resourceID)
	//if err != nil {
	//	logger.Error.Println(err)
	//	return res, err
	//}
	//err = json.Unmarshal([]byte(resStr), &res)
	//if err != nil {
	//	logger.Error.Println(err)
	//	return res, err
	//}
}

func ResourceDownloadCnt(fileID, resourceID, inventoryid string) {

	//更新资产下载量
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"inventoryid": inventoryid,
					},
				},
			},
		},
	}

	cnt, err := esutil.JustCount(common.DataBaseModelAsset, common.TbResourceDownload, body)
	if err != nil {
		logger.Error.Println(err)
	} else {
		err = Statistics(inventoryid, "downloadtimes", cnt)
		if err != nil {
			logger.Error.Println(err)
		}
	}

	t := DatrixDownload{
		FileID:       fileID,
		ResourceID:   resourceID,
		InventoryID:  inventoryid,
		DownloadTime: commontools.GetTNow(),
	}
	_, err = esutil.AddWithoutID(common.DataBaseModelAsset, common.TbResourceDownload, t)
	if err != nil {
		logger.Error.Println(err)
	}

	return
}

// 访问量计数 id：资产ID，dataType:visitedtimes 访问量，calltimes 调用量，downloadtimes 下载量
func Statistics(id, dataType string, times int) error {
	_, err := rediscli.Exec("HSET", dataType, id, 1)
	if err != nil {
		logger.Error.Println("redis插入错误", err)
		return err
	}

	key := id + "_" + dataType
	reply, err := rediscli.Exec("exists", key)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	isExist, _ := red.Int(reply, err)
	if isExist == 0 {
		_, err = rediscli.Exec("SET", key, times+1) //key 不存在时初始化
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	} else {
		_, err = rediscli.Exec("INCR", key)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	}
	return nil
}

func GetDownloadTimes(fileID, resourceID, inventoryid string) (int, error) {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": []gin.H{
					gin.H{
						"term": gin.H{
							"fileid": fileID,
						},
					},
					gin.H{
						"term": gin.H{
							"resourceid": resourceID,
						},
					},
					gin.H{
						"term": gin.H{
							"inventoryid": inventoryid,
						},
					},
				},
			},
		},
	}
	res, err := esutil.JustCount(common.DataBaseModelAsset, common.TbResourceDownload, body)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	return res, nil
}

func ResourceDirID(id string) ([]string, error) {
	ids := []string{}
	for {
		//delid = map[string]string{}
		bytes, err := esutil.SearchByID(common.DataBaseAnts, common.TbFolder, id)
		if err != nil {
			logger.Error.Printf("根据prentid查询文件夹信息失败:%s", err.Error())
			return ids, err
		}
		fatherid := gjson.Get(bytes, "fatherid").String()
		name := gjson.Get(bytes, "name").String()
		if fatherid == "" {
			ids = append(ids, name)
			break
		}
		ids = append(ids, name)
		id = fatherid
	}
	return ids, nil
}

func AssetVerionList(sp AssetVersionSearch) (list []AssetCategoryVerion, total int, err error) {
	var r AssetCategoryVerion
	var body interface{}
	var terms []interface{}
	var sort []map[string]interface{}
	var run gin.H

	if sp.Page == 0 {
		sp.Page = 1
	}
	if sp.Perpage == 0 {
		sp.Perpage = 100000
	}
	if sp.Sort.SortField == "" && sp.Match == "" {
		sp.Sort.SortField = "createdtime"
		sp.Sort.SortType = "desc"
	}
	if sp.ShowNameType == "" {
		sp.ShowNameType = "username"
	}

	if sp.Filter.FilterField != "" && len(sp.Filter.FilterValue) > 0 {
		switch sp.Filter.FilterField {
		case "userdepartment":
			users, err := user.GetAllUserWithProject(sp.ProjectID)
			if err != nil {
				logger.Error.Println(err)
				return list, total, err
			}

			userinfo, _, err := user.GetUserInfo(users)
			if err != nil {
				logger.Error.Println(err)
				return list, total, err
			}
			var userList []string
			for _, v := range userinfo {
				for _, v2 := range sp.Filter.FilterValue {
					if v.UserDepartment == v2 {
						userList = append(userList, v.UserName)
						break
					}
				}

			}
			if len(userList) > 0 {
				tUserName := gin.H{
					"terms": gin.H{
						"username": userList,
					},
				}
				terms = append(terms, tUserName)
			} else {
				tUserName := gin.H{
					"terms": gin.H{
						"username": []string{},
					},
				}
				terms = append(terms, tUserName)
			}

		}
	}

	if sp.Sort.SortField != "" {
		run = gin.H{
			sp.Sort.SortField: gin.H{
				"order": sp.Sort.SortType,
			},
		}
		sort = append(sort, run)
	} else {
		sort = []map[string]interface{}{}
	}

	term1 := gin.H{
		"term": gin.H{
			"projectid": sp.ProjectID,
		},
	}
	terms = append(terms, term1)

	var should []map[string]interface{}
	if sp.Match != "" {
		if sp.ShowNameType == "username" {
			s1 := gin.H{
				"query_string": gin.H{
					"fields": []string{"assetcategoryname^100",
						"exportfileformat",
						"username",
						"assetcategorydesc"},
					"query":                    "*" + sp.Match + "*",
					"default_operator":         "or",
					"lowercase_expanded_terms": true,
				},
			}
			s2 := gin.H{
				"multi_match": gin.H{
					"query": sp.Match,
					"fields": []string{
						"assetcategoryname.text_ik",
					},
					"boost": 1,
				},
			}
			s3 := gin.H{
				"multi_match": gin.H{
					"query": sp.Match,
					"fields": []string{
						"exportfileformat.text_ik",
						"username.text_ik",
						"assetcategorydesc.text_ik"},
					"boost": 0.5,
				},
			}

			should = append(should, s1, s2, s3)
		} else if sp.ShowNameType == "nickname" {
			s1 := gin.H{
				"query_string": gin.H{
					"fields": []string{"assetcategoryname^100",
						"exportfileformat",
						"assetcategorydesc"},
					"query":                    "*" + sp.Match + "*",
					"default_operator":         "or",
					"lowercase_expanded_terms": true,
				},
			}
			s2 := gin.H{
				"multi_match": gin.H{
					"query": sp.Match,
					"fields": []string{
						"assetcategoryname.text_ik",
					},
					"boost": 1,
				},
			}
			s3 := gin.H{
				"multi_match": gin.H{
					"query": sp.Match,
					"fields": []string{
						"exportfileformat.text_ik",
						"assetcategorydesc.text_ik"},
					"boost": 0.5,
				},
			}

			should = append(should, s1, s2, s3)
			//模糊查询先查一下别名和创建人部门
			var userShould []map[string]interface{}
			userTerm1 := gin.H{
				"query_string": gin.H{
					"fields": []string{"nickname",
						"userdepartment"},
					"query":                    "*" + sp.Match + "*",
					"default_operator":         "or",
					"lowercase_expanded_terms": true,
				},
			}
			userTerm2 := gin.H{
				"multi_match": gin.H{
					"query": sp.Match,
					"fields": []string{
						"nickname.text_ik",
						"userdepartment.text_ik",
					},
					"boost": 1,
				},
			}
			userShould = append(userShould, userTerm1, userTerm2)
			userBody := gin.H{
				"size": common.UnlimitSize,
				"sort": sort,
				"query": gin.H{
					"bool": gin.H{
						"should":               userShould,
						"minimum_should_match": 1,
					},
				},
			}
			userByteBody, _ := json.Marshal(userBody)
			fmt.Println(string(userByteBody))
			logger.Info.Println(string(userByteBody))
			strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAuth, common.TbUser, userBody)
			if err != nil {
				logger.Error.Printf("查询资产目录版本失败:%s", err.Error())
				return list, total, err
			}

			source := serveresclient.NewESClientCl.GetSource(strInfo)
			var userids []string
			for _, v := range source {
				userid := gjson.Get(v, "userid").String()
				userids = append(userids, userid)
			}
			if len(userids) > 0 {
				logger.Info.Println("userids:", userids)
				tUserIDS := gin.H{
					"terms": gin.H{
						"userid": userids,
					},
				}
				should = append(should, tUserIDS)
			}
		}

		body = gin.H{
			"from": (sp.Page - 1) * sp.Perpage,
			"size": sp.Perpage,
			"sort": sort,
			"query": gin.H{
				"bool": gin.H{
					"must":                 terms,
					"should":               should,
					"minimum_should_match": 1,
				},
			},
		}
	} else {
		body = gin.H{
			"from": (sp.Page - 1) * sp.Perpage,
			"size": sp.Perpage,
			"sort": sort,
			"query": gin.H{
				"bool": gin.H{
					"must": terms,
				},
			},
		}
	}

	byteBody, _ := json.Marshal(body)
	fmt.Println(string(byteBody))
	logger.Info.Println(string(byteBody))
	strInfo, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseModelAsset, common.TbAssetCatalogVersion, body)
	if err != nil {
		logger.Error.Printf("查询资产目录版本失败:%s", err.Error())
		return list, total, err
	}

	source := serveresclient.NewESClientCl.GetSource(strInfo)

	for _, v := range source {
		err = json.Unmarshal([]byte(v), &r)
		if err != nil {
			logger.Error.Printf("解析表数据失败:%s", err.Error())
			return list, total, err
		}
		list = append(list, r)
	}

	bodycount := gin.H{
		"size": common.UnlimitSize,
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": sp.ProjectID,
					},
				},
			},
		},
	}
	if sp.Match != "" {
		bodycount = gin.H{
			"size": common.UnlimitSize,
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"projectid": sp.ProjectID,
						},
					},
					"should":               should,
					"minimum_should_match": 1,
				},
			},
		}
	}
	total, err = serveresclient.NewESClientCl.SearchCount(common.DataBaseModelAsset, common.TbAssetCatalogVersion, bodycount)
	if err != nil {
		logger.Error.Printf("获取total失败:%s", err.Error())
		return list, total, err
	}

	for k, v := range list {
		_, userMap, err := user.GetUserInfo([]string{v.UserID})
		if err != nil {
			logger.Error.Println(err)
			return list, total, err
		}
		if _, ok := userMap[v.UserID]; ok {
			list[k].NickName = userMap[v.UserID].NickName
			list[k].UserDepartment = userMap[v.UserID].UserDepartment
		}
	}

	return list, total, nil
}

func AssetVerionAttList(id string) (attnamelist, attsignlist []string, attvaluelist, attvalueidlist [][]string, err error) {

	catalogInfo, err := CatalogDetail(id)
	if err != nil {
		logger.Error.Printf("获取资产编目详情失败:%s", err.Error())
		return
	}
	for _, v1 := range catalogInfo.CustomInfo {
		for _, v2 := range v1.CustomProperties {
			if v2.ProType == "select" || v2.ProType == "checkbox" {
				attnamelist = append(attnamelist, v2.ProName)
				attsignlist = append(attsignlist, v2.ProSign)
				var tmpValues []string
				if len(v2.MulitiSelected) > 0 {
					for _, k := range v2.MulitiEnumRated {
						for _, s := range v2.MulitiSelected {
							if k.ID == s {
								v2.ProValue = k.Value
								tmpValues = append(tmpValues, k.Value)
							}
						}
					}

				}
				attvaluelist = append(attvaluelist, tmpValues)

			}
		}
	}
	return attnamelist, attsignlist, attvaluelist, attvalueidlist, nil
}

func AssetVerionCustomFilteringQuery(sp AssetVersionSearch) (list []AssetInventorySimple, err error) {
	//获取所有ids

	ids, err := GetInventoryIDsByProjectid(sp.ProjectID)
	if err != nil {
		return list, err
	}
	var needids []string
	var propertyValueMaps []map[string]map[string]bool
	if len(sp.AssetMatch) > 0 {
		for _, v := range ids {
			info, err := GetInventoryAllInfo(v)
			if err != nil {
				return list, err
			}
			//(key: 属性名称)，(value: (key:属性值，value))
			var propertyValueMap = make(map[string]map[string]bool)
			for _, tempCustom := range info.AssetCatalog.CustomInfo {
				for _, tempProperty := range tempCustom.CustomProperties {
					var ValueMap = make(map[string]bool)
					//特殊处理资产类目ZCLM
					if tempProperty.ProSign == "ZCLM" {
						tempValue := fmt.Sprintf("%s_%s", info.InventoryCategoryID, info.InventoryCategory)
						ValueMap[tempValue] = true
						propertyValueMap[tempProperty.ProName] = ValueMap
						continue
					}
					//层级关系，存了被选中的最后一层
					var ValueIdMap = make(map[string]bool)
					for _, valueid := range tempProperty.MulitiSelected {
						ValueIdMap[valueid] = true
					}

					for _, value := range tempProperty.MulitiEnumRated {
						// id_fatherid_value
						if ValueIdMap[value.ID] == true {
							tempValue := fmt.Sprintf("%s_%s", value.ID, value.Value)
							ValueMap[tempValue] = true
						}
					}
					propertyValueMap[tempProperty.ProName] = ValueMap
				}
			}

			propertyValueMaps = append(propertyValueMaps, propertyValueMap)

		}

		var auditManager AuditManager
		auditManager.AssetMatch = sp.AssetMatch
		auditManager.AssetOption = sp.AssetOption

		chunkSize := 1000 // 每次并发处理的块大小

		results := make(map[int]bool, 0)
		var wg sync.WaitGroup

		if chunkSize > len(propertyValueMaps) {
			chunkSize = len(propertyValueMaps)
		}

		for i := 0; i < len(propertyValueMaps); i += chunkSize {
			end := i + chunkSize
			if end > len(propertyValueMaps) {
				end = len(propertyValueMaps)
			}

			wg.Add(1)

			go func(start, end int, auditManager AuditManager, propertyValueMaps *[]map[string]map[string]bool) {
				defer wg.Done()

				for j := start; j < end; j++ {
					flag := compareAssetPro(auditManager, (*propertyValueMaps)[j])
					results[j] = flag
				}

			}(i, end, auditManager, &propertyValueMaps)
		}

		wg.Wait()

		for k, v := range results {
			if v {
				needids = append(needids, ids[k])
			}
		}
	} else {
		needids = ids
	}
	if len(needids) > 0 {
		list, err = GetInventorybyids(needids, sp.ProjectID)
		if err != nil {
			return list, err
		}
	} else {
		return []AssetInventorySimple{}, nil
	}

	return list, nil
}

func GetInventorybyids(ids []string, projectid string) (res []AssetInventorySimple, err error) {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"terms": gin.H{
						"inventoryid": ids,
					},
				},
			},
		},
		"size": common.UnlimitSize,
		"sort": gin.H{
			"updatedtime": gin.H{
				"order": "desc",
			},
		},
	}

	strInfo, err := esutil.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产编目失败:%s", err.Error())
		return res, err
	}
	var response map[string]interface{}
	err = json.Unmarshal([]byte(strInfo), &response)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}
	var assets []AssetInventory
	if response["hits"] != nil {
		hits := response["hits"].(map[string]interface{})["hits"].([]interface{})
		for _, hit := range hits {
			var r AssetInventory
			sourceRes := hit.(map[string]interface{})["_source"]
			b, err := json.Marshal(sourceRes)
			if err != nil {
				return res, err
			}
			err = json.Unmarshal(b, &r)
			if err != nil {
				return res, err
			}
			assets = append(assets, r)

		}
	}

	var metadataIDS []string
	idandrow := make(map[string]int)
	for _, asset := range assets {
		metadataIDS = append(metadataIDS, asset.AttachInfo.AttachTableInfo.Id)
	}

	if len(metadataIDS) > 0 {
		var terms []gin.H
		term1 := gin.H{
			"term": gin.H{
				"projectid": projectid,
			},
		}
		terms = append(terms, term1)
		term2 := gin.H{
			"terms": gin.H{
				"id": metadataIDS,
			},
		}
		terms = append(terms, term2)

		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": terms,
				},
			},
			"size": common.UnlimitSize,
		}

		resultStr, err := esutil.SearchByTerm(common.DatabaseMetadata, common.TbSourcetb, body)
		if err != nil {
			logger.Error.Println("获取表信息错误")
		}
		tbinfos := esutil.GetSource(resultStr)

		for _, v := range tbinfos {
			idandrow[gjson.Get(v, "id").String()] = int(gjson.Get(v, "rowsnum").Int())
		}
	}

	var res1, res2, res3 []AssetInventorySimple
	for _, v := range assets {
		if v.AttachInfo.AttachStatus == "已挂载" && v.PutInfo.Put_Status == "" {
			v.PutInfo.Put_Status = "待上架"
		}

		if v.PutInfo.Put_Status == "下架审核中" {
			v.PutInfo.Put_Status = "审核中"
		}
		var simple AssetInventorySimple
		simple.UserID = v.UserID
		simple.UserName = v.UserName
		simple.ProjectID = v.ProjectID
		simple.CreatedTime = v.CreatedTime
		simple.UpdatedTime = v.UpdatedTime
		simple.InventoryID = v.InventoryID
		simple.InventoryName = v.InventoryName
		simple.AssetCode = v.AssetCatalog.BasicInfo.AssetCode
		simple.InventoryPath = v.InventoryPath
		simple.AssetType = v.AssetCatalog.BasicInfo.AssetType
		simple.SharedType = v.AssetCatalog.SharedInfo.SharedType
		simple.SharedMode = v.AssetCatalog.SharedInfo.SharedMode
		simple.OpenType = v.AssetCatalog.OpenInfo.OpenType
		simple.Status = v.Status
		simple.DATAAREAS = v.AssetCatalog.BasicInfo.DATAAREAS
		simple.DATASOURCESYSTEM = v.DataSourceSystem
		simple.DataNum = idandrow[v.AttachInfo.AttachTableInfo.Id]
		simple.Put_Status = v.PutInfo.Put_Status
		//res = append(res, simple)

		if !strings.Contains(v.InventoryID, "change") {
			switch v.Status {
			case 1:
				res1 = append(res1, simple)
			case 4:
				res2 = append(res2, simple)
			default:
				res3 = append(res3, simple)
			}
		}
	}
	res = append(res, res1...)
	//fmt.Println(len(res))
	res = append(res, res2...)
	//fmt.Println(len(res))
	res = append(res, res3...)
	//fmt.Println(len(res))

	return res, nil
}
func GetInventoryAllInfo(id string) (res MoneySaveInfo, err error) {
	var r MoneySaveInfo
	strInfo, err := esutil.SearchByID(common.DataBaseModelAsset, common.TbAssetInventory, id)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Printf("查询资产编目失败:%s", err.Error())
		}
		return res, err
	}
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return res, err
	}

	return r, nil
}

func compareAssetPro(auditConf AuditManager, propertyValueMap map[string]map[string]bool) bool {
	s, _ := json.Marshal(auditConf)
	logger.Info.Println("compareAssetPro  auditConf: ", string(s))
	logger.Info.Println("compareAssetPro  propertyValueMap: ", propertyValueMap)
	for _, assetMatch := range auditConf.AssetMatch {
		logger.Info.Println("compare assetName:", assetMatch.AssetName, "  | condition: ", assetMatch.Condition)
		tempValueMap := propertyValueMap[assetMatch.AssetName]
		var matchMap = make(map[string]bool)
		for _, matchV := range assetMatch.AssetValue {
			// id_fatherid_value
			tempValue := fmt.Sprintf("%s_%s", matchV.Id, matchV.Value)
			matchMap[tempValue] = true
		}
		flag := matchMapKey(tempValueMap, matchMap, assetMatch.Condition)
		if strings.ToUpper(auditConf.AssetOption) == "AND" {
			if flag == false {
				return false
			} else {
				continue
			}
		} else {
			if flag == true {
				return true
			} else {
				continue
			}
		}
	}
	if strings.ToUpper(auditConf.AssetOption) == "AND" {
		return true
	}
	return false
}

func AssetVerionDownload(sp AssetVersionSearch) (filename string, orifilename string, err error) {
	var headers []string
	var oridata [][][]string
	var alldata [][][]string
	var exportNum int         //聚合的列数
	var exportNums []int      //聚合的列数
	var exportRows []int      //聚合的行数
	var exportAllRows [][]int //聚合的行数
	var filenamepath string

	allinfo, filename, exportmodesign, exportfileformat, onlyHear, lenExport, valueList, err := AssetVersionDownloadForm(sp.ID)
	if err != nil {
		logger.Error.Printf("获取导出数据信息失败", err.Error())
		return filename, "", err
	}
	orifilename = filename
	if len(allinfo) == 0 {

		filename = tools.ReplacFileName(filename)
		filenamepath = common.AssetDownloadPath + filename

		switch exportfileformat {
		case "xlsx", "Excel":
			filename = filename + ".xlsx"
			orifilename = orifilename + ".xlsx"
			headers = onlyHear
			logger.Info.Println("headers:", onlyHear)
			err = ExcelFile(headers, alldata, exportNums, exportAllRows, filenamepath)
			if err != nil {
				logger.Error.Printf("生成xlsx数据信息失败", err.Error())
				return filename, orifilename, err
			}

		case "docx", "Word":
			return filename, orifilename, fmt.Errorf("No Word.")
		}
		return filename, orifilename, nil
	}
	//logger.Info.Println("len(allinfo):", len(allinfo))
	//logger.Info.Println("allinfo:", allinfo)
	//处理数据
	//先遍历出表头和数据
	//num最大值
	maxNum := 0
	mapinfos := make([]map[int]HeaderName, 0)
	for _, v := range allinfo {
		mapinfo := make(map[int]HeaderName)
		for _, v1 := range v {
			mapinfo[v1.Num] = v1
			if v1.Num > maxNum {
				maxNum = v1.Num
			}
		}
		mapinfos = append(mapinfos, mapinfo)
	}
	logger.Info.Println("maxNum", maxNum)

	logger.Info.Println("len(mapinfos):", len(mapinfos))
	logger.Info.Println("mapinfos:", mapinfos)

	tempNum := 0
	for i := 0; i <= maxNum; i++ {
		if v, ok := mapinfos[0][i]; ok {
			headers = append(headers, v.ExportHeaderName)
			if exportmodesign != "" && v.OriHeaderSign == exportmodesign {
				exportNum = tempNum
				exportNums = append(exportNums, exportNum)
				for i := 1; i < lenExport; i++ {
					exportNums = append(exportNums, exportNum+i)
				}
			}
			tempNum++
		}
	}
	//所有行数的值，并且找到聚合字段标识
	exportmodesignMaps := make(map[string][]int)
	exportmodesignMaps2 := make(map[string][]int)
	exportmodesignMaps3 := make(map[string][]int)
	exportmodesignMaps4 := make(map[string][]int)
	exportmodesignMaps5 := make(map[string][]int)
	var exportmodesignMapsNew []string
	var exportmodesignMapsNew2 []string
	var exportmodesignMapsNew3 []string
	var exportmodesignMapsNew4 []string
	var exportmodesignMapsNew5 []string

	for k, mapinfo := range mapinfos {
		var d [][]string
		for i := 0; i <= maxNum; i++ {
			if v, ok := mapinfo[i]; ok {
				if exportmodesign != "" && v.OriHeaderSign == exportmodesign {
					if len(v.ExportHeaderValues) > 0 {
						if _, ok2 := exportmodesignMaps[v.ExportHeaderValueID]; ok2 {
							if v.ExportHeaderValues[0] == " " || v.ExportHeaderValues[0] == "" {
								v.ExportHeaderValueID = "yijiID"
							}
							logger.Info.Println("idandk", v.ExportHeaderValueID, k)
							exportmodesignMaps[v.ExportHeaderValueID] = append(exportmodesignMaps[v.ExportHeaderValueID], k)
							exportmodesignMapsNew = addUnique(exportmodesignMapsNew, v.ExportHeaderValueID)
						} else {
							if v.ExportHeaderValues[0] == " " {
								v.ExportHeaderValueID = "yijiID"
							}
							exportmodesignMaps[v.ExportHeaderValueID] = append(exportmodesignMaps[v.ExportHeaderValueID], k)
							exportmodesignMapsNew = addUnique(exportmodesignMapsNew, v.ExportHeaderValueID)
						}
					}
				}
				if exportmodesign != "" && v.OriHeaderSign == "erjisx" {
					if len(v.ExportHeaderValues) > 0 {
						if _, ok2 := exportmodesignMaps2[v.ExportHeaderValueID]; ok2 {
							//if v.ExportHeaderValues[0] == " " || v.ExportHeaderValues[0] == "" {
							//	v.ExportHeaderValueID = "erjiID"
							//}
							exportmodesignMaps2[v.ExportHeaderValueID] = append(exportmodesignMaps2[v.ExportHeaderValueID], k)
							exportmodesignMapsNew2 = addUnique(exportmodesignMapsNew2, v.ExportHeaderValueID)
						} else {
							if v.ExportHeaderValues[0] == " " {
								v.ExportHeaderValueID = "erjiID"
							}
							exportmodesignMaps2[v.ExportHeaderValueID] = append(exportmodesignMaps2[v.ExportHeaderValueID], k)
							exportmodesignMapsNew2 = addUnique(exportmodesignMapsNew2, v.ExportHeaderValueID)
						}
					}

				}
				if exportmodesign != "" && v.OriHeaderSign == "sanjisx" {
					if len(v.ExportHeaderValues) > 0 {
						if _, ok2 := exportmodesignMaps3[v.ExportHeaderValueID]; ok2 {
							//if v.ExportHeaderValues[0] == " " || v.ExportHeaderValues[0] == "" {
							//	v.ExportHeaderValueID = "sanjiID"
							//}
							exportmodesignMaps3[v.ExportHeaderValueID] = append(exportmodesignMaps3[v.ExportHeaderValueID], k)
							exportmodesignMapsNew3 = addUnique(exportmodesignMapsNew3, v.ExportHeaderValueID)
						} else {
							if v.ExportHeaderValues[0] == " " {
								v.ExportHeaderValueID = "sanjiID"
							}
							exportmodesignMaps3[v.ExportHeaderValueID] = append(exportmodesignMaps3[v.ExportHeaderValueID], k)
							exportmodesignMapsNew3 = addUnique(exportmodesignMapsNew3, v.ExportHeaderValueID)
						}
					}
				}
				if exportmodesign != "" && v.OriHeaderSign == "sijisx" {
					if len(v.ExportHeaderValues) > 0 {
						if _, ok2 := exportmodesignMaps4[v.ExportHeaderValueID]; ok2 {
							//if v.ExportHeaderValues[0] == " " || v.ExportHeaderValues[0] == "" {
							//	v.ExportHeaderValueID = "sijiID"
							//}
							exportmodesignMaps4[v.ExportHeaderValueID] = append(exportmodesignMaps4[v.ExportHeaderValueID], k)
							exportmodesignMapsNew4 = addUnique(exportmodesignMapsNew4, v.ExportHeaderValueID)
						} else {
							if v.ExportHeaderValues[0] == " " {
								v.ExportHeaderValueID = "sijiID"
							}
							exportmodesignMaps4[v.ExportHeaderValueID] = append(exportmodesignMaps4[v.ExportHeaderValueID], k)
							exportmodesignMapsNew4 = addUnique(exportmodesignMapsNew4, v.ExportHeaderValueID)
						}
					}

				}
				if exportmodesign != "" && v.OriHeaderSign == "wujisx" {
					if len(v.ExportHeaderValues) > 0 {
						if _, ok2 := exportmodesignMaps5[v.ExportHeaderValueID]; ok2 {
							//if v.ExportHeaderValues[0] == " " || v.ExportHeaderValues[0] == "" {
							//	v.ExportHeaderValueID = "wujiID"
							//}
							exportmodesignMaps5[v.ExportHeaderValueID] = append(exportmodesignMaps5[v.ExportHeaderValueID], k)
							exportmodesignMapsNew5 = addUnique(exportmodesignMapsNew5, v.ExportHeaderValueID)
						} else {
							if v.ExportHeaderValues[0] == " " {
								v.ExportHeaderValueID = "wujiID"
							}
							exportmodesignMaps5[v.ExportHeaderValueID] = append(exportmodesignMaps5[v.ExportHeaderValueID], k)
							exportmodesignMapsNew5 = addUnique(exportmodesignMapsNew5, v.ExportHeaderValueID)
						}
					}

				}
				if len(v.ExportHeaderValues) == 0 {
					v.ExportHeaderValues = []string{" "}
				}
				d = append(d, v.ExportHeaderValues)
			}
		}
		oridata = append(oridata, d)
	}

	logger.Info.Println("exportmodesignMaps:", exportmodesignMaps)
	logger.Info.Println("exportmodesignMaps2:", exportmodesignMaps2)
	logger.Info.Println("exportmodesignMaps3:", exportmodesignMaps3)
	logger.Info.Println("exportmodesignMaps4:", exportmodesignMaps4)
	logger.Info.Println("exportmodesignMaps5:", exportmodesignMaps5)
	logger.Info.Println("exportmodesignMapsNew:", exportmodesignMapsNew)
	logger.Info.Println("exportmodesignMapsNew2:", exportmodesignMapsNew2)
	logger.Info.Println("exportmodesignMapsNew3:", exportmodesignMapsNew3)
	logger.Info.Println("exportmodesignMapsNew4:", exportmodesignMapsNew4)
	logger.Info.Println("exportmodesignMapsNew5:", exportmodesignMapsNew5)

	//排序
	logger.Info.Println("oridata", oridata)
	logger.Info.Println("exportmodesignMaps", exportmodesignMaps)
	var exportRows2 []int
	var exportRows3 []int
	var exportRows4 []int
	var exportRows5 []int
	var alldata2 [][][]string
	if exportmodesign != "" {
		for _, v := range exportmodesignMapsNew {
			if _, ok := exportmodesignMaps[v]; ok {
				m := 0
				if v != "yijiID" {
					for _, v2 := range exportmodesignMaps[v] {
						n := 0
						alldata = append(alldata, oridata[v2])
						for _, v3 := range oridata[v2] {
							if len(v3) > n {
								n = len(v3)
							}
						}
						m += n
					}
					exportRows = append(exportRows, m)
				} else {
					for _, v2 := range exportmodesignMaps[v] {
						alldata2 = append(alldata2, oridata[v2])
					}
				}

			}
		}
		logger.Info.Println("alldata2:", alldata2)
		alldata = append(alldata, alldata2...)
		if len(exportmodesignMapsNew2) > 0 {
			for key, value := range exportmodesignMaps2 {
				if key != "" && key != " " && key != "erjiID" {
					for _, v := range value {
						for k, val := range exportmodesignMaps {
							exportmodesignMaps[k] = removeElement(val, v)
						}
					}
					exportmodesignMaps[key] = value
				}
			}
			exportmodesignMaps2 = exportmodesignMaps
			exportmodesignMapsNew2 = append(exportmodesignMapsNew, exportmodesignMapsNew2...)
			sortByValues(exportmodesignMapsNew2, valueList)
			logger.Info.Println("exportmodesignMaps2:", exportmodesignMaps2)
			logger.Info.Println("exportmodesignMapsNew2:", exportmodesignMapsNew2)
			for _, v := range exportmodesignMapsNew2 {
				if _, ok := exportmodesignMaps2[v]; ok {
					m := 0
					if v != "yijiID" && v != "erjiID" {
						for _, v2 := range exportmodesignMaps2[v] {
							n := 0
							for _, v3 := range oridata[v2] {
								if len(v3) > n {
									n = len(v3)
								}
							}
							m += n
						}
						exportRows2 = append(exportRows2, m)
					}

				}
			}
			if len(exportmodesignMapsNew3) > 0 {
				for key, value := range exportmodesignMaps3 {
					if key != "" && key != " " && key != "sanjiID" {
						for _, v := range value {
							for k, val := range exportmodesignMaps2 {
								exportmodesignMaps2[k] = removeElement(val, v)
							}
						}
						exportmodesignMaps2[key] = value
					}
				}
				exportmodesignMaps3 = exportmodesignMaps2
				exportmodesignMapsNew3 = append(exportmodesignMapsNew2, exportmodesignMapsNew3...)
				sortByValues(exportmodesignMapsNew3, valueList)
				logger.Info.Println("exportmodesignMaps3", exportmodesignMaps3)
				logger.Info.Println("exportmodesignMapsNew3:", exportmodesignMapsNew3)
				for _, v := range exportmodesignMapsNew3 {
					if _, ok := exportmodesignMaps3[v]; ok {
						m := 0
						if v != "yijiID" && v != "erjiID" && v != "sanjiID" {
							for _, v2 := range exportmodesignMaps3[v] {
								n := 0
								for _, v3 := range oridata[v2] {
									if len(v3) > n {
										n = len(v3)
									}
								}
								m += n
							}
							exportRows3 = append(exportRows3, m)
						}

					}
				}
			}
			if len(exportmodesignMapsNew4) > 0 {
				for key, value := range exportmodesignMaps4 {
					if key != "" && key != " " && key != "sijiID" {
						for _, v := range value {
							for k, val := range exportmodesignMaps3 {
								exportmodesignMaps3[k] = removeElement(val, v)
							}
						}
						exportmodesignMaps3[key] = value
					}
				}
				exportmodesignMaps4 = exportmodesignMaps3
				exportmodesignMapsNew4 = append(exportmodesignMapsNew3, exportmodesignMapsNew4...)
				sortByValues(exportmodesignMapsNew4, valueList)
				logger.Info.Println("exportmodesignMaps4:", exportmodesignMaps4)
				logger.Info.Println("exportmodesignMapsNew4:", exportmodesignMapsNew4)
				for _, v := range exportmodesignMapsNew4 {
					if _, ok := exportmodesignMaps4[v]; ok {
						m := 0
						if v != "yijiID" && v != "erjiID" && v != "sanjiID" && v != "sijiID" {
							for _, v2 := range exportmodesignMaps4[v] {
								n := 0
								for _, v3 := range oridata[v2] {
									if len(v3) > n {
										n = len(v3)
									}
								}
								m += n
							}
							exportRows4 = append(exportRows4, m)
						}

					}
				}
				if len(exportmodesignMapsNew5) > 0 {
					for key, value := range exportmodesignMaps5 {
						if key != "" && key != " " && key != "wujiID" {
							for _, v := range value {
								for k, val := range exportmodesignMaps4 {
									exportmodesignMaps4[k] = removeElement(val, v)
								}
							}
							exportmodesignMaps4[key] = value
						}
					}
					exportmodesignMaps5 = exportmodesignMaps4
					exportmodesignMapsNew5 = append(exportmodesignMapsNew4, exportmodesignMapsNew5...)
					sortByValues(exportmodesignMapsNew5, valueList)
					logger.Info.Println("exportmodesignMaps5:", exportmodesignMaps5)
					logger.Info.Println("exportmodesignMapsNew5:", exportmodesignMapsNew5)
					for _, v := range exportmodesignMapsNew5 {
						if _, ok := exportmodesignMaps5[v]; ok {
							m := 0
							if v != "yijiID" && v != "erjiID" && v != "sanjiID" && v != "sijiID" && v != "wujiID" {
								for _, v2 := range exportmodesignMaps5[v] {
									n := 0
									for _, v3 := range oridata[v2] {
										if len(v3) > n {
											n = len(v3)
										}
									}
									m += n
								}
								exportRows5 = append(exportRows5, m)
							}

						}
					}
				}
			}
		}

		exportAllRows = append(exportAllRows, exportRows)
		if len(exportRows2) > 0 {
			exportAllRows = append(exportAllRows, exportRows2)
			if len(exportRows3) > 0 {
				exportAllRows = append(exportAllRows, exportRows3)
				if len(exportRows4) > 0 {
					exportAllRows = append(exportAllRows, exportRows4)
					if len(exportRows5) > 0 {
						exportAllRows = append(exportAllRows, exportRows5)
					}
				}
			}
		}

		if len(alldata) == 0 {
			alldata = oridata
		}
	} else {
		alldata = oridata
	}

	logger.Info.Println("headers:", headers)
	logger.Info.Println("alldata:", alldata)
	logger.Info.Println("exportNum:", exportNum)
	logger.Info.Println("exportNums:", exportNums)
	logger.Info.Println("exportRows:", exportRows)
	logger.Info.Println("exportAllRows:", exportAllRows)

	filename = tools.ReplacFileName(filename)
	filenamepath = common.AssetDownloadPath + filename

	switch exportfileformat {
	case "xlsx", "Excel":
		filename = filename + ".xlsx"
		orifilename = orifilename + ".xlsx"
		err = ExcelFile(headers, alldata, exportNums, exportAllRows, filenamepath)
		if err != nil {
			logger.Error.Printf("生成xlsx数据信息失败", err.Error())
			return filename, orifilename, err
		}

	case "docx", "Word":
		//写文件
		filePath := "/etc/dt.d/danastudio/assetdownloaddocx.txt"

		fileContent := ""
		for _, arr2D := range alldata {
			jsonBytes, err := json.Marshal(arr2D)
			if err != nil {
				fmt.Println("Error marshaling array:", err)
				return filename, orifilename, err
			}

			fileContent += string(jsonBytes) + "\n"
		}

		err = ioutil.WriteFile(filePath, []byte(fileContent), 0644)
		if err != nil {
			fmt.Println("Error writing file:", err)
			return filename, orifilename, err
		}

		filename = filename + ".docx"
		orifilename = orifilename + ".docx"
		// 设置Python脚本和参数
		pythonScript := "/etc/dt.d/danastudio/assetdownloaddocx.py"

		// 构建命令和参数
		var args []string
		args = append(args, pythonScript)
		args = append(args, "-headers")
		for _, v := range headers {
			args = append(args, `"`+v+`"`)
		}

		/*	args = append(args, "-alldata")
			// 转换为JSON字符串
			jsonData, err := json.Marshal(alldata)
			if err != nil {
				fmt.Println("JSON序列化失败:", err)
				return filename, err
			}
			args = append(args, `"`+strings.ReplaceAll(string(jsonData), `"`, `'`)+`"`) // 在这里添加双引号和转义*/
		args = append(args, "-exportnum")
		args = append(args, strconv.Itoa(exportNum))
		args = append(args, "-exportrows")
		for _, row := range exportRows {
			args = append(args, strconv.Itoa(row))
		}
		args = append(args, "-filename")
		args = append(args, `"`+filenamepath+`"`) // 在这里添加双引号

		// 打印执行的命令语句
		command := "python3 " + strings.Join(args, " ")
		for _, v := range args {
			fmt.Println(v)
		}
		fmt.Println("执行的命令语句:", command)

		outputerr, output, err := Exepython(command)
		if err != nil {
			fmt.Printf("执行Python脚本出错：%v%s", err)
			logger.Error.Printf("执行Python脚本出错：%v%s", err)
			return filename, orifilename, err
		}
		// 输出Python脚本的执行结果
		fmt.Println(string(output))
		fmt.Println("-----")
		fmt.Println(string(outputerr))
	}
	//logger.Info.Println("orifilename:", orifilename)
	return filename, orifilename, nil
}
func addUnique(items []string, newItem string) []string {
	seen := make(map[string]bool)
	for _, item := range items {
		seen[item] = true
	}

	if !seen[newItem] && newItem != "" {
		items = append(items, newItem)
	}
	return items
}
func removeElement(slice []int, elem int) []int {
	index := -1
	for i, v := range slice {
		if v == elem {
			index = i
			break
		}
	}
	if index == -1 {
		return slice
	}
	return append(slice[:index], slice[index+1:]...)
}

func Exepython(command string) (string, string, error) {
	fmt.Println("执行命令:", command)

	cmd := exec.Command("/bin/bash", "-c", command)
	stdout, err := cmd.Output()
	stderr := string(stdout)
	if err != nil {
		fmt.Println("执行命令出错: ", err.Error())
		return "", stderr, err
	}

	return stderr, string(stdout), nil
}

func ExcelFile(headers []string, alldata [][][]string, exportNum []int, exportRows [][]int, filename string) error {

	file := excelize.NewFile()

	// 创建一个新的工作表
	sheet := file.NewSheet("Sheet1")

	// 创建样式
	style, err := file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
		},
	})
	if err != nil {
		logger.Error.Println("创建样式失败,err:", err)
		return err
	}

	// 表头数据
	//headers := []string{"姓名", "年龄", "性别"}

	// 将表头数据写入工作表的第一行
	/*for i, header := range headers {
		cell := fmt.Sprintf("%c%d", 'A'+i, 1) // 根据索引生成单元格地址
		file.SetCellValue("Sheet1", cell, header)
	}*/
	for i, header := range headers {
		cell := indexToColumnName(i+1) + "1"
		err = file.SetCellValue("Sheet1", cell, header)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return err
		}
	}
	if len(alldata) > 0 {
		//// 字段信息
		//data := [][]string{
		//	{"张三", "25", "男"},
		//	{"李四", "30", "男", "工程师"},
		//	{"王五", "28", "女", "教师", "北京"},
		//}
		//num := 2
		//for _, data := range alldata {
		//	// 分批将字段信息写入工作表，同时合并单元格
		//	orinum := num
		//	maxnum := 0
		//	for rowIndex, row := range data {
		//		num = orinum
		//		rownum := 0
		//		for _, value := range row {
		//			//cell := fmt.Sprintf("%c%d", 'A'+rowIndex, num) // 根据索引生成单元格地址，行索引加2是因为表头已占用第一行
		//			cell := indexToColumnName(rowIndex+1) + fmt.Sprintf("%d", num) // 根据索引生成单元格地址，行索引加2是因为表头已占用第一行
		//			err = file.SetCellValue("Sheet1", cell, value)
		//			if err != nil {
		//				logger.Error.Println("写入值失败err:", err)
		//				return err
		//			}
		//			err = file.SetCellStyle("Sheet1", cell, cell, style)
		//			if err != nil {
		//				logger.Error.Println("写入表格类型err:", err)
		//				return err
		//			}
		//			num++
		//			rownum++
		//		}
		//		if rownum > maxnum {
		//			maxnum = rownum
		//		}
		//	}
		//	logger.Info.Println("data:", data)
		//	//合并单元格
		//	for colIndex := range headers {
		//		//logger.Info.Println("colIndex:", colIndex)
		//		if len(data[colIndex]) == 1 {
		//			//cell1 := fmt.Sprintf("%c%d", 'A'+colIndex, orinum)          // 合并单元格的起始地址
		//			//cell2 := fmt.Sprintf("%c%d", 'A'+colIndex, orinum+maxnum-1) // 合并单元格的结束地址
		//			cell1 := indexToColumnName(colIndex+1) + fmt.Sprintf("%d", orinum)          // 合并单元格的起始地址
		//			cell2 := indexToColumnName(colIndex+1) + fmt.Sprintf("%d", orinum+maxnum-1) // 合并单元格的结束地址
		//			err = file.MergeCell("Sheet1", cell1, cell2)
		//			if err != nil {
		//				logger.Error.Println("合并每行单元格失败err:", err)
		//				return err
		//			}
		//		}
		//	}
		//	num = orinum + maxnum
		//}
		num := 2
		for _, data := range alldata {
			// 将每行数据转换为一整行切片

			maxlen := 0
			for _, row := range data {
				//for _, value := range row {
				//	rowData = append(rowData, value)
				//}
				//rowData = append(rowData, row)
				if len(row) > maxlen {
					maxlen = len(row)
				}
			}
			if maxlen >= 1 {
				for i := 0; i < maxlen; i++ {
					rowData := make([]interface{}, 0)
					logger.Info.Println("data:", data)
					for _, row := range data {
						if len(row) > i {
							rowData = append(rowData, row[i])
						} else if len(row) == 1 {
							rowData = append(rowData, row[0])
						} else {
							rowData = append(rowData, " ")
						}
					}
					// 一次性写入整行数据
					logger.Info.Println("rowData:", rowData)
					cell := fmt.Sprintf("A%d", num)
					if err := file.SetSheetRow("Sheet1", cell, &rowData); err != nil {
						logger.Error.Println("写入值失败err:", err)
						return err
					}
					num++
				}

			}

		}
		num = 2
		for _, data := range alldata {
			orinum := num
			maxnum := 0
			for _, row := range data {
				num = orinum
				rownum := 0
				for _, _ = range row {
					//cell := fmt.Sprintf("%c%d", 'A'+rowIndex, num) // 根据索引生成单元格地址，行索引加2是因为表头已占用第一行
					//cell := indexToColumnName(rowIndex+1) + fmt.Sprintf("%d", num) // 根据索引生成单元格地址，行索引加2是因为表头已占用第一行
					//err = file.SetCellValue("Sheet1", cell, value)
					//if err != nil {
					//	logger.Error.Println("写入值失败err:", err)
					//	return err
					//}
					//err = file.SetCellStyle("Sheet1", cell, cell, style)
					//if err != nil {
					//	logger.Error.Println("写入表格类型err:", err)
					//	return err
					//}
					num++
					rownum++
				}
				if rownum > maxnum {
					maxnum = rownum
				}
			}
			logger.Info.Println("data:", data)
			//合并单元格
			for colIndex := range headers {
				//logger.Info.Println("colIndex:", colIndex)
				if len(data[colIndex]) == 1 {
					//cell1 := fmt.Sprintf("%c%d", 'A'+colIndex, orinum)          // 合并单元格的起始地址
					//cell2 := fmt.Sprintf("%c%d", 'A'+colIndex, orinum+maxnum-1) // 合并单元格的结束地址
					cell1 := indexToColumnName(colIndex+1) + fmt.Sprintf("%d", orinum)          // 合并单元格的起始地址
					cell2 := indexToColumnName(colIndex+1) + fmt.Sprintf("%d", orinum+maxnum-1) // 合并单元格的结束地址
					err = file.MergeCell("Sheet1", cell1, cell2)
					if err != nil {
						logger.Error.Println("合并每行单元格失败err:", err)
						return err
					}
				}
			}
			num = orinum + maxnum

			// 统一设置样式
			//for colIndex := range headers {
			//	if len(data[colIndex]) == 1 {
			//		cell1 := indexToColumnName(colIndex+1) + fmt.Sprintf("%d", num)
			//		cell2 := indexToColumnName(colIndex+1) + fmt.Sprintf("%d", num)
			//		if err := file.MergeCell("Sheet1", cell1, cell2); err != nil {
			//			logger.Error.Println("合并每行单元格失败err:", err)
			//			return err
			//		}
			//		// 设置样式
			//		if err := file.SetCellStyle("Sheet1", cell1, cell2, style); err != nil {
			//			logger.Error.Println("设置样式失败err:", err)
			//			return err
			//		}
			//	}
			//}

			//num++
		}

		// 设置全局样式
		for rowIndex := 2; rowIndex < num; rowIndex++ { // 从第二行开始
			for colIndex := 0; colIndex < len(headers); colIndex++ {
				cell := fmt.Sprintf("%s%d", indexToColumnName(colIndex+1), rowIndex) // 生成单元格地址
				if err := file.SetCellStyle("Sheet1", cell, cell, style); err != nil {
					logger.Error.Println("设置样式失败err:", err)
					return err
				}
			}
		}

		//合并聚合的单元格
		if len(exportRows) == len(exportNum) {
			logger.Info.Println("开始合并聚合")
			for k, row := range exportRows {
				startrow := 2
				for _, row2 := range row {
					if row2 > 0 {
						//cell1 := fmt.Sprintf("%c%d", 'A'+exportNum, startrow)       // 合并单元格的起始地址
						//cell2 := fmt.Sprintf("%c%d", 'A'+exportNum, startrow+row-1) // 合并单元格的结束地址
						cell1 := indexToColumnName(exportNum[k]+1) + fmt.Sprintf("%d", startrow)        // 合并单元格的起始地址
						cell2 := indexToColumnName(exportNum[k]+1) + fmt.Sprintf("%d", startrow+row2-1) // 合并单元格的结束地址
						err = file.MergeCell("Sheet1", cell1, cell2)
						if err != nil {
							logger.Error.Println("合并聚合单元格失败err:", err)
							return err
						}
						startrow += row2
					}

				}
			}
		}

	}

	// 设置默认活动工作表
	file.SetActiveSheet(sheet)

	// 保存文件
	//filename = common.AssetDownloadPath + filename
	if err := file.SaveAs(filename + ".xlsx"); err != nil {
		logger.Error.Println("保存文件出错：", err)
		return err
	}

	logger.Info.Println("xlsx 文件创建成功")

	return nil

}

func indexToColumnName(index int) string {
	columnName := ""
	for index > 0 {
		index--
		columnName = string(rune(index%26)+'A') + columnName
		index /= 26
	}
	return columnName
}

func AssetVersionDownloadForm(id string) (allinfo [][]HeaderName, filename string, exportmodesign string, format string, onlyHear []string, lenExport int, valueList []string, err error) {
	strInfo, err := esutil.SearchByID(common.DataBaseModelAsset, common.TbAssetCatalogVersion, id)
	if err != nil {
		logger.Error.Printf("查询共享开放目录中详情查询失败:%s", err.Error())
		return allinfo, "", "", "", []string{}, lenExport, valueList, err
	}
	var r AssetCategoryVerion
	err = json.Unmarshal([]byte(strInfo), &r)
	if err != nil {
		return allinfo, "", "", "", []string{}, lenExport, valueList, err
	}
	exportmodesign = r.ExportModeSign
	oriInfo := make(map[string]HeaderName)
	for _, v := range r.HeaderName {
		if v.Displayed {
			oriInfo[v.OriHeaderSign] = v
			onlyHear = append(onlyHear, v.ExportHeaderName)
		}

	}
	logger.Info.Println("oriInfo:", oriInfo)
	var body gin.H
	if len(r.AssetInventoryIDs) > 0 {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"terms": gin.H{
							"inventoryid": r.AssetInventoryIDs,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
	} else {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"projectid": r.ProjectID,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
	}

	var assetinfo []AssetInventory

	strInfo2, err := esutil.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产编目详情查询失败:%s", err.Error())
		return allinfo, r.AssetCategoryName, r.ExportModeSign, r.ExportFileFormat, onlyHear, lenExport, valueList, err
	}

	data := esutil.GetSource(strInfo2)
	for _, value := range data {
		var r AssetInventory
		_ = json.Unmarshal([]byte(value), &r)
		assetinfo = append(assetinfo, r)
	}
	logger.Info.Println("lenassetinfo:", len(assetinfo))
	//取聚合方式排序
	var isO bool
	idandfa := make(map[string]string)
	idandvalue := make(map[string]string)

	if len(assetinfo) > 0 && r.ExportModeSign != "" {
		for _, v := range assetinfo[0].AssetCatalog.CustomInfo {
			for _, v1 := range v.CustomProperties {
				if v1.ProSign == r.ExportModeSign {
					for _, v2 := range v1.MulitiEnumRated {
						idandfa[v2.ID] = v2.FatherID
						idandvalue[v2.ID] = v2.Value
					}
					isO = true
					break
				}
			}
			if isO {
				break
			}
		}
	}
	// 存储结果
	numMap := make(map[string]int)
	//新的排序
	thisExportMap := make(map[string]thisExport)

	logger.Info.Println("idandfa:", idandfa)
	// 遍历 ID，分配 Num
	for id, fatherID := range idandfa {
		if fatherID == "" {
			numMap[id] = 1 // 顶层 ID
		}
		thisExportMap[id] = thisExport{
			Id:     id,
			Father: fatherID,
			Num:    1,
			Values: []string{idandvalue[id]},
			IDS:    []string{id},
		}
		lenExport = 1
	}
	for i := 2; i <= 5; i++ {
		for id, fatherID := range idandfa {
			var l []string
			if _, exists := numMap[fatherID]; exists && numMap[fatherID] == i-1 {
				numMap[id] = i // 子节点层级加1
				thisExportMap[id] = thisExport{
					Id:     id,
					Father: fatherID,
					Num:    i,
					Values: append(thisExportMap[fatherID].Values, idandvalue[id]),
					IDS:    append(thisExportMap[fatherID].IDS, id),
					Value:  idandvalue[id],
				}
				l = append(l, idandvalue[id])
				if lenExport < i {
					lenExport = i
				}
			}
		}
	}
	logger.Info.Println("lenExport:", lenExport)
	logger.Info.Println("thisExportMap:", thisExportMap)

	logger.Info.Println("numMap:", numMap)
	// 构建结果
	type headerStruct struct {
		a   string
		b   string
		c   string
		d   string
		e   string
		aid string
		bid string
		cid string
		did string
		eid string
		ID  string
	}
	var headerInfos []headerStruct
	//var tableExportList []int
	allinfos := make(map[string][]HeaderName)
	var exportNum int
	for _, r2 := range assetinfo {
		var res []HeaderName
		for _, v := range r2.AssetCatalog.CustomInfo {
			for _, v2 := range v.CustomProperties {
				if v3, ok := oriInfo[v2.ProSign]; ok {
					if v2.ProValue != "" {
						v3.ExportHeaderValues = []string{v2.ProValue}
					} else if len(v2.ProValues) > 0 {
						var newStr string
						for k4, v4 := range v2.ProValues {
							if k4 == 0 {
								newStr = fmt.Sprintf(v4)
							} else {
								newStr = fmt.Sprintf(newStr + "、" + v4)
							}
						}
						v3.ExportHeaderValues = []string{newStr}
					} else {
						v3.ExportHeaderValues = []string{" "}
					}
					if r.ExportModeSign != "" {
						if v2.ProSign == r.ExportModeSign {
							//获取第一层的值
							var pvalues, pids []string
							var headerInfo headerStruct
							exportNum = v3.Num
							if (v2.ProValue != "" || len(v2.ProValues) != 0) && len(v2.MulitiSelected) == 1 {
								logger.Info.Println("进入到这一层了")
								if p, ok := thisExportMap[v2.MulitiSelected[0]]; ok {
									logger.Info.Println("进入到一层层了")
									logger.Info.Println("v2：", v2)
									pvalues = p.Values
									pids = p.IDS
									if len(p.Values) > 0 {
										v3.ExportHeaderValues = []string{p.Values[0]}
										headerInfo.a = p.Values[0]
										headerInfo.aid = p.IDS[0]
										v3.ExportHeaderValueID = p.IDS[0]
									} else {
										logger.Info.Println("还是在这里吗？")
										logger.Info.Println("v22：", v2)
										v3.ExportHeaderValues = []string{}
										headerInfo.a = ""
										headerInfo.aid = "yijiID"
										v3.ExportHeaderValueID = "yijiID"
									}
								}
							} else {
								logger.Info.Println("在这里吗？")
								logger.Info.Println("v222：", v2)
								headerInfo.a = ""
								//headerInfo.aid = "yijiID"
								//v3.ExportHeaderValueID = "yijiID"
								//v3.ExportHeaderValues = []string{" "}
							}

							res = append(res, v3)
							logger.Info.Println("pvalues:", pvalues)
							if lenExport > 1 {
								var hs []HeaderName
								//在设置的多级目录的第一级后加
								for j := 2; j <= 5 || j <= lenExport; j++ {
									var h HeaderName
									switch j {
									case 2:
										if lenExport == 2 {
											h.ExportHeaderName = "子" + v3.ExportHeaderName
										} else {
											h.ExportHeaderName = "二级" + v3.ExportHeaderName
										}
										h.Num = v3.Num + 1
										h.OriHeaderSign = "erjisx"
										if len(pvalues) > 1 {
											h.ExportHeaderValues = []string{pvalues[1]}
											headerInfo.b = pvalues[1]
											headerInfo.bid = pids[1]
											h.ExportHeaderValueID = pids[1]
										} else {
											h.ExportHeaderValues = []string{" "}
											headerInfo.b = ""
											//headerInfo.bid = "erjiID"
											//h.ExportHeaderValueID = "erjiID"
										}
									case 3:
										h.ExportHeaderName = "三级" + v3.ExportHeaderName
										h.Num = v3.Num + 2
										h.OriHeaderSign = "sanjisx"
										if len(pvalues) > 2 {
											h.ExportHeaderValues = []string{pvalues[2]}
											headerInfo.c = pvalues[2]
											//headerInfo.cid = pids[2]
											//h.ExportHeaderValueID = pids[2]
										} else {
											h.ExportHeaderValues = []string{" "}
											headerInfo.c = ""
											//headerInfo.cid = "sanjiID"
											//h.ExportHeaderValueID = "sanjiID"
										}
									case 4:
										h.ExportHeaderName = "四级" + v3.ExportHeaderName
										h.Num = v3.Num + 3
										h.OriHeaderSign = "sijisx"
										if len(pvalues) > 3 {
											h.ExportHeaderValues = []string{pvalues[3]}
											headerInfo.d = pvalues[3]
											headerInfo.did = pids[3]
											h.ExportHeaderValueID = pids[3]
										} else {
											h.ExportHeaderValues = []string{" "}
											headerInfo.d = ""
											//headerInfo.did = "sijiID"
											//h.ExportHeaderValueID = "sijiID"
										}
									case 5:
										h.ExportHeaderName = "五级" + v3.ExportHeaderName
										h.Num = v3.Num + 4
										h.OriHeaderSign = "wujisx"
										if len(pvalues) > 4 {
											h.ExportHeaderValues = []string{pvalues[4]}
											headerInfo.e = pvalues[4]
											headerInfo.eid = pids[4]
											h.ExportHeaderValueID = pids[4]
										} else {
											h.ExportHeaderValues = []string{" "}
											headerInfo.e = ""
											//headerInfo.eid = "wujiID"
											//h.ExportHeaderValueID = "wujiID"
										}
									}
									//logger.Info.Println("hhhhh:", h)
									hs = append(hs, h)
									//logger.Info.Println("hssss000:", hs)
								}
								//logger.Info.Println("hssss:", hs)
								headerInfo.ID = r2.InventoryID
								headerInfos = append(headerInfos, headerInfo)
								res = append(res, hs[:lenExport-1]...)
							}
						} else {
							if lenExport > 1 && v3.Num > exportNum && exportNum != 0 {
								v3.Num = v3.Num + lenExport - 1
							}
							res = append(res, v3)
						}
					} else {
						res = append(res, v3)
					}
				}
			}
		}
		if v, ok := oriInfo["信息项"]; ok {
			for _, v1 := range r2.AssetInfo {
				if v1.InfoItemsName == "" {
					v1.InfoItemsName = " "
				}
				v.ExportHeaderValues = append(v.ExportHeaderValues, v1.InfoItemsName)
			}
			if lenExport > 1 {
				v.Num = v.Num + lenExport - 1
			}
			res = append(res, v)
		}
		if v, ok := oriInfo["字段英文名称"]; ok {
			var isT bool
			for _, v1 := range r2.AssetInfo {
				if v1.OriName == "" {
					v1.OriName = " "
				} else {
					isT = true
				}
				v.ExportHeaderValues = append(v.ExportHeaderValues, v1.OriName)
			}
			if !isT {
				f, err := getOriname(r2.InventoryID)
				if err != nil {
					logger.Error.Printf("获取资产字段名称信息失败:%s", err.Error())
					//return allinfo1, r.AssetCategoryName, r.ExportModeSign, r.ExportFileFormat, onlyHear, err
					//资产挂载删除挂载表之后查询不报错
					f = []string{""}
				}
				if len(f) > 0 {
					v.ExportHeaderValues = f
				}
			}
			if lenExport > 1 {
				v.Num = v.Num + lenExport - 1
			}
			res = append(res, v)
		}

		if v, ok := oriInfo["字段名称"]; ok {
			for _, v1 := range r2.AssetInfo {
				v.ExportHeaderValues = append(v.ExportHeaderValues, v1.InfoItemsName)
			}
			if lenExport > 1 {
				v.Num = v.Num + lenExport - 1
			}
			res = append(res, v)
		}
		if v, ok := oriInfo["类型"]; ok {
			for _, v1 := range r2.AssetInfo {
				v.ExportHeaderValues = append(v.ExportHeaderValues, v1.InfoItemsType)
			}
			if lenExport > 1 {
				v.Num = v.Num + lenExport - 1
			}
			res = append(res, v)
		}
		if v, ok := oriInfo["资产发布时间"]; ok {
			v.ExportHeaderValues = append(v.ExportHeaderValues, r2.PutInfo.Put_OnTime)
			if lenExport > 1 {
				v.Num = v.Num + lenExport - 1
			}
			res = append(res, v)
		}
		if v, ok := oriInfo["数据最近更新时间"]; ok {
			v.ExportHeaderValues = append(v.ExportHeaderValues, r2.UpdatedTime)
			if lenExport > 1 {
				v.Num = v.Num + lenExport - 1
			}
			res = append(res, v)
		}

		allinfos[r2.InventoryID] = res

	}
	logger.Info.Println("allinfos:", allinfos)
	logger.Info.Println("lenallinfos:", len(allinfos))
	//排序
	//var allinfo [][]HeaderName
	//var a1, a2, a3, a4, a5, a6, a7 [][]HeaderName
	//logger.Info.Println("tableExportList:", tableExportList)
	//if len(allinfo1) == len(tableExportList) {
	//	for k, v := range allinfo1 {
	//		switch tableExportList[k] {
	//		case 1:
	//			a1 = append(a1, v)
	//		case 2:
	//			a2 = append(a2, v)
	//		case 3:
	//			a3 = append(a3, v)
	//		case 4:
	//			a4 = append(a4, v)
	//		case 5:
	//			a5 = append(a5, v)
	//		case 9999:
	//			a6 = append(a6, v)
	//		default:
	//			a7 = append(a7, v)
	//		}
	//	}
	//	allinfo = append(allinfo, a1...)
	//	allinfo = append(allinfo, a2...)
	//	allinfo = append(allinfo, a3...)
	//	allinfo = append(allinfo, a4...)
	//	allinfo = append(allinfo, a5...)
	//	allinfo = append(allinfo, a6...)
	//	allinfo = append(allinfo, a7...)
	//}

	// 使用sort.Slice进行排序
	logger.Info.Println("headerInfos:", headerInfos)
	if len(headerInfos) > 0 {
		sort.Slice(headerInfos, func(i, j int) bool {
			// 先按a字段排序，如果a字段相同，则按b字段排序，如果b字段相同，则按c字段排序
			if headerInfos[i].a != headerInfos[j].a {
				if headerInfos[i].a == "" {
					return false
				}
				if headerInfos[j].a == "" {
					return true
				}
				return headerInfos[i].a < headerInfos[j].a
			}
			if headerInfos[i].b != headerInfos[j].b {
				if headerInfos[i].b == "" {
					return false
				}
				if headerInfos[j].b == "" {
					return true
				}
				return headerInfos[i].b < headerInfos[j].b
			}
			if headerInfos[i].c != headerInfos[j].c {
				if headerInfos[i].c == "" {
					return false
				}
				if headerInfos[j].c == "" {
					return true
				}
				return headerInfos[i].c < headerInfos[j].c
			}
			if headerInfos[i].d != headerInfos[j].d {
				if headerInfos[i].d == "" {
					return false
				}
				if headerInfos[j].d == "" {
					return true
				}
				return headerInfos[i].d < headerInfos[j].d
			}

			if headerInfos[i].e == "" {
				return false
			}
			if headerInfos[j].e == "" {
				return true
			}

			return headerInfos[i].e < headerInfos[j].e
		})
		logger.Info.Println("headerInfos2:", headerInfos)
		for _, v := range headerInfos {
			if vv, ok := allinfos[v.ID]; ok {
				allinfo = append(allinfo, vv)
				//allinfos[v.ID] = []HeaderName{}
				delete(allinfos, v.ID)
			}
		}
		for _, v := range allinfos {
			allinfo = append(allinfo, v)
		}
		var valueList1 []string
		for _, v := range headerInfos {
			valueList1 = addUnique(valueList1, v.aid)
			valueList1 = addUnique(valueList1, v.bid)
			valueList1 = addUnique(valueList1, v.cid)
			valueList1 = addUnique(valueList1, v.did)
			valueList1 = addUnique(valueList1, v.eid)
		}

		for _, v := range valueList1 {
			valueList = append(valueList, v, "")
		}
	} else {
		for _, v := range allinfos {
			allinfo = append(allinfo, v)
		}
	}

	nn := 1
	for k, v := range allinfo {
		a := HeaderName{
			ExportHeaderName:   "序号",
			Displayed:          true,
			ExportHeaderValues: []string{strconv.Itoa(nn)},
		}
		var aa []HeaderName
		aa = append(aa, a)
		aa = append(aa, v...)
		allinfo[k] = aa
		nn++
	}
	logger.Info.Println("valueList:", valueList)
	logger.Info.Println("allinfo this:", allinfo)
	logger.Info.Println("lenallinfo this:", len(allinfo))
	return allinfo, r.AssetCategoryName, r.ExportModeSign, r.ExportFileFormat, onlyHear, lenExport, valueList, nil
}

type thisExport struct {
	Id     string
	Father string
	Num    int
	Values []string
	Value  string
	IDS    []string
}

func getNum() {
	idandfa := make(map[string]string)
	// 存储结果
	numMap := make(map[string]int)

	// 遍历 ID，分配 Num
	for id, fatherID := range idandfa {
		if fatherID == "" {
			numMap[id] = 1 // 顶层 ID
		} else {
			if num, exists := numMap[fatherID]; exists {
				numMap[id] = num + 1 // 子节点层级加1
			}
		}
	}
}

func getOriname(id string) (fieldnames []string, err error) {
	info, err := GetInventoryAllInfo(id)
	if err != nil {
		logger.Error.Println(err)
		return fieldnames, err
	}

	//修正挂载的fieldinfo
	if info.AttachInfo.AttachTableInfo.Id != "" {
		info.AttachInfo.AttachTableInfo.OnlyAttachFieldinfo, err = SyncAttachFieldInfo(info.AttachInfo.AttachTableInfo.Id, info.AttachInfo.AttachTableInfo.OnlyAttachFieldinfo)
		if err != nil {
			logger.Error.Println(err)
			return fieldnames, err
		}
		for _, v := range info.AttachInfo.AttachTableInfo.OnlyAttachFieldinfo {
			if v.Name == "" {
				v.Name = " "
			}
			fieldnames = append(fieldnames, v.Name)
		}
		return fieldnames, nil
	} else {
		return []string{}, nil
	}

}

// 修正挂载
func SyncAttachFieldInfo(tbid string, old []MetaSourceFieldInfo) ([]MetaSourceFieldInfo, error) {
	var newFieldInfo = make([]MetaSourceFieldInfo, 0)
	var fieldMap = make(map[string]MetaSourceFieldInfo, 0)
	fieldRes, _, _, err := MoneyTableFieldInfo(source.MoneyTableDetail{}, tbid)
	if err != nil {
		return newFieldInfo, err
	}

	for _, v := range fieldRes {
		var thisInfo MetaSourceFieldInfo
		thisInfo.Number = v.Number
		thisInfo.Annotation = v.Annotation
		thisInfo.Name = v.Name
		thisInfo.FieldType = v.FieldType
		thisInfo.RankNum = v.RankNum
		thisInfo.RankName = v.RankName
		thisInfo.IsPrim = v.IsPrim
		thisInfo.IsUnique = v.IsUnique
		thisInfo.IsPartition = v.IsPartition
		fieldMap[v.Name] = thisInfo
	}

	for _, v := range old {
		newFieldInfo = append(newFieldInfo, fieldMap[v.Name])
	}

	return newFieldInfo, nil
}

func MoneyTableFieldInfo(s source.MoneyTableDetail, id string) ([]source.MetaSourceFieldInfo, []source.PartitionFieldInfo, map[string]interface{}, error) {

	var sp = make([]source.MetaSourceFieldInfo, 0)
	var spp = make([]source.PartitionFieldInfo, 0)
	var typeCastMap = make(map[string]interface{})

	strinfo, err := esutil.SearchByID(common.DatabaseMetadata, common.TbSourcetb, id)
	if err != nil {
		logger.Error.Printf("列举表失败:%s", err.Error())
		return sp, spp, nil, err
	}
	var a source.MetaSourceTb

	err = json.Unmarshal([]byte(strinfo), &a)
	if err != nil {
		logger.Error.Printf("解析表数据失败:%s", err.Error())
		return sp, spp, nil, err
	}
	//========================
	for k, v := range a.FieldInfo {
		if s.NeedCast {
			typeCastMap[v.Name] = tools.ConversionType(a.DBType, v.FieldType)
		}

		a.FieldInfo[k].InfoItemsType = tools.SingleField2Chinese(v.FieldType)

		a.FieldInfo[k].RankNum = 0
		a.FieldInfo[k].RankName = "未识别"
	}
	if len(a.FieldInfo) != 0 && a.FieldInfo[0].Number == 0 {
		for i := 0; i < len(a.FieldInfo); i++ {
			a.FieldInfo[i].Number = i + 1
		}
	}
	//========================
	return a.FieldInfo, a.PartitionField, typeCastMap, nil
}

func matchMapKey(proValueMap, matchMap map[string]bool, condition string) bool {
	logger.Info.Println("matchMapKey proValueMap:", proValueMap)
	logger.Info.Println("matchMapKey matchMap:", matchMap)
	logger.Info.Println("matchMapKey condition:", condition)
	switch condition {
	case "等于":
		if len(proValueMap) != len(matchMap) {
			return false
		}
		for key, _ := range proValueMap {
			if matchMap[key] == false {
				return false
			}
		}
		return true
	case "包含":
		for key, _ := range matchMap {
			if proValueMap[key] == false {
				return false
			}
		}
		return true
	case "不包含":
		for key, _ := range matchMap {
			if proValueMap[key] == true {
				return false
			}
		}
		return true
	case "包含于":
		for key, _ := range proValueMap {
			if matchMap[key] == false {
				return false
			}
		}
		return true
	case "取交集":
		//只要有一个元素相即可
		for key, _ := range matchMap {
			if proValueMap[key] == true {
				return true
			}
		}
	}
	return false
}

func GetInventoryIDsByProjectid(id string) (ids []string, err error) {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": id,
					},
				},
			},
		},
		"size": common.UnlimitSize,
	}

	strInfo, err := esutil.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Printf("查询资产编目失败:%s", err.Error())
		return ids, err
	}
	sourcedbs := esutil.GetSource(strInfo)
	for _, v := range sourcedbs {
		ids = append(ids, gjson.Get(v, "inventoryid").String())
	}
	return ids, nil
}

func GetTerm(con, value string) (termMust, termMustNot []interface{}) {
	//资产名称对比条件 ：等于、不等于、为空、不为空、包含、不包含、开头包含、结尾包含、文本长度小于、文本长度大于3
	switch con {
	case "等于":
		return append(termMust, gin.H{
			"term": gin.H{
				"inventoryname": value,
			},
		}), termMustNot
	case "不等于":
		return termMust, append(termMustNot, gin.H{
			"term": gin.H{
				"inventoryname": value,
			},
		})
	case "为空":
		return termMust, append(termMustNot, gin.H{
			"exists": gin.H{
				"field": "inventoryname",
			},
		})
	case "不为空":
		return append(termMust, gin.H{
			"exists": gin.H{
				"field": "inventoryname",
			},
		}), termMustNot
	case "包含":
		return append(termMust, gin.H{
			"term": gin.H{
				"query_string": gin.H{
					"query":            "*" + value + "*",
					"fields":           []string{"inventoryname"},
					"default_operator": "or",
				},
			},
		}), termMustNot
	case "不包含":
		return termMust, append(termMustNot, gin.H{
			"term": gin.H{
				"query_string": gin.H{
					"query":            "*" + value + "*",
					"fields":           []string{"inventoryname"},
					"default_operator": "or",
				},
			},
		})
	case "开头包含":
		return append(termMust, gin.H{
			"term": gin.H{
				"query_string": gin.H{
					"query":            "*" + value,
					"fields":           []string{"inventoryname"},
					"default_operator": "or",
				},
			},
		}), termMustNot
	case "结尾包含":
		return append(termMust, gin.H{
			"term": gin.H{
				"query_string": gin.H{
					"query":            value + "*",
					"fields":           []string{"inventoryname"},
					"default_operator": "or",
				},
			},
		}), termMustNot
	case "文本长度小于":
		return append(termMust, gin.H{
			"exists": gin.H{
				"field": "inventoryname",
			},
		}, gin.H{
			"script": gin.H{
				"script": "doc['inventoryname'].value.length() < " + value,
			},
		}), termMustNot
	case "文本长度大于":
		return append(termMust, gin.H{
			"exists": gin.H{
				"field": "inventoryname",
			},
		}, gin.H{
			"script": gin.H{
				"script": "doc['inventoryname'].value.length() > " + value,
			},
		}), termMustNot
	}
	return termMust, termMustNot
}

func GetCasToken(resource ResourceRegis) (string, error) {
	var thisToken string
	thisToken = GetDtToken(resource.Id)

	if resource.ZbProjectId == "" {
		return "该指标没有项目权限", errors.New("该指标没有项目权限")
	}
	//logger.Info.Println("cas-token:", thisToken) 会空指针
	fmt.Println("cas-token:", thisToken)
	//fmt.Println("Redis中有token!")
	if thisToken == "" {
		//重新获取DS的cas的token
		var token string
		var err, err2 error
		if resource.Url == "" {
			token, err = commontools.DSGetCASToken(resource.HttpUrl, resource.UserName, resource.ZbProjectId)
			if err != nil {
				return thisToken, err
			}
			fmt.Println("resource-token:", token)
		} else {
			if resource.HttpUrl == "" {
				token, err = commontools.DSGetCASToken(resource.Url, resource.UserName, resource.ZbProjectId)
				if err != nil {
					logger.Error.Println("httpurl-token:", err)
					return thisToken, err
				}
			} else {
				//两者任意一个失败，都算失败，且返回对应失败日志
				token, err = commontools.DSGetCASToken(resource.Url, resource.UserName, resource.ZbProjectId)
				token, err2 = commontools.DSGetCASToken(resource.HttpUrl, resource.UserName, resource.ZbProjectId)
				if err != nil {
					return thisToken, err
				}
				if err2 != nil {
					return thisToken, err2
				}
			}
		}

		thisToken = token
		rediscli.Exec("SETEX", resource.Id, 9*60, token)
	}

	return thisToken, nil
}

// 列举DS文件夹
func ListDSFolder(param DSFolderPara, resource ResourceRegis) (DSFolderRes, error) {
	var res DSFolderRes
	//补充默认参数
	param.Module = "metrics"
	param.Listall = true
	param.ProjectID = resource.ZbProjectId
	token, err := GetCasToken(resource)
	if err != nil {
		fmt.Println("castoken获取错误:", err.Error())
		logger.Error.Println(err)
		return res, err
	}
	resBody, err := CommonDSPostJsonWithToken(resource, toolscommon.DSListFolder, commontools.MaptoJson(param), token)
	//resBody, err := httpclient2.DSPostJsonWithToken(resource.Url+toolscommon.DSListFolder, commontools.MaptoJson(param), token)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	//fmt.Println("listlink res: ", string(resBody))
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	return res, nil
}

// 列举DS指标
func ListDSMetrics(param DSMetricsPara, resource ResourceRegis) (DSFolderRes, error) {
	var res DSFolderRes
	//补充默认参数
	token, err := GetCasToken(resource)
	if err != nil {
		fmt.Println("castoken获取错误:", err.Error())
		logger.Error.Println(err)
		return res, err
	}
	param.ProjectID = resource.ZbProjectId
	resBody, err := CommonDSPostJsonWithToken(resource, toolscommon.DSSearchMetrics, commontools.MaptoJson(param), token)
	//resBody, err := httpclient2.DSPostJsonWithToken(resource.Url+toolscommon.DSSearchMetrics, commontools.MaptoJson(param), token)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	return res, nil
}

// 获取DS指标详情
func GetDSMetricsDetail(param DSMetricsPara, resource ResourceRegis) (DSFolderRes, error) {
	var res DSFolderRes
	var err error

	if resource.UserName == "" && resource.Id != "" {
		resource, err = ResourceQuery(resource.Id)
		if err != nil {
			return res, err
		}

	}

	//补充默认参数
	token, err := GetCasToken(resource)
	if err != nil {
		fmt.Println("castoken获取错误:", err.Error())
		logger.Error.Println(err)
		return res, err
	}
	resBody, err := CommonDSPostJsonWithToken(resource, toolscommon.DSMetricsDetail, commontools.MaptoJson(param), token)
	//resBody, err := httpclient2.DSPostJsonWithToken(resource.Url+toolscommon.DSMetricsDetail, commontools.MaptoJson(param), token)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}

	return res, nil
}

// 获取DS指标详情
func InnerGetDSMetricsList(param DSMetricsPara, resource ResourceRegis) (DsInnerMetricsList, error) {
	var res DsInnerMetricsList
	var err error

	if resource.UserName == "" && resource.Id != "" {
		resource, err = ResourceQuery(resource.Id)
		if err != nil {
			return res, err
		}

	}

	//补充默认参数
	token, err := GetCasToken(resource)
	if err != nil {
		fmt.Println("castoken获取错误:", err.Error())
		logger.Error.Println(err)
		return res, err
	}

	resBody, err := httpclient2.DSPostJsonWithToken(resource.Url+toolscommon.DSMetricsDetail, commontools.MaptoJson(param), token)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		logger.Error.Println(err)
		return res, err
	}

	return res, nil
}

func QueryRealMessage(id string) (source.RealMessage, error) {
	var message source.RealMessage
	realres, err := esutil.SearchByID(common.DataBaseAnts, common.RealMessage, id)
	if err != nil {
		logger.Error.Printf("根据id获取数据失败:%s", err.Error())
		return message, err
	}
	err = json.Unmarshal([]byte(realres), &message)
	if err != nil {
		logger.Error.Printf("根据id获取数据失败:%s", err.Error())
		return message, err
	}
	return message, nil
}

// 获取DS指标详情
func InnerGetDSMetricsDetail(param DSMetricsPara, resource ResourceRegis) (DsInnerMetricsDetail, bool, bool, error) {
	var res DsInnerMetricsDetail
	var err error
	var zbErr bool
	var resourceErr bool

	if resource.UserName == "" && resource.Id != "" {
		fmt.Println("resourceid:", resource.Id)
		resource, err = ResourceQuery(resource.Id)
		if err != nil {
			return res, true, zbErr, errors.New("资源库异常:" + err.Error())
		}
	}

	if resource.Status == 2 {
		resourceErr = true
	}

	//补充默认参数
	token, err := GetCasToken(resource)
	if err != nil {
		//fmt.Println("castoken获取错误:", err.Error())
		return res, resourceErr, zbErr, err
	}
	resBody, err := CommonDSPostJsonWithToken(resource, toolscommon.DSMetricsDetail, commontools.MaptoJson(param), token)
	//resBody, err := httpclient2.DSPostJsonWithToken(resource.Url+toolscommon.DSMetricsDetail, commontools.MaptoJson(param), token)
	if err != nil {
		return res, resourceErr, true, err
	}

	err = json.Unmarshal(resBody, &res)
	fmt.Println(err)
	if err != nil {
		fmt.Println("序列化结果错误，错误信息：", string(resBody))
		//fmt.Println("resbody:", string(resBody))
		return res, resourceErr, true, err
	}

	return res, resourceErr, zbErr, nil
}

// 列举DS指标
func GetDSMenu(param DSMetricsPara, resource ResourceRegis) (bool, error) {
	var hasAuth bool
	var res DSMenuRes
	//补充默认参数
	token, err := GetCasToken(resource)
	if err != nil {
		fmt.Println("castoken获取错误:", err.Error())
		logger.Error.Println(err)
		return hasAuth, err
	}
	param.ProjectID = resource.ZbProjectId
	resBody, err := CommonDSPostJsonWithToken(resource, toolscommon.DSMenuList, commontools.MaptoJson(param), token)
	//resBody, err := httpclient2.DSPostJsonWithToken(resource.Url+toolscommon.DSMenuList, commontools.MaptoJson(param), token)
	if err != nil {
		logger.Error.Println(err)
		return hasAuth, err
	}
	//fmt.Println("权限菜单：", string(resBody))
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		logger.Error.Println(err)
		return hasAuth, err
	}

	for _, v := range res.Result.Dev {
		if v.Id == "dataMetrics" && v.Auth != "0" {
			return true, nil
		}
	}
	return hasAuth, nil
}
func ResourceApiCreate(sp ServiceApiInfo) (string, error) {
	sp.CreatTime = time.Now().Format("2006-01-02 15:04:05")
	sp.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	id, err := esutil.AddSingleWithRefresh(common.DataBaseModelAsset, common.TbServiceApi, "id", common.RefreshTrue, sp)
	//_, err := esutil.AddSingleWithRefresh(common.DataBaseModelAsset, "tytest2", "id", common.RefreshTrue, sp)

	if err != nil {
		logger.Error.Println(err)
		return "注册API失败", err
	}
	return id, nil
}
func ResourceApiSave(sp ServiceApiInfo) (string, error) {
	sp.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	docbody := gin.H{
		"name":            sp.Name,
		"describe":        sp.Describe,
		"updatetime":      sp.UpdateTime,
		"serviceexternal": sp.ServiceExternal,
		"servicemap":      sp.ServiceMap,
		"servicesafe":     sp.ServiceSafe,
	}
	//_, err := esutil.AddSingleWithRefresh(common.DataBaseModelAsset, common.TbServiceApi, "id", common.RefreshTrue, sp)
	err := serveresclient.NewESClientCl.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbServiceApi, common.RefreshWait, sp.ID, docbody)
	if err != nil {
		logger.Error.Println(err)
		return "更改API失败", err
	}
	//更改挂载和发布的任务信息
	var musts []interface{}
	term1 := gin.H{
		"term": gin.H{
			"attachinfo.attachtpapiinfo.apiid": sp.ID,
		},
	}
	musts = append(musts, term1)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnlimitSize,
	}
	res, err := esutil.SearchByTerm(common.DataBaseModelAsset, common.TbAssetInventory, body)
	if err != nil {
		logger.Error.Println(err)
		return "搜索挂载或者发布信息失败", err
	}
	relList, _ := esutil.GetSourceAndTotal(res)
	for _, v := range relList {
		var oneRecord AttachRes
		_ = json.Unmarshal([]byte(v), &oneRecord)
		for i, api := range oneRecord.AttachInfo.AttachTPAPIInfo {
			if api.APIID == sp.ID {
				oneRecord.AttachInfo.AttachTPAPIInfo[i].APIName = sp.Name
				oneRecord.AttachInfo.AttachTPAPIInfo[i].APIDesc = sp.Describe
			}
		}
		for i, api := range oneRecord.PutInfo.Put_ServerInfo {
			if api.Serverid == sp.ID {
				oneRecord.PutInfo.Put_ServerInfo[i].ServerName = sp.Name
				oneRecord.PutInfo.Put_ServerInfo[i].ServerDescribe = sp.Describe
			}
		}

		updatebody := gin.H{
			"putinfo": gin.H{
				"putserverinfo": oneRecord.PutInfo.Put_ServerInfo,
			},
			"attachinfo": gin.H{
				"attachtpapiinfo": oneRecord.AttachInfo.AttachTPAPIInfo,
			},
		}
		err = esutil.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetInventory, common.RefreshTrue, oneRecord.InventoryID, updatebody)
	}
	return "更改成功", nil
}
func ResourceApiList(sp ApiSearch) ([]ApiListInfo, int, error) {
	info := make([]ApiListInfo, 0)
	var terms []interface{}
	//分页查询
	if sp.Page < 0 { //0：搜索全部
		sp.Page = 1
	}
	if sp.Perpage < 1 {
		sp.Perpage = 10
	}
	//项目id查询
	if sp.ProjectID != "" {
		projectT := gin.H{
			"term": gin.H{
				"projectid": sp.ProjectID,
			},
		}
		terms = append(terms, projectT)
	}
	if sp.ResourceID != "" {
		resourceid := gin.H{
			"term": gin.H{
				"resourceid": sp.ResourceID,
			},
		}
		terms = append(terms, resourceid)
	}
	if !sp.Listall {
		term1 := gin.H{
			"term": gin.H{
				"userid": sp.UserID,
			},
		}
		terms = append(terms, term1)
	}
	//name, descrition 的模糊匹配
	if sp.Match != "" {
		match := gin.H{
			"query_string": gin.H{
				"query":            "*" + sp.Match + "*",
				"fields":           []string{"name", "describe"},
				"default_operator": "or",
			},
		}
		terms = append(terms, match)
	}
	sortc := []gin.H{}
	var sortorder = "desc"
	if sp.Sort == 0 {
		sortorder = "asc"
	}
	switch {
	//筛选
	case len(sp.FilterDS) != 0:
		for _, v := range sp.FilterDS {
			if len(v.FilterContent) != 0 {
				Field, content := _moneylist_change_filter(v.FilterField, v.FilterContent)

				term := gin.H{
					"terms": gin.H{
						Field: content,
					},
				}
				terms = append(terms, term)

			}

		}
	}
	switch sp.Sorttype {
	case 1: //时间降序
		sorttime := gin.H{
			"updatetime": sortorder,
		}
		sortc = append(sortc, sorttime)
		var body gin.H
		//outfield := []string{"content", "sourcedb.fieldinfo", "realinfo"}
		if sp.Page == 0 {
			body = gin.H{
				"_source": gin.H{
					"exclude": "password",
				},
				"query": gin.H{
					"bool": gin.H{
						"must": terms,
					},
				},
				"size": 10000,
				"sort": sortc,
			}
		} else {
			body = gin.H{
				"_source": gin.H{
					"exclude": "password",
				},
				"query": gin.H{
					"bool": gin.H{
						"must": terms,
					},
				},
				"size": sp.Perpage,
				"from": (sp.Page - 1) * sp.Perpage,
				"sort": sortc,
			}
		}
		byteBody, err := json.Marshal(body)
		if err != nil {
			return info, 0, err
		}
		fmt.Println("body:", string(byteBody))
		resp, err := serveresclient.NewESClientCl.SearchByTerm(DataBaseModelAsset, common.TbServiceApi, body)
		//resp, err := serveresclient.NewESClientCl.SearchByTerm(DataBaseModelAsset, "tytest2", body)
		if err != nil {
			logger.Error.Println(err)
			return info, 0, nil
		}
		var sou ServiceApiResult
		err = json.Unmarshal(resp, &sou)
		if err != nil {
			logger.Error.Println(err)
			return info, 0, err
		}
		//var array []ServiceApiInfo
		//for i := 0; i < len(sou.Hits.HitsT); i++ {
		//	array = append(array, sou.Hits.HitsT[i].ServiceApiSource)
		//}

		var arrays []ApiListInfo
		for _, v := range sou.Hits.HitsT {
			var array ApiListInfo
			array.Id = v.ServiceApiSource.ID
			array.Name = v.ServiceApiSource.Name
			array.Describe = v.ServiceApiSource.Describe
			array.ApiIpType = v.ServiceApiSource.ServiceSafe.ApiIpType
			array.Permission = v.ServiceApiSource.ServiceMap.Permission
			array.OpenFlow = v.ServiceApiSource.ServiceSafe.OpenFlow
			array.IsInventory = v.ServiceApiSource.IsInventory
			array.IsPublished = v.ServiceApiSource.IsPublished
			array.UpdateTime = v.ServiceApiSource.UpdateTime
			array.AccessUrl = v.ServiceApiSource.ServiceMap.AccessUrl
			array.ExtUrl = v.ServiceApiSource.ServiceExternal.ExtUrl
			arrays = append(arrays, array)
		}
		return arrays, sou.Hits.Total, nil
	}
	return info, 0, nil
}
func ResourceApiDetail(info ApiSearch) (interface{}, error) {
	resp, err := serveresclient.NewESClientCl.SearchByIDResult(DataBaseModelAsset, common.TbServiceApi, info.ID)
	if err != nil {
		logger.Error.Println(err)
		return info, nil
	}
	var sou ServiceApiHit
	err = json.Unmarshal(resp, &sou)
	if err != nil {
		logger.Error.Println(err)
		return info, err
	}
	return sou.ServiceApiSource, nil
}
func ResourceApiTest(sp ServiceApiInfo) (interface{}, error) {
	resultMap := make(map[string]interface{})
	resultMaps := make([]map[string]interface{}, 0)
	resultMap, _ = InterfaceConvertToMap(sp.ServiceExternal.RequestParam, resultMaps, 0)
	body, err := json.Marshal(resultMap)
	if err != nil {
		logger.Error.Println(err)
		return resultMap, err
	}
	res, err := PostApiRequest(sp.ServiceExternal.ExtUrl, string(body), sp.ServiceExternal.ApiType, sp.ServiceExternal.Method, sp.ServiceExternal.HeaderParam)
	if err != nil {
		logger.Error.Println(err)
		return resultMap, err
	}
	return string(res), nil
}
func PostApiRequest(url string, body string, requesttype, method string, headparam []ServiceParam) (response []byte, err error) {
	client := &http.Client{}
	if requesttype == "https" {
		client.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		}
	}

	req, err := http.NewRequest(method, url, strings.NewReader(body))
	if err != nil {
		return []byte(""), err
	}
	for _, v := range headparam {
		if v.Required {
			req.Header.Set(v.ParamName, v.DefaultValue)
		}
	}
	//req.Header.Set("Content-Type", "application/json")
	//req.Header.Set("Authorization", token)
	//req.SetBasicAuth(AuthUser, AuthPasswd)
	resp, err := client.Do(req)
	if err != nil {
		return []byte(""), err
	}

	defer resp.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	var errget error
	if resp.StatusCode != 200 {
		errget = errors.New(string(buf.Bytes()))
		return buf.Bytes(), errget
	}
	return buf.Bytes(), nil
}

// API注册 前端结构体转化为实际参数所需要的json
func InterfaceConvertToMap(results []ServiceParam, resultMaps []map[string]interface{}, n int) (map[string]interface{}, []map[string]interface{}) {
	resultMap := make(map[string]interface{})
	for _, result := range results {
		var bool1 bool

		switch result.Type {
		case "bool":
			bool1, _ = strconv.ParseBool(result.DefaultValue)
			if n == 0 {
				resultMap[result.ParamName] = bool1
			} else {
				resultMap[result.ParamName] = bool1
			}
			continue
		case "int":
			resint, _ := strconv.Atoi(result.DefaultValue)
			resultMap[result.ParamName] = resint
			continue
		case "string":
			if n == 2 {
				resultMap[result.ParamName] = result.DefaultValue
				resultMaps = append(resultMaps, resultMap)
			} else {
				resultMap[result.ParamName] = result.DefaultValue

			}
			continue
		case "[]string":
			//以逗号分割
			var arr []string
			if n == 2 {
				for _, s := range strings.Split(result.DefaultValue, ",") {
					arr = append(arr, s)
				}
				//map k,v值 存在1对多的情况
				if _, ok := resultMap[result.ParamName]; !ok {
					//map中不存在k值，直接添加
					resultMap[result.ParamName] = arr
					//为什么上述步骤直接赋值resultMaps --因为是浅拷贝，对应map也发生相应变化
				}
			} else {
				for _, s := range strings.Split(result.DefaultValue, ",") {
					arr = append(arr, s)
				}
				resultMap[result.ParamName] = arr
			}
			continue
		case "[]int":
			var arr []int
			for _, s := range strings.Split(result.DefaultValue, ",") {
				ins, _ := strconv.Atoi(s)
				arr = append(arr, ins)
			}
			resultMap[result.ParamName] = arr
			continue
		case "object":
			if len(result.SubField) != 0 {
				nestedResults := result.SubField
				resultMap[result.ParamName], _ = InterfaceConvertToMap(nestedResults, resultMaps, 1)
			}
		case "[]object":
			for _, v1 := range result.SubFields {
				nestedResults := v1
				_, resultMaps = InterfaceConvertToMap(nestedResults, resultMaps, 2)
			}
			resultMap[result.ParamName] = resultMaps
		case "":

		}
	}
	return resultMap, resultMaps
}
func _moneylist_change_filter(s string, t []string) (string, interface{}) {
	switch s {
	case "permission":
		var t1 []int
		for _, v := range t {
			ato, _ := strconv.Atoi(v)
			t1 = append(t1, ato)
		}
		return "servicemap.permission", t1
	case "apiiptype":
		var t1 []int
		for _, v := range t {
			ato, _ := strconv.Atoi(v)
			t1 = append(t1, ato)
		}
		return "servicesafe.apiiptype", t1
	case "openflow":
		var t1 []bool
		for _, v := range t {
			ato, _ := strconv.ParseBool(v)
			t1 = append(t1, ato)
		}
		return "servicesafe.openflow", t1
	case "isinverntory":
		var t1 []bool
		for _, v := range t {
			ato, _ := strconv.ParseBool(v)
			t1 = append(t1, ato)
		}
		return "isinverntory", t1
	case "ispublished":
		var t1 []bool
		for _, v := range t {
			ato, _ := strconv.ParseBool(v)
			t1 = append(t1, ato)
		}
		return "ispublished", t1
	}
	return s, t
}

//	func convertToMap(results []gjson.Result, resultMaps []map[string]interface{}, n int) (map[string]interface{}, []map[string]interface{}) {
//		resultMap := make(map[string]interface{})
//		//resultMaps := make([]map[string]interface{}, 0)
//		for j, result := range results {
//			fmt.Println("convmap:", gjson.JSON, result.Type, j, result)
//			var bool1 bool
//			if result.Type == gjson.JSON {
//				switch result.Get("type").String() {
//				case "bool":
//					bool1, _ = strconv.ParseBool(result.Get("defaultvalue").String())
//					if n == 0 {
//						resultMap[result.Get("paramname").String()] = bool1
//					} else {
//						resultMap[result.Get("paramname").String()] = bool1
//					}
//					continue
//				case "int":
//					resint, _ := strconv.Atoi(result.Get("defaultvalue").String())
//					resultMap[result.Get("paramname").String()] = resint
//					continue
//				case "string":
//					if n == 2 {
//						resultMap[result.Get("paramname").String()] = result.Get("defaultvalue").String()
//						resultMaps = append(resultMaps, resultMap)
//					} else {
//						resultMap[result.Get("paramname").String()] = result.Get("defaultvalue").String()
//
//					}
//					fmt.Println("resultMap:", resultMap, j)
//					fmt.Println("resultMaps:", resultMaps, j)
//					continue
//				case "[]string":
//					//以逗号分割
//					var arr []string
//					fmt.Println("[]string-resultMaps--kaishi:", resultMaps, j)
//					//resultMap[result.Get("paramname").String()] = arr
//					if n == 2 {
//						for _, s := range strings.Split(result.Get("defaultvalue").String(), ",") {
//							arr = append(arr, s)
//						}
//						fmt.Println("[]string-resultMaps--kaishi2:", resultMaps, j, result.Get("paramname").String(), arr)
//						//map k,v值 存在1对多的情况
//						if _, ok := resultMap[result.Get("paramname").String()]; !ok {
//							//map中不存在k值，直接添加
//							fmt.Println("[]string-resultMaps--kaishi3:", resultMap, resultMaps, j)
//							resultMap[result.Get("paramname").String()] = arr
//							//为什么上述步骤直接赋值resultMaps --因为是浅拷贝，对应map也发生相应变化
//							fmt.Println("[]string-resultMaps--kaishi4:", resultMap, resultMaps, j)
//						}
//
//						//resultMaps = append(resultMaps, resultMap)
//						//fmt.Println("[]string-resultMaps4:", resultMaps, j)
//					} else {
//						for _, s := range strings.Split(result.Get("defaultvalue").String(), ",") {
//							arr = append(arr, s)
//						}
//						resultMap[result.Get("paramname").String()] = arr
//						fmt.Println("[]string-resultMap2:", resultMap, j)
//					}
//					fmt.Println("[]string-resultMap:", resultMap, j)
//
//					continue
//				case "[]int":
//					var arr []int
//					for _, s := range strings.Split(result.Get("defaultvalue").String(), ",") {
//						ins, _ := strconv.Atoi(s)
//						arr = append(arr, ins)
//					}
//					resultMap[result.Get("paramname").String()] = arr
//					continue
//				case "object":
//					if len(result.Get("subfield").Array()) != 0 {
//						//fmt.Println("result1:", result.Get("subfield").Array())
//						nestedResults := result.Get("subfield").Array()
//						resultMap[result.Get("paramname").String()], _ = convertToMap(nestedResults, resultMaps, 1)
//					}
//				case "[]object":
//					for _, v1 := range result.Get("subfields").Array() {
//						nestedResults := v1.Array()
//						_, resultMaps = convertToMap(nestedResults, resultMaps, 2)
//					}
//					resultMap[result.Get("paramname").String()] = resultMaps
//				case "":
//
//				}
//			} else {
//				resultMap[result.Get("paramname").String()] = result.Get("defaultvalue").String()
//				fmt.Println("resultMap2:", resultMap)
//			}
//		}
//		return resultMap, resultMaps
//	}
func ResourceApiDelete(id string) error {
	err := serveresclient.NewESClientCl.DelByIDWithRefresh(common.DataBaseModelAsset, common.TbServiceApi, "true", id)
	//err := serveresclient.NewESClientCl.DelByIDWithRefresh(common.DataBaseModelAsset, "tytest2", "true", id)
	if err != nil {
		logger.Error.Println("api删除失败", err.Error())
		return err
	}
	return nil
}
func ResourceApiUserAuth(userid string) (string, error) {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"userid": userid,
					},
				},
			},
		},
	}
	//byteBody, _ := json.Marshal(body)
	//uri := fmt.Sprintf("/%s/%s/_search", Database, NewTbAsset)
	response, err := serveresclient.NewESClientCl.SearchByTerm(common.DataBaseAuth, common.UserESTB, body)
	if err != nil {
		logger.Error.Println("获取用户验证码失败", err.Error())
		return "", err
	}
	e := gjson.Get(string(response), "hits.hits").Array()
	var userauth string
	for _, v := range e {
		userauth = v.Get("_source.userauth").String()
		break
	}
	return userauth, nil
}

func UpdatePreviewValue(inventoryid string, updateValue string) error {

	logger.Info.Println("需要更新的资产：", inventoryid, "需要更新的值:", updateValue)
	fmt.Println("需要更新的资产：", inventoryid, "需要更新的值:", updateValue)
	updatebody := gin.H{
		"putinfo": gin.H{
			"putmetricsinfo": gin.H{
				"metricsvaluev999": updateValue,
			},
		},
	}
	err := esutil.UpdByIDWithRefresh(common.DataBaseModelAsset, common.TbAssetInventory, common.RefreshTrue, inventoryid, updatebody)
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	return nil

}
func CommonDSPostJsonWithToken(resource ResourceRegis, info string, body string, token string) ([]byte, error) {
	var resBody []byte
	var err, err2 error
	if resource.Url == "" {
		fmt.Println("ceshi:", resource.HttpUrl+info)
		fmt.Println("body:", body, token)
		resBody, err = httpclient2.DSPostJsonWithToken(resource.HttpUrl+info, body, token)
		if err != nil {
			fmt.Println(err)
			return resBody, err
		}
	} else {
		if resource.HttpUrl == "" {
			resBody, err = httpclient2.DSPostJsonWithToken(resource.Url+info, body, token)
			if err != nil {
				fmt.Println(err)
				return resBody, err
			}
		} else {
			resBody, err = httpclient2.DSPostJsonWithToken(resource.Url+info, body, token)
			resBody, err2 = httpclient2.DSPostJsonWithToken(resource.HttpUrl+info, body, token)
			if err != nil {
				fmt.Println(err)
				return resBody, err
			}
			if err2 != nil {
				fmt.Println(err)
				return resBody, err2
			}
		}
	}
	return resBody, nil
}
func CommonDSLoginVerfy(sp ResourceRegis) ([]byte, error) {
	var resBody []byte
	var err, err2 error
	if sp.Url == "" {
		_, err = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.HttpUrl)
		if err != nil {
			logger.Error.Println(err)
			return resBody, err
		}
	} else {
		if sp.HttpUrl == "" {
			_, err = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.Url)
			if err != nil {
				logger.Error.Println(err)
				return resBody, err
			}
		} else {
			_, err = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.Url)
			_, err2 = commontools.DSLoginVerfy(sp.UserName, sp.Password, sp.HttpUrl)
			if err != nil {
				logger.Error.Println(err)
				return resBody, err
			}
			if err2 != nil {
				logger.Error.Println(err2)
				return resBody, err2
			}
		}
	}
	return resBody, nil

}
func CommonDSGetCASToken(resource ResourceRegis) (string, error) {
	var token string
	var err, err2 error
	if resource.Url == "" {
		token, err = commontools.DSGetCASToken(resource.HttpUrl, resource.UserName, resource.ZbProjectId)
		if err != nil {
			return token, err
		}
		logger.Info.Println("resource-token:", token)
	} else {
		if resource.HttpUrl == "" {
			token, err = commontools.DSGetCASToken(resource.Url, resource.UserName, resource.ZbProjectId)
			if err != nil {
				return token, err
			}
		} else {
			//两者任意一个失败，都算失败，且返回对应失败日志
			token, err = commontools.DSGetCASToken(resource.Url, resource.UserName, resource.ZbProjectId)
			token, err2 = commontools.DSGetCASToken(resource.HttpUrl, resource.UserName, resource.ZbProjectId)
			if err != nil {
				return token, err
			}
			if err2 != nil {
				return token, err2
			}
		}
	}
	return token, nil
}
func sortByValues(slice []string, sortBy []string) {
	// 创建一个 map 用于存储每个值在 sortBy 中的索引位置
	indexMap := make(map[string]int)
	for i, v := range sortBy {
		indexMap[v] = i
	}

	// 使用 sort.Slice 方法进行排序
	sort.Slice(slice, func(i, j int) bool {
		return indexMap[slice[i]] < indexMap[slice[j]]
	})
}
