package menu

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"datatom.com/auth/common"
	"datatom.com/metadata/httpdo/esutil"
	escli "datatom.com/tools/common/escli"
	"github.com/tidwall/gjson"
	"gopkg.in/olivere/elastic.v5"

	"datatom/gin.v1"

	. "datatom.com/auth/handlerpack"
	"datatom.com/auth/logger"
)

func DeleteMenuUnpublished(projectID string) error {

	var must []interface{}
	term1 := gin.H{
		"term": gin.H{
			"isrelease": false,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"projectid": projectID,
		},
	}
	must = append(must, term1, term2)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
	}
	err := escli.NewESClientCl.DelByQuery(common.DanastudioAuth, common.TbMenu, body)
	if err != nil {
		logger.Error.Println(err)
	}
	return err
}

func CreatePreview(m CreateMenu, projectID string) error {

	var addList []ModifyInfo
	// 列举菜单
	menuList, err := ListMenu(projectID, true)
	if err != nil {
		return err
	}
	// 若导航ID为空且导航名不为空，表示需要导航为新建的导航
	var navInfo ModifyInfo
	if m.NavID == "" && m.NavName != "" {
		navInfo.Id = escli.NewESClientCl.GenerateSubID()
		navInfo.ParentID = ""

		// 查询导航的最大sort值，以便+1赋给新建的导航
		var maxNavSort = 0
		for _, v := range menuList {
			if v.ParentID == "" {
				if v.Sort > maxNavSort {
					maxNavSort = v.Sort
				}
			}
		}
		navInfo.Sort = maxNavSort + 1
		navInfo.Name = m.NavName
		navInfo.IsOriginal = false
		navInfo.CreateTime = time.Now().Format(TimeFormat)
		navInfo.UpdateTime = navInfo.CreateTime
		navInfo.IsRelease = false
		navInfo.Disabled = false
		navInfo.Path = navInfo.Id
		navInfo.Icon = ""
		navInfo.ProjectID = projectID
		navInfo.Owner = m.Owner
		addList = append(addList, navInfo)
	}
	// 拼接二级菜单的数据结构
	var catalogInfo ModifyInfo
	// 若传参没有目录ID且目录名不为空时，表示该目录为需要新建的目录
	if m.CatalogID == "" && m.CatalogName != "" {
		// 生成唯一字符串做该目录的ID
		catalogInfo.Id = escli.NewESClientCl.GenerateSubID()
		// 若导航ID不为空，表示导航也是本次新建的
		if navInfo.Id != "" {
			catalogInfo.ParentID = navInfo.Id
			catalogInfo.Sort = 1
		} else {
			// 否则以传参中的导航ID做本次新建目录的父级ID
			catalogInfo.ParentID = m.NavID
			// 获取同级目录最大的sort值，将其+1作为本目录的sort
			var maxCatalogSort = 0
			for _, v := range menuList {
				if v.ParentID == m.NavID {
					if v.Sort > maxCatalogSort {
						maxCatalogSort = v.Sort
					}
				}
			}

			catalogInfo.Sort = maxCatalogSort + 1
		}
		catalogInfo.Name = m.CatalogName
		catalogInfo.IsOriginal = false
		catalogInfo.CreateTime = time.Now().Format(TimeFormat)
		catalogInfo.UpdateTime = catalogInfo.CreateTime
		catalogInfo.IsRelease = false
		catalogInfo.Disabled = false
		catalogInfo.Path = catalogInfo.Id
		catalogInfo.Icon = "icon-customize"
		catalogInfo.ProjectID = projectID
		addList = append(addList, catalogInfo)
	}

	var menuInfo ModifyInfo
	menuInfo.Id = escli.NewESClientCl.GenerateSubID()
	// 将页面的父级ID置为空
	menuInfo.ParentID = ""
	menuInfo.Icon = ""
	// 判断父级ID从属关系, 如果页面层级是目录，则需要赋值icon
	//path拼接
	var menuPath string
	if m.CatalogID != "" { //原导航+原菜单+新子页面
		menuInfo.ParentID = m.CatalogID
		menuPath = m.NavID + "/" + m.CatalogID + "/" + menuInfo.Id
	} else if catalogInfo.Id != "" { //原导航+新菜单+新子页面
		menuInfo.ParentID = catalogInfo.Id
		menuPath = m.NavID + "/" + catalogInfo.Id + "/" + menuInfo.Id
	} else if m.NavID != "" { //原导航+新菜单
		menuInfo.ParentID = m.NavID
		menuInfo.Icon = "icon-customize"
		for _, v := range menuList {
			if v.Id == m.NavID {
				menuPath = v.Path + "/" + menuInfo.Id
				break
			}
		}
	} else if navInfo.Id != "" { //新导航+新菜单
		menuInfo.ParentID = navInfo.Id
		menuInfo.Icon = "icon-customize"
		menuPath = navInfo.Id + "/" + menuInfo.Id
	} else { //新导航
		menuPath = menuInfo.Id
	}

	// 遍历找出同级菜单的最大sort值
	var s = 0
	for _, v := range menuList {
		if v.ParentID == menuInfo.ParentID {
			if v.Sort > s {
				s = v.Sort
			}
		}
	}
	// 将最大sort值+1 赋给当前菜单
	menuInfo.Sort = s + 1
	menuInfo.Name = m.PageName
	menuInfo.IsOriginal = false
	menuInfo.CreateTime = time.Now().Format(TimeFormat)
	menuInfo.UpdateTime = menuInfo.CreateTime
	menuInfo.IsRelease = false
	menuInfo.Disabled = false
	menuInfo.Path = menuPath
	menuInfo.PageUrl = m.PageUrl

	menuInfo.ProjectID = projectID
	menuInfo.Owner = m.Owner
	addList = append(addList, menuInfo)

	err = esutil.AddBulk(common.DanastudioAuth, common.TbMenu, "id", addList)
	if err != nil {
		return err
	}

	return err
}

// 方法同listMenu,仅为初始化数据时兼容老数据使用，所以修改了部分查询条件
func ListMenuForInit(projectID string, isRelease bool) (menuList []Info, err error) {

	var should []interface{}
	// 通过本条件获取系统内置菜单
	exist := gin.H{
		"bool": gin.H{
			"must_not": gin.H{
				"exists": gin.H{
					"field": "projectid",
				},
			},
		},
	}
	should = append(should, exist)
	if projectID != "" {
		projectTerm := gin.H{
			"term": gin.H{
				"projectid": projectID,
			},
		}
		should = append(should, projectTerm)
	}

	var must []map[string]interface{}
	boolShould := gin.H{
		"bool": gin.H{
			"should": should,
		},
	}
	must = append(must, boolShould)
	var should2 []interface{}
	if isRelease {
		// 兼容4.6数据,当时使用大写的字段
		relTerm1 := gin.H{
			"term": gin.H{
				"isRelease": true,
			},
		}
		// 4.6.1及以后数据全为小写
		relTerm := gin.H{
			"term": gin.H{
				"isrelease": true,
			},
		}
		should2 = append(should2, relTerm, relTerm1)
	}

	boolShould2 := gin.H{
		"bool": gin.H{
			"should": should2,
		},
	}
	must = append(must, boolShould2)

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
		"sort": gin.H{
			"sort": gin.H{
				"order": "asc",
			},
		},
		"size": common.UnLimitSize,
	}
	resp, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, common.TbMenu, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	sourceList := escli.NewESClientCl.GetSource(resp)

	// ------------------------------对比tb_menusetting----------------------
	var mustSetting []map[string]interface{}

	Settingmust := gin.H{
		"term": gin.H{
			"projectid": projectID,
		},
	}
	mustSetting = append(mustSetting, Settingmust)
	bodySetting := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": mustSetting,
			},
		},
		"size": common.UnLimitSize,
	}

	respSetting, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, TbMenuSetting, bodySetting)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	SettingList := escli.NewESClientCl.GetSource(respSetting)
	// ----------------------------------------------------------------------
	for _, value := range sourceList {
		var menuInfo Info
		_ = json.Unmarshal([]byte(value), &menuInfo)

		for _, v := range SettingList {
			var menuSettingInfo NewNameInfo
			_ = json.Unmarshal([]byte(v), &menuSettingInfo)
			if menuSettingInfo.MenuId == menuInfo.Id {
				// 如果新表里面的name为空取原始的。
				if menuSettingInfo.NewName != "" {
					menuInfo.Name = menuSettingInfo.NewName
				}
				if menuSettingInfo.Sort != -1 {
					menuInfo.Sort = menuSettingInfo.Sort
				}
				break
			}
		}

		menuList = append(menuList, menuInfo)
	}
	return menuList, nil
}

func ListMenu(projectID string, isRelease bool) (menuList []Info, err error) {

	var should []interface{}
	// 通过本条件获取系统内置菜单
	exist := gin.H{
		"bool": gin.H{
			"must_not": gin.H{
				"exists": gin.H{
					"field": "projectid",
				},
			},
		},
	}
	should = append(should, exist)
	if projectID != "" {
		projectTerm := gin.H{
			"term": gin.H{
				"projectid": projectID,
			},
		}
		should = append(should, projectTerm)
	}

	var must []map[string]interface{}
	boolShould := gin.H{
		"bool": gin.H{
			"should": should,
		},
	}
	if isRelease {
		relTerm := gin.H{
			"term": gin.H{
				"isrelease": true,
			},
		}
		must = append(must, relTerm)
	}
	must = append(must, boolShould)

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
		"sort": gin.H{
			"sort": gin.H{
				"order": "asc",
			},
		},
		"size": common.UnLimitSize,
	}
	resp, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, common.TbMenu, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	sourceList := escli.NewESClientCl.GetSource(resp)

	// ------------------------------对比tb_menusetting----------------------
	var mustSetting []map[string]interface{}

	Settingmust := gin.H{
		"term": gin.H{
			"projectid": projectID,
		},
	}
	mustSetting = append(mustSetting, Settingmust)
	bodySetting := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": mustSetting,
			},
		},
		"size": common.UnLimitSize,
	}

	respSetting, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, TbMenuSetting, bodySetting)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	SettingList := escli.NewESClientCl.GetSource(respSetting)
	// ----------------------------------------------------------------------
	for _, value := range sourceList {
		var menuInfo Info
		_ = json.Unmarshal([]byte(value), &menuInfo)
		for _, v := range SettingList {
			var menuSettingInfo NewNameInfo
			_ = json.Unmarshal([]byte(v), &menuSettingInfo)
			if menuSettingInfo.MenuId == menuInfo.Id {
				// 如果新表里面的name为空取原始的。
				if menuSettingInfo.NewName != "" {
					menuInfo.Name = menuSettingInfo.NewName
				}
				if menuSettingInfo.Sort != -1 {
					menuInfo.Sort = menuSettingInfo.Sort
				}
				break
			}
		}

		menuList = append(menuList, menuInfo)
	}

	return menuList, nil
}

// 列举导航栏名
func ListModuleMenu(projectID string) (menuList []MiniInfo, err error) {
	// 获取导航层级的菜单名及其ID
	body := gin.H{
		"query": gin.H{
			"term": gin.H{
				"parentid": "",
			},
		},
		"size": common.UnLimitSize,
	}
	resp, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, common.TbMenu, body)
	if err != nil {
		logger.Error.Println(err)
		return []MiniInfo{}, err
	}
	sourceList := escli.NewESClientCl.GetSource(resp)
	for _, value := range sourceList {
		var menuInfo MiniInfo
		_ = json.Unmarshal([]byte(value), &menuInfo)
		menuList = append(menuList, menuInfo)
	}

	// 筛选项目下的数据
	if projectID == "" {
		return menuList, nil
	}

	// 以项目ID作为查询条件，获取项目下的菜单权限
	body2 := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": projectID,
					},
				},
			},
		},
	}

	res, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, common.TbProject, body2)
	if err != nil {
		logger.Error.Println(err)
		return []MiniInfo{}, err
	}

	menuInfo := gjson.Get(string(res), "hits.hits.0._source.moduleids").Array()
	var infoList []MiniInfo
	// 遍历获取项目下的菜单
	for _, value := range menuInfo {
		for _, menu := range menuList {
			if menu.Id == value.String() {
				infoList = append(infoList, menu)
				break
			}
		}
	}
	return infoList, nil
}

func ListProjectMenu(projectID string, isRelease bool, isAdmin bool) ([]Info, error) {
	// 获取基础菜单集合
	menuList, err := ListMenu(projectID, isRelease)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	// 当projectID为空时返回全部基础菜单
	if projectID == "" || isAdmin {
		return menuList, nil
	}

	// 以项目ID作为查询条件，获取项目下的菜单权限
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": projectID,
					},
				},
			},
		},
	}

	res, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, common.TbProject, body)
	if err != nil {
		logger.Error.Println(err)
		return menuList, err
	}

	menuInfo := gjson.Get(string(res), "hits.hits.0._source.moduleids").Array()
	var infoList []Info
	// 遍历获取项目下的菜单
	for _, menu := range menuList {
		for _, value := range menuInfo {
			if menu.Id == value.String() || menu.ProjectID == projectID {
				infoList = append(infoList, menu)
				break
			}
		}
	}

	return infoList, nil
}

// 更新菜单
func UpdateMenu(info UpdateMenuInfo, projectID string) error {
	// 列举菜单
	menuList, err := ListMenu(projectID, true)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	// 获取原始父级ID，以便之后处理当前菜单从原始父级移除后，且原始父级菜单没有其它子级需要将父级菜单删除的逻辑
	var oldParentID string
	// 获取当前菜单的父级ID
	for _, v := range menuList {
		if v.Id == info.ID {
			oldParentID = v.ParentID
			break
		}
	}
	// 遍历获取原始父级有几个子级菜单
	var oldChildrenList []string
	for _, v := range menuList {
		if v.ParentID == oldParentID {
			oldChildrenList = append(oldChildrenList, v.Id)
		}
	}

	var addList []ModifyInfo
	// 列举菜单
	// 若导航ID为空且导航名不为空，表示需要导航为新建的导航
	var navInfo ModifyInfo
	if info.NavID == "" && info.NavName != "" {
		navInfo.Id = escli.NewESClientCl.GenerateSubID()
		navInfo.ParentID = ""

		// 查询导航的最大sort值，以便+1赋给新建的导航
		var maxNavSort = 0
		for _, v := range menuList {
			if v.ParentID == "" {
				if v.Sort > maxNavSort {
					maxNavSort = v.Sort
				}
			}
		}
		navInfo.Sort = maxNavSort + 1
		navInfo.Name = info.NavName
		navInfo.IsOriginal = false
		navInfo.CreateTime = time.Now().Format(TimeFormat)
		navInfo.UpdateTime = navInfo.CreateTime
		navInfo.IsRelease = false
		navInfo.Disabled = false
		navInfo.Path = navInfo.Id
		navInfo.Icon = ""
		navInfo.ProjectID = projectID
		navInfo.Owner = info.Owner
		addList = append(addList, navInfo)
	}
	// 拼接二级菜单的数据结构
	var catalogInfo ModifyInfo
	// 若传参没有目录ID且目录名不为空时，表示该目录为需要新建的目录
	if info.CatalogID == "" && info.CatalogName != "" {
		// 生成唯一字符串做该目录的ID
		catalogInfo.Id = escli.NewESClientCl.GenerateSubID()
		// 若导航ID不为空，表示导航也是本次新建的
		if navInfo.Id != "" {
			catalogInfo.ParentID = navInfo.Id
			catalogInfo.Sort = 1
		} else {
			// 否则以传参中的导航ID做本次新建目录的父级ID
			catalogInfo.ParentID = info.NavID
			// 获取同级目录最大的sort值，将其+1作为本目录的sort
			var maxCatalogSort = 0
			for _, v := range menuList {
				if v.ParentID == info.NavID {
					if v.Sort > maxCatalogSort {
						maxCatalogSort = v.Sort
					}
				}
			}

			catalogInfo.Sort = maxCatalogSort + 1
		}
		catalogInfo.Name = info.CatalogName
		catalogInfo.IsOriginal = false
		catalogInfo.CreateTime = time.Now().Format(TimeFormat)
		catalogInfo.UpdateTime = catalogInfo.CreateTime
		catalogInfo.IsRelease = false
		catalogInfo.Disabled = false
		if catalogInfo.ParentID != "" {
			catalogInfo.Path = catalogInfo.ParentID + "/" + catalogInfo.Id
		} else {
			catalogInfo.Path = catalogInfo.Id
		}
		catalogInfo.Icon = "icon-customize"
		catalogInfo.ProjectID = projectID
		navInfo.Owner = info.Owner
		addList = append(addList, catalogInfo)
	}

	// 添加新导航和目录
	err = esutil.AddBulk(common.DanastudioAuth, common.TbMenu, "id", addList)
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	var menuInfo ModifyInfo
	menuInfo.Id = info.ID
	// if判断顺序不可更改，关系到菜单的父级ID是否正确设置
	// 将页面的父级ID置为空
	menuInfo.ParentID = ""
	menuInfo.Icon = ""
	menuInfo.Path = info.ID
	// 判断父级ID从属关系，若页面在目录基本，则需要赋值icon
	if info.CatalogID != "" {
		menuInfo.ParentID = info.CatalogID
	} else if catalogInfo.Id != "" {
		menuInfo.ParentID = catalogInfo.Id
	} else if info.NavID != "" {
		menuInfo.Icon = "icon-customize"
		menuInfo.ParentID = info.NavID
	} else if navInfo.Id != "" {
		menuInfo.Icon = "icon-customize"
		menuInfo.ParentID = navInfo.Id
	}
	if menuInfo.ParentID != "" {
		menuInfo.Path = menuInfo.ParentID + "/" + menuInfo.Id
	}
	// 当页面的父级菜单改变时需处理冗余数据
	if menuInfo.ParentID != oldParentID {
		var deleteIDs []string
		// 当原始父级菜单仅存在当前页面一个页面时，需要将原始父级菜单删除
		if len(oldChildrenList) == 1 {
			// 删除菜单表中的数据
			err := esutil.DelByID(common.DanastudioAuth, common.TbMenu, oldParentID)
			if err != nil {
				logger.Error.Println(err)
				return err
			}
			// 由于父级ID被删除，所以需要清除其相关菜单配置
			deleteIDs = append(deleteIDs, projectID+"_"+oldParentID)
		}
		// 由于父级ID改变，所以当前菜单的sort设置及重名规则需要被清空
		deleteIDs = append(deleteIDs, projectID+"_"+info.ID)
		// 删除sort及重命名表的冗余数据
		err = esutil.DelByID(common.DanastudioAuth, TbMenuSetting, deleteIDs)
		if err != nil {
			logger.Error.Println(err)
		}
	}

	var maxSort = 0
	for _, v := range menuList {
		if v.ParentID == menuInfo.ParentID {
			// 若发现目录没有从原先的父级移动，则不改变sort值
			if v.Id == menuInfo.Id {
				maxSort = 0
				break
			}
			if v.Sort > maxSort {
				maxSort = v.Sort
			}
		}
	}
	menuInfo.Sort = maxSort + 1
	menuInfo.Name = info.PageName
	menuInfo.UpdateTime = time.Now().Format(TimeFormat)
	menuInfo.PageUrl = info.PageUrl

	var body gin.H
	if maxSort == 0 {
		body = gin.H{
			"pageurl":    menuInfo.PageUrl,
			"parentid":   menuInfo.ParentID,
			"name":       menuInfo.Name,
			"updatetime": menuInfo.UpdateTime,
			"isrelease":  false,
			"path":       menuInfo.Path,
		}
	} else {
		body = gin.H{
			"pageurl":    menuInfo.PageUrl,
			"parentid":   menuInfo.ParentID,
			"name":       menuInfo.Name,
			"sort":       menuInfo.Sort,
			"updatetime": menuInfo.UpdateTime,
			"isrelease":  false,
			"path":       menuInfo.Path,
		}
	}

	err = escli.NewESClientCl.UpdByID(common.DanastudioAuth, common.TbMenu, menuInfo.Id, body)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

func FindMenu(menuList []Info, id string) Info {
	for _, v := range menuList {
		if v.Id == id {
			return v
		}
		if len(v.ChildrenList) > 0 {
			v := FindMenu(v.ChildrenList, id)
			if v.Id != "" {
				return v
			}
		}
	}
	return Info{}
}

func GetAllIdFromInfo(info Info, ids []string) []string {
	ids = append(ids, info.Id)
	if len(info.ChildrenList) > 0 {
		for _, v := range info.ChildrenList {
			ids = GetAllIdFromInfo(v, ids)
		}
	}
	return ids
}

// 删除
func DeleteMenu(id string, menuList []Info) error {
	var idList []string
	// 递归找到所有的目录，并删除
	info := FindMenu(menuList, id)
	idList = GetAllIdFromInfo(info, idList)
	// 找到父级ID，若父级没有其它子菜单则直接删除
	parentInfo := FindMenu(menuList, info.ParentID)
	// 若父级只有一个菜单，则将改ID也加入到删除列表中
	// 由于用户只能创建两级菜单，所以不需要继续查找父级ID
	if len(parentInfo.ChildrenList) == 1 {
		idList = append(idList, parentInfo.Id)
	}
	// 删除菜单表中的数据
	err := esutil.DeleteBulk(common.DanastudioAuth, common.TbMenu, idList)
	if err != nil {
		logger.Error.Println(err)
	}
	return err
}

func ReleaseMenu(ids []string) error {
	var must []interface{}
	var projectTerm = gin.H{
		"terms": gin.H{
			"id": ids,
		},
	}
	must = append(must, projectTerm)

	// 使用脚本的方式
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
		"script": gin.H{
			"inline": fmt.Sprintf("ctx._source.isrelease=%v", true),
		},
	}
	err := escli.NewESClientCl.UpdByQuery(common.DanastudioAuth, common.TbMenu, body)
	if err != nil {
		logger.Error.Println(err)

	}
	return err
}

func (m *ModifyInfo) ReName() (mes string, err error) {
	// v4.6.2 重命名
	id := m.ProjectID + "_" + m.Id

	response, err := ESClient.Search().Index(DataBase).Type(TbMenuSetting).
		Query(elastic.NewTermQuery("id", id)).
		Do(context.Background())

	var sorts = -1
	var menuInfo ModifyInfo
	if response.Hits.TotalHits == 1 {
		for _, v := range response.Hits.Hits {
			_ = json.Unmarshal(*v.Source, &menuInfo)
			sorts = menuInfo.Sort
		}
	}

	body := gin.H{
		"id":         id,
		"newname":    m.Name,
		"projectid":  m.ProjectID,
		"menuid":     m.Id,
		"sort":       sorts,
		"updatetime": time.Now().Format("2006-01-02 15:04:05"),
	}

	_, err = ESClient.Index().Index(DataBase).Type(TbMenuSetting).Id(id).
		BodyJson(body).
		Refresh("true").
		Do(context.Background())

	//response, err := ESClient.Search().Index(DataBase).Type(TbMenu).
	//	Query(es.NewTermQuery("name", m.Name)).
	//	Do(context.Background())
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	return menuInfo.Name, err
}

func (m *ModifyInfo) MoveMenu() (mes string, err error) {

	//response, err := ESClient.Search().Index(DataBase).Type(TbMenu).
	//	Query(es.NewTermQuery("parentid", m.ParentID)).
	//	//Query(es.NewTermQuery("s","2")).
	//	Do(context.Background())
	//if err != nil {
	//	logger.Error.Println(err)
	//	return "移动失败！！！", err
	//}

	menulist, err := ListMenu(m.ProjectID, true)

	info, err := searchProject(m.ProjectID)
	if err != nil {
		logger.Error.Println(err)
		return "移动失败！！！", err
	}

	//区分开发平台和数据门户的菜单移动
	var dev []string
	var portal []string
	for _, v := range info.ModuleIDs {
		_, ok := common.PortalMap[v]
		if ok {
			portal = append(portal, v)
		} else {
			dev = append(dev, v)
		}
	}

	// 遍历，找到项目拥有权限对应的菜单
	var resultList []Info
	if m.MoveType == "portal" {
		for _, value := range portal {
			for _, projectInfo := range menulist {
				if value == projectInfo.Id {
					resultList = append(resultList, projectInfo)
					break
				}
			}
		}

		// 自定义
		for _, projectInfo := range menulist {
			if projectInfo.ProjectID == m.ProjectID && projectInfo.Owner == "portal" {
				resultList = append(resultList, projectInfo)
			}
		}
	} else {
		for _, value := range dev {
			for _, projectInfo := range menulist {
				if value == projectInfo.Id {
					resultList = append(resultList, projectInfo)
					break
				}
			}
		}

		// 自定义
		for _, projectInfo := range menulist {
			if projectInfo.ProjectID == m.ProjectID && projectInfo.Owner != "portal" {
				resultList = append(resultList, projectInfo)
			}
		}
	}

	var o []int
	for _, value := range resultList {
		if value.ParentID == m.ParentID {
			o = append(o, value.Sort)
		}
	}

	sort.Ints(o)
	//fmt.Printf("o2:%+v\n", o)
	orignSort := m.Sort
	var newSort int //

	for key, value := range o {
		if value == orignSort {
			if m.Move == "up" {
				newSort = o[key-1]
			} else {
				newSort = o[key+1]
			}
		}
	}

	//if m.Move == "up" {
	//	newSort = orignSort - 1
	//} else {
	//	newSort = orignSort + 1
	//}

	for _, v := range resultList {

		if v.Id == m.Id && v.ParentID == m.ParentID {
			id := m.ProjectID + "_" + m.Id
			// id  存在，要加 v.name  不存在 ""
			termQuery := elastic.NewTermQuery("id", id)
			qmust := elastic.NewBoolQuery().Must(termQuery)
			resss, err := ESClient.Search().Index(DataBase).Type(TbMenuSetting).Query(qmust).
				Do(context.Background())

			body := gin.H{
				"id": id,
				//"newname":    name ,
				"projectid":  m.ProjectID,
				"menuid":     m.Id,
				"sort":       newSort,
				"updatetime": time.Now().Format("2006-01-02 15:04:05"),
			}
			if resss.Hits.TotalHits == 1 {
				_, err = ESClient.Update().Index(DataBase).Type(TbMenuSetting).Id(id).
					Doc(body).
					RetryOnConflict(100).
					Refresh("true").
					Do(context.Background())
				if err != nil {
					logger.Error.Println(err)
					return "移动失败！！！", err
				}
			} else {
				_, err = ESClient.Index().Index(DataBase).Type(TbMenuSetting).Id(id).
					BodyJson(body).
					Refresh("true").
					Do(context.Background())
				if err != nil {
					logger.Error.Println(err)
					return "移动失败！！！", err
				}
			}
		}

		if v.Sort == newSort && v.ParentID == m.ParentID {
			id := m.ProjectID + "_" + v.Id
			// id  存在，要加 v.name  不存在 ""
			termQuery := elastic.NewTermQuery("id", id)
			qmust := elastic.NewBoolQuery().Must(termQuery)
			resss, err := ESClient.Search().Index(DataBase).Type(TbMenuSetting).Query(qmust).
				Do(context.Background())

			body := gin.H{
				"id": id,
				//"newname":    name ,
				"projectid":  m.ProjectID,
				"menuid":     v.Id,
				"sort":       orignSort,
				"updatetime": time.Now().Format("2006-01-02 15:04:05"),
			}
			if resss.Hits.TotalHits == 1 {
				_, err = ESClient.Update().Index(DataBase).Type(TbMenuSetting).Id(id).
					Doc(body).
					RetryOnConflict(100).
					Refresh("true").
					Do(context.Background())
				if err != nil {
					logger.Error.Println(err)
					return "移动失败！！！", err
				}
			} else {
				_, err = ESClient.Index().Index(DataBase).Type(TbMenuSetting).Id(id).
					BodyJson(body).
					Refresh("true").
					Do(context.Background())
				if err != nil {
					logger.Error.Println(err)
					return "移动失败！！！", err
				}
			}

		}
	}
	return "移动成功！！！", nil
}

// 拼接项目下的菜单
func GetMenuByProject(projectID string, menuIDList []string) ([]InfoForChoose, error) {
	var projectMenuList []Info
	// 获取原始菜单
	menuList, err := ListMenu(projectID, true)
	if err != nil {
		return nil, err
	}
	// 遍历给初始角色赋值
	for _, menu := range menuList {
		// 将项目下用户添加的页面放入项目的菜单中
		if menu.ProjectID == projectID {
			menu.Auth = "0"
			menu.Checked = false
			projectMenuList = append(projectMenuList, menu)
			continue
		}
		for _, menuID := range menuIDList {
			// 剔除项目设置的权限
			//if menuID == common.ProjectSetting {
			//	continue
			//}
			if menuID == menu.Id {
				menu.Auth = "0"
				menu.Checked = false
				projectMenuList = append(projectMenuList, menu)
				break
			}
		}
	}

	// 拼接名为全部的菜单给前端使用
	var allDevInfo Info
	allDevInfo.Sort = 0
	allDevInfo.Id = "checkalldev"
	allDevInfo.Name = "开发平台"
	allDevInfo.Checked = false
	allDevInfo.Auth = "0"

	// 遍历数据将名为全部的放在首列
	var menuAll []Info
	menuAll = append(menuAll, allDevInfo)
	menuAll = append(menuAll, projectMenuList...)
	return CutMenu(ParseMenuData(menuAll)), nil
}

// 转换结构体，减少无用字段的传
func CutMenu(menuList []Info) []InfoForChoose {
	var forChooseList []InfoForChoose
	for _, value := range menuList {
		var forChoose InfoForChoose
		forChoose.Sort = value.Sort
		forChoose.Id = value.Id
		forChoose.ParentID = value.ParentID
		forChoose.Checked = value.Checked
		forChoose.Auth = value.Auth
		forChoose.Name = value.Name
		forChoose.IsOriginal = value.IsOriginal
		forChoose.Owner = value.Owner
		// 若存在子集则递归调用
		if value.ChildrenList != nil {
			forChoose.ChildrenList = CutMenu(value.ChildrenList)
		}
		forChooseList = append(forChooseList, forChoose)
	}
	return forChooseList
}

// 转换结构体，减少无用字段的传
func CutMenuForProject(menuList []Info) []InfoForProjectList {
	var forChooseList []InfoForProjectList
	for _, value := range menuList {
		var forChoose InfoForProjectList
		forChoose.Sort = value.Sort
		forChoose.Id = value.Id
		forChoose.ParentID = value.ParentID
		forChoose.Checked = value.Checked
		forChoose.Disabled = value.Disabled
		forChoose.Name = value.Name
		// 若存在子集则递归调用
		if value.ChildrenList != nil {
			forChoose.ChildrenList = CutMenuForProject(value.ChildrenList)
		}
		forChooseList = append(forChooseList, forChoose)
	}
	return forChooseList
}

// 转换菜单格式方法，将菜单从列表结构转为层级结构
func ParseMenuData(menuList []Info) []Info {
	var navList []Info
	var newList []Info
	// 取出导航栏的数据
	for _, value := range menuList {
		// 找到所有导航栏的数据，没有父菜单则是导航栏
		if value.ParentID == "" {
			navList = append(navList, value)
		} else {
			// 将不为菜单的数据单独取出，以减少遍历次数
			newList = append(newList, value)
		}
	}

	// 递归获取所有子集菜单
	for index, value := range navList {
		var childrenList []Info
		childrenList, newList = getChildren(value, newList)
		navList[index].ChildrenList = childrenList
	}
	return navList
}

// 递归方法，获取所有层级的子菜单
func getChildren(parentMenu Info, menuList []Info) (childrenMenuList []Info, remainMenuList []Info) {
	for _, value := range menuList {
		// 若当前循环的菜单的父菜单ID是所给菜单的ID，则表示该菜单是其的子菜单
		if value.ParentID == parentMenu.Id {
			childrenMenuList = append(childrenMenuList, value)
			// 否则将其赋给剩余菜单，进行下次遍历
		} else {
			remainMenuList = append(remainMenuList, value)
		}
	}
	// 继续遍历下层
	for index, value := range childrenMenuList {
		var childrenList []Info
		childrenList, remainMenuList = getChildren(value, remainMenuList)
		childrenMenuList[index].ChildrenList = childrenList
	}
	return childrenMenuList, remainMenuList
}

// 转换菜单格式方法，将菜单从列表结构转为层级结构
func ParseMenuDataForShow(menuList []Info) []Info {
	var navList []Info
	var newList []Info
	allCheckFlag := true
	// 取出导航栏的数据
	for _, value := range menuList {
		// 找到所有导航栏的数据，没有父菜单则是导航栏
		if value.ParentID == "" {
			navList = append(navList, value)
		} else {
			// 将不为菜单的数据单独取出，以减少遍历次数
			newList = append(newList, value)
		}
		if !value.Checked {
			allCheckFlag = false
		}
	}

	// 递归获取所有子集菜单
	for index, value := range navList {
		var childrenAllChecked bool
		var childrenList []Info
		childrenList, newList, childrenAllChecked = getChildrenForShow(value, newList)
		navList[index].ChildrenList = childrenList
		if !childrenAllChecked {
			navList[index].Checked = false
		}
	}
	// 拼接名为全部的菜单栏,以便前端渲染使用
	var allInfo Info
	allInfo.Id = "checkall"
	allInfo.Checked = allCheckFlag
	allInfo.Disabled = false
	allInfo.Name = "全部"
	allInfo.Sort = 0
	allInfo.Auth = "0"
	//navList = append(navList, allInfo)
	// 遍历数据将名为全部的放在首列
	var menuAll []Info
	menuAll = append(menuAll, allInfo)
	menuAll = append(menuAll, navList...)
	return navList
}

// 递归方法，获取所有层级的子菜单
func getChildrenForShow(parentMenu Info, menuList []Info) (childrenMenuList []Info, remainMenuList []Info, allChecked bool) {
	allChecked = true
	for _, value := range menuList {
		// 若当前循环的菜单的父菜单ID是所给菜单的ID，则表示该菜单是其的子菜单
		if value.ParentID == parentMenu.Id {
			childrenMenuList = append(childrenMenuList, value)
			if !value.Checked {
				allChecked = false
			}
			// 否则将其赋给剩余菜单，进行下次遍历
		} else {
			remainMenuList = append(remainMenuList, value)
		}
	}
	// 继续遍历下层
	for index, value := range childrenMenuList {
		var childrenList []Info
		var childrenAllChecked bool
		childrenList, remainMenuList, childrenAllChecked = getChildrenForShow(value, remainMenuList)
		childrenMenuList[index].ChildrenList = childrenList
		if !childrenAllChecked {
			childrenMenuList[index].Checked = false
			allChecked = false
		}
	}
	return childrenMenuList, remainMenuList, allChecked
}

func RecoveryMenu(info RecoveryMenuInfo, projectID string) error {
	if info.ID == "" && info.NavID == "" && info.CatalogID == "" {
		return nil
	}
	var addList []ModifyInfo
	var navInfo ModifyInfo
	if !info.NavIsOriginal && info.NavName != "" && info.NavID != "" {
		navInfo.Id = info.NavID
		navInfo.ParentID = ""
		navInfo.Sort = info.NavSort
		navInfo.Name = info.NavName
		navInfo.IsOriginal = false
		navInfo.CreateTime = time.Now().Format(TimeFormat)
		navInfo.UpdateTime = navInfo.CreateTime
		navInfo.IsRelease = true
		navInfo.Disabled = false
		navInfo.Path = navInfo.Id
		navInfo.Icon = ""
		navInfo.ProjectID = projectID
		addList = append(addList, navInfo)
	}
	var catalogInfo ModifyInfo
	if !info.CatalogIsOriginal && info.CatalogName != "" && info.CatalogID != "" {
		catalogInfo.Id = info.CatalogID
		catalogInfo.ParentID = info.NavID
		catalogInfo.Sort = info.CatalogSort
		catalogInfo.Name = info.CatalogName
		catalogInfo.IsOriginal = false
		catalogInfo.CreateTime = time.Now().Format(TimeFormat)
		catalogInfo.UpdateTime = catalogInfo.CreateTime
		catalogInfo.IsRelease = true
		catalogInfo.Disabled = false
		catalogInfo.Path = catalogInfo.Id
		catalogInfo.Icon = "icon-customize"
		catalogInfo.ProjectID = projectID
		addList = append(addList, catalogInfo)
	}
	var menuInfo ModifyInfo
	menuInfo.Id = info.ID
	menuInfo.ParentID = ""
	if info.CatalogID != "" {
		menuInfo.ParentID = info.CatalogID
	} else if info.NavID != "" {
		menuInfo.ParentID = info.NavID
	}
	menuInfo.Sort = info.PageSort
	menuInfo.Name = info.PageName
	menuInfo.IsOriginal = false
	menuInfo.CreateTime = time.Now().Format(TimeFormat)
	menuInfo.UpdateTime = menuInfo.CreateTime
	menuInfo.IsRelease = true
	menuInfo.Disabled = false
	menuInfo.Path = info.ID
	menuInfo.Icon = ""
	menuInfo.ProjectID = projectID
	menuInfo.PageUrl = info.PageUrl
	addList = append(addList, menuInfo)
	err := esutil.AddBulk(common.DanastudioAuth, common.TbMenu, "id", addList)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	return nil
}

// cp project 那边的方法，那边引入这边方法，再应用汇循环，
func searchProject(id string) (projectInfo ProjectInfo, err error) {
	body := gin.H{
		"query": gin.H{
			"term": gin.H{
				"projectid": id,
			},
		},
		"size": common.UnLimitSize,
	}

	result, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, common.TbProject, body)
	if err != nil {
		logger.Error.Println(err)
		return projectInfo, err
	}
	// 解析数据
	info, err := escli.NewESClientCl.GetSourceFirst(result)
	if err != nil {
		logger.Error.Println(err)
		return projectInfo, err
	}
	_ = json.Unmarshal([]byte(info), &projectInfo)
	return projectInfo, nil
}

// 列举系统菜单
func ListAllSysMenu() (menuList []MiniInfo, err error) {
	// 获取导航层级的菜单名及其ID
	body := gin.H{
		"size": common.UnLimitSize,
	}
	resp, err := escli.NewESClientCl.SearchByTerm(common.DanastudioAuth, common.TbMenu, body)
	if err != nil {
		logger.Error.Println(err)
		return []MiniInfo{}, err
	}
	sourceList := escli.NewESClientCl.GetSource(resp)
	for _, value := range sourceList {
		var menuInfo MiniInfo
		_ = json.Unmarshal([]byte(value), &menuInfo)
		menuList = append(menuList, menuInfo)
	}

	return menuList, nil
}
