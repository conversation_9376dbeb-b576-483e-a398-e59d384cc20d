package common

// 全局接口--不需要权限
var Whole = []string{
	"users/notifyrealinfo",
	"users/issso",
	"users/info",
	"users/notify/list",
	"platform/getconfig",
	"platform/nodes/overview",
	"auth/theme",
	"portal/footer/get",
	"portal/carousel/get",
	"asset/oem/listoeminfo",
	"version",
	"menu/list",
	"metadata/collect/tbsyncpolling",
	"refresh_token",
	"log/submit/logout",
	"metadata/collect/updatesyncstatus",
	"ants/access/realtime/pushapi",
	"modeling/resource/preview",
	"modeling/resource/thumbnail",
	"modeling/resource/gettoken",
	"users/getdownloadkey",
	"/api/run",
	"/apiauth/postnoauth",
	"/apiauth/postbasicauth",
	"/apiauth/postapikeyauth",
	"/apiauth/postjwtauth",
	"/apiauth/postjwtauthes",
	"/apiauth/postjwtauthrs",
	"/apiauth/postjwtauthps",
	"/apiauth/postoauthclientauth",
	"/apiauth/postoauthpasswdauth",
	"/apiauth/getnoauth",
	"/apiauth/getbasicauth",
	"/apiauth/getapikeyauth",
	"/apiauth/getjwtauth",
	"/apiauth/getjwtauthrs",
	"/apiauth/getjwtauthps",
	"/apiauth/getjwtauthes",
	"/apiauth/posttokenauth",
	"/apiauth/gettokenauth",
	"metadata/lineage/setconfig",
	"metadata/lineage/getconfig",
	"metadata/lineage/list",
}

// 首页
var portalIndex = []string{
	"portal/homepage/search",
	"portal/homepage/trending",
	"portal/homepage/indicator",
	"portal/homepage/charts/A0",
	"portal/homepage/charts/A4",
	"portal/homepage/charts/A5",
	"portal/homepage/charts/A6",
	"portal/homepage/charts/A10",
	"portal/homepage/charts/A11",
	"portal/homepage/charts/A12",
	"portal/homepage/charts/A13",
	"portal/homepage/charts/A14",
	"portal/homepage/charts/A15",
	"portal/homepage/charts/A16",
	"portal/homepage/charts/A17",
	"portal/homepage/charts/A18",
	"portal/homepage/charts/B1",
	"portal/homepage/charts/B2",
	"portal/homepage/charts/B3",
	"portal/homepage/charts/B4",
	"portal/homepage/charts/C1",
	"portal/homepage/charts/C2",
	"portal/homepage/charts/C3",
	"portal/homepage/charts/C4",
	"portal/homepage/charts/C5",
	"portal/homepage/charts/C6",
	"portal/homepage/charts/C7",
	"portal/homepage/charts/C8",
	"portal/homepage/charts/C9",
	"portal/homepage/charts/C11",
	"portal/homepage/charts/C12",
	"portal/homepage/charts/C14",
	"portal/homepage/charts/C15",
	"portal/infoclass/list",
	"portal/info/list"}

// 运行管理
var runManagement = []string{
	"portal/management/charts/A1",
	"portal/management/charts/B1",
	"portal/management/charts/B2",
	"portal/management/charts/B3",
	"portal/management/charts/B4",
	"portal/management/charts/B5",
	"portal/management/charts/B6",
	"portal/management/charts/B7",
	"portal/management/charts/B8",
	"portal/management/charts/B9",
	"portal/management/charts/B10",
	"portal/management/charts/D1",
	"portal/management/charts/D2",
	"portal/management/charts/D3",
	"portal/management/charts/D4",
	"portal/management/charts/D5",
	"portal/management/charts/D6",
	"portal/management/charts/C1",
	"portal/management/charts/C2",
	"portal/management/charts/C3",
	"portal/management/charts/C4",
	"portal/management/charts/C5",
	"portal/management/charts/C6",
	"portal/management/charts/C7",
	"portal/management/charts/C8"}

// 开放数据
var public = []string{
	"modeling/catalog/searchused",
	"portal/datasource/detail",
	"modeling/category/list",
	"portal/datasource/list",
	"portal/datasource/conditions",
	"modeling/catalog/searchused",
	"portal/collect",
	"portal/cancel/collect",
	"modeling/datacorr/datacorrsubmit",
	"portal/score",
	"service/downloadOffLine",
	"portal/calling/add",
	"portal/calling/reapply",
	"modeling/moneyattach/applist",
	"portal/protocol/list",
	"portal/calling/documentation",
	"users/get/department",
	"portal/useragreementdoc",
	"portal/calling/customquery",
	"metadata/moneycenter/sqlsearch",
	"portal/footer/get",
	"portal/apidoc",
}

// 共享数据
var sharedData = []string{
	"modeling/catalog/searchused",
	"portal/datasource/detail",
	"modeling/category/list",
	"portal/datasource/list",
	"portal/datasource/conditions",
	"modeling/catalog/searchused",
	"portal/collect",
	"portal/cancel/collect",
	"modeling/datacorr/datacorrsubmit",
	"portal/score",
	"service/downloadOffLine",
	"portal/calling/add",
	"portal/calling/reapply",
	"modeling/moneyattach/applist",
	"portal/protocol/list",
	"portal/calling/documentation",
	"users/get/department",
	"users/list/project",
	"portal/useragreementdoc",
	"portal/calling/customquery",
	"metadata/moneycenter/sqlsearch",
	"portal/footer/get",
	"portal/apidoc",
}

// 共享开放目录
var openCatalog = []string{
	"modeling/catalog/searchused",
	"portal/datasource/detail",
	"modeling/category/list",
	"portal/datasource/list",
	"portal/datasource/conditions",
	"modeling/catalog/searchused",
	"portal/collect",
	"portal/cancel/collect",
	"modeling/datacorr/datacorrsubmit",
	"portal/score",
	"service/downloadOffLine",
	"portal/calling/add",
	"portal/calling/reapply",
	"modeling/moneyattach/applist",
	"portal/protocol/list",
	"portal/calling/documentation",
	"users/get/department",
	"portal/useragreementdoc",
	"portal/calling/customquery",
	"metadata/moneycenter/sqlsearch",
	"portal/footer/get",
	"portal/apidoc",
	"modeling/version/download",
	"portal/dir/list",
	"portal/dir/detail",
	"portal/dir/search",
	"portal/dir/form/detail",
}

// 数据需求
var requirements = []string{
	"modeling/datareq/datareqlist",
	"modeling/datareq/datareqsearch",
	"modeling/datareq/datareqsubmit"}

// 数据纠错
var corrections = []string{
	"modeling/datacorr/datacorrlist",
	"modeling/category/list",
	"portal/datasource/list",
	"modeling/datacorr/datacorrsearch",
	"modeling/datacorr/datacorrsubmit"}

// 数据申请
var applications = []string{"portal/calling/list"}

// 帮助中心
var helpCenter = []string{
	"portal/help/download",
	"portal/help/list",
}

// 应用管理
var applicationManage = []string{
	"modeling/moneyattach/appaggdownload",
	"modeling/moneyattach/appfdownload",
	"modeling/moneyattach/appdelete",
	"modeling/moneyattach/applist",
	"portal/calling/list",
	"modeling/moneyattach/appsave",
	"modeling/moneyattach/appsave/v1",
	"users/get/department",
	"users/list/project",
	"modeling/catalog/searchused",
	"portal/footer/get",
	"modeling/moneyattach/appfdownload",
}

// 我的数据
var portalPersonal = []string{
	"portal/mydata/show",
	"portal/mydata/tbsearch",
	"modeling/datacorr/datacorrsubmit",
	"users/needcode",
	"platform/getconfig",
	"verify_code",
	"users/avatar",
	"insidelogin",
	"users/modify/basicinfo",
	"users/info",
	"users/update/auth",
	"portal/mydata/get",
	"portal/datebase/list"}

// 我的消息
var portalMessageRecord = []string{
	"users/notify/list",
	"users/notify/markread",
	"audit/formischeck",
	"users/notify/delete"}

// 我的收藏
var portalCollection = []string{
	"portal/cancel/collect",
	"portal/collection/list"}

// 我的申请
var useApplication = []string{
	"modeling/catalog/searchused",
	"portal/protocol/list",
	"portal/calling/list",
	"portal/calling/detail",
	"portal/apidetaildoc",
	"portal/apidoc",
	"portal/useragreementdoc",
	"portal/calling/auditdetail",
	"modeling/datacorr/datacorrsubmit",
	"portal/calling/flow",
	"portal/calling/flowv2",
	"portal/calling/delete",
	"portal/calling/renewal",
	"portal/calling/downloadOffline",
	"modeling/moneyattach/applist",
	"users/list/project",
	"users/get/department",
	"portal/calling/documentation",
	"portal/calling/reapply",
	"portal/calling/withdrawal",
	"portal/calling/getOffLine",
	"portal/calling/getwsmessage"}

// 我的需求
var myNeeds = []string{"portal/data/reqsearch"}

// 我的纠错
var errorCorrection = []string{
	"modeling/datacorr/datacorrsubmit",
	"portal/data/corrsearch",
	"portal/data/corrscore"}

// 我的待办
var portalTodoList = []string{
	"modeling/catalog/searchused",
	"portal/calling/getdoc",
	"audit/gettransuser",
	"users/group/list",
	"service/detail",
	"modeling/inventory/detail",
	"portal/calling/auditdetail",
	"modeling/datareq/detail",
	"modeling/datacorr/detail",
	"audit/runcheck",
	"audit/auditchart",
	"audit/auditchartv2",
	"audit/buildcheck",
	"audit/buildcheckv2",
	"audit/listrecords",
	"metadata/moneycenter/tablebasicinfo",
	"modeling/inventory/detail",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/sqlsearch",
	"metadata/moneycenter/tablefieldinfo",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"modeling/moneyattach/tpapilist",
	"service/detail",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/tablefieldpartitioninfo",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"modeling/moneyattach/putinfo",
	"metadata/moneycenter/tablebasicinfo",
	"modeling/inventory/detail",
	"metadata/moneycenter/tablefieldinfo",
	"metadata/moneycenter/tablebasicinfo",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/sqlsearch",
	"portal/calling/getdocdir",
}

// 数据源
var datasource = []string{
	"metadata/collect/getdbversion",
	"metadata/collect/stopsyncextr",
	"ants/folder/list",
	"ants/folder/create",
	"ants/folder/update",
	"ants/folder/delete",
	"metadata/collect/newdatabase",
	"metadata/collect/tbupload",
	"metadata/collect/localtb/batchdel",
	"metadata/collect/dbcheck",
	"metadata/collect/dbdelete",
	"metadata/collect/dblist",
	"metadata/collect/dblog",
	"metadata/collect/dbupdate",
	"metadata/collect/tbhst",
	"metadata/collect/singletbinfo",
	"metadata/collect/autosyncextr",
	"metadata/collect/tbsync",
	"metadata/collect/tbsyncpolling",
	"metadata/collect/tbsyncdelete",
	"metadata/collect/listtemplate",
	"metadata/collect/addtemplate",
	"metadata/collect/deletetemplate",
	"metadata/collect/mobiledatabase",
	"metadata/collect/tbsearchnew",
	"metadata/collect/tbcompetence",
	"metadata/collect/gettimezone",
	"metadata/collect/currenttime",
	"platform/driver/list",
	"dodox/filemanager/file/uploads",
	"metadata/collect/listapiurl",
	"metadata/collect/edittemplate",
	"asset/codecollect/search",
	"ants/access/reportcheck/search",
	"metadata/collect/newapi",
	"metadata/collect/apilist",
	"metadata/collect/apiupdate",
	"metadata/collect/apidelete",
	"metadata/collect/apijson",
}

// 分层管理
var layerManage = []string{
	"metadata/devcenter/listdatabase",
	"metadata/devcenter/listsource",
	"metadata/devcenter/listlayer",
	"metadata/devcenter/layerdependent",
	"metadata/devcenter/allauth",
	"metadata/devcenter/tasklist",
	"metadata/devcenter/addlayer",
	"metadata/devcenter/dellayer",
	"metadata/devcenter/movelayer",
	"metadata/devcenter/addsource",
	"metadata/devcenter/listengine",
	"metadata/devcenter/listsourcepro",
	"metadata/devcenter/listschemas",
	"metadata/devcenter/delsource",
}

// 表模型管理
var modelManage = []string{
	"metadata/devcenter/listleveldir",
	"metadata/devcenter/adddir",
	"metadata/devcenter/listorder",
	"metadata/devcenter/catorder",
	"metadata/devcenter/listdir",
	"metadata/devcenter/deldir",
	"metadata/devcenter/movedir",
	"metadata/devcenter/tbdetail",
	"ants/folder/list",
	"metadata/devcenter/extractinfo",
	"metadata/devcenter/deltable",
	"metadata/devcenter/listlayerinfo",
	"metadata/devcenter/addtable",
	"metadata/devcenter/addtablebulk",
	"metadata/devcenter/tbdetail",
	"metadata/devcenter/editingtable",
	"metadata/devcenter/tbdetail",
	"metadata/devcenter/getsynctime",
	"metadata/devcenter/tbname",
	"metadata/devcenter/syncallsource",
	"metadata/devcenter/listhistory",
	"metadata/devcenter/listtablenew",
	"metadata/devcenter/isdeltb",
	"metadata/devcenter/stopsyncsource",
	"ants/develop/new/dev",
	"ants/develop/save",
	"ants/develop/checksave",
	"ants/develop/run",
	"ants/develop/result",
	"ants/develop/stop",
	"metadata/devcenter/multiinput",
	"metadata/devcenter/multicheck",
	"metadata/collect/dblist",
	"metadata/devcenter/inputtable",
	"metadata/devcenter/betchinputtable",
	"metadata/collect/tbsearchnew",
	"metadata/devcenter/extractinfo"}

// 实时消息流管理
var realTimeData = []string{
	"metadata/collect/getrealmessagenum",
	"metadata/collect/deleterealmessage",
	"metadata/collect/listrealmessage",
	"metadata/collect/createrealmessage",
	"metadata/collect/detailrealmessage",
	"metadata/collect/updatemessage",
	"metadata/collect/relatedtasks",
	"metadata/collect/listexisttopic",
	"metadata/collect/gettopicinfo",
	"metadata/collect/synccallrealmessage",
	"metadata/collect/syncall/stopsyncrealmessage",
}

// 自定义函数
var customFunction = []string{
	"metadata/devcenter/listlayerinfo",
	"asset/udfget/filepath",
	"asset/udf/sync",
	"asset/udf/syncstatus",
	"asset/udf/delete",
	"asset/udf/folder/search",
	"asset/udf/new/search",
	"asset/udf/folder/create",
	"asset/udf/new/search",
	"metadata/devcenter/listlayerinfo",
	"asset/udf/regisstatus",
	"asset/udf/move",
	"asset/udf/syncstatus",
	"asset/udf/detail",
	"asset/udf/new",
	"asset/udf/edit",
	"dodox/filemanager/file/jarupload",
	"asset/udf/folder/update"}

// 标签管理
var tags = []string{
	"metadata/devcenter/listdir",
	"metadata/devcenter/listtag",
	"metadata/devcenter/deltag",
	"metadata/devcenter/addtag",
	"metadata/devcenter/movetag",
	"metadata/devcenter/listleveldir",
	"metadata/devcenter/adddir",
	"metadata/devcenter/deldir"}

// 脱敏规则
var desensitization = []string{
	"publish/rules/search",
	"publish/rules/delete",
	"publish/rules/update",
	"publish/rules/add",
	"publish/rules/search"}

// 敏感等级
var level = []string{
	"ants/dataprocess/sensitiverank/list",
	"ants/dataprocess/sensitiverank/save",
	"ants/dataprocess/sensitiverank/delete"}

// 敏感数据识别
var identification = []string{
	"ants/dataprocess/sensitiverank/list",
	"ants/dataprocess/sensitivecheck/save",
	"ants/dataprocess/sensitivecheck/list",
	"ants/dataprocess/sensitivecheck/delete"}

// 填报校验规则
var dataFillingRules = []string{
	"asset/codecollect/search",
	"ants/access/reportcheck/search",
	"ants/access/reportcheck/search",
	"ants/access/reportcheck/detail",
	"ants/access/reportcheck/save",
	"ants/access/reportcheck/delete",
}

// 库表采集
var offline = []string{
	"ants/folder/list",
	"ants/folder/delete",
	"ants/folder/create",
	"ants/folder/update",
	"ants/access/realtime/submittasks",
	"ants/access/dataxsearch",
	"ants/access/realtime/quicksilver",
	"ants/access/realtime/taskstatus",
	"ants/access/realtime/operate",
	"ants/access/submit",
	"ants/access/batchsubmit",
	"ants/access/old",
	"ants/access/delete",

	"ants/access/datareport/sync",
	"ants/access/datareport/save",
	"ants/access/datareport/search",
	"ants/access/datareport/delete",
	"ants/access/datareport/detail",
	"ants/access/datareport/publish",
	"ants/access/datareport/template/download",
	"ants/access/datareport/putdata",
	"ants/access/datareport/putdata/list",
	"ants/access/datareport/putdata/query",
	"ants/access/datareport/putdata/download",
	"ants/access/datareport/authchange",
	"ants/access/datareport/putdata/tempcheck",
	"ants/access/datareport/putdata/check",
	"ants/access/datareport/putdata/checkdownload",
	"ants/access/reportcheck/search",
	"ants/access/reportcheck/detail",
	"ants/access/reportcheck/save",
	"ants/access/reportcheck/delete",

	"ants/access/dataxmove",
	"metadata/devcenter/listtagdir",
	"metadata/devcenter/addtable",
	"metadata/devcenter/addtablebulk",

	"metadata/devcenter/acctbdetail",
	"metadata/devcenter/inputtable",
	"metadata/devcenter/listlayer",
	"metadata/collect/dblist",
	"dodox/job/isrunning",
	"dodox/job/del",
	"dodox/job/isbeingoffline",
	"metadata/devcenter/listtagdir",
	"ants/access/dataxsearch",
	"ants/access/newdatax",
	"ants/access/updatedatax",
	"ants/access/dataxdetail",
	"metadata/devcenter/tbname",
	"metadata/devcenter/addtable",
	"metadata/devcenter/addtablebulk",

	"ants/access/content",
	"ants/access/singletodefine",
	"metadata/devcenter/listlayerinfo",
	"metadata/devcenter/acctbdetail",
	"metadata/devcenter/listtablenew",
	"metadata/devcenter/inputtable",
	"ants/access/fieldtrans",
	"metadata/collect/fieldcomparison",
	"ants/folder/list",
	"metadata/collect/dblist",
	"metadata/collect/tbsearchnew",
	"metadata/collect/singletbinfo",
	"metadata/collect/tblist",
	"metadata/collect/tbcompetence",
	"ants/access/wherecontentjudge",
	"ants/workflow/list/submitjobv2",
	"ants/inside/dodox/getnode",
	"manager/batch/resource/templete",
	"ants/access/save",
	"ants/access/run",
	"ants/access/result",
	"metadata/collect/dblist",
	"metadata/collect/tbsearchnew",
	"metadata/collect/sourcetblist",
	"metadata/collect/tbcompetence",
	"metadata/collect/singletbinfo",
	"metadata/collect/fieldcomparison",
	"metadata/devcenter/betchinputtable",
	"metadata/devcenter/listrenametable",
	"metadata/devcenter/betchinputtable",
	"metadata/devcenter/listlayerinfo",
	"metadata/devcenter/tbname",
	"metadata/devcenter/listtablenew",
	"metadata/devcenter/acctbdetail",
	"ants/access/dataxdetail",
	"ants/access/updatetask",
	"ants/access/updatesubtask",
	"ants/access/newtask",
	"ants/access/subtaskcode",
	"ants/access/dataxsearch",
	"ants/access/wherecontentjudge",
	"ants/access/fieldtrans",
	"ants/folder/create",
	"ants/folder/list",
	"ants/workflow/get/precrontab",
	"ants/inside/dodox/getnode",
	"metadata/devcenter/tbdetail",
	"metadata/devcenter/tbname",
	"metadata/devcenter/addtable",
	"metadata/devcenter/addtablebulk",

	"metadata/devcenter/listlayerinfo",
	"metadata/devcenter/listtablenew",
	"metadata/devcenter/tbdetail",
	"metadata/devcenter/inputtable",
	"metadata/devcenter/listtag",
	"metadata/collect/dblist",
	"metadata/collect/tbcompetence",
	"metadata/collect/singletbinfo",
	"metadata/collect/listtemplate",
	"metadata/collect/tbsearchnew",
	"ants/access/updatedatax",
	"ants/access/dataxdetail",
	"ants/access/newdatax",
	"ants/access/dataxsearch",
	"ants/access/fieldtrans",
	"metadata/devcenter/listlayerinfo",
	"ants/folder/create",
	"ants/folder/delete",
	"ants/folder/list",
	"ants/access/dataxsearch",
	"ants/folder/update",
	"manager/batch/resource/templete",
	"ants/inside/dodox/getnode",
	"ants/access/old",
	"ants/access/dataxdetail",
	"ants/access/save",
	"ants/access/stop",
	"metadata/devcenter/listdir",
	"ants/access/delsubtask",
	"ants/access/subtasklog",
	"ants/access/apiconn",
	"ants/access/apiupdate",
	"ants/access/newapi",
	"ants/access/apipyrun",
	"ants/access/apipystop",
	"ants/access/apipyresult",
}

// 数据治理
var customProcess = []string{
	"metadata/devcenter/listtagdir",
	"platform/engines/curengine",
	"metadata/devcenter/listlayer",
	"ants/folder/list",
	"ants/workflow/searchv2",
	"ants/dataprocess/list/metadata",
	"ants/folder/create",
	"ants/folder/update",
	"ants/folder/delete",
	"ants/workflow/newv2",
	"ants/workflow/contentv2",
	"ants/workflow/taskdetail",
	"ants/inside/dodox/getnode",
	"ants/inside/localip",
	"ants/workflow/searchv2",
	"ants/workflow/list/submitjobv2",
	"ants/workflow/wfexport",
	"ants/workflow/import",
	"ants/workflow/submit",
	"ants/workflow/schedulev2",
	"dodox/job/isrunning",
	"dodox/job/isbeingoffline",
	"dodox/job/del",
	"ants/workflow/movev2",
	"ants/workflow/deletev2",
	"ants/dataprocess/new/modulev2",
	"ants/workflow/list/marjob",
	"ants/workflow/savev2",
	"ants/develop/new/devv2",
	"ants/dataprocess/module/delete",
	"ants/develop/deletev2",
	"ants/workflow/update",
	"ants/dataprocess/cooking/listmodule",
	"ants/dataprocess/query/module",
	"ants/dataprocess/access/fieldget",
	"metadata/devcenter/listtablenew",
	"metadata/moneycenter/sqlsearch",
	"ants/dataprocess/list/tbinfo",
	"ants/dataprocess/access/save",
	"ants/dataprocess/submit/judge",
	"ants/dataprocess/watch/info",
	"ants/dataprocess/watch/infov2",
	"ants/dataprocess/watch/tableget",
	"ants/dataprocess/watch/save",
	"ants/dataprocess/watch/savev2",
	"ants/dataprocess/checkdup",
	"ants/dataquality/rule/list",
	"ants/dataprocess/standard/info",
	"asset/dataprocess/search",
	"asset/codecollect/search",
	"ants/dataprocess/standard/tableget",
	"metadata/devcenter/listdir",
	"metadata/devcenter/liststandard",
	"ants/dataprocess/standard/fieldgroup",
	"asset/codecollect/infos",
	"ants/dataprocess/standard/savepre",
	"ants/dataprocess/cooking/fieldget",
	"ants/dataprocess/cooking/outputsave",
	"ants/dataprocess/sensitive/get",
	"publish/rules/search",
	"ants/dataprocess/sensitive/save",
	"metadata/devcenter/listlayerinfo",
	"metadata/devcenter/listengine",
	"metadata/devcenter/listtag",
	"metadata/devcenter/listtablenew",
	"ants/access/fieldtrans",
	"metadata/devcenter/tbdetail",
	"metadata/devcenter/addtable",
	"metadata/devcenter/addtablebulk",

	"ants/dataprocess/output/savev2",
	"ants/dataprocess/sensitivecheck/module/list",
	"ants/dataprocess/sensitivecheck/module/get",
	"ants/dataprocess/sensitivecheck/module/save",
	"ants/dataprocess/cooking/listmodule",
	"ants/develop/content",
	"ants/develop/fetch",
	"ants/develop/savev2",
	"ants/develop/run",
	"ants/develop/result",
	"ants/develop/star",
	"ants/workflow/run",
	"dodox/job/record/get",
	"dodox/job/record/log",
	"ants/workflow/star",
	"dodox/filemanager/file/upload",
	"ants/workflow/stop",
	"ants/develop/scriptsexportnew",
	"asset/dataprocess/textlist",
	"asset/data/classify/list",
	"ants/dataprocess/module/clean",
	"ants/dataprocess/module/changestatus",
	"asset/data/detail",
	"ants/dataprocess/update/module",
	"ants/dataprocess/where/judge",
	"ants/dataprocess/module/showresult",
	"ants/workflow/batchsubmit",
	"ants/develop/syncdata",
}

// 算子开发
var optdevelopment = []string{
	"ants/develop/search",
	"ants/develop/fetch",
	"ants/develop/detail",
	"ants/develop/copy",
	"ants/develop/delete",
	"ants/develop/globalget",
	"ants/develop/content",
	"ants/develop/save",
	"ants/develop/run",
	"ants/develop/stop",
	"ants/develop/search",
	"ants/develop/result",
	"ants/develop/star",
	"ants/develop/recommend/tags",
	"ants/develop/goto",
	"ants/develop/new/dev",
	"ants/folder/list",
	"ants/folder/create",
	"ants/folder/update",
	"ants/folder/delete",
	"platform/engines/curengine",
	"platform/engines/list",
	"search/list/database",
	"metadata/devcenter/listtagdir",
	"dodox/filemanager/file/upload",
	"ants/access/newkettle",
	"ants/develop/scriptsexport",
	"ants/develop/scriptsexportnew",
	"ants/develop/move",
	"ants/develop/list/history",
	"ants/develop/generate/version",
}

// 模型管理
var modelMgm = []string{
	"ants/workflow/search",
	"ants/workflow/content",
	"ants/workflow/new",
	"ants/workflow/save",
	"ants/workflow/run",
	"ants/workflow/stop",
	"ants/workflow/star",
	"ants/workflow/delete",
	"ants/workflow/move",
	"ants/workflow/update",
	"ants/workflow/import",
	"ants/workflow/taskdetail",
	"ants/workflow/schedule",
	"ants/workflow/list/submitjob",
	"ants/workflow/list/submitjobv2",
	"ants/workflow/wfexport",
	"ants/workflow/get/precrontab",
	"ants/folder/list",
	"ants/folder/create",
	"ants/folder/update",
	"ants/folder/delete",
	"ants/dataprocess/list/metadata",
	"ants/analyse/submit",
	"ants/inside/dodox/getnode",
	"ants/inside/localip",
	"ants/access/dataxsearch",
	"ants/access/content",
	"metadata/devcenter/listtagdir",
	"metadata/devcenter/listlayer",
	"platform/engines/curengine",
	"dodox/job/isbeingoffline",
	"dodox/job/isrunning",
	"dodox/job/record/get",
	"dodox/job/del",
	"dodox/job/canceloffline",
	"ants/develop/scriptsexportnew",
	"ants/develop/move",
	"ants/develop/copy",
	"ants/dataprocess/update/module",
}

// 导入导出管理
var loadingInAndOut = []string{"ants/workflow/inout/details"}

// 实时计算
var realtimeComputation = []string{"platform/flinkurl"}

// 作业总览
var dashboard = []string{
	"dodox/job/record/indexdata",
	"dodox/job/record/exectimedistribution",
	"dodox/job/runtimerate",
	"dodox/job/stats/byfield",
	"dodox/job/failtimesrate",
	"dodox/job/queuetimesrate"}

// 实时运维
var actualTime = []string{
	"dodox/alarmrule/reallistuserfield",
	"dodox/alarmrule/realsetuserfield",
	"ants/access/realtime/tasklists",
	"ants/access/realtime/realtaskdetail",
	"ants/access/realtime/realtasklog",
	"ants/access/realtime/operate",
	"metadata/devcenter/tbdetail",
	"ants/access/realtime/pushloglist"}

// 周期任务
var task = []string{
	"dodox/alarmrule/setsinglejob",
	"dodox/alarmrule/setbatchjob",
	"dodox/alarmrule/list",
	"users/list/project/all",
	"metadata/devcenter/listdir",
	"metadata/devcenter/listtag",
	"dodox/alarmrule/listuserfield",
	"dodox/job/runnow",
	"dodox/job/runjob",
	"dodox/job/renew",
	"dodox/job/ban",
	"dodox/job/isrunning",
	"dodox/job/isbeingoffline",
	"dodox/job/del",
	"dodox/job/batchrun",
	"dodox/job/batchrenew",
	"dodox/job/batchdel",
	"dodox/job/batchban",
	"dodox/alarmrule/listequal",
	"dodox/alarmrule/listjob",
	"dodox/job/canceloffline",
	"dodox/job/batchbeingoffline",
	"dodox/job/list",
	"dodox/job/get",
	"dodox/alarmrule/setuserfield",
	"dodox/job/update/cron"}

// 周期作业
var circle = []string{
	"dodox/job/rerun/subjob",
	"dodox/cluster/node/list",
	"dodox/cluster/node/listexeoneDel",
	"dodox/job/batchget",
	"ants/develop/content",
	"dodox/job/getrerunrecord",
	"dodox/job/record/list",
	"dodox/job/record/batchdel",
	"dodox/job/record/batchrerun",
	"dodox/job/runnow",
	"dodox/job/runjob",
	"dodox/job/detail",
	"dodox/job/record/inside/list",
	"dodox/job/record/export/log",
	"dodox/job/record/dataxlog",
	"dodox/job/record/log",
	"dodox/job/record/downerrfile",
	"ants/dataprocess/custom/info",
	"dodox/job/record/del",
	"dodox/job/set/success",
}

// 运行队列
var queue = []string{
	"dodox/job/queue/batchprior",
	"dodox/job/queue/batchdeferred",
	"dodox/job/queue/batchcancel",
	"dodox/job/batchstop",
	"dodox/job/newqueue/list",
	"dodox/job/queue/prior",
	"dodox/job/queue/deferred",
	"dodox/job/queue/cancel",
	"dodox/job/stop",
	"dodox/job/queuev2/list",
	"dodox/job/queuev2/prior",
	"dodox/job/queuev2/deferred",
	"dodox/job/queuev2/batchprior",
	"dodox/job/queuev2/batchdeferred",
	"dodox/job/queuev2/stop",
	"dodox/job/queuev2/batchstop",
}

// 规则管理
var rules = []string{
	"dodox/alarmrule/listruledjob",
	"dodox/alarmrule/list",
	"users/list/project/all",
	"dodox/alarmrule/add",
	"dodox/alarmrule/edit",
	"dodox/alarmrule/delete",
	"dodox/alarmrule/removeruledjob"}

// 告警记录
var records = []string{
	"dodox/alarmrule/listalarmrecord",
	"users/list/project/all",
	"users/list/project/all/list",
	"dodox/alarmrule/listemail",
	"dodox/alarmrule/delalarmrecord"}

// 资源概览
var resourceCenter = []string{
	"metadata/devcenter/listlayer",
	"metadata/devcenter/listdir",
	"metadata/moneycenter/totalview",
	"metadata/moneycenter/recordincrement",
	"metadata/moneycenter/sensitivepie",
	"ants/dataprocess/sensitiverank/list",
	"metadata/moneycenter/sensitiveincrement",
	"metadata/moneycenter/storeincrement",
	"metadata/moneycenter/catalogtopten",
	"metadata/moneycenter/tableincretopten",
}

// 资源检索
var assetsCenter = []string{
	"metadata/moneycenter/listtablev2",
	"ants/dataprocess/sensitiverank/list",
	"metadata/devcenter/listdir",
	"metadata/devcenter/listtag",
	"metadata/moneycenter/tabledetail",
	"metadata/moneycenter/tablebasicinfo",
	"metadata/devcenter/syncallsource",
	"metadata/devcenter/syncall/getwsmessage",
	"metadata/devcenter/stopsyncsource",
	"metadata/devcenter/syncsourcetb",
	"metadata/moneycenter/tablefieldinfo",
	"metadata/moneycenter/tablefieldpartitioninfo",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/listtablev2",
	"metadata/moneycenter/saveblood",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/sqlsearch",
	"metadata/moneycenter/savesensitive",
	"metadata/collect/realmessagedata",
	"metadata/collect/listrealmessage",
	"metadata/collect/detailrealmessage",
}

// 服务管理
var management = []string{
	"asset/layer/dwsv2/gettable",
	"publish/service/batchrecallreview",
	"asset/layer/dwsv2/getsql",
	"dataauth/devlist",
	"devcenter/listtablenew",
	"metadata/devcenter/tbdetail",
	"asset/layer/dwsv2/showenum",
	"asset/layer/dwsv2/showresult",
	"metadata/devcenter/listlayer",
	"dataauth/gettables",
	"metadata/devcenter/listtag",
	"metadata/devcenter/liststandard",
	"metadata/devcenter/listdir",
	"metadata/devcenter/standarddetail",
	"publish/rules/search",
	"asset/layer/dwsv2/listtbtaskname",
	"asset/layer/dwsv2/fieldcomparison",
	"ants/inside/dodox/getnode",
	"ants/workflow/get/precrontab",
	"metadata/devcenter/listlayer",
	"asset/layer/dwsv2/delete",
	"dodox/job/isbeingoffline",
	"asset/layer/dwsv2/unsubmit",
	"dodox/job/isrunning",
	"dodox/job/canceloffline",
	"asset/layer/dwsv2/submit",
	"asset/layer/dwsv2/listtable",
	"publish/getpath",
	"asset/layer/dwsv2/tryrun",
	"asset/layer/dwsv2/gettbdata",
	"service/listallname",
	"service/movecatalog",
	"service/delete",
	"service/pushserver/unsubmit",
	"service/publish",
	"service/recall",
	"service/getOffLine",
	"service/pushserver/submit",
	"service/listall",
	"ants/folder/list",
	"ants/folder/delete",
	"ants/folder/create",
	"ants/folder/update",
	"service/save",
	"metadata/devcenter/listtag",
	"service/listallname",
	"modeling/category/list",
	"metadata/moneycenter/listtablev2",
	"modeling/moneyattach/attachlist",
	"metadata/moneycenter/sqlsearch",
	"metadata/moneycenter/tablefieldinfo",
	"service/detail",
	"service/test/query",
	"service/apiaccess/set",
	"service/getpath",
	"service/fieldcomparison",
	"ants/dataprocess/where/judge",
	"publish/dblist",
	"service/pushtbdata",
	"publish/newtbstruct",
	"publish/newtblist",
	"service/flow",
	"service/flowv2",
	"ants/workflow/list/submitjobv2",
	"service/downloadOffLine",
}

// 服务调用汇总
var useSummary = []string{
	"service/monitor/list",
	"service/monitor/apicount",
	"service/monitor/apigroup",
	"service/monitor/setapioffset",
	"publish/blackorwhite/list",
	"publish/blackorwhite/set",
}

// 服务调用明细
var useDetails = []string{
	"service/monitor/detail",
	"dodox/job/record/log",
}

// 资产编目
var catalog = []string{
	"modeling/inventory/list",
	"modeling/inventory/submit",
	"modeling/inventory/delete",
	"modeling/inventory/recall",
	"modeling/inventory/reqdelete",
	"modeling/inventory/detail",
	"modeling/inventory/addsave",
	"modeling/inventory/change",
	"modeling/inventory/update",
	"modeling/inventory/getassetcode",
	"modeling/inventory/newcategory",
	"modeling/catalog/searchused",
	"modeling/category/list",
	"modeling/moneyattach/flowwatch",
	"modeling/moneyattach/flowwatchv2",
	"metadata/devcenter/listdir",
	"metadata/moneycenter/listtablev2",
	"metadata/moneycenter/tablefieldinfo",
	"modeling/inventory/import",
	"modeling/resource/listlink",
	"modeling/resource/fileinfo",
	"modeling/version/add",
	"modeling/version/update",
	"modeling/version/delete",
	"modeling/version/publish",
	"modeling/version/unpublish",
	"modeling/version/detail",
	"modeling/version/list",
	"modeling/version/download",
	"modeling/version/attlist",
	"modeling/version/customquery",
}

// 资产挂载
var mount = []string{
	"modeling/catalog/searchused",
	"modeling/category/list",
	"metadata/moneycenter/listblood",
	"modeling/moneyattach/list",
	"metadata/moneycenter/tablefieldinfo",
	"metadata/moneycenter/listtablev2",
	"metadata/devcenter/listdir",
	"metadata/moneycenter/tablebasicinfo",
	"modeling/inventory/detail",
	"metadata/moneycenter/tablebasicinfo",
	"metadata/moneycenter/sqlsearch",
	"metadata/moneycenter/tablefieldinfo",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"service/detail",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/tablefieldpartitioninfo",
	"modeling/moneyattach/attachsave",
	"modeling/moneyattach/attachsave/batch",
	"modeling/moneyattach/attachinfo",
	"modeling/moneyattach/flowwatch",
}

// 资产发布
var release = []string{
	"modeling/inventory/list",
	"modeling/inventory/detail",
	"modeling/moneyattach/attachlist",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"modeling/moneyattach/flowwatch",
	"modeling/moneyattach/putsave",
	"modeling/moneyattach/putinfo",
	"modeling/catalog/searchused",
	"modeling/category/list",
	"users/list/project",
	"users/group/list",
	"roles/list",
	"service/detail",
	"metadata/moneycenter/tablebasicinfo",
	"metadata/moneycenter/tablefieldinfo",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/sqlsearch",
	"users/queryuser/byids",
}

// 数据溯源
var datatrace = []string{
	"metadata/moneycenter/trace/del",
	"metadata/moneycenter/trace/list",
	"dodox/filemanager/file/uploads",
	"metadata/moneycenter/trace/add",
}

// 资产类目管理
var category = []string{
	"modeling/category/list",
	"modeling/category/save",
}

// 资产目录模板
var template = []string{
	"modeling/catalog/list",
	"modeling/catalog/disable",
	"modeling/catalog/enable",
	"modeling/catalog/delete",
	"modeling/catalog/detail",
	"modeling/catalog/update",
	"modeling/catalog/addsave",
	"modeling/catalog/",
	"modeling/catalog/enum",
}

// 项目信息
var projectInfo = []string{"project/info"}

// 用户管理
var userManage = []string{
	"users/import/log",
	"users/group/remove_user",
	"users/group/list",
	"users/group/create",
	"users/group/delete",
	"users/group/add_user",
	"users/group/setdynamic",
	"users/group/detail",
	"users/group/sqlquery",
	"users/projectuser/delete",
	"users/projectuser/remove/project",
	"users/projectuser/delete",
	"users/projectuser/modify",
	"users/add",
	"users/addbulk",
	"users/needcode",
	"users/list/user_group",
	"users/get/fieldset",
	"users/set/fieldset",
	"metadata/moneycenter/sqlsearch",
	"metadata/devcenter/listdir",
	"metadata/moneycenter/listtablev2",
	"ants/dataprocess/list/metadata",
	"roles/list/foruser",
	"roles/list",
	"platform/getconfig",
	"dataauth/list",
	"verify_code",
	"users/modify/servicelife",
	"users/group/modify",
	"users/department/save",
	"users/department/list",
}

// 角色管理
var roleManage = []string{
	"roles/delete",
	"roles/list",
	"roles/modify",
	"roles/add",
	"roles/menu/list",
	"users/list/project",
}

// 数据权限管理
var dataAuthManage = []string{
	"dataauth/gettables",
	"dataauth/devlist",
	"dataauth/cataloglist",
	"dataauth/taglist",
	"dataauth/update",
	"dataauth/add",
	"dataauth/list",
	"dataauth/query",
	"dataauth/tbremove",
	"dataauth/rename",
	"dataauth/delete",
}

// 审核配置
var auditAllocation = []string{
	"audit/add",
	"audit/modify",
	"audit/delete",
	"audit/confget",
	"audit/confgetv2",
	"users/group/list",
	"users/get/fieldset",
	"modeling/catalog/searchused",
	"users/queryuser/byids",
}

// 审核记录
var auditRecord = []string{
	"modeling/catalog/searchused",
	"portal/calling/getdoc",
	"audit/gettransuser",
	"users/group/list",
	"service/detail",
	"modeling/inventory/detail",
	"portal/calling/auditdetail",
	"modeling/datareq/detail",
	"modeling/datacorr/detail",
	"audit/auditchart",
	"audit/buildcheck",
	"audit/listrecords",
	"metadata/moneycenter/tablebasicinfo",
	"modeling/inventory/detail",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/sqlsearch",
	"metadata/moneycenter/tablefieldinfo",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/tablefieldpartitioninfo",
	"service/detail",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"modeling/moneyattach/putinfo",
	"metadata/moneycenter/tablebasicinfo",
	"modeling/inventory/detail",
	"metadata/moneycenter/tablefieldinfo",
	"metadata/moneycenter/tablebasicinfo",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/sqlsearch",
	"portal/calling/getdocdir",
}

// 用户行为日志
var userBehaviorLogs = []string{
	"users/log",
	"users/list/project/all",
	"log/export",
}

// 日志设置
var logSetting = []string{
	"dodox/auth/record/confupdate",
	"dodox/auth/record/confget",
	"log/setting/get",
	"log/setting/update",
}

var carousalManage = []string{
	"portal/carousel/get",
	"portal/carousel/save",
	"portal/uploadpic",
	"portal/cut",
}

// 资讯管理
var portalIndexInfo = []string{
	"portal/info/del",
	"portal/info/update",
	"portal/info/publish",
	"portal/info/add",
	"portal/info/list",
	"portal/infoclass/del",
	"portal/infoclass/update",
	"portal/infoclass/move",
	"portal/infoclass/add",
	"portal/info/upload",
	"portal/infoclass/list",
	"portal/info/addopen",
}

// 我的数据配置
var myDataSetting = []string{
	"portal/mydata/get",
	"portal/datebase/list",
	"portal/mydata/save",
}

// 帮助中心管理
var helpCenterManage = []string{
	"portal/help/upload",
	"portal/help/release",
	"portal/help/del",
	"portal/help/rename",
	"portal/help/list",
}

// 首页配置
var indexManage = []string{
	"portal/carousel/save",
	"modeling/catalog/searchused2",
	"portal/carousel/get",
	"portal/uploadpic",
	"portal/cut",
}

// 主题管理
var themeManage = []string{
	"portal/footer/save",
	"portal/footer/get",
	"asset/oem/upload",
	"portal/info/upload"}

// 协议管理
var protocolManage = []string{
	"portal/protocol/list",
	"portal/protocol/save",
	"portal/info/upload"}

// 页面管理
var pageManage = []string{
	"menu/recovery",
	"menu/clear",
	"menu/create",
	"menu/update",
	"menu/release",
	"menu/delete",
	"menu/rename",
	"menu/move",
}

// 基本信息
var devProfile = []string{
	"users/needcode",
	"users/modify/basicinfo",
	"users/avatar",
	"verify_code",
	"users/update/auth",
	"platform/getconfig",
}

// 我的消息
var devMessageRecord = []string{
	"users/notify/list",
	"users/notify/markread",
	"users/notify/delet",
	"audit/formischeck",
}

// 我的待办
var devTodoList = []string{
	"modeling/catalog/searchused",
	"portal/calling/getdoc",
	"audit/gettransuser",
	"users/group/list",
	"service/detail",
	"modeling/inventory/detail",
	"portal/calling/auditdetail",
	"modeling/datareq/detail",
	"modeling/datacorr/detail",
	"audit/runcheck",
	"audit/auditchart",
	"audit/buildcheck",
	"audit/listrecords",
	"metadata/moneycenter/tablebasicinfo",
	"modeling/inventory/detail",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/sqlsearch",
	"metadata/moneycenter/tablefieldinfo",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"service/detail",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/tablefieldpartitioninfo",
	"modeling/moneyattach/serverlist",
	"modeling/moneyattach/filelist",
	"modeling/moneyattach/putinfo",
	"metadata/moneycenter/tablebasicinfo",
	"modeling/inventory/detail",
	"metadata/moneycenter/tablefieldinfo",
	"metadata/moneycenter/tablebasicinfo",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/sqlsearch",
	"portal/calling/getdocdir",
}

// 数据填报(开发平台)
var dataFilling = []string{
	"ants/access/datareport/sync",
	"ants/access/datareport/save",
	"ants/access/datareport/search",
	"ants/access/datareport/delete",
	"ants/access/datareport/detail",
	"ants/access/datareport/publish",
	"ants/access/datareport/template/download",
	"ants/access/datareport/putdata",
	"ants/access/datareport/putdata/list",
	"ants/access/datareport/putdata/query",
	"ants/access/datareport/putdata/download",
	"ants/access/datareport/authchange",
	"ants/access/datareport/putdata/tempcheck",
	"ants/access/datareport/putdata/check",
	"ants/access/datareport/putdata/checkdownload",
	"metadata/devcenter/listengine",
	"users/group/list",
	"roles/list",
	"users/get/department",
}

// 数据填报(门户)
var portalDataFilling = []string{
	"ants/access/datareport/sync",
	"ants/access/datareport/save",
	"ants/access/datareport/search",
	"ants/access/datareport/delete",
	"ants/access/datareport/detail",
	"ants/access/datareport/publish",
	"ants/access/datareport/template/download",
	"ants/access/datareport/putdata",
	"ants/access/datareport/putdata/list",
	"ants/access/datareport/putdata/query",
	"ants/access/datareport/putdata/download",
	"ants/access/datareport/authchange",
	"ants/access/datareport/putdata/tempcheck",
	"ants/access/datareport/putdata/check",
	"ants/access/datareport/putdata/checkdownload",
}

// 数据指标
var dataMetrics = []string{
	"ants/metrics/save",
	"ants/metrics/delete",
	"ants/metrics/search",
	"ants/metrics/detail",
	"ants/metrics/move",
	"ants/metrics/getcode",
	"ants/metrics/submit",
	"ants/folder/list",
	"metadata/devcenter/listengine",
	"metadata/devcenter/listlayerinfo",
}

// 资源注册
var registry = []string{
	"modeling/resource/status",
	"modeling/resource/register",
	"modeling/resource/update",
	"modeling/resource/list",
	"modeling/resource/listlink",

	"modeling/resource/checklog",
	"modeling/resource/delete",
	"modeling/resource/delete",
	"modeling/resource/move",
	"modeling/resource/download",

	"modeling/resource/list/project",
	"modeling/resource/list/metricsfolder",
	"modeling/resource/list/metrics",
	"modeling/resource/metrics/info",
	"modeling/resource/apicreate",
	"modeling/resource/apisave",
	"modeling/resource/apilist",
	"modeling/resource/apidelete",
	"modeling/resource/apidetail",
	"modeling/resource/apiuserauth",
	"modeling/resource/apitest",
	"modeling/resource/apitest2",
	"ants/folder/list",
}

// 指标中心
var dataIndex = []string{
	"portal/zbdownload",
	"modeling/category/list",
	"portal/datasource/list",
	"portal/datasource/conditions",
	"portal/datasource/detail",
	"modeling/resource/metrics/info",
	"portal/calling/customquery",
	"metadata/moneycenter/sqlsearch",
}

// 质量规则
var qualityRule = []string{
	"ants/dataquality/rule/list",
	"ants/dataquality/rule/save",
	"ants/dataquality/rule/detail",
	"ants/dataquality/rule/delete",
	"ants/dataquality/rule/relatedtasks",
	"ants/dataquality/rule/getdimension",
	"ants/dataquality/rule/checkname",
	"ants/folder/list",
	"asset/codecollect/search",
	"ants/dataquality/report/ruleweight/save",
	"ants/dataquality/report/ruleweight/query",
}

// 质量指标
var indicator = []string{
	"ants/folder/list",
	"metadata/devcenter/listengine",
	"metadata/devcenter/listlayerinfo",
	"ants/metrics/quality/save",
	"ants/metrics/quality/delete",
	"ants/metrics/quality/search",
	"ants/metrics/quality/detail",
	"ants/metrics/quality/move",
	"ants/metrics/quality/getcode",
	"ants/metrics/quality/submit",
}

// 报告模板
var reportTemplate = []string{
	"ants/dataquality/reportconf/save",
	"ants/dataquality/reportconf/get",
	"ants/dataquality/report/upload",
	"ants/dataquality/report/firstdown",
	"users/queryuser/byids",
	"users/get/fieldset",
	"users/group/list",
	"users/list/project",
	"metadata/devcenter/listtag",
	"ants/dataquality/report/template/save",
	"ants/dataquality/report/template/add",
	"ants/dataquality/report/template/delete",
	"ants/dataquality/report/template/query",
	"ants/dataquality/report/template/list",
	"ants/dataquality/report/template/getdoc",
	"ants/dataquality/report/template/savedoc",
	"ants/dataquality/report/ruleweight/save",
	"ants/dataquality/report/ruleweight/query",
}

// 质量报告(父菜单--质量报告管理)
var reportList = []string{
	"ants/dataquality/report/query",
	"ants/dataquality/report/download",
	"ants/dataquality/report/del",
	"ants/dataquality/report/create",
	"ants/dataquality/report/createv2",
	"ants/dataquality/report/createv3",
	"ants/dataquality/report/createv4",
	"ants/dataquality/report/stop",
	"metadata/devcenter/listtag",
	"log/reportsetting/get",
	"log/reportsetting/update",
}

// 质量报告(父菜单--开发者个人中心)
var devQaport = []string{
	"ants/dataquality/report/query",
	"ants/dataquality/report/download",
	"ants/dataquality/report/del",
	"log/reportsetting/get",
	"log/reportsetting/update",
}

// 质量报告(父菜单--门户个人中心)
var portalQaReport = []string{
	"ants/dataquality/report/query",
	"ants/dataquality/report/download",
	"ants/dataquality/report/del",
}

// 质量问题数据
var problemdata = []string{
	"asset/problem/listrepeateddata",
	"asset/problem/searchrepeateddata",
	"asset/problem/searchprotable",
	"asset/problem/delproblemtable",
	"asset/problem/listquestiondata",
	"asset/problem/searchquestiondata",
	"asset/problem/status",
	"asset/problem/refresh",
	"metadata/devcenter/qualitytablelist",
	"metadata/devcenter/qualitylistdir",
	"metadata/devcenter/qualitylisttag",
	"metadata/devcenter/qualitydelete",
	"metadata/devcenter/qualityproblemdetail",
	"metadata/devcenter/qualitytabledetail",
	"metadata/devcenter/qualitysync",
	"metadata/devcenter/getwsqualitysync",
	"metadata/devcenter/qualityonetablestatic",
	"metadata/devcenter/qualityexport",
	"metadata/devcenter/batchqualityexport",
	"metadata/devcenter/qualityfieldsearch",
	"metadata/devcenter/qualitydatadelete",
	"metadata/devcenter/qualitytablemonthsync",
	"metadata/devcenter/getqualitytablemonthsync",
	"metadata/devcenter/getduplicatenumber",
	"metadata/devcenter/modelexternalimport",
	"metadata/devcenter/externalupload",
	"metadata/devcenter/modelexport",
	"metadata/devcenter/externaluploadv2",
	"metadata/devcenter/modelfileimport",
}

// 批数据治理
var basic = []string{
	"metadata/devcenter/listtagdir",
	"platform/engines/curengine",
	"metadata/devcenter/listlayer",
	"ants/folder/list",
	"ants/workflow/searchv2",
	"ants/dataprocess/list/metadata",
	"ants/folder/create",
	"ants/folder/update",
	"ants/folder/delete",
	"ants/workflow/newv2",
	"ants/workflow/contentv2",
	"ants/workflow/taskdetail",
	"ants/inside/dodox/getnode",
	"ants/inside/localip",
	"ants/workflow/searchv2",
	"ants/workflow/list/submitjobv2",
	"ants/workflow/wfexport",
	"ants/workflow/import",
	"ants/workflow/submit",
	"ants/workflow/schedulev2",
	"dodox/job/isrunning",
	"dodox/job/isbeingoffline",
	"dodox/job/del",
	"ants/workflow/movev2",
	"ants/workflow/deletev2",
	"ants/dataprocess/new/modulev2",
	"ants/workflow/list/marjob",
	"ants/workflow/savev2",
	"ants/develop/new/devv2",
	"ants/dataprocess/module/delete",
	"ants/develop/deletev2",
	"ants/workflow/update",
	"ants/dataprocess/cooking/listmodule",
	"ants/dataprocess/query/module",
	"ants/dataprocess/access/fieldget",
	"metadata/devcenter/listtablenew",
	"metadata/moneycenter/sqlsearch",
	"ants/dataprocess/list/tbinfo",
	"ants/dataprocess/access/save",
	"ants/dataprocess/submit/judge",
	"ants/dataprocess/watch/info",
	"ants/dataprocess/watch/infov2",
	"ants/dataprocess/watch/tableget",
	"ants/dataprocess/watch/save",
	"ants/dataprocess/watch/savev2",
	"ants/dataprocess/checkdup",
	"ants/dataquality/rule/list",
	"ants/dataprocess/standard/info",
	"asset/dataprocess/search",
	"asset/codecollect/search",
	"ants/dataprocess/standard/tableget",
	"metadata/devcenter/listdir",
	"metadata/devcenter/liststandard",
	"ants/dataprocess/standard/fieldgroup",
	"asset/codecollect/infos",
	"ants/dataprocess/standard/savepre",
	"ants/dataprocess/cooking/fieldget",
	"ants/dataprocess/cooking/outputsave",
	"ants/dataprocess/sensitive/get",
	"publish/rules/search",
	"ants/dataprocess/sensitive/save",
	"metadata/devcenter/listlayerinfo",
	"metadata/devcenter/listengine",
	"metadata/devcenter/listtag",
	"metadata/devcenter/listtablenew",
	"ants/access/fieldtrans",
	"metadata/devcenter/tbdetail",
	"metadata/devcenter/addtable",
	"metadata/devcenter/addtablebulk",

	"ants/dataprocess/output/savev2",
	"ants/dataprocess/sensitivecheck/module/list",
	"ants/dataprocess/sensitivecheck/module/get",
	"ants/dataprocess/sensitivecheck/module/save",
	"ants/dataprocess/cooking/listmodule",
	"ants/develop/content",
	"ants/develop/fetch",
	"ants/develop/savev2",
	"ants/develop/run",
	"ants/develop/result",
	"ants/develop/star",
	"ants/workflow/run",
	"dodox/job/record/get",
	"dodox/job/record/log",
	"ants/workflow/star",
	"dodox/filemanager/file/upload",
	"ants/workflow/stop",
	"ants/develop/scriptsexportnew",
	"asset/dataprocess/textlist",
	"asset/data/classify/list",
	"ants/dataprocess/module/clean",
	"ants/dataprocess/module/changestatus",
	"asset/data/detail",
	"ants/dataprocess/update/module",
	"ants/dataprocess/where/judge",
	"ants/dataprocess/module/showresult",
	"ants/workflow/batchsubmit",
	"ants/develop/syncdata",
}

// 实时同步
var realtime = []string{}

// 流数据治理
var flow = []string{
	"metadata/devcenter/listtagdir",
	"platform/engines/curengine",
	"metadata/devcenter/listlayer",
	"ants/folder/list",
	"ants/workflow/searchv2",
	"ants/dataprocess/list/metadata",
	"ants/folder/create",
	"ants/folder/update",
	"ants/folder/delete",
	"ants/workflow/newv2",
	"ants/workflow/contentv2",
	"ants/workflow/taskdetail",
	"ants/inside/dodox/getnode",
	"ants/inside/localip",
	"ants/workflow/searchv2",
	"ants/workflow/list/submitjobv2",
	"ants/workflow/wfexport",
	"ants/workflow/import",
	"ants/workflow/submit",
	"ants/workflow/schedulev2",
	"dodox/job/isrunning",
	"dodox/job/isbeingoffline",
	"dodox/job/del",
	"ants/workflow/movev2",
	"ants/workflow/deletev2",
	"ants/dataprocess/new/modulev2",
	"ants/workflow/list/marjob",
	"ants/workflow/savev2",
	"ants/develop/new/devv2",
	"ants/dataprocess/module/delete",
	"ants/develop/deletev2",
	"ants/workflow/update",
	"ants/dataprocess/cooking/listmodule",
	"ants/dataprocess/query/module",
	"ants/dataprocess/access/fieldget",
	"metadata/devcenter/listtablenew",
	"metadata/moneycenter/sqlsearch",
	"ants/dataprocess/list/tbinfo",
	"ants/dataprocess/access/save",
	"ants/dataprocess/submit/judge",
	"ants/dataprocess/watch/info",
	"ants/dataprocess/watch/infov2",
	"ants/dataprocess/watch/tableget",
	"ants/dataprocess/watch/save",
	"ants/dataprocess/watch/savev2",
	"ants/dataprocess/checkdup",
	"ants/dataquality/rule/list",
	"ants/dataprocess/standard/info",
	"asset/dataprocess/search",
	"asset/codecollect/search",
	"ants/dataprocess/standard/tableget",
	"metadata/devcenter/listdir",
	"metadata/devcenter/liststandard",
	"ants/dataprocess/standard/fieldgroup",
	"asset/codecollect/infos",
	"ants/dataprocess/standard/savepre",
	"ants/dataprocess/cooking/fieldget",
	"ants/dataprocess/cooking/outputsave",
	"ants/dataprocess/sensitive/get",
	"publish/rules/search",
	"ants/dataprocess/sensitive/save",
	"metadata/devcenter/listlayerinfo",
	"metadata/devcenter/listengine",
	"metadata/devcenter/listtag",
	"metadata/devcenter/listtablenew",
	"ants/access/fieldtrans",
	"metadata/devcenter/tbdetail",
	"metadata/devcenter/addtable",
	"metadata/devcenter/addtablebulk",

	"ants/dataprocess/output/savev2",
	"ants/dataprocess/sensitivecheck/module/list",
	"ants/dataprocess/sensitivecheck/module/get",
	"ants/dataprocess/sensitivecheck/module/save",
	"ants/dataprocess/cooking/listmodule",
	"ants/develop/content",
	"ants/develop/fetch",
	"ants/develop/savev2",
	"ants/develop/run",
	"ants/develop/result",
	"ants/develop/star",
	"ants/workflow/run",
	"dodox/job/record/get",
	"dodox/job/record/log",
	"ants/workflow/star",
	"dodox/filemanager/file/upload",
	"ants/workflow/stop",
	"ants/develop/scriptsexportnew",
	"asset/dataprocess/textlist",
	"asset/data/classify/list",
	"ants/dataprocess/module/clean",
	"ants/dataprocess/module/changestatus",
	"asset/data/detail",
	"ants/dataprocess/update/module",
	"ants/dataprocess/where/judge",
	"ants/dataprocess/module/showresult",
	"ants/workflow/batchsubmit",
	"ants/develop/syncdata",
}

// 库表数据
var database = []string{
	"metadata/moneycenter/listtablev2",
	"ants/dataprocess/sensitiverank/list",
	"metadata/devcenter/listdir",
	"metadata/devcenter/listtag",
	"metadata/moneycenter/tabledetail",
	"metadata/moneycenter/tablebasicinfo",
	"metadata/devcenter/syncallsource",
	"metadata/devcenter/syncall/getwsmessage",
	"metadata/devcenter/stopsyncsource",
	"metadata/devcenter/syncsourcetb",
	"metadata/moneycenter/tablefieldinfo",
	"metadata/moneycenter/tablefieldpartitioninfo",
	"metadata/moneycenter/listblood",
	"metadata/moneycenter/listtablev2",
	"metadata/moneycenter/saveblood",
	"metadata/moneycenter/singleincrement",
	"metadata/moneycenter/sqlsearch",
	"metadata/moneycenter/savesensitive",
}

// 消息流数据
var flowdata = []string{
	"ants/folder/list",
	"metadata/devcenter/listdir",
	"metadata/collect/listrealmessage",
	"metadata/devcenter/listtag",
	"metadata/devcenter/syncsourcetb",
	"metadata/collect/detailrealmessage",
}

// 码表管理
var codesManage = []string{
	"metadata/devcenter/listdir",
	"metadata/devcenter/liststandard",
	"metadata/devcenter/standarddetail",
	"metadata/devcenter/checkstandard",
	"metadata/devcenter/addstandard",
	"metadata/devcenter/liststdorder",
	"metadata/devcenter/listtag",
	"metadata/devcenter/inputcodetable",
	"metadata/devcenter/listleveldir",
	"metadata/devcenter/addstandarddir",
	"metadata/devcenter/delstandarddir",
	"metadata/devcenter/standard/history",
	"metadata/devcenter/standard/template",
	"metadata/devcenter/standard/publish",
	"metadata/devcenter/standard/recall",
	"metadata/devcenter/standard/unpublish",
	"metadata/devcenter/standard/reqrevise",
	"metadata/devcenter/standard/import",
	"metadata/devcenter/standard/bulkadd",
	"metadata/devcenter/standard/export",
	"metadata/devcenter/asociatetasks",
	"metadata/devcenter/ct/add",
	"metadata/devcenter/ct/update",
	"metadata/devcenter/ct/list",
	"metadata/devcenter/ct/detail",
	"metadata/devcenter/ct/del",
	"metadata/devcenter/ct/history",
	"metadata/devcenter/ct/template",
	"metadata/devcenter/ct/import",
	"metadata/devcenter/ct/bulkadd",
	"metadata/devcenter/ct/export",
	"metadata/devcenter/ct/publish",
	"metadata/devcenter/ct/recall",
	"metadata/devcenter/ct/unpublish",
	"metadata/devcenter/ct/reqrevise",
	"metadata/devcenter/getflow",
}

// 标准管理
var standardsManage = []string{
	"metadata/devcenter/listdir",
	"metadata/devcenter/liststandard",
	"metadata/devcenter/standarddetail",
	"metadata/devcenter/checkstandard",
	"metadata/devcenter/addstandard",
	"metadata/devcenter/liststdorder",
	"metadata/devcenter/listtag",
	"metadata/devcenter/inputcodetable",
	"metadata/devcenter/listleveldir",
	"metadata/devcenter/addstandarddir",
	"metadata/devcenter/delstandarddir",
	"metadata/devcenter/delstandarddir",
	"metadata/devcenter/standard/history",
	"metadata/devcenter/standard/template",
	"metadata/devcenter/standard/publish",
	"metadata/devcenter/standard/recall",
	"metadata/devcenter/standard/unpublish",
	"metadata/devcenter/standard/reqrevise",
	"metadata/devcenter/standard/import",
	"metadata/devcenter/standard/bulkadd",
	"metadata/devcenter/standard/export",
	"metadata/devcenter/asociatetasks",
	"metadata/devcenter/ct/add",
	"metadata/devcenter/ct/update",
	"metadata/devcenter/ct/list",
	"metadata/devcenter/ct/detail",
	"metadata/devcenter/ct/del",
	"metadata/devcenter/ct/history",
	"metadata/devcenter/ct/template",
	"metadata/devcenter/ct/import",
	"metadata/devcenter/ct/bulkadd",
	"metadata/devcenter/ct/export",
	"metadata/devcenter/ct/publish",
	"metadata/devcenter/ct/recall",
	"metadata/devcenter/ct/unpublish",
	"metadata/devcenter/ct/reqrevise",
	"metadata/devcenter/getflow",
}

var AllAPI = map[string][]string{
	"portalIndex":             portalIndex,
	"runManagement":           runManagement,
	"public":                  public,
	"sharedData":              sharedData,
	"openCatalog":             openCatalog,
	"requirements":            requirements,
	"corrections":             corrections,
	"applications":            applications,
	"applicationManage":       applicationManage,
	"portalPersonal":          portalPersonal,
	"portalMessageRecord":     portalMessageRecord,
	"portalCollection":        portalCollection,
	"useApplication":          useApplication,
	"myNeeds":                 myNeeds,
	"errorCorrection":         errorCorrection,
	"portalTodoList":          portalTodoList,
	"datasource":              datasource,
	"layerManage":             layerManage,
	"modelManage":             modelManage,
	"realTimeData":            realTimeData,
	"customFunction":          customFunction,
	"tags":                    tags,
	"desensitization":         desensitization,
	"level":                   level,
	"identification":          identification,
	"offline":                 offline,
	"customProcess":           customProcess,
	"optdevelopment":          optdevelopment,
	"modelMgm":                modelMgm,
	"loadingInAndOut":         loadingInAndOut,
	"realtimeComputation":     realtimeComputation,
	"job/dashboard":           dashboard,
	"actualTime":              actualTime,
	"task":                    task,
	"circle":                  circle,
	"job/queue":               queue,
	"rules":                   rules,
	"records":                 records,
	"resourceCenter/overview": resourceCenter,
	"assetsCenter":            assetsCenter,
	"problemdata":             problemdata,
	"management":              management,
	"useSummary":              useSummary,
	"useDetails":              useDetails,
	"catalog":                 catalog,
	"mount":                   mount,
	"release":                 release,
	"datatrace":               datatrace,
	"category":                category,
	"template":                template,
	"projectInfo":             projectInfo,
	"userManage":              userManage,
	"roleManage":              roleManage,
	"dataAuthManage":          dataAuthManage,
	"auditAllocation":         auditAllocation,
	"auditRecord":             auditRecord,
	"userBehaviorLogs":        userBehaviorLogs,
	"logSetting":              logSetting,
	"carousalManage":          carousalManage,
	"portalIndexInfo":         portalIndexInfo,
	"myDataSetting":           myDataSetting,
	"pageManage":              pageManage,
	"devProfile":              devProfile,
	"devMessageRecord":        devMessageRecord,
	"devTodoList":             devTodoList,
	"indexManage":             indexManage,
	"themeManage":             themeManage,
	"protocolManage":          protocolManage,
	"helpCenterManage":        helpCenterManage,
	"helpCenter":              helpCenter,
	"dataFilling":             dataFilling,
	"portalDataFilling":       portalDataFilling,
	"dataMetrics":             dataMetrics,
	"registry":                registry,
	"dataIndex":               dataIndex,
	"qualityRule":             qualityRule,
	"indicator":               indicator,
	"reportTemplate":          reportTemplate,
	"reportList":              reportList,
	"devQaport":               devQaport,
	"portalQaReport":          portalQaReport,
	"codesManage":             codesManage,
	"standardsManage":         standardsManage,
}
