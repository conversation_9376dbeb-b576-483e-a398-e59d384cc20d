/*
Copyright (c) Datatom Software, Inc.(2020)
Author: 徐文见（<EMAIL>）
Creating Time: 2020-05-21

> 连接数据库
*/

package collect

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"regexp"
	"strconv"
	"strings"
	"time"

	my_xls "github.com/extrame/xls"
	"github.com/go-ini/ini"
	"github.com/tealeg/xlsx"
	my_exce "github.com/xuri/excelize/v2"

	"github.com/lextoumbourou/goodhosts"

	"datatom/gin.v1"

	"datatom.com/metadata/httpclient"
	"datatom.com/metadata/httpdo/esutil"

	_ "github.com/denisenkom/go-mssqldb"
	"github.com/go-xorm/xorm"

	//_ "github.com/ibmdb/go_ibm_db"
	"github.com/tidwall/gjson"

	"datatom.com/metadata/common"
	"datatom.com/metadata/logger"
	"datatom.com/metadata/source"
	errcode "datatom.com/tools/common/code"
	comtools "datatom.com/tools/common/tools"
	tools "datatom.com/tools/httpdo"
)

func ConnMysql(dbinfo source.AccDbInfo) (*xorm.Engine, error) {
	connstr := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&allowNativePasswords=true", dbinfo.NormalConn.DBUser, dbinfo.NormalConn.DBPassword, dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.Database)
	engine, err := xorm.NewEngine(dbinfo.DBType, connstr)
	if err != nil {
		logger.Error.Println(err)
		return engine, err
	}

	return engine, nil
}

func ConnStork(dbinfo source.AccDbInfo) (*xorm.Engine, error) {
	info := fmt.Sprintf("user=%s password=%s dbname=%s host=%s port=%s sslmode=disable",
		dbinfo.NormalConn.DBUser, dbinfo.NormalConn.DBPassword, dbinfo.NormalConn.Database, dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort)
	engine, err := xorm.NewEngine("postgres", info)
	if err != nil {
		logger.Error.Println(err)
		return engine, err
	}
	engine.ShowSQL()
	return engine, nil
}

func ConnDB2(dbinfo source.AccDbInfo) (*sql.DB, error) {

	con := fmt.Sprintf("HOSTNAME=%s;DATABASE=%s;PORT=%s;UID=%s;PWD=%s",
		dbinfo.NormalConn.DBIP, dbinfo.NormalConn.Database, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.DBUser, dbinfo.NormalConn.DBPassword)
	db, err := sql.Open("go_ibm_db", con)
	if err != nil {
		logger.Error.Println(err)
		return db, err
	}
	return db, nil
}

func ConnSqlsever(dbinfo source.AccDbInfo) (*sql.DB, error) {
	connString := fmt.Sprintf("server=%s;port=%s;database=%s;user id=%s;password=%s;encrypt=disable",
		dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.Database, dbinfo.NormalConn.DBUser, dbinfo.NormalConn.DBPassword)
	fmt.Println("------connString--------", connString)
	db, err := sql.Open("mssql", connString)
	if err != nil {
		logger.Error.Println(err)
		return db, err
	}
	return db, err
}

/*
func ConnPG(dbinfo source.AccDbInfo) (*xorm.Engine, error){
	engine, err := xorm.NewEngine(dbinfo.DBType, "")
	if err != nil {
		logger.Error.Println(err)
		return engine, err
	}
	return engine, nil
}
*/
//检测抽取源连接情况
func ConnMysqlCheck(dbinfo source.AccDbInfo) error {

	engine, err := ConnMysql(dbinfo)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	//连接测试
	if err := engine.Ping(); err != nil {
		fmt.Println(err.Error())
		return err
	}
	defer engine.Close()
	return nil
}

func ConnStorkCheck(dbinfo source.AccDbInfo) error {
	engine, err := ConnStork(dbinfo)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	//连接测试
	if err := engine.Ping(); err != nil {
		fmt.Println(err.Error())
		return err
	}
	defer engine.Close()
	return nil
}

func ConnSqlServerCheck(dbinfo source.AccDbInfo) error {
	db, err := ConnSqlsever(dbinfo)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	err = db.Ping()
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	defer db.Close()
	return nil
}

func ConnDB2Check(dbinfo source.AccDbInfo) error {
	db, err := ConnDB2(dbinfo)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	defer db.Close()
	err = db.Ping()
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	//db.Close()
	return nil
}

func ConnCSVCheck(dbinfo source.AccDbInfo) error {
	path := dbinfo.CsvConn.CsvPaths
	commond := fmt.Sprintf("if [ -d %s ]; then echo yes; else echo no; fi", path)
	//fmt.Println(commond)
	port, _ := strconv.Atoi(dbinfo.CsvConn.Port)
	result, err := tools.RemoteLogin(dbinfo.CsvConn.LoginUser, dbinfo.CsvConn.LoginPassword, dbinfo.CsvConn.IP, commond, port)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	//目录存在？
	if ok := strings.Contains(result, "yes"); ok {
		//检测编码格式
		//DS5.5.4  需求： 2.1 CSV文件格式支持自动检测机制 ，在检测数据源时不需要检测文件编码格式
		_, err = GetCsvPathFiles(dbinfo.CsvConn)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	} else {
		logger.Error.Println(errors.New(result))
		return errors.New(result)
	}
	return nil
}

// 获取CSV数据源目录下所有.csv文件名称
func GetCsvPathFiles(conn source.CSVConn) ([]string, error) {
	var farr, filenames []string

	exec := fmt.Sprintf("cd %s && ls", conn.CsvPaths)
	//sshports, _ := tools.HiveSSHInfo()
	portint, err := strconv.Atoi(conn.Port)
	if err != nil {
		logger.Error.Println(err)
		return filenames, err
	}
	content, err := tools.RemoteLogin(conn.LoginUser, conn.LoginPassword, conn.IP, exec, portint)
	if err != nil {
		logger.Error.Println(err)
		return filenames, err
	}
	farr = strings.Split(content, "\n")

	for _, v := range farr {
		fileSuffix := path.Ext(v)
		if fileSuffix == ".csv" {
			filenames = append(filenames, v)
		}
	}

	return filenames, nil
}

func CheckCode(conn source.CSVConn, onefilename string) ([]string, map[string]string, error) {
	var farr, filenames, endFilenames []string
	var nameAndEncodeMap = make(map[string]string)
	//确保有/
	dir := conn.CsvPaths
	last := conn.CsvPaths[len(conn.CsvPaths)-1:]
	if last != "/" {
		dir = conn.CsvPaths + "/"
	}

	if onefilename == "" {
		exec := fmt.Sprintf("cd %s && ls", conn.CsvPaths)
		portint, err := strconv.Atoi(conn.Port)
		if err != nil {
			logger.Error.Println(err)
			return endFilenames, nameAndEncodeMap, err
		}
		content, err := tools.RemoteLogin(conn.LoginUser, conn.LoginPassword, conn.IP, exec, portint)
		if err != nil {
			logger.Error.Println(err)
			return endFilenames, nameAndEncodeMap, err
		}
		farr = strings.Split(content, "\n")
		for _, v := range farr {
			fileSuffix := path.Ext(v)
			if fileSuffix == ".csv" {
				filenames = append(filenames, v)
			}
		}
	} else {
		filenames = append(filenames, onefilename)
	}

	for _, v := range filenames {
		filename := dir + v
		//远程或本地
		charset, err := tools.CheckCsvEncode(filename, conn.LoginUser, conn.LoginPassword, conn.IP, conn.Port, "")
		if err != nil {
			logger.Error.Println(err)
			return endFilenames, nameAndEncodeMap, err
		}
		nameAndEncodeMap[v] = charset
		endFilenames = append(endFilenames, v)

		//if charset == "ISO-8859" {
		//	nameAndEncodeMap[v] = "ANSI"
		//	endFilenames = append(endFilenames, v)
		//	continue
		//}
		//if charset == "ASCII" || charset == "UTF-8" {
		//	nameAndEncodeMap[v] = charset
		//	endFilenames = append(endFilenames, v)
		//	continue
		//}
		//if strings.Contains(charset, "data") {
		//	nameAndEncodeMap[v] = "ASCII"
		//	endFilenames = append(endFilenames, v)
		//	continue
		//}
		//if charset != conn.Encoding {
		//	str := filename + "该文件编码格式与数据源编码格式不一致"
		//	logger.Error.Println(errors.New(str))
		//	return filenames, errors.New(str)
		//}
		//if charset == "不支持该编码格式" {
		//	str := filename + "不支持该编码格式"
		//	logger.Error.Println(errors.New(str))
		//	return filenames, errors.New(str)
		//}
	}
	return endFilenames, nameAndEncodeMap, nil
}

// 通过远程方式执行stat命令获取csv文件的修改时间
func GetcsvModifyTime(conn source.CSVConn, filenames []string) (map[string]string, map[string]uint64, error) {
	var csvModifyMap = make(map[string]string)
	var csvSizeMap = make(map[string]uint64)
	//确保有/
	dir := conn.CsvPaths
	last := conn.CsvPaths[len(conn.CsvPaths)-1:]
	if last != "/" {
		dir = conn.CsvPaths + "/"
	}
	portint, err := strconv.Atoi(conn.Port)
	if err != nil {
		logger.Error.Println(err)
		return csvModifyMap, csvSizeMap, err
	}

	for _, v := range filenames {
		filename := dir + v
		exec := fmt.Sprintf("stat '%s'", filename)
		content, err := tools.RemoteLogin(conn.LoginUser, conn.LoginPassword, conn.IP, exec, portint)
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		farr := strings.Split(content, "\n")
		for _, i := range farr {
			i = strings.Trim(i, " ")
			if strings.HasPrefix(i, "Size:") {
				splist := strings.Split(i, "Blocks")
				if len(splist) == 2 {
					sizetemp := strings.ReplaceAll(splist[0], "Size:", "")
					sizetemp = strings.ReplaceAll(sizetemp, " ", "")
					sizetemp = strings.ReplaceAll(sizetemp, "\t", "")
					uint64temp, err := strconv.ParseUint(sizetemp, 10, 64)
					if err != nil {
						logger.Error.Println("GetcsvModifyTime stat file ", v, " get size err:", err)
					}
					csvSizeMap[v] = uint64temp
				}
			}
			if strings.HasPrefix(i, "Modify:") {
				i = strings.ReplaceAll(i, "Modify: ", "")
				if len(i) >= 19 {
					csvModifyMap[v] = i[0:19]
				} else {
					csvModifyMap[v] = i
				}
				break
			}
		}
	}
	return csvModifyMap, csvSizeMap, nil
}

/*
func ConnCSVCheck(dbinfo source.AccDbInfo) {
	path := dbinfo.CsvConn.CsvPaths
	commond := fmt.Sprintf("if [ -d %s ]; then echo yes; else echo no; fi", path)

	commond, err := RemoteLogin(sp.LoginUser, sp.LoginPassword, sp.Ip, commond, sshports["scp"])
	if err != nil {
		fmt.Println(err.Error())
	}
	if ok := strings.Contains(commond, "yes"); ok{

	}
}

*/

//func ConnOracleCheck(dbinfo source.AccDbInfo) error {
//	var jdbcUrl string
//	if dbinfo.NormalConn.OracleServer != "" {
//		jdbcUrl = fmt.Sprintf("**************************", dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.OracleServer)
//	}
//	if dbinfo.NormalConn.Oraclesid != "" {
//		jdbcUrl = fmt.Sprintf("**************************", dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.Oraclesid)
//	}
//	strbody := fmt.Sprintf("jdbcUrl=%s&username=%s&password=%s", jdbcUrl, dbinfo.NormalConn.DBUser, dbinfo.NormalConn.DBPassword)
//	//获取本地ip
//	dsip, _ := tools.SelfIp()
//	logger.Info.Println("strbody", strbody)
//	_, err := tools.AccessPools(strbody, dsip, common.POOLPORT, "/pool/checkConnect")
//	if err != nil {
//		logger.Error.Println(err)
//		return err
//	}
//	return nil
//}

func ConnHiveCheck(dbinfo source.AccDbInfo) error {
	//检测上传文件
	err := HiveCheckFile(dbinfo)
	if err != nil {
		logger.Error.Println("文件名检测错误")
		return err
	}

	var body gin.H
	dbid := dbinfo.Id
	if dbid == "" {
		dbid = esutil.GenerateSubID()
	}
	hiveurl := URLHive(dbinfo)

	//fmt.Println("dbinfo.HiveConn.DriveInfo", dbinfo.HiveConn.DriveInfo)
	body = gin.H{
		"id":                           dbid,
		"name":                         dbinfo.DBName,
		"dataSourceType":               dbinfo.DBType,
		"url":                          hiveurl,
		"username":                     dbinfo.HiveConn.HiveUser,
		"password":                     dbinfo.HiveConn.HivePassword,
		"driverPluginName":             dbinfo.HiveConn.DriveInfo.DriverPluginName,
		"driverLocation":               dbinfo.HiveConn.DriveInfo.DriverPath,
		"hadoopConfigurationResources": dbinfo.HiveConn.HiveSiteXMLPath + "," + dbinfo.HiveConn.HdfsSiteXMLPath + "," + dbinfo.HiveConn.CoreSiteXMLPath + "," + dbinfo.HiveConn.YarnSiteXMLPath,
	}

	if dbinfo.HiveConn.KeytabFilePath != "" {
		body["kerberosPrincipal"] = dbinfo.HiveConn.Principal
		body["kerberosKeytab"] = dbinfo.HiveConn.KeytabFilePath
		body["kerberosKrb5Conf"] = dbinfo.HiveConn.Krb5ConfPath
	}

	uri := "/meta-gateway/datasources/check"
	logger.Info.Println("---uri---", uri)
	strbody, _ := json.Marshal(body)
	logger.Info.Println("------string(strbody)-----", string(strbody))
	_, err = MGPOST(uri, string(strbody))
	if err != nil {
		logger.Error.Println("数据源注册MG出错:%s", err)
		return err
	}
	return nil
}

func ConnHive(dbinfo source.AccDbInfo) error {
	//检测上传文件
	err := HiveCheckFile(dbinfo)
	if err != nil {
		logger.Error.Println("文件名检测错误")
		return err
	}

	var body gin.H
	dbid := dbinfo.Id
	if dbid == "" {
		dbid = esutil.GenerateSubID()
	}
	hiveurl := URLHive(dbinfo)

	//fmt.Println("dbinfo.HiveConn.DriveInfo", dbinfo.HiveConn.DriveInfo)
	body = gin.H{
		"id":                           dbid,
		"name":                         dbinfo.DBName,
		"dataSourceType":               dbinfo.DBType,
		"url":                          hiveurl,
		"username":                     dbinfo.HiveConn.HiveUser,
		"password":                     dbinfo.HiveConn.HivePassword,
		"driverPluginName":             dbinfo.HiveConn.DriveInfo.DriverPluginName,
		"driverLocation":               dbinfo.HiveConn.DriveInfo.DriverPath,
		"hadoopConfigurationResources": dbinfo.HiveConn.HiveSiteXMLPath + "," + dbinfo.HiveConn.HdfsSiteXMLPath + "," + dbinfo.HiveConn.CoreSiteXMLPath + "," + dbinfo.HiveConn.YarnSiteXMLPath,
	}

	if dbinfo.HiveConn.KeytabFilePath != "" {
		body["kerberosPrincipal"] = dbinfo.HiveConn.Principal
		body["kerberosKeytab"] = dbinfo.HiveConn.KeytabFilePath
		body["kerberosKrb5Conf"] = dbinfo.HiveConn.Krb5ConfPath
	}

	uri := "/meta-gateway/datasources"
	logger.Info.Println("---uri---", uri)
	strbody, _ := json.Marshal(body)
	logger.Info.Println("------string(strbody)-----", string(strbody))
	_, err = MGPOST(uri, string(strbody))
	if err != nil {
		logger.Error.Println("数据源注册MG出错:%s", err)
		return err
	}
	return nil
}

func HiveCheckFile(dbinfo source.AccDbInfo) error {
	exist, _ := comtools.PathExists(dbinfo.HiveConn.HiveSiteXMLPath)
	if !exist {
		logger.Error.Println("HiveSite导入文不存在")
		return errors.New("HiveSite导入文件不存在")
	}
	filenameall_HiveSiteXMLPath := path.Base(dbinfo.HiveConn.HiveSiteXMLPath)
	filesuffix_HiveSiteXMLPath := path.Ext(dbinfo.HiveConn.HiveSiteXMLPath)
	fileprefix_HiveSiteXMLPath := filenameall_HiveSiteXMLPath[0 : len(filenameall_HiveSiteXMLPath)-len(filesuffix_HiveSiteXMLPath)]
	if filesuffix_HiveSiteXMLPath != ".xml" {
		logger.Error.Println("HiveSite导入文件格式错误，只支持.xml")
		return fmt.Errorf("HiveSite导入文件格式错误，只支持.xml")
	}
	if fileprefix_HiveSiteXMLPath != "hive-site" {
		logger.Error.Println("HiveSite导入文件名称错误，只支持hive-site")
		return fmt.Errorf("HiveSite导入文件名称错误，只支持hive-site")
	}
	//exist, _ = comtools.PathExists(dbinfo.HiveConn.YarnSiteXMLPath)
	//if !exist {
	//	logger.Error.Println("YarnSite导入文不存在")
	//	return errors.New("YarnSite导入文件不存在")
	//}
	//filenameall_YarnSiteXMLPath := path.Base(dbinfo.HiveConn.YarnSiteXMLPath)
	//filesuffix_YarnSiteXMLPath := path.Ext(dbinfo.HiveConn.YarnSiteXMLPath)
	//fileprefix_YarnSiteXMLPath := filenameall_YarnSiteXMLPath[0 : len(filenameall_YarnSiteXMLPath)-len(filesuffix_YarnSiteXMLPath)]
	//if filesuffix_YarnSiteXMLPath != ".xml" {
	//	return fmt.Errorf("YarnSite导入文件格式错误，只支持.xml")
	//}
	//if fileprefix_YarnSiteXMLPath != "yarn-site" {
	//	return fmt.Errorf("YarnSite导入文件名称错误，只支持yarn-site")
	//}
	exist, _ = comtools.PathExists(dbinfo.HiveConn.HdfsSiteXMLPath)
	if !exist {
		logger.Error.Println("HdfsSite导入文不存在")
		return errors.New("HdfsSite导入文件不存在")
	}
	filenameall_HdfsSiteXMLPath := path.Base(dbinfo.HiveConn.HdfsSiteXMLPath)
	filesuffix_HdfsSiteXMLPath := path.Ext(dbinfo.HiveConn.HdfsSiteXMLPath)
	fileprefix_HdfsSiteXMLPath := filenameall_HdfsSiteXMLPath[0 : len(filenameall_HdfsSiteXMLPath)-len(filesuffix_HdfsSiteXMLPath)]
	if filesuffix_HdfsSiteXMLPath != ".xml" {
		return fmt.Errorf("HdfsSite导入文件格式错误，只支持.xml")
	}
	if fileprefix_HdfsSiteXMLPath != "hdfs-site" {
		return fmt.Errorf("HdfsSite导入文件名称错误，只支持hdfs-site")
	}
	exist, _ = comtools.PathExists(dbinfo.HiveConn.CoreSiteXMLPath)
	if !exist {
		logger.Error.Println("CoreSite导入文不存在")
		return errors.New("CoreSite导入文件不存在")
	}
	filenameall_CoreSiteXMLPath := path.Base(dbinfo.HiveConn.CoreSiteXMLPath)
	filesuffix_CoreSiteXMLPath := path.Ext(dbinfo.HiveConn.CoreSiteXMLPath)
	fileprefix_CoreSiteXMLPath := filenameall_CoreSiteXMLPath[0 : len(filenameall_CoreSiteXMLPath)-len(filesuffix_CoreSiteXMLPath)]
	if filesuffix_CoreSiteXMLPath != ".xml" {
		return fmt.Errorf("CoreSite导入文件格式错误，只支持.xml")
	}
	if fileprefix_CoreSiteXMLPath != "core-site" {
		return fmt.Errorf("CoreSite导入文件名称错误，只支持core-site")
	}
	if dbinfo.HiveConn.AuthMethod == 2 {
		exist, _ = comtools.PathExists(dbinfo.HiveConn.Krb5ConfPath)
		if !exist {
			logger.Error.Println("Krb5导入文不存在")
			return errors.New("Krb5导入文件不存在")
		}
		filenameall_Krb5ConfPath := path.Base(dbinfo.HiveConn.Krb5ConfPath)
		filesuffix_Krb5ConfPath := path.Ext(dbinfo.HiveConn.Krb5ConfPath)
		fileprefix_Krb5ConfPath := filenameall_Krb5ConfPath[0 : len(filenameall_Krb5ConfPath)-len(filesuffix_Krb5ConfPath)]
		if filesuffix_Krb5ConfPath != ".conf" {
			return fmt.Errorf("Krb5导入文件格式错误，只支持.conf")
		}
		if fileprefix_Krb5ConfPath != "krb5" {
			return fmt.Errorf("Krb5导入文件名称错误，只支持krb5")
		}
		exist, _ = comtools.PathExists(dbinfo.HiveConn.KeytabFilePath)
		if !exist {
			logger.Error.Println("Keytab导入文不存在")
			return errors.New("Keytab导入文件不存在")
		}
		filenameall_KeytabFilePath := path.Base(dbinfo.HiveConn.KeytabFilePath)
		filesuffix_KeytabFilePath := path.Ext(dbinfo.HiveConn.KeytabFilePath)
		fileprefix_KeytabFilePath := filenameall_KeytabFilePath[0 : len(filenameall_KeytabFilePath)-len(filesuffix_KeytabFilePath)]
		str := []string{"?", "@", "#", "$", "%", "|", "/", "\\", "'", "\"", "`", " ", "(", ")", "[", "]", "{", "}", "（", "）", "【", "】"}
		if filesuffix_KeytabFilePath != ".keytab" {
			return fmt.Errorf("Keytab导入文件格式错误，只支持.keytab")
		}
		err := tools.IsInString(fileprefix_KeytabFilePath, str)
		if err != nil {
			return fmt.Errorf("Keytab导入文件名称错误，不支持 ?@#$%%|/\\'\"` 空格括号字符")
		}
	}
	return nil
}

func ConnDMDB(dbinfo source.AccDbInfo) error {
	var body gin.H
	dbid := dbinfo.Id
	if dbid == "" {
		dbid = esutil.GenerateSubID()
	}

	url := fmt.Sprintf(`jdbc:dm://%s:%s/`, dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort)
	body = gin.H{
		"id":               dbid,
		"name":             dbinfo.DBName,
		"dataSourceType":   dbinfo.DBType,
		"url":              url,
		"username":         dbinfo.NormalConn.DBUser,
		"password":         dbinfo.NormalConn.DBPassword,
		"driverPluginName": common.DM_Driver,
	}

	uri := "/meta-gateway/datasources"
	fmt.Println("---uri---", uri)
	strbody, _ := json.Marshal(body)
	fmt.Println("------string(strbody)-----", string(strbody))
	_, err := MGPOST(uri, string(strbody))
	if err != nil {
		logger.Error.Println("数据源注册MG出错:%s", err)
		return err
	}
	return nil
}

func ConnUXDB(dbinfo source.AccDbInfo) error {
	var body gin.H
	dbid := dbinfo.Id
	if dbid == "" {
		dbid = esutil.GenerateSubID()
	}

	url := fmt.Sprintf(`jdbc:uxdb://%s:%s/%s`, dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.Database)
	body = gin.H{
		"id":              dbid,
		"dataSourceType":  dbinfo.DBType,
		"url":             url,
		"username":        dbinfo.NormalConn.DBUser,
		"password":        dbinfo.NormalConn.DBPassword,
		"driverClassName": common.UXDB_Driver_Class,
	}

	uri := "/meta-gateway/datasources"
	fmt.Println("---uri---", uri)
	strbody, _ := json.Marshal(body)
	fmt.Println("------string(strbody)-----", string(strbody))
	_, err := MGPOST(uri, string(strbody))
	if err != nil {
		logger.Error.Println("数据源注册MG出错:%s", err)
		return err
	}
	return nil
}

func ConnGaussDBCheck(dbinfo source.AccDbInfo) error {
	var body gin.H
	dbid := dbinfo.Id
	if dbid == "" {
		dbid = esutil.GenerateSubID()
	}
	url := fmt.Sprintf(`jdbc:postgresql://%s:%s/%s`, dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.Database)
	body = gin.H{
		"id":              dbid,
		"dataSourceType":  dbinfo.DBType,
		"url":             url,
		"username":        dbinfo.NormalConn.DBUser,
		"password":        dbinfo.NormalConn.DBPassword,
		"driverClassName": "org.postgresql.Driver",
	}

	uri := "/meta-gateway/datasources/check"
	fmt.Println("---uri---", uri)
	strbody, _ := json.Marshal(body)
	fmt.Println("------string(strbody)-----", string(strbody))
	_, err := MGPOST(uri, string(strbody))
	if err != nil {
		logger.Error.Println("数据源注册MG出错:%s", err)
		return err
	}
	return nil
}

func ConnGaussDB(dbinfo source.AccDbInfo) error {
	var body gin.H
	dbid := dbinfo.Id
	if dbid == "" {
		dbid = esutil.GenerateSubID()
	}
	url := fmt.Sprintf(`jdbc:postgresql://%s:%s/%s`, dbinfo.NormalConn.DBIP, dbinfo.NormalConn.DBPort, dbinfo.NormalConn.Database)
	body = gin.H{
		"id":              dbid,
		"dataSourceType":  dbinfo.DBType,
		"url":             url,
		"username":        dbinfo.NormalConn.DBUser,
		"password":        dbinfo.NormalConn.DBPassword,
		"driverClassName": "org.postgresql.Driver",
	}

	uri := "/meta-gateway/datasources"
	fmt.Println("---uri---", uri)
	strbody, _ := json.Marshal(body)
	fmt.Println("------string(strbody)-----", string(strbody))
	_, err := MGPOST(uri, string(strbody))
	if err != nil {
		logger.Error.Println("数据源注册MG出错:%s", err)
		return err
	}
	return nil
}

func ConnPushType(dbinfo source.AccDbInfo) error {
	NowTime := time.Now().Unix() //当前实际时间
	loc, _ := time.LoadLocation("Local")
	startUnix, _ := time.ParseInLocation(common.DateFormat, dbinfo.CreatedTime, loc)
	cTime := startUnix.Unix()
	date := cTime + int64(dbinfo.ExpireData)*86400 - NowTime
	if date < 0 && dbinfo.ExpireData != 0 {
		err := errors.New("数据源已过期")
		return err
	}
	return nil
}

// 需要填写database的数据库类型
func AccDbDatabase(dbtype string) bool {
	var dblist []string
	dblist = append(dblist, common.MYSQL, common.POSTGRES, common.GREENPLUM, common.Stork, common.Teryx,
		common.SQLserver, common.DB2, common.GaussDB)
	for _, i := range dblist {
		if dbtype == i {
			return true
		}
	}
	return false
}

func CheckAccdbParam(connInfo source.AccDbInfo) error {
	if connInfo.DBName == "" {
		return errors.New("数据库名称必填")
	}
	if AccDbDatabase(connInfo.DBType) && connInfo.NormalConn.DBPort == "" {
		return errors.New("该类型数据库端口必填")
	}
	if AccDbDatabase(connInfo.DBType) && connInfo.NormalConn.Database == "" {
		return errors.New("数据库库名必填")
	}
	if connInfo.NormalConn.Domains != nil && len(connInfo.NormalConn.Domains) > 0 {
		for _, dom := range connInfo.NormalConn.Domains {
			if dom.Ip == "" || dom.Dns == "" {
				return errors.New("域名填写错误")
			}
			//域名只能保护字母、数字、连字符，首字母不能为数字，不能包含下划线
			match, _ := regexp.MatchString("^[A-Za-z-][0-9A-Za-z-]*$", dom.Dns)
			if match == false {
				return errors.New("域名格式错误，只能保护字母、数字、连字符，首字母不能为数字")
			}
		}
	}
	return nil
}

// 此接口获取时区，并检测时区
func GetTimeZone(connInfo source.AccDbInfo, check bool) (source.TimeZoneInfo, error) {
	//#18114 时区转换问题
	if connInfo.TimeZoneInfo.ServerTimeZone == "CST" {
		connInfo.TimeZoneInfo.ServerTimeZone = "Asia/Shanghai"
	}

	var tzinfo source.TimeZoneInfo
	body := gin.H{
		//"id":              engininfo.EngineID,
		"id":             esutil.GenerateSubID(),
		"name":           connInfo.DBName,
		"dataSourceType": connInfo.DBType,
		"username":       connInfo.NormalConn.DBUser,
		"password":       connInfo.NormalConn.DBPassword,
	}
	if connInfo.TimeZoneInfo.ServerTimeZone != "" {
		body["pros"] = gin.H{
			"serverTimezone": connInfo.TimeZoneInfo.ServerTimeZone,
		}
	}
	if check {
		body = gin.H{
			//"id":              engininfo.EngineID,
			"id":             esutil.GenerateSubID(),
			"name":           connInfo.DBName,
			"dataSourceType": connInfo.DBType,
			"username":       connInfo.NormalConn.DBUser,
			"password":       connInfo.NormalConn.DBPassword,
			"pros": gin.H{
				"time_zone":      connInfo.TimeZoneInfo.TimeZone,
				"serverTimezone": connInfo.TimeZoneInfo.ServerTimeZone,
			},
		}
	}

	url := fmt.Sprintf(`jdbc:mysql://%s:%s/%s`,
		connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
		connInfo.NormalConn.Database)
	body["driverClassName"] = "com.mysql.jdbc.Driver"

	body["url"] = url
	uri := "/meta-gateway/datasources"
	strBody, err := json.Marshal(body)
	if err != nil {
		logger.Error.Printf("解析数据源请求参数失败:%s", err.Error())
		return tzinfo, err
	}
	fmt.Println("ceshi:", string(strBody))
	//logger.Error.Println("-----string(strBody)-----", string(strBody))
	res, err := MGPOST(uri, string(strBody))
	if err != nil {
		if logger.Error != nil {
			logger.Error.Printf("添加数据源失败:%s,返回信息为:%s", err.Error(), string(res))
		}
		return tzinfo, err
	}
	var registerinfo source.RegisterRes
	//logger.Error.Println("-----res-----", string(res))
	err = json.Unmarshal(res, &registerinfo)
	if err != nil {
		logger.Error.Printf("解析registerinfo参数失败:%s", err.Error())
		return tzinfo, err
	}
	tzinfo.TimeZone = registerinfo.Data.TZInfo.TimeZone
	tzinfo.ServerTimeZone = registerinfo.Data.TZInfo.ServerTimezone
	return tzinfo, nil
}

// 此接口获取库版本
func GetDBVersion(connInfo source.AccDbInfo, check bool) (string, error) {
	//#18114 时区转换问题
	if connInfo.TimeZoneInfo.ServerTimeZone == "CST" {
		connInfo.TimeZoneInfo.ServerTimeZone = "Asia/Shanghai"
	}
	var version string
	body := gin.H{
		//"id":              engininfo.EngineID,
		"id":             esutil.GenerateSubID(),
		"name":           connInfo.DBName,
		"dataSourceType": connInfo.DBType,
		"username":       connInfo.NormalConn.DBUser,
		"password":       connInfo.NormalConn.DBPassword,
	}
	if check {
		body = gin.H{
			//"id":              engininfo.EngineID,
			"id":             esutil.GenerateSubID(),
			"name":           connInfo.DBName,
			"dataSourceType": connInfo.DBType,
			"username":       connInfo.NormalConn.DBUser,
			"password":       connInfo.NormalConn.DBPassword,
			"pros": gin.H{
				"time_zone":      connInfo.TimeZoneInfo.TimeZone,
				"serverTimezone": connInfo.TimeZoneInfo.ServerTimeZone,
			},
		}
	}

	url := fmt.Sprintf(`jdbc:mysql://%s:%s/%s`,
		connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
		connInfo.NormalConn.Database)
	body["driverClassName"] = "com.mysql.jdbc.Driver"

	body["url"] = url
	uri := "/meta-gateway/datasources"
	strBody, err := json.Marshal(body)
	if err != nil {
		logger.Error.Printf("解析数据源请求参数失败:%s", err.Error())
		return version, err
	}
	//logger.Error.Println("-----string(strBody)-----", string(strBody))
	res, err := MGPOST(uri, string(strBody))
	if err != nil {
		logger.Error.Printf("添加数据源失败:%s,返回信息为:%s", err.Error(), string(res))
		return version, err
	}
	var registerinfo source.RegisterRes
	//logger.Error.Println("-----res-----", string(res))
	err = json.Unmarshal(res, &registerinfo)
	if err != nil {
		logger.Error.Printf("解析registerinfo参数失败:%s", err.Error())
		return version, err
	}
	version = registerinfo.Data.Version
	return version, nil
}

func AccDatabaseCheck(dbinfo source.AccDbInfo) error {
	//本地数据源不做检测
	if dbinfo.DBType == common.LocalDB {
		return nil
	}

	errchan := make(chan error)
	var err error
	logger.Info.Println("-asdasdasdsa-d-------ad-asd-as-da-d-as-da-sd-as")
	go func(errchan chan error) {
		//暂时不统一，根据库名进行匹对
		switch dbinfo.DBType {
		case "mysql":
			//err := ConnMysqlCheck(dbinfo)
			err := MGSourceCheck(dbinfo)
			if err != nil {
				errchan <- err
			}
			_, err = GetTimeZone(dbinfo, true)
			errchan <- err
		case "stork", "postgres", "postgresql": // 4.6.2 添加 GaussDB
			//err := ConnStorkCheck(dbinfo)
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case "teryx", "greenplum":
			//err := ConnStorkCheck(dbinfo)
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case "oracle":
			err := MGSourceCheck(dbinfo)
			//err := ConnOracleCheck(dbinfo)
			errchan <- err
		case "SQL server":
			//err := ConnSqlServerCheck(dbinfo)
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case "db2":
			//err := ConnDB2Check(dbinfo)
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case "csv":
			err := ConnCSVCheck(dbinfo)
			errchan <- err
		case "hive":
			logger.Info.Println("hvihvivhvvivhvivhvivhviv")
			err := ConnHiveCheck(dbinfo)
			errchan <- err
		case common.DMDB:
			//err := ConnDMDB(dbinfo)
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case common.UXDB:
			//err := ConnUXDB(dbinfo)
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case common.GAUSSDB:
			err := ConnGaussDBCheck(dbinfo)
			errchan <- err
		case common.Gbase8s:
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case common.KingBaseEs, common.VastBase, common.Tbase:
			err := MGSourceCheck(dbinfo)
			errchan <- err
		case common.PushType:
			err := ConnPushType(dbinfo)
			errchan <- err
		default:
			errchan <- errors.New("未知数据库类型")
			// return false, errors.New("未知数据库类型")
		}
	}(errchan)

	select {
	case err = <-errchan:
		if err != nil {
			err1 := CheckUpdate(&dbinfo, false)
			if err1 != nil {
				logger.Error.Println(err1)
				return err1
			}
			return err
		}
		err = CheckUpdate(&dbinfo, true)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
		return nil
	case <-time.After(time.Second * time.Duration(common.ConnTimeout)):
		err = CheckUpdate(&dbinfo, false)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
		return errors.New("检测超时")
	}
	return nil
}

func DbCheckLog() {
	resp, err := esutil.SearchAll(common.DataBaseMetaData, common.TbMetaExtract)
	if err != nil {
		logger.Error.Println("例举数据源失败:%s", err.Error())
		return
	}
	var sour source.DBInfoHits1
	err = json.Unmarshal(resp, &sour)
	if err != nil {
		logger.Error.Println(err)
		return
	}
	for _, v := range sour.Hits1.Hits2 {
		err = AccDatabaseCheck(v.DBInfoSource)
		if err != nil {
			logger.Error.Println(err)
		}
	}
}

func CheckUpdate(dbinfo *source.AccDbInfo, success bool) error {
	//todo 1指针问题，2局部更新
	if dbinfo.Id == "" { //""==注册检测==不需要更新
		return nil
	}
	dbinfo1, err := DBSearch(dbinfo.Id)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	dbinfo = &dbinfo1
	var checklog source.AccCheckLog
	if success {
		dbinfo.DBStatus = true
		checklog.CheckStatus = 1
		checklog.CheckMessage = "元数据检测成功"
	} else {
		dbinfo.DBStatus = false
		checklog.CheckStatus = 0
		checklog.CheckMessage = "元数据检测失败"
	}
	checklog.CheckTime = time.Now().Format("2006-01-02 15:04:05")
	dbinfo.CheckLogs = append(dbinfo.CheckLogs, checklog)
	err = MonthLogDel(dbinfo)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	doc := gin.H{
		"dbstatus": dbinfo.DBStatus,
		"checklog": dbinfo.CheckLogs,
		"synclog":  dbinfo.SyncLogs,
	}
	err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaExtract, common.RefreshWait, dbinfo.Id, doc)
	if err != nil {
		logger.Error.Printf("更新日志:%s", err.Error())
		return err
	}

	//err = DBUpdate(*dbinfo)
	//if err != nil {
	//	logger.Error.Println(err)
	//	return err
	//}
	return nil
}

func ExistMGConn(id string) bool {
	connurl := fmt.Sprintf("/meta-gateway/datasources/%s", id)
	fmt.Println("--connurl-", connurl)
	_, err := MGQuery("GET", connurl, "")
	if err != nil {
		//logger.Error.Println("MGQuery err:%s", err.Error())
		return false
	}
	return true
}

func RegisterMG(engininfo source.EngineInfo, dbid string) error {
	body := gin.H{}
	var url string
	//  注释掉检测id存在，MG兼容，存在--进行检测，不存在--注册
	//if ExistMGConn(dbid) {
	//	//fmt.Println("-----已注册---")
	//	return nil
	//}
	switch engininfo.EngineName {
	case "stork", common.POSTGRES:
		url = fmt.Sprintf(`jdbc:postgresql://%s:%d/%s`, engininfo.IP, engininfo.Port, engininfo.InitDB)
		body = gin.H{
			//"id":              engininfo.EngineID,
			"id":              dbid,
			"name":            engininfo.Name,
			"dataSourceType":  engininfo.EngineName,
			"url":             url,
			"username":        engininfo.Username,
			"password":        engininfo.Password,
			"driverClassName": "org.postgresql.Driver",
		}
	case "teryx", common.GAUSSDB, common.GAUSSDBA, common.GREENPLUM:
		url = fmt.Sprintf(`jdbc:postgresql://%s:%d/%s`, engininfo.IP, engininfo.Port, engininfo.InitDB)
		body = gin.H{
			//"id":              engininfo.EngineID,
			"id":              dbid,
			"name":            engininfo.Name,
			"dataSourceType":  engininfo.EngineName,
			"url":             url,
			"username":        engininfo.Username,
			"password":        engininfo.Password,
			"driverClassName": "org.postgresql.Driver",
		}
	case "hive", common.Inceptor:

		//todo engininfo.InitDB会为nil？ default
		if engininfo.JdbcURL != "" {
			url = engininfo.JdbcURL
			if engininfo.InitDB != "" {
				url = InsteadDBname(url, engininfo.InitDB)
			}
		} else {
			url = fmt.Sprintf(`*********************************`, engininfo.IP, engininfo.Port, engininfo.InitDB)
		}
		tmps := strings.Split(engininfo.HiveFilePath, "/")
		var hivexml, hdfsxml, corexml, yarnxml string

		if tmps[len(tmps)-1] != "" {
			hivexml = engininfo.HiveFilePath + "/" + "hive-site.xml"
			hdfsxml = engininfo.HiveFilePath + "/" + "hdfs-site.xml"
			corexml = engininfo.HiveFilePath + "/" + "core-site.xml"
			yarnxml = engininfo.HiveFilePath + "/" + "yarn-site.xml"
		} else {
			hivexml = engininfo.HiveFilePath + "hive-site.xml"
			hdfsxml = engininfo.HiveFilePath + "hdfs-site.xml"
			corexml = engininfo.HiveFilePath + "core-site.xml"
			yarnxml = engininfo.HiveFilePath + "yarn-site.xml"
		}

		body = gin.H{
			"id":             dbid,
			"name":           engininfo.Name,
			"dataSourceType": engininfo.EngineName,
			"url":            url,
			//"driverClassName": 				"org.apache.hive.jdbc.HiveDriver",
			"username":                     engininfo.Username,
			"password":                     engininfo.Password,
			"driverPluginName":             engininfo.DriverInfo.DriverPluginName,
			"driverLocation":               engininfo.DriverInfo.DriverPath,
			"hadoopConfigurationResources": hivexml + "," + hdfsxml + "," + corexml + "," + yarnxml,
		}
		if engininfo.IsConfigPros {
			pros := make(map[string]string)
			for _, v := range engininfo.Pros {
				pros[v.Key] = v.Value
			}
			body["pros"] = pros
		}
		if engininfo.IsConfigKbs == "1" {
			body["kerberosPrincipal"] = engininfo.KerberosInfo.Principal
			body["kerberosKeytab"] = engininfo.KerberosInfo.KeyTabPath
			body["kerberosKrb5Conf"] = engininfo.KerberosInfo.Krb5ConfPath
		}

	case common.DMDB:
		if engininfo.RegisterWay == "colony" {
			//ip为空则直接获取jdbcurl传参，当做集群处理
			url = engininfo.JdbcURL
		} else {
			url = fmt.Sprintf("jdbc:dm://%s:%d/", engininfo.IP, engininfo.Port)
		}
		//url = fmt.Sprintf(`jdbc:dm://%s:%d/`, engininfo.IP, engininfo.Port)
		body = gin.H{
			"id":               dbid,
			"name":             engininfo.Name,
			"dataSourceType":   engininfo.EngineName,
			"url":              url,
			"username":         engininfo.Username,
			"password":         engininfo.Password,
			"driverPluginName": common.DM_Driver,
		}
	case common.UXDB:
		url = fmt.Sprintf("jdbc:uxdb://%s:%d/%s", engininfo.IP, engininfo.Port, engininfo.InitDB)
		body = gin.H{
			"dataSourceType":  common.UXDB,
			"driverClassName": common.UXDB_Driver_Class,
			"id":              dbid,
			"name":            engininfo.Name,
			"password":        engininfo.Password,
			"url":             url,
			"username":        engininfo.Username,
		}
	case common.ODPS:
		body = gin.H{
			"id":             dbid,
			"name":           engininfo.Name,
			"dataSourceType": engininfo.EngineName,
			"username":       engininfo.Username,
			"password":       engininfo.Password,
			"pros": gin.H{
				"odpsURL":   engininfo.OdpsUrl,
				"tunnelURL": engininfo.TunnelUrl,
				"project":   engininfo.OdpsProject,
			},
		}
	case common.TDSQL:
		if engininfo.RegisterWay == "colony" {
			//ip为空则直接获取jdbcurl传参，当做集群处理
			url = engininfo.JdbcURL
		} else {
			url = fmt.Sprintf(`jdbc:postgresql://%s:%d/%s`, engininfo.IP, engininfo.Port, engininfo.InitDB)
		}
		body = gin.H{
			"id":               dbid,
			"name":             engininfo.Name,
			"dataSourceType":   "stork", //tdsql数据库类型MG处使用stork
			"url":              url,
			"username":         engininfo.Username,
			"password":         engininfo.Password,
			"driverPluginName": "org.postgresql.Driver",
		}
		//drealdb
	case common.MYSQL, common.DRealDb:
		dsType := engininfo.EngineName
		//if engininfo.EngineName == common.DRealDb {
		//	dsType = common.MYSQL
		//}
		url = fmt.Sprintf(`**********************************`,
			engininfo.IP, engininfo.Port,
			engininfo.InitDB)
		body = gin.H{
			"id":              dbid,
			"name":            engininfo.Name,
			"dataSourceType":  dsType,
			"url":             url,
			"username":        engininfo.Username,
			"password":        engininfo.Password,
			"driverClassName": "com.mysql.jdbc.Driver",
		}
		if engininfo.TimeZoneInfo.ServerTimeZone == "CST" {
			engininfo.TimeZoneInfo.ServerTimeZone = "Asia/Shanghai"
		}
		body["driverClassName"] = "com.mysql.jdbc.Driver"
		body["pros"] = gin.H{
			"time_zone":      engininfo.TimeZoneInfo.TimeZone,
			"serverTimezone": engininfo.TimeZoneInfo.ServerTimeZone,
		}
	}
	uri := "/meta-gateway/datasources"
	strbody, _ := json.Marshal(body)
	//fmt.Println("---------------")
	fmt.Println("string(strbody)", string(strbody))
	fmt.Println("uri", uri)
	res, err := MGPOST(uri, string(strbody))
	if err != nil {
		fmt.Println("请求MG返回错误信息：", string(res))
		fmt.Println("MG请求出错:%s", err)
		fmt.Println("MG请求报错的情况---->请求的body为：  ", string(strbody))
		return err
	}
	// gaussdb 获取到 gaussdb版本信息。
	if engininfo.EngineName == common.GAUSSDB || engininfo.EngineName == common.GaussDBA {
		version := gjson.Get(string(res), "data.version").String()
		common.GAUSSDBVERSION = version
	}
	return nil
}

func MGPOST(uri, reqbody string) ([]byte, error) {
	var code int64
	var res []byte
	ip := common.ServerIP
	url := fmt.Sprintf("http://%s:%s%s",
		ip,
		common.MGPort,
		uri)

	client := &http.Client{}

	req, err := http.NewRequest("POST", url, strings.NewReader(reqbody))
	// req, err := http.Get(url)
	if err != nil { //fmt.Println("---reqbody--", reqbody)
		//logger.Error.Println("postMG出错:%s", err.Error())
		fmt.Println("postMG出错:%s", err.Error())
		return res, comtools.WrapError(err, errcode.PostAndRequestErrorCode)
	}
	//fmt.Println("---url--", url)
	req.Header.Set("Content-Type", "application/json")
	req.SetBasicAuth(common.MGUser, common.MGPassword)

	resp, err := client.Do(req)
	if err != nil {
		//logger.Error.Printf("注册MG出错:%s", err.Error())
		fmt.Printf("注册MG出错:%s", err.Error())
		return res, comtools.WrapError(err, errcode.PostAndRequestErrorCode)
	}
	defer resp.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	res = buf.Bytes()
	code = gjson.Get(string(buf.Bytes()), "code").Int()
	if code != 200 {
		errstr := fmt.Sprintf("postMG失败,原因:MGInfo:%s", string(res))
		return res, comtools.WrapMg(errors.New(errstr))
		// return res, errors.New("postMG失败")
	}
	//data := gjson.Get(string(buf.Bytes()), "data")
	return res, err
}

func MGPOST_Timeout(uri, reqbody string) ([]byte, error) {
	var code int64
	var res []byte
	ip := common.ServerIP
	url := fmt.Sprintf("http://%s:%s%s",
		ip,
		common.MGPort,
		uri)

	client := &http.Client{
		Transport: &http.Transport{
			ResponseHeaderTimeout: time.Second * time.Duration(common.SyncTimeoutonce),
		},
	}

	req, err := http.NewRequest("POST", url, strings.NewReader(reqbody))
	// req, err := http.Get(url)
	if err != nil { //fmt.Println("---reqbody--", reqbody)
		//logger.Error.Println("postMG出错:%s", err.Error())
		fmt.Println("postMG出错:%s", err.Error())
		return res, comtools.WrapError(err, errcode.PostAndRequestErrorCode)
	}
	//fmt.Println("---url--", url)
	req.Header.Set("Content-Type", "application/json")
	req.SetBasicAuth(common.MGUser, common.MGPassword)

	resp, err := client.Do(req)
	if err != nil {
		//logger.Error.Printf("注册MG出错:%s", err.Error())
		fmt.Printf("注册MG出错:%s", err.Error())
		return res, comtools.WrapError(err, errcode.PostAndRequestErrorCode)
	}
	defer resp.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	res = buf.Bytes()
	code = gjson.Get(string(buf.Bytes()), "code").Int()
	if code != 200 {
		errstr := fmt.Sprintf("postMG失败,原因:MGInfo:%s", string(res))
		return res, comtools.WrapMg(errors.New(errstr))
		// return res, errors.New("postMG失败")
	}
	//data := gjson.Get(string(buf.Bytes()), "data")
	return res, err
}

func MGGET(uri, reqbody string) ([]byte, error) {
	var code int64
	var res []byte
	ip := common.ServerIP
	url := fmt.Sprintf("http://%s:%s%s",
		ip,
		common.MGPort,
		uri)

	client := &http.Client{}

	req, err := http.NewRequest("GET", url, strings.NewReader(reqbody))
	// req, err := http.Get(url)
	if err != nil { //fmt.Println("---reqbody--", reqbody)
		//logger.Error.Println("postMG出错:%s", err.Error())
		fmt.Println("postMG出错:%s", err.Error())
		return res, err
	}
	//fmt.Println("---url--", url)
	req.Header.Set("Content-Type", "application/json")
	req.SetBasicAuth(common.MGUser, common.MGPassword)

	resp, err := client.Do(req)
	if err != nil {
		//logger.Error.Printf("注册MG出错:%s", err.Error())
		fmt.Printf("注册MG出错:%s", err.Error())
		return res, err
	}
	defer resp.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	res = buf.Bytes()
	code = gjson.Get(string(buf.Bytes()), "code").Int()
	if code != 200 {
		errstr := fmt.Sprintf("postMG失败,原因:%s", string(res))
		return res, errors.New(errstr)
		// return res, errors.New("postMG失败")
	}
	//data := gjson.Get(string(buf.Bytes()), "data")
	return res, err
}

func MGQuery(method, uri, reqbody string) ([]byte, error) {
	var code int64
	var res []byte
	ip := common.ServerIP
	url := fmt.Sprintf("http://%s:%s%s",
		ip,
		common.MGPort,
		uri)
	client := &http.Client{}
	req, err := http.NewRequest(method, url, strings.NewReader(reqbody))
	if err != nil {
		//logger.Error.Println("MGQuery出错:%s", err.Error())
		return res, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.SetBasicAuth(common.MGUser, common.MGPassword)

	resp, err := client.Do(req)
	if err != nil {
		//logger.Error.Printf("MGQuery出错:%s", err)
		return res, err
	}
	defer resp.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	res = buf.Bytes()
	code = gjson.Get(string(buf.Bytes()), "code").Int()
	//fmt.Println("--res---",string(res))
	if code != 200 {
		//logger.Error.Printf("MGQuery出错res:%s", string(res))
		return res, errors.New("MGQuery出错:" + string(res))
	}
	return res, err
}

// MGDelete 删除长连接
func MGDelete(uri, body string) ([]byte, error) {
	url := fmt.Sprintf("http://%s:%s%s",
		common.ServerIP,
		common.MGPort,
		uri)
	client := &http.Client{}
	req, err := http.NewRequest("DELETE", url, strings.NewReader(body))
	if err != nil {
		return []byte(""), err
	}
	req.Header.Set("Content-Type", "application/json")
	req.SetBasicAuth(common.MGUser, common.MGPassword)

	resp, err := client.Do(req)
	if err != nil {
		return []byte(""), err
	}
	defer resp.Body.Close()
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	res := buf.Bytes()
	return res, err
}

func InsteadDBname(url, dbname string) string {
	sindex := strings.Index(url, ";")
	var subUrl, lastUrl string
	if sindex == -1 {
		// url不包含认证，类似于jdbc:hive2://**************:10000/default
		subUrl = url
		mindex := strings.LastIndex(subUrl, "/")
		midUrl := subUrl[:mindex]
		lastUrl = midUrl + "/" + dbname
	} else {
		subUrl = url[:sindex]
		mindex := strings.LastIndex(subUrl, "/")
		midUrl := subUrl[:mindex]
		lastUrl = midUrl + "/" + dbname + url[sindex:]
	}
	return lastUrl
}

// MGSourceCheck 通过MG检测数据源连接情况
func MGSourceCheck(connInfo source.AccDbInfo) error {
	// postgres、greenplum一律置为stork
	//if connInfo.DBType == common.POSTGRES || connInfo.DBType == common.POSTGRES ||connInfo.DBType == common.Greenplum {
	//	connInfo.DBType = common.Stork
	//}
	dbtype := connInfo.DBType
	if dbtype == common.SQLserver {
		dbtype = "SQLSERVER"
	}
	if dbtype == common.DMDB && strings.ToUpper(connInfo.DBVersion) == "V7" {
		dbtype = common.Dameng7
	}
	id := connInfo.Id
	if id == "" {
		id = esutil.GenerateSubID()
	}
	//logger.Info.Println("------id-------", id)
	var err error
	var url string
	body := gin.H{
		//"id":              engininfo.EngineID,
		"id":             id,
		"name":           connInfo.DBName,
		"dataSourceType": dbtype,
		"username":       connInfo.NormalConn.DBUser,
		"password":       connInfo.NormalConn.DBPassword,
	}
	url = URLNormalDB(connInfo)
	switch connInfo.DBType {
	case common.Stork, common.Teryx, common.GREENPLUM, common.POSTGRES, common.GAUSSDB, common.GaussDBA:
		body["driverClassName"] = "org.postgresql.Driver"
	case common.MYSQL:
		//#18114 时区转换问题
		if connInfo.TimeZoneInfo.ServerTimeZone == "CST" {
			connInfo.TimeZoneInfo.ServerTimeZone = "Asia/Shanghai"
		}
		body["driverClassName"] = "com.mysql.jdbc.Driver"
		body["pros"] = gin.H{
			"time_zone":      connInfo.TimeZoneInfo.TimeZone,
			"serverTimezone": connInfo.TimeZoneInfo.ServerTimeZone,
		}
	case common.ORACLE:
		if connInfo.NormalConn.OracleServer != "" {
			url = fmt.Sprintf(`**************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.OracleServer)
		} else if connInfo.NormalConn.Oraclesid != "" {
			url = fmt.Sprintf(`**************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Oraclesid)
		} else {
			return fmt.Errorf("oracle数据源SID/服务名不能都为空")
		}
		body["driverClassName"] = "oracle.jdbc.OracleDriver"
	case common.SQLserver:
		body["driverPluginName"] = "sqlserver-8"
	case common.DB2:
		body["driverClassName"] = "com.ibm.db2.jcc.DB2Driver"
	case common.DMDB:
		if strings.ToUpper(connInfo.DBVersion) == "V7" {
			body["driverPluginName"] = common.DM7_Driver
		} else {
			body["driverPluginName"] = common.DM_Driver
		}
	case common.UXDB:
		body["driverClassName"] = common.UXDB_Driver_Class
	case common.Gbase8s:
		body["driverClassName"] = common.GBASE_Driver_Class
	case common.VastBase, common.KingBaseEs, common.Tbase:
		body["driverClassName"] = "org.postgresql.Driver"
	}
	body["url"] = url
	uri := "/meta-gateway/datasources/check"
	strBody, err := json.Marshal(body)
	if err != nil {
		logger.Error.Printf("解析数据源请求参数失败:%s", err.Error())
		return err
	}
	fmt.Println("--MG-body--------", string(strBody))
	res, err := MGPOST(uri, string(strBody))
	if err != nil {
		if logger.Error != nil {
			logger.Error.Printf("添加数据源失败:%s,返回信息为:%s", err.Error(), string(res))
		}
		return err
	}
	//上述方法检测数据源连接，MG接口有5分钟延时，为了避免大量连接，如果5分钟内重复调用接口，会直接返回首次调用结果，而不是查询数据库
	//导致底层数据库被删除后无法立即感知
	//新增方法对数据库进行判定
	//#16156 数据库和schema存在区别，以下方法是获取一个数据源中的所有schema
	//if connInfo.NormalConn.Database != "" {
	//	_, exist, err := GetAllDBNamesByDatasourceId(id, connInfo.NormalConn.Database)
	//	if err != nil {
	//		logger.Error.Printf("注册数据源查询库失败:%s,返回信息为:%s", err.Error(), string(res))
	//		return err
	//	}
	//	if !exist {
	//		err = errors.New("注册数据源失败，底层不存在库名：" + connInfo.NormalConn.Database)
	//		logger.Error.Printf("注册数据源查询库失败:%s", err.Error())
	//		return err
	//	}
	//}
	return nil
}

// AddMGSource 向mg注册相应数据库jdbc连接
func AddMGSource(connInfo source.AccDbInfo) error {
	// postgres、greenplum一律置为stork
	//if connInfo.DBType == common.POSTGRES || connInfo.DBType == common.POSTGRES ||connInfo.DBType == common.Greenplum {
	//	connInfo.DBType = common.Stork
	//}
	dbtype := connInfo.DBType
	if dbtype == common.SQLserver {
		dbtype = "SQLSERVER"
	}
	if dbtype == common.DMDB && strings.ToUpper(connInfo.DBVersion) == "V7" {
		dbtype = common.Dameng7
	}
	id := connInfo.Id
	if id == "" {
		id = esutil.GenerateSubID()
	}
	//logger.Info.Println("------id-------", id)
	var err error
	var url string
	body := gin.H{
		//"id":              engininfo.EngineID,
		"id":             id,
		"name":           connInfo.DBName,
		"dataSourceType": dbtype,
		"username":       connInfo.NormalConn.DBUser,
		"password":       connInfo.NormalConn.DBPassword,
	}
	url = URLNormalDB(connInfo)
	switch connInfo.DBType {
	case common.Stork, common.Teryx, common.GREENPLUM, common.POSTGRES, common.GAUSSDB, common.GaussDBA:
		body["driverClassName"] = "org.postgresql.Driver"
	case common.MYSQL:
		//#18114 时区转换问题
		if connInfo.TimeZoneInfo.ServerTimeZone == "CST" {
			connInfo.TimeZoneInfo.ServerTimeZone = "Asia/Shanghai"
		}
		body["driverClassName"] = "com.mysql.jdbc.Driver"
		body["pros"] = gin.H{
			"time_zone":      connInfo.TimeZoneInfo.TimeZone,
			"serverTimezone": connInfo.TimeZoneInfo.ServerTimeZone,
		}
	case common.ORACLE:
		if connInfo.NormalConn.OracleServer != "" {
			url = fmt.Sprintf(`**************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.OracleServer)
		} else if connInfo.NormalConn.Oraclesid != "" {
			url = fmt.Sprintf(`**************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Oraclesid)
		} else {
			return fmt.Errorf("oracle数据源SID/服务名不能都为空")
		}
		body["driverClassName"] = "oracle.jdbc.OracleDriver"
	case common.SQLserver:
		body["driverPluginName"] = "sqlserver-8"
	case common.DB2:
		body["driverClassName"] = "com.ibm.db2.jcc.DB2Driver"
	case common.DMDB:
		if strings.ToUpper(connInfo.DBVersion) == "V7" {
			body["driverPluginName"] = common.DM7_Driver
		} else {
			body["driverPluginName"] = common.DM_Driver
		}
	case common.UXDB:
		body["driverClassName"] = common.UXDB_Driver_Class
	case common.Gbase8s:
		body["driverClassName"] = common.GBASE_Driver_Class
	case common.VastBase, common.KingBaseEs, common.Tbase:
		body["driverClassName"] = "org.postgresql.Driver"
	}
	body["url"] = url
	uri := "/meta-gateway/datasources"
	strBody, err := json.Marshal(body)
	if err != nil {
		logger.Error.Printf("解析数据源请求参数失败:%s", err.Error())
		return err
	}
	fmt.Println("--MG-body--------", string(strBody))
	res, err := MGPOST(uri, string(strBody))
	if err != nil {
		if logger.Error != nil {
			logger.Error.Printf("添加数据源失败:%s,返回信息为:%s", err.Error(), string(res))
		}
		return err
	}
	//上述方法检测数据源连接，MG接口有5分钟延时，为了避免大量连接，如果5分钟内重复调用接口，会直接返回首次调用结果，而不是查询数据库
	//导致底层数据库被删除后无法立即感知
	//新增方法对数据库进行判定
	//#16156 数据库和schema存在区别，以下方法是获取一个数据源中的所有schema
	//if connInfo.NormalConn.Database != "" {
	//	_, exist, err := GetAllDBNamesByDatasourceId(id, connInfo.NormalConn.Database)
	//	if err != nil {
	//		logger.Error.Printf("注册数据源查询库失败:%s,返回信息为:%s", err.Error(), string(res))
	//		return err
	//	}
	//	if !exist {
	//		err = errors.New("注册数据源失败，底层不存在库名：" + connInfo.NormalConn.Database)
	//		logger.Error.Printf("注册数据源查询库失败:%s", err.Error())
	//		return err
	//	}
	//}
	return nil
}

func URLNormalDB(connInfo source.AccDbInfo) (url string) {
	self_def := ""
	switch connInfo.DBType {
	case common.Stork, common.TDSQL, common.Teryx, common.GREENPLUM, common.POSTGRES, common.GAUSSDB, common.GaussDBA,
		common.KingBaseEs, common.VastBase, common.Tbase:
		url = fmt.Sprintf(`jdbc:postgresql://%s:%s/%s`,
			connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
			connInfo.NormalConn.Database)
		if connInfo.NormalConn.ISOpenSSL {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += "&"
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
			}
			url = fmt.Sprintf(`************************************************************************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				connInfo.NormalConn.PGSSLConf.SSLMode, connInfo.NormalConn.PGSSLConf.SSLRootCertPath,
				connInfo.NormalConn.PGSSLConf.SSLKeyPath, connInfo.NormalConn.PGSSLConf.SSLCertPath, self_def)
		} else if len(connInfo.NormalConn.SelfDefines) > 0 {
			for i, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
				if i+1 != len(connInfo.NormalConn.SelfDefines) {
					self_def += "&"
				}
			}
			url = fmt.Sprintf(`*****************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				self_def)
		}

	case common.MYSQL, common.DRealDb:
		var tmpDefines []source.SelfDefine
		tmpDefines = connInfo.NormalConn.SelfDefines
		if !connInfo.NormalConn.ISOpenSSL {
			hasSSL := false
			for _, i := range tmpDefines {
				if i.Key == "useSSL" {
					hasSSL = true
				}
			}
			if !hasSSL {
				tmpDefines = append(tmpDefines, source.SelfDefine{Key: "useSSL", Value: "false"})
			}
		}

		url = fmt.Sprintf(`jdbc:mysql://%s:%s/%s`,
			connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
			connInfo.NormalConn.Database)
		if connInfo.TMPURL != "" {
			url = connInfo.TMPURL
			url += "&"
		} else if connInfo.NormalConn.ISOpenSSL || len(tmpDefines) > 0 {
			url += "?"
		}

		if connInfo.NormalConn.ISOpenSSL {
			for _, v := range tmpDefines {
				self_def += "&"
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
			}

			url = fmt.Sprintf(`%suseSSL=true&trustCertificateKeyStoreUrl=file:%s&trustCertificateKeyStorePassword=%s%s`,
				url,
				connInfo.NormalConn.MySQLSSLConf.TrustCertificateKeyStoreUrl, connInfo.NormalConn.MySQLSSLConf.TrustCertificateKeyStorePassword,
				self_def)
		} else if len(tmpDefines) > 0 {
			for i, v := range tmpDefines {
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
				if i+1 != len(tmpDefines) {
					self_def += "&"
				}
			}
			url = fmt.Sprintf(`%s%s`,
				url,
				self_def)
		}
	case common.DB2:
		url = fmt.Sprintf(`jdbc:db2://%s:%s/%s`,
			connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
			connInfo.NormalConn.Database)
		if connInfo.NormalConn.ISOpenSSL {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s;", v.Key, v.Value)
			}
			url = fmt.Sprintf(`*******************************************************************************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				connInfo.NormalConn.DB2SSLConf.SSLTrustStoreLocation, connInfo.NormalConn.DB2SSLConf.SSLTrustStorePassword,
				self_def)
		} else if len(connInfo.NormalConn.SelfDefines) > 0 {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s;", v.Key, v.Value)
			}
			url = fmt.Sprintf(`**********************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				self_def)
		}

	case common.SQLserver:
		url = fmt.Sprintf(`**********************************`,
			connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
			connInfo.NormalConn.Database)
		if connInfo.NormalConn.ISOpenSSL {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s;", v.Key, v.Value)
			}
			hostNameInCertificate := ""
			//todo 域名
			if len(connInfo.NormalConn.SqlServerSSLConf.Domains) > 0 {
				//写入/etc/hosts
				err := WriteDNSTOHosts(connInfo.NormalConn.SqlServerSSLConf.Domains)
				if err != nil {
					logger.Error.Println("域名映射写入/etc/hosts错误", err)
				}
				hostNameInCertificate = connInfo.NormalConn.SqlServerSSLConf.Domains[0].Dns
			}
			//logger.Info.Println("connInfo.NormalConn.SqlServerSSLConf.TrustServerCertificate", connInfo.NormalConn.SqlServerSSLConf.TrustServerCertificate)
			url = fmt.Sprintf(`*********************************************************************************************************************************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				connInfo.NormalConn.SqlServerSSLConf.TrustServerCertificate,
				connInfo.NormalConn.SqlServerSSLConf.TrustStore,
				connInfo.NormalConn.SqlServerSSLConf.TrustStorePassword,
				hostNameInCertificate,
				self_def)
		} else if len(connInfo.NormalConn.SelfDefines) > 0 {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s;", v.Key, v.Value)
			}
			url = fmt.Sprintf(`**********************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				self_def)
		}
	case common.DMDB:
		if connInfo.NormalConn.RegisterWay == "colony" {
			//达梦支持集群，由用户填写jdbcurl
			url = connInfo.NormalConn.JdbcUrl
		} else {
			url = fmt.Sprintf(`jdbc:dm://%s:%s/`, connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort)
		}
		fmt.Println("--------url-----", url)
		if connInfo.NormalConn.ISOpenSSL {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += "&"
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
			}
			url = fmt.Sprintf(`%s?sslFilesPath=%s&sslKeystorePass=%s%s`,
				url,
				connInfo.NormalConn.DMSSLConf.SSLFilesPath, connInfo.NormalConn.DMSSLConf.SSLKeystorePass,
				self_def)
			fmt.Println("--222------url-----", url)
		} else if len(connInfo.NormalConn.SelfDefines) > 0 {

			for i, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
				if i+1 != len(connInfo.NormalConn.SelfDefines) {
					self_def += "&"
				}
			}
			url = fmt.Sprintf(`%s?%s`, url, self_def)
		}
	case common.UXDB:
		url = fmt.Sprintf(`jdbc:uxdb://%s:%s/%s`,
			connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
			connInfo.NormalConn.Database)
		if connInfo.NormalConn.ISOpenSSL {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += "&"
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
			}
			url = fmt.Sprintf(`******************************************************************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				connInfo.NormalConn.UXSSLConf.SSLMode, connInfo.NormalConn.UXSSLConf.SSLRootCertPath,
				connInfo.NormalConn.UXSSLConf.SSLKeyPath, connInfo.NormalConn.UXSSLConf.SSLCertPath, self_def)
		} else if len(connInfo.NormalConn.SelfDefines) > 0 {
			for i, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
				if i+1 != len(connInfo.NormalConn.SelfDefines) {
					self_def += "&"
				}
			}
			url = fmt.Sprintf(`***********************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database,
				self_def)
		}
	case common.ORACLE:
		if connInfo.NormalConn.OracleServer != "" {
			url = fmt.Sprintf(`**************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.OracleServer)
		} else if connInfo.NormalConn.Oraclesid != "" {
			url = fmt.Sprintf(`**************************`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Oraclesid)
		} else {
			return ""
		}
	case common.Gbase8s:
		url = fmt.Sprintf(`jdbc:gbasedbt-sqli://%s:%s/%s:GBASEDBTSERVER=%s;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=10;IFX_ISOLATION_LEVEL=1U;`,
			connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort, connInfo.NormalConn.Database,
			connInfo.NormalConn.GbaseServer)
		if connInfo.NormalConn.ISOpenSSL {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
				self_def += ";"
			}
			//暂未开启SSL
			url = fmt.Sprintf(`jdbc:gbasedbt-sqli://%s:%s/%s:GBASEDBTSERVER=%s;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=10;IFX_ISOLATION_LEVEL=1U;%s`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort, connInfo.NormalConn.Database,
				connInfo.NormalConn.GbaseServer, self_def)
		} else if len(connInfo.NormalConn.SelfDefines) > 0 {
			for _, v := range connInfo.NormalConn.SelfDefines {
				self_def += fmt.Sprintf("%s=%s", v.Key, v.Value)
				self_def += ";"
			}
			url = fmt.Sprintf(`jdbc:gbasedbt-sqli://%s:%s/%s:GBASEDBTSERVER=%s;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=10;IFX_ISOLATION_LEVEL=1U;%s`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort, connInfo.NormalConn.Database,
				connInfo.NormalConn.GbaseServer, self_def)
		}
	case common.TDSQL:
		if connInfo.NormalConn.RegisterWay == "colony" {
			//达梦支持集群，由用户填写jdbcurl
			url = connInfo.NormalConn.JdbcUrl
		} else {
			url = fmt.Sprintf(`jdbc:postgresql://%s:%s/%s`,
				connInfo.NormalConn.DBIP, connInfo.NormalConn.DBPort,
				connInfo.NormalConn.Database)
		}
	}

	return url
}

func URLHive(connInfo source.AccDbInfo) (url string) {
	url = connInfo.HiveConn.HiveURL
	self_def := ""
	if connInfo.HiveConn.ISOpenSSL {
		for _, v := range connInfo.NormalConn.SelfDefines {
			self_def += fmt.Sprintf("%s=%s;", v.Key, v.Value)
		}
		url = fmt.Sprintf("%s;ssl=true;sslTrustStore=%s;trustStorePassword=%s;%s", url,
			connInfo.HiveConn.HiveSSLConf.SSLTrustStorePath,
			connInfo.HiveConn.HiveSSLConf.TrustStorePassword,
			self_def)
	} else if len(connInfo.NormalConn.SelfDefines) > 0 {
		for _, v := range connInfo.NormalConn.SelfDefines {
			self_def += fmt.Sprintf("%s=%s;", v.Key, v.Value)
		}
		url = fmt.Sprintf("%s;%s", url, self_def)
	}
	return url
}

func WriteDNSTOHosts(domains []source.Domain) error {
	hosts, err := goodhosts.NewHosts()
	if err != nil {
		return err
	}

	for _, v := range domains {
		// IP相同，域名相同
		if hosts.Has(v.Ip, v.Dns) {
			return nil
		}
		err := hosts.Add(v.Ip, v.Dns)
		if err != nil {
			return err
		}
		err = hosts.Flush()
		if err != nil {
			return err
		}
	}
	return nil
}

func LocalTbUpload(localDbInfo source.AccDbInfo, filename string, file *multipart.File, delimiter string) (string, error) {
	//存储目录固定：/var/dana/localdb/{dbid}
	dir := common.LocalDBPath + localDbInfo.Id
	_, err := os.Stat(dir)
	if err != nil {
		err := os.MkdirAll(dir, os.ModePerm)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
	}

	filename = checkLocalTbName(localDbInfo.Id, filename)
	filePath := dir + "/" + filename
	//创建文件
	out, err := os.Create(filePath)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	defer func() {
		if out != nil {
			out.Close()
		}
	}()
	_, err = io.Copy(out, *file)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	fileinfo, err := out.Stat()
	fileSize := fileinfo.Size()
	out.Close()

	//处理表信息
	var tbinfo source.TableInfo
	tbinfo.TableID = esutil.GenerateSubID()
	tbinfo.ID = tbinfo.TableID
	tbinfo.TableName = filename
	tbinfo.CreateTime = time.Now().Format("2006-01-02 15:04:05")
	tbinfo.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	tbinfo.DistributionMode = ""
	tbinfo.DistributionBond = []string{}
	tbinfo.SizeNum = uint64(fileSize)
	tbinfo.StorageSpace = tools.ByteCalWithSource(tbinfo.SizeNum)
	tbinfo.DBID = localDbInfo.Id
	if strings.HasSuffix(strings.ToLower(filename), ".csv") {
		//插入文件记录
		tbinfo, err = processLocalTbCSV(filePath, tbinfo)
		if err != nil {
			logger.Error.Println(err)
			os.Remove(filePath)
			return filePath, err
		}
		tbinfo.LocalTbType = "csv"
		tbinfo.CSVDelimiter = delimiter
	} else if strings.HasSuffix(strings.ToLower(filename), ".xlsx") {
		tbinfo.LocalTbType = "excel"
		tbinfo, err = processLocalTbExcelXLSX(filePath, tbinfo)
		if err != nil {
			logger.Error.Println(err)
			os.Remove(filePath)
			return filePath, err
		}
	} else if strings.HasSuffix(strings.ToLower(filename), ".xls") {
		tbinfo.LocalTbType = "excel"
		tbinfo, err = processLocalTbExcelXLS(filePath, tbinfo)
		if err != nil {
			logger.Error.Println(err)
			os.Remove(filePath)
			return filePath, err
		}
	}

	err = esutil.AddWithAimID(common.DataBaseMetaData, common.TbInfoMeta, tbinfo.ID, tbinfo)
	if err != nil {
		logger.Error.Printf(filePath, " 添加localdb数据源失败:%s", err.Error())
		return filePath, err
	}

	//新增表数量
	go countLocaltbnum(localDbInfo.Id)
	//同步到其他节点
	go localTbSync(dir, filePath)

	return filePath, nil
}

// 如果出现重名，需要加上_1(数字根据实际情况变动)
func checkLocalTbName(dbid string, filename string) string {
	fileSuffix := path.Ext(filename)
	filePrefix := strings.TrimSuffix(filename, fileSuffix)
	var tempParam source.ParamLocalDb
	tempParam.DbId = dbid
	tempParam.TableName = filename
	nameList, _ := LocalTbQuery(tempParam)
	if len(nameList) == 0 {
		return filename
	}
	temp := tools.RandomString(6)
	return filePrefix + "_" + temp + fileSuffix
}

// 需要根据上传的文件对数据及字段进行解析,获取表详情
func processLocalTbCSV(filePath string, tbinfo source.TableInfo) (source.TableInfo, error) {
	tbinfo.CSVEncode, _ = Query_Csv_Encode(filePath)
	var conn source.CSVConn
	conn.Encoding = "localdbcsv"
	conn.LocalPath = common.LocalDBPath + tbinfo.DBID + "/"
	if tbinfo.CSVDelimiter == "" {
		tbinfo.CSVDelimiter = "," //如果不传参，则默认为逗号分割
	}
	conn.Delimiter = tbinfo.CSVDelimiter
	fieldname, threeRecord, err := ReadLineV2(filePath, tbinfo.CSVEncode, conn)
	logger.Info.Println("fieldname1:", fieldname)
	if err != nil {
		logger.Error.Println(filePath, "readlinev2  | readline error:", err)
		return tbinfo, err
	}
	if len(fieldname) == 0 {
		return tbinfo, errors.New("上传的" + tbinfo.TableName + "文件解析字段为空")
	}
	for _, f := range fieldname {
		var filedinfo source.Field
		filedinfo.FieldName = strings.Replace(f, `"`, "", -1)
		filedinfo.FieldType = "string"
		filedinfo.ContainCh = false
		if tools.IsContainChinese(f) {
			filedinfo.ContainCh = true
			tbinfo.ContainChinese = true
		}
		filedinfo.IsDigit = tools.StrISNum(f)
		if !tbinfo.FieldHaveDigit {
			tbinfo.FieldHaveDigit = filedinfo.IsDigit
		}
		tbinfo.Fields = append(tbinfo.Fields, filedinfo)
	}
	tbinfo.FieldNum = len(tbinfo.Fields)
	//三条数据获取
	for i, v := range threeRecord {
		if i >= 3 {
			break
		}
		record := strings.Replace(v, " ", "", -1)
		result := strings.Split(record, conn.Delimiter)
		for j := 0; j < len(result); j++ {
			result[j] = strings.Trim(result[j], `"`)
		}
		tbinfo.ThreeRecord[i] = result
	}
	//获取数据量和表大小
	rowNums, err := Analyze_CSV_V2(&tbinfo, conn)
	if err != nil {
		logger.Error.Println("获取csv数据量等失败:", err)
	}
	if rowNums <= 0 {
		tbinfo.Status = 0
	} else {
		tbinfo.Status = 1
	}
	tbinfo.RowNum = rowNums
	return tbinfo, nil
}

// 根据上传的excel文件进行解析，并获取表信息详情,包含读取表数据信息：第一行是表字段名，第二行之后均为数据
func processLocalTbExcelXLSX(filePath string, tbinfo source.TableInfo) (source.TableInfo, error) {
	f, err := my_exce.OpenFile(filePath)
	if err != nil {
		logger.Error.Println(filePath, " processLocalTbExcel OpenFile error:", err.Error())
		return tbinfo, err
	}
	defer f.Close()
	// 获取 Sheet1 上所有单元格
	sheet1 := f.GetSheetName(0)
	rows, err := f.GetRows(sheet1)
	if err != nil {
		logger.Error.Println(filePath, " processLocalTbExcel GetRows error:", err.Error())
		return tbinfo, err
	}
	if len(rows) == 0 {
		return tbinfo, errors.New("上传的" + tbinfo.TableName + " excel文件为空")
	}
	tbinfo.RowNum = len(rows) - 1
	for i, row := range rows {
		if i > 3 {
			break
		}
		if i == 0 {
			for _, headCell := range row {
				var filedinfo source.Field
				filedinfo.FieldName = strings.Replace(headCell, `"`, "", -1)
				filedinfo.FieldType = "string"
				filedinfo.ContainCh = false
				if tools.IsContainChinese(headCell) {
					filedinfo.ContainCh = true
					tbinfo.ContainChinese = true
				}
				filedinfo.IsDigit = tools.StrISNum(headCell)
				if !tbinfo.FieldHaveDigit {
					tbinfo.FieldHaveDigit = filedinfo.IsDigit
				}
				tbinfo.Fields = append(tbinfo.Fields, filedinfo)
			}
			tbinfo.FieldNum = len(tbinfo.Fields)
		}
		if i > 0 && i <= 3 {
			var result []string
			for _, colCell := range row {
				result = append(result, colCell)
			}
			tbinfo.ThreeRecord[i-1] = result
		}
	}
	if tbinfo.RowNum <= 0 {
		tbinfo.Status = 0
	} else {
		tbinfo.Status = 1
	}
	return tbinfo, nil
}

// 根据上传的excel文件进行解析，并获取表信息详情
func processLocalTbExcelXLS(filePath string, tbinfo source.TableInfo) (source.TableInfo, error) {
	xlsFile, err := my_xls.Open(filePath, "utf-8")
	if err != nil {
		logger.Error.Println(filePath, " processLocalTbExcel OpenFile error:", err.Error())
		return tbinfo, err
	}
	// 获取xls文件的第一个sheet
	sheet := xlsFile.GetSheet(0)
	tbinfo.RowNum = int(sheet.MaxRow)
	if sheet.Row(0) == nil {
		return tbinfo, errors.New("上传的" + tbinfo.TableName + " excel文件为空")
	}
	//取三行数据
	for i := 0; i <= int(sheet.MaxRow); i++ {
		row := sheet.Row(i)
		if row == nil {
			break
		}
		if i > 3 {
			break
		}
		if i == 0 {
			for j := 0; j < row.LastCol(); j++ {
				col := row.Col(j)
				var filedinfo source.Field
				filedinfo.FieldName = strings.Replace(col, `"`, "", -1)
				filedinfo.FieldType = "string"
				filedinfo.ContainCh = false
				if tools.IsContainChinese(col) {
					filedinfo.ContainCh = true
					tbinfo.ContainChinese = true
				}
				filedinfo.IsDigit = tools.StrISNum(col)
				if !tbinfo.FieldHaveDigit {
					tbinfo.FieldHaveDigit = filedinfo.IsDigit
				}
				tbinfo.Fields = append(tbinfo.Fields, filedinfo)
			}
			tbinfo.FieldNum = len(tbinfo.Fields)
		} else {
			data := make([]string, 0)
			for j := 0; j < row.LastCol(); j++ {
				col := row.Col(j)
				data = append(data, col)
			}
			tbinfo.ThreeRecord[i-1] = data
		}
	}
	if tbinfo.RowNum <= 0 {
		tbinfo.Status = 0
	} else {
		tbinfo.Status = 1
	}
	return tbinfo, nil
}

func localTbSync(dir, filepath string) error {
	cfg, err := ini.Load(common.ServerConf)
	other, _ := cfg.GetSection("other")
	if err != nil {
		common.Nodeport = 22
	} else {
		searchnum, err := other.GetKey("nodeport")
		if err != nil {
			common.Nodeport = 22
		}
		common.Nodeport, err = searchnum.Int()
		if err != nil {
			common.Nodeport = 22
		}
	}

	ip := tools.GetHostIP()
	//获取集群节点
	nodes, err := ListDsNode()
	if err != nil {
		if logger.Error != nil {
			logger.Error.Printf("获取集群ip失败:%s", err)
			return err
		}
	}

	username := os.Getenv("USER")
	//logger.Info.Println("ds代码获取的用户名为: ", username)

	err = createRemotePath(dir, ip, common.Nodeport, nodes, username)
	if err != nil {
		if logger.Error != nil {
			logger.Error.Println("localTbSync 创建集群目录失败: ", err)
			return err
		}
	}

	outpath := dir + "/"

	for _, v := range nodes {
		if v != ip {
			command := fmt.Sprintf("scp -P %d %s %s@%s:%s", common.Nodeport, filepath, username, v, outpath)
			_, err := tools.ExeCommand(command)
			if err != nil {
				if logger.Error != nil {
					logger.Error.Printf("localTbSync 集群同步文件失败:%s", err.Error(), " command is :", command)
				}
			}

		}
	}
	return nil
}

func createRemotePath(path, selfip string, Nodeport int, ips []string, username string) error {
	command := fmt.Sprintf(`
		if [ ! -d "%s" ]; then
			mkdir -p "%s" && chmod 777 "%s"
fi`, path, path, path)
	for _, v := range ips {
		if v != selfip {
			exec := fmt.Sprintf("ssh -p %d %s@%s '%s'", Nodeport, username, v, command)
			str, err := tools.ExeCommand(exec)
			if err != nil {
				if logger.Error != nil {
					logger.Error.Printf("createRemotePath 集群目录新建失败原因:%s and 失败信息:%s", exec, str, err.Error())
					return err
				}
			}
		}
	}
	return nil
}

// 查询本地数据库的表信息,返回库名，
func LocalTbQuery(info source.ParamLocalDb) ([]string, error) {
	var terms []interface{}
	if info.DbId != "" {
		term1 := gin.H{
			"term": gin.H{
				"dbid": info.DbId,
			},
		}
		terms = append(terms, term1)
	}
	if info.TableName != "" {
		term3 := gin.H{
			"term": gin.H{
				"tablename": info.TableName,
			},
		}
		terms = append(terms, term3)
	}
	if len(info.TableId) != 0 {
		term2 := gin.H{
			"terms": gin.H{
				"id": info.TableId,
			},
		}
		terms = append(terms, term2)
	}
	var body gin.H
	body = gin.H{
		"_source": []string{"id", "dbid", "tablename", "localtbtype"},
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 10000,
		"sort": gin.H{
			"createtime": "desc",
		},
	}
	resp, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbInfoMeta, body)
	if err != nil {
		logger.Error.Println("例举数据表失败", err.Error())
		return nil, err
	}
	var sour source.TBInfoHits1
	err = json.Unmarshal(resp, &sour)
	if err != nil {
		logger.Error.Println("Unmarshal数据表失败", err.Error())
		return nil, err
	}

	var tableNames []string
	for _, v := range sour.Hits1.Hits2 {
		tableNames = append(tableNames, v.TBInfo.TableName)
	}
	return tableNames, nil
}

// 删除本地数据源表，需要删除本地文件及集群其他节点的文件
func LocalTbDelete(delInfo source.ParamLocalDb, tableNames []string) error {
	//删除ES中存储的表信息
	if len(delInfo.TableId) != 0 {
		err := esutil.DeleteBulk(common.DataBaseMetaData, common.TbInfoMeta, delInfo.TableId)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
		go countLocaltbnum(delInfo.DbId)
	}
	//删除底层文件
	var delPathList []string
	var delFailed []string
	dir := common.LocalDBPath + delInfo.DbId
	for _, name := range tableNames {
		pathtemp := dir + "/" + name
		delPathList = append(delPathList, pathtemp)
		err := os.Remove(pathtemp)
		if err != nil {
			logger.Error.Println("删除本地数据库表：", pathtemp, " | 报错：", err)
			delFailed = append(delFailed, pathtemp)
		}
	}

	nodes, err := ListDsNode()
	if err != nil {
		if logger.Error != nil {
			logger.Error.Printf("获取集群ip失败:%s", err)
			return err
		}
	}
	tempDelPostParam, _ := json.Marshal(delPathList)
	ip := tools.GetHostIP()
	for _, v := range nodes {
		if v != ip {
			parambody := fmt.Sprintf(`{"pathlist":%s}`, string(tempDelPostParam))
			_, err := httpclient.PostJson(v, 21619, "/danastudio/metadata/collect/localtb/delfile", string(parambody))
			if err != nil {
				logger.Error.Println(err)
			}
		}
	}
	if delFailed != nil {
		return errors.New("删除本地文件失败，报错文件路径：" + strings.Join(delFailed, ","))
	}
	return nil
}

func countLocaltbnum(dbid string) {
	//根据localdbid查询该数据源下表数量
	countbody := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"dbid": dbid,
					},
				},
			},
		},
	}
	tbnumCnt, err := esutil.JustCount(common.DataBaseMetaData, common.TbInfoMeta, countbody)
	if err != nil {
		logger.Error.Printf("countLocaltbnum查询数据源表失败:%s", err.Error())
		return
	}
	//更新count数据
	body := gin.H{
		"tbnum": tbnumCnt,
	}
	err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaExtract, common.RefreshTrue, dbid, body)
	if err != nil {
		logger.Error.Printf("countLocaltbnum更新数据源失败:%s", err.Error())
	}
}
func KafkaIsInstall() (bool, error) {

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"enginename": "kafka",
					},
				},
			},
		},
	}
	res, err := esutil.SearchByTerm(common.DataBasePlatform, common.TbEngine, body)
	if err != nil {
		return false, err
	}
	total := gjson.Get(string(res), "hits.total").Int()
	if total == 0 {
		return false, nil
	} else {
		return true, nil
	}
}
func RecordTbUpload(filename string, file *multipart.File, delimiter string) (source.ModeUploadInfo, error) {
	var tbinfo source.TableInfo
	var sourcetable source.MetaSourceFieldInfo
	var sourcetables []source.MetaSourceFieldInfo
	var uploadinfos source.ModeUploadInfo
	//存储目录固定：/var/dana/localmodel/{dbid}
	localid := tools.RandomString(6)
	dir := common.LocalModelDBPath + localid
	_, err := os.Stat(dir)
	if err != nil {
		err := os.MkdirAll(dir, os.ModePerm)
		if err != nil {
			logger.Error.Println(err)
			return uploadinfos, err
		}
	}

	filename = checkLocalTbName(localid, filename)
	filePath := dir + "/" + filename
	//创建文件
	out, err := os.Create(filePath)
	if err != nil {
		logger.Error.Println(err)
		return uploadinfos, err
	}
	defer func() {
		if out != nil {
			out.Close()
		}
	}()
	_, err = io.Copy(out, *file)
	if err != nil {
		logger.Error.Println(err)
		return uploadinfos, err
	}
	//fileinfo, err := out.Stat()
	//fileSize := fileinfo.Size()
	out.Close()

	//处理表信息

	tbinfo.TableID = esutil.GenerateSubID()
	tbinfo.ID = tbinfo.TableID
	tbinfo.TableName = filename
	tbinfo.CreateTime = time.Now().Format("2006-01-02 15:04:05")
	tbinfo.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	tbinfo.DistributionMode = ""
	tbinfo.DistributionBond = []string{}
	//tbinfo.SizeNum = uint64(fileSize)
	tbinfo.StorageSpace = tools.ByteCalWithSource(tbinfo.SizeNum)
	if strings.HasSuffix(strings.ToLower(filename), ".csv") {
		//插入文件记录
		tbinfo, err = processLocalTbCSV(filePath, tbinfo)
		if err != nil {
			logger.Error.Println(err)
			os.Remove(filePath)
			return uploadinfos, err
		}
		tbinfo.LocalTbType = "csv"
		tbinfo.CSVDelimiter = delimiter
	} else if strings.HasSuffix(strings.ToLower(filename), ".xlsx") {
		tbinfo.LocalTbType = "excel"
		tbinfo, err = processExternalTbExcelXLSX(filePath, tbinfo)
		if err != nil {
			logger.Error.Println(err)
			os.Remove(filePath)
			return uploadinfos, err
		}
	} else if strings.HasSuffix(strings.ToLower(filename), ".xls") {
		tbinfo.LocalTbType = "excel"
		tbinfo, err = processExternalTbExcelXLS(filePath, tbinfo)
		if err != nil {
			logger.Error.Println(err)
			os.Remove(filePath)
			return uploadinfos, err
		}
	}

	//err = esutil.AddWithAimID(common.DataBaseMetaData, common.TbInfoMeta, tbinfo.ID, tbinfo)
	//if err != nil {
	//	logger.Error.Printf(filePath, " 添加localdb数据源失败:%s", err.Error())
	//	return filePath, err
	//}

	//新增表数量
	//go countLocaltbnum(localDbInfo.Id)
	////同步到其他节点
	//go localTbSync(dir, filePath)

	for _, v := range tbinfo.Fields {
		sourcetable.Name = v.FieldName
		sourcetable.FieldType = v.FieldType
		sourcetable.Annotation = v.Comment
		sourcetables = append(sourcetables, sourcetable)
	}
	uploadinfos.MetaSourceFieldInfo = sourcetables
	uploadinfos.ExternalPathId = localid
	return uploadinfos, nil
}
func ExternalExportInfo(sourceinfo source.MetaSourceTb) ([][]source.MetaSourceFieldInfo, error) {
	var req [][]source.MetaSourceFieldInfo
	for i, fname := range sourceinfo.Filename {
		//存储目录固定：/var/dana/localmodel/{dbid}
		var tbinfo source.TableInfo
		dir := common.LocalModelDBPath + sourceinfo.ExternalPathId[i]
		_, err := os.Stat(dir)
		if err != nil {
			logger.Error.Println(err)
			return req, err
		}
		filename := checkLocalTbName(sourceinfo.ExternalPathId[i], fname)
		filePath := dir + "/" + filename
		logger.Info.Println("filePath1:", filePath)
		if strings.HasSuffix(strings.ToLower(filename), ".csv") {
			//插入文件记录
			logger.Info.Println("filepath2:", filePath)
			tbinfo, err = processLocalTbCSV(filePath, tbinfo)
			if err != nil {
				logger.Error.Println(err)
				os.Remove(filePath)
				return req, err
			}
		} else if strings.HasSuffix(strings.ToLower(filename), ".xlsx") {
			tbinfo.LocalTbType = "excel"
			tbinfo, err = processLocalTbExcelXLSX(filePath, tbinfo)
			if err != nil {
				logger.Error.Println(err)
				os.Remove(filePath)
				return req, err
			}
		} else if strings.HasSuffix(strings.ToLower(filename), ".xls") {
			tbinfo.LocalTbType = "excel"
			tbinfo, err = processLocalTbExcelXLS(filePath, tbinfo)
			if err != nil {
				logger.Error.Println(err)
				os.Remove(filePath)
				return req, err
			}
		}
		var infos []source.MetaSourceFieldInfo
		logger.Info.Println("infostest:", infos)
		for _, field := range tbinfo.Fields {
			var info source.MetaSourceFieldInfo
			info.Name = field.FieldName
			info.FieldType = field.FieldType
			info.Annotation = field.Comment
			infos = append(infos, info)
		}
		req = append(req, infos)
	}
	return req, nil
}
func ExportTBModelXLSX(ids []string) (filename string, err error) {

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"terms": gin.H{
						"id": ids,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}

	bodyJson, _ := json.Marshal(body)
	logger.Info.Println("代码表查询body:", string(bodyJson))

	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
	if err != nil {
		logger.Error.Printf("查询代码表信息失败:%s", err.Error())
		return "", err
	}

	sources := esutil.GetSource(bytes)

	var TBModelInfo source.XLSXModelParseResult
	for _, s := range sources {
		var l source.MetaSourceTb
		_ = json.Unmarshal([]byte(s), &l)
		TBModelInfo.TableName = l.TBName
		TBModelInfo.TableDesc = l.Describe
		if len(sources) == 1 {
			TBModelInfo.Filename = fmt.Sprintf(`%s_%s.xlsx`, l.TBName, time.Now().Format("20060102"))
			TBModelInfo.SheetNames = append(TBModelInfo.SheetNames, "表模型数据")
			fmt.Println("TBModelInfo.Filename:", TBModelInfo.Filename)
		} else {
			TBModelInfo.Filename = fmt.Sprintf(`表模型数据_%s.xlsx`, time.Now().Format("20060102"))
			if len(l.TBName) > 31 {
				l.TBName = l.TBName[:31]
			}
			TBModelInfo.SheetNames = append(TBModelInfo.SheetNames, l.TBName)
		}
		TBModelInfo.ModelDefinitions = append(TBModelInfo.ModelDefinitions, l.FieldInfo)
	}
	dir := common.ModelExportPath
	_, err = os.Stat(dir)
	if err != nil {
		err := os.MkdirAll(dir, os.ModePerm)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
	}
	filePath := common.ModelExportPath + TBModelInfo.Filename

	// 3. 生成XLSX文件
	file, err := GenerateMTXLSX(&TBModelInfo)
	if err != nil {
		fmt.Printf("生成XLSX失败: %v\n", err)
		return
	}

	// 保存文件
	err = file.Save(filePath)
	if err != nil {
		fmt.Printf("保存文件失败: %v\n", err)
		return
	}
	return TBModelInfo.Filename, nil
}
func GenerateMTXLSX(result *source.XLSXModelParseResult) (*xlsx.File, error) {
	file := xlsx.NewFile()
	// 2. 为每个代码表创建单独的工作表
	for i, encode := range result.ModelDefinitions {
		if i >= len(result.ModelDefinitions) {
			continue
		}

		sheet, err := file.AddSheet(result.SheetNames[i])
		if err != nil {
			return nil, fmt.Errorf("创建工作表失败: %v", err)
		}

		// 添加表头
		header := sheet.AddRow()
		header.AddCell().SetString("序号")
		header.AddCell().SetString("字段名")
		header.AddCell().SetString("类型")
		header.AddCell().SetString("字段注释")
		header.AddCell().SetString("表名称")
		header.AddCell().SetString("表描述")

		// 添加数据行
		for i, code := range encode {
			row := sheet.AddRow()
			row.AddCell().SetString(strconv.Itoa(code.Number))
			row.AddCell().SetString(code.Name)
			row.AddCell().SetString(code.FieldType)
			row.AddCell().SetString(code.Annotation)
			if i == 0 {
				row.AddCell().SetString(result.TableName)
				row.AddCell().SetString(result.TableDesc)
			}
		}
	}

	return file, nil
}

// 解析外部导入的XLSX文件信息：字段名、字段类型、字段注释
func processExternalTbExcelXLSX(filePath string, tbinfo source.TableInfo) (source.TableInfo, error) {
	f, err := my_exce.OpenFile(filePath)
	if err != nil {
		logger.Error.Println(filePath, " processLocalTbExcel OpenFile error:", err.Error())
		return tbinfo, err
	}
	defer f.Close()
	// 获取 Sheet1 上所有单元格
	sheet1 := f.GetSheetName(0)
	rows, err := f.GetRows(sheet1)
	if err != nil {
		logger.Error.Println(filePath, " processLocalTbExcel GetRows error:", err.Error())
		return tbinfo, err
	}
	tbinfo.RowNum = len(rows) - 1

	for i, row := range rows {
		// 跳过表头（如果有）
		if i == 0 {
			continue
		}
		// 确保每行有足够的列
		if len(row) < 3 {
			return tbinfo, fmt.Errorf("行 %d 数据不完整，需要3列，得到%d列", i, len(row))
		}
		if len(row) == 3 {
			tbinfo.Fields = append(tbinfo.Fields, source.Field{FieldName: row[1], FieldType: row[2]})
		} else {
			tbinfo.Fields = append(tbinfo.Fields, source.Field{FieldName: row[1], FieldType: row[2], Comment: row[3]})
		}

	}
	return tbinfo, nil
}

// 解析外部导入的XLS文件信息：字段名、字段类型、字段注释
func processExternalTbExcelXLS(filePath string, tbinfo source.TableInfo) (source.TableInfo, error) {
	xlsFile, err := my_xls.Open(filePath, "utf-8")
	if err != nil {
		logger.Error.Println(filePath, " processLocalTbExcel OpenFile error:", err.Error())
		return tbinfo, err
	}
	// 获取xls文件的第一个sheet
	sheet := xlsFile.GetSheet(0)
	tbinfo.RowNum = int(sheet.MaxRow)
	if sheet.Row(0) == nil {
		return tbinfo, errors.New("上传的" + tbinfo.TableName + " excel文件为空")
	}
	for i := 0; i <= int(sheet.MaxRow); i++ {
		row := sheet.Row(i)
		// 跳过表头（如果有）
		if i == 0 {
			continue
		}
		if row == nil {
			break
		}

		for j := 0; j < row.LastCol(); j++ {
			fmt.Print("danyuange:", row.LastCol(), row.Col(j))
			if row.LastCol() < 3 {
				return tbinfo, fmt.Errorf("行 %d 数据不完整", i)
			}
			if row.LastCol() == 2 {
				tbinfo.Fields = append(tbinfo.Fields, source.Field{FieldName: row.Col(1), FieldType: row.Col(2)})
			} else {
				tbinfo.Fields = append(tbinfo.Fields, source.Field{FieldName: row.Col(1), FieldType: row.Col(2), Comment: row.Col(3)})
			}
		}

	}
	return tbinfo, nil
}

func RecordTbUploadV2(filename string, file *multipart.File, delimiter string) ([]source.FileTbInfo, error) {
	var uploadtbInfoList []source.FileTbInfo
	//存储目录固定：/var/dana/localmodel/{dbid}
	localid := tools.RandomString(6)
	dir := common.LocalModelDBPath + localid
	_, err := os.Stat(dir)
	if err != nil {
		err := os.MkdirAll(dir, os.ModePerm)
		if err != nil {
			logger.Error.Println(err)
			return uploadtbInfoList, err
		}
	}

	filePath := dir + "/" + filename
	logger.Info.Println("上传文件的路径：", filePath)
	//创建文件
	out, err := os.Create(filePath)
	if err != nil {
		logger.Error.Println(err)
		return uploadtbInfoList, err
	}
	defer func() {
		if out != nil {
			out.Close()
		}
	}()
	_, err = io.Copy(out, *file)
	if err != nil {
		logger.Error.Println(err)
		return uploadtbInfoList, err
	}
	out.Close()
	//避免文件堆积
	defer os.Remove(filePath)

	//处理表信息
	if strings.HasSuffix(strings.ToLower(filename), ".csv") {
		var oneTbInfo source.FileTbInfo
		oneTbInfo.TbName = filename
		oneTbInfo.FileName = filename
		oneTbInfo.ExternalPathId = localid
		//插入文件记录
		oneTbInfo.MetaSourceFieldInfo, err = parseCsvFields(filePath, delimiter)
		if err != nil {
			logger.Error.Println(err)
			return uploadtbInfoList, err
		}
		uploadtbInfoList = append(uploadtbInfoList, oneTbInfo)
	} else if strings.HasSuffix(strings.ToLower(filename), ".xlsx") {
		uploadtbInfoList, err = parseXLSXTbinfos(filePath, filename, localid)
		if err != nil {
			logger.Error.Println(err)
			return uploadtbInfoList, err
		}
	} else if strings.HasSuffix(strings.ToLower(filename), ".xls") {
		uploadtbInfoList, err = parseXLSTbinfos(filePath, filename, localid)
		if err != nil {
			logger.Error.Println(err)
			return uploadtbInfoList, err
		}
	}
	return uploadtbInfoList, nil
}

func parseCsvFields(filePath string, delimiter string) ([]source.MetaSourceFieldInfo, error) {
	var fieldList []source.MetaSourceFieldInfo
	command := fmt.Sprintf("cat '%s' ", filePath)
	combuf, err := tools.ExeCommand(command)
	if err != nil {
		logger.Error.Println(command, " readline v2 error: ", err.Error())
		return fieldList, err
	}
	farr := strings.Split(combuf, "\n")
	for i, strline := range farr {
		line := strings.TrimSpace(strline)
		if !strings.Contains(line, delimiter) {
			continue
		}
		templist := strings.Split(line, delimiter)
		if len(templist) < 3 {
			return fieldList, nil
		}
		if i == 0 {
			if templist[0] != "序号" || templist[1] != "字段名" || templist[2] != "类型" {
				return fieldList, errors.New("csv文件首行格式不对：序号,字段名,类型,字段注释")
			}
		} else {
			var onefield source.MetaSourceFieldInfo
			num, _ := strconv.Atoi(templist[0])
			onefield.Number = num
			onefield.Name = templist[1]
			onefield.FieldType = templist[2]
			onefield.Annotation = templist[3]
			fieldList = append(fieldList, onefield)
		}
	}
	return fieldList, nil
}

func parseXLSXTbinfos(filePath, filename, pathId string) ([]source.FileTbInfo, error) {
	var tblist []source.FileTbInfo
	f, err := my_exce.OpenFile(filePath)
	if err != nil {
		logger.Error.Println(filePath, " parseXLSXFields OpenFile error:", err.Error())
		return tblist, err
	}
	defer f.Close()
	// 获取所有sheet页
	allSheetMap := f.GetSheetMap()
	for sheetId, sheetName := range allSheetMap {
		rows, err := f.GetRows(sheetName)
		if err != nil {
			logger.Error.Println(filePath, sheetId, sheetName, " parseXLSXFields GetRows error:", err.Error())
			return tblist, err
		}
		if len(rows) == 0 {
			return tblist, errors.New("上传的 " + filename + " | " + sheetName + " excel sheet页面为空")
		}
		var onetbInfo source.FileTbInfo
		onetbInfo.FileName = filename
		onetbInfo.TbName = sheetName
		onetbInfo.ExternalPathId = pathId
		for i, cells := range rows {
			if len(cells) < 3 {
				continue
				//return tblist, errors.New("上传的 " + filename + " | " + sheetName + " excel sheet页面格式不对：序号，字段名，类型，字段注释")
			}
			if i == 0 {
				if cells[0] != "序号" || cells[1] != "字段名" || cells[2] != "类型" {
					return tblist, errors.New("上传的 " + filename + " | " + sheetName + " excel sheet页面首行格式不对：序号，字段名，类型，字段注释")
				}
			} else {
				var onefield source.MetaSourceFieldInfo
				num, _ := strconv.Atoi(cells[0])
				onefield.Number = num
				onefield.Name = cells[1]
				onefield.FieldType = cells[2]
				if len(cells) >= 4 {
					//此处注释可能为空，导致导出下标范围
					onefield.Annotation = cells[3]
				}
				onetbInfo.MetaSourceFieldInfo = append(onetbInfo.MetaSourceFieldInfo, onefield)
			}
		}
		tblist = append(tblist, onetbInfo)
	}
	return tblist, nil
}

func parseXLSTbinfos(filePath, filename, pathId string) ([]source.FileTbInfo, error) {
	var tblist []source.FileTbInfo
	xlsFile, err := my_xls.Open(filePath, "utf-8")
	if err != nil {
		logger.Error.Println(filePath, " processLocalTbExcel OpenFile error:", err.Error())
		return tblist, err
	}
	for i := 0; i < xlsFile.NumSheets(); i++ {
		sheet := xlsFile.GetSheet(i)
		var onetbInfo source.FileTbInfo
		onetbInfo.FileName = filename
		onetbInfo.TbName = sheet.Name
		onetbInfo.ExternalPathId = pathId

		if sheet.Row(0) == nil {
			return tblist, errors.New("上传的 " + filename + " | " + sheet.Name + " excel sheet页面为空")
		}
		for j := 0; j <= int(sheet.MaxRow); j++ {
			onerow := sheet.Row(j)
			if onerow == nil || onerow.LastCol() < 3 {
				break
			}
			if j == 0 {
				if onerow.Col(0) != "序号" || onerow.Col(1) != "字段名" || onerow.Col(2) != "类型" {
					return tblist, errors.New("上传的 " + filename + " | " + sheet.Name + " excel sheet页面首行格式不对：序号，字段名，类型，字段注释")
				}
			} else {
				var onefield source.MetaSourceFieldInfo
				num, _ := strconv.Atoi(onerow.Col(0))
				onefield.Number = num
				onefield.Name = onerow.Col(1)
				onefield.FieldType = onerow.Col(2)
				onefield.Annotation = onerow.Col(3)
				onetbInfo.MetaSourceFieldInfo = append(onetbInfo.MetaSourceFieldInfo, onefield)
			}
			tblist = append(tblist, onetbInfo)

		}
	}
	return tblist, nil
}
