/*
Copyright (c) Datatom Software, Inc.(2020)
Author: 陈洪彪（<EMAIL>）
Creating Time: 2020-05-28

mgm层元数据开发--编目/标签管理
*/

package devcenter

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"datatom.com/metadata/httpdo/collect"
	"datatom.com/metadata/httpdo/dbutil"
	serveresclient "datatom.com/tools/common/escli"
	"datatom.com/tools/common/tools"
	"github.com/360EntSecGroup-Skylar/excelize/v2"

	"datatom.com/auth/handlerpack/dtauth"

	"archive/zip"
	"datatom/gin.v1"

	"datatom.com/metadata/common"
	"datatom.com/metadata/httpclient"
	"datatom.com/metadata/httpdo/esutil"
	"datatom.com/metadata/logger"
	"datatom.com/metadata/source"
	"github.com/tidwall/gjson"
	xlsx "github.com/xuri/excelize/v2"
)

// AddDir 新增/编辑文件夹记录
func AddDir(req source.MetaDir) (string, error) {
	if req.ParentName == "其他" {
		return "", fmt.Errorf("其他文件夹下不允许新增文件夹")
	}
	if req.DirType == "" {
		return "", fmt.Errorf("文件夹类型不能为空")
	}
	var id string
	var err error
	err = checkDirName(req.Name, req.ID, req.DirType, req.ParentID, req.ProjectID)
	if err != nil {
		logger.Error.Printf("文件夹名称重名检测失败:%s", err.Error())
		return id, err
	}
	var target bool
	if req.DirType != common.StandardDirModule && req.DirType != common.CodeTableDir {
		target, err = specialDir(req)
		if err != nil {
			logger.Error.Printf("文件夹特殊交互处理失败:%s", err.Error())
			return id, err
		}
	}
	// 设定编号
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"parentid": req.ParentID,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("查询上层文件夹信息失败:%s, %s", req.ParentName, err.Error())
		return id, err
	}
	sources := esutil.GetSource(bytes)
	var number int
	for _, s := range sources {
		n := gjson.Get(s, "number").Int()
		name := gjson.Get(s, "name").String()
		if name == "其他" {
			continue
		}
		if number < int(n) {
			number = int(n)
		}
	}
	req.Number = number + 1
	// 设定是否最底层
	req.Isfourth, err = isDirFourth(req.ParentID)
	if err != nil {
		logger.Error.Printf("新增时查询文件夹是否最底层失败:%s, %s", req.ParentName, err.Error())
		return id, err
	}
	req.Created = time.Now().Format(common.TimeFormat)
	req.Modified = req.Created
	// 判定新增or编辑
	if req.ID != "" {
		err = setDir(req)
		if err != nil {
			logger.Error.Printf("编辑文件夹出错:%s,文件夹信息:%v", err.Error(), req)
			return "", err
		}
		return req.ID, err
	}
	// 新增时重新设定order
	if req.DirType == common.CatalogModule && target {
		req.Order, err = CatOrder(req.ParentID)
		if err != nil {
			logger.Error.Printf("获取默认编号失败:%s", err.Error())
			return id, err
		}
	}
	id, err = esutil.AddSingleWithRefresh(common.DataBaseMetaData, common.TbMetaDir, "id", common.RefreshWait, req)
	if err != nil {
		logger.Error.Printf("新增文件夹失败:%s", err.Error())
		return id, err
	}
	return id, nil
}

// 编辑文件夹
func setDir(req source.MetaDir) error {
	var err error
	doc := gin.H{
		"name":       req.Name,
		"dirtype":    req.DirType,
		"parentid":   req.ParentID,
		"parentname": req.ParentName,
		"order":      req.Order,
		"isfourth":   req.Isfourth,
		"modified":   req.Modified,
	}
	err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaDir, common.RefreshWait, req.ID, doc)
	if err != nil {
		logger.Error.Printf("编辑文件夹失败:%s", err.Error())
		return err
	}
	// time.Sleep(time.Millisecond * 1000)
	return nil
}

// 是否第四层文件夹
func isDirFourth(parentid string) (bool, error) {
	// 确定上级文件夹是否为第三级文件夹
	var count int
	for parentid != "" {
		count++
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": parentid,
						},
					},
				},
			},
		}
		bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
		if err != nil {
			logger.Error.Printf("根据id查询文件夹信息失败:%s", err.Error())
			return false, err
		}
		catalog, err := esutil.GetSourceFirst(bytes)
		if err != nil {
			logger.Error.Printf("获取文件夹记录失败:%s", err.Error())
			return false, err
		}
		// 如果上级文件夹为最底层文件夹,则报错返回
		isfourth := gjson.Get(catalog, "isfourth").Bool()
		if isfourth {
			parentname := gjson.Get(catalog, "name").String()
			return false, fmt.Errorf("已经是最底层文件夹:%s,不允许再新增子文件夹", parentname)
		}
		parentid = gjson.Get(catalog, "parentid").String()
	}
	return count == 3, nil
}

// 文件夹特殊交互处理
func specialDir(req source.MetaDir) (bool, error) {
	// 特殊交互处理
	/*
			每层下的一级文件夹中，自动有个叫“其他”的文件夹，“其他”文件夹
		下不允许新增文件夹。 对于建表/标签/标准时什么文件夹都不选的表，自动归入
		“其他文件夹”.
			当本层下边本身已经是表了，添加同层文件夹的时候，在本层同
		步添加名为其他的文件夹，原来本层下边的表进入到其他的文件夹中。
		如果没有这种情况，则不需要自动加“其他”文件夹。
	*/
	// 1.判定上级文件夹下是否无子文件夹且有无表
	var target bool
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"parentid": req.ParentID,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	total, err := esutil.SearchCount(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("获取子文件夹总个数失败:%s", err.Error())
		return target, err
	}
	if total != 0 {
		// 已有子文件夹，直接返回
		return target, nil
	}
	// 根据文件夹类型判定编目下有无表/标签文件夹下有无标签/标准文件夹下有无标准
	total, err = fileCount(req.DirType, req.ParentID)
	if total == 0 {
		// 文件夹下无表，直接返回
		return target, nil
	}
	target = true
	// 新建其他文件夹
	var info source.MetaDir
	info.Name = "其他"
	info.DirType = req.DirType
	info.Number = 10000
	if req.DirType == common.CatalogModule {
		info.Order, err = CatOrder(req.ParentID)
		if err != nil {
			logger.Error.Printf("获取默认编号失败:%s", err.Error())
			return target, err
		}
		info.Order = info.Order[:len(info.Order)-2]
		info.Order = fmt.Sprintf("%sZZ", info.Order)
	}
	info.ParentID = req.ParentID
	info.ParentName = req.ParentName
	info.ProjectID = req.ProjectID
	info.Isfourth, err = isDirFourth(req.ParentID)
	if err != nil {
		logger.Error.Printf("处理特殊交互时查询文件夹是否最底层失败:%s, %s", req.ParentName, err.Error())
		return target, err
	}
	info.Created = time.Now().Format(common.TimeFormat)
	info.Modified = info.Created
	id, err := esutil.AddSingleWithRefresh(common.DataBaseMetaData, common.TbMetaDir, "id", common.RefreshWait, info)
	if err != nil {
		logger.Error.Printf("新增文件夹失败:%s", err.Error())
		return target, err
	}
	// 根据文件夹类型修正所属文件属性
	err = fixFileProperty(id, info.Name, req.ParentID, req.ParentName, req.DirType)
	if err != nil {
		logger.Error.Printf("根据文件夹类型修正所属文件属性失败:%s", err.Error())
	}
	return target, nil
}

// 文件数量
func fileCount(dirtype, parentid string) (int, error) {
	var body gin.H
	var total int
	var err error
	switch dirtype {
	case common.CatalogModule:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"catalogid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("获取文件夹下表个数失败:%s", err.Error())
			return total, err
		}
	case common.TagDirModule:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"dirid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTag, body)
		if err != nil {
			logger.Error.Printf("获取文件夹下标签个数失败:%s", err.Error())
			return total, err
		}
	case common.StandardDirModule:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"dirid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaStandard, body)
		if err != nil {
			logger.Error.Printf("获取文件夹下标准个数失败:%s", err.Error())
			return total, err
		}

	case common.CodeTableDir:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"dirid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaCodeTable, body)
		if err != nil {
			logger.Error.Printf("获取文件夹下代码表个数失败:%s", err.Error())
			return total, err
		}
	}
	return total, nil
}

// 根据文件夹类型修正所属文件属性
func fixFileProperty(id, name, parentid, parentname, dirtype string) error {
	var body gin.H
	switch dirtype {
	case common.CatalogModule:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"catalogid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("查询编目表失败:%s, %s", name, err.Error())
			return err
		}
		sources := esutil.GetSource(bytes)
		var catlist source.CataList
		catlist.ID = id
		catlist.Name = name
		catlist.ParentID = parentid
		catlist.ParentName = parentname
		newlist, err := TbCatList(catlist)
		if err != nil {
			logger.Error.Printf("获取表新文件夹列表失败:%s", err.Error())
			return err
		}
		for _, s := range sources {
			tbid := gjson.Get(s, "id").String()
			if tbid == "" {
				continue
			}
			doc := gin.H{
				"catalist":  newlist,
				"catalogid": id,
			}
			err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaTable, common.RefreshWait, tbid, doc)
			if err != nil {
				logger.Error.Printf("修改元数据表所属文件夹失败:%s", err.Error())
				return err
			}
		}
	case common.TagDirModule:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"dirid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTag, body)
		if err != nil {
			logger.Error.Printf("查询标签信息失败:%s, %s", name, err.Error())
			return err
		}
		sources := esutil.GetSource(bytes)
		for _, s := range sources {
			tagid := gjson.Get(s, "id").String()
			if tagid == "" {
				continue
			}
			doc := gin.H{
				"dirid":   id,
				"dirname": name,
			}
			err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaTag, common.RefreshWait, tagid, doc)
			if err != nil {
				logger.Error.Printf("修改元数据标签所属文件夹失败:%s", err.Error())
				return err
			}
		}
	case common.StandardDirModule:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"dirid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaStandard, body)
		if err != nil {
			logger.Error.Printf("查询标准信息失败:%s, %s", name, err.Error())
			return err
		}
		sources := esutil.GetSource(bytes)
		for _, s := range sources {
			stdid := gjson.Get(s, "id").String()
			if stdid == "" {
				continue
			}
			doc := gin.H{
				"dirid":   id,
				"dirname": name,
			}
			err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaStandard, common.RefreshWait, stdid, doc)
			if err != nil {
				logger.Error.Printf("修改元数据标准所属文件夹失败:%s", err.Error())
				return err
			}
		}
	case common.CodeTableDir:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"dirid": parentid,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaCodeTable, body)
		if err != nil {
			logger.Error.Printf("查询代码表信息失败:%s, %s", name, err.Error())
			return err
		}
		sources := esutil.GetSource(bytes)
		for _, s := range sources {
			stdid := gjson.Get(s, "id").String()
			if stdid == "" {
				continue
			}
			doc := gin.H{
				"dirid":   id,
				"dirname": name,
			}
			err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaCodeTable, common.RefreshWait, stdid, doc)
			if err != nil {
				logger.Error.Printf("修改元数据代码表所属文件夹失败:%s", err.Error())
				return err
			}
		}
	}
	return nil
}

// TbCatList 循环获取上级编目信息
func TbCatList(catalist source.CataList) ([]source.CataList, error) {
	ids := []source.CataList{}
	delid := map[string]string{catalist.ID: catalist.ID}
	for len(delid) != 0 {
		tempid := delid
		delid = map[string]string{}
		var info source.CataList
		for _, v := range tempid {
			body := gin.H{
				"query": gin.H{
					"bool": gin.H{
						"must": gin.H{
							"term": gin.H{
								"id": v,
							},
						},
					},
				},
				"size": common.UnLimitSize,
			}
			bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
			if err != nil {
				//logger.Error.Printf("根据parentid查询编目信息失败:%s", err.Error())
				return ids, err
			}
			sources := esutil.GetSource(bytes)
			for _, s := range sources {
				info.ID = gjson.Get(s, "id").String()
				info.Name = gjson.Get(s, "name").String()
				info.ParentID = gjson.Get(s, "parentid").String()
				info.ParentName = gjson.Get(s, "parentname").String()
				info.IsFourth = gjson.Get(s, "isfourth").Bool()
				info.IsLower, err = IsLowerDir(info.ID)
				if err != nil {
					//logger.Error.Printf("判定是否最底层编目失败:%s", err.Error())
					return ids, err
				}
				ids = append(ids, info)
				if info.ParentID != "" {
					delid[info.ParentID] = info.ParentID
				}
			}
		}
	}
	return ids, nil
}

// CatOrder 推荐编目编号
func CatOrder(parentid string) (string, error) {
	var order string
	// 获取将要新建编目编号
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"parentid": parentid,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("推荐编目编号查询上层编目信息失败:%s", err.Error())
		return order, err
	}
	sources := esutil.GetSource(bytes)
	var number int
	var anotherOrder string
	var target bool
	var index int
	for _, s := range sources {
		n := gjson.Get(s, "number").Int()
		name := gjson.Get(s, "name").String()
		if name == "其他" {
			anotherOrder = gjson.Get(s, "order").String()
			target = true
			continue
		}
		if number < int(n) {
			number = int(n)
		}
		index++
	}
	if number <= index {
		number = index + 1
	}
	if target {
		if number < 26 && strings.Contains(anotherOrder, "AA") {
			number += 1
		} else if number >= 26 && strings.Contains(anotherOrder, "AZ") {
			number += 1
		}
	}
	fIndex := (number+1)/26 + 1
	lIndex := (number + 1) % 26
	order = fmt.Sprintf("%s%s", common.OrderMap[fIndex], common.OrderMap[lIndex])
	var orderLen int
	for orderLen == len(sources) {
		for _, s := range sources {
			sord := gjson.Get(s, "order").String()
			orderLen++
			if sord == order {
				lIndex++
				if lIndex >= 26 {
					fIndex++
					lIndex = 1
				}
				order = fmt.Sprintf("%s%s", common.OrderMap[fIndex], common.OrderMap[lIndex])
				break
			}
		}
		if orderLen == len(sources) {
			break
		}
	}

	for parentid != "" {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": parentid,
						},
					},
				},
			},
		}
		bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
		if err != nil {
			logger.Error.Printf("推荐编目编号根据id查询编目信息失败:%s", err.Error())
			return order, err
		}
		catalog, err := esutil.GetSourceFirst(bytes)
		if err != nil {
			logger.Error.Printf("推荐编目编号获取编目记录失败:%s", err.Error())
			return order, err
		}
		parentid = gjson.Get(catalog, "parentid").String()
		if parentid == "" {
			shortname := gjson.Get(catalog, "order").String()
			order = fmt.Sprintf("%s%s", shortname, order)
		} else {
			number = int(gjson.Get(catalog, "number").Int())
			fIndex = number/26 + 1
			lIndex = number % 26
			parOrder := fmt.Sprintf("%s%s", common.OrderMap[fIndex], common.OrderMap[lIndex])
			order = fmt.Sprintf("%s%s", parOrder, order)
		}
	}
	return order, nil
}

// ListOrder 列举编目编号
func ListOrder(projectid string) ([]string, error) {
	list := []string{}
	var musts []interface{}
	term := gin.H{
		"term": gin.H{
			"projectid": projectid,
		},
	}
	musts = append(musts, term)
	term = gin.H{
		"term": gin.H{
			"dirtype": common.CatalogModule,
		},
	}
	musts = append(musts, term)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("列举编目信息失败:%s", err.Error())
		return list, err
	}
	catstr := esutil.GetSource(bytes)
	for _, v := range catstr {
		list = append(list, gjson.Get(v, "order").String())
	}
	return list, nil
}

// ListLevelDir 列举上级文件夹下所有下一级级文件夹
func ListLevelDir(parentid, dirtype, projectid string) ([]string, error) {
	list := []string{}
	var musts []interface{}
	term := gin.H{
		"term": gin.H{
			"projectid": projectid,
		},
	}
	musts = append(musts, term)
	term = gin.H{
		"term": gin.H{
			"dirtype": dirtype,
		},
	}
	musts = append(musts, term)
	term = gin.H{
		"term": gin.H{
			"parentid": parentid,
		},
	}
	musts = append(musts, term)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("列举文件夹信息失败:%s", err.Error())
		return list, err
	}
	catstr := esutil.GetSource(bytes)
	for _, v := range catstr {
		list = append(list, gjson.Get(v, "name").String())
	}
	return list, nil
}

// ListDir 列举文件夹
func ListDir(sorttype int, projectid, dirtype string) ([]source.MetaDir, error) {
	/********新增start********/
	sort := []gin.H{}
	var run = gin.H{
		"number": gin.H{
			"order": "asc",
		},
	}
	if sorttype == 1 {
		run = gin.H{
			"name": gin.H{
				"order": "asc",
			},
		}
	}
	sort = append(sort, run)
	/**********新增end**********/
	list := []source.MetaDir{}
	if dirtype == "" {
		return list, fmt.Errorf("文件夹类型不能为空")
	}
	var musts []interface{}
	term := gin.H{
		"term": gin.H{
			"projectid": projectid,
		},
	}
	musts = append(musts, term)
	term = gin.H{
		"term": gin.H{
			"dirtype": dirtype,
		},
	}
	//【注】若后续要在此接口添加条件查询，需关注下面的服务层标签目录是否会重复初始化
	musts = append(musts, term)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnLimitSize,
		"sort": sort,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("列举文件夹失败:%s", err.Error())
		return list, err
	}
	var cathit source.MetaDirHits
	err = json.Unmarshal(bytes, &cathit)
	if err != nil {
		logger.Error.Printf("解析文件夹数据失败:%s", err.Error())
		return list, err
	}
	for _, v := range cathit.Hits1.Hits2 {
		list = append(list, v.MetaDirSource)
	}
	return list, nil
}

// 获取文件夹名
func GetMetaDirByID(id string) (source.MetaDir, error) {
	metaDir := source.MetaDir{}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": id,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("查询文件夹名失败:%s", err.Error())
		return metaDir, err
	}

	metaDirString, err := esutil.GetSourceFirst(bytes)
	if err != nil {
		logger.Error.Printf("查询文件夹名失败:%s", err.Error())
		return metaDir, err
	}
	_ = json.Unmarshal([]byte(metaDirString), &metaDir)
	return metaDir, nil
}

// 获取文件夹名
func GetTagByID(id string) (source.MetaTag, error) {
	metaTag := source.MetaTag{}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": id,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTag, body)
	if err != nil {
		logger.Error.Printf("查询标签信息失败:%s", err.Error())
		return metaTag, err
	}

	metaTagString, err := esutil.GetSourceFirst(bytes)
	if err != nil {
		logger.Error.Printf("解析标签信息失败:%s", err.Error())
		return metaTag, err
	}
	_ = json.Unmarshal([]byte(metaTagString), &metaTag)
	return metaTag, nil
}

// ListDirWithCount 列举编目-包含表数量统计
func ListDirWithCount(sorttype int, projectid, dirtype string, auth httpclient.Result) ([]source.MetaDir, error) {
	list, err := ListDir(sorttype, projectid, dirtype)
	if err != nil {
		logger.Error.Printf("获取编目信息失败:%s", err.Error())
		return list, err
	}
	// 修正编目下的表数量/标签文件夹下标签数量/标准文件夹下标准数量
	list, err = dirTbCount(list, auth)
	if err != nil {
		logger.Error.Printf("获取编目表数量失败:%s", err.Error())
		return list, err
	}
	return list, nil
}

// 修正编目下的表数量/标签文件夹下标签数量/标准文件夹下标准数量
func dirTbCount(list []source.MetaDir, auth httpclient.Result) ([]source.MetaDir, error) {
	if len(list) == 0 {
		// list为空则直接返回
		return list, nil
	}
	var err error
	countMap := make(map[string]int)
	ericha := make(chan error)
	//timeout := make(chan bool)
	end := make(chan bool)
	var iterator int
	var mutux sync.RWMutex
	chLimit := make(chan struct{}, 10)
	for _, v := range list {
		chLimit <- struct{}{}
		go func(dirid, dirtype string) {
			var total int
			var body gin.H
			notTerms := []string{v.ProjectID + "_wt", v.ProjectID + "_basedb"}
			//ids, err := MetaDirID(dirid)
			ids, err := MetaDirID2(dirid, list)
			if err != nil {
				logger.Error.Printf("列举表时获取子编目id失败:%s", err.Error())
				ericha <- err
				<-chLimit
				return
			}
			switch dirtype {
			case common.CatalogModule:
				var terms []interface{}
				var term gin.H
				term = gin.H{
					"term": gin.H{
						"projectid": v.ProjectID,
					},
				}
				terms = append(terms, term)
				term = gin.H{
					"terms": gin.H{
						"catalogid": ids,
					},
				}
				terms = append(terms, term)
				if !auth.AllAuth { //数据权限过滤
					body = gin.H{
						"query": gin.H{
							"bool": gin.H{
								"must": terms,
								"must_not": gin.H{
									"terms": gin.H{
										"sourcedb": notTerms,
									},
								},
								"should":               auth.Should,
								"minimum_should_match": 1,
							},
						},
						"size": common.UnLimitSize,
					}
				} else {
					body = gin.H{
						"query": gin.H{
							"bool": gin.H{
								"must": terms,
								"must_not": gin.H{
									"terms": gin.H{
										"sourcedb": notTerms,
									},
								},
							},
						},
						"size": common.UnLimitSize,
					}
				}

				str1, _ := json.Marshal(body)
				logger.Info.Println("---catalog:", string(str1))
				total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTable, body)
				if err != nil {
					logger.Error.Printf("编目表数量处理获取编目下元数据表总个数失败:%s", err.Error())
					ericha <- err
					<-chLimit
					return
				}
			case common.TagDirModule:
				body = gin.H{
					"query": gin.H{
						"bool": gin.H{
							"must": gin.H{
								"terms": gin.H{
									"dirid": ids,
								},
							},
						},
					},
					"size": common.UnLimitSize,
				}
				total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTag, body)
				if err != nil {
					logger.Error.Printf("文件夹标签数量处理获取文件夹下标签总个数失败:%s", err.Error())
					ericha <- err
					<-chLimit
					return
				}
			case common.StandardDirModule:
				body = gin.H{
					"query": gin.H{
						"bool": gin.H{
							"must": gin.H{
								"terms": gin.H{
									"dirid": ids,
								},
							},
						},
					},
					"size": common.UnLimitSize,
				}
				total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaStandard, body)
				if err != nil {
					logger.Error.Printf("文件夹标准数量处理获取文件夹下标准总个数失败:%s", err.Error())
					ericha <- err
					<-chLimit
					return
				}
			case common.CodeTableDir:
				body = gin.H{
					"query": gin.H{
						"bool": gin.H{
							"must": gin.H{
								"terms": gin.H{
									"dirid": ids,
								},
							},
						},
					},
					"size": common.UnLimitSize,
				}
				total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaCodeTable, body)
				if err != nil {
					logger.Error.Printf("文件夹代码表数量处理获取文件夹下标准总个数失败:%s", err.Error())
					ericha <- err
					<-chLimit
					return
				}
			}
			mutux.Lock()
			countMap[dirid] = total
			iterator++
			if iterator == len(list) {
				end <- true
			}
			<-chLimit
			mutux.Unlock()
		}(v.ID, v.DirType)
	}
	//go func() {
	//	time.Sleep(10 * time.Second)
	//	timeout <- true
	//}()

	select {
	//case <-timeout:
	//	return list, fmt.Errorf("修正编目表数量超时")
	case <-end:
		for i := range list {
			// 判定文件夹是否最底层
			list[i].IsLower, err = IsLowerDir(list[i].ID)
			if err != nil {
				logger.Error.Printf("判定文件夹是否最底层失败:%s", err.Error())
				return list, err
			}
			if total, ok := countMap[list[i].ID]; ok {
				list[i].Count = total
			}
		}
		return list, nil
	case err := <-ericha:
		if err != nil {
			return list, err
		}
	}
	return list, nil
}

// IsLowerDir 判定是否最底层文件夹
func IsLowerDir(id string) (bool, error) {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"parentid": id,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	total, err := esutil.SearchCount(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("获取文件夹下子文件夹总个数失败:%s", err.Error())
		return false, err
	}
	if total == 0 {
		return true, nil
	}
	return false, nil
}

// DirLevel 判定是第几级文件夹
func DirLevel(id string) ([]string, error) {
	ids := []string{id}
	delid := map[string]string{id: id}
	for len(delid) != 0 {
		tempid := delid
		delid = map[string]string{}
		for _, v := range tempid {
			bytes, err := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaDir, v)
			if err != nil {
				logger.Error.Printf("根据prentid查询文件夹信息失败:%s", err.Error())
				return ids, err
			}
			dirid := gjson.Get(bytes, "parentid").String()
			//防止出现id为空的脏数据导致死循环
			if dirid == "" {
				break
			}
			delid[dirid] = dirid
			ids = append(ids, dirid)
		}
	}
	return ids, nil
}

// DelDir 删除文件夹
func DelDir(id, dirtype string) error {
	if dirtype == "" {
		return fmt.Errorf("文件夹类型不能为空")
	}
	// 当文件夹下边有文件时不允许删除。提示“含子文件，无法删除”
	var body gin.H
	var total int
	var err error
	ids := []string{}
	ids, err = MetaDirID(id)
	if err != nil {
		return fmt.Errorf("循环获取子文件夹id失败:%s", err.Error())
	}
	switch dirtype {
	case common.CatalogModule:
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"catalist.id": id,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("获取编目下元数据表总个数失败:%s", err.Error())
			return err
		}
		if total != 0 {
			// 编目下有表
			return fmt.Errorf("当前编目下存在表,不允许删除")
		}
	case common.TagDirModule:
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"terms": gin.H{
							"dirid": ids,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err := esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTag, body)
		if err != nil {
			logger.Error.Printf("获取元数据标签总个数失败:%s", err.Error())
			return err
		}
		if total != 0 {
			return fmt.Errorf("目录下有标签,不允许删除")
		}
	case common.StandardDirModule:
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"terms": gin.H{
							"dirid": ids,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err := esutil.SearchCount(common.DataBaseMetaData, common.TbMetaStandard, body)
		if err != nil {
			logger.Error.Printf("获取元数据标准总个数失败:%s", err.Error())
			return err
		}
		if total != 0 {
			return fmt.Errorf("目录下有标准,不允许删除")
		}
	case common.CodeTableDir:
		//todo 判断代码表个数
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"terms": gin.H{
							"dirid": ids,
						},
					},
				},
			},
			"size": common.UnLimitSize,
		}
		total, err := esutil.SearchCount(common.DataBaseMetaData, common.TbMetaCodeTable, body)
		if err != nil {
			logger.Error.Printf("获取元数据代码表总个数失败:%s", err.Error())
			return err
		}
		if total != 0 {
			return fmt.Errorf("目录下有代码表,不允许删除")
		}
	}

	// 遍历删除
	for _, v := range ids {
		err := esutil.DelByIDWithRefresh(common.DataBaseMetaData, common.TbMetaDir, common.RefreshWait, v)
		if err != nil {
			logger.Error.Printf("删除文件夹失败:%s", err.Error())
			return err
		}
	}
	return nil
}

// MoveDir 移动文件夹
func MoveDir(req source.MoveInfo) error {
	// 支持文件夹在本层之间的上移和下移
	if req.LayerID == "" || req.TargetID == "" {
		logger.Error.Printf("移动文件夹失败,分层id为空")
		return fmt.Errorf("移动文件夹失败,分层id为空")
	}
	doc := gin.H{
		"number": req.TargetNumber,
	}
	err := esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaDir, common.RefreshWait, req.LayerID, doc)
	if err != nil {
		logger.Error.Printf("移动文件夹失败:%s", err.Error())
		return err
	}
	doc = gin.H{
		"number": req.LayerNumber,
	}
	err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaDir, common.RefreshWait, req.TargetID, doc)
	if err != nil {
		logger.Error.Printf("移动文件夹失败:%s", err.Error())
		return err
	}
	return nil
}

// MetaDirID 循环获取所有子文件夹id
func MetaDirID(id string) ([]string, error) {
	ids := []string{id}
	delid := map[string]string{id: id}
	for len(delid) != 0 {
		tempid := delid
		delid = map[string]string{}
		for _, v := range tempid {
			body := gin.H{
				"query": gin.H{
					"bool": gin.H{
						"must": gin.H{
							"term": gin.H{
								"parentid": v,
							},
						},
					},
				},
				"size": common.UnLimitSize,
			}
			bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
			if err != nil {
				logger.Error.Printf("根据prentid查询文件夹信息失败:%s", err.Error())
				return ids, err
			}
			sources := esutil.GetSource(bytes)
			for _, s := range sources {
				dirid := gjson.Get(s, "id").String()
				//防止出现id为空的脏数据导致死循环
				if dirid == "" {
					logger.Error.Printf("出现脏数据catalogid为空，parentid为：%s", v)
					continue
				}
				delid[dirid] = dirid
				ids = append(ids, dirid)
			}
		}
	}
	return ids, nil
}

func MetaDirID2(id string, list []source.MetaDir) ([]string, error) {
	ids := []string{id}
	delid := map[string]string{id: id}
	for len(delid) != 0 {
		tempid := delid
		delid = map[string]string{}
		for _, v := range tempid {
			for _, s := range list {
				if s.ParentID == v {
					//防止出现id为空的脏数据导致死循环
					if s.ID == "" {
						logger.Error.Printf("出现脏数据catalogid为空，parentid为：%s", v)
						continue
					}
					delid[s.ID] = s.ID
					ids = append(ids, s.ID)
				}
			}
		}
	}
	return ids, nil
}

// AddTag 添加/编辑标签记录
func AddTag(req source.MetaTag) (string, error) {
	var id string
	var err error
	err = CheckName(req.Name, req.ID, req.DirID, common.TagModule)
	if err != nil {
		logger.Error.Printf("标签名称重名检测失败:%s", err.Error())
		return id, err
	}
	// 设定标签编目/属性
	req.Property, err = tagProperty(req.DirID)
	if err != nil {
		logger.Error.Printf("设定标签编目失败:%s", err.Error())
		return id, err
	}
	req.Created = time.Now().Format(common.TimeFormat)
	req.Modified = req.Created
	if req.ID != "" {
		err = setTag(req)
		if err != nil {
			logger.Error.Printf("编辑标签出错:%s,标签信息:%v", err.Error(), req)
			return "", err
		}
		return req.ID, err
	}
	// 新建时确定标签颜色
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"dirid": req.DirID,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTag, body)
	if err != nil {
		logger.Error.Printf("查询文件夹下标签信息失败:%s", err.Error())
		return id, err
	}
	sources := esutil.GetSource(bytes)
	var number int
	for _, s := range sources {
		n := gjson.Get(s, "number").Int()
		if number < int(n) {
			number = int(n)
		}
	}
	req.Number = number + 1
	index := req.Number % 10
	if index < 0 || index >= 10 {
		return "", fmt.Errorf("标签编号超出限制:%d", req.Number)
	}
	req.Color = common.TagColor[index]
	// 获取topid
	req.TopID, _ = topDirID(req.DirID)
	id, err = esutil.AddSingleWithRefresh(common.DataBaseMetaData, common.TbMetaTag, "id", common.RefreshWait, req)
	if err != nil {
		logger.Error.Printf("新增标签文件夹失败:%s", err.Error())
		return id, err
	}
	return id, nil
}

// 编辑标签
func setTag(req source.MetaTag) error {
	var err error
	err = _tag_sync(req)
	if err != nil {
		logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
		return err
	}
	doc := gin.H{
		"name":     req.Name,
		"dirid":    req.DirID,
		"property": req.Property,
		"modified": req.Modified,
	}
	err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaTag, common.RefreshWait, req.ID, doc)
	if err != nil {
		logger.Error.Printf("编辑标签失败:%s", err.Error())
		return err
	}
	return nil
}
func _tag_sync(req source.MetaTag) error {
	taginfo, err := GetMetaTagByID(req.ID)
	if err != nil {
		logger.Error.Printf("查询标签失败:%s", err.Error())
		return err
	}

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"taglist.id": req.ID,
					},
				},
			},
		},
		"script": gin.H{
			"lang":   "painless",
			"inline": "for (int i = 0; i < ctx._source.taglist.length; i++) { if(ctx._source.taglist[i].id==params.tagid){ ctx._source.taglist[i].dirid= params.dirid ;ctx._source.taglist[i].name= params.newtagname; }}",
			"params": gin.H{
				"dirid":      req.DirID,
				"olddirid":   taginfo.DirID,
				"newtagname": req.Name,
				"tagid":      req.ID,
			},
		},
	}
	logger.Info.Println("元数据表批量更新语句", body)
	err = esutil.UpdByQuery(common.DataBaseMetaData, common.TbMetaTable, body)
	if err != nil {
		logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
		return err
	}
	//----------
	//采集更新语句
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"tags.id": req.ID,
					},
				},
			},
		},
		"script": gin.H{
			"lang":   "painless",
			"inline": "for (int i = 0; i < ctx._source.tags.length; i++) { if(ctx._source.tags[i].id==params.tagid){ ctx._source.tags[i].dirid= params.dirid ;ctx._source.tags[i].name= params.newtagname; }}",
			"params": gin.H{
				"dirid":      req.DirID,
				"olddirid":   taginfo.DirID,
				"newtagname": req.Name,
				"tagid":      req.ID,
			},
		},
	}
	logger.Info.Println("采集表批量更新语句", body)
	err = esutil.UpdByQuery(common.DataBaseLab, common.TbAccess, body)
	if err != nil {
		logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
		return err
	}
	//服务管理更新语句
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"tags.id": req.ID,
					},
				},
			},
		},
		"script": gin.H{
			"lang":   "painless",
			"inline": "for (int i = 0; i < ctx._source.tags.length; i++) { if(ctx._source.tags[i].id==params.tagid){ ctx._source.tags[i].dirid= params.dirid ;ctx._source.tags[i].name= params.newtagname; }}",
			"params": gin.H{
				"dirid":      req.DirID,
				"olddirid":   taginfo.DirID,
				"newtagname": req.Name,
				"tagid":      req.ID,
			},
		},
	}
	logger.Info.Println("服务管理根据标签变动批量更新语句", body)
	err = esutil.UpdByQuery(common.DataBaseAsset, common.ServiceESTB, body)
	if err != nil {
		logger.Error.Printf("服务管理批量更新标签结果失败:%s", err.Error())
		return err
	}

	switch taginfo.DirName {
	case "来源系统":
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"taglist.id": req.ID,
						},
					},
				},
			},
			"script": gin.H{
				"lang":   "painless",
				"inline": "ctx._source.datasourcesystem = params.new_datasourcesystem",
				"params": gin.H{
					"new_datasourcesystem": req.Name,
				},
			},
		}
		logger.Info.Println("元数据表批量更新语句", body)
		err = esutil.UpdByQuery(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
			return err
		}
	case "来源部门":
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"taglist.id": req.ID,
						},
					},
				},
			},
			"script": gin.H{
				"lang":   "painless",
				"inline": "ctx._source.dataprovider = params.new_dataprovider",
				"params": gin.H{
					"new_dataprovider": req.Name,
				},
			},
		}
		logger.Info.Println("元数据表批量更新语句", body)
		err = esutil.UpdByQuery(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
			return err
		}
	case "业务领域":
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"taglist.id": req.ID,
						},
					},
				},
			},
			"script": gin.H{
				"lang":   "painless",
				"inline": "ctx._source.dataarea = params.new_dataarea",
				"params": gin.H{
					"new_dataarea": req.Name,
				},
			},
		}
		logger.Info.Println("元数据表批量更新语句", body)
		err = esutil.UpdByQuery(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
			return err
		}
	}
	return nil
}
func _tag_remove(tagid string) error {
	//元数据
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"taglist.id": tagid,
					},
				},
			},
		},
		"script": gin.H{
			"lang":   "painless",
			"inline": "for (int i = 0; i < ctx._source.taglist.length; i++) { if(ctx._source.taglist[i].id==params.oldtagid){ctx._source.taglist.remove(i)  }  }",
			"params": gin.H{
				"oldtagid": tagid,
			},
		},
	}
	err := esutil.UpdByQuery(common.DataBaseMetaData, common.TbMetaTable, body)
	logger.Info.Println("元数据表批量更新语句", body)
	if err != nil {
		logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
		return err
	}
	//------------------------------------
	//采集
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"tags.id": tagid,
					},
				},
			},
		},
		"script": gin.H{
			"lang":   "painless",
			"inline": "for (int i = 0; i < ctx._source.tags.length; i++) { if(ctx._source.tags[i].id==params.oldtagid){ctx._source.tags.remove(i)  }  }",
			"params": gin.H{
				"oldtagid": tagid,
			},
		},
	}
	logger.Info.Println("采集批量更新语句", body)
	err = esutil.UpdByQuery(common.DataBaseLab, common.TbAccess, body)
	if err != nil {
		logger.Error.Printf("批量更新标签结果失败:%s", err.Error())
		return err
	}
	//服务管理
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"tags.id": tagid,
					},
				},
			},
		},
		"script": gin.H{
			"lang":   "painless",
			"inline": "for (int i = 0; i < ctx._source.tags.length; i++) { if(ctx._source.tags[i].id==params.oldtagid){ctx._source.tags.remove(i)  }  }",
			"params": gin.H{
				"oldtagid": tagid,
			},
		},
	}
	logger.Info.Println("服务管理根据标签变动批量更新语句", body)
	err = esutil.UpdByQuery(common.DataBaseAsset, common.ServiceESTB, body)
	if err != nil {
		logger.Error.Printf("服务管理批量更新标签结果失败:%s", err.Error())
		return err
	}
	return nil
}
func GetMetaTagByID(id string) (source.MetaTag, error) {
	metatag := source.MetaTag{}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": id,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTag, body)
	if err != nil {
		logger.Error.Printf("查询tag失败:%s", err.Error())
		return metatag, err
	}
	metaDirString, err := esutil.GetSourceFirst(bytes)
	if err != nil {
		logger.Error.Printf("查询tag失败:%s", err.Error())
		return metatag, err
	}
	_ = json.Unmarshal([]byte(metaDirString), &metatag)
	logger.Info.Printf("查询tag结果为:%v", metatag)
	return metatag, nil
}

// 设定标签编目/属性
func tagProperty(dirid string) (string, error) {
	var property string
	var body gin.H
	var err error
	var target bool
	for dirid != "" {
		body = gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": dirid,
						},
					},
				},
			},
		}
		bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
		if err != nil {
			logger.Error.Printf("设定标签编目根据id查询文件夹信息失败:%s", err.Error())
			return property, err
		}
		tagdir, err := esutil.GetSourceFirst(bytes)
		if err != nil {
			logger.Error.Printf("设定标签编目获取文件夹记录失败:%s", err.Error())
			return property, err
		}
		dirid = gjson.Get(tagdir, "parentid").String()
		name := gjson.Get(tagdir, "name").String()
		if !target {
			property = name
			target = true
		} else {
			property = fmt.Sprintf("%s/%s", name, property)
		}
	}
	return property, err
}

// MoveTag 移动标签
func MoveTag(req []source.MoveTagReq) error {
	var doc gin.H
	var property string
	var err error
	for _, v := range req {
		// 重新设定标签类目
		property, err = tagProperty(v.DirID)
		if err != nil {
			logger.Error.Printf("移动标签时设定标签编目失败:%s", err.Error())
			return err
		}
		// 判定是否跨分类移动
		bytes, _ := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaDir, v.TagID)
		hisid := gjson.Get(bytes, "dirid").String()
		count := gjson.Get(bytes, "citations").Int()
		if hisid != v.DirID {
			histopid, _ := topDirID(hisid)
			newtopid, _ := topDirID(v.DirID)
			if histopid != newtopid {
				count = 0
			}
		}
		doc = gin.H{
			"dirid":     v.DirID,
			"dirname":   v.DirName,
			"citations": count,
			"property":  property,
		}
		err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.TbMetaTag, common.RefreshWait, v.TagID, doc)
		if err != nil {
			logger.Error.Printf("移动标签失败:%s", err.Error())
			return err
		}
	}
	return nil
}

// DelTag 删除标签记录
func DelTag(ids []string) (int, error) {
	for k, id := range ids {
		// 将当前标签从标签池中删除，并且所有引用
		// 此标签的表或任务等下方的此标签对应删除
		err := esutil.DelByIDWithRefresh(common.DataBaseMetaData, common.TbMetaTag, common.RefreshWait, id)
		if err != nil {
			logger.Error.Printf("删除标签记录失败:%s", err.Error())
			return k, err
		}
		// 删除表/任务等下方的此标签
		err = _tag_remove(id)
		if err != nil {
			logger.Error.Printf("更新标签记录失败:%s", err.Error())
			return k, err
		}
	}
	return len(ids) - 1, nil
}

// ListTag 列举标签
func ListTag(req source.MetaTagReq) ([]source.MetaTag, int, error) {
	list := []source.MetaTag{}
	var musts []interface{}
	var term gin.H
	var sort gin.H
	term = gin.H{
		"term": gin.H{
			"projectid": req.ProjectID,
		},
	}
	musts = append(musts, term)
	switch req.SortContent.Name {
	case common.TagSortName:
		if req.SortContent.Number == 1 {
			sort = gin.H{
				"name": gin.H{
					"order": "desc",
				},
			}
		} else {
			sort = gin.H{
				"name": gin.H{
					"order": "asc",
				},
			}
		}
	case common.TagSortProperty:
		if req.SortContent.Number == 1 {
			sort = gin.H{
				"property": gin.H{
					"order": "desc",
				},
			}
		} else {
			sort = gin.H{
				"property": gin.H{
					"order": "asc",
				},
			}
		}
	case common.TagSortCitations:
		if req.SortContent.Number == 1 {
			sort = gin.H{
				"citations": gin.H{
					"order": "desc",
				},
			}
		} else {
			sort = gin.H{
				"citations": gin.H{
					"order": "asc",
				},
			}
		}
	case common.TagSortTime:
		if req.SortContent.Number == 1 {
			sort = gin.H{
				"created": gin.H{
					"order": "desc",
				},
			}
		} else {
			sort = gin.H{
				"created": gin.H{
					"order": "asc",
				},
			}
		}
	default:
		sort = gin.H{
			"created": gin.H{
				"order": "desc",
			},
		}
	}
	for _, v := range req.SearchContent {
		switch v.Name {
		case common.TagSearchName:
			v.Value = tools.SpecialCharacterAdaptation(v.Value)
			fmt.Println("v.Value:", v.Value)
			term = gin.H{
				"query_string": gin.H{
					"query":            "*" + v.Value + "*",
					"fields":           []string{"name"},
					"default_operator": "or",
				},
			}
		case common.TagSearchDirID:
			// 循环获取所有子文件夹id
			ids, err := MetaDirID(v.Value)
			if err != nil {
				logger.Error.Printf("列举标签时获取标签子文件夹id失败:%s", err.Error())
				return list, 0, err
			}
			term = gin.H{
				"terms": gin.H{
					"dirid": ids,
				},
			}
		}
		musts = append(musts, term)
	}
	if req.Page == 0 {
		req.Page = 1
		req.Rows = common.UnLimitSize
	}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": req.Rows,
		"from": (req.Page - 1) * req.Rows,
		"sort": sort,
	}
	fmt.Printf("搜索body%#v\n", body)
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTag, body)
	if err != nil {
		logger.Error.Printf("列举标签失败:%s", err.Error())
		return list, 0, err
	}
	var layhit source.MetaTagHits
	err = json.Unmarshal(bytes, &layhit)
	if err != nil {
		logger.Error.Printf("解析标签数据失败:%s", err.Error())
		return list, 0, err
	}
	var tagList []string
	for _, v := range layhit.Hits1.Hits2 {
		if v.MetaTagSource.TopID == "" {
			v.MetaTagSource.TopID, _ = topDirID(v.MetaTagSource.DirID)
		}
		tagList = append(tagList, v.MetaTagSource.ID)
		list = append(list, v.MetaTagSource)
	}

	if req.Isrealmessage {
		var tbhit source.RealMessageHits1
		if len(tagList) > 0 {
			body2 := gin.H{
				"query": gin.H{
					"terms": gin.H{
						"taglist.id": tagList,
					},
				},
				"size": common.UnLimitSize,
			}
			b, _ := json.Marshal(body2)
			fmt.Println("body2 ", string(b))
			bytes2, err := esutil.SearchByTerm(common.DataBaseLab, common.RealMessage, body2)
			if err != nil {
				logger.Error.Printf("列举存储表记录失败:%s", err.Error())
				return list, 0, err
			}
			fmt.Println("tbhit ", string(bytes2))
			err = json.Unmarshal(bytes2, &tbhit)
			if err != nil {
				logger.Error.Printf("解析表数据失败:%s", err.Error())
				return list, 0, err
			}
		}
		// 标签目录修正
		for i := 0; i < len(list); i++ {
			list[i].Property, err = tagProperty(list[i].DirID)
			if err != nil {
				logger.Error.Printf("列举标签时修正标签目录失败:%s", err.Error())
			}

			for _, t := range tbhit.Hits1.Hits2 {
				for _, l := range t.Realinfo.TagList {
					if l.ID == list[i].ID {
						list[i].TablesNum++
					}
				}
			}
			//list[i].TablesNum = list[i].Citations
		}
	} else {
		var tbhit source.MoneyMetaListTbHits
		if len(tagList) > 0 {
			body2 := gin.H{
				"query": gin.H{
					"terms": gin.H{
						"taglist.id": tagList,
					},
				},
				"size": common.UnLimitSize,
			}
			jsonbody2, _ := json.Marshal(body2)
			fmt.Println("tagListbody:", string(jsonbody2))
			bytes2, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body2)
			if err != nil {
				logger.Error.Printf("列举存储表记录失败:%s", err.Error())
				return list, 0, err
			}
			err = json.Unmarshal(bytes2, &tbhit)
			if err != nil {
				logger.Error.Printf("解析表数据失败:%s", err.Error())
				return list, 0, err
			}
		}
		// 标签目录修正
		taglistinfo := make(map[string]int)
		for _, t := range tbhit.Hits1.Hits2 {
			for _, l := range t.MoneyMetaListTbSource.Taglist {
				if _, ok := taglistinfo[l.ID]; ok {
					taglistinfo[l.ID]++
				} else {
					taglistinfo[l.ID] = 1
				}
			}
		}
		for i := 0; i < len(list); i++ {
			list[i].Property, err = tagProperty(list[i].DirID)
			if err != nil {
				logger.Error.Printf("列举标签时修正标签目录失败:%s", err.Error())
			}

			//for _, t := range tbhit.Hits1.Hits2 {
			//
			//	for _, l := range t.MoneyMetaListTbSource.Taglist {
			//		if l.ID == list[i].ID {
			//			list[i].TablesNum++
			//		}
			//	}
			//}
			//list[i].TablesNum = list[i].Citations
			if _, ok := taglistinfo[list[i].ID]; ok {
				list[i].TablesNum = taglistinfo[list[i].ID]
			}
		}
	}

	total := layhit.Hits1.Total
	return list, total, nil
}

// CheckName 检测分层名称、分层简称、标签文件夹、标签、编目名称是否重复
func CheckName(name, id, parentid, module string) error {
	var err error
	switch module {
	case common.LayerModule:
		err = checkLayerName(name, id, parentid)
	case common.SubLayerModule:
		err = checkShortLayerName(name, id, parentid)
	// case common.DirModule:
	// 	err = checkDirName(name, id, parentid)
	case common.TagModule:
		err = checkTagName(name, id, parentid)
	case common.StandardModule:
		err = checkStandardName(name, id, parentid)
	case common.TableModule:
		_, err = checkTableName(name, id, parentid)
	default:
	}
	return err
}

// 分层名称重名检测
func checkLayerName(name, id, projectid string) error {
	if len(name) > 128 {
		return errors.New("分层名称长度超出128个字符串")
	}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": projectid,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaLayer, body)
	if err != nil {
		logger.Error.Printf("分层名称重名检测查询项目分层信息失败:%s, %s", projectid, err.Error())
		return err
	}
	sources := esutil.GetSource(bytes)
	for _, s := range sources {
		layername := gjson.Get(s, "name").String()
		layerid := gjson.Get(s, "id").String()
		if layername == name && (id == "" || (id != "" && id != layerid)) {
			return fmt.Errorf("分层名称已存在:%s", name)
		}
	}
	return nil
}

// 分层简称重名检测
func checkShortLayerName(name, id, projectid string) error {
	if len(name) > 128 {
		return errors.New("分层简称长度超出128个字符串")
	}

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"projectid": projectid,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaLayer, body)
	if err != nil {
		logger.Error.Printf("分层简称重名检测查询项目分层信息失败:%s, %s", projectid, err.Error())
		return err
	}
	sources := esutil.GetSource(bytes)
	for _, s := range sources {
		shortname := gjson.Get(s, "shortname").String()
		layerid := gjson.Get(s, "id").String()
		if shortname == name && (id == "" || (id != "" && id != layerid)) {
			return fmt.Errorf("分层简称已存在:%s", name)
		}
	}
	return nil
}

// 文件夹重名检测
func checkDirName(name, id, dirtype, parentid, projectid string) error {
	var musts []interface{}
	var term gin.H
	term = gin.H{
		"term": gin.H{
			"projectid": projectid,
		},
	}
	musts = append(musts, term)
	term = gin.H{
		"term": gin.H{
			"parentid": parentid,
		},
	}
	musts = append(musts, term)
	term = gin.H{
		"term": gin.H{
			"dirtype": dirtype,
		},
	}
	musts = append(musts, term)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Printf("文件夹重名检测查询文件夹信息失败:%s", err.Error())
		return err
	}
	sources := esutil.GetSource(bytes)
	for _, s := range sources {
		dirname := gjson.Get(s, "name").String()
		dirid := gjson.Get(s, "id").String()
		if dirname == name && (id == "" || (id != "" && id != dirid)) {
			return fmt.Errorf("文件夹名称已存在:%s", name)
		}
	}
	return nil
}

// 标签重名检测
func checkTagName(name, id, parentid string) error {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"dirid": parentid,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTag, body)
	if err != nil {
		logger.Error.Printf("标签重名检测查询标签信息失败:%s", err.Error())
		return err
	}
	sources := esutil.GetSource(bytes)
	for _, s := range sources {
		tagname := gjson.Get(s, "name").String()
		tagid := gjson.Get(s, "id").String()
		if tagname == name && (id == "" || (id != "" && id != tagid)) {
			return fmt.Errorf("标签名称已存在:%s", name)
		}
	}
	return nil
}

// 标准重名检测
func checkStandardName(name, id, parentid string) error {
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"dirid": parentid,
					},
				},
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaStandard, body)
	if err != nil {
		logger.Error.Printf("标准重名检测查询标准信息失败:%s", err.Error())
		return err
	}
	sources := esutil.GetSource(bytes)
	for _, s := range sources {
		stdname := gjson.Get(s, "name").String()
		stdid := gjson.Get(s, "id").String()
		if stdname == name && (id == "" || (id != "" && id != stdid)) {
			return fmt.Errorf("标准名称已存在:%s", name)
		}
	}
	return nil
}

// 表重名检测 false--表重名, true--表不重名
func checkTableName(name, id, sourceid string) (bool, error) {
	var musts []interface{}
	var term gin.H
	term = gin.H{
		"term": gin.H{
			"sourceid": sourceid,
		},
	}
	musts = append(musts, term)
	term = gin.H{
		"term": gin.H{
			"tbname": name,
		},
	}
	musts = append(musts, term)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnLimitSize,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
	if err != nil {
		logger.Error.Printf("表重名检测查询表信息失败:%s", err.Error())
		return true, err
	}
	sources := esutil.GetSource(bytes)
	for _, s := range sources {
		tbname := gjson.Get(s, "tbname").String()
		tbid := gjson.Get(s, "id").String()
		if tbname == name && (id == "" || (id != "" && id != tbid)) {
			return false, fmt.Errorf("表名称已存在:%s", name)
		}
	}
	return true, nil
}

func DealCatalog(projectID string) (map[string]string, error) {
	var catalogMap = make(map[string]string)
	//查询meta_dir表中catalog模块的目录
	var terms []map[string]interface{}
	ModuleTerm := gin.H{
		"term": gin.H{
			"dirtype": "catalog",
		},
	}
	proTerm := gin.H{
		"term": gin.H{
			"projectid": projectID,
		},
	}
	terms = append(terms, ModuleTerm, proTerm)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 10000,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaDir, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	relList := esutil.GetSource(bytes)
	//将所有数据扁平塞进[]CatalogList
	var serviceList = make([]*dtauth.CatalogList, len(relList))
	for index, v := range relList {
		temp := new(dtauth.CatalogList)
		temp.ID = gjson.Get(v, "id").String()
		temp.Name = gjson.Get(v, "name").String()
		temp.ParentID = gjson.Get(v, "parentid").String()
		serviceList[index] = temp
		catalogMap[temp.ID] = gjson.Get(v, "name").String()
	}
	var head []*dtauth.CatalogList
	res := dtauth.HanderTree(head, serviceList)
	handleEmpty(res)
	dtauth.HandleCatalogName(res, catalogMap, "")
	return catalogMap, nil
}

func GetMIDMap(projectID string) (map[string]struct{}, error) {
	var tableidMap = make(map[string]struct{}, 0)
	//查询meta_dir表中catalog模块的目录
	var terms []map[string]interface{}
	proTerm := gin.H{
		"term": gin.H{
			"projectid": projectID,
		},
	}
	terms = append(terms, proTerm)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 10000,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseLab, common.TbMetrics, body)
	if err != nil {
		logger.Error.Println(err)
		return nil, err
	}
	relList := esutil.GetSource(bytes)
	for _, v := range relList {
		tid := gjson.Get(v, "sqlstore.tableid").String()
		tableidMap[tid] = struct{}{}
	}

	return tableidMap, nil
}

func GetMetricsLayerId(projectid string) (string, error) {

	var terms []map[string]interface{}
	proTerm := gin.H{
		"term": gin.H{
			"projectid": projectid,
		},
	}
	bTerm := gin.H{
		"term": gin.H{
			"ismetrics": true,
		},
	}

	terms = append(terms, proTerm, bTerm)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": 10000,
	}
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaLayer, body)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	relList, total := esutil.GetSourceAndTotal(bytes)
	if total == 0 {
		return "", nil
	}

	mid := gjson.Get(relList[0], "id").String()
	fmt.Println("查询到当前的指标层id是：", mid)
	return mid, nil

}

// 判断目录中包含的发布服务是否为空
func handleEmpty(t []*dtauth.CatalogList) bool {
	var notEmpty bool
	for i := range t {
		if t[i].ApiIdName != nil {
			notEmpty = true
			t[i].NotEmpty = true
		}
		if handleEmpty(t[i].Children) {
			notEmpty = true
			t[i].NotEmpty = true
		}
	}
	return notEmpty
}

// ListDirWithCount 列举编目-包含表数量统计
func QualityListDirWithCount(sorttype int, projectid, dirtype string, auth httpclient.Result) ([]source.MetaDir, error) {
	list, err := ListDir(sorttype, projectid, dirtype)
	if err != nil {
		logger.Error.Printf("获取编目信息失败:%s", err.Error())
		return list, err
	}
	// 修正编目下的表数量/标签文件夹下标签数量/标准文件夹下标准数量
	list, err = QualitydirTbCount(list, auth)
	if err != nil {
		logger.Error.Printf("获取编目表数量失败:%s", err.Error())
		return list, err
	}
	return list, nil
}

// 修正编目下的表数量/标签文件夹下标签数量/标准文件夹下标准数量
func QualitydirTbCount(list []source.MetaDir, auth httpclient.Result) ([]source.MetaDir, error) {
	if len(list) == 0 {
		// list为空则直接返回
		return list, nil
	}
	var err error
	countMap := make(map[string]int)
	ericha := make(chan error)
	//timeout := make(chan bool)
	end := make(chan bool)
	var iterator int
	var mutux sync.RWMutex
	chLimit := make(chan struct{}, 10)
	for _, v := range list {
		chLimit <- struct{}{}
		go func(dirid, dirtype string) {
			var total int
			var body gin.H
			//ids, err := MetaDirID(dirid)
			ids, err := MetaDirID2(dirid, list)
			if err != nil {
				logger.Error.Printf("列举表时获取子编目id失败:%s", err.Error())
				ericha <- err
				<-chLimit
				return
			}
			switch dirtype {
			case common.CatalogModule:
				var terms []interface{}
				var term gin.H
				term = gin.H{
					"term": gin.H{
						"projectid": v.ProjectID,
					},
				}
				terms = append(terms, term)
				term = gin.H{
					"terms": gin.H{
						"sourcetablecatalogid": ids,
					},
				}
				terms = append(terms, term)
				//term = gin.H{
				//	"term": gin.H{
				//		"sourcedb": v.ProjectID + "_wt",
				//	},
				//}
				//terms = append(terms, term)
				if !auth.AllAuth { //数据权限过滤
					body = gin.H{
						"query": gin.H{
							"bool": gin.H{
								"must":                 terms,
								"should":               auth.Should,
								"minimum_should_match": 1,
							},
						},
						"size": common.UnLimitSize,
					}
				} else {
					body = gin.H{
						"query": gin.H{
							"bool": gin.H{
								"must": terms,
							},
						},
						"size": common.UnLimitSize,
					}
				}

				str1, _ := json.Marshal(body)
				logger.Info.Println("---catalog:", string(str1))
				//total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTable, body)
				total, err = esutil.SearchCount(common.DataBaseMetaData, common.QualityProblem, body)
				if err != nil {
					logger.Error.Printf("编目表数量处理获取编目下元数据表总个数失败:%s", err.Error())
					ericha <- err
					<-chLimit
					return
				}
			case common.TagDirModule:
				body = gin.H{
					"query": gin.H{
						"bool": gin.H{
							"must": gin.H{
								"terms": gin.H{
									"dirid": ids,
								},
							},
						},
					},
					"size": common.UnLimitSize,
				}
				total, err = esutil.SearchCount(common.DataBaseMetaData, common.TbMetaTag, body)
				if err != nil {
					logger.Error.Printf("文件夹标签数量处理获取文件夹下标签总个数失败:%s", err.Error())
					ericha <- err
					<-chLimit
					return
				}
			}
			mutux.Lock()
			countMap[dirid] = total
			iterator++
			if iterator == len(list) {
				end <- true
			}
			<-chLimit
			mutux.Unlock()
		}(v.ID, v.DirType)
	}

	select {
	//case <-timeout:
	//	return list, fmt.Errorf("修正编目表数量超时")
	case <-end:
		for i := range list {
			// 判定文件夹是否最底层
			list[i].IsLower, err = IsLowerDir(list[i].ID)
			if err != nil {
				logger.Error.Printf("判定文件夹是否最底层失败:%s", err.Error())
				return list, err
			}
			if total, ok := countMap[list[i].ID]; ok {
				list[i].Count = total
			}
		}
		return list, nil
	case err := <-ericha:
		if err != nil {
			return list, err
		}
	}
	return list, nil
}
func QualityListTag(req source.MetaTagReq) ([]source.MetaTag, int, error) {
	list := []source.MetaTag{}
	var musts []interface{}
	var term gin.H
	var sort gin.H
	term = gin.H{
		"term": gin.H{
			"projectid": req.ProjectID,
		},
	}
	musts = append(musts, term)
	sort = gin.H{
		"created": gin.H{
			"order": "desc",
		},
	}

	if req.Page == 0 {
		req.Page = 1
		req.Rows = common.UnLimitSize
	}
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": req.Rows,
		"from": (req.Page - 1) * req.Rows,
		"sort": sort,
	}
	fmt.Printf("搜索body%#v\n", body)
	bytes, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTag, body)
	if err != nil {
		logger.Error.Printf("列举标签失败:%s", err.Error())
		return list, 0, err
	}
	var layhit source.MetaTagHits
	err = json.Unmarshal(bytes, &layhit)
	if err != nil {
		logger.Error.Printf("解析标签数据失败:%s", err.Error())
		return list, 0, err
	}
	var tagList []string
	for _, v := range layhit.Hits1.Hits2 {
		if v.MetaTagSource.TopID == "" {
			v.MetaTagSource.TopID, _ = topDirID(v.MetaTagSource.DirID)
		}
		tagList = append(tagList, v.MetaTagSource.ID)
		list = append(list, v.MetaTagSource)
	}
	var tbhit source.MoneyMetaListTbHits
	//var tbhit2 source.MoneyMetaListTbHits
	if len(tagList) > 0 {
		body2 := gin.H{
			"query": gin.H{
				"terms": gin.H{
					"taglist.id": tagList,
				},
			},
			"size": common.UnLimitSize,
		}
		//该条件搜索出来的表是所有带标签的表，当前我们只需要统计所有带标签的问题表--具体设计待定
		bytes2, err := esutil.SearchByTerm(common.DataBaseMetaData, common.QualityProblem, body2)
		if err != nil {
			logger.Error.Printf("列举存储表记录失败:%s", err.Error())
			return list, 0, err
		}
		err = json.Unmarshal(bytes2, &tbhit)
		if err != nil {
			logger.Error.Printf("解析表数据失败:%s", err.Error())
			return list, 0, err
		}
	}
	// 标签目录修正
	for i := 0; i < len(list); i++ {
		list[i].Property, err = tagProperty(list[i].DirID)
		if err != nil {
			logger.Error.Printf("列举标签时修正标签目录失败:%s", err.Error())
		}

		for _, t := range tbhit.Hits1.Hits2 {
			for _, l := range t.MoneyMetaListTbSource.Taglist {
				//list列举的标签id与tb_metadatdb中的标签id对应，且该tb_metadatatb中存在schema_tbname记录
				if l.ID == list[i].ID {
					//根据project_表名_
					//var arr []string
					//var submusts []interface{}
					//tbname := t.MoneyMetaListTbSource.Schema + "_" + t.MoneyMetaListTbSource.TBName
					//cf_tbname := "cf_" + t.MoneyMetaListTbSource.Schema + "_" + t.MoneyMetaListTbSource.TBName
					//arr = append(arr, tbname, cf_tbname)
					//term1 := gin.H{
					//	"term": gin.H{
					//		"layerid": t.MoneyMetaListTbSource.Layerid,
					//	},
					//}
					//term2 := gin.H{
					//	"term": gin.H{
					//		"engineid": t.MoneyMetaListTbSource.Engineid,
					//	},
					//}
					//term3 := gin.H{
					//	"terms": gin.H{
					//		"tbname": arr,
					//	},
					//}
					//submusts = append(submusts, term1, term2, term3)
					//body3 := gin.H{
					//	"query": gin.H{
					//		"bool": gin.H{
					//			"must": submusts,
					//		},
					//	},
					//	"size": common.UnLimitSize,
					//}
					////该条件搜索出来的表是所有带标签的表，当前我们只需要统计所有带标签的问题表--具体设计待定
					//bytes3, err := esutil.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body3)
					//if err != nil {
					//	logger.Error.Printf("列举存储表记录失败:%s", err.Error())
					//	return list, 0, err
					//}
					////标签对应的表数量
					//err = json.Unmarshal(bytes3, &tbhit2)
					//if tbhit2.Hits1.Total != 0 {
					list[i].TablesNum++
					//}
				}
			}
		}
	}
	total := tbhit.Hits1.Total
	return list, total, nil
}

// 将数据库中的表信息导入xlsx文件中
// 批量导入:将所有问题表放在一个xlsx文件中，将所有重复数据表放到另一个xlsx文件中
// alldata存放的是同一种类型的所有表信息
//
//	func QualityExcel(alldata [][][]string, filenames []string, tableinfo []Mgm.Protables) error {
//		//设计:确保alldata保存的是所有表的信息
//		file := excelize.NewFile()
//
//		// 创建样式
//		style, err := file.NewStyle(&excelize.Style{
//			Alignment: &excelize.Alignment{
//				Horizontal: "left",   //水平对齐方式为左对齐
//				Vertical:   "center", //垂直对齐方式为居中对齐  ;两个对齐方式适用于合并单元格的情况
//			},
//		})
//		if err != nil {
//			fmt.Println("创建样式失败,err:", err.Error())
//			return err
//		}
//
//		for sheetIndex, data := range alldata {
//			fmt.Println("data:", data, len(alldata))
//			// 创建工作表
//			sheet1 := file.NewSheet(tableinfo[sheetIndex].Tablename)
//			// 将表头数据写入工作表的第一行
//			for i, header := range tableinfo[sheetIndex].Fields {
//				cell := indexToColumnName(i+1) + "1"
//				fmt.Println("test:", i, header, cell)
//				err = file.SetCellValue(tableinfo[sheetIndex].Tablename, cell, header)
//				if err != nil {
//					fmt.Println("写入表头失败err:", err.Error())
//					return err
//				}
//			}
//
//			if len(data) > 0 {
//				num := 2
//				for _, row := range data {
//					fmt.Println("data2:", row)
//					rownum := 0
//					for colIndex, value := range row {
//						cell := indexToColumnName(colIndex+1) + fmt.Sprintf("%d", num)
//						err = file.SetCellValue(tableinfo[sheetIndex].Tablename, cell, value)
//						if err != nil {
//							fmt.Println("写入值失败err:", err.Error())
//							return err
//						}
//						err = file.SetCellStyle(tableinfo[sheetIndex].Tablename, cell, cell, style)
//						if err != nil {
//							fmt.Println("写入表格类型err:", err.Error())
//							return err
//						}
//						rownum++
//					}
//					num++
//				}
//			}
//
//			// 设置默认活动工作表
//			file.SetActiveSheet(sheet1)
//		}
//
//		// 保存文件
//		for _, filename := range filenames {
//			if err := file.SaveAs(filename + ".xlsx"); err != nil {
//				fmt.Println("保存文件出错：", err.Error())
//				return err
//			}
//			fmt.Printf("xlsx 文件 %s 创建成功\n", filename)
//		}
//
//		return nil
//	}
func indexToColumnName(index int) string {
	columnName := ""
	for index > 0 {
		index--
		columnName = string(rune(index%26)+'A') + columnName
		index /= 26
	}
	return columnName
}

func p(i interface{}) string {
	b, _ := json.Marshal(i)
	return string(b)
}

// 质量报告生成逻辑中，需要生成excel问题表
func BatchQualityExportForReport(ids []string, department, month, reportPath string) (string, string, error) {
	department = strings.ReplaceAll(department, `/`, `_`)
	probleminfo, err := BatchCommon(ids, false, month)
	if err != nil {
		fmt.Println("BatchQualityExportForReport 质量报告获取问题数据SQL失败：", err)
		return "", "", err
	}
	// 创建一个新的 XLSX 文件
	file := xlsx.NewFile()
	defer func() {
		if err := file.Close(); err != nil {
			fmt.Println("BatchQualityExportForReport :", err)
		}
	}()
	problemfile := xlsx.NewFile()
	defer func() {
		if err := problemfile.Close(); err != nil {
			fmt.Println("BatchQualityExportForReport :", err)
		}
	}()
	var num, cfnum int
	for _, v := range probleminfo {
		if v.Cfsql != "" {
			cfnum++
		}
		if v.Problemsql != "" {
			num++
		}
	}
	eriCha2 := make(chan error)
	ch2 := make(chan string, cfnum)
	proch2 := make(chan string, num)
	param := fmt.Sprintf(`___dep: %s,month: %s`, department, month)
	fmt.Println("probleminfo: ", param)
	for i, v := range probleminfo {
		fmt.Println("BatchQualityExportForReport probleminfo:", i, v.Cfsql, v.Problemsql)
		if v.Cfsql != "" {
			go func(v source.BatchProblem) {
				err := BatchRepatiton(v, file)
				if err != nil {
					eriCha2 <- err
					return
				}
				ch2 <- "OK"
			}(v)
		}
		if v.Problemsql != "" {
			go func(v source.BatchProblem) {
				err := BatchProblem(v, problemfile)
				if err != nil {
					eriCha2 <- err
					return
				}
				proch2 <- "OK"
			}(v)
		}
	}
	cf_nnn := 0
	pro_nnn := 0
	for {
		fmt.Println("probleminfo#2: ", param)
		fmt.Println("BatchQualityExportForReport pro_nnn:", cf_nnn, pro_nnn, cfnum, num)
		if cf_nnn == cfnum && pro_nnn == num {
			break
		}
		select {
		case err = <-eriCha2:
			if err != nil {
				fmt.Println("BatchQualityExportForReport EAddBulk ADD err:", err)
				return "", "", err
			}
		case <-ch2:
			cf_nnn += 1
		case <-proch2:
			pro_nnn += 1
		}
	}
	fmt.Println("====结束====")
	var filename1, filename2 string
	if cfnum != 0 {
		filename1 = reportPath + "/" + department + "_" + month + "_重复数据.xlsx"
		if err := file.SaveAs(filename1); err != nil {
			fmt.Println("BatchQualityExportForReport 保存文件出错：", err)
			return filename1, filename2, err
		}
	}
	if num != 0 {
		filename2 = reportPath + "/" + department + "_" + month + "_异常数据.xlsx"
		if err := problemfile.SaveAs(filename2); err != nil {
			fmt.Println("BatchQualityExportForReport 保存文件出错2：", err)
			return filename1, filename2, err
		}
	}
	return filename1, filename2, nil
}

func BatchQualityExport(ids []string, middletable int) (string, error) {
	var filename string
	var probleminfo []source.BatchProblem
	var err error
	if middletable != 1 {
		probleminfo, err = BatchCommon(ids, false, "")
	} else {
		probleminfo, err = BatchMiddleCommon(ids, false, "")
	}

	if err != nil {
		logger.Error.Println("执行MG语句失败：", err)
	}
	//for _, v := range probleminfo {
	//
	//}
	// 创建一个新的 XLSX 文件
	file := xlsx.NewFile()
	defer func() {
		if err := file.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	problemfile := xlsx.NewFile()
	defer func() {
		if err := problemfile.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	var num, cfnum int
	for _, v := range probleminfo {
		if v.Cfsql != "" {
			cfnum++
		}
		if v.Problemsql != "" {
			num++
		}
	}
	eriCha2 := make(chan error)
	ch2 := make(chan string, cfnum)
	proch2 := make(chan string, num)
	for _, v := range probleminfo {
		v.MiddleTableType = middletable
		if v.Cfsql != "" {
			go func(v source.BatchProblem) {
				err := BatchRepatiton(v, file)
				if err != nil {
					eriCha2 <- err
					return
				}
				ch2 <- "OK"
			}(v)
		}
		if v.Problemsql != "" {
			go func(v source.BatchProblem) {
				err := BatchProblem(v, problemfile)
				if err != nil {
					eriCha2 <- err
					return
				}
				proch2 <- "OK"
			}(v)
		}
	}
	cf_nnn := 0
	pro_nnn := 0
	for {
		if cf_nnn == cfnum && pro_nnn == num {
			break
		}
		select {
		case err = <-eriCha2:
			if err != nil {
				logger.Error.Println("EAddBulk ADD err:", err)
				return filename, err
			}
		case <-ch2:
			cf_nnn += 1
		case <-proch2:
			pro_nnn += 1
		}
	}
	fmt.Println("====结束====")
	var filename1, filename2 string
	var files []*os.File
	if cfnum != 0 {
		filename1 = common.MetadataDownloadPath + "重复数据_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename1); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename1, err
		}
		f, err := os.Open(filename1)
		if err != nil {
			logger.Error.Println("打开文件出错：", err)
			return filename1, err
		}
		files = append(files, f)
		defer f.Close()
	}
	if num != 0 {
		filename2 = common.MetadataDownloadPath + "质量问题数据_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := problemfile.SaveAs(filename2); err != nil {
			logger.Error.Println("保存文件出错2：", err)
			return filename2, err
		}
		f, err := os.Open(filename2)
		if err != nil {
			logger.Error.Println("打开文件出错：", err)
			return filename2, err
		}
		files = append(files, f)
		defer f.Close()
	}
	//问题数据
	fileExitPath := common.MetadataDownloadPath + "质量问题数据_" + time.Now().Format("2006-01-02") + ".zip"
	err = Compress(files, fileExitPath)
	if err != nil {
		logger.Error.Println("压缩文件出错：", err)
		return fileExitPath, err
	}

	return fileExitPath, nil
}
func Compress(files []*os.File, dest string) error {
	d, _ := os.Create(dest)
	defer d.Close()
	w := zip.NewWriter(d)
	defer w.Close()
	for _, file := range files {
		err := compress(file, "", w)
		if err != nil {
			return err
		}
	}
	return nil
}

// 压缩文件
func compress(file *os.File, prefix string, zw *zip.Writer) error {
	info, err := file.Stat()
	if err != nil {
		return err
	}
	if info.IsDir() {
		prefix = prefix + "/" + info.Name()
		fileInfos, err := file.Readdir(-1)
		if err != nil {
			return err
		}
		for _, fi := range fileInfos {
			f, err := os.Open(file.Name() + "/" + fi.Name())
			if err != nil {
				return err
			}
			err = compress(f, prefix, zw)
			if err != nil {
				return err
			}
		}
	} else {
		header, err := zip.FileInfoHeader(info)
		header.Name = prefix + "/" + header.Name
		if err != nil {
			return err
		}
		writer, err := zw.CreateHeader(header)
		if err != nil {
			return err
		}
		_, err = io.Copy(writer, file)
		file.Close()
		if err != nil {
			return err
		}
	}
	return nil
}
func CommonGetTableInfo(engineinfo source.EngineInfo, problemtype int, dbname, sourcedb, tbname, problemsourcedb, fieldinfos string, fieldinfoarr []string) (string, error) {
	var sqltxt, cf_sqltxt, normalsqltxt string
	if problemtype == 1 {
		//重复数据
		cf_tbname := "cf_" + sourcedb + "_" + tbname
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
		case common.HIVE, common.Inceptor:
			cf_sqltxt = fmt.Sprintf("select * from `%s.%s`", problemsourcedb, cf_tbname)
		case common.ODPS:
			cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, cf_tbname)
		case common.DMDB:
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
		}
		//res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, 1)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
		}
		fmt.Println("==========test2=======", res.Data, cf_sqltxt)
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := excelize.NewFile()
		style, err := file.NewStyle(&excelize.Style{
			Alignment: &excelize.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})
		// 工作表数量
		sheetName := "重复数据"
		index := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		//删除默认的Sheet1
		sheet1Index := file.GetSheetIndex("Sheet1")
		if sheet1Index == -1 {
			fmt.Println("找不到默认的 Sheet1")
		} else {
			// 删除默认的 Sheet1
			file.DeleteSheet("Sheet1")
		}

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		fmt.Println("headers", fieldinfoarr)
		for i, header := range fieldinfoarr {
			cell := indexToColumnName(i+1) + "1"
			err := file.SetCellValue(sheetName, cell, header)
			if err != nil {
				logger.Error.Println("写入表头失败err:", err)
				return filename, err
			}
		}
		for {
			fmt.Println("==========rsuid=======", rsuid)

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			fmt.Println("==========res2=======", res2.Data, fieldinfoarr, number)
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---
			number, err = RepetitionWriteDataToFile(res2.Data, file, number, style)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

			//1、字段名称、来源表、来源字段、问题描述、规则类型 格式如下

		}
		// 保存文件
		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil

	} else if problemtype == 2 {
		//问题数据
		problemtbname := sourcedb + "_" + tbname
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s" `, fieldinfos, problemsourcedb, problemtbname)
		case common.HIVE, common.Inceptor:
			//sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
			sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s"`, fieldinfos, problemsourcedb, problemtbname)
		}

	} else if problemtype == 3 {
		//重复数据、问题数据都有
		problemtbname := sourcedb + "_" + tbname
		cf_tbname := "cf_" + sourcedb + "_" + tbname
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s" `, fieldinfos, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s" `, fieldinfos, problemsourcedb, cf_tbname)
		case common.HIVE, common.Inceptor:
			sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf("select `%s` from `%s.%s`", fieldinfos, problemsourcedb, cf_tbname)
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s" `, fieldinfos, problemsourcedb, cf_tbname)
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s"`, fieldinfos, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s" `, fieldinfos, problemsourcedb, cf_tbname)
		}

	}
	fmt.Println(sqltxt, cf_sqltxt, normalsqltxt)
	return "", nil
}
func CommonGetTableInfo2(sourceid string, engineinfo source.EngineInfo, problemtype int, dbname, sourcedb, tbname, problemsourcedb, problemtable string, fieldinfoarr, describearr []string) (string, error) {
	var sqltxt, cf_sqltxt, normalsqltxt string
	if problemtype == 1 {
		//重复数据
		var cf_tbname string
		if problemtable == "" {
			cf_tbname = "cf_" + sourcedb + "_" + tbname
		} else {
			cf_tbname = "cf_" + sourcedb + "_" + problemtable
		}

		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.HIVE, common.Inceptor:
			cf_sqltxt = fmt.Sprintf("select * from `%s.%s`", problemsourcedb, cf_tbname)
		case common.ODPS:
			fieldinfoarr = append(fieldinfoarr, "cf_rksj")
			cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, cf_tbname)
		case common.DMDB:
			fieldinfoarr = append(fieldinfoarr, "CF_RKSJ")
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
		}
		describearr = append(describearr, "")
		//res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "重复数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		//styleID, err := file.NewStyle(&xlsx.Style{Font: &xlsx.Font{Color: "777777"}})
		//if err != nil {
		//	fmt.Println(err)
		//}
		// 工作表数量

		//删除默认的Sheet1
		//sheet1Index := file.GetSheetIndex("Sheet1")
		//if sheet1Index == -1 {
		//	fmt.Println("找不到默认的 Sheet1")
		//} else {
		//	// 删除默认的 Sheet1
		//	file.DeleteSheet("Sheet1")
		//}

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}

		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//

		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---
			number, err = RepetitionWriteDataToFile2(res2.Data, file, number, style, sw, fieldinfoarr, engineinfo.EngineName, cf_tbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

			//1、字段名称、来源表、来源字段、问题描述、规则类型 格式如下

		}
		// 保存文件
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil

	} else if problemtype == 2 {
		extract, _ := SearchTbAccess(sourceid)
		//问题数据
		//需要访问获取全部数据
		//根据每行的dwd_ruleid 中的id 找到
		var problemtbname string
		if problemtable == "" {
			problemtbname = sourcedb + "_" + tbname
		} else {
			problemtbname = sourcedb + "_" + problemtable
		}
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.HIVE, common.Inceptor:
			//sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
			sqltxt = fmt.Sprintf("select * from `%s.%s` ", problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "DWD_RKSJ")
		}
		describearr = append(describearr, "")
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "问题数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: "wtjl"})
		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: "问题记录"})
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			number, _ = CombData(res2.Data, extract, sw, style, number, fieldinfoarr, engineinfo.EngineName, problemtbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
		}
		// 保存文件
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil

	} else if problemtype == 3 {
		//重复数据、问题数据都有
		extract, _ := SearchTbAccess(sourceid)
		cffieldinfoarr := fieldinfoarr
		var problemtbname, cf_tbname string
		if problemtable == "" {
			problemtbname = sourcedb + "_" + tbname
			cf_tbname = "cf_" + sourcedb + "_" + tbname
		} else {
			problemtbname = sourcedb + "_" + problemtable
			cf_tbname = "cf_" + sourcedb + "_" + problemtable
		}

		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.HIVE, common.Inceptor:
			sqltxt = fmt.Sprintf("select * from `%s.%s` ", problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf("select * from `%s.%s`", problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "DWD_RKSJ")
			cffieldinfoarr = append(fieldinfoarr, "CF_RKSJ")
		}
		describearr = append(describearr, "")
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "问题数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: "wtjl"})
		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: "问题记录"})
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---

			number, err = CombData(res2.Data, extract, sw, style, number, fieldinfoarr, engineinfo.EngineName, problemtbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

		}
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}
		//重复数据
		cf_res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
		}
		for _, v := range cf_res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var cf_number int = 2

		sheetName2 := "重复数据"
		index2, _ := file.NewSheet(sheetName2)
		file.SetActiveSheet(index2)
		cf_sw, err := file.NewStreamWriter(sheetName2)
		if err != nil {
			fmt.Println(err)

		}
		style, err = file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName2 = file.GetSheetName(file.GetActiveSheetIndex())
		var cf_mySlice []interface{}

		for _, header := range cffieldinfoarr {
			cf_mySlice = append(cf_mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		//cell := indexToColumnName(i+1) + "1"
		cf_cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = cf_sw.SetRow(cf_cell, cf_mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//

		for {
			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---
			cf_number, err = RepetitionWriteDataToFile2(res2.Data, file, cf_number, style, cf_sw, cffieldinfoarr, engineinfo.EngineName, cf_tbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

		}

		// 保存文件
		if err := cf_sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil
	}
	fmt.Println(sqltxt, cf_sqltxt, normalsqltxt)
	return "", nil
}
func CombData(data []map[string]interface{}, extract []byte, sw *xlsx.StreamWriter, style, number int, fieldinfoarr []string, enginename, table string) (int, error) {
	//根据qualitypronlem中的fieldinfos 数据规则
	//	xh                              -----字段
	//来源表:ST_STUSERTRIP               ------extactdb——table
	//来源字段:xuehao                     ------字段注释
	//问题1:
	//	问题描述：不符合空值校验           errmsg
	//	规则类型：单字段校验             type==(es与查询到的每行数据的id进行对比得出)
	//问题2:
	//	问题描述：不符合XX规则
	//	规则类型：单字段校验
	extractdbfield := gjson.Get(string(extract), "hits.hits.0._source.extractdb.field").Array()
	tasktype := gjson.Get(string(extract), "hits.hits.0._source.tasktype").Int()
	extractdbtable := gjson.Get(string(extract), "hits.hits.0._source.extractdb.table").String()
	sourcedbdbfield := gjson.Get(string(extract), "hits.hits.0._source.sourcedb.field").Array()
	tablerelation := gjson.Get(string(extract), "hits.hits.0._source.tablerelation.edges").Array()
	for rowIndex, rowData := range data {
		//按照行解析
		var tb_ruleinfo string
		var mySlice []interface{}
		switch enginename {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			if _, ok := rowData["dwd_ruleid"].(string); ok {
				tb_ruleinfo = rowData["dwd_ruleid"].(string)
				if tb_ruleinfo != "" {
					//理论上不存在这种情况
				}
			}
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if v == k {
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}

			}
		case common.DMDB:
			if _, ok := rowData["DWD_RULEID"].(string); ok {
				tb_ruleinfo = rowData["DWD_RULEID"].(string)
				if tb_ruleinfo != "" {
					//理论上不存在这种情况
				}
			}
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if v == k {
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}

			}

		case common.HIVE, common.Inceptor, common.ODPS:
			if _, ok := rowData["dwd_ruleid"].(string); ok {
				tb_ruleinfo = rowData["dwd_ruleid"].(string)
				if tb_ruleinfo != "" {
					//理论上不存在这种情况
				}
			} else if _, ok := rowData[table+".dwd_ruleid"].(string); ok {
				tb_ruleinfo = rowData[table+".dwd_ruleid"].(string)
			}
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if v == k || table+"."+v == k {
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}
			}
		}
		var data1 []map[string]interface{}
		err := json.Unmarshal([]byte(tb_ruleinfo), &data1)
		if err != nil {
			fmt.Println("Error:", err)
		}
		var ruleinfos []source.RuleInfo
		var wt_datas [][]map[string]interface{}
		for _, v := range data1 {
			//根据dwd_ruleid获取所有规则，然后进行errMsg筛选
			var ruleinfo source.RuleInfo
			ruleinfo.Field = v["field"].(string)
			var ruleids string
			// if _, ok := v["ruleid"].([]interface{}); ok {
			// 	for _, v := range v["ruleid"].([]interface{}) {
			// 		ruleinfo.Ruleid = append(ruleinfo.Ruleid, v.(string))
			// 		ruleids = ruleids + v.(string) + ","
			// 	}
			// }
			if ruleidVal, ok := v["ruleid"]; ok && ruleidVal != nil {
				if ruletypeSlice, ok := ruleidVal.([]interface{}); ok {
					for _, v1 := range ruletypeSlice {
						ruleinfo.Ruleid = append(ruleinfo.Ruleid, v1.(string))
						ruleids = ruleids + v1.(string) + ","
						//fmt.Println("ruletype:", v1.(string), v["ruleid"].([]interface{}), ruleinfo.Ruletype)
					}
				}
			} else {
				continue
			}
			if _, ok := v["ruletype"].([]interface{}); ok {
				for _, v1 := range v["ruletype"].([]interface{}) {
					ruleinfo.Ruletype = append(ruleinfo.Ruletype, v1.(string))
				}
			}

			if _, ok := v["ruledimension"].([]interface{}); ok {
				for _, v := range v["ruledimension"].([]interface{}) {
					ruleinfo.Ruledimension = append(ruleinfo.Ruledimension, v.(string))
				}
			}

			var wt_data []map[string]interface{}
			switch enginename {
			case common.HIVE, common.Inceptor, common.ODPS:
				tmpfieldname := "wt_" + ruleinfo.Field
				//fmt.Println("tmpfieldname:", tmpfieldname, rowData)
				if rowData[tmpfieldname] != nil {
					//fmt.Println("tmpfieldname2:", tmpfieldname, rowData)
					wt_data = ContentTransValue(rowData[tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				} else if rowData[table+"."+tmpfieldname] != nil {
					//fmt.Println("tmpfieldname3:", tmpfieldname, rowData)
					wt_data = ContentTransValue(rowData[table+"."+tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				}
			case common.DMDB:
				//特殊情况：达梦数据库表中wt开头的都是大写如WT_RULEID
				tmpfieldname := "WT_" + strings.ToUpper(ruleinfo.Field)
				if rowData[tmpfieldname] != nil {
					wt_data = ContentTransValue(rowData[tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				}
			default:
				tmpfieldname := "wt_" + ruleinfo.Field
				if rowData[tmpfieldname] != nil {
					wt_data = ContentTransValue(rowData[tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				}
			}
			for _, wt_v := range wt_data {
				if wt_v["errID"] != nil && strings.Contains(ruleids, wt_v["errID"].(string)) {
					fmt.Println("wt_v:", wt_v["errID"].(string), wt_v["errMsg"].(string))
					ruleinfo.ProblemDesc = append(ruleinfo.ProblemDesc, wt_v["errMsg"].(string))
				}
			}
			//fmt.Println("ruleinfo.ProblemDesc:", ruleinfo.ProblemDesc)
			if tasktype == 4 {
				for num, sfield := range tablerelation {
					if sfield.Get("target").String() == ruleinfo.Field {
						ruleinfo.Extractfield = extractdbfield[num].String()
						ruleinfo.Extracttable = extractdbtable
					}
				}
			} else {
				for num, sfield := range sourcedbdbfield {
					if sfield.String() == ruleinfo.Field {
						ruleinfo.Extractfield = extractdbfield[num].String()
						ruleinfo.Extracttable = extractdbtable
					}
				}
			}

			ruleinfos = append(ruleinfos, ruleinfo)
		}
		var three string
		for _, v := range ruleinfos {
			if len(v.ProblemDesc) == 0 {
				continue
			}
			three = three + fmt.Sprintf("%s\n来源表:%s\n来源字段:%s\n", v.Field, v.Extracttable, v.Extractfield)
			for j, v2 := range v.ProblemDesc {
				if j >= len(v.Ruletype) {
					continue
				}
				if v.Ruletype[j] == "single" {
					three = three + fmt.Sprintf("问题%d:\n问题描述:%s\n规则类型:单字段校验\n", j+1, v2)
				} else {
					three = three + fmt.Sprintf("问题%d:\n问题描述:%s\n规则类型:多字段联合校验\n", j+1, v2)
				}

			}

		}
		//还有其他字段
		//fmt.Println("three:", three)
		//fmt.Println("fieldinfoarr:", fieldinfoarr)

		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: three})
		var cell string
		if number != 0 {
			cell, err = xlsx.CoordinatesToCellName(1, number+1)
		} else {
			cell, err = xlsx.CoordinatesToCellName(1, rowIndex+3)
		}

		//cell, err := xlsx.CoordinatesToCellName(1, rowIndex+1)
		if err != nil {
			fmt.Println(err)
			break
		}
		if err := sw.SetRow(cell, mySlice); err != nil {
			fmt.Println(err)
			break
		}
		number = number + 1
	}
	//for rowIndex, rowData := range data {
	//	tb_ruleid := rowData["dwd_ruleid"].(string)
	//	tb_ruleids, _ := StringTransValue(tb_ruleid, 0)
	//	fmt.Println("tb_ruleids:", tb_ruleids, fieldinforule)
	//	mapinfos := make(map[string]source.ProblemDwd_Ruleid, len(tb_ruleids))
	//	//for i, tb_ruleid := range tb_ruleids { // 1 3 2 4 ---表里的规则  (1 1)
	//	//	fmt.Println("test11:", i, tb_ruleid, len(fieldinforule))
	//	//	for _, dsinfo := range fieldinforule { //id 2 3  ;age 4 1  ds质量问题库中的规则(1,1,"")
	//	//		fmt.Println("dsinfo:", dsinfo)
	//	//		fieldname := dsinfo.Get("name").String()
	//	//		ds_ruleids := dsinfo.Get("rulerid").Array()
	//	//		for j, ds_ruleid := range ds_ruleids {
	//	//			fmt.Println("tb_ruleid:", tb_ruleid, ds_ruleid.String())
	//	//			if tb_ruleid == ds_ruleid.String() {
	//	//				fmt.Println("j:", j)
	//	//				ruletype := dsinfo.Get("ruletype").Array()[j].String()
	//	//				tmpfield := "wt_" + fieldname
	//	//				content := ContentTransValue(rowData[tmpfield].(string), i)
	//	//				fmt.Println("content:", content)
	//	//				if content != "" {
	//	//					if _, ok := mapinfos[fieldname]; !ok {
	//	//						//不存在则添加
	//	//						probleminfo.Fieldname = fieldname
	//	//						for num, sfield := range sourcedbdbfield {
	//	//							if sfield.String() == fieldname {
	//	//								probleminfo.Extractfield = extractdbfield[num].String()
	//	//								probleminfo.Extracttable = extractdbtable
	//	//							}
	//	//						}
	//	//						probleminfo.Ruletype = append(probleminfo.Ruletype, ruletype)
	//	//						probleminfo.Problemdescribe = append(probleminfo.Problemdescribe, content)
	//	//						mapinfos[fieldname] = probleminfo
	//	//					} else {
	//	//						//存在则追加
	//	//						s := mapinfos[fieldname]
	//	//						s.Ruletype = append(s.Ruletype, ruletype)
	//	//						s.Problemdescribe = append(s.Problemdescribe, content)
	//	//						mapinfos[fieldname] = s
	//	//					}
	//	//				}
	//	//			}
	//	//		}
	//	//	}
	//	//}
	//	var three string
	//	for _, v := range mapinfos {
	//		three = three + fmt.Sprintf("%s\n来源表:%s\n来源字段:%s\n", v.Fieldname, v.Extracttable, v.Extractfield)
	//		for j, v2 := range v.Problemdescribe {
	//			three = three + fmt.Sprintf("问题%d:\n问题描述:%s\n规则类型:%s\n", j+1, v2, v.Ruletype[j])
	//		}
	//
	//	}
	//	filtered := make(map[string]interface{})
	//	//还有其他字段
	//	fmt.Println("three:", three)
	//	fmt.Println("rowData:", rowData)
	//	fmt.Println("fieldinfoarr:", fieldinfoarr)
	//	//for k, v := range rowData {
	//	//	for _, v2 := range fieldinfoarr {
	//	//		if k == v2 {
	//	//			filtered[k] = v
	//	//		}
	//	//	}
	//	//
	//	//}
	//	for _, v := range fieldinfoarr {
	//		for k, v2 := range rowData {
	//			if v == k {
	//				filtered[k] = v2
	//			}
	//		}
	//
	//	}
	//	filtered["dwd_ruleid"] = three
	//	fmt.Println("filtered:", filtered)
	//	var mySlice []interface{}
	//	for _, value := range filtered {
	//		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: value})
	//	}
	//	fmt.Println("myslicetest:", mySlice)
	//	var cell string
	//	var err error
	//	if number != 0 {
	//		fmt.Println("aa:", number)
	//		cell, err = xlsx.CoordinatesToCellName(1, number+1)
	//	} else {
	//		cell, err = xlsx.CoordinatesToCellName(1, rowIndex+3)
	//	}
	//
	//	//cell, err := xlsx.CoordinatesToCellName(1, rowIndex+1)
	//	if err != nil {
	//		fmt.Println(err)
	//		break
	//	}
	//	fmt.Println("cell:", cell, mySlice)
	//	if err := sw.SetRow(cell, mySlice); err != nil {
	//		fmt.Println(err)
	//		break
	//	}
	//	number = number + 1
	//}
	return number, nil
}
func StringTransValue(s string, i int) ([]string, string) {
	var ids []string
	var id string
	arrs := strings.Split(s, "}")
	for j, v := range arrs {
		subarr := strings.Split(v, "{")
		if j == i {
			id = subarr[1]
		}
		if subarr[len(subarr)-1] != "" {
			ids = append(ids, subarr[len(subarr)-1])
		}
	}
	return ids, id
}
func ContentTransValue2(s string) []map[string]interface{} {
	var content string
	arr := strings.Split(s, "}")
	for i := 0; i <= len(arr)-1; i++ {
		if i == 0 {
			content = arr[i] + "}"
		} else if i <= len(arr)-2 {
			content = content + "," + arr[i] + "},"
		}
	}
	content = strings.Trim(content, ",")
	content = "[" + content + "]"
	var data []map[string]interface{}
	err := json.Unmarshal([]byte(content), &data)
	if err != nil {
		fmt.Println("Error:", err)
	}
	return data
}
func ContentTransValue(s string) []map[string]interface{} {
	s = strings.ReplaceAll(s, `\d`, `\\d`)
	content := AddCommaAfterChar(s, "}{")
	content = strings.Trim(content, ",")
	content = "[" + content + "]"
	var data []map[string]interface{}
	err := json.Unmarshal([]byte(content), &data)
	if err != nil {
		fmt.Println("Error:", err)
	}
	return data
}
func AddCommaAfterChar(input string, char string) string {
	// 查找指定字符的索引位置
	index := strings.Index(input, char)
	// 如果找到指定字符
	if index != -1 {
		// 将字符串切分为两部分：指定字符之前和之后
		before := input[:index+len(char)-1]
		after := input[index+len(char)-1:]
		fmt.Println("before:", before)
		fmt.Println("after:", after)
		// 在指定字符后面添加逗号
		result := before + "," + after
		return result
	}

	// 如果未找到指定字符，则返回原始字符串
	return input
}
func RepetitionWriteDataToFile(data []map[string]interface{}, file *excelize.File, number, style int) (int, error) {
	sheetName := file.GetSheetName(file.GetActiveSheetIndex())
	for rowIndex, rowData := range data {
		var i int
		for _, cellData := range rowData {
			var cell string
			var err error
			if number != 0 {
				cell, err = excelize.CoordinatesToCellName(i+1, number+1)
			} else {
				cell, err = excelize.CoordinatesToCellName(i+1, rowIndex+3)
			}

			//第一个参数代表列，第二个参数代表行  (字段名称和字段注释各占一行)
			//cell, err = excelize.CoordinatesToCellName(i+1, rowIndex+3)
			if err != nil {
				fmt.Println("err:", err.Error())
			}
			file.SetCellValue(sheetName, cell, cellData)
			err = file.SetCellStyle(sheetName, cell, cell, style)
			if err != nil {
				logger.Error.Println("写入表格类型err:", err)
				return number, err
			}
			i++
			//

		}
		number = number + 1

	}
	return number, nil

}
func RepetitionWriteDataToFile2(data []map[string]interface{}, file *xlsx.File, number, style int, sw *xlsx.StreamWriter, fieldinfoarr []string, enginename, table string) (int, error) {

	//
	var cell string
	var err error
	for rowIndex, rowData := range data {
		filtered := make(map[string]interface{})
		//还有其他字段
		var mySlice []interface{}
		switch enginename {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL, common.DMDB:
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if v == k {
						filtered[k] = v2
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}
			}
		case common.HIVE, common.Inceptor, common.ODPS:
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if table+"."+v == k || v == k {
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}
			}
		}
		//
		if number != 0 {
			cell, err = xlsx.CoordinatesToCellName(1, number+1)
		} else {
			cell, err = xlsx.CoordinatesToCellName(1, rowIndex+3)
		}

		//cell, err := xlsx.CoordinatesToCellName(1, rowIndex+1)
		if err != nil {
			fmt.Println(err)
			break
		}
		if err := sw.SetRow(cell, mySlice); err != nil {
			fmt.Println(err)
			break
		}
		number = number + 1
	}
	//for rowIndex, rowData := range data {
	//	var i int
	//	for _, cellData := range rowData {
	//		fmt.Println("cellData", cellData)
	//		var cell string
	//		var err error
	//		if number != 0 {
	//			fmt.Println("aa:", number)
	//			cell, err = xlsx.CoordinatesToCellName(i+1, number+1)
	//		} else {
	//			cell, err = xlsx.CoordinatesToCellName(i+1, rowIndex+3)
	//		}
	//
	//		//第一个参数代表列，第二个参数代表行  (字段名称和字段注释各占一行)
	//		//cell, err = excelize.CoordinatesToCellName(i+1, rowIndex+3)
	//		if err != nil {
	//			fmt.Println("err:", err.Error())
	//		}
	//		fmt.Println("cell:", cell, cellData)
	//		file.SetCellValue(sheetName, cell, cellData)
	//		err = file.SetCellStyle(sheetName, cell, cell, style)
	//		if err != nil {
	//			logger.Error.Println("写入表格类型err:", err)
	//			return number, err
	//		}
	//		i++
	//		//
	//
	//	}
	//	number = number + 1
	//
	//}
	return number, nil

}

func GetDrealDbEngine(projectid string) source.EngineInfo {
	var s source.EngineInfo
	s.EngineID = projectid + "_" + "drealdb"
	s.EngineName = common.MYSQL
	s.IP = common.DRealDbDetail.IP
	s.Port = common.DRealDbDetail.PORT
	s.Username = common.DRealDbDetail.UserName
	s.Password = common.DRealDbDetail.PassWord
	s.InitDB = "sys"
	return s
}
func QualityTableDetail(info source.QualityDataInfo) (map[string]interface{}, []string, error) {
	var re map[string]interface{}
	//var qualityinfo source.QualityTableInfo
	//根据info.id获取引擎问题表信息
	//taskinfo, err := CommonTaskInfo(info.Id)
	//if err != nil {
	//	logger.Error.Println("查询P信息失败：", err.Error())
	//	return re, err
	//}
	var repetitionmainkeys []string
	var qualityinfo source.QualityTableInfo
	quares, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, info.Id)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return re, repetitionmainkeys, err
	}
	err = json.Unmarshal([]byte(quares), &qualityinfo)
	if err != nil {
		return re, repetitionmainkeys, err
	}
	var dateString string
	if strings.Count(qualityinfo.ExplorationTime, "-") != 1 {
		if qualityinfo.ExplorationTime != "" {
			dateString = qualityinfo.ExplorationTime[:strings.LastIndex(qualityinfo.ExplorationTime, "-")]
		}
	}
	repetitionmainkey := gjson.Get(quares, "duproles.repetitionmainkey").Array()
	for _, v := range repetitionmainkey {
		repetitionmainkeys = append(repetitionmainkeys, v.String())
	}
	problemtype := gjson.Get(quares, "problemtype").Int()
	dbname := gjson.Get(quares, "database").String()
	sourcedb := gjson.Get(quares, "sourcedb").String()
	tbname := gjson.Get(quares, "tbname").String()

	projectid := gjson.Get(quares, "projectid").String()
	problemsourcedb := projectid + "_wt"

	//fieldinfo := gjson.Get(quares, "fieldinfo").Array()
	//var fieldinfos string
	//for _, v := range fieldinfo {
	//	name := v.Get("name").String()
	//	if len(fieldinfos) == 0 {
	//		fieldinfos = name
	//	} else {
	//		fieldinfos = fieldinfos + "," + name
	//	}
	//}
	engineinfo, err := dbutil.EngineInfo(info.EngineId)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return re, repetitionmainkeys, err
	}
	//if engineinfo.EngineName == common.Hive || engineinfo.EngineName == common.Inceptor {
	//	dbname = info.ProjectID + "_wt"
	//} else {
	//	dbname = qualityinfo.Database
	//}
	var sqltxt string
	var commontable string
	var probleversion []string
	var drealdbCatalog string
	var queryEngine source.EngineInfo
	if common.DRealDbDetail.IP != "" && engineinfo.EngineName == common.HIVE {
		drealdbCatalog = "`" + common.DRealDbDetail.CatalogName + "`."
		queryEngine = GetDrealDbEngine(engineinfo.EngineID)
		dbname = "sys"
		logger.Info.Println("drealdbCatalog", drealdbCatalog)
	} else {
		queryEngine = engineinfo
	}
	for _, v := range qualityinfo.HistoryLogInfo {
		probleversion = append(probleversion, v.YearMonth)
	}
	compareFn := func(i, j int) bool {
		// 将字符串解析为时间对象
		timeI, _ := time.Parse("2006-01", probleversion[i])
		timeJ, _ := time.Parse("2006-01", probleversion[j])

		// 按照时间对象的升序进行比较
		return timeI.Before(timeJ)
	}

	// 使用自定义的比较函数进行排序
	sort.Slice(probleversion, compareFn)
	var timefield string
	if info.IsProblemTable {
		if problemtype == 1 {
			return re, repetitionmainkeys, nil
		} else if problemtype == 2 || problemtype == 3 {
			var problemtbname string
			for _, v := range qualityinfo.HistoryLogInfo {
				if v.YearMonth == dateString {
					if v.History == 0 {
						problemtbname = sourcedb + "_" + tbname
					} else {
						problemtbname = sourcedb + "_" + tbname + "_" + strings.Replace(v.YearMonth, "-", "", -1)
					}
				}
			}
			if qualityinfo.MiddleTableType == 1 {
				problemtbname = tbname
			}
			if qualityinfo.Module == 7 {
				problemtbname = sourcedb + "_" + tbname
			}
			if engineinfo.EngineName == common.DMDB {
				timefield = "DWD_RKSJ"
			} else {
				timefield = "dwd_rksj"
			}
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s" ORDER BY "%s" DESC limit %s`, problemsourcedb, problemtbname, timefield, common.LIMIT)
				//sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s" `, fieldinfos, problemsourcedb, problemtbname)
			case common.HIVE, common.Inceptor:
				//sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
				//sqltxt = fmt.Sprintf("select  * from %s`%s.%s` ORDER BY `%s` DESC LIMIT %s", drealdbCatalog, problemsourcedb, problemtbname, timefield, common.LIMIT)
				sqltxt = fmt.Sprintf("select  * from `%s`.`%s` ORDER BY `%s` DESC LIMIT %s", problemsourcedb, problemtbname, timefield, common.LIMIT)
			case common.ODPS:
				//sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
				sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ORDER BY `%s` DESC LIMIT %s ", problemsourcedb, problemtbname, timefield, common.LIMIT)
			case common.DMDB:
				//sqltxt = fmt.Sprintf(`select "%s" from "%s"."%s"`, fieldinfos, problemsourcedb, problemtbname)
				sqltxt = fmt.Sprintf(`select * from "%s"."%s"  ORDER BY "%s" DESC LIMIT %s`, problemsourcedb, problemtbname, timefield, common.LIMIT)
			}
			commontable = problemtbname
		}
	} else {
		if problemtype == 2 {
			return re, repetitionmainkeys, nil
		} else if problemtype == 1 || problemtype == 3 {
			var cf_tbname string
			for _, v := range qualityinfo.HistoryLogInfo {
				if v.YearMonth == dateString {
					if v.History == 0 {
						cf_tbname = "cf_" + sourcedb + "_" + tbname
					} else {
						cf_tbname = "cf_" + sourcedb + "_" + tbname + "_" + strings.Replace(v.YearMonth, "-", "", -1)
					}
				}
			}
			if qualityinfo.MiddleTableType == 1 {
				cf_tbname = tbname
			}
			if engineinfo.EngineName == common.DMDB {
				timefield = "CF_RKSJ"
			} else {
				timefield = "cf_rksj"
			}
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s" ORDER BY "%s" DESC limit %s`, problemsourcedb, cf_tbname, timefield, common.LIMIT)
			case common.HIVE, common.Inceptor:
				//sqltxt = fmt.Sprintf("select * from %s`%s.%s` ORDER BY `%s` DESC LIMIT %s", drealdbCatalog, problemsourcedb, cf_tbname, timefield, common.LIMIT)
				sqltxt = fmt.Sprintf("select * from `%s`.`%s` ORDER BY `%s` DESC LIMIT %s", problemsourcedb, cf_tbname, timefield, common.LIMIT)
			case common.ODPS:
				sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select `%s` from `%s.%s` ORDER BY `%s` LIMIT %s", problemsourcedb, cf_tbname, timefield, common.LIMIT)
			case common.DMDB:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s" ORDER BY "%s" DESC LIMIT %s`, problemsourcedb, cf_tbname, timefield, common.LIMIT)
			}
			commontable = cf_tbname
		}
	}
	fmt.Println("sqltxt: ", sqltxt, commontable)

	restxt, err1 := collect.MGExecuteQuerySqlV2(engineinfo, dbname, sqltxt)
	//restxt, err1 := collect.MGExecuteQuerySqlV2(queryEngine, dbname, sqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
		logger.Error.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
		return re, repetitionmainkeys, err
	}

	var col []string
	var filetype []string // 5.2 需要字段的类型
	var data [][]interface{}
	var comment []string
	if qualityinfo.MiddleTableType == 1 {
		//
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL, common.DMDB:
			for _, j := range restxt.Cols {
				for k, v := range j {
					isremove := FieldRemove(k)
					if isremove {
						continue
					}
					col = append(col, k)
					filetype = append(filetype, v)
				}
			}
			data = tools.ParseData(restxt.Data, col)
		case common.HIVE, common.Inceptor, common.ODPS:
			for _, j := range restxt.Cols {
				//[map[czy_newapplication_and_alltypes.length:string] ]
				for k, v := range j {
					isremove := FieldRemove(k)
					if isremove {
						continue
					}
					if strings.Contains(k, ".") {
						col = append(col, strings.Split(k, ".")[1])
					} else {
						col = append(col, k)
					}

					filetype = append(filetype, v)
				}
			}
			data = tools.MidHiveParseData(restxt.Data, col)
		}
		resdesc, err1 := collect.MGExecuteQuerySqlMetadata(queryEngine, dbname, tbname, sqltxt)
		if err != nil {
			fmt.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			logger.Error.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			return re, repetitionmainkeys, err
		}
		for _, j := range resdesc.TBInfo {
			for _, v := range j.Cols {
				comment = append(comment, v.Comment)
			}
			for _, v := range j.TablePrimaryKeyInfo.PrimaryColsList {
				repetitionmainkeys = append(repetitionmainkeys, v.Name)
			}
			break
		}
	} else {
		var fieldname []string
		if qualityinfo.MetadataIdNull {
			for _, v := range qualityinfo.HistoryLogInfo {
				for _, v2 := range v.Fieldinfo {
					col = append(col, v2.Name, "wt_"+v2.Name)
					filetype = append(filetype, v2.FieldType, v2.FieldType)
					comment = append(comment, v2.Annotation, v2.Annotation)
				}
			}
			col = append(col, timefield)
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL, common.DMDB:
				//for _, j := range restxt.Cols {
				//	for k, v := range j {
				//		col = append(col, k)
				//		filetype = append(filetype, v)
				//	}
				//}
				data = tools.ParseData(restxt.Data, col)
			case common.HIVE, common.Inceptor, common.ODPS:
				fmt.Println("restxt.cols:", restxt.Cols)
				//for _, j := range restxt.Cols {
				//	//[map[czy_newapplication_and_alltypes.length:string] ]
				//	for k, v := range j {
				//		if strings.Contains(k, ".") {
				//			col = append(col, strings.Split(k, ".")[1])
				//		} else {
				//			col = append(col, k)
				//		}
				//		filetype = append(filetype, v)
				//	}
				//}
				fmt.Println("filetype:", filetype, col)
				fmt.Println("restxt.Data:", restxt.Data)
				data = tools.MidHiveParseData(restxt.Data, col)
			}
			//resdesc, err1 := collect.MGExecuteQuerySqlMetadata(queryEngine, dbname, tbname, sqltxt)
			//if err != nil {
			//	fmt.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			//	logger.Error.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			//	return re, repetitionmainkeys, err
			//}
			//for _, j := range resdesc.TBInfo {
			//	for _, v := range j.Cols {
			//		comment = append(comment, v.Comment)
			//	}
			//	for _, v := range j.TablePrimaryKeyInfo.PrimaryColsList {
			//		repetitionmainkeys = append(repetitionmainkeys, v.Name)
			//	}
			//	break
			//}
		} else {
			comment, fieldname, err = GetProblemInfo(info.Id, info.IsProblemTable)
			fieldname = append(fieldname, timefield)
			fmt.Println("fieldname", fieldname)
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL, common.DMDB:
				for _, j := range restxt.Cols {
					for k, v := range j {
						for _, f := range fieldname {
							if k == f {
								col = append(col, k)
								filetype = append(filetype, v)
							}
						}
						continue
					}
				}
				data = tools.ParseData(restxt.Data, col)
			case common.HIVE, common.Inceptor, common.ODPS:
				for _, j := range restxt.Cols {
					//[map[czy_newapplication_and_alltypes.length:string] ]
					for k, v := range j {
						for _, f := range fieldname {
							if strings.Contains(k, ".") {
								if strings.Split(k, ".")[1] == f {
									col = append(col, strings.Split(k, ".")[1])
									filetype = append(filetype, v)
								}
							} else {
								if k == f {
									col = append(col, k)
									filetype = append(filetype, v)
								}
							}

						}
						continue
					}
				}
				//data = tools.HiveParseData(restxt.Data, col, commontable)
				data = tools.ParseData(restxt.Data, col)
			}
		}
	}
	var tmpcol []string
	//if engineinfo.EngineName == common.DMDB {
	for i, v := range col {
		if strings.Contains(v, "WT_") {
			tmpcol = append(tmpcol, "wt_"+col[i-1])
			continue
		}
		//if strings.Contains(v, "DWD_RKSJ") || strings.Contains(v, "dwd_rksj") {
		//	continue
		//}
		if strings.Contains(v, "DWD_SFKY") || strings.Contains(v, "dwd_sfky") {
			continue
		}
		if strings.Contains(v, "DWD_SFWT") || strings.Contains(v, "dwd_sfwt") {
			continue
		}
		if strings.Contains(v, "children") {
			continue
		}
		tmpcol = append(tmpcol, v)
	}
	//col = _handle_dm_field(col)
	//} else {
	//	tmpcol = col
	//}

	for i, v := range tmpcol {
		if strings.Contains(v, "dwd_zjid") {
			tmpcol[i] = strings.Replace(tmpcol[i], "dwd_zjid", "DWD_ZJID", 1)
		}

	}

	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return re, repetitionmainkeys, err
	}
	//前端要求：columns数组中wt_或者WT_开头的字段,必须转为小写wt_,其他字段保持不变
	ress := gin.H{
		"columns":        tmpcol,
		"data":           data,
		"fieldtype":      filetype,
		"fieldcomments":  comment,
		"dbtype":         engineinfo.EngineName,
		"problemversion": probleversion,
	}
	return ress, repetitionmainkeys, err
}
func GetProblemInfo(id string, isProblemTable bool) ([]string, []string, error) {
	var comment, fieldname []string
	res, err := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.TbMetaTable, id)
	if err != nil {
		logger.Error.Printf("查询治理任务信息失败:%s, %s", err.Error())
		return comment, fieldname, err
	}
	enginetype := gjson.Get(string(res), "_source.dbtype").String()
	e := gjson.Get(string(res), "_source.fieldinfo").Array()
	for _, field := range e {
		if isProblemTable {
			comment = append(comment, field.Get("annotation").String())
			fieldname = append(fieldname, field.Get("name").String())
			comment = append(comment, field.Get("annotation").String())
			if enginetype == common.DMDB {
				fieldname = append(fieldname, "WT_"+strings.ToUpper(field.Get("name").String()))
			} else {
				fieldname = append(fieldname, "wt_"+field.Get("name").String())
			}

		} else {
			comment = append(comment, field.Get("annotation").String())
			fieldname = append(fieldname, field.Get("name").String())
		}

	}
	return comment, fieldname, nil

}
func CommonTaskInfo(id string) (source.TaskInfo, error) {
	var info source.TaskInfo
	response, err := esutil.SearchByID(common.DataBaseLab, common.TbDev, id)
	if err != nil {
		logger.Error.Printf("查询治理任务信息失败:%s, %s", err.Error())
		return info, err
	}
	info.Schema = gjson.Get(response, "cookinginput.0.schema").String()
	info.Engineid = gjson.Get(response, "cookinginput.0.engineid").String()
	info.Dbtype = gjson.Get(response, "cookinginput.0.dbtype").String()
	info.Metadataid = gjson.Get(response, "cookinginput.0.metadataid").String()
	info.Layerid = gjson.Get(response, "cookinginput.0.layerid").String()
	info.Sourceid = gjson.Get(response, "cookinginput.0.sourceid").String()
	info.Dataclean = gjson.Get(response, "cookinginput.0.duproles.dataclean").Bool()
	info.Problem = gjson.Get(response, "cookinginput.0.duproles.problem").Bool()
	info.Dbname = gjson.Get(response, "cookinginput.0.duproles.dbname").String()
	arr := gjson.Get(response, "cookinginput.0.mainkeyfield").Array()
	for _, v := range arr {
		info.Mainkeyfield = append(info.Mainkeyfield, v.String())
	}
	if info.Metadataid != "" {
		res, err := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaTable, info.Metadataid)
		if err != nil {
			logger.Error.Printf("查询表元数据信息失败:%s, %s", err.Error())
			return info, err
		}
		e := gjson.Get(res, "fieldinfo").Array()
		for _, v := range e {
			info.Annotation = append(info.Annotation, v.Get("annotation").String())
		}
	}
	return info, nil
}
func _handle_dm_field(cols []string) []string {
	for i, v := range cols {

		if strings.Contains(v, "DWD_RKSJ") || strings.Contains(v, "dwd_rksj") {
			break
		}
		if strings.Contains(v, "WT_") || strings.Contains(v, "wt_") {
			cols[i] = strings.Replace(cols[i], "WT_", "wt_", 1)
			continue
		}
		tmpcol := []byte(cols[i+1])
		for k, v := range []byte(v) {
			fmt.Println("v是什么,", v)
			if v <= 122 && v >= 97 {
				tmpcol[k+3] = tmpcol[k+3] + byte(32)
			}
		}
		cols[i+1] = string(tmpcol)
	}
	return cols
}

func QueryRecord(id string) (string, error) {
	var res string //updatetime
	nowTime := tools.GetTNow()
	timeParam := "starttime"
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"jobid": id,
					},
				},
			},
		},
		"size": 1,
		"sort": gin.H{
			timeParam: gin.H{
				"order": "desc",
			},
		},
		"_source": []string{timeParam},
	}

	queryByte, err := esutil.SearchByTerm(common.DataBaseRecord, common.TbRecord, body)
	if err != nil {
		logger.Error.Println(err)
		return nowTime, err
	}

	queryStr, err := esutil.GetSourceFirst(queryByte)
	if err != nil {
		logger.Error.Println(err)
		return nowTime, err
	}

	res = gjson.Get(queryStr, timeParam).String()
	return res, nil
}
func QualityOneTableStatic(id, updatetime string, ishistory bool, inputtype, tasktype int) (string, error) {
	var result string
	var qualityinfo source.QualityTableInfo
	//fmt.Println("updatetime: ", updatetime)
	//更新元数据
	syncBody := fmt.Sprintf(`{"id":"%s"}`, id)
	syncRes, _ := httpclient.PostJson("127.0.0.1", common.ServerPort, "/danastudio/metadata/devcenter/syncsourcetbinside", syncBody)
	fmt.Println("syncres: ", string(syncRes))
	res, err := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaTable, id)
	if err != nil {
		logger.Error.Printf("查询表元数据信息失败:%s, %s", err.Error(), id)
		return result, err
	}

	//整表时效性
	var lastUpdate string
	var timelinessNum int
	timelinessNum = -1
	var thisRule source.QualityRule
	thisRule.Timeliness = source.Timeliness{}
	lastUpdate = gjson.Get(res, "updated").String()
	if len(lastUpdate) == 19 {
		lastUpdate = lastUpdate[0:10]
	}
	fmt.Println("lastUpdate: ", lastUpdate)

	//
	//格式化年份和月份
	var dateString string
	if updatetime != "" {
		if strings.Count(updatetime, "-") != 1 {
			dateString = updatetime[:strings.LastIndex(updatetime, "-")]
		} else {
			dateString = updatetime
		}
	} else {
		currentTime := time.Now()
		dateString = currentTime.Format("2006-01")
	}
	qualityinfo.Engineid = gjson.Get(res, "engineid").String()
	qualityinfo.ProjectID = gjson.Get(res, "projectid").String()
	qualityinfo.Database = gjson.Get(res, "dbname").String() //dbname
	qualityinfo.Tbname = gjson.Get(res, "tbname").String()

	qualityinfo.Enginetype = gjson.Get(res, "dbtype").String()
	qualityinfo.SourceDb = gjson.Get(res, "sourcedb").String()
	qualityinfo.Fieldnum = len(gjson.Get(res, "fieldinfo").Array())
	qualityinfo.SourceTableCatalogid = gjson.Get(res, "catalogid").String()
	qualityinfo.Describe = gjson.Get(res, "describe").String()
	qualityinfo.IsHistory = ishistory
	var sourcecataglist []gjson.Result
	sourcecataglist = gjson.Get(res, "taglist").Array()
	for _, s := range sourcecataglist {
		var tag source.TagList
		tag.ID = s.Get("id").String()
		tag.DirID = s.Get("dirid").String()
		tag.Name = s.Get("name").String()
		tag.Color = s.Get("color").String()
		qualityinfo.TagList = append(qualityinfo.TagList, tag)
	}
	//schema := gjson.Get(res, "schema").String()
	extract, err := SearchTbAccess(id)
	if err != nil {
		logger.Error.Printf("获取业务来源表表元数据信息失败:%s, %s", err.Error())
		//return result, err
	}
	if gjson.Get(string(extract), "hits.total").Int() != 0 {
		qualityinfo.ExtractDbname = gjson.Get(string(extract), "hits.hits.0._source.extractdb.dbname").String()
		qualityinfo.ExtractSchema = gjson.Get(string(extract), "hits.hits.0._source.extractdb.schema").String()
		qualityinfo.ExtractTable = gjson.Get(string(extract), "hits.hits.0._source.extractdb.table").String()
	}
	//数据接入查询
	redev, err := SearchDev(id, qualityinfo.ProjectID, 1)
	//质量监控查询
	tancha_redev, err := SearchDev(id, qualityinfo.ProjectID, 0)
	if err != nil {
		logger.Error.Printf("查询表元数据信息失败:%s, %s", err.Error())
		//return result, err
	}
	qualityinfo.Duproles.InputType = inputtype
	if gjson.Get(string(redev), "hits.total").Int() != 0 {
		cookinginput := gjson.Get(string(redev), "hits.hits.0._source.cookinginput").Array()
		for _, v := range cookinginput {
			qualityinfo.Duproles.Problem = v.Get("duproles.problem").Bool()
			qualityinfo.Duproles.Dataclean = v.Get("duproles.problem").Bool()

			mainkeys := v.Get("mainkeyfield").Array()
			for _, mainkey := range mainkeys {
				qualityinfo.Duproles.InputType = 0
				qualityinfo.Duproles.RepetitionMainKey = append(qualityinfo.Duproles.RepetitionMainKey, mainkey.String())
			}
			break
		}

	}
	var watchnum int
	var watchruler bool
	if gjson.Get(string(tancha_redev), "hits.total").Int() != 0 {
		watchcheck := gjson.Get(string(tancha_redev), "hits.hits.0._source.watchduprole.watchcheck").Bool()
		//watchcheck=true代表有重复数据，watchdup=false 代表重复数据需要放入到问题表中
		if watchcheck == true {
			qualityinfo.Duproles.Problem = true
			watchnum++
			mainkeys := gjson.Get(string(tancha_redev), "hits.hits.0._source.watchduprole.watchmainkey").Array()
			for _, mainkey := range mainkeys {
				qualityinfo.Duproles.InputType = 1
				watchruler = true
				qualityinfo.Duproles.RepetitionMainKey = append(qualityinfo.Duproles.RepetitionMainKey, mainkey.String())
			}
		}

		wholeTableRule := gjson.Get(string(tancha_redev), "hits.hits.0._source.wholetablerule").Array()
		for _, i := range wholeTableRule {
			ruleID := i.Get("id").String()
			ruleInfo, err := esutil.SearchByID(common.DataBaseLab, common.TbQualityRule, ruleID)
			if err != nil {
				logger.Error.Printf("查询质量规则信息失败:%s, %s", err.Error(), ruleID)
				continue
			}

			err = json.Unmarshal([]byte(ruleInfo), &thisRule)
			if err != nil {
				logger.Error.Printf("解析质量规则信息失败:%s, %s", err.Error(), ruleID)
				continue
			}
			if thisRule.RuleTemplate != 206 {
				continue
			}

			thisRule.Timeliness.RealDimension = thisRule.RealDimension

			timelinessNum = inRegion(thisRule, lastUpdate)

			fmt.Println("timelinessNum: ", timelinessNum)
		}
	}
	qualityexist, err := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, id)
	var hitsinfo source.QualityHits3
	json.Unmarshal(qualityexist, &hitsinfo)
	var isexist bool
	if err != nil {
		if strings.Contains(err.Error(), "Error 404") {
			isexist = true
		}
	}
	var tq source.QualityHits3
	json.Unmarshal(qualityexist, &tq)
	//qualityinfo.ExplorationTime = gjson.Get(string(redev), "hits.hits.0._source.updatetime").String()
	problemsourcedb := qualityinfo.ProjectID + "_wt"
	var problemtbname, cf_tbname string
loop:
	if tasktype == 1 {
		//针对新版本任务运行
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(dateString, "-", "", -1)
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(dateString, "-", "", -1)
		qualityinfo.DsVersion = tasktype
		if strings.Count(updatetime, "-") == 1 {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		} else {
			qualityinfo.ExplorationTime = updatetime
		}

	} else if tasktype == 0 {
		//针对历史任务兼容：初始化元数据至最新探查时间所在的月份表中(es中存储)
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		//ds同步历史任务，同步一次自动将该版本信息也更新至最新
		qualityinfo.DsVersion = 1
		if tq.QualityTableInfo.ExplorationTime == "" {
			qualityinfo.ExplorationTime = updatetime
		} else {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		}
	} else {
		//针对最外层同步(历史任务和新建任务同步)
		var isyear bool
		for _, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			fmt.Println("dateString:", dateString)
			fmt.Println("YearMonth:", v.YearMonth)
			if v.YearMonth == dateString {
				tasktype = v.History
				isyear = true
				fmt.Println("tasktype:", tasktype)
			}
		}
		if isyear {
			goto loop
		} else {
			tasktype = 1
			goto loop
		}

	}
	//找到对应的所有问题表
	engineinfo, err := dbutil.EngineInfo(qualityinfo.Engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return "", err
	}
	qualityinfo.Enginename = engineinfo.EngineName
	var sqltxt, cf_sqltxt, normalsqltxt string
	switch engineinfo.EngineName {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	case common.HIVE, common.Inceptor:
		sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", problemsourcedb, cf_tbname)
	case common.ODPS:
		sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, cf_tbname)
	case common.DMDB:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s"`, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	}
	restxt, err1 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, sqltxt)
	if err1 != nil {
		fmt.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
		//当报问题表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err1.Error(), "does not exist") || strings.Contains(err1.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			return "", err1
		}
	}
	cf_restxt, err2 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, cf_sqltxt)
	//{200 Success [map[COUNT(*):3]] map[COUNT(*):BIGINT] [map[COUNT(*):BIGINT]]}  dtameng
	//{200 Success [map[count:4]] map[count:int8] [map[count:int8]]} [map[count:4]]  teryx
	if err2 != nil {
		fmt.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
		//当报重复表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err2.Error(), "does not exist") || strings.Contains(err2.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
			return "", err2
		}
	}
	var rescount, cfcount float64
	rescount = SqlDataTransCount(restxt, engineinfo.EngineName)
	cfcount = SqlDataTransCount(cf_restxt, engineinfo.EngineName)

	//如何判断数据是否更新

	//if isexist {
	//	if rescount == 0 && cfcount == 0 {
	//		//清除该条记录
	//		logger.Info.Println("sourcetableid:", id)
	//		esutil.DelByID(common.DataBaseMetaData, common.QualityProblem, id)
	//		return "不存在问题数据", nil
	//	}
	//}
	normalrestxt, err := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, normalsqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		return "", err
	}
	normalcount := SqlDataTransCount(normalrestxt, engineinfo.EngineName)
	//运行一次，问题数据量为空，不记录
	var scoreinfos source.RulerScore
	if rescount == 0 && cfcount == 0 {
		//产品方案变更，需要展示无问题表
		qualityinfo.ProblemQualifyRate = 100
		qualityinfo.ProblemRecord = 0
		qualityinfo.VerifyRecord = int(normalcount)
		OneTableStatic(tancha_redev, engineinfo, &qualityinfo, id, qualityinfo.Database, problemsourcedb, problemtbname, qualityinfo.VerifyRecord)
		qualityinfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber + watchnum
		qualityinfo.ProblemType = 4
		qualityinfo.Duproles.RepetitionNumber = 0
		qualityinfo.Duproles.RepetitionRate = 0
		scoreinfos, _ = ScoreStatic(qualityinfo.Fieldinfo, qualityinfo.ProjectID, qualityinfo.RepetitionRate, watchruler, timelinessNum)
		//return "不存在问题数据", nil
	}
	if ishistory == false {
		if qualityinfo.Duproles.InputType == 0 {
			qualityinfo.VerifyRecord = int(normalcount) - int(cfcount)
		} else {
			qualityinfo.VerifyRecord = int(normalcount)
		}
		if rescount != 0 {
			//统计问题数据
			qualityinfo.ProblemRecord = int(rescount)
			//qualityinfo.ProblemQualifyRate = float64(qualityinfo.ProblemRecord) / normalcount
			if qualityinfo.VerifyRecord != 0 {
				qualityinfo.ProblemQualifyRate = TruncateDecimal(float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord), 2)
				//qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord)), 64)
			} else {
				qualityinfo.ProblemQualifyRate = 0
			}
			qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", 100-qualityinfo.ProblemQualifyRate), 64)
			OneTableStatic(tancha_redev, engineinfo, &qualityinfo, id, qualityinfo.Database, problemsourcedb, problemtbname, qualityinfo.VerifyRecord)
			qualityinfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber + watchnum

			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 3
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err)
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				//qualityinfo.Duproles.RepetitionRate = qualityinfo.Duproles.RepetitionNumber / float64(qualityinfo.VerifyRecord)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate

			} else {
				qualityinfo.ProblemType = 2
			}
			//增加对质量分等指标的统计
			scoreinfos, _ = ScoreStatic(qualityinfo.Fieldinfo, qualityinfo.ProjectID, qualityinfo.RepetitionRate, watchruler, timelinessNum)
		} else {
			//不统计问题数据,查看是否统计重复数据
			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 1
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err.Error())
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate
				if watchruler {
					scoreinfos.Normative = scoreinfos.Normative + (100 - qualityinfo.RepetitionRate)
					scoreinfos.TableQualityScore = scoreinfos.Normative
				}
			}
		}
	} else {
		if rescount != 0 {
			qualityinfo.ProblemRecord = int(rescount)
			if cfcount != 0 {
				qualityinfo.ProblemType = 3
			} else {
				qualityinfo.ProblemType = 2
			}
		} else {
			if cfcount != 0 {
				qualityinfo.ProblemType = 1
			}
		}

	}
	//统计规则数
	//OneTableStatic(engineinfo, &qualityinfo, res, id, dbname, problemsourcedb, problemtbname, qualityinfo.VerifyRecord)
	//
	//_, err = serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, id)
	//var isexist bool
	//if err != nil {
	//	if strings.Contains(err.Error(), "Error 404") {
	//		isexist = true
	//	}
	//}
	qualityinfo.Id = id
	qualityinfo.Module = 14 //先加上，防止后期产品变卦，方便兼容历史表
	if !isexist {
		//存在记录
		//if rescount == 0 && cfcount == 0 {
		//	//删除历史数据
		//	esutil.DelByID(common.DataBaseMetaData, common.QualityProblem, id)
		//	return "成功", nil
		//}
		//var historytime, nowtime string
		//if gjson.Get(string(qualityexist), "explorationtime").String() != "" {
		//	nowtime = gjson.Get(string(qualityexist), "explorationtime").String()[:strings.LastIndex(gjson.Get(string(qualityexist), "explorationtime").String(), "-")]
		//}
		//e := gjson.Get(string(qualityexist), "historyloginfo").Array()

		qualityinfo.HistoryLogInfo = tq.QualityTableInfo.HistoryLogInfo
		var isupdate bool
		fmt.Println("====kaishile===")
		for i, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			fmt.Println("datastring:", v.YearMonth, dateString, id)
			if v.YearMonth == dateString {
				isupdate = true
				//更新1 +更新2
				qualityinfo.HistoryLogInfo[i].ProblemQualifyRate = qualityinfo.ProblemQualifyRate
				qualityinfo.HistoryLogInfo[i].ProblemRecord = qualityinfo.ProblemRecord
				qualityinfo.HistoryLogInfo[i].VerifyRecord = qualityinfo.VerifyRecord
				qualityinfo.HistoryLogInfo[i].VerifyRulerNumber = qualityinfo.VerifyRulerNumber
				qualityinfo.HistoryLogInfo[i].VerifyFieldNumber = qualityinfo.VerifyFieldNumber
				//qualityinfo.HistoryLogInfo[i].DupDataResource = qualityinfo.DupDataResource
				qualityinfo.HistoryLogInfo[i].Duproles = qualityinfo.Duproles
				qualityinfo.HistoryLogInfo[i].Fieldinfo = qualityinfo.Fieldinfo
				qualityinfo.HistoryLogInfo[i].RulerScore = scoreinfos
				qualityinfo.HistoryLogInfo[i].YearMonth = dateString
				qualityinfo.HistoryLogInfo[i].ProblemType = qualityinfo.ProblemType
				qualityinfo.HistoryLogInfo[i].RulerScoreIsZero = ScorceIsZero(qualityinfo.Fieldinfo, scoreinfos)
				if thisRule.Timeliness.TimelinessType != "" {
					qualityinfo.HistoryLogInfo[i].Timeliness = &thisRule.Timeliness
				} else {
					qualityinfo.HistoryLogInfo[i].Timeliness = nil
				}

				//添加表级时效性校验
				switch timelinessNum {
				case 0: //不在区间内
					qualityinfo.HistoryLogInfo[i].Timeliness.ShowTimeliness = false
				case 1:
					qualityinfo.HistoryLogInfo[i].Timeliness.Tabletimelinessres = true
					qualityinfo.HistoryLogInfo[i].Timeliness.ShowTimeliness = true
				case 2:
					qualityinfo.HistoryLogInfo[i].Timeliness.ShowTimeliness = true
				}
				if tasktype == 1 {
					qualityinfo.HistoryLogInfo[i].History = 1
				} else {
					qualityinfo.HistoryLogInfo[i].History = 0
				}
				fmt.Println("HistoryLogInfo:", i, qualityinfo.Fieldinfo)
				break
			}
		}
		if !isupdate {
			//没有对应年月记录
			//更新1 + 追加2
			fmt.Println("yunxingisupdate:", id)
			var loginfo source.HistoryLogInfo
			loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
			loginfo.ProblemRecord = qualityinfo.ProblemRecord
			loginfo.VerifyRecord = qualityinfo.VerifyRecord
			loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
			loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
			//loginfo.DupDataResource = qualityinfo.DupDataResource
			loginfo.Duproles = qualityinfo.Duproles
			loginfo.Fieldinfo = qualityinfo.Fieldinfo
			loginfo.RulerScore = scoreinfos
			loginfo.YearMonth = dateString
			loginfo.ProblemType = qualityinfo.ProblemType
			loginfo.RulerScoreIsZero = ScorceIsZero(qualityinfo.Fieldinfo, scoreinfos)
			if tasktype == 1 {
				loginfo.History = 1
			} else {
				loginfo.History = 0
			}
			qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		}
		err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.QualityProblem, "true", id, qualityinfo)
	} else {
		//不存在
		//if rescount == 0 && cfcount == 0 {
		//	return "成功", nil
		//}
		//添加1-最外层数据+ 添加2-内部日志记录
		var loginfo source.HistoryLogInfo
		loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
		loginfo.ProblemRecord = qualityinfo.ProblemRecord
		loginfo.VerifyRecord = qualityinfo.VerifyRecord
		loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
		loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
		//loginfo.DupDataResource = qualityinfo.DupDataResource
		loginfo.Duproles = qualityinfo.Duproles
		loginfo.Fieldinfo = qualityinfo.Fieldinfo
		loginfo.RulerScore = scoreinfos
		loginfo.YearMonth = dateString
		loginfo.ProblemType = qualityinfo.ProblemType
		loginfo.RulerScoreIsZero = ScorceIsZero(qualityinfo.Fieldinfo, scoreinfos)
		if tasktype == 1 {
			loginfo.History = 1
		} else {
			loginfo.History = 0
		}
		qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		err = esutil.AddWithAimID(common.DataBaseMetaData, common.QualityProblem, id, qualityinfo)
		if err != nil {
			fmt.Println("addes:", err.Error())
		}
	}
	if err != nil {
		logger.Error.Println(err)
		return "失败", err
	}
	return "成功", nil
}
func ScorceIsZero(fieldinfo []source.ProblemFieldInfo, scoreinfos source.RulerScore) source.RulerScoreIsZero {
	var ruleisexist = make(map[string]bool, 0)
	var sourceinfo source.RulerScoreIsZero
	var tableQualityScore bool
	for _, v := range fieldinfo {
		for _, ruler := range v.QualityDimension {
			ruleisexist[ruler] = true
		}
	}
	if scoreinfos.Accuracy == 0 {
		if _, ok := ruleisexist["准确性"]; ok {
			sourceinfo.AccuracyIsZero = true
			tableQualityScore = true
		} else {
			sourceinfo.AccuracyIsZero = false
		}
	}
	if scoreinfos.Accessibility == 0 {
		if _, ok := ruleisexist["可访问性"]; ok {
			sourceinfo.AccessibilityIsZero = true
			tableQualityScore = true
		} else {
			sourceinfo.AccessibilityIsZero = false
		}
	}
	if scoreinfos.Timeliness == 0 {
		if _, ok := ruleisexist["时效性"]; ok {
			sourceinfo.TimelinessIsZero = true
			tableQualityScore = true
		} else {
			sourceinfo.TimelinessIsZero = false
		}
	}
	if scoreinfos.Normative == 0 {
		if _, ok := ruleisexist["规范性"]; ok {
			sourceinfo.NormativeIsZero = true
			tableQualityScore = true
		} else {
			sourceinfo.NormativeIsZero = false
		}
	}
	if scoreinfos.Integrity == 0 {
		if _, ok := ruleisexist["完整性"]; ok {
			sourceinfo.IntegrityIsZero = true
			tableQualityScore = true
		} else {
			sourceinfo.IntegrityIsZero = false
		}
	}
	if scoreinfos.Uniformity == 0 {
		if _, ok := ruleisexist["一致性"]; ok {
			sourceinfo.UniformityIsZero = true
			tableQualityScore = true
		} else {
			sourceinfo.UniformityIsZero = false
		}
	}

	if scoreinfos.TableQualityScore == 0 {
		if tableQualityScore {
			sourceinfo.TableQualityScoreIsZero = true
		} else {
			sourceinfo.TableQualityScoreIsZero = false
		}
	}
	return sourceinfo
}
func ScoreStatic(info []source.ProblemFieldInfo, projectid string, repetitionrate float64, watchruler bool, timelinessNum int) (source.RulerScore, error) {
	var socres source.RulerScore
	res, err := esutil.SearchByID(common.DataBaseLab, common.TbQualityConf, projectid)
	if err != nil {
		logger.Error.Printf("查询表元数据信息失败:%s", err.Error())
		return socres, err
	}
	gfwd := gjson.Get(res, "gfwd").Int()   //规范性权重
	wzwd := gjson.Get(res, "wzwd").Int()   //完整性权重
	zqwd := gjson.Get(res, "zqwd").Int()   //准确性权重
	yzwd := gjson.Get(res, "yzwd").Int()   //一致性权重
	sxwd := gjson.Get(res, "sxwd").Int()   //时效性权重
	kfwwd := gjson.Get(res, "kfwwd").Int() //可访问性权重
	var sumwd int64
	scoreinfo := make(map[string][]float64)
	for _, fscore := range info {
		for j, r := range fscore.QualityDimension {
			score, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(fscore.VerifyRecord[j]-fscore.ProblemRecord[j])/float64(fscore.VerifyRecord[j])*100), 64)
			scoreinfo[r] = append(scoreinfo[r], score)
		}
	}
	if _, ok := scoreinfo["规范性"]; ok {
		for _, k := range scoreinfo["规范性"] {
			socres.Normative += k
		}
		// if watchruler {
		// 	socres.Normative = socres.Normative + (100 - repetitionrate)
		// 	socres.Normative, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Normative/float64(len(scoreinfo["规范性"])+1)), 64)
		// } else {
		// 	socres.Normative, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Normative/float64(len(scoreinfo["规范性"]))), 64)
		// }
		socres.Normative, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Normative/float64(len(scoreinfo["规范性"]))), 64)
		if socres.Normative != 0 {
			socres.TableQualityScore = socres.TableQualityScore + socres.Normative*float64(gfwd)
			sumwd += gfwd
		}
	}
	if _, ok := scoreinfo["准确性"]; ok {
		for _, k := range scoreinfo["准确性"] {
			socres.Accuracy += k
		}
		if watchruler {
			socres.Accuracy = socres.Accuracy + (100 - repetitionrate)
			socres.Accuracy, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Accuracy/float64(len(scoreinfo["准确性"])+1)), 64)
		} else {
			socres.Accuracy, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Accuracy/float64(len(scoreinfo["准确性"]))), 64)
		}
		//socres.Accuracy, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Accuracy/float64(len(scoreinfo["准确性"]))), 64)
		if socres.Accuracy != 0 {
			socres.TableQualityScore = socres.TableQualityScore + socres.Accuracy*float64(zqwd)
			sumwd += zqwd
		}
	}
	if _, ok := scoreinfo["完整性"]; ok {
		for _, k := range scoreinfo["完整性"] {
			socres.Integrity += k
		}
		socres.Integrity, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Integrity/float64(len(scoreinfo["完整性"]))), 64)
		if socres.Integrity != 0 {
			socres.TableQualityScore = socres.TableQualityScore + socres.Integrity*float64(wzwd)
			sumwd += wzwd
		}
	}
	if _, ok := scoreinfo["时效性"]; ok {
		thisTimeLinessNum := 0
		isWholeTableTL := 0
		switch timelinessNum {
		case 0, 1:
			isWholeTableTL = 1
			thisTimeLinessNum = 100
		case 2:
			isWholeTableTL = 1
		}
		for _, k := range scoreinfo["时效性"] {
			socres.Timeliness += k
		}
		socres.Timeliness = socres.Timeliness + float64(thisTimeLinessNum)
		socres.Timeliness, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Timeliness/float64(len(scoreinfo["时效性"])+isWholeTableTL)), 64)
		if socres.Timeliness != 0 {
			socres.TableQualityScore = socres.TableQualityScore + socres.Timeliness*float64(sxwd)
			sumwd += sxwd
		}
	} else {
		switch timelinessNum {
		case 0, 1:
			socres.Timeliness = 100
		}
	}
	if _, ok := scoreinfo["可访问性"]; ok {
		for _, k := range scoreinfo["可访问性"] {
			socres.Accessibility += k
		}
		socres.Accessibility, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Accessibility/float64(len(scoreinfo["可访问性"]))), 64)
		if socres.Accessibility != 0 {
			socres.TableQualityScore = socres.TableQualityScore + socres.Accessibility*float64(kfwwd)
			sumwd += kfwwd
		}
	}
	if _, ok := scoreinfo["一致性"]; ok {
		for _, k := range scoreinfo["一致性"] {
			socres.Uniformity += k
		}
		socres.Uniformity, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.Uniformity/float64(len(scoreinfo["一致性"]))), 64)
		if socres.Uniformity != 0 {
			socres.TableQualityScore = socres.TableQualityScore + socres.Uniformity*float64(yzwd)
			sumwd += yzwd
		}
	}
	//表质量分
	if sumwd != 0 {
		socres.TableQualityScore, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", socres.TableQualityScore/float64(sumwd)), 64)
	}
	return socres, nil
}
func SqlDataTransCount(restxt source.MGCommonRes, enginetype string) float64 {
	fmt.Println("enginetype:", enginetype)
	var rescount float64
	switch enginetype {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["count"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	case common.HIVE, common.Inceptor:
		//先按照teryx
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["_c0"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	case common.ODPS:
		//先按照teryx
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["count"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	case common.DMDB:
		fmt.Println("SqlDataTransCount4:", restxt.Data)
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["COUNT(*)"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	}

	return rescount
}

func HiveLikeCount(restxt source.MGCommonRes, enginetype string) float64 {
	fmt.Println("hiveenginetype:", enginetype)
	var rescount float64
	switch enginetype {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		fmt.Println("SqlDataTransCount:", restxt.Data)
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["count"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	case common.HIVE, common.Inceptor:
		fmt.Println("SqlDataTransCount2:", restxt.Data)
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["_c0"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	case common.ODPS:
		//先按照teryx
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["count"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	case common.DMDB:
		fmt.Println("SqlDataTransCount4:", restxt.Data)
		if len(restxt.Data) == 0 {
			rescount = 0
		} else {
			count2 := restxt.Data[0]["COUNT(*)"]
			if _, ok := count2.(float64); ok {
				rescount = count2.(float64)
			}
		}
	}

	return rescount
}
func SearchDev(sourceid, projectid string, num int) ([]byte, error) {
	var musts []interface{}
	term1 := gin.H{}
	if num == 0 {
		term1 = gin.H{
			"term": gin.H{
				"module": 14,
			},
		}
	} else if num == 1 {
		term1 = gin.H{
			"term": gin.H{
				"module": 6,
			},
		}
		problemterm := gin.H{
			"term": gin.H{
				"cookinginput.duproles.problem": true,
			},
		}
		musts = append(musts, problemterm)
	} else {
		term1 = gin.H{
			"term": gin.H{
				"module": 7,
			},
		}
	}

	term2 := gin.H{
		"term": gin.H{
			"cookinginput.metadataid": sourceid,
		},
	}
	term3 := gin.H{
		"term": gin.H{
			"projectid": projectid,
		},
	}

	musts = append(musts, term1, term2, term3)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnLimitSize,
	}
	strbody, _ := json.Marshal(body)
	fmt.Println("strbodydev1:", string(strbody))
	re, err := esutil.SearchByTerm(common.DataBaseLab, common.TbDev, body)
	if err != nil {
		logger.Error.Println(err)
		return re, err
	}
	return re, nil
}
func SearchTbAccess(sourceid string) ([]byte, error) {
	var musts []interface{}
	term1 := gin.H{
		"term": gin.H{
			"sourcedb.tableid": sourceid,
		},
	}
	musts = append(musts, term1)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
		"size": common.UnLimitSize,
	}
	strbody, _ := json.Marshal(body)
	fmt.Println("strbodytest:", string(strbody))
	re, err := esutil.SearchByTerm(common.DataBaseLab, common.TbAccess, body)
	if err != nil {
		fmt.Println("strbodytest(err):", err.Error())
		logger.Error.Println(err)
		return re, err
	}
	fmt.Println("strbodytest(re):", string(re))
	return re, nil
}
func OneTableStatic(re []byte, engineinfo source.EngineInfo, pro *source.QualityTableInfo, sourceid, dbname, problemsourcedb, problemtbname string, normalcount int) error {
	//re, err := SearchDev(sourceid)
	//var musts []interface{}
	//term1 := gin.H{
	//	"term": gin.H{
	//		"module": 14,
	//	},
	//}
	//term2 := gin.H{
	//	"term": gin.H{
	//		"cookinginput.metadataid": sourceid,
	//	},
	//}
	//musts = append(musts, term1, term2)
	//body := gin.H{
	//	"query": gin.H{
	//		"bool": gin.H{
	//			"must": musts,
	//		},
	//	},
	//	"size": common.UnLimitSize,
	//}
	//strbody, _ := json.Marshal(body)
	//fmt.Println("strbody1:", string(strbody))
	//re, err := esutil.SearchByTerm(common.DataBaseLab, common.TbDev, body)
	//if err != nil {
	//	logger.Error.Println(err)
	//	return err
	//}
	res, _ := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, sourceid)
	//var isexist bool
	//if err != nil {
	//	if strings.Contains(err.Error(), "Error 404") {
	//		isexist = true
	//	}
	//}

	var hitsinfo source.QualityHits3
	json.Unmarshal(res, &hitsinfo)
	logger.Info.Println("查询问题数据失败res:", string(res), hitsinfo)
	fmt.Println("tancha:", gjson.Get(string(re), "hits.total").Int())

	e := gjson.Get(string(re), "hits.hits").Array()
	//核验字段数、核验规则数、 最近探查时间

	var problems []source.ProblemFieldInfo
	//第一次 无探查规则   有探查规则
	//第二次 无探查规则   有探查规则
	//更新统计
	for _, v := range e {
		//cookinginput := v.Get("cookinginput").Array()
		//for _, c := range cookinginput {
		//	c.Get()
		//}
		fieldinfos := v.Get("_source.fieldinfos").Array()
		var verifyfieldnum int
		fmt.Println("fieldinfos:", len(fieldinfos))
		//数据开发规则配置源
		var ruleids []string
		for _, c := range fieldinfos {
			var problem source.ProblemFieldInfo
			problem.Name = c.Get("field").String()
			problem.FieldType = c.Get("fieldtype").String()
			problem.Annotation = c.Get("fieldcomment").String()
			fieldrule := c.Get("fieldrule").Array()
			if len(fieldrule) != 0 {
				verifyfieldnum++
				for _, ruler := range fieldrule {
					problem.RulerId = append(problem.RulerId, ruler.Get("id").String())
					ruleids = append(ruleids, ruler.Get("id").String())
					problem.ExploratoryRule = append(problem.ExploratoryRule, ruler.Get("name").String())
					problem.RuleType = append(problem.QualityDimension, ruler.Get("ruletype").String())
					problem.QualityDimension = append(problem.QualityDimension, ruler.Get("ruledimension").String())
					pronum, _ := RulerStaticTable(engineinfo, dbname, problemsourcedb, problemtbname, "wt_"+problem.Name, ruler.Get("id").String())
					problem.ProblemRecord = append(problem.ProblemRecord, pronum)
					problem.VerifyRecord = append(problem.VerifyRecord, normalcount)
					var qualifyrate float64
					if normalcount != 0 {
						qualifyrate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", 100-float64(pronum)/float64(normalcount)*100), 64)
					} else {
						qualifyrate = 0
					}
					problem.QualifyRate = append(problem.QualifyRate, qualifyrate)
				}
			}
			fmt.Println("problem", problem)
			problems = append(problems, problem)
		}
		pro.VerifyFieldNumber = verifyfieldnum
		pro.VerifyRulerNumber = len(tools.RemoveRepeatedElement(ruleids))
		break
	}
	//var sourcearr []source.ProblemFieldInfo
	//if !isexist {
	//	sourcearr = hitsinfo.QualityTableInfo.Fieldinfo
	//}

	//logger.Info.Println("sourcearr:", len(sourcearr))
	//if len(sourcearr) != 0 {
	//	//赋值给pro
	//	pro.Fieldinfo = sourcearr
	//	//对比后，只添加新规则
	//	for _, new := range problems { //数据开发配置的规则
	//		// name age sex,sex2    age sex
	//		for i, old := range sourcearr { //问题数据存储的旧规则
	//			if old.Name == new.Name {
	//				//有这个规则,直接更新
	//				pro.Fieldinfo[i] = new
	//			} else {
	//				if i == len(sourcearr)-1 {
	//					//没有这个规则，则追加
	//					pro.Fieldinfo = append(pro.Fieldinfo, new)
	//				}
	//			}
	//		}
	//
	//	}
	//} else {
	//	//添加
	//	pro.Fieldinfo = problems
	//}
	pro.Fieldinfo = problems
	return nil
}
func RulerStaticTable(engineinfo source.EngineInfo, dbname, problemsourcedb, problemtbname, fieldname, rulerid string) (int, error) {
	var sqltxt string
	switch engineinfo.EngineName {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" where "%s" like '%%%s%%' `, problemsourcedb, problemtbname, fieldname, rulerid)
	case common.HIVE, common.Inceptor:
		sqltxt = fmt.Sprintf("select count(*) from `%s.%s`  where `%s` like '%%%s%%'", problemsourcedb, problemtbname, fieldname, rulerid)
	case common.ODPS:
		sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` where `%s` like '%%%ss%%'", problemsourcedb, problemtbname, fieldname, rulerid)
	case common.DMDB:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" where "%s" like "%%%s%%"`, problemsourcedb, problemtbname, fieldname, rulerid)
	}
	fmt.Println("rulesql:", sqltxt)
	restxt, err := collect.MGExecuteQuerySqlV2(engineinfo, dbname, sqltxt)
	if err != nil {
		logger.Error.Println(err)
		return 0, err
	}
	count := HiveLikeCount(restxt, engineinfo.EngineName)
	//count := SqlDataTransCount(restxt, engineinfo.EngineName)
	return int(count), nil
}
func OneTableStaticRepitation(engineinfo source.EngineInfo, dbname, problemsourcedb, cf_tbname string) (float64, error) {
	var sqltxt string
	switch engineinfo.EngineName {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s"  `, problemsourcedb, cf_tbname)
	case common.HIVE, common.Inceptor:
		sqltxt = fmt.Sprintf("select count(*) from `%s.%s` ", problemsourcedb, cf_tbname)
	case common.ODPS:
		sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, cf_tbname)
	case common.DMDB:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s"`, problemsourcedb, cf_tbname)
	}
	restxt, err := collect.MGExecuteQuerySqlV2(engineinfo, dbname, sqltxt)
	if err != nil {
		logger.Error.Println(err)
		return 0, err
	}
	count := SqlDataTransCount(restxt, engineinfo.EngineName)
	return count, nil
}
func QualityProblemDetail(protables source.QualityDataInfo) (source.ProblemDetailMonthInfo, error) {
	var qualityinfo source.QualityTableInfo
	var detailinfo source.ProblemDetailMonthInfo
	res, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, protables.Id)
	if err != nil {
		logger.Error.Println(err)
		return detailinfo, err
	}
	err = json.Unmarshal([]byte(res), &qualityinfo)
	//detailinfo.Fieldnum = qualityinfo.Fieldnum
	//detailinfo.EngineName = qualityinfo.Enginename
	//detailinfo.EngineType = qualityinfo.Enginetype
	//detailinfo.SourceDBname = qualityinfo.ExtractDbname
	//detailinfo.SourceTable = qualityinfo.ExtractTable
	//detailinfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
	//detailinfo.VerifyRuleNumber = qualityinfo.VerifyRulerNumber
	//detailinfo.VerifyRecord = qualityinfo.VerifyRecord
	//detailinfo.ProblemRecord = qualityinfo.ProblemRecord
	//detailinfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
	//detailinfo.Duproles = qualityinfo.Duproles
	//detailinfo.Fieldinfo = qualityinfo.Fieldinfo
	detailinfo.Fieldnum = qualityinfo.Fieldnum
	detailinfo.EngineName = qualityinfo.Enginename
	detailinfo.EngineType = qualityinfo.Enginetype
	detailinfo.SourceDBname = qualityinfo.ExtractDbname
	detailinfo.SourceTable = qualityinfo.ExtractTable
	if protables.ProblemVersion == "" {
		detailinfo.HistoryLogInfo = qualityinfo.HistoryLogInfo
		for _, v := range qualityinfo.HistoryLogInfo {
			detailinfo.Historytime = append(detailinfo.Historytime, v.YearMonth)
		}
	} else {
		for _, v := range qualityinfo.HistoryLogInfo {
			var historyinfo source.HistoryLogInfo
			if v.YearMonth == protables.ProblemVersion {
				historyinfo.Fieldinfo = v.Fieldinfo
				historyinfo.Duproles = v.Duproles
				historyinfo.ProblemRecord = v.ProblemRecord
				historyinfo.VerifyRecord = v.VerifyRecord
				historyinfo.ProblemQualifyRate = v.ProblemQualifyRate
				historyinfo.VerifyRulerNumber = v.VerifyRulerNumber
				historyinfo.VerifyFieldNumber = v.VerifyFieldNumber
				historyinfo.RulerScore = v.RulerScore
				historyinfo.History = v.History
				historyinfo.RulerScoreIsZero = ScorceIsZero(v.Fieldinfo, v.RulerScore)
				detailinfo.HistoryLogInfo = append(detailinfo.HistoryLogInfo, historyinfo)
			}
			detailinfo.Historytime = append(detailinfo.Historytime, v.YearMonth)
		}
	}
	compareFn := func(i, j int) bool {
		// 将字符串解析为时间对象
		timeI, _ := time.Parse("2006-01", detailinfo.Historytime[i])
		timeJ, _ := time.Parse("2006-01", detailinfo.Historytime[j])

		// 按照时间对象的降序进行比较
		return timeI.Before(timeJ)
	}

	// 使用自定义的比较函数进行排序
	sort.Slice(detailinfo.Historytime, compareFn)
	return detailinfo, nil
}
func QualityExport(id, problemversion string) (string, error) {
	var filename string
	sourceres, err := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaTable, id)
	if err != nil {
		logger.Error.Println(err)
		return filename, err
	}
	fieldinfo := gjson.Get(sourceres, "fieldinfo").Array()

	var fieldinfoarr, describearr []string
	for _, v := range fieldinfo {
		name := v.Get("name").String()
		describe := v.Get("annotation").String()
		fieldinfoarr = append(fieldinfoarr, name)
		describearr = append(describearr, describe)
	}
	//res, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, id)
	qualityinfo, err := SearchProbleTable(id)
	if err != nil {
		logger.Error.Println(err)
		return filename, err
	}
	dbname := qualityinfo.Database
	sourcedb := qualityinfo.SourceDb
	tbname := qualityinfo.Tbname
	problemtype := qualityinfo.ProblemType
	projectid := qualityinfo.ProjectID
	problemsourcedb := projectid + "_wt"
	engineid := qualityinfo.Engineid
	engineinfo, err := dbutil.EngineInfo(engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return filename, err
	}
	module := gjson.Get(sourceres, "module").Int()
	if module == 7 {
		//质量探查--没有按月归档功能
		problemtable := tbname
		//将最新记录的problemtype作为最终依据
		findMaxTime := func(timestrs []source.HistoryLogInfo) (int, error) {
			var problemtype int
			maxTime, err := time.Parse("2006-01", timestrs[0].YearMonth)
			if err != nil {
				return problemtype, err
			}
			for _, t_str := range timestrs {
				t, err := time.Parse("2006-01", t_str.YearMonth)
				if err != nil {
					return problemtype, err
				}
				if t.After(maxTime) {
					maxTime = t
					problemtype = t_str.ProblemType
				}
			}
			return problemtype, nil
		}
		problemtype, _ = findMaxTime(qualityinfo.HistoryLogInfo)
		filename, err = CommonGetTanchaTableInfo2(id, engineinfo, int(problemtype), dbname, sourcedb, tbname, problemsourcedb, problemtable, fieldinfoarr, describearr)
		if err != nil {
			logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
			return filename, err
		}
	} else {
		//质量监控
		var problemtable string
		for _, v := range qualityinfo.HistoryLogInfo {
			if v.YearMonth == problemversion {
				//fieldruler := gjson.Get(res, "fieldinfo").Array()
				if v.History == 1 {
					problemtable = tbname + "_" + strings.Replace(v.YearMonth, "-", "", -1)
					problemtype = v.ProblemType
				}
			}
		}

		//filename, err = CommonGetTableInfo(engineinfo, int(problemtype), dbname, sourcedb, tbname, problemsourcedb, fieldinfos, fieldinfoarr)
		//if err != nil {
		//	logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		//	return filename, err
		//}
		filename, err = CommonGetTableInfo2(id, engineinfo, int(problemtype), dbname, sourcedb, tbname, problemsourcedb, problemtable, fieldinfoarr, describearr)
		if err != nil {
			logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
			return filename, err
		}
	}
	return filename, nil
}
func GetAfterMonth(queryMonth string, s int) []string {
	var result []string
	layout := "2006-01"
	startDate, _ := time.Parse(layout, queryMonth)
	for i := 1; i <= s; i++ {
		previousMonth := startDate.AddDate(0, i, 0)
		result = append(result, previousMonth.Format(layout))
	}
	return result
}

func BatchCommon(ids []string, needwhere bool, month string) ([]source.BatchProblem, error) {

	var nextmonth string
	if needwhere {
		tempList := GetAfterMonth(month, 1)
		if tempList != nil {
			nextmonth = tempList[0]
		}
	}

	var infos []source.BatchProblem
	var sqltxt, cf_sqltxt string
	for _, id := range ids {
		var info source.BatchProblem
		info.ID = id
		info.Extract, _ = SearchTbAccess(id)
		fmt.Println("id:", id)
		var qualityinfo source.QualityTableInfo
		res, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, id)
		if err != nil {
			fmt.Println("查询问题数据失败2:", err.Error())
			return infos, err
		}
		err = json.Unmarshal([]byte(res), &qualityinfo)
		if err != nil {
			return infos, err
		}
		var problemtype, history int
		for _, v := range qualityinfo.HistoryLogInfo {
			if month == "" {
				if v.YearMonth == qualityinfo.ExplorationTime[:strings.LastIndex(qualityinfo.ExplorationTime, "-")] {
					if v.History == 0 {
						history = 0
					} else {
						history = 1
					}
					info.Fieldinfo = v.Fieldinfo
					problemtype = v.ProblemType
					month = v.YearMonth
				}
			} else {
				if v.YearMonth == month {
					if v.History == 0 {
						history = 0
					} else {
						history = 1
					}
					info.Fieldinfo = v.Fieldinfo
					problemtype = v.ProblemType
				}
			}

		}
		//info.Fieldinfo = gjson.Get(res, "fieldinfo").Array()
		sourceres, err := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaTable, id)
		if err != nil {
			fmt.Println("search metaid error:", err)
			continue
		}
		sourcefieldinfo := gjson.Get(sourceres, "fieldinfo").Array()
		var fieldinfos string
		for _, v := range sourcefieldinfo {
			name := v.Get("name").String()
			if len(fieldinfos) == 0 {
				fieldinfos = name
			} else {
				fieldinfos = fieldinfos + "," + name
			}
			info.Fieldinfoarr = append(info.Fieldinfoarr, name)
			info.CfFieldinfoarr = append(info.CfFieldinfoarr, name)
			describe := v.Get("annotation").String()
			info.DescribeArr = append(info.DescribeArr, describe)
		}
		engineid := gjson.Get(res, "engineid").String()
		engineinfo, err := dbutil.EngineInfo(engineid)
		if err != nil {
			fmt.Println("查询ODS Layer Engine Info信息失败：", err.Error())
			return infos, err
		}
		info.EngineInfo = engineinfo
		info.Dbname = gjson.Get(res, "database").String()
		sourcedb := gjson.Get(res, "sourcedb").String()
		info.Tbname = gjson.Get(res, "tbname").String()
		//problemtype := gjson.Get(res, "problemtype").Int()
		projectid := gjson.Get(res, "projectid").String()
		problemsourcedb := projectid + "_wt"
		if problemtype == 1 {
			//重复数据
			var cf_tbname string
			if history == 0 {
				cf_tbname = "cf_" + sourcedb + "_" + info.Tbname
			} else {
				cf_tbname = "cf_" + sourcedb + "_" + info.Tbname + "_" + strings.ReplaceAll(month, "-", "")
			}
			info.CfTable = cf_tbname
			info.CfFieldinfoarr = info.Fieldinfoarr
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "cf_rksj" < '%s' and "cf_rksj" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.HIVE, common.Inceptor:
				cf_sqltxt = fmt.Sprintf("select * from `%s`.`%s`", problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.ODPS:
				cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s`.`%s` ", problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.DMDB:
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "CF_RKSJ" < '%s' and "CF_RKSJ" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "CF_RKSJ")

			}
			info.DescribeArr = append(info.DescribeArr, "")
			info.Cfsql = cf_sqltxt + " limit 10000"

		} else if problemtype == 2 {
			//问题数据
			//需要访问获取全部数据
			//根据每行的dwd_ruleid 中的id 找到
			var problemtbname string
			if history == 0 {
				problemtbname = sourcedb + "_" + info.Tbname
			} else {
				problemtbname = sourcedb + "_" + info.Tbname + "_" + strings.ReplaceAll(month, "-", "")
			}
			info.Tbname = problemtbname
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf(`%s where "dwd_rksj" < '%s' and "dwd_rksj" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
			case common.HIVE, common.Inceptor:
				//sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
				sqltxt = fmt.Sprintf("select * from `%s`.`%s` ", problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
			case common.ODPS:
				sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s`.`%s` ", problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
			case common.DMDB:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf(`%s where "DWD_RKSJ" < '%s' and "DWD_RKSJ" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "DWD_RKSJ")
			}
			info.DescribeArr = append(info.DescribeArr, "")
			info.Problemsql = sqltxt + " limit 10000"
			info.WtTable = problemtbname
		} else if problemtype == 3 {
			//重复数据、问题数据都有
			var problemtbname, cf_tbname string
			if history == 0 {
				problemtbname = sourcedb + "_" + info.Tbname
				cf_tbname = "cf_" + sourcedb + "_" + info.Tbname
			} else {
				problemtbname = sourcedb + "_" + info.Tbname + "_" + strings.ReplaceAll(month, "-", "")
				cf_tbname = "cf_" + sourcedb + "_" + info.Tbname + "_" + strings.ReplaceAll(month, "-", "")
			}
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "cf_rksj" < '%s' and "cf_rksj" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf(`%s where "dwd_rksj" < '%s' and "dwd_rksj" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.HIVE, common.Inceptor:
				sqltxt = fmt.Sprintf("select * from `%s`.`%s` ", problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf("select * from `%s`.`%s`", problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.ODPS:
				sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.DMDB:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "CF_RKSJ" < '%s' and "CF_RKSJ" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf(`%s where "DWD_RKSJ" < '%s' and "DWD_RKSJ" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "DWD_RKSJ")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "CF_RKSJ")
			}
			info.DescribeArr = append(info.DescribeArr, "")
			info.Cfsql = cf_sqltxt + " limit 10000"
			info.Problemsql = sqltxt + " limit 10000"
			info.WtTable = problemtbname
			info.CfTable = cf_tbname
		}
		infos = append(infos, info)
	}
	return infos, nil
}
func BatchRepatiton(info source.BatchProblem, file *xlsx.File) error {
	res, err := collect.MGExecuteSelectSql(info.EngineInfo, info.Dbname, info.Cfsql, common.Rows)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", info.Cfsql)
		//logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", info.Cfsql)
		return err
	}
	fmt.Println("==========test2=======", res.Data, info.Cfsql)
	var rsuid string
	for _, v := range res.Data {
		for k2, v2 := range v {
			if k2 == "rsUid" {
				fmt.Println("k2,v2:", k2, v2)
				rsuid = fmt.Sprintf("%v", v2)
				break
			}
		}
	}
	var number int = 2

	sheetName := info.Tbname
	if len(sheetName) > 31 {
		sheetName = sheetName[:31]
	}
	index, _ := file.NewSheet(sheetName)
	file.SetActiveSheet(index)
	sw, err := file.NewStreamWriter(sheetName)
	if err != nil {
		fmt.Println(err)
		return err
	}
	style, err := file.NewStyle(&xlsx.Style{
		Alignment: &xlsx.Alignment{
			Horizontal: "left",
			Vertical:   "center",
		},
	})

	// 获取当前活动工作表名称
	sheetName = file.GetSheetName(file.GetActiveSheetIndex())
	var mySlice []interface{}

	for _, header := range info.CfFieldinfoarr {
		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
	}
	cell, _ := xlsx.CoordinatesToCellName(1, 1)
	err = sw.SetRow(cell, mySlice)
	if err != nil {
		fmt.Println("写入表头失败err:", err)
		return err
	}
	for {
		res2, err := collect.MGSelectSqlResult(info.EngineInfo, info.Dbname, rsuid)
		if err != nil {
			fmt.Println("test2:", err.Error())
		}
		_, err = json.Marshal(res2)
		if err != nil {
			fmt.Println("zhuanhuan:", err.Error())
		}
		if len(res2.Data) == 0 {
			break
		}
		//数据处理---
		number, err = RepetitionWriteDataToFile2(res2.Data, file, number, style, sw, info.CfFieldinfoarr, info.EngineInfo.EngineName, info.CfTable)
		if err != nil {
			fmt.Println("zhuanhuan:", err.Error())
		}
	}
	// 保存文件
	if err := sw.Flush(); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}
func BatchProblem(info source.BatchProblem, file *xlsx.File) error {
	res, err := collect.MGExecuteSelectSql(info.EngineInfo, info.Dbname, info.Problemsql, common.Rows)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", info.Problemsql)
		//logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", info.Problemsql)
		return err
	}
	fmt.Println("==========Problemsql:=======", res.Data, info.Problemsql)
	var rsuid string
	for _, v := range res.Data {
		for k2, v2 := range v {
			if k2 == "rsUid" {
				fmt.Println("k2,v2:", k2, v2)
				rsuid = fmt.Sprintf("%v", v2)
				break
			}
		}
	}
	var number int = 2

	sheetName := info.Tbname
	if len(sheetName) > 31 {
		sheetName = sheetName[:31]
	}
	index, _ := file.NewSheet(sheetName)
	file.SetActiveSheet(index)
	sw, err := file.NewStreamWriter(sheetName)
	if err != nil {
		fmt.Println(err)
		return err
	}
	style, err := file.NewStyle(&xlsx.Style{
		Alignment: &xlsx.Alignment{
			Horizontal: "left",
			Vertical:   "center",
		},
	})

	// 获取当前活动工作表名称
	sheetName = file.GetSheetName(file.GetActiveSheetIndex())
	var mySlice []interface{}

	for _, header := range info.Fieldinfoarr {
		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
	}
	mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: "wtjl"})
	cell, _ := xlsx.CoordinatesToCellName(1, 1)
	err = sw.SetRow(cell, mySlice)
	if err != nil {
		fmt.Println("写入表头失败err:", err)
		return err
	}
	//

	//展示问题记录数
	var problemrecord int64
	var proComment string
	proStr, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, info.ID)
	if err != nil {
		logger.Error.Println(err)
	}
	problemrecord = gjson.Get(proStr, "problemrecord").Int()
	var descSlice []interface{}

	for _, describe := range info.DescribeArr {
		descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
	}
	if problemrecord > 10000 {
		proComment = fmt.Sprintf(`共有%d条，仅展示10000条`, problemrecord)
	}
	descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: "问题记录 " + proComment})
	desccell, _ := xlsx.CoordinatesToCellName(1, 2)
	fmt.Println("ziduanxinx:", info.DescribeArr, info.Fieldinfoarr)
	err = sw.SetRow(desccell, descSlice)
	if err != nil {
		fmt.Println("写入表头失败err:", err)
		return err
	}
	//
	for {
		res2, err := collect.MGSelectSqlResult(info.EngineInfo, info.Dbname, rsuid)
		if err != nil {
			fmt.Println("test2:", err.Error())
		}
		_, err = json.Marshal(res2)
		if err != nil {
			fmt.Println("zhuanhuan:", err.Error())
		}
		if len(res2.Data) == 0 {
			break
		}
		//数据处理---
		if info.MiddleTableType != 1 {
			number, _ = CombData(res2.Data, info.Extract, sw, style, number, info.Fieldinfoarr, info.EngineInfo.EngineName, info.WtTable)
			if err != nil {
				fmt.Println("质量问题表数据转换:", err.Error())
			}
		} else {
			number, _ = CombMiddleData(res2.Data, info.Extract, sw, style, number, info.Fieldinfoarr, info.EngineInfo.EngineName, info.WtTable)
			if err != nil {
				fmt.Println("中间表数据转换:", err.Error())
			}
		}

	}
	// 保存文件
	if err := sw.Flush(); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}
func QualityDelProblemInfo(id string) error {
	err := esutil.DelByID(common.DataBaseMetaData, common.QualityProblem, id)
	if err != nil {
		return err
	}
	return nil
}
func GetQualityDate(info source.QualityDataInfo) ([]string, error) {
	var yearmonth []string
	res, err := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, info.Id)
	if err != nil {
		logger.Error.Printf("查询治理任务信息失败:%s, %s", err.Error())
		return yearmonth, err
	}
	e := gjson.Get(string(res), "_source.historyloginfo").Array()
	for _, v := range e {
		yearmonth = append(yearmonth, v.Get("yearmonth").String())
	}
	return yearmonth, nil

}
func SearchProbleTable(id string) (source.QualityTableInfo, error) {
	var qualityinfo source.QualityTableInfo
	res, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, id)
	if err != nil {
		logger.Error.Println(err)
		return qualityinfo, err
	}
	err = json.Unmarshal([]byte(res), &qualityinfo)
	if err != nil {
		return qualityinfo, err
	}
	return qualityinfo, nil
}
func QualityDataDelete(info source.DeleteTableInfo, fieldinfo map[string]interface{}, qualityinfo source.QualityTableInfo) error {
	var sqltxt string
	engineinfo, err := dbutil.EngineInfo(qualityinfo.Engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
	}

	var drealdbCatalog string
	var queryEngine source.EngineInfo
	if common.DRealDbDetail.IP != "" && engineinfo.EngineName == common.HIVE {
		drealdbCatalog = "`" + common.DRealDbDetail.CatalogName + "`."
		queryEngine = GetDrealDbEngine(engineinfo.EngineID)
		qualityinfo.Database = queryEngine.InitDB
	} else {
		queryEngine = engineinfo
	}
	//删除数据:数据库类型 +表名+ 字段+字段值
	var problemtbname, problemsourcedb string
	problemsourcedb = qualityinfo.ProjectID + "_wt"
	if info.IsProblemTable {
		for _, v := range qualityinfo.HistoryLogInfo {
			if v.YearMonth == info.ProblemVersion {
				if v.History == 0 {
					problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
				} else {
					problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(v.YearMonth, "-", "", -1)
				}
			}
		}
		if qualityinfo.MiddleTableType == 1 {
			problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		}
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"='%s' and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`delete from "%s"."%s" where  %s`, problemsourcedb, problemtbname, sqltxt)
		case common.HIVE, common.Inceptor:
			for k, v := range fieldinfo {
				if v == "null" || v == "NULL" {
					sqltxt = sqltxt + fmt.Sprintf("`%s` is %s and ", k, v)
				} else {
					sqltxt = sqltxt + fmt.Sprintf("`%s`=\"%s\" and ", k, v)
				}

			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("delete from %s`%s.%s` where  %s ", drealdbCatalog, problemsourcedb, problemtbname, sqltxt)
		case common.ODPS:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("%s=`%s` and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;delete from `%s.%s`  %s ", problemsourcedb, problemtbname, sqltxt)
		case common.DMDB:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"=\"%s\" and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`delete from "%s"."%s"  where %s`, problemsourcedb, problemtbname, sqltxt)
		}

	} else {
		for _, v := range qualityinfo.HistoryLogInfo {
			if v.YearMonth == info.ProblemVersion {
				if v.History == 0 {
					problemtbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
				} else {
					problemtbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(v.YearMonth, "-", "", -1)
				}
			}
		}
		if qualityinfo.MiddleTableType == 1 {
			problemtbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		}
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"='%s' and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`delete from "%s"."%s" where  %s`, problemsourcedb, problemtbname, sqltxt)
		case common.HIVE, common.Inceptor:
			for k, v := range fieldinfo {
				if v == "null" || v == "NULL" {
					sqltxt = sqltxt + fmt.Sprintf("`%s` is %s and ", k, v)
				} else {
					sqltxt = sqltxt + fmt.Sprintf("`%s`=\"%s\" and ", k, v)
				}

			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("delete from %s`%s.%s` where  %s ", drealdbCatalog, problemsourcedb, problemtbname, sqltxt)
		case common.ODPS:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("%s=`%s` and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;delete from `%s.%s`  %s ", problemsourcedb, problemtbname, sqltxt)
		case common.DMDB:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"=\"%s\" and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`delete from "%s"."%s"  where %s`, problemsourcedb, problemtbname, sqltxt)
		}

	}
	logger.Info.Println("shanchushuju:", sqltxt)
	_, err = collect.MGExecuteQuerySqlV2(queryEngine, qualityinfo.Database, sqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		return err
	}
	return nil
}
func GetDuplicateNumber(info source.DeleteTableInfo, fieldinfo map[string]interface{}, qualityinfo source.QualityTableInfo) (float64, error) {
	var sqltxt string
	var duplicatenum float64
	engineinfo, err := dbutil.EngineInfo(qualityinfo.Engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
	}
	//删除数据:数据库类型 +表名+ 字段+字段值
	var problemtbname, problemsourcedb string
	problemsourcedb = qualityinfo.ProjectID + "_wt"
	if info.IsProblemTable {
		for _, v := range qualityinfo.HistoryLogInfo {
			if v.YearMonth == info.ProblemVersion {
				if v.History == 0 {
					problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
				} else {
					problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(v.YearMonth, "-", "", -1)
				}
			}
		}
		if qualityinfo.MiddleTableType == 1 {
			problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		}
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"='%s' and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`select count(*) as row_num from "%s"."%s" where  %s`, problemsourcedb, problemtbname, sqltxt)
		case common.HIVE, common.Inceptor:
			for k, v := range fieldinfo {
				if v == "null" || v == "NULL" {
					sqltxt = sqltxt + fmt.Sprintf("`%s` is %s and ", k, v)
				} else {
					sqltxt = sqltxt + fmt.Sprintf("`%s`=\"%s\" and ", k, v)
				}

			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("select count(*) as row_num from `%s.%s` where  %s ", problemsourcedb, problemtbname, sqltxt)
		case common.ODPS:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("%s=`%s` and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) as row_num from `%s.%s`  %s ", problemsourcedb, problemtbname, sqltxt)
		case common.DMDB:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"=\"%s\" and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`select count(*) as row_num from "%s"."%s"  where %s`, problemsourcedb, problemtbname, sqltxt)
		}

	} else {
		for _, v := range qualityinfo.HistoryLogInfo {
			if v.YearMonth == info.ProblemVersion {
				if v.History == 0 {
					problemtbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
				} else {
					problemtbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(v.YearMonth, "-", "", -1)
				}
			}
		}
		if qualityinfo.MiddleTableType == 1 {
			problemtbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		}
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"='%s' and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`select count(*) as row_num from "%s"."%s" where  %s`, problemsourcedb, problemtbname, sqltxt)
		case common.HIVE, common.Inceptor:
			for k, v := range fieldinfo {
				if v == "null" || v == "NULL" {
					sqltxt = sqltxt + fmt.Sprintf("`%s` is %s and ", k, v)
				} else {
					sqltxt = sqltxt + fmt.Sprintf("`%s`=\"%s\" and ", k, v)
				}

			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("select count(*) as row_num from `%s.%s` where  %s ", problemsourcedb, problemtbname, sqltxt)
		case common.ODPS:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("%s=`%s` and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) as row_num from `%s.%s`  %s ", problemsourcedb, problemtbname, sqltxt)
		case common.DMDB:
			for k, v := range fieldinfo {
				sqltxt = sqltxt + fmt.Sprintf("\"%s\"=\"%s\" and ", k, v)
			}
			sqltxt = sqltxt[:strings.LastIndex(sqltxt, "and")]
			sqltxt = fmt.Sprintf(`select count(*) as row_num from "%s"."%s"  where %s`, problemsourcedb, problemtbname, sqltxt)
		}

	}
	logger.Info.Println("shanchushuju:", sqltxt)
	restxt, err := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, sqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		return duplicatenum, err
	}

	count2 := restxt.Data[0]["row_num"]
	if _, ok := count2.(float64); ok {
		duplicatenum = count2.(float64)
	}
	return duplicatenum, nil
}
func QualityMiddleTableStatic(info source.QualityDataInfo, updatetime string) (string, error) {
	qualityexist, err := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, info.JobID)
	var hitsinfo source.QualityHits3
	json.Unmarshal(qualityexist, &hitsinfo)
	var isexist bool
	if err != nil {
		if strings.Contains(err.Error(), "Error 404") {
			isexist = true
		}
	}
	//找到对应的所有问题表

	var result source.QualityTableInfo
	engineinfo, err := dbutil.EngineInfo(info.NewTBinfo.Engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return "", err
	}
	var problemtbname, cf_tbname string
	if info.Module == 7 {
		if info.NewTBinfo.Schema != "" {
			problemtbname = info.NewTBinfo.Schema + "_" + info.NewTBinfo.Tbname
			cf_tbname = "cf_" + info.NewTBinfo.Schema + "_" + info.NewTBinfo.Tbname
		} else {
			problemtbname = info.NewTBinfo.Dbname + "_" + info.NewTBinfo.Tbname
			cf_tbname = "cf_" + info.NewTBinfo.Dbname + "_" + info.NewTBinfo.Tbname
		}
	} else {
		if len(info.HistoryLogInfo) == 0 {
			//历史中间表
			if info.NewTBinfo.Schema != "" {
				problemtbname = info.NewTBinfo.Schema + "_" + info.NewTBinfo.Tbname
				cf_tbname = "cf_" + info.NewTBinfo.Schema + "_" + info.NewTBinfo.Tbname
			} else {
				problemtbname = info.NewTBinfo.Dbname + "_" + info.NewTBinfo.Tbname
				cf_tbname = "cf_" + info.NewTBinfo.Dbname + "_" + info.NewTBinfo.Tbname
			}
		} else {
			//质量监控产生的中间表-取最新表更新
			result.HistoryLogInfo = info.HistoryLogInfo
			findMaxTime := func(timestrs []source.HistoryLogInfo) (string, error) {
				maxTime, err := time.Parse("2006-01", timestrs[0].YearMonth)
				if err != nil {
					return "", err
				}
				for _, t_str := range timestrs {
					t, err := time.Parse("2006-01", t_str.YearMonth)
					if err != nil {
						return "", err
					}
					if t.After(maxTime) {
						maxTime = t
					}
				}
				return maxTime.Format("2006-01"), nil
			}
			YearMonth, _ := findMaxTime(info.HistoryLogInfo)
			if info.NewTBinfo.Schema != "" {
				problemtbname = info.NewTBinfo.Schema + "_" + info.NewTBinfo.Tbname + "_" + strings.Replace(YearMonth, "-", "", -1)
				cf_tbname = "cf_" + info.NewTBinfo.Schema + "_" + info.NewTBinfo.Tbname + "_" + strings.Replace(YearMonth, "-", "", -1)
			} else {
				problemtbname = info.NewTBinfo.Dbname + "_" + info.NewTBinfo.Tbname + "_" + strings.Replace(YearMonth, "-", "", -1)
				cf_tbname = "cf_" + info.NewTBinfo.Dbname + "_" + info.NewTBinfo.Tbname + "_" + strings.Replace(YearMonth, "-", "", -1)
			}

		}
	}

	problemres, err := collect.MGExecuteQuerySqlMetadata(engineinfo, info.NewTBinfo.Dbname, info.NewTBinfo.Schema, problemtbname)
	if err != nil {
		fmt.Println("问题表执行MG语句失败：", err.Error())
		//当报问题表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "Table not found")) {
			logger.Error.Println("问题表执行MG语句失败：", err.Error())
			return "", err
		}
	}
	//rescount = SqlDataTransCount(restxt, engineinfo.EngineName)
	//运行一次，问题数据量为空，不记录
	var procount, cfcount int
	for _, v := range problemres.TBInfo {
		procount = v.TableRowcount
		break
	}
	cfres, err := collect.MGExecuteQuerySqlMetadata(engineinfo, info.NewTBinfo.Dbname, info.NewTBinfo.Schema, cf_tbname)
	if err != nil {
		fmt.Println("重复表执行MG语句失败：", err.Error())
		//当报问题表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "Table not found")) {
			logger.Error.Println("重复表执行MG语句失败：", err.Error())
			return "", err
		}
	}
	for _, v := range cfres.TBInfo {
		cfcount = v.TableRowcount
		break
	}
	if procount == 0 && cfcount == 0 {
		return "不存在问题数据", nil
	}
	//var sqltxt string
	//switch engineinfo.EngineName {
	//case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
	//	sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, info.NewTBinfo.Schema, info.NewTBinfo.Tbname)
	//case common.HIVE, common.Inceptor:
	//	sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s` ", info.NewTBinfo.Dbname, info.NewTBinfo.Tbname)
	//
	//case common.ODPS:
	//	sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", info.NewTBinfo.Dbname, info.NewTBinfo.Tbname)
	//case common.DMDB:
	//	sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s"`, info.NewTBinfo.Schema, info.NewTBinfo.Tbname)
	//}
	restxt, err := collect.MGExecuteQuerySqlMetadata(engineinfo, info.NewTBinfo.Dbname, info.NewTBinfo.Schema, info.NewTBinfo.Tbname)
	//restxt, err1 := collect.MGExecuteQuerySqlV2(engineinfo, info.NewTBinfo.Dbname, sqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error())
		//当报问题表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err.Error(), "does not exist") || strings.Contains(err.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败：", err.Error())
			return "", err
		}
	}
	var rescount int
	//rescount = SqlDataTransCount(restxt, engineinfo.EngineName)
	//运行一次，问题数据量为空，不记录
	for _, v := range restxt.TBInfo {
		rescount = v.TableRowcount
		result.Fieldnum = v.Fieldcount
		result.Engineid = info.NewTBinfo.Engineid
		result.Enginename = engineinfo.EngineName
		result.Enginetype = info.NewTBinfo.Tbtype
		break
	}
	//

	//
	//不存在
	//if rescount == 0 && cfcount == 0 {
	//	return "成功", nil
	//}
	//添加1-最外层数据+ 添加2-内部日志记录
	//var loginfo source.HistoryLogInfo
	//loginfo.ProblemRecord = result.ProblemRecord
	//loginfo.ProblemType = 2
	//loginfo.History = 0
	//result.HistoryLogInfo = append(result.HistoryLogInfo, loginfo)
	if procount != 0 {
		//统计问题数据
		result.ProblemRecord = rescount
		if rescount == 0 {
			result.ProblemType = 2
		} else {
			result.ProblemType = 3
		}
	} else {
		if rescount != 0 {
			result.ProblemType = 1
		}
	}

	result.MiddleTableType = 1
	result.MiddleTableDir = "middletable_" + info.ProjectID
	result.ProjectID = info.ProjectID
	result.Database = info.NewTBinfo.Dbname
	result.SourceDb = info.NewTBinfo.Schema
	result.Tbname = info.NewTBinfo.Tbname
	result.ExplorationTime = updatetime
	result.Id = info.JobID
	if !isexist {
		//存在
		err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.QualityProblem, "true", info.JobID, result)
	} else {
		//不存在记录
		err = esutil.AddWithAimID(common.DataBaseMetaData, common.QualityProblem, info.JobID, result)
	}

	if err != nil {
		logger.Error.Println(err)
		return "失败", err
	}
	return "成功", nil
}

// 保留两位小数且不进行四舍五入
func TruncateDecimal(num float64, precision int) float64 {
	// 将浮点数乘以 100（保留两位小数），然后取整，最后再除以 100 得到截断后的值

	multiplier := math.Pow(10, float64(precision))
	truncated := float64(int(num*float64(multiplier))) / float64(multiplier)
	truncated, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", truncated), 64)
	return truncated
}
func PrefixMatch(middletable string) int {
	//质量问题表命名规则存在如下几种方式
	//1、cf_开头，可能以日期(202409)结尾
	//2、不以cf_开头，可能以日期结尾
	// 定义正则表达式模式-删除字符串结尾为_6位数字的字符,保留其余字符
	var problemtype int = 2
	pattern := `^(.*?)_\d{6}$`
	re, err := regexp.Compile(pattern)
	if err != nil {
		fmt.Println("正则表达式编译错误:", err)
		return problemtype
	}
	//思路-符合规则 则去除符合规则的部分，保留子字符串
	// 查找匹配项
	matches := re.FindStringSubmatch(middletable)
	if len(matches) == 2 {
		//删除cf_开头的字符，保留其他子字符
		re2 := regexp.MustCompile(`^cf_.+`)
		// 查找匹配项
		if re2.MatchString(matches[1]) {
			fmt.Println("字符串匹配成功")
			middletable = strings.Replace(matches[1], "cf_", "", 1)
			problemtype = 1
		} else {
			middletable = matches[1]
			problemtype = 2
		}
	} else {
		re2 := regexp.MustCompile(`^cf_.+`)
		// 查找匹配项
		if re2.MatchString(middletable) {
			fmt.Println("字符串匹配成功")
			middletable = strings.Replace(middletable, "cf_", "", 1)
			problemtype = 1
		} else {
			middletable = matches[1]
			problemtype = 2
		}
	}
	return problemtype
}
func QualityMiddleExport(id, problemversion string) (string, error) {
	var filename string

	var fieldinfoarr, describearr []string
	//res, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, id)
	qualityinfo, err := SearchProbleTable(id)
	if err != nil {
		logger.Error.Println(err)
		return filename, err
	}
	dbname := qualityinfo.Database
	sourcedb := qualityinfo.SourceDb
	tbname := qualityinfo.Tbname
	problemtype := qualityinfo.ProblemType
	projectid := qualityinfo.ProjectID
	problemsourcedb := projectid + "_wt"
	engineid := qualityinfo.Engineid
	engineinfo, err := dbutil.EngineInfo(engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return filename, err
	}
	//获取原表的字段信息和描述信息
	fieldinfoarr, describearr, err = GetOriginalTableInfo(engineinfo, dbname, sourcedb, tbname)
	if err != nil {
		logger.Error.Println("查询原表信息失败：", err.Error())
		return filename, err
	}
	logger.Info.Println("fieldinfoarr1:", fieldinfoarr, describearr)
	filename, err = CommonGetMiddleTableInfo2(id, engineinfo, int(problemtype), dbname, sourcedb, tbname, problemsourcedb, "", fieldinfoarr, describearr)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return filename, err
	}
	return filename, nil
}
func CommonGetMiddleTableInfo2(sourceid string, engineinfo source.EngineInfo, problemtype int, dbname, sourcedb, tbname, problemsourcedb, problemtable string, fieldinfoarr, describearr []string) (string, error) {
	var sqltxt, cf_sqltxt, normalsqltxt string
	if problemtype == 1 {
		//重复数据
		var cf_tbname string
		if problemtable == "" {
			cf_tbname = "cf_" + sourcedb + "_" + tbname
		} else {
			cf_tbname = "cf_" + sourcedb + "_" + problemtable
		}

		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.HIVE, common.Inceptor:
			cf_sqltxt = fmt.Sprintf("select * from `%s.%s`", problemsourcedb, cf_tbname)
		case common.ODPS:
			fieldinfoarr = append(fieldinfoarr, "cf_rksj")
			cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, cf_tbname)
		case common.DMDB:
			fieldinfoarr = append(fieldinfoarr, "CF_RKSJ")
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
		}
		describearr = append(describearr, "")
		//res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "重复数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})
		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}

		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//

		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---
			number, err = RepetitionWriteDataToFile2(res2.Data, file, number, style, sw, fieldinfoarr, engineinfo.EngineName, cf_tbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

			//1、字段名称、来源表、来源字段、问题描述、规则类型 格式如下

		}
		// 保存文件
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil

	} else if problemtype == 2 {
		var extract []byte //先定义，留着后续用
		//, _ := SearchTbAccess(sourceid)
		//问题数据
		//需要访问获取全部数据
		//根据每行的dwd_ruleid 中的id 找到
		var problemtbname string
		if problemtable == "" {
			//problemtbname = sourcedb + "_" + tbname
			problemtbname = tbname
		} else {
			//problemtbname = sourcedb + "_" + problemtable
			problemtbname = problemtable
		}
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.HIVE, common.Inceptor:
			//sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
			sqltxt = fmt.Sprintf("select * from `%s.%s` ", problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "DWD_RKSJ")
		}
		describearr = append(describearr, "")
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "问题数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		//mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: "wtjl"})
		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		//descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: "问题记录"})
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		logger.Info.Println("describearr:", describearr, fieldinfoarr)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			logger.Info.Println("changdu:", len(res2.Data))
			number, _ = CombMiddleData(res2.Data, extract, sw, style, number, fieldinfoarr, engineinfo.EngineName, problemtbname)
			logger.Info.Println("number:", number)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
		}
		// 保存文件
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil

	} else if problemtype == 3 {
		//重复数据、问题数据都有
		var extract []byte
		//extract, _ := SearchTbAccess(sourceid)
		cffieldinfoarr := fieldinfoarr
		var problemtbname, cf_tbname string
		if problemtable == "" {
			problemtbname = sourcedb + "_" + tbname
			cf_tbname = "cf_" + sourcedb + "_" + tbname
		} else {
			problemtbname = sourcedb + "_" + problemtable
			cf_tbname = "cf_" + sourcedb + "_" + problemtable
		}

		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.HIVE, common.Inceptor:
			sqltxt = fmt.Sprintf("select * from `%s.%s` ", problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf("select * from `%s.%s`", problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "DWD_RKSJ")
			cffieldinfoarr = append(fieldinfoarr, "CF_RKSJ")
		}
		describearr = append(describearr, "")
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "问题数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: "wtjl"})
		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: "问题记录"})
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---

			number, err = CombMiddleData(res2.Data, extract, sw, style, number, fieldinfoarr, engineinfo.EngineName, problemtbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

		}
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}
		//重复数据
		cf_res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
		}
		for _, v := range cf_res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var cf_number int = 2

		sheetName2 := "重复数据"
		index2, _ := file.NewSheet(sheetName2)
		file.SetActiveSheet(index2)
		cf_sw, err := file.NewStreamWriter(sheetName2)
		if err != nil {
			fmt.Println(err)

		}
		style, err = file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName2 = file.GetSheetName(file.GetActiveSheetIndex())
		var cf_mySlice []interface{}

		for _, header := range cffieldinfoarr {
			cf_mySlice = append(cf_mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		//cell := indexToColumnName(i+1) + "1"
		cf_cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = cf_sw.SetRow(cf_cell, cf_mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//

		for {
			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---
			cf_number, err = RepetitionWriteDataToFile2(res2.Data, file, cf_number, style, cf_sw, cffieldinfoarr, engineinfo.EngineName, cf_tbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

		}

		// 保存文件
		if err := cf_sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil
	}
	fmt.Println(sqltxt, cf_sqltxt, normalsqltxt)
	return "", nil
}
func CombMiddleData(data []map[string]interface{}, extract []byte, sw *xlsx.StreamWriter, style, number int, fieldinfoarr []string, enginename, table string) (int, error) {
	//根据qualitypronlem中的fieldinfos 数据规则
	//	xh                              -----字段
	//来源字段:xuehao                     ------字段注释
	//问题1:
	//	问题描述：不符合空值校验           errmsg
	//问题2:
	//	问题描述：不符合XX规则
	//dwd_ruleid格式:
	//[
	//    {
	//        "field":"id",
	//        "ruleid":["id1","id2"]
	//    },{
	//        "field":"name",
	//        "ruleid":["id3","id4"]
	//    }
	//]
	for rowIndex, rowData := range data {
		var tb_ruleinfo string
		var mySlice []interface{}
		switch enginename {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			if _, ok := rowData["dwd_ruleid"].(string); ok {
				tb_ruleinfo = rowData["dwd_ruleid"].(string)
				if tb_ruleinfo != "" {
					//理论上不存在这种情况
				}
			}
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if v == k {
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}

			}
		case common.DMDB:
			if _, ok := rowData["DWD_RULEID"].(string); ok {
				tb_ruleinfo = rowData["DWD_RULEID"].(string)
				if tb_ruleinfo != "" {
					//理论上不存在这种情况
				}
			}
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if v == k {
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}

			}

		case common.HIVE, common.Inceptor, common.ODPS:
			if _, ok := rowData["dwd_ruleid"].(string); ok {
				tb_ruleinfo = rowData["dwd_ruleid"].(string)
				if tb_ruleinfo != "" {
					//理论上不存在这种情况
				}
			} else if _, ok := rowData[table+".dwd_ruleid"].(string); ok {
				tb_ruleinfo = rowData[table+".dwd_ruleid"].(string)
			}
			for _, v := range fieldinfoarr {
				for k, v2 := range rowData {
					if v == k || table+"."+v == k {
						mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: v2})
					}
				}
			}
		}
		var data1 []map[string]interface{}
		err := json.Unmarshal([]byte(tb_ruleinfo), &data1)
		if err != nil {
			fmt.Println("Error:", err)
		}
		var ruleinfos []source.RuleInfo
		var wt_datas [][]map[string]interface{}
		for _, v := range data1 {
			//根据dwd_ruleid获取所有规则，然后进行errMsg筛选
			var ruleinfo source.RuleInfo
			ruleinfo.Field = v["field"].(string)
			var ruleids string
			if _, ok := v["ruleid"].([]interface{}); ok {
				for _, v := range v["ruleid"].([]interface{}) {
					ruleinfo.Ruleid = append(ruleinfo.Ruleid, v.(string))
					ruleids = ruleids + v.(string) + ","
				}
			}
			if _, ok := v["ruletype"].([]interface{}); ok {
				for _, v := range v["ruletype"].([]interface{}) {
					ruleinfo.Ruletype = append(ruleinfo.Ruletype, v.(string))
				}
			}
			if _, ok := v["ruledimension"].([]interface{}); ok {
				for _, v := range v["ruledimension"].([]interface{}) {
					ruleinfo.Ruledimension = append(ruleinfo.Ruledimension, v.(string))
				}
			}

			var wt_data []map[string]interface{}
			switch enginename {
			case common.HIVE, common.Inceptor, common.ODPS:
				tmpfieldname := "wt_" + ruleinfo.Field
				//fmt.Println("tmpfieldname:", tmpfieldname, rowData)
				if rowData[tmpfieldname] != nil {
					//fmt.Println("tmpfieldname2:", tmpfieldname, rowData)
					wt_data = ContentTransValue(rowData[tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				} else if rowData[table+"."+tmpfieldname] != nil {
					//fmt.Println("tmpfieldname3:", tmpfieldname, rowData)
					wt_data = ContentTransValue(rowData[table+"."+tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				}
			case common.DMDB:
				//特殊情况：达梦数据库表中wt开头的都是大写如WT_RULEID
				tmpfieldname := "WT_" + strings.ToUpper(ruleinfo.Field)
				if rowData[tmpfieldname] != nil {
					wt_data = ContentTransValue(rowData[tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				}
			default:
				tmpfieldname := "wt_" + ruleinfo.Field
				if rowData[tmpfieldname] != nil {
					wt_data = ContentTransValue(rowData[tmpfieldname].(string))
					wt_datas = append(wt_datas, wt_data)
				}
			}

			for _, wt_v := range wt_data {
				if wt_v["errID"] != nil && strings.Contains(ruleids, wt_v["errID"].(string)) {

					ruleinfo.ProblemDesc = append(ruleinfo.ProblemDesc, wt_v["errMsg"].(string))
				}
			}
			ruleinfos = append(ruleinfos, ruleinfo)
		}
		var three string
		for _, v := range ruleinfos {
			if len(v.ProblemDesc) == 0 {
				continue
			}
			three = three + fmt.Sprintf("%s\n", v.Field)
			for j, v2 := range v.ProblemDesc {
				three = three + fmt.Sprintf("问题%d:\n问题描述:%s\n", j+1, v2)
			}

		}

		mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: three})
		var cell string
		if number != 0 {
			cell, err = xlsx.CoordinatesToCellName(1, number+1)
		} else {
			cell, err = xlsx.CoordinatesToCellName(1, rowIndex+3)
		}
		if err != nil {
			fmt.Println(err)
			break
		}
		if err := sw.SetRow(cell, mySlice); err != nil {
			fmt.Println(err)
			break
		}
		number = number + 1
	}
	return number, nil
}
func GetOriginalTableInfo(engineinfo source.EngineInfo, dbname, sourcedb, tbname string) ([]string, []string, error) {
	var sqltxt string
	switch engineinfo.EngineName {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		sqltxt = fmt.Sprintf(`select * from "%s"."%s"  limit 1`, sourcedb, tbname)
	case common.HIVE, common.Inceptor:
		sqltxt = fmt.Sprintf("select * from `%s.%s`  LIMIT 1", sourcedb, tbname)
	case common.ODPS:
		sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select `%s` from `%s.%s` ORDER BY `%s` LIMIT %s", sourcedb, tbname)
	case common.DMDB:
		sqltxt = fmt.Sprintf(`select * from "%s"."%s" LIMIT 1`, sourcedb, tbname)
	}
	sqltxt = tbname
	fmt.Println("sqltxt: ", sqltxt)
	var col []string
	var comment []string
	resdesc, err := collect.MGExecuteQuerySqlMetadata(engineinfo, dbname, sourcedb, sqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		return col, comment, err
	}
	logger.Info.Println("resdesc:", resdesc)
	logger.Info.Println("resdesc.TBInfo :", resdesc.TBInfo)
	for _, j := range resdesc.TBInfo {
		logger.Info.Println("jcols:", j.Cols)
		for _, v := range j.Cols {
			comment = append(comment, v.Comment)
			col = append(col, v.Name)
		}
	}
	return col, comment, nil
}
func BatchMiddleCommon(ids []string, needwhere bool, month string) ([]source.BatchProblem, error) {

	var nextmonth string
	if needwhere {
		tempList := GetAfterMonth(month, 1)
		if tempList != nil {
			nextmonth = tempList[0]
		}
	}

	var infos []source.BatchProblem
	var sqltxt, cf_sqltxt string
	for _, id := range ids {
		var info source.BatchProblem
		var qualityinfo source.QualityTableInfo
		res, err := esutil.SearchByID(common.DataBaseMetaData, common.QualityProblem, id)
		if err != nil {
			fmt.Println("查询问题数据失败2:", err.Error())
			return infos, err
		}
		err = json.Unmarshal([]byte(res), &qualityinfo)
		dbname := qualityinfo.Database
		sourcedb := qualityinfo.SourceDb
		tbname := qualityinfo.Tbname
		problemtype := qualityinfo.ProblemType
		projectid := qualityinfo.ProjectID
		problemsourcedb := projectid + "_wt"
		engineid := qualityinfo.Engineid
		engineinfo, err := dbutil.EngineInfo(engineid)
		if err != nil {
			logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
			return infos, err
		}
		//获取原表的字段信息和描述信息
		fieldinfoarr, describearr, err := GetOriginalTableInfo(engineinfo, dbname, sourcedb, tbname)
		if err != nil {
			logger.Error.Println("查询原表信息失败：", err.Error())
			return infos, err
		}
		if err != nil {
			return infos, err
		}
		var fieldinfos string
		for _, v := range fieldinfoarr {
			name := v
			if len(fieldinfos) == 0 {
				fieldinfos = name
			} else {
				fieldinfos = fieldinfos + "," + name
			}
			info.Fieldinfoarr = append(info.Fieldinfoarr, name)
			info.CfFieldinfoarr = append(info.CfFieldinfoarr, name)
		}
		info.DescribeArr = describearr
		if problemtype == 1 {
			//重复数据
			var cf_tbname string
			cf_tbname = "cf_" + sourcedb + "_" + info.Tbname
			info.CfTable = cf_tbname
			info.CfFieldinfoarr = info.Fieldinfoarr
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "cf_rksj" < '%s' and "cf_rksj" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.HIVE, common.Inceptor:
				cf_sqltxt = fmt.Sprintf("select * from `%s`.`%s`", problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.ODPS:
				cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s`.`%s` ", problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.DMDB:
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "CF_RKSJ" < '%s' and "CF_RKSJ" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "CF_RKSJ")

			}
			info.DescribeArr = append(info.DescribeArr, "")
			info.Cfsql = cf_sqltxt

		} else if problemtype == 2 {
			//问题数据
			//需要访问获取全部数据
			//根据每行的dwd_ruleid 中的id 找到
			var problemtbname string
			problemtbname = sourcedb + "_" + info.Tbname
			info.Tbname = problemtbname
			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf(`%s where "dwd_rksj" < '%s' and "dwd_rksj" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
			case common.HIVE, common.Inceptor:
				//sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
				sqltxt = fmt.Sprintf("select * from `%s`.`%s` ", problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
			case common.ODPS:
				sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s`.`%s` ", problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
			case common.DMDB:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
				if needwhere {
					sqltxt = fmt.Sprintf(`%s where "DWD_RKSJ" < '%s' and "DWD_RKSJ" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "DWD_RKSJ")
			}
			info.DescribeArr = append(info.DescribeArr, "")
			info.Problemsql = sqltxt
			info.WtTable = problemtbname
		} else if problemtype == 3 {
			//重复数据、问题数据都有
			var problemtbname, cf_tbname string
			problemtbname = sourcedb + "_" + info.Tbname
			cf_tbname = "cf_" + sourcedb + "_" + info.Tbname

			switch engineinfo.EngineName {
			case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "cf_rksj" < '%s' and "cf_rksj" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf(`%s where "dwd_rksj" < '%s' and "dwd_rksj" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.HIVE, common.Inceptor:
				sqltxt = fmt.Sprintf("select * from `%s`.`%s` ", problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf("select * from `%s`.`%s`", problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.ODPS:
				sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf("%s where `cf_rksj` < '%s' and `cf_rksj` >= '%s'", cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf("%s where `dwd_rksj` < '%s' and `dwd_rksj` >= '%s'", sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "dwd_rksj")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "cf_rksj")
			case common.DMDB:
				sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
				cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
				if needwhere {
					cf_sqltxt = fmt.Sprintf(`%s where "CF_RKSJ" < '%s' and "CF_RKSJ" >= '%s'`, cf_sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
					sqltxt = fmt.Sprintf(`%s where "DWD_RKSJ" < '%s' and "DWD_RKSJ" >= '%s'`, sqltxt, nextmonth+"-01 00:00:00", month+"-01 00:00:00")
				}
				info.Fieldinfoarr = append(info.Fieldinfoarr, "DWD_RKSJ")
				info.CfFieldinfoarr = append(info.CfFieldinfoarr, "CF_RKSJ")
			}
			info.DescribeArr = append(info.DescribeArr, "")
			info.Cfsql = cf_sqltxt
			info.Problemsql = sqltxt
			info.WtTable = problemtbname
			info.CfTable = cf_tbname
		}
		infos = append(infos, info)
	}
	return infos, nil
}
func QualityProMidOneTableStatic(sourceinfo source.QualityDataInfo, updatetime string, ishistory bool, inputtype, tasktype int) (string, error) {
	var qualityinfo source.QualityTableInfo
	qualityinfo.Engineid = sourceinfo.NewTBinfo.Engineid
	qualityinfo.Database = sourceinfo.NewTBinfo.Dbname
	qualityinfo.SourceDb = sourceinfo.NewTBinfo.Schema
	qualityinfo.Tbname = sourceinfo.NewTBinfo.Tbname
	qualityinfo.Enginetype = sourceinfo.NewTBinfo.Tbtype
	qualityinfo.Duproles.InputType = sourceinfo.InputType
	qualityinfo.ProjectID = sourceinfo.ProjectID
	qualityinfo.MetadataIdNull = sourceinfo.MetadataIdNull
	//格式化年份和月份
	var dateString string
	if updatetime != "" {
		if strings.Count(updatetime, "-") != 1 {
			dateString = updatetime[:strings.LastIndex(updatetime, "-")]
		} else {
			dateString = updatetime
		}
	} else {
		currentTime := time.Now()
		dateString = currentTime.Format("2006-01")
	}
	//质量监控查询
	//tancha_redev, err := SearchDev(sourceinfo.Cid, qualityinfo.ProjectID, 0)
	tancha_redev, err := esutil.SearchByID(common.DataBaseLab, common.TbDev, sourceinfo.Cid)
	if err != nil {
		logger.Error.Printf("查询表元数据信息失败:%s, %s", err.Error())
		//return result, err
	}
	var watchnum int
	var watchruler bool
	watchcheck := gjson.Get(tancha_redev, "watchduprole.watchcheck").Bool()
	//watchcheck=true代表有重复数据，watchdup=false 代表重复数据需要放入到问题表中
	if watchcheck == true {
		qualityinfo.Duproles.Problem = true
		watchnum++
		mainkeys := gjson.Get(tancha_redev, "watchduprole.watchmainkey").Array()
		for _, mainkey := range mainkeys {
			qualityinfo.Duproles.InputType = 1
			watchruler = true
			qualityinfo.Duproles.RepetitionMainKey = append(qualityinfo.Duproles.RepetitionMainKey, mainkey.String())
		}
	}
	//问题原表是否有记录，有则更新，无则添加
	qualityexist, err := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, sourceinfo.Cid)
	var hitsinfo source.QualityHits3
	json.Unmarshal(qualityexist, &hitsinfo)
	var isexist bool
	if err != nil {
		if strings.Contains(err.Error(), "Error 404") {
			isexist = true
		}
	}
	var tq source.QualityHits3
	json.Unmarshal(qualityexist, &tq)
	//qualityinfo.ExplorationTime = gjson.Get(string(redev), "hits.hits.0._source.updatetime").String()
	problemsourcedb := sourceinfo.ProjectID + "_wt"
	var problemtbname, cf_tbname string
loop:
	if tasktype == 1 {
		//针对新版本任务运行
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(dateString, "-", "", -1)
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname + "_" + strings.Replace(dateString, "-", "", -1)
		qualityinfo.DsVersion = tasktype
		if strings.Count(updatetime, "-") == 1 {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		} else {
			qualityinfo.ExplorationTime = updatetime
		}

	} else if tasktype == 0 {
		//针对历史任务兼容：初始化元数据至最新探查时间所在的月份表中(es中存储)
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		//ds同步历史任务，同步一次自动将该版本信息也更新至最新
		qualityinfo.DsVersion = 1
		if tq.QualityTableInfo.ExplorationTime == "" {
			qualityinfo.ExplorationTime = updatetime
		} else {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		}
	} else {
		//针对最外层同步(历史任务和新建任务同步)
		var isyear bool
		for _, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			fmt.Println("dateString:", dateString)
			fmt.Println("YearMonth:", v.YearMonth)
			if v.YearMonth == dateString {
				tasktype = v.History
				isyear = true
				fmt.Println("tasktype:", tasktype)
			}
		}
		if isyear {
			goto loop
		} else {
			tasktype = 1
			goto loop
		}

	}
	//找到对应的所有问题表
	engineinfo, err := dbutil.EngineInfo(sourceinfo.NewTBinfo.Engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return "", err
	}
	qualityinfo.Enginename = engineinfo.EngineName
	var sqltxt, cf_sqltxt, normalsqltxt string
	switch engineinfo.EngineName {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	case common.HIVE, common.Inceptor:
		sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", problemsourcedb, cf_tbname)
	case common.ODPS:
		sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, cf_tbname)
	case common.DMDB:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s"`, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	}
	restxt, err1 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, sqltxt)
	if err1 != nil {
		fmt.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
		//当报问题表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err1.Error(), "does not exist") || strings.Contains(err1.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			return "", err1
		}
	}
	cf_restxt, err2 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, cf_sqltxt)
	//{200 Success [map[COUNT(*):3]] map[COUNT(*):BIGINT] [map[COUNT(*):BIGINT]]}  dtameng
	//{200 Success [map[count:4]] map[count:int8] [map[count:int8]]} [map[count:4]]  teryx
	if err2 != nil {
		fmt.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
		//当报重复表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err2.Error(), "does not exist") || strings.Contains(err2.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
			return "", err2
		}
	}
	var rescount, cfcount float64
	rescount = SqlDataTransCount(restxt, engineinfo.EngineName)
	cfcount = SqlDataTransCount(cf_restxt, engineinfo.EngineName)
	//运行一次，问题数据量为空，不记录
	if rescount == 0 && cfcount == 0 {
		return "不存在问题数据", nil
	}
	normalrestxt, err := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, normalsqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		return "", err
	}
	normalcount := SqlDataTransCount(normalrestxt, engineinfo.EngineName)
	var scoreinfos source.RulerScore
	if ishistory == false {
		if qualityinfo.Duproles.InputType == 0 {
			qualityinfo.VerifyRecord = int(normalcount) - int(cfcount)
		} else {
			qualityinfo.VerifyRecord = int(normalcount)
		}
		if rescount != 0 {
			//统计问题数据
			qualityinfo.ProblemRecord = int(rescount)
			//qualityinfo.ProblemQualifyRate = float64(qualityinfo.ProblemRecord) / normalcount
			if qualityinfo.VerifyRecord != 0 {
				qualityinfo.ProblemQualifyRate = TruncateDecimal(float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord), 2)
				//qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord)), 64)
			} else {
				qualityinfo.ProblemQualifyRate = 0
			}
			qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", 100-qualityinfo.ProblemQualifyRate), 64)
			ProMidOneTableStatic(tancha_redev, engineinfo, &qualityinfo, sourceinfo.Cid, qualityinfo.Database, problemsourcedb, problemtbname, qualityinfo.VerifyRecord)
			qualityinfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber + watchnum

			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 3
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err)
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				//qualityinfo.Duproles.RepetitionRate = qualityinfo.Duproles.RepetitionNumber / float64(qualityinfo.VerifyRecord)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate

			} else {
				qualityinfo.ProblemType = 2
			}
			//增加对质量分等指标的统计
			scoreinfos, _ = ScoreStatic(qualityinfo.Fieldinfo, qualityinfo.ProjectID, qualityinfo.RepetitionRate, watchruler, 0)
		} else {
			//不统计问题数据,查看是否统计重复数据
			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 1
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err.Error())
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate
				if watchruler {
					scoreinfos.Normative = scoreinfos.Normative + (100 - qualityinfo.RepetitionRate)
					scoreinfos.TableQualityScore = scoreinfos.Normative
				}
			}
		}
	} else {
		if rescount != 0 {
			qualityinfo.ProblemRecord = int(rescount)
			if cfcount != 0 {
				qualityinfo.ProblemType = 3
			} else {
				qualityinfo.ProblemType = 2
			}
		} else {
			if cfcount != 0 {
				qualityinfo.ProblemType = 1
			}
		}

	}
	qualityinfo.Id = sourceinfo.Cid

	if !isexist {
		//存在记录
		qualityinfo.HistoryLogInfo = tq.QualityTableInfo.HistoryLogInfo
		var isupdate bool
		fmt.Println("====kaishile===")
		for i, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			if v.YearMonth == dateString {
				isupdate = true
				//更新1 +更新2
				qualityinfo.HistoryLogInfo[i].ProblemQualifyRate = qualityinfo.ProblemQualifyRate
				qualityinfo.HistoryLogInfo[i].ProblemRecord = qualityinfo.ProblemRecord
				qualityinfo.HistoryLogInfo[i].VerifyRecord = qualityinfo.VerifyRecord
				qualityinfo.HistoryLogInfo[i].VerifyRulerNumber = qualityinfo.VerifyRulerNumber
				qualityinfo.HistoryLogInfo[i].VerifyFieldNumber = qualityinfo.VerifyFieldNumber
				//qualityinfo.HistoryLogInfo[i].DupDataResource = qualityinfo.DupDataResource
				qualityinfo.HistoryLogInfo[i].Duproles = qualityinfo.Duproles
				qualityinfo.HistoryLogInfo[i].Fieldinfo = qualityinfo.Fieldinfo
				qualityinfo.HistoryLogInfo[i].RulerScore = scoreinfos
				qualityinfo.HistoryLogInfo[i].YearMonth = dateString
				qualityinfo.HistoryLogInfo[i].ProblemType = qualityinfo.ProblemType
				if tasktype == 1 {
					qualityinfo.HistoryLogInfo[i].History = 1
				} else {
					qualityinfo.HistoryLogInfo[i].History = 0
				}
				fmt.Println("HistoryLogInfo:", i, qualityinfo.Fieldinfo)
				break
			}
		}
		if !isupdate {
			//没有对应年月记录
			//更新1 + 追加2
			var loginfo source.HistoryLogInfo
			loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
			loginfo.ProblemRecord = qualityinfo.ProblemRecord
			loginfo.VerifyRecord = qualityinfo.VerifyRecord
			loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
			loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
			loginfo.Duproles = qualityinfo.Duproles
			loginfo.Fieldinfo = qualityinfo.Fieldinfo
			loginfo.RulerScore = scoreinfos
			loginfo.YearMonth = dateString
			loginfo.ProblemType = qualityinfo.ProblemType
			if tasktype == 1 {
				loginfo.History = 1
			} else {
				loginfo.History = 0
			}
			qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		}
		err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.QualityProblem, "true", sourceinfo.Cid, qualityinfo)
	} else {
		//不存在
		//添加1-最外层数据+ 添加2-内部日志记录
		var loginfo source.HistoryLogInfo
		loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
		loginfo.ProblemRecord = qualityinfo.ProblemRecord
		loginfo.VerifyRecord = qualityinfo.VerifyRecord
		loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
		loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
		loginfo.Duproles = qualityinfo.Duproles
		loginfo.Fieldinfo = qualityinfo.Fieldinfo
		loginfo.RulerScore = scoreinfos
		loginfo.YearMonth = dateString
		loginfo.ProblemType = qualityinfo.ProblemType
		if tasktype == 1 {
			loginfo.History = 1
		} else {
			loginfo.History = 0
		}
		qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		err = esutil.AddWithAimID(common.DataBaseMetaData, common.QualityProblem, sourceinfo.Cid, qualityinfo)
		if err != nil {
			fmt.Println("addes:", err.Error())
		}
	}
	if err != nil {
		logger.Error.Println(err)
		return "失败", err
	}
	return "成功", nil
}
func ProMidOneTableStatic(re string, engineinfo source.EngineInfo, pro *source.QualityTableInfo, sourceid, dbname, problemsourcedb, problemtbname string, normalcount int) error {
	//res, _ := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, sourceid)
	//var hitsinfo source.QualityHits3
	//json.Unmarshal(res, &hitsinfo)
	//logger.Info.Println("查询问题数据失败res:", string(res), hitsinfo)
	//核验字段数、核验规则数、 最近探查时间

	var problems []source.ProblemFieldInfo
	//第一次 无探查规则   有探查规则
	//第二次 无探查规则   有探查规则
	//更新统计
	fieldinfos := gjson.Get(re, "fieldinfos").Array()
	var verifyfieldnum int
	fmt.Println("fieldinfos:", len(fieldinfos))
	//数据开发规则配置源
	var ruleids []string
	for _, c := range fieldinfos {
		var problem source.ProblemFieldInfo
		problem.Name = c.Get("field").String()
		problem.FieldType = c.Get("fieldtype").String()
		problem.Annotation = c.Get("fieldcomment").String()
		fieldrule := c.Get("fieldrule").Array()
		if len(fieldrule) != 0 {
			verifyfieldnum++
			for _, ruler := range fieldrule {
				problem.RulerId = append(problem.RulerId, ruler.Get("id").String())
				ruleids = append(ruleids, ruler.Get("id").String())
				problem.ExploratoryRule = append(problem.ExploratoryRule, ruler.Get("name").String())
				problem.RuleType = append(problem.QualityDimension, ruler.Get("ruletype").String())
				problem.QualityDimension = append(problem.QualityDimension, ruler.Get("ruledimension").String())
				pronum, _ := RulerStaticTable(engineinfo, dbname, problemsourcedb, problemtbname, "wt_"+problem.Name, ruler.Get("id").String())
				problem.ProblemRecord = append(problem.ProblemRecord, pronum)
				problem.VerifyRecord = append(problem.VerifyRecord, normalcount)
				var qualifyrate float64
				if normalcount != 0 {
					qualifyrate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", 100-float64(pronum)/float64(normalcount)*100), 64)
				} else {
					qualifyrate = 0
				}
				problem.QualifyRate = append(problem.QualifyRate, qualifyrate)
			}
		}
		fmt.Println("problem", problem)
		problems = append(problems, problem)
	}
	pro.VerifyFieldNumber = verifyfieldnum
	pro.VerifyRulerNumber = len(tools.RemoveRepeatedElement(ruleids))
	pro.Fieldinfo = problems
	return nil
}
func FieldRemove(v string) bool {
	var tmpcol bool
	if strings.Contains(v, "DWD_RULEID") || strings.Contains(v, "dwd_ruleid") {
		return true
	}
	if strings.Contains(v, "DWD_GXSJ") || strings.Contains(v, "dwd_gxsj") {
		return true
	}
	if strings.Contains(v, "DT_SPE_PT") || strings.Contains(v, "dt_spe_pt") {
		return true
	}
	if strings.Contains(v, "DWD_SFKY") || strings.Contains(v, "dwd_sfky") {
		return true
	}
	if strings.Contains(v, "DWD_SFWT") || strings.Contains(v, "dwd_sfwt") {
		return true
	}
	if strings.Contains(v, "children") {
		return true
	}
	return tmpcol
}
func QualityTanchaOneTableStatic(id, updatetime string, ishistory bool, inputtype, tasktype int) (string, error) {
	var result string
	var qualityinfo source.QualityTableInfo
	//fmt.Println("updatetime: ", updatetime)
	res, err := esutil.SearchByID(common.DataBaseMetaData, common.TbMetaTable, id)
	if err != nil {
		logger.Error.Printf("查询表元数据信息失败:%s, %s", err.Error(), id)
		return result, err
	}
	//
	//格式化年份和月份
	var dateString string
	if updatetime != "" {
		if strings.Count(updatetime, "-") != 1 {
			dateString = updatetime[:strings.LastIndex(updatetime, "-")]
		} else {
			dateString = updatetime
		}
	} else {
		currentTime := time.Now()
		dateString = currentTime.Format("2006-01")
	}
	qualityinfo.Engineid = gjson.Get(res, "engineid").String()
	qualityinfo.ProjectID = gjson.Get(res, "projectid").String()
	qualityinfo.Database = gjson.Get(res, "dbname").String() //dbname
	qualityinfo.Tbname = gjson.Get(res, "tbname").String()

	qualityinfo.Enginetype = gjson.Get(res, "dbtype").String()
	qualityinfo.SourceDb = gjson.Get(res, "sourcedb").String()
	qualityinfo.Fieldnum = len(gjson.Get(res, "fieldinfo").Array())
	qualityinfo.SourceTableCatalogid = gjson.Get(res, "catalogid").String()
	qualityinfo.Describe = gjson.Get(res, "describe").String()
	qualityinfo.IsHistory = ishistory
	var sourcecataglist []gjson.Result
	sourcecataglist = gjson.Get(res, "taglist").Array()
	for _, s := range sourcecataglist {
		var tag source.TagList
		tag.ID = s.Get("id").String()
		tag.DirID = s.Get("dirid").String()
		tag.Name = s.Get("name").String()
		tag.Color = s.Get("color").String()
		qualityinfo.TagList = append(qualityinfo.TagList, tag)
	}
	//schema := gjson.Get(res, "schema").String()
	extract, err := SearchTbAccess(id)
	if err != nil {
		logger.Error.Printf("获取业务来源表表元数据信息失败:%s, %s", err.Error())
		//return result, err
	}
	if gjson.Get(string(extract), "hits.total").Int() != 0 {
		qualityinfo.ExtractDbname = gjson.Get(string(extract), "hits.hits.0._source.extractdb.dbname").String()
		qualityinfo.ExtractSchema = gjson.Get(string(extract), "hits.hits.0._source.extractdb.schema").String()
		qualityinfo.ExtractTable = gjson.Get(string(extract), "hits.hits.0._source.extractdb.table").String()
	}
	//数据接入查询
	redev, err := SearchDev(id, qualityinfo.ProjectID, 1)
	//质量监控查询
	tancha_redev, err := SearchDev(id, qualityinfo.ProjectID, 2)
	if err != nil {
		logger.Error.Printf("查询表元数据信息失败:%s, %s", err.Error())
		//return result, err
	}
	qualityinfo.Duproles.InputType = inputtype
	if gjson.Get(string(redev), "hits.total").Int() != 0 {
		cookinginput := gjson.Get(string(redev), "hits.hits.0._source.cookinginput").Array()
		for _, v := range cookinginput {
			qualityinfo.Duproles.Problem = v.Get("duproles.problem").Bool()
			qualityinfo.Duproles.Dataclean = v.Get("duproles.problem").Bool()

			mainkeys := v.Get("mainkeyfield").Array()
			for _, mainkey := range mainkeys {
				qualityinfo.Duproles.InputType = 0
				qualityinfo.Duproles.RepetitionMainKey = append(qualityinfo.Duproles.RepetitionMainKey, mainkey.String())
			}
			break
		}
	}
	var watchnum, vertifynum int
	var watchruler bool
	if gjson.Get(string(tancha_redev), "hits.total").Int() != 0 {
		fieldinfos := gjson.Get(string(tancha_redev), "hits.hits.0._source.fieldinfos").Array()
		//watchcheck=true代表有重复数据，watchdup=false 代表重复数据需要放入到问题表中
		for _, v := range fieldinfos {
			vertifynum++
			if v.Get("fieldbasicroles").String() != "" {
				watchnum++
				continue
			}
			fielddeeproles := v.Get("fielddeeproles").String()
			if fielddeeproles != "" {
				if len(gjson.Get(fielddeeproles, "udfroles").Array()) != 0 {
					watchnum = watchnum + len(gjson.Get(fielddeeproles, "udfroles").Array())
				}
				if len(gjson.Get(fielddeeproles, "udfroles").Array()) != 0 {
					watchnum = watchnum + len(gjson.Get(fielddeeproles, "udfroles").Array())
				}
				if len(gjson.Get(fielddeeproles, "udfroles").Array()) != 0 {
					watchnum = watchnum + len(gjson.Get(fielddeeproles, "udfroles").Array())
				}
			}
		}

		qualityinfo.Duproles.Problem = true
		mainkeys := gjson.Get(string(tancha_redev), "hits.hits.0._source.mainkeyfield").Array()
		for _, mainkey := range mainkeys {
			qualityinfo.Duproles.InputType = 1
			watchruler = true
			qualityinfo.Duproles.RepetitionMainKey = append(qualityinfo.Duproles.RepetitionMainKey, mainkey.String())
		}

	}
	qualityinfo.VerifyFieldNumber = vertifynum
	qualityinfo.VerifyRulerNumber = watchnum //理论上统计不到，先放着
	qualityexist, err := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, id)
	var hitsinfo source.QualityHits3
	json.Unmarshal(qualityexist, &hitsinfo)
	var isexist bool
	if err != nil {
		if strings.Contains(err.Error(), "Error 404") {
			isexist = true
		}
	}
	var tq source.QualityHits3
	json.Unmarshal(qualityexist, &tq)
	//qualityinfo.ExplorationTime = gjson.Get(string(redev), "hits.hits.0._source.updatetime").String()
	problemsourcedb := qualityinfo.ProjectID + "_wt"
	var problemtbname, cf_tbname string
loop:
	if tasktype == 1 {
		//针对新版本任务运行
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		qualityinfo.DsVersion = tasktype
		if strings.Count(updatetime, "-") == 1 {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		} else {
			qualityinfo.ExplorationTime = updatetime
		}

	} else if tasktype == 0 {
		//针对历史任务兼容：初始化元数据至最新探查时间所在的月份表中(es中存储)
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		//ds同步历史任务，同步一次自动将该版本信息也更新至最新
		qualityinfo.DsVersion = 1
		if tq.QualityTableInfo.ExplorationTime == "" {
			qualityinfo.ExplorationTime = updatetime
		} else {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		}
	} else {
		//针对最外层同步(历史任务和新建任务同步)
		var isyear bool
		for _, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			fmt.Println("dateString:", dateString)
			fmt.Println("YearMonth:", v.YearMonth)
			if v.YearMonth == dateString {
				tasktype = v.History
				isyear = true
				fmt.Println("tasktype:", tasktype)
			}
		}
		if isyear {
			goto loop
		} else {
			tasktype = 1
			goto loop
		}

	}
	//找到对应的所有问题表
	engineinfo, err := dbutil.EngineInfo(qualityinfo.Engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return "", err
	}
	qualityinfo.Enginename = engineinfo.EngineName
	var sqltxt, cf_sqltxt, normalsqltxt string
	switch engineinfo.EngineName {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	case common.HIVE, common.Inceptor:
		sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", problemsourcedb, cf_tbname)
	case common.ODPS:
		sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, cf_tbname)
	case common.DMDB:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s"`, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	}
	restxt, err1 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, sqltxt)
	if err1 != nil {
		fmt.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
		//当报问题表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err1.Error(), "does not exist") || strings.Contains(err1.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			return "", err1
		}
	}
	cf_restxt, err2 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, cf_sqltxt)
	//{200 Success [map[COUNT(*):3]] map[COUNT(*):BIGINT] [map[COUNT(*):BIGINT]]}  dtameng
	//{200 Success [map[count:4]] map[count:int8] [map[count:int8]]} [map[count:4]]  teryx
	if err2 != nil {
		fmt.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
		//当报重复表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err2.Error(), "does not exist") || strings.Contains(err2.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
			return "", err2
		}
	}
	var rescount, cfcount float64
	rescount = SqlDataTransCount(restxt, engineinfo.EngineName)
	cfcount = SqlDataTransCount(cf_restxt, engineinfo.EngineName)
	//运行一次，问题数据量为空，不记录
	if rescount == 0 && cfcount == 0 {
		return "不存在问题数据", nil
	}
	normalrestxt, err := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, normalsqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		return "", err
	}
	normalcount := SqlDataTransCount(normalrestxt, engineinfo.EngineName)
	var scoreinfos source.RulerScore
	if ishistory == false {
		if qualityinfo.Duproles.InputType == 0 {
			qualityinfo.VerifyRecord = int(normalcount) - int(cfcount)
		} else {
			qualityinfo.VerifyRecord = int(normalcount)
		}
		if rescount != 0 {
			//统计问题数据
			qualityinfo.ProblemRecord = int(rescount)
			//qualityinfo.ProblemQualifyRate = float64(qualityinfo.ProblemRecord) / normalcount
			if qualityinfo.VerifyRecord != 0 {
				qualityinfo.ProblemQualifyRate = TruncateDecimal(float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord), 2)
				//qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord)), 64)
			} else {
				qualityinfo.ProblemQualifyRate = 0
			}
			qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", 100-qualityinfo.ProblemQualifyRate), 64)
			//OneTableStatic(tancha_redev, engineinfo, &qualityinfo, id, qualityinfo.Database, problemsourcedb, problemtbname, qualityinfo.VerifyRecord)
			//qualityinfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber + watchnum

			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 3
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err)
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				//qualityinfo.Duproles.RepetitionRate = qualityinfo.Duproles.RepetitionNumber / float64(qualityinfo.VerifyRecord)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate

			} else {
				qualityinfo.ProblemType = 2
			}
			//增加对质量分等指标的统计
			//scoreinfos, _ = ScoreStatic(qualityinfo.Fieldinfo, qualityinfo.ProjectID, qualityinfo.RepetitionRate, watchruler)
		} else {
			//不统计问题数据,查看是否统计重复数据
			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 1
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err.Error())
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate
				if watchruler {
					scoreinfos.Normative = scoreinfos.Normative + (100 - qualityinfo.RepetitionRate)
					scoreinfos.TableQualityScore = scoreinfos.Normative
				}
			}
		}
	} else {
		if rescount != 0 {
			qualityinfo.ProblemRecord = int(rescount)
			if cfcount != 0 {
				qualityinfo.ProblemType = 3
			} else {
				qualityinfo.ProblemType = 2
			}
		} else {
			if cfcount != 0 {
				qualityinfo.ProblemType = 1
			}
		}

	}
	qualityinfo.Id = id
	qualityinfo.Module = 7
	if !isexist {
		//存在记录
		qualityinfo.HistoryLogInfo = tq.QualityTableInfo.HistoryLogInfo
		var isupdate bool
		fmt.Println("====kaishile===")
		for i, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			fmt.Println("datastring:", v.YearMonth, dateString, id)
			if v.YearMonth == dateString {
				isupdate = true
				//更新1 +更新2
				qualityinfo.HistoryLogInfo[i].ProblemQualifyRate = qualityinfo.ProblemQualifyRate
				qualityinfo.HistoryLogInfo[i].ProblemRecord = qualityinfo.ProblemRecord
				qualityinfo.HistoryLogInfo[i].VerifyRecord = qualityinfo.VerifyRecord
				qualityinfo.HistoryLogInfo[i].VerifyRulerNumber = qualityinfo.VerifyRulerNumber
				qualityinfo.HistoryLogInfo[i].VerifyFieldNumber = qualityinfo.VerifyFieldNumber
				//qualityinfo.HistoryLogInfo[i].DupDataResource = qualityinfo.DupDataResource
				qualityinfo.HistoryLogInfo[i].Duproles = qualityinfo.Duproles
				qualityinfo.HistoryLogInfo[i].Fieldinfo = qualityinfo.Fieldinfo
				qualityinfo.HistoryLogInfo[i].RulerScore = scoreinfos
				qualityinfo.HistoryLogInfo[i].YearMonth = dateString
				qualityinfo.HistoryLogInfo[i].ProblemType = qualityinfo.ProblemType
				if tasktype == 1 {
					qualityinfo.HistoryLogInfo[i].History = 1
				} else {
					qualityinfo.HistoryLogInfo[i].History = 0
				}
				fmt.Println("HistoryLogInfo:", i, qualityinfo.Fieldinfo)
				break
			}
		}
		if !isupdate {
			//没有对应年月记录
			//更新1 + 追加2
			fmt.Println("yunxingisupdate:", id)
			var loginfo source.HistoryLogInfo
			loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
			loginfo.ProblemRecord = qualityinfo.ProblemRecord
			loginfo.VerifyRecord = qualityinfo.VerifyRecord
			loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
			loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
			//loginfo.DupDataResource = qualityinfo.DupDataResource
			loginfo.Duproles = qualityinfo.Duproles
			loginfo.Fieldinfo = qualityinfo.Fieldinfo
			loginfo.RulerScore = scoreinfos
			loginfo.YearMonth = dateString
			loginfo.ProblemType = qualityinfo.ProblemType
			if tasktype == 1 {
				loginfo.History = 1
			} else {
				loginfo.History = 0
			}
			qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		}
		err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.QualityProblem, "true", id, qualityinfo)
	} else {
		//不存在
		//if rescount == 0 && cfcount == 0 {
		//	return "成功", nil
		//}
		//添加1-最外层数据+ 添加2-内部日志记录
		var loginfo source.HistoryLogInfo
		loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
		loginfo.ProblemRecord = qualityinfo.ProblemRecord
		loginfo.VerifyRecord = qualityinfo.VerifyRecord
		loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
		loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
		//loginfo.DupDataResource = qualityinfo.DupDataResource
		loginfo.Duproles = qualityinfo.Duproles
		loginfo.Fieldinfo = qualityinfo.Fieldinfo
		loginfo.RulerScore = scoreinfos
		loginfo.YearMonth = dateString
		loginfo.ProblemType = qualityinfo.ProblemType
		if tasktype == 1 {
			loginfo.History = 1
		} else {
			loginfo.History = 0
		}
		qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		err = esutil.AddWithAimID(common.DataBaseMetaData, common.QualityProblem, id, qualityinfo)
		if err != nil {
			fmt.Println("addes:", err.Error())
		}
	}
	if err != nil {
		logger.Error.Println(err)
		return "失败", err
	}
	return "成功", nil
}

// 有中间组件的质量探查
func QualityTanchaMidOneTableStatic(sourceinfo source.QualityDataInfo, updatetime string, ishistory bool, inputtype, tasktype int) (string, error) {
	var qualityinfo source.QualityTableInfo
	qualityinfo.Engineid = sourceinfo.NewTBinfo.Engineid
	qualityinfo.Database = sourceinfo.NewTBinfo.Dbname
	qualityinfo.SourceDb = sourceinfo.NewTBinfo.Schema
	qualityinfo.Tbname = sourceinfo.NewTBinfo.Tbname
	qualityinfo.Enginetype = sourceinfo.NewTBinfo.Tbtype
	qualityinfo.Duproles.InputType = sourceinfo.InputType
	qualityinfo.ProjectID = sourceinfo.ProjectID
	qualityinfo.MetadataIdNull = sourceinfo.MetadataIdNull
	qualityinfo.Module = 7
	//格式化年份和月份
	var dateString string
	if updatetime != "" {
		if strings.Count(updatetime, "-") != 1 {
			dateString = updatetime[:strings.LastIndex(updatetime, "-")]
		} else {
			dateString = updatetime
		}
	} else {
		currentTime := time.Now()
		dateString = currentTime.Format("2006-01")
	}
	//质量探查查询
	//tancha_redev, err := SearchDev(sourceinfo.Cid, qualityinfo.ProjectID, 0)
	tancha_redev, err := esutil.SearchByID(common.DataBaseLab, common.TbDev, sourceinfo.Cid)
	if err != nil {
		logger.Error.Printf("查询表元数据信息失败:%s, %s", err.Error())
		//return result, err
	}
	var watchnum int
	var watchruler bool

	fieldinfos := gjson.Get(tancha_redev, "fieldinfos").Array()
	for _, v := range fieldinfos {
		if v.Get("fieldbasicroles").String() != "" {
			//基础探查
			watchnum++
			continue
		}
		if v.Get("fielddeeproles").String() != "" {
			watchnum++
		}
	}
	mainkeys := gjson.Get(tancha_redev, "mainkeyfield").Array()
	for _, mainkey := range mainkeys {
		qualityinfo.Duproles.InputType = 1
		watchruler = true
		qualityinfo.Duproles.RepetitionMainKey = append(qualityinfo.Duproles.RepetitionMainKey, mainkey.String())
	}

	//问题原表是否有记录，有则更新，无则添加
	qualityexist, err := serveresclient.NewESClientCl.SearchByIDResult(common.DataBaseMetaData, common.QualityProblem, sourceinfo.Cid)
	var hitsinfo source.QualityHits3
	json.Unmarshal(qualityexist, &hitsinfo)
	var isexist bool
	if err != nil {
		if strings.Contains(err.Error(), "Error 404") {
			isexist = true
		}
	}
	var tq source.QualityHits3
	json.Unmarshal(qualityexist, &tq)
	//qualityinfo.ExplorationTime = gjson.Get(string(redev), "hits.hits.0._source.updatetime").String()
	problemsourcedb := sourceinfo.ProjectID + "_wt"
	var problemtbname, cf_tbname string
loop:
	if tasktype == 1 {
		//针对新版本任务运行
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		qualityinfo.DsVersion = tasktype
		if strings.Count(updatetime, "-") == 1 {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		} else {
			qualityinfo.ExplorationTime = updatetime
		}

	} else if tasktype == 0 {
		//针对历史任务兼容：初始化元数据至最新探查时间所在的月份表中(es中存储)
		problemtbname = qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		cf_tbname = "cf_" + qualityinfo.SourceDb + "_" + qualityinfo.Tbname
		//ds同步历史任务，同步一次自动将该版本信息也更新至最新
		qualityinfo.DsVersion = 1
		if tq.QualityTableInfo.ExplorationTime == "" {
			qualityinfo.ExplorationTime = updatetime
		} else {
			qualityinfo.ExplorationTime = tq.QualityTableInfo.ExplorationTime
		}
	} else {
		//针对最外层同步(历史任务和新建任务同步)
		var isyear bool
		for _, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			fmt.Println("dateString:", dateString)
			fmt.Println("YearMonth:", v.YearMonth)
			if v.YearMonth == dateString {
				tasktype = v.History
				isyear = true
				fmt.Println("tasktype:", tasktype)
			}
		}
		if isyear {
			goto loop
		} else {
			tasktype = 1
			goto loop
		}

	}
	//找到对应的所有问题表
	engineinfo, err := dbutil.EngineInfo(sourceinfo.NewTBinfo.Engineid)
	if err != nil {
		logger.Error.Println("查询ODS Layer Engine Info信息失败：", err.Error())
		return "", err
	}
	qualityinfo.Enginename = engineinfo.EngineName
	var sqltxt, cf_sqltxt, normalsqltxt string
	switch engineinfo.EngineName {
	case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	case common.HIVE, common.Inceptor:
		sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("select count(*) from `%s`.`%s`", problemsourcedb, cf_tbname)
	case common.ODPS:
		sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select count(*) from `%s.%s` ", problemsourcedb, cf_tbname)
	case common.DMDB:
		sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s"`, problemsourcedb, problemtbname)
		normalsqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, qualityinfo.SourceDb, qualityinfo.Tbname)
		cf_sqltxt = fmt.Sprintf(`select count(*) from "%s"."%s" `, problemsourcedb, cf_tbname)
	}
	restxt, err1 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, sqltxt)
	if err1 != nil {
		fmt.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
		//当报问题表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err1.Error(), "does not exist") || strings.Contains(err1.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败：", err1.Error(), "执行SQL为：", sqltxt)
			return "", err1
		}
	}
	cf_restxt, err2 := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, cf_sqltxt)
	//{200 Success [map[COUNT(*):3]] map[COUNT(*):BIGINT] [map[COUNT(*):BIGINT]]}  dtameng
	//{200 Success [map[count:4]] map[count:int8] [map[count:int8]]} [map[count:4]]  teryx
	if err2 != nil {
		fmt.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
		//当报重复表不存在时，程序继续执行，不视为报错
		if !(strings.Contains(err2.Error(), "does not exist") || strings.Contains(err2.Error(), "Table not found")) {
			logger.Error.Println("执行MG语句失败2：", err2.Error(), "执行SQL为：", cf_sqltxt)
			return "", err2
		}
	}
	var rescount, cfcount float64
	rescount = SqlDataTransCount(restxt, engineinfo.EngineName)
	cfcount = SqlDataTransCount(cf_restxt, engineinfo.EngineName)
	//运行一次，问题数据量为空，不记录
	if rescount == 0 && cfcount == 0 {
		return "不存在问题数据", nil
	}
	normalrestxt, err := collect.MGExecuteQuerySqlV2(engineinfo, qualityinfo.Database, normalsqltxt)
	if err != nil {
		fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", normalsqltxt)
		return "", err
	}
	normalcount := SqlDataTransCount(normalrestxt, engineinfo.EngineName)
	var scoreinfos source.RulerScore
	if ishistory == false {
		if qualityinfo.Duproles.InputType == 0 {
			qualityinfo.VerifyRecord = int(normalcount) - int(cfcount)
		} else {
			qualityinfo.VerifyRecord = int(normalcount)
		}
		if rescount != 0 {
			//统计问题数据
			qualityinfo.ProblemRecord = int(rescount)
			//qualityinfo.ProblemQualifyRate = float64(qualityinfo.ProblemRecord) / normalcount
			if qualityinfo.VerifyRecord != 0 {
				qualityinfo.ProblemQualifyRate = TruncateDecimal(float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord), 2)
				//qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", float64(qualityinfo.ProblemRecord)*100/float64(qualityinfo.VerifyRecord)), 64)
			} else {
				qualityinfo.ProblemQualifyRate = 0
			}
			qualityinfo.ProblemQualifyRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", 100-qualityinfo.ProblemQualifyRate), 64)
			//ProMidOneTableStatic(tancha_redev, engineinfo, &qualityinfo, sourceinfo.Cid, qualityinfo.Database, problemsourcedb, problemtbname, qualityinfo.VerifyRecord)
			qualityinfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber + watchnum

			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 3
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err)
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				//qualityinfo.Duproles.RepetitionRate = qualityinfo.Duproles.RepetitionNumber / float64(qualityinfo.VerifyRecord)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate

			} else {
				qualityinfo.ProblemType = 2
			}
			//增加对质量分等指标的统计
			scoreinfos, _ = ScoreStatic(qualityinfo.Fieldinfo, qualityinfo.ProjectID, qualityinfo.RepetitionRate, watchruler, 0)
		} else {
			//不统计问题数据,查看是否统计重复数据
			if cfcount != 0 {
				//统计重复数据
				qualityinfo.ProblemType = 1
				number, err := OneTableStaticRepitation(engineinfo, qualityinfo.Database, problemsourcedb, cf_tbname)
				if err != nil {
					logger.Error.Println(err.Error())
				}
				qualityinfo.Duproles.RepetitionNumber = number
				qualityinfo.Duproles.RepetitionRate = TruncateDecimal(qualityinfo.Duproles.RepetitionNumber*100/normalcount, 2)
				//qualityinfo.Duproles.RepetitionRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", qualityinfo.Duproles.RepetitionNumber*100/normalcount), 64)
				qualityinfo.RepetitionRate = qualityinfo.Duproles.RepetitionRate
				if watchruler {
					scoreinfos.Normative = scoreinfos.Normative + (100 - qualityinfo.RepetitionRate)
					scoreinfos.TableQualityScore = scoreinfos.Normative
				}
			}
		}
	} else {
		if rescount != 0 {
			qualityinfo.ProblemRecord = int(rescount)
			if cfcount != 0 {
				qualityinfo.ProblemType = 3
			} else {
				qualityinfo.ProblemType = 2
			}
		} else {
			if cfcount != 0 {
				qualityinfo.ProblemType = 1
			}
		}

	}
	qualityinfo.Id = sourceinfo.Cid

	if !isexist {
		//存在记录
		qualityinfo.HistoryLogInfo = tq.QualityTableInfo.HistoryLogInfo
		var isupdate bool
		fmt.Println("====kaishile===")
		for i, v := range hitsinfo.QualityTableInfo.HistoryLogInfo {
			if v.YearMonth == dateString {
				isupdate = true
				//更新1 +更新2
				qualityinfo.HistoryLogInfo[i].ProblemQualifyRate = qualityinfo.ProblemQualifyRate
				qualityinfo.HistoryLogInfo[i].ProblemRecord = qualityinfo.ProblemRecord
				qualityinfo.HistoryLogInfo[i].VerifyRecord = qualityinfo.VerifyRecord
				qualityinfo.HistoryLogInfo[i].VerifyRulerNumber = qualityinfo.VerifyRulerNumber
				qualityinfo.HistoryLogInfo[i].VerifyFieldNumber = qualityinfo.VerifyFieldNumber
				//qualityinfo.HistoryLogInfo[i].DupDataResource = qualityinfo.DupDataResource
				qualityinfo.HistoryLogInfo[i].Duproles = qualityinfo.Duproles
				qualityinfo.HistoryLogInfo[i].Fieldinfo = qualityinfo.Fieldinfo
				qualityinfo.HistoryLogInfo[i].RulerScore = scoreinfos
				qualityinfo.HistoryLogInfo[i].YearMonth = dateString
				qualityinfo.HistoryLogInfo[i].ProblemType = qualityinfo.ProblemType
				if tasktype == 1 {
					qualityinfo.HistoryLogInfo[i].History = 1
				} else {
					qualityinfo.HistoryLogInfo[i].History = 0
				}
				fmt.Println("HistoryLogInfo:", i, qualityinfo.Fieldinfo)
				break
			}
		}
		if !isupdate {
			//没有对应年月记录
			//更新1 + 追加2
			var loginfo source.HistoryLogInfo
			loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
			loginfo.ProblemRecord = qualityinfo.ProblemRecord
			loginfo.VerifyRecord = qualityinfo.VerifyRecord
			loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
			loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
			loginfo.Duproles = qualityinfo.Duproles
			loginfo.Fieldinfo = qualityinfo.Fieldinfo
			loginfo.RulerScore = scoreinfos
			loginfo.YearMonth = dateString
			loginfo.ProblemType = qualityinfo.ProblemType
			if tasktype == 1 {
				loginfo.History = 1
			} else {
				loginfo.History = 0
			}
			qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		}
		err = esutil.UpdByIDWithRefresh(common.DataBaseMetaData, common.QualityProblem, "true", sourceinfo.Cid, qualityinfo)
	} else {
		//不存在
		//添加1-最外层数据+ 添加2-内部日志记录
		var loginfo source.HistoryLogInfo
		loginfo.ProblemQualifyRate = qualityinfo.ProblemQualifyRate
		loginfo.ProblemRecord = qualityinfo.ProblemRecord
		loginfo.VerifyRecord = qualityinfo.VerifyRecord
		loginfo.VerifyRulerNumber = qualityinfo.VerifyRulerNumber
		loginfo.VerifyFieldNumber = qualityinfo.VerifyFieldNumber
		loginfo.Duproles = qualityinfo.Duproles
		loginfo.Fieldinfo = qualityinfo.Fieldinfo
		loginfo.RulerScore = scoreinfos
		loginfo.YearMonth = dateString
		loginfo.ProblemType = qualityinfo.ProblemType
		if tasktype == 1 {
			loginfo.History = 1
		} else {
			loginfo.History = 0
		}
		qualityinfo.HistoryLogInfo = append(qualityinfo.HistoryLogInfo, loginfo)
		err = esutil.AddWithAimID(common.DataBaseMetaData, common.QualityProblem, sourceinfo.Cid, qualityinfo)
		if err != nil {
			fmt.Println("addes:", err.Error())
		}
	}
	if err != nil {
		logger.Error.Println(err)
		return "失败", err
	}
	return "成功", nil
}
func CommonGetTanchaTableInfo2(sourceid string, engineinfo source.EngineInfo, problemtype int, dbname, sourcedb, tbname, problemsourcedb, problemtable string, fieldinfoarr, describearr []string) (string, error) {
	var sqltxt, cf_sqltxt, normalsqltxt string
	if problemtype == 1 {
		//重复数据
		var cf_tbname string
		if problemtable == "" {
			cf_tbname = "cf_" + sourcedb + "_" + tbname
		} else {
			cf_tbname = "cf_" + sourcedb + "_" + problemtable
		}

		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.HIVE, common.Inceptor:
			cf_sqltxt = fmt.Sprintf("select * from `%s.%s`", problemsourcedb, cf_tbname)
		case common.ODPS:
			fieldinfoarr = append(fieldinfoarr, "cf_rksj")
			cf_sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, cf_tbname)
		case common.DMDB:
			fieldinfoarr = append(fieldinfoarr, "CF_RKSJ")
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
		}
		describearr = append(describearr, "")
		//res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "重复数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})
		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}

		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//

		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---
			number, err = RepetitionWriteDataToFile2(res2.Data, file, number, style, sw, fieldinfoarr, engineinfo.EngineName, cf_tbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

			//1、字段名称、来源表、来源字段、问题描述、规则类型 格式如下

		}
		// 保存文件
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil

	} else if problemtype == 2 {
		extract, _ := SearchTbAccess(sourceid)
		//问题数据
		//需要访问获取全部数据
		//根据每行的dwd_ruleid 中的id 找到
		var problemtbname string
		if problemtable == "" {
			problemtbname = sourcedb + "_" + tbname
		} else {
			problemtbname = sourcedb + "_" + problemtable
		}
		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.HIVE, common.Inceptor:
			//sqltxt = fmt.Sprintf("select `%s` from `%s.%s` ", fieldinfos, problemsourcedb, problemtbname)
			sqltxt = fmt.Sprintf("select * from `%s.%s` ", problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
			fieldinfoarr = append(fieldinfoarr, "DWD_RKSJ")
		}
		describearr = append(describearr, "")
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "问题数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		//mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: "wtjl"})
		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		//descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: "问题记录"})
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			number, _ = CombData(res2.Data, extract, sw, style, number, fieldinfoarr, engineinfo.EngineName, problemtbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
		}
		// 保存文件
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil

	} else if problemtype == 3 {
		//重复数据、问题数据都有
		extract, _ := SearchTbAccess(sourceid)
		cffieldinfoarr := fieldinfoarr
		var problemtbname, cf_tbname string
		if problemtable == "" {
			problemtbname = sourcedb + "_" + tbname
			cf_tbname = "cf_" + sourcedb + "_" + tbname
		} else {
			problemtbname = sourcedb + "_" + problemtable
			cf_tbname = "cf_" + sourcedb + "_" + problemtable
		}

		switch engineinfo.EngineName {
		case common.STORK, common.TERYX, common.GAUSSDBA, common.GAUSSDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.TDSQL:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.HIVE, common.Inceptor:
			sqltxt = fmt.Sprintf("select * from `%s.%s` ", problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf("select * from `%s.%s`", problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.ODPS:
			sqltxt = fmt.Sprintf("set odps.sql.allow.fullscan = true;select * from `%s.%s` ", problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "dwd_rksj")
			cffieldinfoarr = append(fieldinfoarr, "cf_rksj")
		case common.DMDB:
			sqltxt = fmt.Sprintf(`select * from "%s"."%s"`, problemsourcedb, problemtbname)
			cf_sqltxt = fmt.Sprintf(`select * from "%s"."%s" `, problemsourcedb, cf_tbname)
			fieldinfoarr = append(fieldinfoarr, "DWD_RKSJ")
			cffieldinfoarr = append(fieldinfoarr, "CF_RKSJ")
		}
		describearr = append(describearr, "")
		res, err := collect.MGExecuteSelectSql(engineinfo, dbname, sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", sqltxt)
		}
		var rsuid string
		for _, v := range res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var number int = 2
		var filename string
		// 创建一个新的 XLSX 文件
		file := xlsx.NewFile()
		defer func() {
			if err := file.Close(); err != nil {
				fmt.Println(err)
			}
		}()
		sheetName := "问题数据"
		index, _ := file.NewSheet(sheetName)
		file.SetActiveSheet(index)
		sw, err := file.NewStreamWriter(sheetName)
		if err != nil {
			fmt.Println(err)

		}
		style, err := file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName = file.GetSheetName(file.GetActiveSheetIndex())
		var mySlice []interface{}

		for _, header := range fieldinfoarr {
			mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		//mySlice = append(mySlice, xlsx.Cell{StyleID: style, Value: "wtjl"})
		//cell := indexToColumnName(i+1) + "1"
		cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = sw.SetRow(cell, mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//
		var descSlice []interface{}

		for _, describe := range describearr {
			descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: describe})
		}
		//descSlice = append(descSlice, xlsx.Cell{StyleID: style, Value: "问题记录"})
		desccell, _ := xlsx.CoordinatesToCellName(1, 2)
		err = sw.SetRow(desccell, descSlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		for {

			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---

			number, err = CombData(res2.Data, extract, sw, style, number, fieldinfoarr, engineinfo.EngineName, problemtbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

		}
		if err := sw.Flush(); err != nil {
			fmt.Println(err)
		}
		//重复数据
		cf_res, err := collect.MGExecuteSelectSql(engineinfo, dbname, cf_sqltxt, common.Rows)
		if err != nil {
			fmt.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
			logger.Error.Println("执行MG语句失败：", err.Error(), "执行SQL为：", cf_sqltxt)
		}
		for _, v := range cf_res.Data {
			for k2, v2 := range v {
				if k2 == "rsUid" {
					logger.Info.Println("k2,v2:", k2, v2)
					rsuid = fmt.Sprintf("%v", v2)
					break
				}
			}
		}
		var cf_number int = 2

		sheetName2 := "重复数据"
		index2, _ := file.NewSheet(sheetName2)
		file.SetActiveSheet(index2)
		cf_sw, err := file.NewStreamWriter(sheetName2)
		if err != nil {
			fmt.Println(err)

		}
		style, err = file.NewStyle(&xlsx.Style{
			Alignment: &xlsx.Alignment{
				Horizontal: "left",
				Vertical:   "center",
			},
		})

		// 获取当前活动工作表名称
		sheetName2 = file.GetSheetName(file.GetActiveSheetIndex())
		var cf_mySlice []interface{}

		for _, header := range cffieldinfoarr {
			cf_mySlice = append(cf_mySlice, xlsx.Cell{StyleID: style, Value: header})
		}
		//cell := indexToColumnName(i+1) + "1"
		cf_cell, _ := xlsx.CoordinatesToCellName(1, 1)
		err = cf_sw.SetRow(cf_cell, cf_mySlice)
		//err = sw.SetRow(cell, mySlice)
		if err != nil {
			logger.Error.Println("写入表头失败err:", err)
			return filename, err
		}
		//

		for {
			res2, err := collect.MGSelectSqlResult(engineinfo, dbname, rsuid)
			if err != nil {
				logger.Error.Println("test2:", err.Error())
			}
			_, err = json.Marshal(res2)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
			}
			if len(res2.Data) == 0 {
				break
			}
			//数据处理---
			cf_number, err = RepetitionWriteDataToFile2(res2.Data, file, cf_number, style, cf_sw, cffieldinfoarr, engineinfo.EngineName, cf_tbname)
			if err != nil {
				logger.Error.Println("zhuanhuan:", err.Error())
				return filename, err
			}

		}

		// 保存文件
		if err := cf_sw.Flush(); err != nil {
			fmt.Println(err)
		}

		filename = common.MetadataDownloadPath + tbname + "_" + time.Now().Format("2006-01-02") + ".xlsx"
		if err := file.SaveAs(filename); err != nil {
			logger.Error.Println("保存文件出错：", err)
			return filename, err
		}
		return filename, nil
	}
	fmt.Println(sqltxt, cf_sqltxt, normalsqltxt)
	return "", nil
}

const dateLayout = "2006-01-02"

// inRegion 判断当天是否在有效区间内，并根据 lastupdate 决定最终结果
// lastupdate 格式为 "YYYY-MM-DD" 或空字符串

/*
-1:默认值，不存在表级别时效性校验
0:不在重复区间内，按 100 分计算
1:在重复区间内，区间内有更新，按 100 分计算
2:在重复区间内，区间内无更新，按 0 分计算
*/
func inRegion(ruleInfo source.QualityRule, lastupdate string) int {
	timeliness := ruleInfo.Timeliness
	//now := time.Now().AddDate(0, 0, -1) //实际按前一天日期计算
	realnow := time.Now()

	// 为了只比较日期，忽略时间，将所有时间都 Truncate 到当天的零点
	nowDate := realnow.Truncate(24 * time.Hour)

	if timeliness.TimelinessRepeat == "never" {
		// 解析开始时间
		if timeliness.TimelinessStart != "" {
			startDate, err := time.Parse(dateLayout, timeliness.TimelinessStart)
			if err != nil {
				// 如果开始时间格式无效，则认为规则无效
				fmt.Println("inregion #1")
				return 0
			}
			//startDate = startDate.AddDate(0, 0, -1).Truncate(24 * time.Hour) //开始时间提前1天，计算运行前一天的数据

			// 如果当前日期在开始日期之前
			if nowDate.Before(startDate) {
				fmt.Println("inregion #2")
				return 0
			}
		}

		// 解析结束时间（如果存在）
		if timeliness.TimelinessEnd != "" {
			endDate, err := time.Parse(dateLayout, timeliness.TimelinessEnd)
			if err != nil {
				// 如果结束时间格式无效，则认为规则无效
				fmt.Println("inregion #3")
				return 0
			}
			endDate = endDate.Truncate(24 * time.Hour)
			// 如果当前日期在结束日期之后
			if nowDate.After(endDate) {
				fmt.Println("inregion #4")
				return 0
			}
		}
		if checkNever(lastupdate, realnow) {
			fmt.Println("inregion #5")
			return 1
		} else {
			fmt.Println("inregion #6")
			return 2
		}
	}

	nowInd, nowInRepeatSection := isDateInRepeatSection(realnow, timeliness)
	if !nowInRepeatSection {
		fmt.Println("inregion #7")
		return 0 // 不在重复区间内
	}

	// 3. 当且仅当“当前日期”同时在“主要时间区间”和“重复区间”内时，才检查 lastupdate
	if lastupdate == "" {
		fmt.Println("inregion #8")
		return 2 // lastupdate 为空，无法满足条件
	}

	lastupdateDate, err := time.Parse(dateLayout, lastupdate)
	if err != nil {
		fmt.Println("inregion #9")
		return 2 // lastupdate 格式错误
	}

	// 判断 lastupdate 的日期是否也在同一个重复区间内
	lastInd, lastInRepeat := isDateInRepeatSection(lastupdateDate, timeliness)
	if lastInRepeat && nowInd == lastInd {
		fmt.Println("inregion #10")
		return 1
	} else {
		fmt.Println("inregion #11")
		return 2
	}
}

// isDateInRepeatSection 是一个辅助函数，用于检查给定的日期是否在重复区间内
func isDateInRepeatSection(t time.Time, timeliness source.Timeliness) (int, bool) {
	switch timeliness.TimelinessRepeat {
	case "week":
		return -1, checkWeek(t, timeliness.RepeatSection)
	case "month":
		return -1, checkMonth(t, timeliness.RepeatSection)
	case "year":
		return checkYear(t, timeliness.RepeatSection)
	default:
		return -1, false
	}
}

func checkNever(lastupdate string, now time.Time) bool {
	// 如果 lastupdate 为空，直接返回 false
	if lastupdate == "" {
		return false
	}

	// 解析 lastupdate 字符串
	lastupdateDate, err := time.Parse(dateLayout, lastupdate)
	if err != nil {
		return false // 格式错误，无法比较
	}

	// 为了精确比较日期，忽略时间部分
	lastupdateDate = lastupdateDate.Truncate(24 * time.Hour)
	today := now.Truncate(24 * time.Hour)

	if lastupdateDate.Equal(today) {
		return true
	}
	return false
	// 如果 lastupdateDate >= today，则返回 true
	//return !lastupdateDate.Before(today)
}

// checkWeek 检查指定日期是否在星期的重复区间内
func checkWeek(t time.Time, sections [][]string) bool {
	n := time.Now()

	tYear, tWeek := t.ISOWeek()
	nowYear, nowWeek := n.ISOWeek()

	dayMap := map[string]time.Weekday{
		"Sun": time.Sunday, "Mon": time.Monday, "Tue": time.Tuesday,
		"Wed": time.Wednesday, "Thu": time.Thursday, "Fri": time.Friday,
		"Sat": time.Saturday,
	}
	currentDay := t.Weekday()

	for _, section := range sections {
		if len(section) != 2 {
			continue
		}
		startDay, ok1 := dayMap[section[0]]
		endDay, ok2 := dayMap[section[1]]
		if !ok1 || !ok2 {
			continue
		}

		if startDay <= endDay {
			if currentDay >= startDay && currentDay <= endDay {
				if tYear != nowYear || tWeek != nowWeek {
					return false // 不在同一周，直接返回false
				}
				return true
			}
		} else { // 跨周情况 (e.g., Sat -> Tue)
			if currentDay >= startDay || currentDay <= endDay {
				duration := n.Sub(t)
				if duration < 0 {
					duration = -duration
				}
				if duration < sevenDays {
					return true
				}
			}
		}
	}
	return false
}

const sevenDays = 7 * 24 * time.Hour

// checkMonth 检查指定日期是否在月份的重复区间内
func checkMonth(t time.Time, sections [][]string) bool {
	n := time.Now()
	if t.Year() != n.Year() || t.Month() != n.Month() {
		return false
	}

	currentDay := fmt.Sprintf("%02d", t.Month())
	for _, section := range sections {
		if len(section) != 2 {
			continue
		}
		if currentDay >= section[0] && currentDay <= section[1] {
			return true
		}
	}
	return false
}

// checkYear 检查指定日期是否在年份的重复区间内
func checkYear(t time.Time, sections [][]string) (int, bool) {
	currentMonthDay := t.Format("0102")
	for ind, section := range sections {
		if len(section) != 2 {
			continue
		}
		if currentMonthDay >= section[0] && currentMonthDay <= section[1] {
			return ind, true
		}
	}
	return -1, false
}
