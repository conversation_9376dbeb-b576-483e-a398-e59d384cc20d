/*
Copyright (c) Datatom Software, Inc.(2020)
Author: 陈洪彪（<EMAIL>）
Creating Time: 2020-05-08

存储元数据开发metadata development相关结构体信息
*/

package source

// DwsPublishInfo 专题发布信息
type DwsPublishInfo struct {
	ID           string `json:"id"`           // id
	Type         int    `json:"type"`         // 发布服务类型：1 : API 共享 2 :库表推送
	AccessID     string `json:"accessid"`     // 对应采集任务id
	OnlineStatus bool   `json:"onlinestatus"` // 上线状态
}

type ReqTaskDenpendence struct {
	LayerID  string `json:"layerid"`  // 分层id
	SourceID string `json:"sourceid"` // 存储源id
	TableID  string `json:"tableid"`  // 表id
}

// TaskStatus 任务状态请求参数
type TaskStatus struct {
	TaskID    string `json:"taskid"`    // 任务id
	DataxType int    `json:"dataxtype"` // 采集任务类型（0.自定义 1.单例离线 2.单例实时 3.批量离线 4.实时抽取 5.专题表落地 6.库表推送）
	Species   string `json:"species"`   // 任务类别, datax/dataprocess/dws ，datax：采集任务，dataprocess：治理任务，dws：专题任务
}

// LayerTaskInfo 依赖分层的上线任务信息
type LayerTaskInfo struct {
	ID       string    `json:"id"`       // id
	JobID    string    `json:"jobid"`    // 任务id
	MetatbID string    `json:"metatbid"` // 元数据表id
	AccessID string    `json:"accessid"` // 对应采集任务id
	Name     string    `json:"name"`     // 任务名称
	Species  string    `json:"species"`  // 任务类别, datax/dataprocess/dws/module,datax：采集任务，dataprocess：治理任务，dws：专题任务,module：模型管理;dataservice 数据服务
	TaskType int       `json:"tasktype"` // 采集任务类型（0.自定义 1.单例离线 2.单例实时 3.批量离线 4.实时抽取 5.专题表落地 6.库表推送）
	Tags     []TagList `json:"tags"`     // 标签列表
	Online   int       `json:"online"`   // 上线状态, 1:已上线,2:未上线
}

// ReqSourceDb 存储源请求结构体
type ReqSourceDb struct {
	LayerID     string `json:"layerid"`     // 分层id
	EngineID    string `json:"engineid"`    // 引擎id
	SourceID    string `json:"sourceid"`    // 存储源id
	ProjectID   string `json:"projectid"`   // 所属项目标识
	DbName      string `json:"dbname"`      // 数据库名称
	SourceName  string `json:"sourcename"`  // 存储源名称
	SourceType  string `json:"sourcetype"`  // 存储源类型
	DbExist     bool   `json:"dbexist"`     // 默认数据库是否已存在，true则存在
	SchemaExist bool   `json:"schemaexist"` // 默认schema是否已存在，true则已存在
	Mode        int    `json:"mode"`        // 1:删除, 2:移除
}

// MoveInfo 上移/下移分层/编目结构体
type MoveInfo struct {
	LayerID      string `json:"layerid"`      // 分层/编目id
	LayerNumber  int    `json:"layernumber"`  // 分层/编目编号
	TargetID     string `json:"targetid"`     // 目标分层/编目id
	TargetNumber int    `json:"targetnumber"` // 目标分层/编目编号

}

// MetaLayerBasicInfo 元数据层基本信息(conninfo)
type MetaLayerBasicInfo struct {
	ID        string             `json:"id,omitempty"` // 层id
	LayerName string             `json:"layername"`    // 层名称
	IsGather  bool               `json:"isgather"`     // 是否可用于数据采集,是则true
	ProjectID string             `json:"projectid"`    // 所属项目标识
	EngineID  string             `json:"engineid,omitempty"`
	Sourcedbs []MetaSourceDbList `json:"Sourcedbs,omitempty"`
}

type ReqLayer struct {
	MetaLayer
	DbExist     bool `json:"dbexist"`     // 默认数据库是否已存在，true则存在
	SchemaExist bool `json:"schemaexist"` // 默认schema是否已存在，true则已存在
}

type LayerReq struct {
	EngineID   string `json:"engineid"`   // 引擎id
	EngineName string `json:"enginename"` // 引擎名称,依数据库类型定义,如:hive、stork、dodox、datax
	NeedBasic  bool   `json:"needbasic"`  //去除统计相关，精简返回
	ProjectID  string `json:"projectid"`  // 所属项目标识
}

// MetaLayer 元数据层信息结构体
type MetaLayer struct {
	ID              string   `json:"id,omitempty"`                        // 层id
	Status          int      `json:"status"`                              // 层是否可修改存储引擎状态,1:引擎已被使用，指引擎中已经有表存在,2:引擎中存在用户添加的非默认的存储源,3:引擎中存储源被治理项目依赖,4:可以配置
	Name            string   `json:"name" validate:"required"`            // 层名称
	ShortName       string   `json:"shortname" validate:"required"`       // 层简称
	Describe        string   `json:"describe"`                            // 层描述
	IsGather        bool     `json:"isgather"`                            // 是否可用于数据采集,是则true
	IsGovern        bool     `json:"isgovern"`                            // 是否可用于数据治理,是则true
	IsBazaar        bool     `json:"isbazaar"`                            // 是否可用于数据集市/数据共享,是则true //6.0 去除专题模块，此参数弃用
	IsMetrics       bool     `json:"ismetrics"`                           //是否可用于数据指标
	EngineID        string   `json:"engineid" validate:"required"`        // 引擎id
	EngineName      string   `json:"enginename" validate:"required"`      // 引擎名称,依数据库类型定义,如:hive、stork、dodox、datax
	NameEngine      string   `json:"name_engine,omitempty"`               // admin 引擎注册存的name  udf那边展示使用。
	Occupied        string   `json:"occupied"`                            // 存储占用
	RemainSpace     string   `json:"remainspace"`                         // 剩余空间
	DBCount         int      `json:"dbcount"`                             // 存储源数量
	DBName          []string `json:"dbname"`                              // 存储源名称
	TBCount         int      `json:"tbcount"`                             // 数据表数量
	Number          int      `json:"number"`                              // 层编号
	Arrange         int      `json:"arrange"`                             // 对分层排序,与层编号不同点在于不受层上移下移影响,用于判定默认库
	DefaultName     string   `json:"defaultname"`                         // 默认名称,标识默认层
	DefaultDb       string   `json:"defaultdb" validate:"required"`       // 默认数据库
	DefaultSourceDb string   `json:"defaultsourcedb" validate:"required"` // 层默认存储源,对hive,即为默认数据库,对stork/teryx,为schema
	DefaultSourceID string   `json:"defaultsourceid"`                     // 层默认存储源id
	DbExist         bool     `json:"dbexist"`                             // 默认数据库是否已存在，true则存在
	SchemaExist     bool     `json:"sourceexist"`                         // 默认schema是否已存在，true则已存在
	ProjectID       string   `json:"projectid"`                           // 所属项目标识
	Forbid          bool     `json:"forbid"`                              // 没有层权限，禁止创建存储源
	Created         string   `json:"created"`                             // 创建时间
	Modified        string   `json:"modified"`                            // 修改时间
	NeedBasic       bool     `json:"needbasic"`                           //去除统计相关，精简返回
}

// MetaSourceDb  元数据开发--存储源/数据库信息结构体
type MetaSourceDb struct {
	ID          string       `json:"id"`      // 存储源id
	LayerID     string       `json:"layerid"` // 层id
	EngineID    string       `json:"engineid"`
	DbName      string       `json:"dbname"`      // 数据库名称
	SourceName  string       `json:"sourcename"`  // 存储源名称,对hive,即为默认数据库,对stork/teryx,为schema
	SourceType  string       `json:"sourcetype"`  // 存储源类型 hive/stork
	SchemaNames []string     `json:"schemanames"` // stork获取模式列表,该字段已自动失效
	StorageInfo StorageInfo  `json:"storageinfo"` // 存储源属性
	ProjectID   string       `json:"projectid"`   // 所属项目标识
	DbExist     bool         `json:"dbexist"`     // 默认数据库是否已存在，true则存在
	SchemaExist bool         `json:"schemaexist"` // 默认schema是否已存在，true则已存在
	Created     string       `json:"created"`     // 创建时间
	Modified    string       `json:"modified"`    // 修改时间
	Sync        SyncStrategy `json:"sync,omitempty"`
	//CheckLogs   []AccCheckLog `json:"checklog,omitempty"`    //引擎状态记录日志
	SyncLogs []SyncLog `json:"synclog,omitempty"` //同步信息记录日志
	Tables   []string  `json:"tables"`            //其下所有表id
	TBNames  []string  `json:"tbnames"`           //其下所有表名
}

// ---- 治理相关 ----↓
type TbList struct {
	TableID    string `json:"tableid"`
	TableName  string `json:"tablename"`
	DBName     string `json:"dbname"`
	EngineID   string `json:"engineid"`
	LayerID    string `json:"layerid"`
	RealDBName string `json:"realdbname"` //增对teryx加的dbname
}

// SourceItem 用于表输入的元数据
type SourceItem struct {
	MetaSourceDb
	Children []TbList `json:"children"`
}

//---- 治理相关 ----↑

// MetaSourceDbList 列举存储源所用结构体
type MetaSourceDbList struct {
	ID          string      `json:"id"`          // 存储源id
	SourceID    string      `json:"sourceid"`    // 存储源id
	LayerID     string      `json:"layerid"`     // 存储层id
	DbName      string      `json:"dbname"`      // 数据库名称
	EngineID    string      `json:"engineid"`    // 引擎 ID
	SourceName  string      `json:"sourcename"`  // 存储源名称
	SourceType  string      `json:"sourcetype"`  // 存储源类型 hive/stork
	StorageInfo StorageInfo `json:"storageinfo"` // 存储源属性
	Forbid      bool        `json:"forbid"`      // 没有数据权限的禁止删除
}

// StorageInfo 存储源属性
type StorageInfo struct {
	TBNum    int    `json:"tbnum"`    // 表数量
	TaskNum  int    `json:"tasknum"`  // 任务数量
	Identity string `json:"identity"` // 种类，wt(问题库)、default(默认数据库)、normal(标准数据库)
	Used     int    `json:"used"`     // 是否被使用，1表示被使用
}

// MoveTbReq 移动表请求内容
type MoveTbReq struct {
	ID          string `json:"id"`          // 表id
	CatalogID   string `json:"catalogid"`   // 目标最底层编目id
	CatalogName string `json:"catalogname"` // 目标最底层编目名称
}

// InputTbReq 单表导入请求内容
type InputTbReq struct {
	ExtractID  string   `json:"extractid" validate:"required"` // 抽取源id
	SchemaID   string   `json:"schemaid"`                      // 模式id
	CatalogID  string   `json:"catalogid"`                     // 表编目id
	DBName     string   `json:"dbname"`                        // 抽取源数据库名称/csv文件名称
	Schema     string   `json:"schema"`                        // sqlserver、stork、db2模式名称
	TableID    string   `json:"tableid" validate:"required"`   // 表id
	TBName     string   `json:"tablename" validate:"required"` // 表名称
	FieldNames []string `json:"fieldnames"`                    //字段名称

	//1、原先支持分布类型的数据库，方可至为true；其余false
	//2、只有分布有默认配置
	DefaultConf      bool     `json:"defaultconf"`      //目前只支持分布方式
	DistributionMode string   `json:"distributionmode"` //创建表的分布方式
	DistributionBond []string `json:"distributionbond"` //分布键

	IsRealTime     bool                 `json:"isrealtime"`     //是否是实时抽取
	PartitionField []PartitionFieldInfo `json:"partitionfield"` //分区字段 --> 记录预设字段信息
	RealTimeTB     RealTimeInfo         `json:"realtimetb"`     //实时表建表信息 --> 仅用于addtable传参
	ExternalType   string               `json:"externaltype"`
	Field          []Field              `json:"field"`
}

// BatchInputTbReq 批量导入查询内容
type BatchInputTbReq struct {
	ExtractTabsInfo []BatchExtractTabInfo `json:"extracttabsinfo"`
}

type BatchExtractTabInfo struct {
	ExtractID string   `json:"extractid"` // 抽取源id
	SchemaID  string   `json:"schemaid"`  // 模式id
	CatalogID string   `json:"catalogid"` // 表编目id
	DBName    string   `json:"dbname"`    // 抽取源数据库名称/csv文件名称
	Schema    string   `json:"schema"`    // sqlserver、stork、db2模式名称
	TableID   []string `json:"tableid"`   // 表id
	TBName    []string `json:"tablename"` // 表名称
}

// MultiInputReq 批量导入请求内容
type MultiInputReq struct {
	ExtractID string   `json:"extractid"` // 抽取源id
	DBName    string   `json:"dbname"`    // 抽取源数据库名称/csv文件名称
	Schema    string   `json:"schema"`    // sqlserver、stork、db2模式名称
	TBList    []string `json:"tblist"`    // 表名称列表
}

type MultiCheckReq struct {
	SourceID string       `json:"sourceid" validate:"required"` // 存储源id
	TBList   []InputTbReq `json:"tblist" validate:"required"`   // 批量导入抽取源表信息
}

// MultiAddReq 批量导入表内容
type MultiAddReq struct {
	LayerName  string       `json:"layername" validate:"required"` // 层名称
	LayerID    string       `json:"layerid" validate:"required"`   // 层id
	EngineID   string       `json:"engineid" validate:"required"`  // 引擎id
	DbName     string       `json:"dbname" validate:"required"`    // 数据库名称
	SourceDb   string       `json:"sourcedb" validate:"required"`  // 存储源名称
	SourceID   string       `json:"sourceid" validate:"required"`  // 存储源id
	Schema     string       `json:"schema"`                        // stork模式
	DBType     string       `json:"dbtype" validate:"required"`    // 引擎类型 hive/stork
	FileFormat string       `json:"fileformat"`                    // 文件格式 Orcfile/Textfile
	TBType     string       `json:"tbtype"`                        // 表类型 未分类/事实表/代码表,当前无此选项，默认传未分类
	IsRT       bool         `json:"isrt"`                          // 是否实时 是则true
	CatalogID  string       `json:"catalogid"`                     // 所属最底层编目id
	TagList    []TagList    `json:"taglist"`                       // 标签信息
	IsFolder   bool         `json:"isfolder"`                      // 是否为导入数据源编目
	TBList     []InputTbReq `json:"tblist" validate:"required"`    // 批量导入抽取源表信息
	ProjectID  string       `json:"projectid"`                     // 所属项目id
	UserID     string       `json:"userid"`                        // 用户id
	Username   string       `json:"username"`                      // 用户名
	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级
}

type SyncSourceParam struct {
	ID        string `json:"id"`         //元数据表ID
	ProjectID string `json:"projectid"`  //项目ID
	FrontPage int    `json:"front_page"` //前端页面传参，1表模型，2资源中心
}

// MetaListTb 表基本信息
type MetaListTb struct {
	ID       string `json:"id"`                // 元数据表id
	TBName   string `json:"tbname"`            // 表名称
	LayerID  string `json:"layerid"`           // 分层/编目id
	Chinese  string `json:"chinese,omitempty"` // 表中文名
	Describe string `json:"describe"`          // 表描述
	SourceDb string `json:"sourcedb"`          // 存储源名称/schemaname
	DBName   string `json:"dbname"`            // 库名
	Username string `json:"username"`          // 用户名
	IsDel    bool   `json:"isdel"`             // 是否可删除
	Modified string `json:"modified"`          // 修改时间
	SyncTime string `json:"synctime"`
	TBORView int    `json:"tborview"` //0：表，1：视图
	Isrt     bool   `json:"isrt"`     //是否是实时表
	Dbtype   string `json:"dbtype"`
	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级

	Catalogid   string    `json:"catalogid"` //编目id
	CatalogName string    `json:"catalogname"`
	RowsNum     int       `json:"rowsnum"`
	IsMetrics   bool      `json:"ismetrics"`
	FileFormat  string    `json:"fileformat"` // 文件格式 Orcfile/Textfile
	TagList     []TagList `json:"taglist"`    // 标签信息
}

// 元数据添加、编辑表接口（/danastudio/metadata/devcenter/addtable）的入参设计
// 新增一个请求参数用于判断 专题调用该接口时的逻辑处理
type AddTableHttpReq struct {
	ReqType string `json:"reqtype"` //调用接口的请求来源
	MetaSourceTb
}

// 同步单表请求参数
type SyncOneTbReq struct {
	ID            string `json:"id"`            // 元数据表id
	OnlyPartition bool   `json:"onlypartition"` //true仅同步partition
	IsRealMessage bool   `json:"isrealmessage"` //实时数据
}

// MetaSourceTb 元数据开发--存储源下的表结构
type MetaSourceTb struct {
	ID               string                `json:"id"`                // 元数据表id
	LayerName        string                `json:"layername"`         // 层名称
	LayerID          string                `json:"layerid"`           // 层id
	EngineID         string                `json:"engineid"`          // 引擎id
	DbName           string                `json:"dbname"`            // 数据库名称
	SourceDb         string                `json:"sourcedb"`          // 存储源名称,对hive即为数据库名称,对stork/teryx指schema
	SourceID         string                `json:"sourceid"`          // 存储源id
	Schema           string                `json:"schema"`            // stork模式
	DBType           string                `json:"dbtype"`            // 引擎类型 hive/stork
	FileFormat       string                `json:"fileformat"`        // 文件格式 Orcfile/Textfile
	TBName           string                `json:"tbname"`            // 表名称
	Chinese          string                `json:"chinese,omitempty"` // 表中文名
	Describe         string                `json:"describe"`          // 表描述
	TBType           string                `json:"tbtype"`            // 表类型 未分类/事实表/代码表
	TBORView         int                   `json:"tborview"`          //0：表，1：视图
	IsRT             bool                  `json:"isrt"`              // 是否实时 是则true
	StoreType        bool                  `json:"storetype"`         // 是否实时 是则true
	Location         string                `json:"location"`          // 表底层存储位置
	CatalogID        string                `json:"catalogid"`         // 所属最底层编目id
	CataList         []CataList            `json:"catalist"`          // 所属编目数组
	TagList          []TagList             `json:"taglist"`           // 标签信息
	FieldInfo        []MetaSourceFieldInfo `json:"fieldinfo"`         // 字段信息
	ProjectID        string                `json:"projectid"`         // 所属项目id
	UserID           string                `json:"userid"`            // 用户id
	Username         string                `json:"username"`          // 用户名
	Created          string                `json:"created"`           // 创建时间
	Modified         string                `json:"modified"`          // 修改时间
	Updated          string                `json:"updated"`
	SingleCollection bool                  `json:"singlecollection"`

	StorageSpace string `json:"storagespace,omitempty"`
	SizeByte     uint64 `json:"sizebyte,omitempty"`
	UpdateSpace  uint64 `json:"updatespace,omitempty"`
	RowsNum      int    `json:"rowsnum,omitempty"`
	UpdateNum    int    `json:"updateNum,omitempty"`
	SyncESTime   string `json:"syncestime,omitempty"` //同步中新增表的进入时间，用以区分表冗余表是否是同步引起的
	Origin       string `json:"origin,omitempty"`     //此表从从哪个模块进入es的，目前来源模块有：新建表create、采集collect、同步sync
	SyncTime     string `json:"synctime,omitempty"`   //数据源同步时间
	SyncFreq     string `json:"syncfreq,omitempty"`
	VersionNum   int    `json:"versionnum"`         // 版本号
	Taskinfo     []Task `json:"taskidfo,omitempty"` //表的对应抽取任务id
	IsInput      bool   `json:"isinput"`            // 是否导入新建表
	ExtractType  string `json:"extracttype"`        // 抽取源类型

	DistributionMode string   `json:"distributionmode"` //MPP数据库分布方式
	DistributionBond []string `json:"distributionbond"` //MPP数据库分布键

	PartitionField []PartitionFieldInfo `json:"partitionfield"` //分区字段 --> 记录预设字段信息
	RealTimeTB     RealTimeInfo         `json:"realtimetb"`     //实时表建表信息 --> 仅用于addtable传参

	PartitionInfo PartitionInfo `json:"partitioninfo"` //MG返回分区详情
	SqlTxtTemp    string        `json:"sqltxttemp"`    //临时表建表语句
	SqlTxt        string        `json:"sqltxt"`        //建表语句
	//适配超多字段表保存信息
	SqlTxtTempNew string `json:"sqltxttempnew"` //临时表建表语句
	SqlTxtNew     string `json:"sqltxtnew"`     //建表语句

	Checkresult []CheckToSourcetb `json:"checkresult_test"` // 存储源字段敏感等级设置。只需要使用RankNum 进行记录
	TableRank   int               `json:"table_rank"`       //  表的敏感等级，亮亮那边更新，资产这边获取【0--未识别，99--非敏感，1-10：敏感的具体的内容】

	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级

	//5.6.2 新增业务属性
	DataProvider     string `json:"dataprovider,omitempty"`     //宁波大学-来源部门
	DataSourceSystem string `json:"datasourcesystem,omitempty"` //宁波大学-来源系统
	DataArea         string `json:"dataarea,omitempty"`         //宁波大学-业务领域

	//DataProvider     []string `json:"dataproviderlist,omitempty"`     //宁波大学-来源部门
	//DataSourceSystem []string `json:"datasourcesystemlist,omitempty"` //宁波大学-来源系统
	//DataArea         []string `json:"dataarealist,omitempty"`         //宁波大学-业务领域

	TagTarget      bool         `json:"tagtarget"`
	ExtractTypeInt int          `json:"extracttypeint"`
	ExternalType   bool         `json:"externaltype"` //true代表多文件导入，false代表单文件导入
	ExternalPathId []string     `json:"externalpathid"`
	Filename       []string     `json:"filename"`
	Delimiter      string       `json:"delimiter"`
	FileSuffix     string       `json:"filesuffix"`
	TBList         []InputTbReq `json:"tblist" validate:"required"` // 批量导入抽取源表信息
	//LastUpdate     string       `json:"lastupdate,omitempty"`
}

type MetaSourceTbBulk struct {
	IDS       []string  `json:"ids"`       // 元数据表id
	CatalogID string    `json:"catalogid"` // 所属最底层编目id
	TagList   []TagList `json:"taglist"`   // 标签信息
	ProjectID string    `json:"projectid"` // 所属项目id
	UserID    string    `json:"userid"`    // 用户id
	Username  string    `json:"username"`  // 用户名

	//5.6.2 新增业务属性
	DataProvider     string `json:"dataprovider,omitempty"`     //宁波大学-来源部门
	DataSourceSystem string `json:"datasourcesystem,omitempty"` //宁波大学-来源系统
	DataArea         string `json:"dataarea,omitempty"`         //宁波大学-业务领域

}

type CheckToSourcetb struct {
	Field    string `json:"field"`    //字段名
	Rankid   string `json:"rankid"`   //等级id
	RankNum  int    `json:"ranknum"`  //等级数
	RankName string `json:"rankname"` //等级名称
}

// SourceTBBasic 精简版元数据表
type SourceTBBasic struct {
	ID        string                `json:"id"`                // 元数据表id
	LayerID   string                `json:"layerid"`           // 层id
	EngineID  string                `json:"engineid"`          // 引擎id
	DbName    string                `json:"dbname"`            // 数据库名称
	SourceDb  string                `json:"sourcedb"`          // 存储源名称,对hive即为数据库名称,对stork/teryx指schema
	SourceID  string                `json:"sourceid"`          // 存储源id
	Schema    string                `json:"schema"`            // stork模式
	DBType    string                `json:"dbtype"`            // 引擎类型 hive/stork
	TBName    string                `json:"tbname"`            // 表名称
	CatalogID string                `json:"catalogid"`         // 所属最底层编目id
	TagList   []TagList             `json:"taglist"`           // 标签信息
	ProjectID string                `json:"projectid"`         // 所属项目id
	UserID    string                `json:"userid"`            // 用户id
	Chinese   string                `json:"chinese,omitempty"` // 表中文名
	Describe  string                `json:"describe"`          //表描述
	CataList  []CataList            `json:"catalist"`          // 所属编目数组
	FieldInfo []MetaSourceFieldInfo `json:"fieldinfo"`         // 字段信息
	Modified  string                `json:"modified"`          // 修改时间
}

type RealTimeInfo struct {
	SourcedbTableName string `json:"sourcedbtablename"` //存储源表名
	SourcedbTableID   string `json:"sourcedbtableid"`
	SourcedbType      string `json:"sourcedbtype"` //存储源类型
}

type TopicIinfos struct {
	Name                string `json:"name"`
	Slot                string `json:"slotname"`
	DbServerName        string `json:"dbservername"`
	TableWhitelist      string `json:"tablewhitelist"`
	DbHistoryKafkaTopic string `json:"dbhistorykafkatopic"`
}

type AccTableInfo struct {
	FileFormat          string                `json:"fileformat"`          // 文件格式 Orcfile/Textfile
	FieldInfo           []MetaSourceFieldInfo `json:"fieldinfo"`           // 字段信息
	FieldData           [][]string            `json:"fielddata"`           // 前三行数据
	PartitionField      []PartitionFieldInfo  `json:"partitionfield"`      //分区字段 --> 记录预设字段信息
	PartitionInfoFields []string              `json:"partitioninfofields"` //MG返回分区字段
	//5.0 批量需求新增
	Describe         string     `json:"describe"`         // 表描述
	CatalogID        string     `json:"catalogid"`        // 所属最底层编目id
	CataList         []CataList `json:"catalist"`         // 所属编目数组
	TagList          []TagList  `json:"taglist"`          // 标签信息
	DistributionMode string     `json:"distributionmode"` //MPP数据库分布方式
	DistributionBond []string   `json:"distributionbond"` //MPP数据库分布键
	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级
}

// Task 任务
type Task struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// TbStatusReq 表详情结构体
type TbStatusReq struct {
	ID           string `json:"id"`     // 表id/表版本id
	Status       int    `json:"status"` // 表状态,1:当前表,2:历史版本表
	NeedStandard bool   `json:"needstandard"`
}

// TbVersionList 表版本列表信息
type TbVersionList struct {
	ID          string `json:"id"`                // 表版本id
	TBName      string `json:"tbname"`            // 表名称
	Chinese     string `json:"chinese,omitempty"` // 表中文名
	Describe    string `json:"describe"`          // 表描述
	VersionName string `json:"versionname"`       // 版本号名称
	Username    string `json:"username"`          // 创建人
	UserID      string `json:"userid"`
	NickName    string `json:"nickname"` // 昵称
	Created     string `json:"created"`  // 创建时间
}

// MetaSourceTbVersion 元数据开发--存储源下的表版本管理
type MetaSourceTbVersion struct {
	ID          string       `json:"id"`          // 表版本id
	TBID        string       `json:"tbid"`        // 表id
	VersionNum  int          `json:"versionnum"`  // 版本号
	VersionName string       `json:"versionname"` // 版本号名称
	HistoryData MetaSourceTb `json:"historydata"` // 历史版本表数据
	UserID      string       `json:"userid"`      // 用户id
	Username    string       `json:"username"`    // 用户名
	Created     string       `json:"created"`     // 创建时间
	Modified    string       `json:"modified"`    // 修改时间
	Engineid    string       `json:"engineid"`
}

// CataList 表所属编目信息
type CataList struct {
	ID         string `json:"id"`         // 所属各层编目id
	Name       string `json:"name"`       // 所属各层目录名称
	ParentID   string `json:"parentid"`   // 上一级编目id
	ParentName string `json:"parentname"` // 上一级编目名称
	IsFourth   bool   `json:"isfourth"`   // 是否是第四级编目
	IsLower    bool   `json:"islower"`    // 是否最底层编目
}

// TagList 表标签基本信息
type TagList struct {
	DirID string `json:"dirid"`                  // 文件夹id
	ID    string `json:"id" validate:"required"` // 标签id
	Name  string `json:"name"`                   // 标签名称
	Color string `json:"color"`                  // 标签颜色
}

// MetaSourceFieldInfo 存储源下的表的字段结构
type MetaSourceFieldInfo struct {
	Number        int    `json:"number"`                  // 序号
	Name          string `json:"name"`                    // 字段名称
	Annotation    string `json:"annotation"`              // 注释
	FieldType     string `json:"fieldtype"`               // 字段类型
	IsPrim        bool   `json:"isprim"`                  // 是否主键 是则true
	FormReferName string `json:"formrefername,omitempty"` // 引用标准名称
	FormReferID   string `json:"formreferid,omitempty"`   // 引用标准id
	IsUnique      bool   `json:"isunique"`                // 是否唯一键
	IsPartition   bool   `json:"ispartition"`             //是否分区键

	RankNum  int    `json:"ranknum"`  //等级数---字段的等级。
	RankName string `json:"rankname"` //等级名称---敏感等级

	InfoItemsType string `json:"infoitemstype"` //前端需要添加，判断字段类型中文

	SecondaryClassify string `json:"secondaryclassify"` //宁波大学二级分类
	SecurityLevel     string `json:"securitylevel"`     //宁波大学安全等级

	Standard       StandardConnect `json:"standardinfo,omitempty"`
	CustomStandard CustomStandard  `json:"customstandardinfo,omitempty"`
}
type CustomStandard struct {
	Standards  []CIDName `json:"standards,omitempty"` // 自定义标准
	Codetables []IDName  `json:"codetables,omitempty"`
}

type IDName struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type CIDName struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	Codetbid   string `json:"codetbid"` // 编码表id
	Codetbname string `json:"codetbname"`
}

type StandardConnect struct {
	Standards  []IDName `json:"standards"`
	Codetables []IDName `json:"codetables"`
}

// MetaSourceFieldInfo 存储源下的表的字段结构
type MetaSourceFieldInfoSimple struct {
	Name        string `json:"name"`        // 字段名称
	FieldType   string `json:"fieldtype"`   // 字段类型
	IsPrim      bool   `json:"isprim"`      // 是否主键 是则true
	IsUnique    bool   `json:"isunique"`    // 是否唯一键
	IsPartition bool   `json:"ispartition"` //是否分区键
	Annotation  string `json:"annotation"`  // 注释
}

// PartitionFieldInfo 分区字段信息
type PartitionFieldInfo struct {
	Name          string        `json:"name"`          //字段名称
	Annotation    string        `json:"annotation"`    // 注释
	FieldType     string        `json:"fieldtype"`     // 字段类型
	Ispreset      string        `json:"ispreset"`      //预设字段类型 "year"年 "month"月 "day"日 "notpreset"非预设字段
	CustomizeRule CustomizeRule `json:"customizerule"` //自定义规则
}
type CustomizeRule struct {
	ExtractFieldName string `json:"fieldname"` //来源字段
	FormatSql        string `json:"formatsql"` //格式转换sql

}

// sqlsearch返回
type SqlSearchFieldInfo struct {
	FieldPrim      []bool   `json:"fieldprim"`
	FieldUnique    []bool   `json:"fieldunique"`
	FieldPartition []bool   `json:"fieldpartition"`
	FieldRankNum   []int    `json:"fieldranknum"`
	FieldRankName  []string `json:"fieldrankname"`
	FieldType      []string `json:"fieldtype"`
}

// MetaCatalog 元数据开发--编目信息
type MetaCatalog struct {
	ID         string `json:"id"`         // 编目id
	ParentID   string `json:"parentid"`   // 上一级编目id
	ParentName string `json:"parentname"` // 上一级编目名称
	Name       string `json:"name"`       // 编目名称
	Tbcount    int    `json:"tbcount"`    // 编目表数量
	Order      string `json:"order"`      // 编目编号
	Number     int    `json:"number"`     // 编目实际编号,用于确定相对位置
	Isfourth   bool   `json:"isfourth"`   // 是否是第四级编目
	IsLower    bool   `json:"islower"`    // 标识编目相对位置,后端使用
	ProjectID  string `json:"projectid"`  // 所属项目标识
	Created    string `json:"created"`    // 创建时间
	Modified   string `json:"modified"`   // 修改时间
}

// MetaDir 文件夹信息结构体
/*
DirType:标识文件夹类型，目前有编目、标签、标准三种，对应值:catalog、tagdir、standarddir
Count:即分别指编目下表数量、标签文件夹下标签数量、标准文件夹下标准个数
Order:仅用于编目的特殊编号
*/
type MetaDir struct {
	ID         string   `json:"id"`                 // 文件夹id
	Name       string   `json:"name"`               // 文件夹名称
	DirType    string   `json:"dirtype"`            // 文件夹类型
	ParentID   string   `json:"parentid"`           // 上一级文件夹id
	ParentName string   `json:"parentname"`         // 上一级文件夹名称
	Number     int      `json:"number"`             // 文件夹编号
	Order      string   `json:"order,omitempty"`    // 编目编号
	Count      int      `json:"count"`              // 文件夹包含文件个数
	Isfourth   bool     `json:"isfourth"`           // 是否是第四级文件夹
	IsLower    bool     `json:"islower"`            // 是否最底层文件夹
	ProjectID  string   `json:"projectid"`          // 所属项目标识
	Created    string   `json:"created"`            // 创建时间
	Modified   string   `json:"modified"`           // 修改时间
	Tables     []string `json:"tables"`             // 其下所有表
	Sorttype   int      `json:"sorttype,omitempty"` // 排序字段：1--name ，2 -
}

// MetaTag 标准管理--标签信息
type MetaTag struct {
	ID        string   `json:"id"`        // 标签id
	DirID     string   `json:"dirid"`     // 标签文件夹id
	DirName   string   `json:"dirname"`   // 标签文件夹名称
	TopID     string   `json:"topid"`     // 标签最上层文件夹id
	Name      string   `json:"name"`      // 标签名称
	Color     string   `json:"color"`     // 标签颜色
	Number    int      `json:"number"`    // 标签编号
	Property  string   `json:"property"`  // 标签属性/标签编目
	Citations int      `json:"citations"` // 引用次数
	ProjectID string   `json:"projectid"` // 项目标识
	Created   string   `json:"created"`   // 创建时间
	Modified  string   `json:"modified"`  // 修改时间
	Tables    []string `json:"tables"`    // 其下所有表
	TablesNum int      `json:"tablesnum"` // 其下所有表数量
}

// MetaStandard 标准管理--标准信息
type MetaStandard struct {
	ID        string    `json:"id"`   // 标准id
	Name      string    `json:"name"` // 标准名称
	NickName  string    `json:"nickname"`
	UserID    string    `json:"userid"`
	UserName  string    `json:"username"`
	ShortName string    `json:"shortname"` // 短名(字段名) 英文名
	TagList   []TagList `json:"taglist"`   // 标签信息
	//Status     int          `json:"status"`     // 状态,1:未审核,2:已审核
	DirID      string       `json:"dirid"`      // 所属文件夹id
	DirName    string       `json:"dirname"`    // 所属文件夹名称
	Basis      string       `json:"basis"`      // 标准依据
	Order      string       `json:"order"`      // 标准编号
	Definition string       `json:"definition"` // 标准定义 描述
	Mode       int          `json:"mode"`       // 规范形式,1:无,2:文本,3:代码表
	TextMode   TextMode     `json:"textmode"`   // 规范形式--文本
	CodeTbMode []CodeTbMode `json:"codetbmode"` // 规范形式--代码表 5.9.0舍弃
	ProjectID  string       `json:"projectid"`  // 所属项目标识
	Checked    string       `json:"checked"`    // 审核时间
	Created    string       `json:"created"`    // 创建时间
	Modified   string       `json:"modified"`   // 修改时间
	//5.9.0
	EffectiveInfo EffectiveInfo `json:"effectiveinfo"`
	CodeTbID      string        `json:"codetbid"` //代码表id
	CodeTbName    string        `json:"codetbname"`
	Status        int           `json:"status"`     // 见common.go --> codeStatus
	CiteNumber    int           `json:"citenumber"` //引用次数
	//审核信息
	AuditStatus  int    `json:"auditstatus"`  //最终审核状态  1:已通过，2：已驳回，3：审核中  0：未开始审核 4：已撤回 同审核工单状态
	AuditOpinion string `json:"auditopinion"` //审核意见
	RecordID     string `json:"recordid"`     //当前工单ID
}

// TextMode 规范形式--文本
type TextMode struct {
	Classify   int      `json:"classify"`   // 文本规则分类,1:基础规则,2:自定义规则
	FormType   []string `json:"formtype"`   // 基础规则-格式类型,包括字母、数字、中文、特殊字符
	ContLength []string `json:"contlength"` // 基础规则-内容长度,数组长度为2,分别为最小长度、最大长度
	NumRange   []string `json:"numrange"`   // 基础规则-数值范围,仅单独勾选数字时使用.数组长度为2,分别为最小值、最大值
	RegExp     string   `json:"regexp"`     // 自定义规则-正则表达式
}

// CodeTbMode 规范形式--代码表
type CodeTbMode struct {
	Name    string `json:"name"`    // 代码名称
	Value   string `json:"value"`   // 代码值
	Comment string `json:"comment"` // 备注说明
}

// 5.9.0代码表
type CodeTbDTO struct {
	ID            string        `json:"id"`
	Name          string        `json:"name"`          //码表名称 唯一
	EnCode        string        `json:"encode"`        //编码 唯一
	Desc          string        `json:"desc"`          //描述
	DirID         string        `json:"dirid"`         // 文件夹id
	DirName       string        `json:"dirname"`       // 文件夹名称
	Tags          []TagList     `json:"tags"`          // 标签列表
	EffectiveInfo EffectiveInfo `json:"effectiveinfo"` //生效时间管理

	CodeTbMode []CodeTbMode `json:"codetbmode"` // 规范形式--代码表

	AnyInfoCommon

	Status int `json:"status"` // 见common.go --> codeStatus

	//审核信息
	AuditStatus  int    `json:"auditstatus"`  //最终审核状态  1:已通过，2：已驳回，3：审核中  0：未开始审核 4：已撤回 同审核工单状态
	AuditOpinion string `json:"auditopinion"` //审核意见
	RecordID     string `json:"recordid"`     //当前工单ID
}

type CodeTbHis struct {
	CodeTbDTO            //这个里面的id 为历史版本id
	Version       string `json:"version"`  //版本
	CodeTbID      string `json:"codetbid"` //代码表id
	HisCreateTime string `json:"hiscreatetime"`
}

type StandardTbHis struct {
	MetaStandard         //这个里面的id 为历史版本id
	Version       string `json:"version"`      //版本
	StandardTbID  string `json:"standardtbid"` //标准id
	HisCreateTime string `json:"hiscreatetime"`
}

type CodeTbVO struct {
	ID      string    `json:"id"`
	Name    string    `json:"name"`    //码表名称 唯一
	EnCode  string    `json:"encode"`  //编码 唯一
	Desc    string    `json:"desc"`    //描述
	DirID   string    `json:"dirid"`   // 文件夹id
	DirName string    `json:"dirname"` // 文件夹名称
	Tags    []TagList `json:"tags"`    // 标签列表
	AnyInfoCommon
	EffectiveInfo EffectiveInfo `json:"effectiveinfo"`
	Status        int           `json:"status"`       // 见common.go --> codeStatus
	RecordID      string        `json:"recordid"`     //当前工单ID
	IsReferenced  bool          `json:"isreferenced"` //是否引用
	AuditStatus   int           `json:"auditstatus"`  //最终审核状态  1:已通过，2：已驳回，3：审核中  0：未开始审核 4：已撤回 同审核工单状态
}

// ParseResult 包含解析结果
type XLSXCodeParseResult struct {
	FileName        string         `json:"filename"`
	CodeNames       map[int]string `json:"validsheets"`     // sheet索引--> sheet名称
	SheetNames      []string       `json:"sheetnames"`      // sheet名称 也是代码表名称
	Encodes         []string       `json:"encodes"`         // 编码列表
	CodeDefinitions [][]CodeTbMode `json:"codedefinitions"` // 代码表
}

type CodeTBInfo struct {
	Desc            string       `json:"desc"`            //描述
	Name            string       `json:"name"`            //名称
	Encode          string       `json:"encode"`          //编码
	CodeDefinitions []CodeTbMode `json:"codedefinitions"` // 代码表
	SameNameMethod  int          `json:"samenamemethod"`  //1覆盖2更新3追加4不变
}

type BulkCodeTB struct {
	DirID         string        `json:"dirid"`         // 文件夹id
	DirName       string        `json:"dirname"`       // 文件夹名称
	Tags          []TagList     `json:"tags"`          // 标签列表
	EffectiveInfo EffectiveInfo `json:"effectiveinfo"` //生效时间管理
	CodeTBInfos   []CodeTBInfo  `json:"codetbinfos"`   //导入信息
	AnyInfoCommon
}

type XLSXStandardParseResult struct {
	FileName            string           `json:"filename"`
	SheetNames          []string         `json:"sheetnames"`          // sheet名称 也是标准名称
	StandardDefinitions [][]StandardInfo `json:"standarddefinitions"` // 标准
}

type StandardInfo struct {
	Name      string `json:"name"`      // 标准名称
	ShortName string `json:"shortname"` // 短名(字段名) 英文名
	Basis     string `json:"basis"`     // 标准依据
	Order     string `json:"order"`     // 标准编号
	//Definition string       `json:"definition"` // 标准定义 描述
	Mode     int    `json:"mode"`     // 规范形式,1:无,2:文本,3:代码表
	ModeStr  string `json:"modestr"`  // 规范形式,无,文本,代码表
	RuleCode string `json:"rulecode"` // 规范形式/代码表名称
}
type BulkStandardTB struct {
	DirID   string `json:"dirid"`   // 文件夹id
	DirName string `json:"dirname"` // 文件夹名称
	//Tags          []TagList     `json:"tags"`          // 标签列表
	EffectiveInfo  EffectiveInfo    `json:"effectiveinfo"`   //生效时间管理
	StandardTBInfo []StandardTBInfo `json:"standardtbinfo""` //导入信息
	AnyInfoCommon
}
type StandardTBInfo struct {
	StandardInfo
	SameNameMethod int `json:"samenamemethod"` // 1更新2不更新

}

type CommonReq struct {
	ID        string   `json:"id"`
	IDs       []string `json:"ids"`
	Name      string   `json:"name"`
	Names     []string `json:"names"`
	ProjectID string   `json:"projectid"`
	UserID    string   `json:"userid"`
	Page      int      `json:"page"`      //第几页，从1开始
	Perpage   int      `json:"perpage"`   //每页的个数
	Match     string   `json:"match"`     //模糊查找内容
	Sort      string   `json:"sort"`      //排序 asc desc
	SortField string   `json:"sortfield"` //排序字段
	Status    []int    `json:"status"`    //状态筛选
	BzType    int      `json:"bztype"`    //1标准 2代码表
	DirID     string   `json:"dirid"`
	IsChange  bool     `json:"ischange"` //查询变更表信息
}

type AnyInfoCommon struct {
	CreateTime string `json:"createtime"`
	UpdateTime string `json:"updatetime"`
	UserID     string `json:"userid"`
	UserName   string `json:"username"`
	NickName   string `json:"nickname"`
	ProjectID  string `json:"projectid"`
}

type EffectiveInfo struct {
	PublicTakeEffect   bool   `json:"publictakeeffect"`   // 发布即生效
	EffectiveBeginTime string `json:"effectivebegintime"` // 生效开始时间
	EffectiveEndTime   string `json:"effectiveendtime"`   // 生效结束时间
	ValidForever       bool   `json:"validforever"`       //永久有效
}

type FlowWatchV2 struct {
	Title      string           `json:"title"`
	Date       string           `json:"date"`
	Describe   []FlowDesc       `json:"describe,omitempty"`
	Status     int              `json:"status"` //-1 时没有状态，0：待审核，1：已通过，2：已驳回，3：审核中，4：已撤回，5：未开始的审核
	AuditChart []AuditChartInfo `json:"auditchart,omitempty"`
}

type AuditChartInfo struct {
	Index       int           `json:"index"`       //审核层级顺序
	IndexDesc   string        `json:"indexdesc"`   //审核层级名称
	IndexStatus int           `json:"indexstatus"` //每层的审核状态
	Date        string        `json:"date"`        //最新审核时间
	CheckInfo   []CheckDetail `json:"checkinfo"`   //每层审核人的审核详情
}

type CheckDetail struct {
	SubStatus      int    `json:"substatus"` //每层的审核状态 0未审核，1，已通过，2已驳回
	CheckTime      string `json:"checktime"` //每层的审核时间
	Desc           string `json:"desc"`      //审核描述
	Checker        string `json:"checker"`   //审核人名称
	CheckerId      string `json:"checkerid"` //审核人ID
	Index          int    `json:"index"`     //审核层级顺序
	IndexDesc      string `json:"indexdesc"` //审核层级名称
	NickName       string `json:"nickname"`
	UserDepartment string `json:"userdepartment"`
}

type FlowDesc struct {
	Key            string `json:"key"`
	Value          string `json:"value"`
	NickName       string `json:"nickname"`
	UserDepartment string `json:"userdepartment"`
}

type AuditRecord struct {
	ID             string        `json:"id,omitempty"`         //审核工单ID
	ObjectId       string        `json:"objectid,omitempty"`   //审核对象ID
	ObjectName     string        `json:"objectname,omitempty"` //审核对象---名称
	ObjectFw       string        `json:"objectfw,omitempty"`   //资产上架 对应的下一层服务名称
	ObjectFwID     string        `json:"objectfwid,omitempty"` //资产上架 对应的下一层服务ID
	AssetID        string        `json:"assetid,omitempty"`    //增加一个资产ID，没有则为空
	Type           int           `json:"type,omitempty"`       //审核类型 ：参考common.go文件中AuditTypeMap
	SourceType     int           `json:"sourcetype,omitempty"` //申请来源类型： 参考common.go文件中AuditSourceMap
	Status         int           `json:"status"`               //最终审核状态  1:已通过，2：已驳回，3：审核中  0：未开始审核 4：已撤回
	AutoCheck      bool          `json:"autocheck"`            //是否为自动审核，是为true，否为false
	Detail         []CheckDetail `json:"detail,omitempty"`
	CreateTime     string        `json:"createtime,omitempty"` //创建时间
	UpdateTime     string        `json:"updatetime,omitempty"` //更新时间
	UserID         string        `json:"userid,omitempty"`     //触发审核流程的申请人ID
	UserName       string        `json:"username,omitempty"`   //提交审核 申请人
	ProjectID      string        `json:"projectid,omitempty"`  //所属项目ID
	RecallUser     string        `json:"recalluser"`           //撤回用户
	CurrentDeep    int           `json:"currentdeep"`          //当前审核层级
	CurrentChecker []string      `json:"currentchecker"`       //当前审核人
	CurrentCheckId []string      `json:"currentcheckerids"`    //当前审核人ID，一直存储当前审核人
	CurrentStatus  int           `json:"currentstatus"`        //当前层级审核状态
	TransUserID    string        `json:"transuserid"`          //移交用户ID   可存多个值，用逗号分开
	TransUserName  string        `json:"transusername"`        //移交用户名称
	TransGroupIds  []string      `json:"transgroupids"`        //移交用户组列表
}

//
//type CheckDetail struct {
//	SubStatus      int    `json:"substatus"` //每层的审核状态 0未审核，1，已通过，2已驳回
//	CheckTime      string `json:"checktime"` //每层的审核时间
//	Desc           string `json:"desc"`      //审核描述
//	Checker        string `json:"checker"`   //审核人名称
//	CheckerId      string `json:"checkerid"` //审核人ID
//	Index          int    `json:"index"`     //审核层级顺序
//	IndexDesc      string `json:"indexdesc"` //审核层级名称
//	NickName       string `json:"nickname"`
//	UserDepartment string `json:"userdepartment"`
//}

// StandardList 标准列表信息
type StandardList struct {
	ID         string    `json:"id"`         // 标准id
	DirID      string    `json:"dirid"`      // 标准文件夹id
	Name       string    `json:"name"`       // 标准名称
	ShortName  string    `json:"shortname"`  // 短名(字段名)
	TagList    []TagList `json:"taglist"`    // 标签信息
	Status     int       `json:"status"`     // 状态,1:未审核,2:已审核
	RecordID   string    `json:"recordid"`   //当前工单ID
	CiteNumber int       `json:"citenumber"` //引用次数
	UserName   string    `json:"username"`
	NickName   string    `json:"nickname"`
	Definition string    `json:"definition"` // 标准定义 描述
	Modified   string    `json:"modified"`   // 修改时间
	IsChange   bool      `json:"ischange"`   // 查询是否变更
}

// StdOrderList 标准编号校验结构体
type StdOrderList struct {
	ID        string `json:"id"`        // 标准id
	DirID     string `json:"dirid"`     // 标准所属文件夹id
	ShortName string `json:"shortname"` // 短名(字段名)
	Order     string `json:"order"`     // 标准编号
}

// MetaTagReq 标签信息请求
type MetaTagReq struct {
	SearchContent []SearchContent `json:"searchcontent"`      // 搜索数组
	SortContent   SortContent     `json:"sortcontent"`        // 排序方式
	Page          int             `json:"page"`               // 页数
	Rows          int             `json:"rows"`               // 每页数据条数
	ProjectID     string          `json:"projectid"`          // 所属项目名称
	TagType       string          `json:"tagtype"`            // 标签文件夹类型,现有:"表", "标准", "任务", "脚本"
	ShowDB        bool            `json:"showdb"`             // 数据权限不过滤 sourcedb
	Sorttype      int             `json:"sorttype,omitempty"` // 排序字段：1--name ，2 -
	Mode          int             `json:"mode"`               // 查询标准时根据模式过滤
	SourceCnt     bool            `json:"sourcecnt"`          // 检测存储源是否为空
	Isrealmessage bool            `json:"isrealmessage"`
	Status        []int           `json:"status"`
	DirID         string          `json:"dirid"`
	Match         string          `json:"match"` //模糊查找内容
}

type TagsChangeBatch struct {
	ChangeType     string   `json:"changetype"`     //"update" or "delete"
	CatalogORTable string   `json:"catalogortable"` //"catalog" or "table"
	IDs            []string `json:"ids"`            // catalogid or tableid
	TagIDs         []string `json:"tagids"`         // tagid
	ProjectID      string   `json:"projectid"`
}

type MetaTagReqNew struct {
	ProjectID string     `json:"projectid"`         // 所属项目名称
	ReqType   int        `json:"reqtype,omitempty"` //1：排序，2筛选 3:筛选+排序
	Sort      Sort       `json:"sort,omitempty"`    //排序
	FilterDS  []FilterDS `json:"filterds"`          //筛选
	Page      int        `json:"page"`              //第几页，从1开始
	Perpage   int        `json:"perpage"`           //每页的个数
	//--------资产中心新增
	Tagids       []string `json:"tagids"`
	Catalogdirid string   `json:"catalogdirid"`
	SourceId     string   `json:"sourceid"`
	Isrt         bool     `json:"isrt"`
	FiterSets    struct {
		Fields []string `json:"fields"` //模糊查询的字段
		Match  string   `json:"match"`  //模糊查找内容
	} `json:"filterset"`
	Needblood bool `json:"needblood"`
	//是否展示视图
	IsOnlyShowTB bool `json:"isonlyshowtb"` //是否只展示表
	//新增tbid,查询结果中需要追加tableid对应的表信息
	TableID         string        `json:"tableid"`       //如果入参中有tableid,则查询结果中需要追加该条数据，前端要求
	IgnoreOp        bool          `json:"ignoreop"`      //忽略矫正标签信息,如果为true则忽略，默认不忽略
	RangeFilter     []RangeFilter `json:"rangefilterds"` //范围筛选
	MiddleTableType int           `json:"middletabletype"`
}

// MoveTagReq 移动标签请求体
type MoveTagReq struct {
	TagID   string `json:"tagid"`   // 标签id
	TagName string `json:"tagname"` // 标签名称
	DirID   string `json:"dirid"`   // 文件夹id
	DirName string `json:"dirname"` // 文件夹名称
}

// SearchContent 搜索数组
type SearchContent struct {
	Name  string `json:"name"`  // 搜索名称,支持标签名、标签文件夹id
	Value string `json:"value"` // 搜索的值
}

// SortContent 搜索数组
type SortContent struct {
	Name   string `json:"name"`   // 排序名称,包括标签名、标签属性/标签编目、引用次数、新建时间
	Number int    `json:"number"` // 倒序:1,正序:2
}

// MetaLayerInfo 存储源、编目、标签信息
type MetaLayerInfo struct {
	LayerList   []LayerList     `json:"layerlist"`   // 存储源信息
	CatalogList []CatalogList   `json:"cataloglist"` // 编目信息
	TagList     []TagSourceList `json:"taglist"`     // 标签信息
}

// LayerList 存储源信息
type LayerList struct {
	LayerID    string         `json:"layerid"`    // 层id
	LayerName  string         `json:"layername"`  // 层名称
	EngineID   string         `json:"engineid"`   // 引擎id
	EngineName string         `json:"enginename"` // 引擎名称,依数据库类型定义,如:hive、stork、dodox、datax
	IsMetrics  bool           `json:"ismetrics"`
	IsGather   bool           `json:"isgather"`              // 是否可用于数据采集,是则true
	IsGovern   bool           `json:"isgovern"`              // 是否可用于数据治理,是则true
	IsBazaar   bool           `json:"isbazaar"`              // 是否可用于数据集市,是则true //6.0 去除专题模块，此参数弃用
	SourceDb   []SourcedbList `json:"sourcedb"`              // 存储源列表
	NameEngine string         `json:"name_engine,omitempty"` // admin 引擎注册存的name  udf那边展示使用。对不起用了这个名字
}

// CatalogList 编目信息
type CatalogList struct {
	ID         string `json:"id"`         // 编目id
	ParentID   string `json:"parentid"`   // 上一级编目id
	ParentName string `json:"parentname"` // 上一级编目名称
	Name       string `json:"name"`       // 编目名称
	IsFourth   bool   `json:"isfourth"`   // 是否第四级编目
	IsLower    bool   `json:"islower"`    // 是否最底层编目
}

// SourcedbList 存储源信息
type SourcedbList struct {
	ID          string   `json:"id"`          // 存储源id
	DbName      string   `json:"dbname"`      // 数据库名称
	SourceName  string   `json:"sourcename"`  // 存储源名称
	SourceType  string   `json:"sourcetype"`  // 存储源类型 hive/stork
	SchemaNames []string `json:"schemanames"` // stork获取模式列表
	Count       int      `json:"count"`       //表数量
}

// TagSourceList 标签信息
type TagSourceList struct {
	ID         string    `json:"id"`         // 标签文件夹id
	ParentID   string    `json:"parentid"`   // 上一级标签文件夹id
	ParentName string    `json:"parentname"` // 上一级标签文件夹名称
	Name       string    `json:"name"`       // 标签文件夹名称
	Isfourth   bool      `json:"isfourth"`   // 是否是第四级标签文件夹
	IsLower    bool      `json:"islower"`    // 是否最底层文件夹
	Tag        []TagList `json:"tag"`        // 标签列表
}

// CheckStandard 操作标准信息
type CheckStandard struct {
	Status int      `json:"status"`                  // 操作方式,1:撤回标准，2：审核标准,3:删除标准
	IDs    []string `json:"ids" validate:"required"` // 待审核/撤回/删除 标准id/标签id列表
	Names  []string `json:"names"`                   // 标签/标准名称列表
}

// MetaExtractDb 抽取源关键信息
type MetaExtractDb struct {
	ID         string          `json:"id"`         // 抽取源id
	FolderID   string          `json:"folderid"`   // 所属文件夹id
	DBName     string          `json:"dbname"`     // 抽取源名称
	DBType     string          `json:"dbtype"`     // 抽取源类型
	HaveSchema bool            `json:"haveschema"` // 是否有模式,true则有
	Schemas    []ExtractSchema `json:"schemas"`    // 有模式的抽取源表列表
	TBList     []ExtractTb     `json:"tblist"`     // 无模式的抽取源表列表
}

// ExtractSchema 抽取源模式信息
type ExtractSchema struct {
	Schema string      `json:"schema"` // 表所属模式
	TBList []ExtractTb `json:"tblist"` // 表列表
}

// ExtractTb 抽取源表信息
type ExtractTb struct {
	TableName string `json:"tablename"` // 抽取源表名
	TableID   string `json:"tableid"`   // 抽取有表id
	ExtractID string `json:"extractid"` // 抽取源id
	SchemaID  string `json:"schemaid"`  // 模式
}

// InputTbField 单表导入--表名称、中文名、字段等信息
type InputTbField struct {
	TBName            string                `json:"tbname"`            // 表名称
	TBID              string                `json:"tbid"`              //表ID
	Chinese           string                `json:"chinese,omitempty"` // 表中文名
	Describe          string                `json:"describe"`          // 表描述
	OriginalFieldInfo []MetaSourceFieldInfo `json:"originalfieldinfo"` // 原始字段信息
	StorkFieldInfo    []MetaSourceFieldInfo `json:"storkfieldinfo"`    // stork字段信息
	HiveFieldInfo     []MetaSourceFieldInfo `json:"hivefieldinfo"`     // hive字段信息
	DMFieldInfo       []MetaSourceFieldInfo `json:"dmfieldinfo"`       // 达梦字段信息
	UxDBFieldInfo     []MetaSourceFieldInfo `json:"uxdbfieldinfo"`     // uxdb字段信息
	DRealDBFieldInfo  []MetaSourceFieldInfo `json:"drealdbfieldinfo"`  // drealdb字段信息

	DistributionMode string   `json:"distributionmode"` //MPP数据库分布方式
	DistributionBond []string `json:"distributionbond"` //MPP数据库分布键

	RecDistributionMode string   `json:"recdistributionmode"` //teryx、guassdb、DM推荐分布方式
	RecDistributionBond []string `json:"recdistributionbond"` //推荐分布键
	UxDistributionMode  string   `json:"uxdistributionmode"`  //ux推荐分布方式
	UxDistributionBond  []string `json:"uxdistributionbond"`  //推荐分布键
	DmDistributionMode  string   `json:"dmdistributionmode"`  //dm推荐分布方式
	DmDistributionBond  []string `json:"dmdistributionbond"`  //分布键
}

// 批量导入--表信息
type BetchInputTbField struct {
	DBName             string         `json:"dbname"`
	SchemaName         string         `json:"schemaname"`
	BetchInputTbsField []InputTbField `json:"betchinputtbsfield"`
}

// DelTbList 删除表合法性结构体
type DelTbList struct {
	DelIDs           []string      `json:"delids"`           // 可删除表id列表
	TaskedIDs        []string      `json:"taskedids"`        // 被任务依赖不可删除表id列表
	NoneEmptyIds     []string      `json:"noneemptyids"`     // 被任务依赖不可删除表id列表
	Dependids        []string      `json:"dependids"`        // 被任务依赖不可删除表id列表
	ServiceDependids []string      `json:"servicedependids"` // 被任务依赖不可删除表id列表
	DepTasks         []DepTaskInfo `json:"deptasks"`         // 依赖的任务信息
}

type DepTaskInfo struct {
	Name     string    `json:"name"`     //任务名称
	TaskType string    `json:"tasktype"` //任务类型
	Status   string    `json:"status"`   //任务状态
	Tags     []TagList `json:"taglist"`  //任务标签
}

// ExtractRenameTable 批量抽取--重名表
type ExtractRenameTable struct {
	TaskID     string   `json:"taskid"`     // 任务id
	ExtractID  string   `json:"extractid"`  // 抽取源id
	SourceID   string   `json:"sourceid"`   // 存储源id
	TBList     []string `json:"tblist"`     // 待批量抽取的表集合
	Prefix     string   `json:"prefix"`     //前缀
	Suffix     string   `json:"suffix"`     //后缀
	Policytype int64    `json:"policytype"` //策略规则(0.使用源表名 1.表存在则加前缀或后缀 2.所有表名加前缀或后缀)

}

type SyncFail struct {
	LayerID    string `json:"layerid"`
	LayerName  string `json:"layername"`
	SourceID   string `json:"sourceid"`
	SourceName string `json:"sourcename"`
	Reasion    string `json:"reasion"`
	ExecTime   string `json:"exectime"`
}
type SyncSuccess struct {
	LayerID    string `json:"layerid"`
	LayerName  string `json:"layername"`
	SourceID   string `json:"sourceid"`
	SourceName string `json:"sourcename"`
	ExecTime   string `json:"exectime"`
}

type SyncResult struct {
	SyncFails      []SyncFail    `json:"syncfails"`
	SyncSuccesses  []SyncSuccess `json:"syncsuccesses"`
	SingleTbResult bool          `json:"singlesync"`
}

type ExecuteSqlInfo struct {
	EngineID     string `json:"engineid"`     // 引擎ID
	DatabaseName string `json:"databasename"` // 数据库名
	Sql          string `json:"sql"`          // 执行sql
}

type UDFInfo struct {
	FuncName  string //函数名
	CreateSql string //创建函数 sql
}

type MoneySaveInfo struct {
	InventoryID   string     `json:"inventoryid"`   //资产编目id
	InventoryName string     `json:"inventoryname"` //资产名称
	AttachInfo    AttachInfo `json:"attachinfo"`    //挂载信息
	//AssetInfo     []AssetInfo `json:"assetinfo"`     //资产项信息
	//PutInfo       PutInfo     `json:"putinfo"`       //上架信息
	//--------下面的参数用与接口交互
	AttachType  string `json:"attachtype"` //挂载行为
	PutType     string `json:"puttype"`    //发布行为: 上架/下架/撤回/授权变更/服务上架/服务下架
	PutServerId string `json:"putserverid"`
	//Fieldinfo
}

type AttachInfo struct {
	AttachStatus    string          `json:"attachstatus"`    //挂载状态 待挂载/审核中/已驳回/已挂载/取消挂载审核中
	AttachType      string          `json:"attachtype"`      //挂载类型,目前为表 table
	AttachTableInfo AttachTableInfo `json:"attachtableinfo"` //挂载的资源表信息
	AttachTime      string          `json:"attachtime"`      //挂载时间
}

type AttachTableInfo struct {
	Id        string `json:"id"`        //表的id
	Catalogid string `json:"catalogid"` //目录id
}

// eagles解析结构体-1层
type QualityHits struct {
	Hits1 QualityHits2 `json:"hits"`
}

// eagles解析结构体-2层
type QualityHits2 struct {
	Hits2 []QualityHits3 `json:"hits"`
	Total int            `json:"total"`
}

// eagles解析结构体-3层
type QualityHits3 struct {
	QualityTableInfo QualityTableInfo `json:"_source"`
}
type QualityTableInfo struct {
	Id                   string    `json:"id"`
	Tbname               string    `json:"tbname"`             //表名--原表--接入表
	ProblemTable         string    `json:"problemTable"`       //问题表名称
	Describe             string    `json:"describe"`           //中文表名-描述
	VerifyFieldNumber    int       `json:"verifyfieldnumber"`  //核验字段数
	VerifyRulerNumber    int       `json:"verifyrulernumber"`  //核验字段数
	VerifyRecord         int       `json:"verifyrecord"`       //核验记录数
	ProblemRecord        int       `json:"problemrecord"`      //问题记录数
	ProblemQualifyRate   float64   `json:"problemqualifyrate"` //问题合格率
	RepetitionRate       float64   `json:"repetitionrate"`     //重复率
	ExplorationTime      string    `json:"explorationtime"`    //最近探查时间
	Engineid             string    `json:"engineid"`
	Initdb               string    `json:"initdb"`
	Database             string    `json:"database"`   //dbname 数据库
	SourceDb             string    `json:"sourcedb"`   //存储源--也可以是schema或者database
	Enginename           string    `json:"enginename"` //引擎名称
	Enginetype           string    `json:"enginetype"`
	ProjectID            string    `json:"projectid"`
	TagList              []TagList `json:"taglist"`
	SourceTableCatalogid string    `json:"sourcetablecatalogid"`
	//SourceTableId        string             `json:"sourcetableid"`
	Pro_Type        string             `json:"pro_type"`
	Fieldinfo       []ProblemFieldInfo `json:"fieldinfo"`
	Duproles        TbDupRoles         `json:"duproles,omitempty"` //重复数据策略
	Fieldnum        int                `json:"fieldnum"`
	ExtractDbname   string             `json:"extractdbname"`
	ExtractSchema   string             `json:"extractschema"`
	ExtractTable    string             `json:"extracttable"`
	ProblemType     int                `json:"problemtype"`     //1代表重复数据 2代表问题数据 3代表既有重复数据又有问题数据
	IsHistory       bool               `json:"ishistory"`       //true代表是历史数据
	DupDataResource int                `json:"dupdataresource"` //0代表数据接入模组 ;1代表数据监控模组
	//RulerScore      RulerScore         `json:"rulerscore"`
	HistoryLogInfo  []HistoryLogInfo `json:"historyloginfo"` //按照月份记录表的统计信息
	DsVersion       int              `json:"dsversion"`      //DS伪版本
	MiddleTableDir  string           `json:"middletabledir"`
	MiddleTableType int              `json:"middletabletype"` //0代表质量问题数据界面 1代表中间表界面
	MetadataIdNull  bool             `json:"metadataidnull"`  //metadataid是否为空
	Module          int              `json:"module"`
}
type HistoryLogInfo struct {
	YearMonth          string             `json:"yearmonth"`          //ex:202407
	VerifyFieldNumber  int                `json:"verifyfieldnumber"`  //核验字段数
	VerifyRulerNumber  int                `json:"verifyrulernumber"`  //核验规则数
	VerifyRecord       int                `json:"verifyrecord"`       //核验记录数
	ProblemRecord      int                `json:"problemrecord"`      //问题记录数
	ProblemQualifyRate float64            `json:"problemqualifyrate"` //问题合格率
	Fieldinfo          []ProblemFieldInfo `json:"fieldinfo"`
	Duproles           TbDupRoles         `json:"duproles,omitempty"` //重复数据策略
	//DupDataResource    int                `json:"dupdataresource"`    //0代表数据接入模组 ;
	RulerScore       RulerScore       `json:"rulerscore"`
	History          int              `json:"history"`     //表名命名规则对应的版本   0代表ds5.7.1 ;1代表DS5.7.2及以后
	ProblemType      int              `json:"problemtype"` //1代表重复数据 2代表问题数据 3代表既有重复数据又有问题数据
	RulerScoreIsZero RulerScoreIsZero `json:"rulerscoreiszero"`
	Timeliness       *Timeliness      `json:"timeliness,omitempty"`
}
type RulerScore struct {
	TableQualityScore float64 `json:"tableQualityScore"` //表质量分
	Integrity         float64 `json:"integrity"`         //完整性
	Normative         float64 `json:"normative"`         //规范性
	Accuracy          float64 `json:"accuracy"`          //准确性
	Uniformity        float64 `json:"uniformity"`        //一致性
	Timeliness        float64 `json:"timeliness"`        //时效性
	Accessibility     float64 `json:"accessibility"`     //可访问性
}
type RulerScoreIsZero struct {
	TableQualityScoreIsZero bool `json:"tableQualityScoreiszero"` //表质量分
	IntegrityIsZero         bool `json:"integrityiszero"`         //完整性是否为0,true代表0;false代表--
	NormativeIsZero         bool `json:"normativeiszero"`         //规范性
	AccuracyIsZero          bool `json:"accuracyiszero"`          //准确性
	UniformityIsZero        bool `json:"uniformityiszero"`        //一致性
	TimelinessIsZero        bool `json:"timelinessiszero"`        //时效性
	AccessibilityIsZero     bool `json:"accessibilityiszero"`     //可访问性
}
type ProblemDetailInfo struct {
	Fieldnum           int                `json:"fieldnum"`           //字段数
	EngineName         string             `json:"enginename"`         //引擎名称
	EngineType         string             `json:"enginetype"`         //引擎类型
	SourceDBname       string             `json:"sourcedbname"`       //来源库名
	SourceTable        string             `json:"sourcetable"`        //来源表
	VerifyFieldNumber  int                `json:"verifyfieldnumber"`  //核验字段数
	VerifyRuleNumber   int                `json:"verifyrulenumber"`   //核验字段数
	VerifyRecord       int                `json:"verifyrecord"`       //核验记录数
	ProblemRecord      int                `json:"problemrecord"`      //问题记录数
	ProblemQualifyRate float64            `json:"problemqualifyrate"` //问题合格率
	Duproles           TbDupRoles         `json:"duproles,omitempty"` //重复数据策略
	Fieldinfo          []ProblemFieldInfo `json:"fieldinfo"`
}
type TbDupRoles struct {
	InputType         int      `json:"inputtype"`           //重复数据的两种来源：0代表数据接入 1代表数据监控
	RepetitionMainKey []string `json:"repetitionmainkey"`   //重复主键
	Problem           bool     `json:"problem,omitempty"`   //放入问题库--true代表全部放入问题库
	Dataclean         bool     `json:"dataclean,omitempty"` //数据去重--true代表去除完全重复数据
	RepetitionNumber  float64  `json:"repetitionnumber"`    //重复数据量
	RepetitionRate    float64  `json:"repetitionrate"`      //重复率
}
type ProblemFieldInfo struct {
	Number           int       `json:"number"`     // 序号
	Name             string    `json:"name"`       // 字段名称
	Annotation       string    `json:"annotation"` // 注释
	FieldType        string    `json:"fieldtype"`  // 字段类型
	RulerId          []string  `json:"rulerid"`
	ExploratoryRule  []string  `json:"exploratoryrule"`  //探查规则名称
	RuleType         []string  `json:"ruletype"`         //规则类型 单字段 single / 多字段 multiple
	QualityDimension []string  `json:"qualitydimension"` //质量维度
	VerifyRecord     []int     `json:"verifyrecords"`    //核验记录数
	ProblemRecord    []int     `json:"problemrecords"`   //问题记录数
	QualifyRate      []float64 `json:"qualifyrate"`      //合格率
}
type QualityDataInfo struct {
	Id              string           `json:"id"` //治理任务id
	ProjectID       string           `json:"projectid"`
	TableName       string           `json:"tablename"`
	Page            int              `json:"page,omitempty"`
	Perpage         int              `json:"perpage,omitempty"`
	IsProblemTable  bool             `json:"isproblemtable"` //true代表问题数据;false代表重复数据
	EngineId        string           `json:"engineid"`
	Initdb          string           `json:"initdb"`
	Column          string           `json:"column"`  //字段名称
	Content         string           `json:"content"` //字段内容
	Updatetime      string           `json:"updatetime"`
	JobID           string           `json:"jobid"`
	StartTime       string           `json:"starttime"`
	EndTime         string           `json:"endtime"`
	ProblemVersion  string           `json:"problemversion"` //问题记录版本
	InputType       int              `json:"inputtype"`
	Sort            int              `json:"sort"`
	FieldSort       string           `json:"fieldsort"`
	NewTBinfo       NewTBinfo        `json:"newtbinfo"`
	MiddleTableType int              `json:"middletabletype"` //1代表中间问题表界面 0代表质量问题数据界面
	Cid             string           `json:"cid"`             //组件id
	MetadataIdNull  bool             `json:"metadataidnull"`  //metadataid是否为空
	Module          int              `json:"module"`
	HistoryLogInfo  []HistoryLogInfo `json:"historyloginfo"` //按照月份记录表的统计信息
}
type NewTBinfo struct {
	Engineid string `json:"engineid"`
	Dbname   string `json:"dbname"`
	Schema   string `json:"schema"`
	Tbname   string `json:"tbname"`
	Tbtype   string `json:"tbtype"`
}
type TaskInfo struct {
	Dbname       string   `json:"dbname"`
	Schema       string   `json:"schema"`
	Sourceid     string   `json:"sourceid"`
	Metadataid   string   `json:"metadataid"`
	Layerid      string   `json:"layerid"`
	Dbtype       string   `json:"dbtype"`
	Problem      bool     `json:"problem,omitempty"`   //放入问题库
	Dataclean    bool     `json:"dataclean,omitempty"` //数据去重
	Mainkeyfield []string `json:"mainkeyfield"`
	Engineid     string   `json:"engineid"`
	Annotation   []string `json:"annotation"`
}
type ProblemDwd_Ruleid struct {
	Fieldname       string   `json:"fieldname"`
	Extracttable    string   `json:"extracttable"`
	Extractfield    string   `json:"extractfield"`
	Number          int      `json:"number"`
	Problemdescribe []string `json:"problemdescribe"`
	Ruletype        []string `json:"ruletype"`
}
type BatchProblem struct {
	ID              string             `json:"id"`
	Extracttable    string             `json:"extracttable"`
	Extractfield    string             `json:"extractfield"`
	Number          int                `json:"number"`
	Problemdescribe []string           `json:"problemdescribe"`
	Ruletype        []string           `json:"ruletype"`
	EngineInfo      EngineInfo         `json:"engineinfo"`
	Problemsql      string             `json:"problemsql"`
	Cfsql           string             `json:"cfsql"`
	Dbname          string             `json:"dbname"`
	Tbname          string             `json:"tbname"`
	Fieldinfoarr    []string           `json:"fieldinfoarr"`
	CfFieldinfoarr  []string           `json:"cffieldinfoarr"`
	Extract         []byte             `json:"extract"`
	Fieldinfo       []ProblemFieldInfo `json:"fieldinfo"`
	DescribeArr     []string           `json:"describearr"`
	CfTable         string             `json:"cftable"`
	WtTable         string             `json:"wttable"`
	MiddleTableType int                `json:"middletabletype"`
}
type RuleInfo struct {
	Field         string   `json:"field"`
	Ruleid        []string `json:"ruleid"`
	Ruletype      []string `json:"ruletype"`
	Ruledimension []string `json:"ruledimension"`
	ProblemDesc   []string `json:"problemdesc"`
	Extracttable  string   `json:"extracttable"`
	Extractfield  string   `json:"extractfield"`
}
type ProblemDetailMonthInfo struct {
	Fieldnum       int              `json:"fieldnum"`     //字段数
	EngineName     string           `json:"enginename"`   //引擎名称
	EngineType     string           `json:"enginetype"`   //引擎类型
	SourceDBname   string           `json:"sourcedbname"` //来源库名
	SourceTable    string           `json:"sourcetable"`  //来源表
	HistoryLogInfo []HistoryLogInfo `json:"historyloginfo"`
	Historytime    []string         `json:"historytime"`
}

type DeleteTableInfo struct {
	Id             string                   `json:"id"`
	IsBatch        bool                     `json:"isbatch"`
	ProblemVersion string                   `json:"problemversion"`
	IsProblemTable bool                     `json:"isproblemtable"` //true代表问题数据;false代表重复数据
	DeleteField    []map[string]interface{} `json:"deletefield"`
}

type DeleteField struct {
	FieldName  string `json:"fieldname"`
	FieldValue string `json:"fieldvalue"`
}

type RealMessageDT struct {
	Topic     string      `json:"topic"`
	Key       interface{} `json:"key"`
	Value     string      `json:"value"`
	Partition int         `json:"partition"`
	Offset    int         `json:"offset"`
}
type TableFieldInfo struct {
	Fieldname []string `json:"fieldname"`
	Fieldtype []string `json:"fieldtype"`
	Comment   []string `json:"comment"`
	MainKey   []string `json:"mainkey"`
}

type AssociateTasksinfo struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Submitted  bool   `json:"submitted"`
	CronDetail string `json:"crondetail"`
}
type XLSXModelParseResult struct {
	Filename         string                  `json:"filename"`
	SheetNames       []string                `json:"sheetnames"`
	ModelDefinitions [][]MetaSourceFieldInfo `json:"modeldefinitions"` // 代码表
	TableName        string                  `json:"tablename"`
	TableDesc        string                  `json:"tabledesc"`
}
type ModeUploadInfo struct {
	MetaSourceFieldInfo []MetaSourceFieldInfo `json:"metasourcefieldinfo"`
	ExternalPathId      string                `json:"externalpathid"`
}

type FileTbInfo struct {
	FileName            string                `json:"filename"` //excel文件或者csv文件的名称
	TbName              string                `json:"tbname"`   //解析的excel文件中sheet页的名称，作为表名称
	MetaSourceFieldInfo []MetaSourceFieldInfo `json:"metasourcefieldinfo"`
	ExternalPathId      string                `json:"externalpathid"`
}

type UploadTbInfosParam struct {
	Tblist []MetaSourceTb `json:"tblist"` //多个表结构
}

type MetricEngine struct {
	IP       string `json:"ip"`
	Port     int    `json:"port"`
	UserName string `json:"username"`
	Password string `json:"password"`
	DBName   string `json:"dbname"`
	Schema   string `json:"schema"`
}

type QualityRule struct {
	ID            string `json:"id"`
	RuleTemplate  int    `json:"ruletemplate"`  //规则模板
	RealDimension string `json:"realdimension"` //二级维度
	//206 数据表级时效性校验
	Timeliness Timeliness `json:"timeliness,omitempty"`
}

type Timeliness struct {
	TimelinessType     string     `json:"timelinesstype"`            //检测时间区间 指定时间 visiable  用户自定义 regex
	TimelinessStart    string     `json:"timelinessstart,omitempty"` //时间区间开始
	TimelinessEnd      string     `json:"timelinessend,omitempty"`   //时间区间结束
	TimelinessRepeat   string     `json:"timelinessrepeat"`          //重复时间 never,week,month,year
	TimelinessRegex    string     `json:"timelinessregex"`           //时间区间正则
	RepeatSection      [][]string `json:"repeatsection"`             //重复区间
	Tabletimelinessres bool       `json:"tabletimelinessres"`        //是否有更新
	ShowTimeliness     bool       `json:"showtimeliness"`            //是否展示
	RealDimension      string     `json:"realdimension"`             //二级维度
}
