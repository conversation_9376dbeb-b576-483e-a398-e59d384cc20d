package common

import (
	"regexp"
	"sync"
)

var (
	// DefaultFolderId .
	DefaultFolderId         = "wjdcfdglbd666"
	ResourceDefaultFolderId = "wjdcfdglbd667"
	QualityDefaultFolderId1 = "wjdcfdglbd668" //数据质量
	QualityDefaultFolderId2 = "wjdcfdglbd669" //数据贡献
	QualityDefaultFolderId3 = "wjdcfdglbd670" //数据申请使用
	QualityDefaultFolderId4 = "wjdcfdglbd671" //整体概况

	// datax目录
	DataxDir = "/opt/dana/datax/"
	HiveLog  = "/var/dana/log/mg/hive-log/"
)

var (
	// DataBaseMetaData 元数据
	DataBaseMetaData = "danastudio_metadata"
	// TbMetaLayer 元数据开发--层信息
	TbMetaLayer = "meta_layer"
	// TbMetaSource 元数据开发--存储源/数据库
	TbMetaSource = "meta_sourcedb"

	// TbMetaTable 元数据开发--存储源下的表
	TbMetaTable = "meta_sourcetb"
	// TbMetaVersion 元数据开发--存储源下的表版本管理
	TbMetaVersion = "meta_tbversion"
	// TbMetaDir 元数据开发--文件夹信息
	TbMetaDir = "meta_dir"
	// TbMetaStandard 标准管理--标准信息
	TbMetaStandard = "meta_standard"
	TbCodeTable    = "meta_codetable" //代码表 5.9.0
	TbCtHis        = "meta_ct_his"    //代码表历史版本 5.9.0
	TbStHis        = "meta_st_his"    //标准表历史版本 5.9.0
	TbCtRecension  = "ct_recension"   //代码表变更记录 5.9.0
	TbStRecension  = "st_recension"   //标准表变更记录 5.9.0
	// TbMetaTag 标准管理--标签信息
	TbMetaTag = "meta_tag"
	// TbMetaBlood .
	TbMetaBlood  = "meta_blood"
	TbExtractDb  = "meta_extractdb"
	DataTemplate = "datatemplate"
	TbInfoMeta   = "meta_extract_tbinfo"
	PushRecord   = "pushrecord"
	//auth
	DatabaseAuth   = "danastudio_auth"
	TbRole         = "tb_role"
	TbUserRelation = "tb_user_relation"
	TbUser         = "tb_user"
	//质量问题表
	QualityProblem = "tb_qualityproblem"
)

// 工作流相关

// DataBaseJob .
var DataBaseJob = "dodox_jobmanager_job"

// TbJob .
var TbJob = "tb_job"

// TbRuledJob 告警规则与任务对应关系表
var TbRuledJob = "tb_ruledjob"

// DataBaseRec
var DataBaseRec = "dodox_jobmanager_record"

// TbRecord
var TbRecord = "tb_record"

// DBAntsLab .
var DBAntsLab = "danastudio_ants_lab"

// TBAccess .
var TBAccess = "tb_access"

// DBAsset .
var DBAsset = "danastudio-asset"

// TBLayer .
var TBLayer = "db_layer"

// DBPlatform .
var DBPlatform = "danastudio_platform"

// TBEngine .
var TBEngine = "tb_engine"

var TbDriver = "tb_driver"

// InOutManager .
var InOutManager = "tb_inout_manager"

// DataBaseModel .
var DataBaseModel = "danastudio_ants_model" // 案例和模型
// DataBaseLab .
var DataBaseLab = "danastudio_ants_lab" // 未上线任务
// DataBaseFtp .
var DataBaseFtp = "danastudio_ants_ftp" // ftp信息
var DSModeAsset = "danastudio_modeling_asset"
var TbResource = "tb_resource"
var TBAssetIventory = "tb_asset_inventory"
var TBCalling = "tb_calling"
var DataInteractiveCommunication = "danastudio_modeling_interactive"
var TbDataRequirements = "tb_data_requirements"        //数据需求
var TbDataErrorCorrection = "tb_data_error_correction" //数据纠错
var DataBaseOPData = "danastudio_operation_data"

// TbModel .
var TbModel = "tb_model"

// TbFtp .
var TbFtp = "tb_ftp"

// TbDataBase .
var TbDataBase = "tb_database"

// TbOdsDataBase .
var TbOdsDataBase = "tb_odsdatabase"

// DataBaseAssert .
var DataBaseAssert = "danastudio-asset"

// TbAccess .
var TbAccess = "tb_access"
var TbDataReport = "tb_datareport"
var TbReportRecord = "tb_report_record"
var TbMetrics = "tb_metrics"
var TbMetricsQuality = "tb_metrics_quality"

// BathcTbAccess 批量采集子任务保存索引，结构 = tbaccess
var BathcTbAccess = "batch_tb_access"

// TbDev .
var TbDev = "tb_dev"
var TbDevHis = "tb_dev_his"
var TbAPI = "meta_api" //api接口

// TbAnalyse .
var TbAnalyse = "tb_analyse"

// TbFolder .
var TbFolder = "tb_folder"

var TbSensitiveRank = "tb_sensitiverank"
var TbSensitiveCheck = "tb_sentivecheck"
var TbReportCheck = "tb_reportcheck"

// TbCreate .
var TbCreate = "tb_access_tableadd"

// TbMiddle .
var TbMiddle = "tb_middle"

// DataBasePlatform .
var DataBasePlatform = "danastudio_platform"

// TableEngine .
var TableEngine = "tb_engine"

// TbProject .
var TbProject = "tb_project"

// TbLayer .
var TbLayer = "db_layer"

// TableTag .
var TableTag = "tb_tag"

// NewTbAsset .
var NewTbAsset = "tb_newasset"

// TbMetaDev 元数据开发--元信息表
var TbMetaDev = "tb_metadev"

// TbMetaDevHistory 元数据开发-操作历史表
var TbMetaDevHistory = "tb_metatbhistory"

// TbMetaModule 元数据模型表
var TbMetaModule = "tb_metamodule"

// NewDbAsset 层存储源信息表
var NewDbAsset = "db_newasset"

// DateFormat 日期格式
var DateFormat = "2006-01-02 15:04:05"

const (
	TBDWSTB         = "dws_tb_info"
	TBServiceTB     = "service_info"
	TbFlowMonitor   = "flow_monitor_info"
	PublishDwsInfo  = "publish_dws_info"
	PublishRecordTB = "publish_record"
)

// ai ods info
var TbOdsTableInfo = "tb_ods_table_info"

// HdfsPath 获取hdfs主节点ip的py脚本路径
var HdfsPath = "/etc/danastudio/hdfs_namenodes.py"

// EtcPath ds配置文件路径
var EtcPath = "/etc/danastudio/"

// TDCTokenFile tdc token 文件路径
var TDCTokenFile = "/etc/danastudio/tdctoken.csv"

// EtcPrestoPath presto用户keytab路径
var EtcPrestoPath = "presto.keytab"

// MasterIPReg hive主节点ip正则
var MasterIPReg = regexp.MustCompile(`[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+`)

// HivePath hive抽取任务json文件存放位置
var HivePath = "/var/dana/dodox/filemanager/file/danastudio-unsubmit/hive"

// HiveMasterIP hive集群主节点ip
var HiveMasterIP string

// DatabaseLayer 层信息表
var DatabaseLayer = "db_layer"

// TBNode 节点信息表
var TBNode = "tb_node"

// Hive12 1.2版本hive
var Hive12 = "1.2"

// Hive3 3.1版本hive
var Hive3 = "3.1"

// HiveTdh tdh
var HiveTdh = "inceptor-0.12"

// HiveTDC tdc
var HiveTDC = "inceptor-2.1"

// HiveCDH cdh
var HiveCDH = "6.2.0"

// HiveHuawei Huawei
var HiveHuawei = "1.2.1"

// TDCLoadPath tdc 落地抽取本地默认路径
var TDCLoadPath = "/opt/danastudio/ants/tdc_simple_load"

// TDCCurlPy tdc curl命令执行脚本
var TDCCurlPy = "/etc/danastudio/tdccurl.py"

// TDCLoadExtract tdc落地抽取类型
var TDCLoadExtract = "落地抽取"

// TypeOrc orc文件类型
var TypeOrc = "orc"

// TypeText text文件类型
var TypeText = "text"

// TypeOrcInput orc输入文件类型
var TypeOrcInput = "OrcInputFormat"

// TypeTextInput text输入文件类型
var TypeTextInput = "TextInputFormat"

// HiveTimeOut .
var HiveTimeOut = 55

// SourceStatusEmpty 抽取源表状态
var SourceStatusEmpty = "空"

// SourceStatusNotEmpty .
var SourceStatusNotEmpty = "正常"

// SchemaPublic 专题表的默认 schema
const SchemaPublic = "public"

// MetaSkillProperty 元模型技术属性
var MetaSkillProperty = []string{
	"表名称", "表类型", "字段数", "字段信息", "表描述", "表生命周期",
	"存储引擎", "存储位置", "分区信息", "共享信息", "共享时间", "共享引用量",
	"总数据量", "更新频率", "最新更新量", "平均更新量", "最新更新时间",
}

// MetaBusinessProperty 元模型业务属性
var MetaBusinessProperty = []string{
	"业务主键", "引用维度表",
}

// MetaManageProperty 元模型管理属性
var MetaManageProperty = []string{
	"表负责人", "表血缘关系",
}

// TimeFormat 标准时间格式
var TimeFormat = "2006-01-02 15:04:05"

// MetaOwner 元模型搜索--所属用户
var MetaOwner = "所属用户"

// MetaLabel 搜索条件-标签名称
var MetaLabel = "标签名称"

// MetaModule 元模型搜索--模型名称
var MetaModule = "模型名称"

// MetaCatalog 元模型搜索--分类名称
var MetaCatalog = "分类名称"

// MetaCreated 元模型搜索--创建时间
var MetaCreated = "创建时间"

// MetaModified 元模型搜索--更新时间
var MetaModified = "更新时间"

// MetaDevData 元数据搜索--有无数据
var MetaDevData = "有无数据"

// MetaDevComplete 元数据搜索--完成度
var MetaDevComplete = "完成度"

// MetaDevTable 元数据搜索--表名称
var MetaDevTable = "表名称"

// MetaDefaultModule 默认模型名称
var MetaDefaultModule = "默认模型"

// MetaDefModFolder 默认分类文件夹
var MetaDefModFolder = "默认文件夹"

// MetaFolderMod 文件夹类别
var MetaFolderMod = "metamodel"

// MetaLeadInPath 元数据导入路径
var MetaLeadInPath = "/var/dana/danastudio/meta/leadin"

// MetaLeadOutPath 元数据导出路径
var MetaLeadOutPath = "/var/dana/danastudio/meta/leadout"

// SuffixExcelFile excel文件后缀名
var SuffixExcelFile = ".xlsx"
var MaxXLSXColumn = 1048576

var (
	// TableODSTBInfo 汇聚表信息
	TableODSTBInfo = "ods_tb_info"
	// ODSInfo .
	ODSInfo = "ods_info"
	// TableBASETBInfo 治理表信息
	TableBASETBInfo = "base_tb_info"
	// TableTaskInfo .
	TableTaskInfo = "tb_task"
	// LayeredName .
	LayeredName = "分层"
	// OdsName .
	OdsName = "汇聚层"
	// BaseName .
	BaseName = "治理层"
	// DwdName .
	DwdName = "主题层"
	// DwsName .
	DwsName = "专题层"
	// GlobalName .
	GlobalName = "全局"
	// UnclassifiedName .
	UnclassifiedName = "未分类"
)

// var TbDag = "tb_dag"

// UnlimitSize .
var UnlimitSize = 200000
var BigSize = 200000

// EmptyString .
var EmptyString = ""

// AuthUser .
var AuthUser = "eagles"

// AuthPasswd .
var AuthPasswd = "datatom.com"

// Modules .
var Modules = []string{"access", "develop", "analyse"}

// Access .
var Access = []string{"datax"}

// Develop .
var Develop = []string{"python3", "python", "shell", "php", "sql", "cmd", "kettle-trans", "kettle-job", "kettle", "stream"}

// var DBAntsLab = "danastudio_ants_lab"
// var TBAccess = "tb_access"
// var DBAsset = "danastudio-asset"
// var TBLayer = "db_layer"
// var DBPlatform = "danastudio_platform"
// var TBEngine = "tb_engine"
// var InOutManager = "tb_inout_manager"

// CHaracter .
var CHaracter = "abcdefghijklmnopqrstuvwxyz"

// Analyse .
var Analyse = []string{"example", "blankml", "model"}

// AdminAuth 永久有效的token
// var AdminAuth = "Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
var AdminAuth = "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

// 离线下载文件路径
var DWSOffLine = "/var/danastudio/DWSOfflineLoadDown/"

// 模块名称
const (
	AccessMD    = "access"
	CollectMD   = "collect"   //数据采集
	StandardMD  = "standard"  //数据标准
	GovernMD    = "govern"    //数据治理
	VaultMD     = "vault"     // 数据中心
	OperatingMD = "operating" // 运维中心
	PlatformMD  = "platform"  // 平台中心
	// 4.6 之后规范的模块
	CollectionMD = "collection"     //数据采集
	WorkFlowMD   = "customProcess"  //工作流
	WorkflowMD   = "customProcess"  //工作流
	MetaDataMd   = "metadataManage" // 元数据
	DataAnalysis = "dataAnalyze"    //数据分析

	//5.1 新增
	ModelMD          = "model" //模型管理
	ResourceRegister = "resourceregister"
	RealMessage      = "realmessage"  //实时消息流管理
	RealTimeSync     = "realtimesync" //实时同步

	//5.9
	QualityTemplate = "qualitytemplate" //质量报告模板
)

// 引擎
const (
	Stork      = "stork"
	Teryx      = "teryx"
	Hive       = "hive"
	GaussDB    = "gaussdb"
	GaussDBA   = "gaussdba"
	Inceptor   = "tdh_inceptor"
	DMDB       = "dameng"
	UXDB       = "uxdb"
	MYSQL      = "mysql"
	ORACLE     = "oracle"
	POSTGRES   = "postgres"
	POSTGRESQL = "postgresql"
	ODPS       = "odps"
	GREENPLUM  = "greenplum"
	Gbase8s    = "gbase8s"
	SQLserver  = "SQL server"
	DB2        = "db2"
	TDSQL      = "tdsql"
	DRealDB    = "drealdb"
)

// MPP数据库分布方式
const (
	HASH       = "HASH"
	RANDOMLY   = "RANDOMLY"
	REPLICATED = "REPLICATED"
	APPEND     = "APPEND"
	DEFAULT    = "DEFAULT"
	FULLY      = "FULLY"
)

const (
	EngPresto = "presto"
	EngSpark  = "spark"
)

const (
	ODSDB  = "odsdb"
	BASEDB = "basedb"
	DWDDB  = "dwddb"
	DWSDB  = "dwsdb"
)

//数据同步页面所有接口设计常量存放位置
//抽取源数量最大值默认100

// ExtractDBNum 抽取源数量最大值默认100
var ExtractDBNum = 100

// Prefix 存储源前缀
var Prefix = "ods_"

// MetaPersonalTag 元数据自定义标签
var MetaPersonalTag = "自定义"

// MetaWordSHeet1 元数据导入导出列数--Sheet1
var MetaWordSHeet1 = []string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T"}

// MetaWordSHeet2 元数据导入导出列数--Sheet2
var MetaWordSHeet2 = []string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"}

var DirtyComment = []string{";", "'", "--"}

// MetaTopCat 元数据导入导出头部类目
var MetaTopCat = map[string]string{
	"A1": "基本信息",
	"N1": "编目信息",
	"Q1": "标签信息",
}

// MetaMergeCell1 元数据导入导出合并单元格范围--Sheet1
var MetaMergeCell1 = map[string]string{
	"A1": "M1",
	"N1": "P1",
	"Q1": "T1",
}

// MetaMergeCell2 元数据导入导出信息--Sheet2
var MetaMergeCell2 = map[string]string{
	"A1": "表名",
	"B1": "序号",
	"C1": "字段名",
	"D1": "字段类型",
	"E1": "是否主键",
	"F1": "描述",
	"G1": "引用代码表",
	"H1": "引用标准",
	"I1": "血缘关系",
	"J1": "血缘表",
	"K1": "血缘字段",
	"L1": "是否加工",
}

// MetaCategories 元数据导入导出详细类目
var MetaCategories = map[string]string{
	"A2": "表名称",
	"B2": "中文名",
	"C2": "表类型",
	"D2": "表描述",
	"E2": "表主键",
	"F2": "生命周期",
	"G2": "字段数",
	"H2": "数据量",
	"I2": "存储量",
	"J2": "来源/存储引擎",
	"K2": "存储位置",
	"L2": "表负责人",
	"M2": "安全等级",
	"N2": "分层编目",
	"O2": "来源编目",
	"P2": "社会分类",
	"Q2": "组织标签",
	"R2": "涉及部门",
	"S2": "涉及人员",
	"T2": "自定义",
}

var RefreshWait = "wait_for"

const (
	JobVer = 1 //tb_job 和 tb_folder 数据版本，自 v4.8.0 引入，从 1 递增
	DevVer = 1 //tb_dev 数据版本，自 v4.8.0 引入，从 1 递增
)

const Hidden = 1 //隐藏的资源(文件夹等)

const (
	XXN  = "xxn_"  //中间表前缀
	Temp = "_temp" //临时表后缀
)

var TbSensiRole = "tb_rules"

var SymbolType = "PROCESS"

var TMPTestRun = "_tmp_testrun"

const DefaultPwd = "123456"

const NotNum = "makeitnotnum"

// DSV5.3新增
const BatchNum = 2000 //批量采集bulk并发个数

var BatchLock sync.Mutex //批量采集全局锁

var LogFileLimit float64

var LogFileMax float64

// 文件夹模块
var FD_DataReport = "datareport"
var FD_Metrics = "metrics"               //指标管理
var FD_QualityMetrics = "qualitymetrics" //质量指标

// 数据填报模块
var STORK_DEFAULT = "postgres"
var STORK_REPORTDB = "datareport"
var STORK_QUALITYDB = "dataquality"

var REPORT_TIMEFD = "_dt_inserttime"
var REPORT_USERFD = "_dt_userid"
var REPORT_RECORDFD = "_dt_recordid"

var REPORT_SERIAL = "_dt_serialid"       //自增ID
var REPORT_SERIAL_NUM = "_dt_serial_num" //自增ID用于问题数据
var REPORT_HASWT = "_dt_haswt"
var EXTRA_COLUMNS = []string{REPORT_TIMEFD, REPORT_USERFD, REPORT_RECORDFD}
var EXTRA_COLUMNTYPES = []string{"text", "text", "text", "text", "int"}
var EXTRA_COLUMNCOMMENT = []string{"插入时间", "插入用户", "记录ID", "自增ID"}
var DATA_APPEND = "append"
var DATA_TRUNCATE = "truncate"
var DATA_UPDATE = "update"

var OperTypeADD = "add"
var OperTypeDel = "delete"
var OperTypeUpdate = "update"
var Init_Check_Rules = []string{
	"固话",
	"信用卡",
	"邮箱",
	"手机号",
	"居民身份证",
	"驾驶证",
	"军官证",
	"中国护照",
	"车牌号",
	"居民身份证或者中国护照",
	"手机号或者固话",
}
var XTYZ_CHECK_TYPE = "系统预置"
var ZDY_CHECK_TYPE = "自定义"
var CHECK_NUM_TYPE = "数值类型"
var CHECK_STR_TYPE = "字符型"
var CHECK_TIME_TYPE = "时间类型"

const (
	CustomStyle  = "custom"
	SystemStyle  = "system"
	SingleType   = "single"
	MultipleType = "multiple"
	TableType    = "table"
)

const TbQualityRule = "tb_qualityrule"
const FDQualityRule = "qualityrules"
const TableODSWT = "ods_wt"

//var STORK_REPORTDB_SCHEMA = "datareport"

const TbQualityConf = "tb_qualityconf"                     //质量报告配置模板
const TbQualityReport = "tb_qualityreport"                 //质量报告记录表
const TbQualityReportTemplate = "tb_qualityreporttemplate" //质量报告模板
