// Copyright (c) Datatom Software, Inc.(2018)
//
// Author:袁赛杰（<EMAIL>）
//
// Creating Time:
package source

type DevInfo struct {
	Id          string        `json:"id"`       //实验的唯一标识符
	Jobid       string        `json:"jobid"`    //自身上线的jobid
	Dags        []GAGIds      `json:"dags"`     //所属工作流
	Dirid       string        `json:"dirid"`    //文件夹id
	Fatherid    string        `json:"fatherid"` //父级文件夹id
	Notetype    string        `json:"notetype"`
	Notename    string        `json:"notename"`
	Datecreated string        `json:"datecreated"`
	Updatetime  string        `json:"updatetime"` //更新时间
	Submitted   bool          `json:"submitted"`
	Submitinfo  DevSubmitInfo `json:"submitinfo"` //保存上线版本的实验信息

	User           string        `json:"user"`
	Username       string        `json:"username"`
	Sqlid          string        `json:"sqlid"`
	Actualnotetype string        `json:"actualnotetype"`
	Udfcmd         string        `json:"udfcmd"`
	Content        string        `json:"content"`
	Variables      []string      `json:"variables"` //参数列表
	Delif          bool          `json:"delif"`     //如果是从工作流中临时创建的，当为未上线时予以删除
	Timevars       []Devtimevars `json:"timevars"`  //时间参数

	Tags         []Tag  `json:"tags"`         //标签
	Trash        bool   `json:"trash"`        //是否在垃圾桶
	Star         bool   `json:"star"`         //星标任务
	Toptime      string `json:"toptime"`      //置顶时间
	Initdb       string `json:"initdb"`       //真实的库名
	SourceDBName string `json:"sourcedbname"` //sourcedb 名称，库名或 schema 名

	Pid               int              `json:"pid"`
	Deletetime        string           `json:"deletetime"`
	Description       string           `json:"description"`
	Concat            []string         `json:"concat"`                      //拼加tag的字段
	Finalcmd          string           `json:"finalcmd"`                    //试运行时的运行命令
	Ktrs              []string         `json:"ktrs"`                        //相关ktrs的名字，针对kettle-jobs
	Kjbs              []string         `json:"kjbs"`                        //保存被哪些kjb使用
	Isown             bool             `json:"isown"`                       //是否是独立任务
	Retrynum          int              `json:"retrynum"`                    //重试次数
	IsRetry           bool             `json:"isretry"`                     //是否重试：true：重试，false：不重试
	RetryInterval     int              `json:"retryinterval"`               //重试间隔时长，单位为分钟
	ProjectID         string           `json:"projectid"`                   //所属的项目id
	DevVersion        int              `json:"devversion"`                  //数据版本
	Materialinfo      MaterialInfo     `json:"materialinfo"`                //素材库相关信息
	Customtable       string           `json:"customtable"`                 //sql脚本输出表
	Customrefertables CustomTableRoles `json:"customrefertables,omitempty"` //自定义脚本中间表处理
	TaskID            string           `json:"taskid"`
	SubID             int              `json:"subid"` //工作流内的子任务ID

	LayerID   string `json:"layerid"` //分层ID
	Module    int    `json:"module,omitempty"`
	ClassName string `json:"classname"` //java  类名

	Finish      bool `json:"finish,omitempty"` //是否运行结束
	MemoryLimit int  `json:"memorylimit"`
	//5.8
	APIID     string `json:"apiid"`
	ParamID   string `json:"paramid"`
	ParamName string `json:"paramname"`
}

// sql 脚本返回的字段类型信息
type ResType struct {
	Num   int               `json:"num"`
	Types map[string]string `json:"types"`
}

// 素材库相关信息
type MaterialInfo struct {
	IsMaterial bool   `json:"ismaterial"` //是否是素材库
	Taskid     string `json:"taskid"`     //所选素材任务
}

type DevSubmitInfo struct {
	Submitpath string `json:"submitpath"`
	Submitcmd  string `json:"submitcmd"`

	Name           string `json:"name"`
	Description    string `json:"description"`
	ExectTimes     *int64 `json:"exectimes,omitempty"`
	Scheduletype   int    `json:"scheduletype"`
	Scheduledetail string `json:"scheduledetail"`
	//	Env            []string `json:"env"`
	AssignNode string `json:"assignnode"`
}

type DevId struct {
	Id string `json:"id"`
}

type DevDirId struct {
	MoveDirId string `json:"moveDirId"`
}

type DevIds struct {
	Ids []string `json:"ids"`
}

type DevUser struct {
	User string `json:"user"`
}

// *********列举相关*************
type DevList struct {
	User    string `json:"user"`
	Listall bool   `json:"listall"`
}

type DevListTrash struct {
	User    string `json:"user"`
	Listall bool   `json:"listall"`
	Page    int    `json:"page"`
	Perpage int    `json:"perpage"`
}

// ********实验操作相关**********
type NewDev struct {
	User           string   `json:"user"`
	Name           string   `json:"name"`
	Dirname        string   `json:"dirname"`
	Dirid          string   `json:"dirid"` //前端不用传
	Notetype       string   `json:"notetype"`
	Tags           []Tag    `json:"tags"`
	Delif          bool     `json:"delif"` //是否为拖拽创建的脚本
	Sqlid          string   `json:"sqlid"` //sql 类型脚本instanceid
	Actualnotetype string   `json:"actualnotetype"`
	Initdb         string   `json:"initdb"`       //真实的库名
	SourceDBName   string   `json:"sourcedbname"` //sourcedb 名，对应库名或 schema 名
	Content        string   `json:"content"`
	Variables      []string `json:"variables"` //参数列表
	Udfcmd         string   `json:"udfcmd"`    //udf类型的脚本文件
	Description    string   `json:"description"`
	Isforce        bool     `json:"isforce"`
	Isown          bool     `json:"isown"`         //脚本是否独立
	IsRetry        bool     `json:"isretry"`       //是否重试：true：重试，false：不重试
	RetryInterval  int      `json:"retryinterval"` //重试间隔时长，单位为分钟
	Retrynum       int      `json:"retrynum"`      //重试次数
	ProjectID      string   `json:"projectid"`     //所属项目id

	IsImport    bool   `json:"isimport"`   //是否为导入的脚本，true表示导入
	IsMetadata  bool   `json:"ismetadata"` //是否为元数据SQL页面
	WorkflowID  string `json:"workflowid"` //所属工作流id
	DevVersion  int    `json:"devversion"` //数据版本
	SubID       int    `json:"subid"`      //工作流内的子任务号
	LayerID     string `json:"layerid"`    //分层ID
	ClassName   string `json:"classname"`  //java 类名
	MemoryLimit int    `json:"memorylimit"`

	JobName string `json:"jobname"` //任务名
	//5.8
	APIID     string `json:"apiid"`
	ParamID   string `json:"paramid"`
	ParamName string `json:"paramname"`
}

type DevSave struct {
	Id             string        `json:"id"`
	Sqlid          string        `json:"sqlid"`
	Actualnotetype string        `json:"actualnotetype"`
	Initdb         string        `json:"initdb"`
	Udfcmd         string        `json:"udfcmd"`
	Content        string        `json:"content"`
	Variables      []string      `json:"variables"`    //参数列表
	Timevars       []Devtimevars `json:"timevars"`     //时间类型参数
	SourceDBName   string        `json:"sourcedbname"` //sourcedb 名，对应库名或 schema 名

	//4.8.0 新增
	Customtable       string           `json:"customtable"`                 //输出表
	Customrefertables CustomTableRoles `json:"customrefertables,omitempty"` //自定义脚本中间表处理

	//5.3新增
	LayerID   string `json:"layerid"`   //分层ID
	ClassName string `json:"classname"` //java 类名

	//content 编码方式
	Encode string `json:"encode,omitempty"`

	MemoryLimit int `json:"memorylimit"`
}

type Devtimevars struct {
	Name string `json:"name"`
	Type string `json:"type"`
}
type DevChange struct {
	Name  string `json:"name"`
	Type  string `json:"type"`
	Value string `json:"value"`
}

//type DevStop struct {
//	Id  string `json:"id"`
//	Pid int    `json:"pid"`
//}

type DevMove struct {
	Ids       []string `json:"ids"`
	MoveDirId string   `json:"moveDirId"`
}
type DevMove1 struct {
	Id        string `json:"id"`
	MoveDirId string `json:"moveDirId"`
}

type DevDetail struct {
	Id            string `json:"id"`
	Newname       string `json:"newname"`
	Newdir        string `json:"newdir"`
	Newdirid      string `json:"newdirid"` //不用前端传
	Star          string `json:"star"`
	Top           string `json:"top"`
	Tags          []Tag  `json:"tags"`
	Description   string `json:"description"`
	Isown         bool   `json:"isown"`         //是否为独立的子任务
	Retrynum      int    `json:"retrynum"`      //重试次数
	IsRetry       bool   `json:"isretry"`       //是否重试：true：重试，false：不重试
	RetryInterval int    `json:"retryinterval"` //重试间隔时长，单位为分钟
	ProjectID     string `json:"projectid"`     //所属的项目id
	Initdb        string `json:"initdb"`        //真实的库名
	SourceDBName  string `json:"sourcedbname"`  //sourcedb 名称，库名或 schema 名
	SqlID         string `json:"sqlid,omitempty"`
	LayerID       string `json:"layerid,omitempty"`
	MemoryLimit   int    `json:"memorylimit"`
}

type DevRun struct {
	Id       string `json:"id"`
	Content  string `json:"content"`
	SourceID string `json:"sourceid"` // 存储源id

	Encode string `json:"encode,omitempty"` //content 编码方式
}

type DevRename struct {
	Id      string `json:"id"`
	Newname string `json:"newname"`
	Newdir  string `json:"newdir"`
}

type DevSearch struct {
	User        string    `json:"user"`
	Dirid       string    `json:"dirid"`
	Notetype    string    `json:"notetype"`
	Star        string    `json:"star"`
	Creatert    RangeTime `json:"creatert"`
	Updatert    RangeTime `json:"updatert"`
	Submitted   string    `json:"submitted"`
	Notename    string    `json:"notename"` //模糊匹配
	Tags        []string  `json:"tags"`
	Sorttype    int       `json:"sorttype"` //按创建时间排序0，按更新时间排序1，都是倒排
	Description string    `json:"description"`
	Match       string    `json:"match"`
	Listall     bool      `json:"listall"`
	Page        int       `json:"page"`
	Perpage     int       `json:"perpage"`
	Id          string    `json:"id"`
	Dagname     string    `json:"dagname"`
	Dirids      []string  `json:"dirids"`    //涉及的文件夹
	Sort        int       `json:"sort"`      // 升降序排序  1 降序  0 升序
	ProjectID   string    `json:"projectid"` //所属的项目id
}

type DevCopy struct {
	Id         string `json:"id"`
	Newname    string `json:"newname"`
	Newdirname string `json:"newdirname"`
	Dirid      string `json:"dirid"`
	User       string `json:"user"`      //用户存储执行操作的用户，前端不传
	Username   string `json:"username"`  //用户存储执行操作的用户，前端不传
	ProjectID  string `json:"projectid"` //项目id
}

// 指定名称和路径的脚本复制
type DevCopys struct {
	Id       string `json:"id"`
	User     string `json:"user"`     //用户存储执行操作的用户，前端不传
	Username string `json:"username"` //用户存储执行操作的用户，前端不传
	Path     string `json:"path"`     //副本存储路径
	FileName string `json:"filename"` //副本名称
}

//********工作流相关**********

type DevFlowId struct {
	Id string `json:"id"`
}

type DevFlowIds struct {
	Ids []string `json:"ids"`
}

type DevFlowJobid struct {
	Ids   []string `json:"ids"`
	Jobid string   `json:"jobid"`
}

type DevFlowAdd struct {
	Ids   []string `json:"ids"`
	Jobid string   `json:"jobid"`
	Name  string   `json:"name"`
}

type DevFlowRename struct {
	Ids   []string `json:"ids"`
	Jobid string   `json:"jobid"`
	Name  string   `json:"name"`
}

type DevOut struct {
	Id    string `json:"id"`
	Jobid string `json:"jobid"`
}

// **********推荐标签*********
type DevRecommendTags struct {
	User    string `json:"user"`
	Listall bool   `json:"listall"`
	Size    int    `json:"size"`
}

// ********上线下线************
type DevSubmit struct {
	User           string   `json:"user"`
	Username       string   `json:"username"`
	Id             string   `json:"id"`
	Name           string   `json:"name"`
	Description    string   `json:"description"`
	ExectTimes     *int64   `json:"exectimes,omitempty"`
	Scheduletype   int      `json:"scheduletype"`
	Scheduledetail string   `json:"scheduledetail"`
	AssignNode     string   `json:"assignnode"`
	Preid          []string `json:"preid"`
}

// *******垃圾篓相关*************
type DevDelete struct {
	Ids     []string `json:"ids"`
	Isdel   bool     `json:"isdel"`   //是否直接删除，不放人垃圾篓
	Force   bool     `json:"force"`   //是否强制删除，即使正在上线或者有其它工作流在使用
	Fromdag string   `json:"fromdag"` //是否来着于工作流的删除
}

type DevTrashDelete struct {
	Ids []string `json:"ids"`
}

type DevTrashRecover struct {
	Ids []string `json:"ids"`
	Dag GAGIds   `json:"dag"`
}

type DevDelif struct {
	Ids     []string `json:"ids"`
	Fromdag string   `json:"fromdag"`
	Isdel   bool     `json:"isdel"`
}

type DevRecif struct {
	Ids     []string `json:"ids"`
	Fromdag string   `json:"fromdag"`
}

// *********解析实验相关**********
// DevHits1  query解析第一层
type DevHits1 struct {
	Hits1 DevHits2 `json:"hits"`
}

// DevHits2  query解析第二层
type DevHits2 struct {
	Hits2 []DevSource `json:"hits"`
	Total int         `json:"total"`
}

// DevSource query解析第三层
type DevSource struct {
	Devlab DevInfo `json:"_source"`
}

type DevSetYarn struct {
	Id      string `json:"id"`
	YarnID  string `json:"yarnid"`
	QueryID string `json:"queryid"`
}

type DevInfoHis struct {
	Id             string        `json:"id"`       //实验的唯一标识符
	Jobid          string        `json:"jobid"`    //自身上线的jobid
	Dags           []GAGIds      `json:"dags"`     //所属工作流
	Dirid          string        `json:"dirid"`    //文件夹id
	Fatherid       string        `json:"fatherid"` //父级文件夹id
	Notetype       string        `json:"notetype"`
	Notename       string        `json:"notename"`
	Datecreated    string        `json:"datecreated"`
	Updatetime     string        `json:"updatetime"` //更新时间
	Submitted      bool          `json:"submitted"`
	Submitinfo     DevSubmitInfo `json:"submitinfo"` //保存上线版本的实验信息
	User           string        `json:"user"`
	Username       string        `json:"username"`
	Sqlid          string        `json:"sqlid"`
	Actualnotetype string        `json:"actualnotetype"`
	Udfcmd         string        `json:"udfcmd"`
	Content        string        `json:"content"`
	Tags           []Tag         `json:"tags"`         //标签
	Initdb         string        `json:"initdb"`       //真实的库名
	SourceDBName   string        `json:"sourcedbname"` //sourcedb 名称，库名或 schema 名
	Description    string        `json:"description"`
	Finalcmd       string        `json:"finalcmd"`  //试运行时的运行命令
	ProjectID      string        `json:"projectid"` //所属的项目id
	TaskID         string        `json:"taskid"`
	SubID          int           `json:"subid"`   //工作流内的子任务ID
	LayerID        string        `json:"layerid"` //分层ID
	Module         int           `json:"module,omitempty"`
	EngineID       string        `json:"engineid"`
	EngineName     string        `json:"enginename"`
	Name           string        `json:"name"`
	Version        int           `json:"version"`      //版本号
	GenerateTime   string        `json:"generatetime"` //生成时间
	DevID          string        `json:"devid"`
	MemoryLimit    int           `json:"memorylimit"`
}
