// Copyright (c) Datatom Software, Inc.(2018)
//
// Author: 王键（<EMAIL>）
//
// Creating Time: 2018-3-20
package source

import (
	"encoding/xml"
)

const (
	MaxSize                    int = 10000
	RESOURCE_TYPE_KETTLE_JOB       = "kettle-job"
	RESOURCE_TYPE_KETTLE_TRANS     = "kettle-trans"
	RESOURCE_TYPE_DATAX            = "datax"
	RESOURCE_TYPE_API              = "api"
)

// 新增：向/new/dev接口创建索引表
type NewDevInfo struct {
	Id          string        `json:"id"`    //实验的唯一标识符
	Jobid       string        `json:"jobid"` //自身上线的jobid
	Dags        []GAGIds      `json:"dags"`  //所属工作流
	Dirid       string        `json:"dirid"`
	Notetype    string        `json:"notetype"`
	Notename    string        `json:"notename"`
	Datecreated string        `json:"datecreated"`
	Updatetime  string        `json:"updatetime"` //更新时间
	Submitted   bool          `json:"submitted"`
	Submitinfo  DevSubmitInfo `json:"submitinfo"` //保存上线版本的实验信息

	User           string   `json:"user"`
	Username       string   `json:"username"`
	Sqlid          string   `json:"sqlid"`
	Actualnotetype string   `json:"actualnotetype"`
	Udfcmd         string   `json:"udfcmd"`
	Content        string   `json:"content"`
	Variables      []string `json:"variables"` //参数列表
	Delif          bool     `json:"delif"`     //如果是从工作流中临时创建的，当为未上线时予以删除

	Tags    []Tag  `json:"tags"`    //标签
	Trash   bool   `json:"trash"`   //是否在垃圾桶
	Star    bool   `json:"star"`    //星标任务
	Toptime string `json:"toptime"` //置顶时间
	Initdb  string `json:"initdb"`

	Pid         int      `json:"pid"`
	Deletetime  string   `json:"deletetime"`
	Description string   `json:"description"`
	Concat      []string `json:"concat"`   //拼加tag的字段
	Finalcmd    string   `json:"finalcmd"` //试运行时的运行命令
}
type AccInfo struct {
	Id       string   `json:"id"`    //实验的唯一标识符
	Jobid    string   `json:"jobid"` //自身上线的jobid
	Jobids   []GAGIds `json:"dags"`  //所属工作流
	Ktrs     []string `json:"ktrs"`  //相关ktrs的名字，针对kettle-jobs
	Kjbs     []string `json:"kjbs"`  //保存被哪些kjb使用
	Dirid    string   `json:"dirid"` //目录id
	Dirname  string   `json:"dirname"`
	Notetype string   `json:"notetype"` //文件类型

	Tags        []Tag             `json:"tags"` //标签
	Concat      []string          `json:"concat"`
	Notename    string            `json:"notename"`    //脚本名
	Cmd         string            `json:"cmd"`         //datax运行cmd
	Datecreated string            `json:"datecreated"` //脚本创建时间
	Updatetime  string            `json:"updatetime"`  //更新时间
	Submitted   bool              `json:"submitted"`   //是否上线
	Submitinfo  AccSubmitInfo     `json:"submitinfo"`  //保存上线版本的实验信息
	User        string            `json:"user"`        //用户
	Username    string            `json:"username"`    //用户名
	Content     string            `json:"content"`     //内容
	Contents    map[string]string `json:"contents"`    //批量采集内容（5.0）
	Variables   []string          `json:"variables"`   //参数列表
	//	parameters null
	Delif       bool   `json:"delif"`       //如果是从工作流中临时创建的，当为未上线时予以删除
	Description string `json:"description"` //描述
	//数据库相关
	Extractdb      AccExtractDb   `json:"extractdb"`                //抽取源信息
	Sourcedb       AccSourceDb    `json:"sourcedb"`                 //存储源信息
	AdvancedConfig AdvancedConfig `json:"advancedconfig,omitempty"` // 采集任务高级配置
	//新增策略
	Prefix     string `json:"prefix"`     //前缀
	Suffix     string `json:"suffix"`     //后缀
	Policytype int    `json:"policytype"` //策略规则(0.使用源表名 1.表存在则加前缀或后缀 2.所有表名加前缀或后缀)

	// Realtime  bool         `json:"realtime"`  //是否实时
	// Batchtesk bool         `json:"batchtesk"` //是否批量
	Tasktype  int    `json:"tasktype"` //任务类型（0.自定义 1.单例离线 2.单例实时 3.批量离线 4.实时抽取 5.专题表落地 6.库表推送,10.消息流库表推送）
	TransForm int    `json:"transform"`
	Trash     bool   `json:"trash"`
	Udfcmd    string `json:"udfcmd"`   //dev的cmd
	Finalcmd  string `json:"finalcmd"` //试运行时的运行命令

	LoginUser     string `json:"loginuser"`     //登录节点用户名
	LoginPassword string `json:"loginpassword"` //登录节点密码
	//csv相关参数
	UploadForm      int    `json:"uploadform"`      //上传形式:1 服务器上传 2 cayman上传
	Enconding       string `json:"enconding"`       //编码格式
	Delimiter       string `json:"delimiter"`       //分隔符
	CsvPaths        string `json:"csvpaths"`        //csv文件的bucket路径或服务器路径
	CaymanPath      string `json:"caymanpath"`      //临时路径 (此处起到一个中转的功能)
	DataDescription string `json:"datadescription"` //数据元描述
	CsvFileName     string `json:"csvfilename"`     //csv文件名
	CsvNum          []int  `json:"csvnum"`
	Extra_type      string `json:"extra_type"` //抽取源类型
	TDCLoad         string `json:"tdcload"`    // tdc抽取类型，落地抽取

	Retrynum      int    `json:"retrynum"`      //重试次数
	IsRetry       bool   `json:"isretry"`       //是否重试：true：重试，false：不重试
	RetryInterval int    `json:"retryinterval"` //重试间隔时长，单位为分钟
	IsBan         bool   `json:"isban"`         //重试失败后是否禁用：true：禁用，false：不禁用
	ProjectID     string `json:"projectid"`     //所属的项目id
	//实时抽取相关参数
	RealInfo RealTransInfo `json:"realinfo"`
	// SourceRealContent string        `json:"producebody"` //生产者参数
	// SinkRealContent   string        `json:"consumebody"` //消费者参数
	// Producename       string        `json:"producename"` //生产者名称
	// Consumename       string        `json:"consumename"` //消费者名称
	// Offline           bool          `json:"offline"`     //是否先离线，然后再实时

	// ProjectID  string `json:"projectid"`  //所属的项目id
	// SubmitTime string `json:"submittime"` // 实时任务的上线时间
	// Offlinepid string `json:"offlinepid"`
	RealNum        bool           `json:"realnum"`
	TableRelation  TableRelation  `json:"tablerelation"`  //字段映射：由前端提供映射规则
	Accincrestatus AccIncreStatus `json:"accincrestatus"` //增量数据策略详情
	MaxTime        string         `json:"maxtime"`        //同tb_job表中的maxtime

	//4.8.0 添加
	Version    int    `json:"version"`    //数据版本
	WorkflowID string `json:"workflowid"` //工作流ID
	TemplateId string `json:"templateid"`

	IsBatchTask      bool                  `json:"isbatchtask"`                // 批量采集增强任务标识,true则是
	Customize        []CustomizeTable      `json:"customize,omitempty"`        // 任务个性化设置，以表信息为基本单位的数组
	GlobalExtractdb  GlobalExtractdb       `json:"globalextractdb,omitempty"`  // 全局数据源信息
	GlobalSourcedb   GlobalSourcedb        `json:"globalsourcedb,omitempty"`   // 全局存储源信息
	GlobalSubmitInfo GlobalSubmitInfo      `json:"globalsubmitinfo,omitempty"` // 全局任务配置信息
	CmdContent       map[string]CmdContent `json:"cmdcontent,omitempty"`       // 批量增强表命令及命令详情
	//5.3新增
	TaskStatus        int        `json:"taskstatus"`                  // 批量采集的子任务以及总任务的创建状态 ：1 成功 2 失败 0 创建中
	FatherId          string     `json:"fatherid"`                    //子任务所属的总任务的id，仅子任务此参数有值
	SubTaskName       string     `json:"subtaskname"`                 //子任务名称
	ErrLog            string     `json:"errlog"`                      //子任务的错误日志
	AnalyzeSql        string     `json:"analyzeSql"`                  // 单例采集的analyzeSql
	SubTaskCmdContent CmdContent `json:"subtaskcmdcontent,omitempty"` // 子任务表命令及命令详情
	FlinkRecord       bool       `json:"flinkrecord"`
	IsPoint           bool       `json:"ispoint"`
	PointValue        string     `json:"pointvalue"`
	Globalvars        GlobalVars `json:"globalvars,omitempty"` //全局参数设置
	Advanced          Advanced   `json:"advanced"`
	NewBatchTask      bool       `json:"newbatchtask"`

	//580依赖任务的周期类型
	SchTypeStr           string        `json:"schtypestr"`           //依赖任务的周期类型：年，月，周，日，时，分
	MessageSourcedb      AccSourceDb   `json:"messagesourcedb"`      //消息流存储源信息
	MessageTableRelation TableRelation `json:"messagetablerelation"` //消息流字段映射：由前端提供映射规则
	SourcedbMethod       int           `json:"sourcedbmethod"`       //0代表普通库表存储源、 1代表消息流存储源、2代表两者兼有

}

// 实时返回总任务下的子任务信息
type TaskAndSubInfo struct {
	TaskID      string       `json:"taskid"`      //总任务ID
	SubInfo     []SubAccInfo `json:"subinfo"`     //子任务信息
	SubSuccess  int          `json:"subsuccess"`  //子任务成功数
	SubFail     int          `json:"subfail"`     //子任务失败数
	SubCreating int          `json:"subcreating"` //子任务创建中个数
	TaskStatus  int          `json:"taskstatus"`  //总任务的创建状态 ：1 成功 2 失败 0 创建中
	AnalyzeSql  string       `json:"analyzeSql"`  // 单例采集的analyzeSql
	JobID       string       `json:"jobid"`       //依赖任务获取子任务信息需要
	CronDetail  string       `json:"crondetail"`  //依赖任务获取子任务信息需要
}

// DSV5.3 子任务信息
type SubAccInfo struct {
	SubTaskName           string `json:"subtaskname"`           //子任务名称
	ExtractTbName         string `json:"extracttbname"`         //源表名
	SourceTbName          string `json:"souretbname"`           //目标表名
	ExtractOnlineStrategy int    `json:"extractonlinestrategy"` //单表数据策略,0：覆盖存储数据，1：全量追加数据，2：增量追加数据
	AcquisitionMode       string `json:"acquisitionmode"`       //采集方式
	DataStrategy          string `json:"datastrategy"`          //数据策略
	Status                int    `json:"status"`                //创建结果：子任务以及总任务的创建状态 ：1 成功 2 失败 0 创建中
	SubTaskId             string `json:"subtaskid"`             //子任务id
}

type RealTransInfo struct {
	SourceRealContent     string `json:"producebody"`     //生产者参数
	SinkRealContent       string `json:"consumebody"`     //消费者参数
	EditSourceRealContent string `json:"editproducebody"` //编辑生产者参数
	EditSinkRealContent   string `json:"editconsumebody"` //编辑消费者参数
	Producename           string `json:"producename"`     //生产者名称
	Consumename           string `json:"consumename"`     //消费者名称
	Topic                 string `json:"topic"`           //Kafka主题
	SubmitTime            string `json:"submittime"`      // 实时任务的上线时间

	Offlinepid        string `json:"offlinepid"`        //离线任务的进程号
	Offline           bool   `json:"offline"`           //是否先离线，然后再实时
	OfflineTaskLog    string `json:"offlinetasklog"`    //离线任务的日志路径
	OfflineTaskStatus string `json:"offlinetaskstatus"` //离线任务的状态
	TableID           string `json:"tableid"`

	RealResults   ResultInfo `json:"realresults"`   //实时运维运行结果信息
	OfflineRecord bool       `json:"offlinerecord"` //上线记录
}

type AccId struct {
	Id string `json:"id"`
}

// 解析xml
type Kjb struct {
	XMLName xml.Name `xml:"job"`
	KjbJob  Entries  `xml:"entries"`
}

type Entries struct {
	XMLName xml.Name `xml:"entries"`
	Entrs   []Entry  `xml:"entry"`
}

type Entry struct {
	XMLName  xml.Name `xml:"entry"`
	FileName string   `xml:"filename"`
	Ktype    string   `xml:"type"`
}

type AdvancedConfig struct {
	RAM          int       `json:"ram"`                   // 运行内存，单位：/G
	KBPS         int       `json:"kbps"`                  // 速率上限，单位：/byte,页面默认为M/s,所以需转换后使用，即kbps*1024*1024
	SplitKey     string    `json:"splitkey"`              // 切分键
	Record       int       `json:"record"`                // 最大记录数
	Concurrency  int       `json:"concurrency,omitempty"` // 并发数
	ErrorInfo    ErrorInfo `json:"errorinfo"`             // 容错
	IsRecordErr  bool      `json:"isrecorderr"`           // 是否记录错误记录，默认记录，true表示记录
	Setting      string    `json:"setting,omitempty"`     // datax job setting高级配置项
	Core         string    `json:"core,omitempty"`        // datax transport高级配置项
	Prioritymode int       `json:"prioritymode"`          //1代表容错率优先    2代表速率优先
	TimeOut      int       `json:"timeout"`               //超时时间
	ErrorCount   int       `json:"errorcount"`            //后端计数用

}

type ErrorInfo struct {
	ErrLogCount int `json:"errlogcount"` // 错误记录数，超出则任务自动结束
	ErrLogPer   int `json:"errlogper"`   // 错误记录占比，超出则任务自动结束
}

// ********实验相关结构体**********
type NewAcc struct {
	Name        string            `json:"name"`        //新建脚本名
	Dirid       string            `json:"dirid"`       //新建目录id
	Dirname     string            `json:"dirname"`     //新建目录名
	DirFatherID string            `json:"dirfatherid"` //新建目录的上级文件夹id
	Notetype    string            `json:"notetype"`    //文件类型
	Content     string            `json:"content"`     //内容
	Contents    map[string]string `json:"contents"`    //批量采集内容（5.0）
	Cmd         string            `json:"cmd"`         //datax脚本运行cmd
	User        string            `json:"user"`        //用户id
	UserName    string            `json:"username"`    // 用户名
	Tags        []Tag             `json:"tags"`        //标签
	Concat      []string          `json:"concat"`
	Variables   []string          `json:"variables"`   //参数列表
	Delif       bool              `json:"delif"`       //是否从工作流临时创建
	Description string            `json:"description"` //描述
	//数据库相关
	Extractdb      AccExtractDb   `json:"extractdb"` //抽取源信息
	Sourcedb       AccSourceDb    `json:"sourcedb"`  //存储源信息
	DBVersion      string         `json:"dbversion"`
	AdvancedConfig AdvancedConfig `json:"advancedconfig,omitempty"` // 采集任务高级配置
	Realtime       bool           `json:"realtime"`                 //是否实时
	Tasktype       int            `json:"tasktype"`                 //任务类型（1.单例离线 2.单例实时 3.批量离线）4代表库表实时、 8代表实时接口推送、 7代表流式数据治理 15API采集  10库表服务消息流推送
	Id             string         `json:"id,omitempty"`             //更新时使用
	Cover          bool           `json:"cover"`                    //是否覆盖
	CsvFileName    string         `json:"csvfilename"`              //csv文件名
	CsvNum         []int          `json:"csvnum"`                   //csv字段的列号
	//新增策略
	Prefix     string `json:"prefix"`     //前缀
	Suffix     string `json:"suffix"`     //后缀
	Policytype int    `json:"policytype"` //策略规则(0.使用源表名 1.表存在则加前缀或后缀 2.所有表名加前缀或后缀)

	LoginUser     string `json:"loginuser"`     //登录节点用户名
	LoginPassword string `json:"loginpassword"` //登录节点密码
	//csv相关参数
	UploadForm      int    `json:"uploadform"`      //上传形式:1 服务器上传 2 cayman上传
	Enconding       string `json:"enconding"`       //编码格式
	Delimiter       string `json:"delimiter"`       //分隔符
	CsvPaths        string `json:"csvpaths"`        //csv文件的bucket路径或服务器路径
	CaymanPath      string `json:"caymanpath"`      //临时路径 (此处起到一个中转的功能)
	DataDescription string `json:"datadescription"` //数据元描述
	FileType        string `json:"filetype"`        // hive文件类型，目前支持两种orc/text
	TDCLoad         string `json:"tdcload"`         // tdc抽取类型，落地抽取
	TransForm       int    `json:"transform"`

	ErrorTB []string //问题表：抽取报错。当前指:批量源表字段数>存储源字段数

	Retrynum      int           `json:"retrynum"`      //重试次数
	IsRetry       bool          `json:"isretry"`       //是否重试：true：重试，false：不重试
	RetryInterval int           `json:"retryinterval"` //重试间隔时长，单位为分钟
	IsBan         bool          `json:"isban"`         //重试失败后是否禁用：true：禁用，false：不禁用
	Offline       bool          `json:"offline"`       //实时抽取时是否先进行离线抽取
	Pkfield       []string      `json:"pkfield"`       //主键
	ProjectID     string        `json:"projectid"`     //所属的项目id
	TableID       string        `json:"tableid"`
	RealInfo      RealTransInfo `json:"realinfo"`
	TableRelation TableRelation `json:"tablerelation"` //字段映射：由前端提供映射规则

	//4.8.0新增
	Version    int    `json:"version"`    //数据版本
	WorkflowID string `json:"workflowid"` //所属工作流ID
	TemplateId string `json:"templateid"`

	IsBatchTask bool             `json:"isbatchtask"` // 批量采集增强任务标识,true则是
	Customize   []CustomizeTable `json:"customize"`   // 任务个性化设置，以表信息为基本单位的数组

	IsCopy bool `json:"iscopy"`
	//5.3新增
	TaskStatus     int               `json:"taskstatus"`     // 子任务以及总任务的创建状态 ：1 成功 2 失败 0 创建中
	SubTbNameAndID map[string]string `json:"subtbnameandid"` //子任务的id和表名对应记录
	// 5.3 ---- > 2.4 自定义全局 提交信息 放置在任务 内部配置
	GlobalSubmitInfo     GlobalSubmitInfo `json:"globalsubmitinfo,omitempty"` // 全局任务配置信息 【自定义添加】
	SavePoint            bool             `json:"savepoint"`
	Globalvars           GlobalVars       `json:"globalvars,omitempty"` //全局参数设置
	Advanced             Advanced         `json:"advanced"`             // 实时任务高级配置
	RetryStatus          bool             `json:"retrystatus"`
	Datastrategy         int              `json:"datastrategy"`
	MessageSourcedb      AccSourceDb      `json:"messagesourcedb"`      //消息流存储源信息
	MessageTableRelation TableRelation    `json:"messagetablerelation"` //消息流字段映射：由前端提供映射规则
	SourcedbMethod       int              `json:"sourcedbmethod"`       //0代表普通库表存储源、 1代表消息流存储源、2代表两者兼有

	//580 流数据治理
	JobID     string   `json:"jobid"`     //对应治理任务 ID
	StreamSql []string `json:"streamsql"` //流处理 sql
	NoteName  string   `json:"notename"`  //任务名

	//580新增
	SchTypeStr string `json:"schtypestr"` //依赖任务的周期类型：年，月，周，日，时，分
	// a、只有单例抽取才会出现此策略
	// b、0：覆盖存储数据，1：全量追加数据，2：增量追加数据
	// c、增加追加数据:只有在有增量条件下，才会显示
	//ExtractOnlineStrategy int            `json:"extractonlinestrategy"`
	Accincrestatus AccIncreStatus `json:"accincrestatus"`
	IsWorkflow     bool           `json:"isworkflow,omitempty"`
}
type Advanced struct {
	Retrynum      int  `json:"retrynum"`      //重试次数
	IsRetry       bool `json:"isretry"`       //是否重试：true：重试，false：不重试
	RetryInterval int  `json:"retryinterval"` //重试间隔时长，单位为分钟
}

type TableRelation struct {
	Nodes   []Nodes   `json:"nodes"`   //字段节点
	Edges   []Edges   `json:"edges"`   //源端与目的端字段对应
	EdgesID []EdgesID `json:"edgesid"` //源端与目的端字段id对应
}
type Edges struct {
	Source string `json:"source"`
	Target string `json:"target"`
}
type EdgesID struct {
	SourceID string `json:"sourceid"`
	TargetID string `json:"targetid"`
}
type Nodes struct {
	ID        string `json:"id"`
	X         int    `json:"x"`         //x轴位置
	Y         int    `json:"y"`         //y轴位置
	FieldName string `json:"fieldname"` //字段名称
	FieldType string `json:"fieldtype"` //字段类型
	Position  string `json:"position"`  //表示左右表的字段位置 值如下：left表示抽取源表、right表示目的端表
}

// -------------------------------
type AccExtractDb struct {
	Id                 string            `json:"id"` //抽取源信息id
	Dbname             string            `json:"dbname"`
	Schema             string            `json:"schema"`
	Schemaid           string            `json:"schemaid"`
	Table              string            `json:"table"`
	Comment            string            `json:"comment"`
	Field              []string          `json:"field"`
	Fieldtype          []string          `json:"fieldtype,omitempty"`
	Fieldcomment       []string          `json:"fieldcomment,omitempty"` //字段注释
	Fieldprim          []int             `json:"fieldprim"`              //0非索引 1：普通索引  2：主键  3：唯一键
	FieldBoth          []bool            `json:"fieldboth"`              //是否既是主键又是唯一键
	Firstrecord        []string          `json:"firstrecord,omitempty"`
	FieldInfo          []RecordFieldInfo `json:"fieldinfo"`          //字段信息(仅记录)
	FieldTypePrecision []int             `json:"fieldtypeprecision"` //oracle number 精度
	FieldTypeScale     []int             `json:"fieldtypescale"`     //oracle number 精度

	Wherecontent     string       `json:"wherecontent,omitempty"`
	Createfield      string       `json:"createfield"`
	Mainkey          []string     `json:"mainkey,omitempty"`
	Timetype         string       `json:"timetype"`              //时间增量类型
	Supportdirtydata bool         `json:"supportdirtydata"`      //支持抽取脏数据
	Batchtables      []string     `json:"batchtables,omitempty"` //批量抽取选表
	SameNameTBs      []SameNameTB `json:"samenametbs,omitempty"` //批量同名策略：0忽略，1覆盖，2追加
	DBType           string       `json:"dbtype"`                // 数据库类型
	ExtractStrategy  int          //同名策略：0忽略，1覆盖，2追加
	HistoryData      bool         `json:"historydata"` // 清空 true 不清空 false [5.0批量]
	ChoseWay         int          //选择策略：1不需要再创建表
	OdsTBname        string       //源表映射至存储源中的表名
	Filtertablenull  bool         `json:"filtertablenull"`      //是否过滤空表
	TablesInfo       []TableInfo  `json:"tablesinfo,omitempty"` //表数据量相关的信息

	//数据服务使用，无需存储
	SqlTxt           string         `json:"sqltxt,omitempty"`   //数据服务生成的 sql
	EngineID         string         `json:"engineid,omitempty"` //数据服务 来源表的引擎 ID
	DistributionMode string         `json:"distributionmode"`   //MPP数据库分布方式
	DistributionBond []string       `json:"distributionbond"`   //MPP数据库分布键
	TableId          string         `json:"tableid"`
	AccIncreStatus   AccIncreStatus `json:"accincrestatus"` // 增量配置

	// ODPS 参数
	OdpsUrl      string `json:"odpsurl,omitempty"`
	TunnelUrl    string `json:"tunnelurl,omitempty"`
	OdpsProject  string `json:"odpsproject,omitempty"`
	FlinkPushUrl string `json:"flinkpushurl"`
	//5.8.0 API
	APiName         string `json:"apiname"` //api名称
	APiID           string `json:"apiid"`
	HttpMethod      string `json:"httpmethod"` //请求方式 "POST"
	Encoding        string `json:"encoding"`   // 编码格式 "UTF-8"
	AuthMethod      string `json:"authmethod"` //认证方式 "no" ,"basic" ,"apikey" ,"token","jwt","oauth"
	AuthBasic       `json:"authbasic"`
	AuthApiKey      `json:"authapikey"`
	AuthBearerToken `json:"authbearertoken"`
	AuthJwt         `json:"authjwt"`
	AuthOauth       `json:"authoauth"`
	HeaderParam     []ServiceParam `json:"headerparam"`
	RequestParam    []ServiceParam `json:"requestparam"`
	ResponseParam   []ServiceParam `json:"responseparam"` //APi ID
	//5.8.0
	IncrementMethod int      `json:"incrementmethod"` // 采集方式,0:全量采集1:增量采集
	IsPage          bool     `json:"ispage"`          //是否分页
	IncrementField  int      `json:"incrementfield"`  //增量字段 0时间类型1自增序列
	TimeFormat      string   `json:"timeformat"`      //时间格式 yymmdd6种
	PageEndRule     int      `json:"pageendrule"`     //分页结束判断规则 0分页返回数据为空 1返回记录总数
	PageEndPath     []string `json:"pageendpath"`     //分页返回数据判断为空的分页字段路径 通过total获取分页页数
	PageEndPathID   []string `json:"pageendpathid"`   //分页返回数据判断为空的分页字段路径ID
	//5.8API
	ParsePath   []string `json:"parsepath"`   // 解析路径
	ParsePathID []string `json:"parsepathid"` // 解析路径
	//5.8.0api 动态参数脚本
	PyParams  []string `json:"pyparams"`  //动态参数数组
	PyContent string   `json:"pycontent"` //脚本
	PyRunRam  int      `json:"pyrunram"`  //运行内存 /G
	PyCode    int      `json:"pycode"`    //缩进
	PyVersion string   `json:"pyversion"` //代码版本
	PyPath    string   `json:"pypath"`    //编辑的时候再传的脚本保留位置
	PyLock    bool     `json:"pylock"`    //是否锁定
}

type CreateSourceUrl struct {
	Ip       string `json:"ip"`       //地址必传
	Port     string `json:"port"`     //端口号
	Dbuser   string `json:"dbuser"`   //用户数据库名字
	Password string `json:"password"` //密码
	//Dbname       string        `json:"dbname"`                 //用户自定义数据库名字
	Dbtype     string `json:"dbtype"`     //数据库类型
	Dbdatabase string `json:"dbdatabase"` //数据库库名
	Schema     string `json:"schema"`
	Dbtable    string `json:"dbtable"` //数据表名称
	//Enginestatus string        `json:"enginestatus"`           //数据库状态
	ScanStartupMode string `json:"scanstartupmode"` //是否离线抽取
	Connector       string `json:"connector"`
	Try_Port        string `json:"try_port"`
	CallBackUrl     string `json:"call_back_url"`
	Format          string `json:"format"`
}
type CreateSinkUrl struct {
	Ip       string `json:"ip"`       //地址必传
	Port     string `json:"port"`     //端口号
	Dbuser   string `json:"dbuser"`   //用户数据库名字
	Password string `json:"password"` //密码
	//Dbname       string        `json:"dbname"`                 //用户自定义数据库名字
	Url        string `json:"url"`
	Dbdatabase string `json:"dbdatabase"` //数据库库名
	Schema     string `json:"schema"`
	Dbtable    string `json:"dbtable"` //数据表名称
	//Enginestatus string        `json:"enginestatus"`           //数据库状态
	ScanStartupMode string `json:"scanstartupmode"` //是否离线抽取
	Connector       string `json:"connector"`
	DirtyIp         string `json:"dirtyip"`
	DirtyPort       string `json:"dirtyport"`
	DirtyUser       string `json:"dirtyuser"`
	DirtyPassword   string `json:"dirtypassword"`
	DirtyDatabase   string `json:"dirtydatabase"`
	DirtySchema     string `json:"dirtyschema"`
	DirtyTable      string `json:"dirtytable"`
}

// 字段信息
type RecordFieldInfo struct {
	FieldName         string `json:"fieldname"`         // 字段名称
	FieldType         string `json:"fieldtype"`         // 字段类型
	FieldComment      string `json:"fieldcomment"`      // 字段注释
	Index             int    `json:"index"`             //0非索引 1：普通索引  2：主键  3:唯一键（唯一键检测在主键前，若同时为唯一键和主键，则已主键为准）
	BothPrimAndUnique bool   `json:"bothprimandunique"` //若同时为主键和唯一键，则为true
	IsPartition       bool   `json:"ispartition"`       //是否分区字段
	RankNum           int    `json:"ranknum"`           //等级数---字段的等级。
	RankName          string `json:"rankname"`          //等级名称---敏感等级
	PartitionNumber   int    `json:"partitionnumber"`   //分区等级（前端计算保存）
	IsPrim            bool   `json:"isprim"`            //是否主键（前端计算）
	IsUnique          bool   `json:"isunique"`          //是否唯一键（前端计算）
	FieldID           string `json:"fieldid"`           //api的字段id
}

// 分区字段（分级）
type RecordPartitionFieldInfo struct {
	Name       string `json:"name"`       //字段名称
	Annotation string `json:"annotation"` // 注释
	FieldType  string `json:"fieldtype"`  // 字段类型
	Ispreset   string `json:"ispreset"`   //预设字段类型 "year"年 "month"月 "day"日 "notpreset"非预设字段
}

type SameNameTB struct {
	TBName string `json:"tbname"`
	Way    int    `json:"way"`
}

type TemplateSet struct {
	FieldName string    `json:"fieldname"` //字段名字
	FieldType string    `json:"fieldtype"` //字段类型
	CheckRule CheckRule `json:"checkrule"` //校验规则
	Required  bool      `json:"required"`  //是否必填
}
type CheckRule struct {
	RuleId         string   `json:"ruleid"`         //规则ID
	InputType      int      `json:"inputtype"`      //输入方式, 输入框、单选框、多选框 1，2，3
	Values         []string `json:"values"`         //选取的值
	TimeFormat     string   `json:"timeformat"`     //时间格式
	Enumvalues     []string `json:"enumvalues"`     //枚举值
	IsProblemDatas []bool   `json:"isproblemdatas"` //校验结果
}

type AccSourceDb struct {
	Odsip         string        `json:"odsip"`
	Odsport       string        `json:"odsport"`
	Odsdatabase   string        `json:"odsdatabase"`
	Odsenginename string        `json:"odsenginename,omitempty"`
	Odsuser       string        `json:"odsuser,omitempty"`
	Odspassword   string        `json:"odspassword,omitempty"`
	OracleServer  string        `json:"oracleserver"`
	Oraclesid     string        `json:"oraclesid"`
	Dbtype        string        `json:"dbtype,omitempty"`
	SchemaID      string        `json:"schemaid"` // 模式id
	Schema        string        `json:"schema,omitempty"`
	SourceTBs     []AccSourceTB `json:"accsourcetb,omitempty"` //1、用于记录对表的处理信息 2、分开库信息与表信息的定义

	Table              string            `json:"table,omitempty"`
	TableComment       string            `json:"tablecomment,omitempty"`
	Field              []string          `json:"field,omitempty"`
	UsedField          []string          `json:"usedfield,omitempty"` //库表推送中被映射的字段
	Wherecontent       string            `json:"wherecontent,omitempty"`
	Fieldtype          []string          `json:"fieldtype,omitempty"`
	Fieldcomment       []string          `json:"fieldcomment,omitempty"` //字段注释
	FieldPrim          []int             `json:"fieldprim,omitempty"`    // 0非索引 1：普通索引  2：主键  3:唯一键（唯一键检测在主键前，若同时为唯一键和主键，则已主键为准）
	FieldBoth          []bool            `json:"fieldboth"`              //是否既是主键又是唯一键
	Firstrecord        []string          `json:"firstrecord,omitempty"`
	FieldInfo          []RecordFieldInfo `json:"fieldinfo"`          //字段信息(仅记录)
	FieldTypePrecision []int             `json:"fieldtypeprecision"` //oracle number 精度
	FieldTypeScale     []int             `json:"fieldtypescale"`     //oracle number 精度

	Engineid  string `json:"engineid,omitempty"` //引擎id
	Layerid   string `json:"layerid,omitempty"`  //分层id
	LayerName string `json:"layername"`          // 分层名称
	Sourceid  string `json:"sourceid"`           // 存储源id
	// Schemaid  string `json:"schemaid"`
	// Schema string `json:"schema"`

	Subject       string       `json:"subject,omitempty"`       //主题
	Odsdatabaseid string       `json:"odsdatabaseid,omitempty"` //ods数据库id
	FileType      string       `json:"filetype"`                // hive文件类型
	NewCreate     bool         `json:"newcreate"`
	CatalogID     string       `json:"catalogid"` // 所属最底层编目id
	CataList      []CataListTb `json:"catalist"`  // 所属编目数组
	TagList       []TagList    `json:"taglist"`   // 标签信息

	//需要初始化的存储引擎,只作用于传参
	HiveVersion     string       `json:"hiveversion"`
	LoginPasswd     string       `json:"loginpasswd"`
	Token           string       `json:"token"`
	TableID         string       `json:"tableid"`
	HiveFilePath    string       `json:"hivefilepath"`    // hive配置文件路径
	HdfsURL         string       `json:"hdfsurl"`         // 表底层存储路径
	HiveInstallPath string       `json:"hiveinstallpath"` // 客户端安装路径
	IsConfigKbs     string       `json:"isconfigkbs"`     //  是否配置 kbs 信息  string --1是 0 否
	KerberosInfo    KerberosInfo `json:"kerberosinfo"`    // kerberos 管理 信息
	JdbcURL         string       `json:"jdbcurl"`         // hive jdbc连接url

	DistributionMode string   `json:"distributionmode"` //MPP数据库分布方式、记录用，实际在SourceTBs内
	DistributionBond []string `json:"distributionbond"` //MPP数据库分布键
	//CompressType     string   `json:"compresstype"`     //压缩方式
	StoreType bool `json:"storetype"` //false代表普通表、true代表拉链表

	PartitionField      []PartitionFieldInfo `json:"partitionfield"`      //分区字段
	PartitionInfoFields []string             `json:"partitioninfofields"` //MG返回分区字段

	// ODPS 参数
	OdpsUrl     string `json:"odpsurl,omitempty"`
	TunnelUrl   string `json:"tunnelurl,omitempty"`
	OdpsProject string `json:"odpsproject,omitempty"`

	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级

	//达梦集群注册方式，前端需要,后端不依照此参数判断，存储加透传给前端使用
	RegisterWay string `json:"registerway,omitempty"` //single 表示单机  colony 表示集群
	IsOpenSSL   bool   `json:"isopenssl"`             //是否开启 ssl

	//6.0 落地模组
	IsCover         bool   `json:"iscover"`        //是否覆盖
	ConditionCover  string `json:"conditioncover"` //iscover为true时，不为空条件覆盖，为空则全量覆盖
	Deletehistory   bool   `json:"deletehistory"`  //是否清空
	TempSourceField `json:"Tempsourcefield"`

	SourceUrl SourceUrl `json:"sourceurl"`
	SinkUrl   SourceUrl `json:"sinkurls"`
	SyncUrl   SourceUrl `json:"syncurls"`
	FlinkReal FlinkReal `json:"flinkreal"` //flink基础信息统计
	RandId    string    `json:"randid"`
	AlterUrl  SourceUrl `json:"alterurls"`
	// 5.8API
	Strategy     int  `json:"strategy"`     // 0覆盖 1追加 API专用
	History      bool `json:"history"`      // 已有表是否清空数据
	AutoComplete bool `json:"autocomplete"` //是否自动补全
}

type TempSourceField struct {
	FieldName    []string `json:"fieldname"`
	FieldType    []string `json:"fieldtype"`
	FieldComment []string `json:"fieldcomment"`
}
type ReportSourceDb struct {
	Odsip         string        `json:"odsip"`
	Odsport       string        `json:"odsport"`
	Odsdatabase   string        `json:"odsdatabase"`
	Odsenginename string        `json:"odsenginename,omitempty"`
	Odsuser       string        `json:"odsuser,omitempty"`
	Odspassword   string        `json:"odspassword,omitempty"`
	Dbtype        string        `json:"dbtype,omitempty"`
	SchemaID      string        `json:"schemaid"` // 模式id
	Schema        string        `json:"schema,omitempty"`
	SourceTBs     []AccSourceTB `json:"accsourcetb,omitempty"` //1、用于记录对表的处理信息 2、分开库信息与表信息的定义

	Table        string            `json:"table,omitempty"`
	TableComment string            `json:"tablecomment,omitempty"`
	Field        []string          `json:"field,omitempty"`
	UsedField    []string          `json:"usedfield,omitempty"` //库表推送中被映射的字段
	Wherecontent string            `json:"wherecontent,omitempty"`
	Fieldtype    []string          `json:"fieldtype,omitempty"`
	Fieldcomment []string          `json:"fieldcomment,omitempty"` //字段注释
	FieldPrim    []int             `json:"fieldprim,omitempty"`    // 0非索引 1：普通索引  2：主键  3:唯一键（唯一键检测在主键前，若同时为唯一键和主键，则已主键为准）
	FieldBoth    []bool            `json:"fieldboth"`              //是否既是主键又是唯一键
	Firstrecord  []string          `json:"firstrecord,omitempty"`
	FieldInfo    []RecordFieldInfo `json:"fieldinfo"` //字段信息(仅记录)

	DataStrategy string `json:"datastrategy"` //数据策略  append/truncate/update

	Engineid  string `json:"engineid,omitempty"` //引擎id
	Layerid   string `json:"layerid,omitempty"`  //分层id
	LayerName string `json:"layername"`          // 分层名称
	Sourceid  string `json:"sourceid"`           // 存储源id
	// Schemaid  string `json:"schemaid"`
	// Schema string `json:"schema"`

	Subject       string       `json:"subject,omitempty"`       //主题
	Odsdatabaseid string       `json:"odsdatabaseid,omitempty"` //ods数据库id
	FileType      string       `json:"filetype"`                // hive文件类型
	NewCreate     bool         `json:"newcreate"`
	CatalogID     string       `json:"catalogid"` // 所属最底层编目id
	CataList      []CataListTb `json:"catalist"`  // 所属编目数组
	TagList       []TagList    `json:"taglist"`   // 标签信息

	//需要初始化的存储引擎,只作用于传参
	HiveVersion     string       `json:"hiveversion"`
	LoginPasswd     string       `json:"loginpasswd"`
	Token           string       `json:"token"`
	TableID         string       `json:"tableid"`
	HiveFilePath    string       `json:"hivefilepath"`    // hive配置文件路径
	HdfsURL         string       `json:"hdfsurl"`         // 表底层存储路径
	HiveInstallPath string       `json:"hiveinstallpath"` // 客户端安装路径
	IsConfigKbs     string       `json:"isconfigkbs"`     //  是否配置 kbs 信息  string --1是 0 否
	KerberosInfo    KerberosInfo `json:"kerberosinfo"`    // kerberos 管理 信息
	JdbcURL         string       `json:"jdbcurl"`         // hive jdbc连接url

	DistributionMode string   `json:"distributionmode"` //MPP数据库分布方式、记录用，实际在SourceTBs内
	DistributionBond []string `json:"distributionbond"` //MPP数据库分布键
	//CompressType     string   `json:"compresstype"`     //压缩方式
	StoreType bool `json:"storetype"` //false代表普通表、true代表拉链表

	PartitionField      []PartitionFieldInfo `json:"partitionfield"`      //分区字段
	PartitionInfoFields []string             `json:"partitioninfofields"` //MG返回分区字段

	// ODPS 参数
	OdpsUrl     string `json:"odpsurl,omitempty"`
	TunnelUrl   string `json:"tunnelurl,omitempty"`
	OdpsProject string `json:"odpsproject,omitempty"`

	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级

	//达梦集群注册方式，前端需要,后端不依照此参数判断，存储加透传给前端使用
	RegisterWay string `json:"registerway,omitempty"` //single 表示单机  colony 表示集群
	IsOpenSSL   bool   `json:"isopenssl"`             //是否开启 ssl

	UpdatePriKey []string `json:"updateprikey"` //填报更新主键
}
type SourceUrl struct {
	Statement       string          `json:"statement"`
	ExecutionConfig ExecutionConfig `json:"executionConfig"`
}
type ExecutionConfig struct {
	TaskName       string `json:"pipeline.name"`
	SavePoint      string `json:"execution.savepoint.path,omitempty"`
	CheckPointMode string `json:"execution.checkpointing.mode,omitempty"`
	UpsertMater    string `json:"table.exec.sink.upsert-materialize,omitempty"`
	Parallelism    string `json:"'parallelism.default"`
}

// PartitionFieldInfo 分区字段信息
type PartitionFieldInfo struct {
	Name          string        `json:"name"`          //字段名称
	Annotation    string        `json:"annotation"`    // 注释
	FieldType     string        `json:"fieldtype"`     // 字段类型
	Ispreset      string        `json:"ispreset"`      //预设字段类型 "year"年 "month"月 "day"日 "notpreset"非预设字段
	CustomizeRule CustomizeRule `json:"customizerule"` //自定义规则
}
type CustomizeRule struct {
	ExtractFieldName string `json:"fieldname"` //来源字段
	FormatSql        string `json:"formatsql"` //格式转换sql

}
type KerberosInfo struct {
	Id   string `json:"id"`
	Name string `json:"name"`

	KeyTabPath   string `json:"keytabpath"`   // 4.7 kbs 管理
	Krb5ConfPath string `json:"krb5confpath"` // 4.7 kbs 管理
	Principal    string `json:"principal"`    // 4.7 kbs 管理
	Description  string `json:"description"`  // 4.7 驱动描述。
	IsUsed       bool   `json:"isused"`       //是否使用
	CreateTime   string `json:"createtime,omitempty"`
	UpdateTime   string `json:"updatetime,omitempty"` //

	IsDefault bool `json:"isdefault"` // 是否默认

}
type MetadataTbInfo1 struct {
	Hits1 MetadataTbInfo2 `json:"hits"`
}

type MetadataTbInfo2 struct {
	Hits2 []MetadataTbInfo3 `json:"hits"`
	Total int               `json:"total"`
}
type MetadataTbInfo3 struct {
	MetadataTbInfo MetadataTbInfo `json:"_source"`
}

// 从metadata获取表信息
type MetadataTbInfo struct {
	DistributionMode string   `json:"distributionmode"` //MPP数据库分布方式
	DistributionBond []string `json:"distributionbond"` //MPP数据库分布键
	CompressType     string   `json:"compresstype"`     //压缩方式
}

// CataListTb 表所属编目信息
type CataListTb struct {
	ID         string `json:"id"`         // 所属各层编目id
	Name       string `json:"name"`       // 所属各层目录名称
	ParentID   string `json:"parentid"`   // 上一级编目id
	ParentName string `json:"parentname"` // 上一级编目名称
	IsFourth   bool   `json:"isfourth"`   // 是否是第四级编目
	IsLower    bool   `json:"islower"`    // 是否最底层编目
}

// TagList 表标签基本信息
type TagList struct {
	DirID string `json:"dirid"` // 文件夹id
	ID    string `json:"id"`    // 标签id
	Name  string `json:"name"`  // 标签名称
	Color string `json:"color"` // 标签颜色
}

type AccSourceTB struct {
	Table           string   `json:"table,omitempty"`
	Comment         string   `json:"comment"`
	FileType        string   `json:"filetype,omitempty"`
	ErrTB           int      `json:"errTB,omitempty"`           //非0为问题表 1：源库字段数>存储源字段数
	ExtractStrategy int      `json:"extractstrategy,omitempty"` //同名策略：0忽略，1覆盖，2追加
	Field           []string `json:"field,omitempty"`
	WhereContent    string   `json:"wherecontent,omitempty"`
	FieldType       []string `json:"fieldtype,omitempty"`
	FieldComment    []string `json:"fieldcomment,omitempty"` //字段注释
	FieldPrim       []int    `json:"fieldprim,omitempty"`    // 0非索引 1：普通索引  2：主键  3:唯一键（唯一键检测在主键前，若同时为唯一键和主键，则已主键为准）
	DataxJson       string   `json:"dataxjson"`              //datax的json体
	DataxCMD        string   `json:"dataxcmd"`
	CreSQL          string   `json:"cresql"`
	//暂未用
	Status int    `json:"status"` //抽取情况 暂未用
	TaskID string `json:"taskid"` //存储表对应子任务id 未做

	DistributionMode string   `json:"distributionmode"` //MPP数据库分布方式
	DistributionBond []string `json:"distributionbond"` //MPP数据库分布键
}

//=============================

type AccList struct {
	User     string `json:"user"`     //用户
	Notetype string `json:"notetype"` //文件类型
	Dirname  string `json:"dirname"`  //目录名
	Page     int    `json:"page"`     //第几页
	Perpage  int    `json:"perpage"`  //每页个数
	Listall  bool   `json:"listall"`  //是否列举所有用户的所有脚本
}
type AccListDir struct {
	User    string `json:"user"`    //用户
	Listall bool   `json:"listall"` //是否列举所有用户的所有目录
}

type AccListTags struct {
	User    string `json:"user"`
	Listall bool   `json:"listall"`
	Size    int    `json:"size"`
}
type AccDirList struct {
	User    string `json:"user"`    //用户
	Listall bool   `json:"listall"` //是否列举所有用户某个目录下的所有脚本
	Dirid   string `json:"dirid"`   //目录id
}
type AccRun struct {
	Id      string `json:"id"`      //运行脚本id
	Content string `json:"content"` //运行脚本内容
}
type AccStop struct {
	Pid int `json:"pid"` //停止运行脚本的pid
}
type AccResult struct {
	Id  string `json:"id"`  //需获取结果的脚本id
	Pid int    `json:"pid"` //停止运行脚本的pid
}
type AccSave struct {
	Id      string `json:"id"`      //接入脚本id
	Name    string `json:"name"`    // 自定义任务名称
	Content string `json:"content"` //脚本内容
	DirID   string `json:"dirid"`
	DirName string `json:"dirname"` //文件夹名称
	Tags    []Tag  `json:"tags"`
}
type AccContent struct {
	Id string `json:"id"` //接入脚本id
}

type AccSearch struct {
	ID              string `json:"id"`
	User            string `json:"user"`            //用户
	Dirid           string `json:"dirid"`           //目录id
	Datestart       string `json:"datestart"`       //搜索该时间以后创建的脚本
	Dateend         string `json:"dateend"`         //搜索该时间之前创建的脚本
	Submitted       string `json:"submitted"`       //脚本是否上线(填字符串类型的true或false)
	SubmittedStatus []int  `json:"submittedstatus"` //DS5.3 上线状态多选 1上线 2下线
	Tags            []Tag  `json:"tags"`            //脚本标签（可通过标签搜索；也可在match中进行标签的模糊匹配）
	Description     string `json:"description"`     //描述
	Match           string `json:"match"`           //进行文件名，标签，描述模糊匹配
	MatchField      string `json:"matchfield"`      //用户输入查询字段
	Notetype        string `json:"notetype"`        //文件类型（datax或kettle-trans、kettle-job，支持模糊查询）
	Notename        string `json:"notename"`
	Page            int    `json:"page"`               //第几页
	Perpage         int    `json:"perpage"`            //每页的个数
	Listall         bool   `json:"listall"`            //是否搜索所有用户
	Tasktype        int    `json:"tasktype"`           //0自定义，1单列/落地,3批量
	TaskTypes       []int  `json:"tasktypes"`          //
	TransForm       bool   `json:"transform"`          //true 屏蔽转换而来的自定义采集
	TDCLoad         string `json:"tdcload"`            // tdc抽取类型，落地抽取 后期得改
	Sorttype        int    `json:"sorttype,omitempty"` // 排序字段：1--updatetime ，2 --tasktype
	Sort            int    `json:"sort"`               // 0升序，1降序（4.6新增）默认降序
	ProjectID       string `json:"projectid"`          //所属的项目id
	ReportSort      struct {
		SortField string `json:"sortfield"`
		SortType  string `json:"sorttype"` // 0升序，1降序（4.6新增）默认降序
	} `json:"reportsort,omitempty"` //排序
	ReportID    string `json:"reportid"`
	OnlyPublish bool   `json:"onlypublish"`
	IsPortal    bool   `json:"isportal"` //是否数据门户使用
	Filter      struct {
		FilterField string   `json:"filterfield"` //筛选字段
		FilterValue []string `json:"filtervalue"` //筛选值
	} `json:"filter"`
	RealMessageType int `json:"realmessagetype"`
}

type AccIds struct {
	Ids []string `json:"ids"` //下线脚本id数组
}

type AccReplace struct {
	Id       string `json:"id"`
	Name     string `json:"name"`
	Notetype string `json:"notetype"`
	Content  string `json:"content"`
}

//********工作流相关**********

type AccFlowIds struct {
	Ids []string `json:"ids"` //脚本id数组
}

type AccRename struct {
	Id   string `json:"id"`   //脚本id
	Name string `json:"name"` //脚本名
}
type AccDelete struct {
	Ids             []string `json:"ids"`       //批量删除脚本id数组
	RepordIds       []string `json:"reportids"` //批量删除脚本id数组
	MetricsIDS      []string `json:"zbids"`
	ZbTypes         []string `json:"zbtypes"`
	Names           []string `json:"names"`
	Force           bool     `json:"force"`
	RealMessageType int      `json:"realmessagetype"`
}
type AccSubmitInfo struct {
	Submitpath     string `json:"submitpath"`          //上线路径
	Submitcmd      string `json:"submitcmd"`           //上线cmd
	Name           string `json:"name"`                //任务名
	Tags           []Tag  `json:"tags"`                //脚本标签
	Description    string `json:"description"`         //描述
	ExectTimes     *int64 `json:"exectimes,omitempty"` //执行次数
	Scheduletype   int    `json:"scheduletype"`        //时间策略类型
	Scheduledetail string `json:"scheduledetail"`      //时间策略详情
	//	Env            []string `json:"env"`
	AssignNode string   `json:"assignnode"` //运行节点ip
	Preid      []string `json:"preid"`      //前置任务id
	// a、只有单例抽取才会出现此策略
	// b、0：覆盖存储数据，1：全量追加数据，2：增量追加数据
	// c、增加追加数据:只有在有增量条件下，才会显示
	ExtractOnlineStrategy int            `json:"extractonlinestrategy"`
	Accincrestatus        AccIncreStatus `json:"accincrestatus"`
	// 5.0新增
	IsStartNow     bool   `json:"isstartnow"`     //是否立即开始，true：立即执行，false：手动开始
	FirstStartTime string `json:"firststarttime"` //固定类型开始时间
}

type AccSubmit struct {
	Tags           []Tag        `json:"tags"`                 //脚本标签
	Concat         []string     `json:"concat"`               //标签拼接
	Ids            []string     `json:"ids"`                  //实验唯一标识符数组
	Name           string       `json:"name"`                 //任务名
	ExectTimes     *int64       `json:"exectimes,omitempty"`  //执行次数，未传参数时，默认为-1(执行无限次)
	Scheduletype   int          `json:"scheduletype"`         //时间策略类型
	Scheduledetail string       `json:"scheduledetail"`       //时间策略详情
	AssignNode     string       `json:"assignnode"`           //运行节点ip
	Preid          []string     `json:"preid"`                //前置任务id
	Istimeout      bool         `json:"istimeout"`            //是否设置超时
	Settimeout     *Timeoutconf `json:"settimeout,omitempty"` //超时配置

	//AdvancedConfig AdvancedConfig `json:"advancedconfig,omitempty"` // 采集任务高级配置

	// a、只有单例抽取才会出现此策略
	// b、0：覆盖存储数据，1：全量追加数据，2：增量追加数据
	// c、增加追加数据:只有在有增量条件下，才会显示
	ExtractOnlineStrategy int            `json:"extractonlinestrategy"`
	Accincrestatus        AccIncreStatus `json:"accincrestatus"`

	IsStartNow     bool   `json:"isstartnow"`     //是否立即开始，true：立即执行，false：手动开始
	FirstStartTime string `json:"firststarttime"` //固定类型开始时间
	PushID         string `json:"pushid"`         //库表推送id
}

type Timeoutconf struct {
	Isstop  bool   `json:"isstop"`  //是否中断
	Timeout string `json:"timeout"` //超时时间 s/m/h/d
	Email   string `json:"email"`   //通知邮箱
}

type AccFlowJobid struct {
	Ids   []string `json:"ids"`   //脚本id数组
	Jobid string   `json:"jobid"` //工作流id
}

type AccFlowAdd struct {
	Ids   []string `json:"ids"`   //脚本id数组
	Jobid string   `json:"jobid"` //工作流id
	Name  string   `json:"name"`  //工作流名称
}

// *********解析实验相关**********
// AccHits1  query解析第一层
type AccHits1 struct {
	Hits1 AccHits2 `json:"hits"`
}

// AccHits2  query解析第二层
type AccHits2 struct {
	Hits2 []AccSource `json:"hits"`
	Total int         `json:"total"`
}

// AccSource query解析第三层
type AccSource struct {
	Acclab AccInfo `json:"_source"`
}

type AccHit struct {
	Res AccInfo `json:"_source"`
}

type AccIp struct {
	Ip string `json:"ip"`
}

type AccIpHits1 struct {
	Hits1 AccIpHits2 `json:"hits"`
}

// AccHits2  query解析第二层
type AccIpHits2 struct {
	Total int `json:"total"`
}

// 数据库相关
type AccDatabase struct {
	Id           string        `json:"id"`                     //数据库唯一id，新建时后端生成
	Ip           string        `json:"ip"`                     //地址必传
	Port         string        `json:"port"`                   //端口号
	Dbuser       string        `json:"dbuser"`                 //用户数据库名字
	Password     string        `json:"password"`               //密码
	Dbname       string        `json:"dbname"`                 //用户自定义数据库名字
	Dbtype       string        `json:"dbtype"`                 //数据库类型
	Dbdatabase   string        `json:"dbdatabase"`             //数据库库名
	Dbtable      string        `json:"dbtable"`                //数据表名称
	Enginestatus string        `json:"enginestatus"`           //数据库状态
	Describe     string        `json:"describe"`               //数据源描述
	Realtime     bool          `json:"realtime"`               //是否支持实时抽取
	Datecreated  string        `json:"datecreated"`            //创建时间自动生成
	Updatetime   string        `json:"updatetime"`             //更新时间
	Sorttype     string        `json:"sorttype,omitempty"`     //排序类型
	Used         bool          `json:"used"`                   //是否存在抽取任务中，在不能删除
	Realtiontype string        `json:"realtiontype"`           //关系型，mpp，nosql
	Match        string        `json:"match"`                  //模糊匹配字段
	Oraclesid    string        `json:"oraclesid,omitempty"`    //oracle的sid
	Oracleserver string        `json:"oracleserver,omitempty"` //oracle的服务名
	Checklog     []AccCheckLog `json:"checklog"`               //引擎状态记录日志

	//Newadd bool `json:"newadd"` //是否新增配置文件
	Extraname string `json:"extraname"` //抽取进程名称
	Transname string `json:"transname"` //传递进程名称
	Copyname  string `json:"copyname"`  //复制进程名称
	Dataname  string `json:"dataname"`  //传入数据名称
	Defile    string `json:"defile"`    //表定义文件对应名称
	Envcon    bool   `json:"envcon"`    //环境变量是否配置
	Flag      bool   `json:"flag"`      //判断数据库是否进行过实时流抽取

	Schema  string `json:"schema,omitempty"`
	Install bool   `json:"install"` //ogg是否安装

	LoginUser     string `json:"loginuser"`     //登录节点用户名
	LoginPassword string `json:"loginpassword"` //登录节点密码
	//csv相关参数
	UploadForm      int        `json:"uploadform"`      //上传形式:1 服务器上传 2 cayman上传
	Enconding       string     `json:"enconding"`       //编码格式
	Delimiter       string     `json:"delimiter"`       //分隔符
	CsvPaths        string     `json:"csvpaths"`        //csv文件的bucket路径或服务器路径
	CaymanPath      string     `json:"caymanpath"`      //临时路径 (此处起到一个中转的功能)
	DataDescription string     `json:"datadescription"` //数据元描述
	CsvFileName     string     `json:"csvfilename"`     //csv文件名
	Num             []string   `json:"num"`
	User            string     `json:"user"`     //用户id
	Username        string     `json:"username"` //用户名
	Folderid        string     `json:"folderid"`
	NormalConn      NormalConn `json:"normalconn"`
}
type NormalConn struct {
	DBIP         string `json:"dbip,omitempty"`
	DBPort       string `json:"dbport,omitempty"`
	DBUser       string `json:"dbuser,omitempty"`
	DBType       string `json:"dbtype,omitempty"`
	DBPassword   string `json:"dbpassword,omitempty"`
	Database     string `json:"database,omitempty"` //连接库
	Oraclesid    string `json:"oraclesid"`          //oracle的sid
	OracleServer string `json:"oracleserver"`       //oracle的服务名
}

// ---------------------------------------------------------
type AccCheckLog struct {
	Checktime    string `json:"timerecord"`
	Checkstatus  string `json:"checkstatus"`
	Checkmessage string `json:"checkmessage"`
}

// -----------------------------------------------------
// AccHits1  query解析第一层
type AccDbHits1 struct {
	Hits1 AccDbHits2 `json:"hits"`
}

// AccHits2  query解析第二层
type AccDbHits2 struct {
	Hits2 []AccDbSource `json:"hits"`
	Total int           `json:"total"`
}

// AccSource query解析第三层
type AccDbSource struct {
	AccDb AccDatabase `json:"_source"`
}
type AccDbHit struct {
	Res AccDatabase `json:"_source"`
}

// 数据库表的查找
type AccDbTable struct {
	Id           string      `json:"id,omitempty"` //数据源配置的id
	Odsdatabase  string      `json:"odsdatabase,omitempty"`
	Odsip        string      `json:"odsip,omitempty"`
	Odsport      string      `json:"odsport,omitempty"`
	Odsuser      string      `json:"odsuser,omitempty"`
	Odspassword  string      `json:"odspassword,omitempty"`
	Dbtype       string      `json:"dbtype,omitempty"`
	Dbdatabase   []string    `json:"dbdatabase,omitempty"`
	Schema       string      `json:"schema,omitempty"`
	Schemas      []string    `json:"schemas,omitempty"`
	Table        string      `json:"table,omitempty"`
	Tables       []string    `json:"tables,omitempty"`
	TablesInfo   []TableInfo `json:"tablesinfo,omitempty"`
	Field        []string    `json:"field,omitempty"`
	Fieldtype    []string    `json:"fieldtype,omitempty"`
	Fieldcomment []string    `json:"fieldcomment,omitempty"` //字段注释
	Firstrecord  []string    `json:"firstrecord,omitempty"`
	ThreeRecord  [3][]string `json:"threerecord,omitempty"`
	Flag         bool        `json:"flag"`
	Envcon       bool        `json:"envcon"`
	Copyname     string      `json:"copyname"`
	Goaldbtype   string      `json:"goaldbtype"`
	FileType     string      `json:"filetype"`
	SchemaID     string      `json:"schemaid"`
}
type TableInfo struct {
	Table            string   `json:"tablename,omitempty"`
	IsNull           bool     `json:"isnull"` //true 为空表
	Estimate         int      `json:"estimate"`
	Status           int      `json:"status"`           //0 为空表 1为有表
	DistributionMode string   `json:"distributionmode"` //MPP数据库分布方式
	DistributionBond []string `json:"distributionbond"` //MPP数据库分布键
}

type AccDataxMove struct {
	Ids             []string `json:"ids"`
	Dirname         string   `json:"dirname"`
	Dirid           string   `json:"dirid"`
	OriDirName      []string `json:"oridirname"`
	RealMessageType int      `json:"realmessagetype"`
}
type AccTbCount struct {
	Id         string `json:"id,omitempty"`
	Ip         string `json:"ip"`
	Database   string `json:"database"`
	Table      string `json:"table"`
	Date       string `json:"date,omitempty"`
	Num        int    `json:"num"`
	Createnum  int    `json:"createnum"`
	Enginename string `json:"enginename"`
	Duration   int    `json:"duration,omitempty"`
	Size       int    `json:"size,omitempty"`
}

type AccTbCountHit struct {
	Res AccTbCount `json:"_source"`
}

type AccTbSearch struct {
	Id           string   `json:"id,omitempty"` //数据源配置的id
	Odsdatabase  string   `json:"odsdatabase,omitempty"`
	Odsip        string   `json:"odsip,omitempty"`
	Odsport      string   `json:"odsport,omitempty"`
	Odsuser      string   `json:"odsuser,omitempty"`
	Odspassword  string   `json:"odspassword,omitempty"`
	Dbtype       string   `json:"dbtype,omitempty"`
	Dbdatabase   []string `json:"dbdatabase,omitempty"`
	Schema       string   `json:"schema,omitempty"`
	Schemas      []string `json:"schemas,omitempty"`
	Table        string   `json:"table,omitempty"`
	Tables       []string `json:"tables,omitempty"`
	Field        []string `json:"field,omitempty"`
	Fieldtype    []string `json:"fieldtype,omitempty"`
	Fieldcomment []string `json:"fieldcomment,omitempty"` //字段注释
	Firstrecord  []string `json:"firstrecord,omitempty"`
	Flag         bool     `json:"flag"`
	Envcon       bool     `json:"envcon"`
	Copyname     string   `json:"copyname"`
}

type DataMapInfo struct {
	SourceTotal      int `json:"sourcetotal,omitempty"`      //抽取源总数
	RelationDatabase int `json:"relationdatabase,omitempty"` //关系数据库
	OfflineText      int `json:"offlinetext,omitempty"`      //离线文本

	SingleExtract    int `json:"singleextract,omitempty"`    //单例抽取
	BatchExtract     int `json:"batchextract,omitempty"`     //批量抽取
	CustomCollection int `json:"customcollection,omitempty"` //自定义采集

	ExtractSuccess   int64 `json:"extractsuccess,omitempty"`   //抽取成功
	ExtractFail      int64 `json:"extractfail,omitempty"`      //抽取失败
	ExtractToPerform int   `json:"extracttoperform,omitempty"` //抽取待执行

	Governtotal     int64 `json:"governtotal,omitempty"`     //治理任务数
	GovernSuccess   int64 `json:"governsuccess,omitempty"`   //治理成功
	GovernFail      int64 `json:"governfail,omitempty"`      //治理失败
	GovernToPerform int   `json:"governtoperform,omitempty"` //治理待执行

	ConvergeTableNumber   int64  `json:"convergetablenumber,omitempty"`   //汇聚层数据表数量
	ConvergeStorageUsage  string `json:"convergestorageusage,omitempty"`  //汇聚层存储使用量
	DataSourceNumNormal   int    `json:"datasourcenumnormal,omitempty"`   //汇聚层数据源数量正常
	DataSourceNumAbnormal int    `json:"datasourcenumabnormal,omitempty"` //汇聚层数据源数量不正常
	DataStorageIncrement  string `json:"datastorageincrement,omitempty"`  //汇聚层数据存储增量

	ThemeTableNumber      int64              `json:"Themetablenumber,omitempty"`      //主题层数据表数量
	ThemeStorageUsage     string             `json:"Themestorageusage,omitempty"`     //主题层存储使用量
	ThemeProblemTableNum  int64              `json:"themeproblemtablenum,omitempty"`  //主题层问题数据表量
	ThemeProblemTableNums []map[string]int64 `json:"themeproblemtablenums,omitempty"` //主题层问题数据表量
}

type TdhKbs struct {
	IsKbs        bool   `json:"iskbs"`        //是否有kerberos认证
	Keytab       string `json:"keytab"`       //keytab文件
	VerifiedUser string `json:"verifieduser"` //认证账户
	TdhPath      string `json:"tdhpath"`      //tdh路径
	Token        string `json:"token"`        //tdc token
}

// datax模板迁徙
type GetTempleteRequest struct {
	Type       string                 `json:"type"`       //资源类型
	Parameters map[string]interface{} `json:"parameters"` // 其他参数
}

// topic等信息命名规则
type TopicIinfos struct {
	Name                string `json:"name"`
	Slot                string `json:"slotname"`
	DbServerName        string `json:"dbservername"`
	TableWhitelist      string `json:"tablewhitelist"`
	DbHistoryKafkaTopic string `json:"dbhistorykafkatopic"`
	Randid              string `json:"randids"`
}

// Kafka信息
type KafkaInfo struct {
	IpPort           string   `json:"ip"` //************:6667
	KafkaInstallPath string   `json:"kafkainstallpath"`
	Username         string   `json:"username"`
	Passwd           string   `json:"passwd"`
	Zookeeper_Info   []string `json:"zookeeper"`
}

type TdhInfo struct {
	Iskbs        bool   `json:"iskbs"`
	KeyTab       string `json:"keytab"`
	Verifieduser string `json:"verifieduser"`
}

// 实时kerberos信息汇总
type RealTimeKerberos struct {
	KeyTab            string `json:"keytab"`
	Principal         string `json:"principal"`
	ServerName        string `json:"servername"`
	NamenodePrincipal string `json:"namenodeprincipal"`
	HdfsPrincipal     string `json:"hdfsprincipal"`
	KafkaSecurity     string `json:"kafkasecurity"`
}

// 增量采集最大值
type AccIncreMax struct {
	Id     string `json:"id"`
	JobId  string `json:"jobid"`
	Max    string `json:"maxtime"`
	AccMax string `json:"accmaxtime"`
	//5.0新增
	TaskName string            `json:"taskname"` //批量任务判定独立任务名称
	TaskMax  map[string]string `json:"taskmax"`  //独立任务的增量时间
	//--
	TaskNameALL bool              `json:"tasknameall"` //查询全部
	Maxs        map[string]string `json:"maxtimes"`
	AccMaxs     map[string]string `json:"accmaxtimes"`
}

type AccUserPassw struct {
	AccId      string    `json:"accid"`
	ReaderConn DataxConn `json:"readerconn"`
	WriterConn DataxConn `json:"writerconn"`
}

type DataxConn struct {
	UserName     string `json:"username"`
	Password     string `json:"password"`
	JdbcUrl      string `json:"jdbcurl"`
	CSVDelimiter string `json:"csvdelimiter"`
	CSVPaths     string `json:"csvpaths"`
	//下列4个参数为hive kerberos参数
	AuthMethod     bool   `json:"authmethod"` //1:普通认证 2、kerberos认证
	KeytabFilePath string `json:"keytabfilepath"`
	Krb5ConfPath   string `json:"krb5confpath"`
	Principal      string `json:"principal"`
	//下列两个参数是odps使用
	OdpsUrl   string `json:"odpsurl"`
	TunnelUrl string `json:"tunnelurl"`
}

// 类型转换
type TypeCast struct {
	ExtrType   string   `json:"extrtype"`
	SourceType string   `json:"sourcetype"`
	FieldType  []string `json:"fieldtype"`
}
type AccPgCreateTable struct {
	Id        string `json:"id"`
	Engineid  string `json:"engineid"`
	Createsql string `json:"createsql"`
}

// --------------------------
// 增量策略修改
type AccIncreStatus struct {
	//当前增量记录时间
	CurrentIncredata string `json:"currincredata"`
	//同步原表历史数据
	Synchistory bool `json:"issynchistory"`
	//清空存储表数据
	DeleteSouTb bool `json:"isdeletesoutb"`
}

type PushApiInfo struct {
	Field              []FieldInfos `json:"field"`
	Topic              string       `json:"topic"`
	PrimaryKey         []string     `json:"primarykey"`
	KafkaServer        string       `json:"kafkaserver"`
	QuckerSilverServer string       `json:"quckersilverserver"`
	RealTaskId         string       `json:"realtaskid"`
	FlinkPushUrl       string       `json:"flinkpushurl"`
	DataTemplateid     string       `json:"datatemplateid"`
	Ip                 string       `json:"ip"`
	StartTime          string       `json:"starttime"`    //接口调用时间
	ExtractNum         int          `json:"extractnum"`   //接入数据量
	SuccessCount       int          `json:"successCount"` //成功数据量
	ErrorCount         int          `json:"errorCount"`   //失败数据量
}
type FieldInfos struct {
	FieldName string `json:"name"`
	FieldType string `json:"type"`
	//Comment string `json:"comment"`
}

type ExtractSsl struct {
	Isopenssl   bool   `json:"isopenssl"`   //是否开启ssl认证
	Sslmode     string `json:"sslmode"`     //ssl的模式
	Sslrootcert string `json:"sslrootcert"` //cal证书路径
	Sslcert     string `json:"sslcert"`     //客户证书路径
	Sslkey      string `json:"sslkey"`      //客户密钥路径
}

// --------------------------
type FlinkReal struct {
	Sourceid          string         `json:"sourceid"`
	Status            string         `json:"status"`
	Log               string         `json:"log"`
	FlinkPointInfo    FlinkPointInfo `json:"flinkpointinfo"`
	LatestPoint       string         `json:"latestpoint"`       //最新同步位点
	LatestPointTime   string         `json:"latestpointtime"`   //最新同步位点
	DataReadRateByte  string         `json:"datareadratebyte"`  //数据读取速度 单位 字节
	DataReadRateNum   string         `json:"datareadratenum"`   //数据读取速度  单位 条数
	DataWriteRateByte string         `json:"datawriteratebyte"` //数据写入速度 单位 字节
	DataWriteRateNum  string         `json:"datawriteratenum"`  //数据写入速度 单位 条数
	ReadNumber        string         `json:"readnumber"`        //读取数量
	WriteNumber       string         `json:"writenumber"`       //写入数量
	DirtyData         string         `json:"dirtydata"`         //脏数据
	HistoryPoint      []string       `json:"historypoint"`
	HistoryPointTime  []string       `json:"historypointtime"`
	FailReTryNumber   int            `json:"failretrynumber"`
	StartTime         string         `json:"starttime"` //治理子任务任务开始执行时间
}
type Infoflink struct {
	Ids         []string `json:"ids"`
	Operatetype int      `json:"operatetype"`
}
type Vertices struct {
	Id    string `json:"id"`
	Value string `json:"value"`
}
type CallBackUrl struct {
	Ip   string `json:"ip"`
	Port int    `json:"port"`
}

// FlinkHits1  query解析第一层
type FlinkHits1 struct {
	Hits1 FlinkHits2 `json:"hits"`
}

// FlinkHits2  query解析第二层
type FlinkHits2 struct {
	Hits2 []FlinkSource `json:"hits"`
	Total int           `json:"total"`
}

// AccSource query解析第三层
type FlinkSource struct {
	Acclab FlinkPushInfo `json:"_source"`
}
type FlinkPushInfo struct {
	Id           string        `json:"id"`
	Code         int           `json:"code"`         //状态码
	ExtractNum   int           `json:"extractnum"`   //接入数据量
	SuccessCount int           `json:"successCount"` //成功数据量
	ErrorCount   int           `json:"errorCount"`   //失败数据量
	Errors       []FlinkErrors `json:"errors"`       //错误返回值详情
	StartTime    string        `json:"starttime"`    //接口调用时间
	ApiUrl       string        `json:"apiurl"`       //接口实际名称
	Type         string        `json:"type"`         //调用类型
	Ip           string        `json:"ip"`           //调用ip
	ConsumeTime  string        `json:"consumetime"`  //实际耗时
	Status       string        `json:"pushstatus"`   //调用结果
	Result       string        `json:"result"`
}
type FlinkErrors struct {
	Result    string `json:"result"`
	ErrorData string `json:"errorData"`
}
type AccRealDetailInfo struct {
	ID          string     `json:"id"`
	Tags        []Tag      `json:"tags"`        //脚本标签（可通过标签搜索；也可在match中进行标签的模糊匹配）
	Description string     `json:"description"` //描述
	TaskType    int        `json:"tasktype"`
	Notename    string     `json:"notename"`
	ProjectID   string     `json:"projectid"` //所属的项目id
	Subjobs     []SubJob   `json:"subjob"`
	Globalvars  GlobalVars `json:"globalvars"` //全局参数设置
	//Label       AntInfo    `json:"label,omitempty"`
	//FlinkReal   FlinkReal  `json:"flinkreal"`
}
type GovernInfo struct {
	GovernStatus    string `json:"governstatus"`    //单个组件所在任务的总状态
	SubGovernStatus string `json:"subgovernstatus"` //单个组件状态
	JobIdUrl        string `json:"jobidurl"`
	JobId           string `json:"jobid"`
	LogInfo         string `json:"loginfo"`
}

// flink治理任务返回值结构体
type FlinkGovernResultInfo struct {
	ResultType    string `json:"resultType"`
	IsQueryResult bool   `json:"isQueryResult"` //streamsql语句的类型:DDL(false)-无jobid(运行结束)、DQL(true)-无jobid(运行结束)、DML(false)-有jobid(运行中)
	JobID         string `json:"jobID"`
	ResultKind    string `json:"resultKind"`
	Results       struct {
		Columns []struct {
			Name        string `json:"name"`
			LogicalType struct {
				Type     string `json:"type"`
				Nullable bool   `json:"nullable"`
				Length   int    `json:"length,omitempty"`
			} `json:"logicalType"`
			Comment interface{} `json:"comment,omitempty"`
		} `json:"columns"`
		RowFormat string `json:"rowFormat"`
		Data      []struct {
			Kind   string   `json:"kind"`
			Fields []string `json:"fields"`
		} `json:"data"`
	} `json:"results"`
	NextResultUri string `json:"nextResultUri"`
}
type FlinkGovernErrorInfo struct {
	ErrorsInfo []string `json:"errors"`
}

type ApiPyInfo struct {
	Content  string `json:"content"`
	Pid      int    `json:"pid"`
	FilePath string `json:"filepath"`
}
