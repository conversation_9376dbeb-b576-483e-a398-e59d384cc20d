package source

import (
	metasource "datatom.com/metadata/source"
)

type ProjectInfo struct {
	ProjectName string       `json:"projectname,omitempty"`
	Describe    string       `json:"describe,omitempty"`
	Id          string       `json:"id"`
	DbInfo      []EngineInfo `json:"dbinfo"`
	Userinfo    []UserInfo   `json:"userinfo,omitempty"`
	Createtime  string       `json:"createtime,omitempty"`
	Updatetime  string       `json:"updatetime,omitempty"`
	ProjectID   string       `json:"projectid"` //所属的项目id
}

type LayerInfo struct {
	Enginename string `json:"enginename,omitempty"`
	Engineid   string `json:"engineid,omitempty"`
	Layerid    string `json:"layerid,omitempty"`
	Layername  string `json:"layername"`
	ColorNum   int64  `json:"colornum,omitempty"`
}

type EngineInfo struct {
	EngineId  string `json:"engineid,omitempty"`
	Layerid   string `json:"layerid,omitempty"`
	Layername string `json:"layername"`
	DbName    string `json:"dbname,omitempty"`
	Dbtype    string `json:"dbtype,omitempty"`
	Onlykey   string `json:"onlykey,omitempty"`
	//Databasename string `json:"databasename"`
}

type UserInfo struct {
	UserName   string `json:"username"`
	UserId     string `json:"userid"`
	DepartMent string `json:"userdepartment"`
}

// type UpdateMessage struct {
// 	Upprojectname string       `json:"upprojectname,omitempty"`
// 	Updescribe    string       `json:"updescribe,omitempty"`
// 	Updbinfo      []EngineInfo `json:"updbinfo"`
// 	Upuserinfo    []string     `json:"upuserinfo"`
// 	Id            string       `json:"id,omitempty"`
// }

type SearchJson struct {
	SearchText string `json:"searchtext,omitempty"`
	Id         string `json:"id,omitempty"`
	SortType   string `json:"sorttype"`
	ListAll    bool   `json:"listall"`
	ProjectID  string `json:"projectid"` //所属的项目id
}

type ProjectHits1 struct {
	Hits1 ProjectHits2 `json:"hits"`
}

type ProjectHits2 struct {
	Hits2 []Source `json:"hits"`
	Total int      `json:"total"`
}

type Source struct {
	Project ProjectInfo `json:"_source"`
}

type TaskDelete struct {
	Ids         []string `json:"ids"`
	DeleteBasic bool     `json:"deletebasic"`
}

type TaskUpdate struct {
	Id          string `json:"id"`
	Newname     string `json:"newname,omitempty"`
	Tags        []Tag  `json:"tags,omitempty"`
	Description string `json:"description,omitempty"`
	Projectid   string `json:"projectid"`
}

type SyncTaskInfo struct {
	TaskID      string `json:"string"`
	TaskName    string `json:"taskname"`
	MiddleTable int    `json:"middletable"`
	Project     string `json:"project"`
}

type ODSInfo struct {
	ID          string `json:"id"`
	ElementName string `json:"elementname"`
	Remark      string `json:"remark"`
}

// type Tag struct {
// 	Name  string `json:"name"`
// 	Color string `json:"color"`
// }

type TaskInfo struct {
	TaskName    string   `json:"taskname"`
	Projectid   string   `json:"projectid"`
	Projectname string   `json:"projectname"`
	User        string   `json:"user"`
	Tags        []Tag    `json:"tags"`
	SubJobs     []SubJob `json:"subjob,omitempty"`
	Description string   `json:"description"`
	Submitted   bool     `json:"submitted,omitempty"`
	Id          string   `json:"id"`
}

type LabelInfo struct {
	Module     string       `json:"module"`
	Langtype   string       `json:"langtype"`
	Dirid      string       `json:"dirid"`
	Updatetime string       `json:"updatetime"` //更新时间
	Submitted  bool         `json:"submitted"`
	Submitinfo WfSubmitInfo `json:"submitinfo"` //保存上线版本的实验信息

	Tags   []Tag    `json:"tags"`   //标签
	Concat []string `json:"concat"` //拼加tag的字段

	Deletetime string   `json:"deletetime"`
	SubJobs    []SubJob `json:"subjob"` //试运行时为取消保存而加此字
}

// type TaskUpdate struct {
// 	Id          string `json:"id"`
// 	Newname     string `json:"newname"`
// 	Newdir      string `json:"newdir"`
// 	Newdirid    string `json:"newdirid"` //不用前端传
// 	Star        string `json:"star"`
// 	Top         string `json:"top"`
// 	Tags        []Tag  `json:"tags"`
// 	Description string `json:"description"`
// }

type DataProcessInfo struct {
	//基础属性
	Id         string   `json:"id,omitempty"`
	Name       string   `json:"name,omitempty"`
	Descp      string   `json:"description,omitempty"`
	ExectTimes *int64   `json:"exectimes,omitempty"`
	CreateTime string   `json:"createtime,omitempty"`
	SubJobs    []SubJob `json:"subjob"`
	Status     string   `json:"status,omitempty"` //BAN RUNNING WAITING
	//第三方相关
	Label    AntInfo `json:"label,omitempty"`
	TaskType string  `json:"tasktype,omitempty"`
	//用户相关
	UserID   string `json:"userid,omitempty"`
	Username string `json:"username,omitempty"`
	//运行相关
	LastRunStatus string `json:"lastrunstatus,omitempty"`
	LastRunTime   string `json:"lastruntime,omitempty"`
	Updatetime    string `json:"updatetime,omitempty"`
}

type TaskSearch struct {
	SearchText string    `json:"searchtext,omitempty"`
	Updatetime RangeTime `json:"updatetime,omitempty"`
	Createtime RangeTime `json:"createtime,omitempty"`
	Submitted  string    `json:"submitted,omitempty"`
	Tags       []string  `json:"tags,omitempty"`
	//Listall    bool      `json:"listall"`
	Sorttype      int    `json:"sorttype,omitempty"` //按默认排序创建时间排序0，按更新时间排序1，上次运行2，任务状态3
	Page          int    `json:"page,omitempty"`
	Perpage       int    `json:"perpage,omitempty"`
	Id            string `json:"id,omitempty"`
	User          string `json:"user,omitempty"`
	Lastrunstatus string `json:"lastrunstatus"`
}

type TaskHits1 struct {
	Hits1 TaskHits2 `json:"hits"`
}

// WfHits2  query解析第二层
type TaskHits2 struct {
	Hits2 []TaskSource `json:"hits"`
	Total int          `json:"total"`
}

// WfSource query解析第三层
type TaskSource struct {
	Lab AllTaskInfo `json:"_source"`
}

type AllTaskInfo struct {
	Id         string   `json:"id,omitempty"`
	Name       string   `json:"name,omitempty"`
	Descp      string   `json:"description,omitempty"`
	ExectTimes *int64   `json:"exectimes,omitempty"`
	CreateTime string   `json:"createtime,omitempty"`
	SubJobs    []SubJob `json:"subjob"`
	Status     string   `json:"status,omitempty"` //BAN RUNNING WAITING
	//第三方相关
	Label    AntInfo `json:"label,omitempty"`
	TaskType string  `json:"tasktype,omitempty"`
	//用户相关
	UserID   string `json:"userid,omitempty"`
	Username string `json:"username,omitempty"`
	//运行相关
	LastRunStatus string `json:"lastrunstatus,omitempty"`
	LastRunTime   string `json:"lastruntime,omitempty"`
	Updatetime    string `json:"updatetime,omitempty"`
	Crondetail    string `json:"crondetail,omitempty"`

	//Exectimes  int    `json:"exectimes,omitempty"`
	AutoAssign bool     `json:"autoassign"`
	Assignnode string   `json:"assignnode,omitempty"`
	Preid      []string `json:"preid"`
	//	上下线相关
	IsBeingOffline bool   `json:"isbeingoffline"` //是否正在下线标识
	OnLineTime     string `json:"onlinetime"`

	Istimeout  bool         `json:"istimeout"`            //是否设置超时
	Settimeout *Timeoutconf `json:"settimeout,omitempty"` //设置超时配置
}

type TaskNumber struct {
	TaskNum int `json:"tasknum"`
	SubNum  int `json:"subnum"`
}

type InstanceInfo struct {
	Ip           string `json:"ip,omitempty"`
	Port         int    `json:"port,omitempty"`
	User         string `json:"username,omitempty"`
	Passwd       string `json:"password,omitempty"`
	Database     string `json:"initdb,omitempty"`
	Engineid     string `json:"engineid,omitempty"`
	Databasetype string `json:"enginename,omitempty"`
	Enginename   string `json:"name,omitempty"`
	HiveFilePath string `json:"hivefilepath,omitempty"`
}

type TbJson struct {
	Engineid  string    `json:"engineid,omitempty"`
	Dbname    string    `json:"dbname,omitempty"`
	Schema    string    `json:"schema,omitempty"`
	Tbname    string    `json:"tbname,omitempty"`
	Layerid   string    `json:"layerid,omitempty"`
	TimeField FieldInfo `json:"timefield"`
	SortField FieldInfo `json:"sortfield"`
	Sqltxt    string    `json:"sqltxt"`
	Id        string    `json:"id"`
	Field     string    `json:"field,omitempty"`
	Fieldtype string    `json:"fieldtype"`
	//Flag      bool      `json:"flag"`
	Mainkeyfield []string   `json:"mainkeyfield"`
	Duproles     TbDupRoles `json:"duproles,omitempty"`
	AITableType  TableType  `json:"aitabletype"` // AI事实表代码表区分结构体
	Preserve     bool       `json:"preserve"`    //是否需要清空后续模组信息
	ProjectID    string     `json:"projectid"`

	SourceTBID   string `json:"sourcetbid"`   //数据服务-源表ID
	SourceTBType int    `json:"sourcetbtype"` //数据服务-源端表类型 1资源表，2资产表
}
type TbDupRoles struct {
	Problem    bool           `json:"problem,omitempty"`    //放入问题库
	Dataclean  bool           `json:"dataclean,omitempty"`  //数据去重
	Extraclean []TbExtraclean `json:"extraclean,omitempty"` //指定条件去重
}

type CopyTbDupRoles struct {
	Problem    bool           `json:"problem"`    //放入问题库
	Dataclean  bool           `json:"dataclean"`  //数据去重
	Extraclean []TbExtraclean `json:"extraclean"` //指定条件去重
}

type TbExtraclean struct {
	Field string `json:"field"` //字段
	Role  string `json:"role"`  // > = <  equal
	Value string `json:"value"` //值
}

type Tbinfo struct {
	Field        []string      `json:"field,omitempty"`
	Fieldtype    []string      `json:"fieldtype,omitempty"`
	Fieldcomment []string      `json:"fieldcomment,omitempty"`
	Fieldfirst   []interface{} `json:"fieldfirst,omitempty"`
	Fieldsecond  []interface{} `json:"fieldsecond,omitempty"`
	Fieldthird   []interface{} `json:"fieldthird,omitempty"`
	Fieldfourth  []interface{} `json:"fieldfourth,omitempty"`
	Table        Tableinfo     `json:"tbinfo"`
	FieldContent FieldContent  `json:"fieldcontent"`
	Tbtype       string        `json:"tbtype"` //表类型--事实表还是代码表，还是未知。
	OdsType      string        `json:"odstype"`
}

type MgTbInfo struct {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	Data    []DescStru `json:"data"`
}

type DescStru struct {
	Comment  string `json:"comment"`
	ColName  string `json:"col_name"`
	DataType string `json:"data_type"`
}

type Tableinfo struct {
	Engineid string `json:"engineid"`
	Dbname   string `json:"dbname"`
	Schema   string `json:"schema"`
	Tbname   string `json:"tbname"`
	Layerid  string `json:"layerid,omitempty"`
	Tbtype   string `json:"tbtype"` //表类型--事实表还是代码表，还是未知。
	AITbID   string `json:"aitbid"` // ai表对应的id字段
}

type Sources struct {
	SaveSource Tbinfo `json:"_source"`
}

type FieldContent struct {
	Timefield    string     `json:"timefield"`
	Timeterm     []string   `json:"timeterm"`
	Sortfield    string     `json:"sortfield"`
	Sorttype     int        `json:"sorttype"`
	Wherecontent string     `json:"wherecontent"`
	Flag         bool       `json:"flag"`
	Mainkeyfield []string   `json:"mainkeyfield"`
	Duproles     TbDupRoles `json:"duproles,omitempty"`
	Fieldtype    string     `json:"fieldtype"`
	Format       string     `json:"format"`
}

type FieldInfo struct {
	Field string `json:"field"`
	//Fields    []string  `json:"fields"`
	Fieldtype   string   `json:"fieldtype"`
	Timeterm    []string `json:"timeterm,omitempty"`
	Sorttype    int      `json:"sorttype,omitempty"`
	Flag        bool     `json:"flag"`
	Format      string   `json:"format"`
	MaxIncre    string   `json:"maxincre"`    //目前的增量最大值
	SetMaxIncre string   `json:"setmaxincre"` //设置增量最大值
}

type ModuleMenu struct {
	Name     string `json:"name"`
	Id       string `json:"id"`
	Subid    int    `json:"subid"`
	Prev     []int  `json:"pre"`
	Post     []int  `json:"post"`
	Module   int    `json:"module"`
	Retrynum int    `json:"retrynum"`
}

// SubJob
type Content struct {
	SubId int    `json:"subid"`
	Cid   string `json:"cid"`
	Ctype string `json:"ctype"`
	Name  string `json:"name"`
	Descp string `json:"description"`
	Cmd   string `json:"cmd"`
	Prev  []int  `json:"pre"`
	Post  []int  `json:"post"`

	Symbol      string `json:"symbol"`       //操作类型，支持工作类子任务ACTION,默认PROCESS，可选DECISION
	SuccessPost []int  `json:"success_post"` //成功后执行子任务组，需要prev所有子任务成功，才判定为成功
	FailPost    []int  `json:"fail_post"`    //失败后执行子任务组，只要prev的一个任务失败，则判定为失败

	//外部支持
	EnvTag []string `json:"envtag"` //用于标志环境变量，为d-fusion支持环境变量

	Longitude int   `json:"x"`
	Latitude  int   `json:"y"`
	Tags      []Tag `json:"tag"`
	Module    int   `json:"module"`
	ModStatus bool  `json:"status"`
}

type UpModule struct {
	Name          string `json:"name"`
	Description   string `json:"description"`
	Tags          []Tag  `json:"tag"`
	Id            string `json:"id"`
	Taskid        string `json:"taskid"`
	Module        int    `json:"module"`
	Retrynum      int    `json:"retrynum"`
	IsRetry       bool   `json:"isretry"`       //是否重试：true：重试，false：不重试
	RetryInterval int    `json:"retryinterval"` //重试间隔时长，单位为分钟
	Inputid       string `json:"inputid"`       //前端想通过上一个模组判断是否是增量
}

type DpIds struct {
	Ids []string `json:"ids"`
}

type DpId struct {
	Id string `json:"id"`
}

type DpRun struct {
	Id      string   `json:"id"`
	SubJobs []SubJob `json:"subjob,omitempty"`
}

type DpSave struct {
	Id         string   `json:"id"`
	SubJobs    []SubJob `json:"subjob,omitempty"`
	IsRealtime bool     `json:"isrealtime"` //是否为实时保存
}

type TableJson struct {
	Taskid    string `json:"taskid"`
	Moduleid  string `json:"moduleid"`
	Pre       []int  `json:"pre"`
	ProjectID string `json:"projectid"`
}
type Extra struct {
	Tag    []Tag  `json:"tag"`
	Module int    `json:"module"`
	Status bool   `json:"status"`
	Cmd    string `json:"cmd"`
	//输出表 来自 newtbinfo
	SourceDB   string `json:"sourcedb"`   //存储源
	Tbname     string `json:"tbname"`     //表名称
	EngineID   string `json:"engineid"`   //引擎ID，用于判断 dbtype
	EngineName string `json:"enginename"` //引擎名称
}

type Link struct {
	Prev []int `json:"pre,omitempty"`
	Post []int `json:"post,omitempty"`
	// Row    int   `json:"row,omitempt"`
	// Column int   `json:"column,omitempt"`
	// Flag   bool  `json:"flag,omitempt"`
}

type Output struct {
	Fieldname []string `json:"fieldname"`
	Moduleid  string   `json:"moduleid"`
}

type OutputSave struct {
	Fieldinfo  []FieldsInfo     `json:"fieldinfo"`
	Finaltable OutputFinalTable `json:"finaltable"`
	Moduleid   string           `json:"moduleid"`
	// Finalcmd   string           `json:"finalcmd"`
	Content          string         `json:"content"`
	Outputfields     []string       `json:"outputfields"`
	Outputfieldtypes []string       `json:"outputfieldtypes"`
	Outputcomment    []string       `json:"outputcomment"`
	OutputShowsql    string         `json:"outputshowsql"`
	OutputPrimkeys   []string       `json:"outputprimkeys"`
	Changedinfo      []Changedfield `json:"changedinfo"`
	// Initdb     string           `json:"initdb"` //表输入选择的数据库
	// Odstype    string           `json:"odstype"`
	Iscover        bool   `json:"iscover"`        //表输出数据是否覆盖 true覆盖 false追加
	ConditionCover string `json:"conditioncover"` //iscover=true的情况，非空条件覆盖，为空全量覆盖
	IsFirst        int    `json:"isfirst"`        //0第一次调用 1弹窗触发

	//480新增
	PartitionField   []metasource.PartitionFieldInfo `json:"partitionfield"` //分区字段 --> 记录预设字段信息
	SortType         string                          `json:"sorttype"`
	SortField        string                          `json:"sortfield"`
	Pre              []int                           `json:"pre"`              //前置模组
	Preserve         bool                            `json:"preserve"`         //清空后续模组
	DistributionMode string                          `json:"distributionmode"` //分布方式
	DistributionBond []string                        `json:"distributionbond"` //分布键

	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级
	//5.1 新增 落地使用已有表
	UseMetaTB     bool              `json:"usemetatb"` //使用已有表
	MetaTBID      string            `json:"metatbid"`  //已有表ID
	FieldMap      `json:"fieldmap"` //已有表字段映射,左 Input, 右 output
	ProjectID     string            `json:"projectid"`
	DeleteHistory bool              `json:"deletehistory"`

	//6.0 改用 accinfo
	NewAcc
}

type FieldMap struct {
	Input  []string `json:"input"`
	Output []string `json:"output"`
}

type PartitionInfos struct {
	PartitionInfo metasource.PartitionInfo `json:"partitioninfo"` //MG返回分区详情
}

type Changedfield struct {
	Fieldname     string `json:"fieldname"`
	Fieldtype     string `json:"fieldtype"`
	Fielddescribe string `json:"fielddescribe"`
	IsPartition   bool   `json:"ispartition"`
}

type FieldsInfo struct {
	Oldfieldname string `json:"oldfieldname"`
	Oldfieldtype string `json:"oldfieldtype"`
	Olddescribe  string `json:"olddescribe"`
	Newfieldname string `json:"newfieldname"`
	Newfieldtype string `json:"newfieldtype"`
	Newdescribe  string `json:"newdescribe"`
	IsPrim       bool   `json:"isprim"`
	//480新增
	IsPartition bool `json:"ispartition"` //是否分区键
}

type OutputFinalTable struct {
	Tbname   string `json:"tbname"`   //表名称
	Dbname   string `json:"dbname"`   //存储源名称
	Schema   string `json:"schema"`   //表所在的schema
	Engineid string `json:"engineid"` //表所在库的引擎id
	Describe string `json:"describe"` //表描述
	Flag     bool   `json:"flag,omitempty"`
	MetaTBID string `json:"metatbid"` // 元数据表id
	JobID    string `json:"jobid"`    // 关联任务id
	ModuleID string `json:"moduleid"` // 启用元模型id
	UserID   string `json:"userid"`   // 所属用户id
	Username string `json:"username"` // 所属用户名称
	//v4.6.1增
	Chinese    string `json:"chinese"`    // 表中文名称
	FileFormat string `json:"fileformat"` // 文件格式 Orcfile/Textfile
	CatalogID  string `json:"catalogid"`  // 所属最底层编目id
	//CataList   []CataList            `json:"catalist"`   // 所属编目数组
	TagList   []metasource.TagList `json:"taglist"`   // 标签信息
	LayerID   string               `json:"layerid"`   //层id
	LayerName string               `json:"layername"` // 层名称
	ProjectID string               `json:"projectid"` //所属项目id
	SourceDB  string               `json:"sourcedb"`  // 存储源名称,对hive即为数据库名称,对stork/teryx指schema
	SourceID  string               `json:"sourceid"`  // 存储源id
	TbType    string               `json:"tbtype"`    // 表类型 未分类/事实表/代码表
	Hasdelete bool                 `json:"hasdelete"` //是否已经被清空过
	//FieldInfo  []MetaSourceFieldInfo `json:"fieldinfo"`  // 字段信息
	// DistributionMode string   `json:"distributionmode"` //分布方式
	// DistributionBond []string `json:"distributionbond"` //分布键
}

// MG单表信息
type MGResTB struct {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	TBInfo  SourceTBMG `json:"data"`
}

type SourceTBMG struct {
	TableName           string              `json:"tableName"`
	SchemaName          string              `json:"schemaName"`
	DBName              string              `json:"dbName"`
	TableDistributeInfo TableDistributeInfo `json:"tableDistributeInfo,omitempty"`
	TablePrimaryKeyInfo TablePrimaryKeyInfo `json:"tablePrimaryKeyInfo"`
	Cols                []MGCols            `json:"cols"`
}

type MGCols struct {
	Name string `json:"name"`
	Type string `json:"type"`
	Len  int    `json:"len"`
	Num  int    `json:"num"`
}

type TablePrimaryKeyInfo struct {
	PrimaryCols     string        `json:"primaryCols"`
	PrimaryColsList []FieldTBInfo `json:"primaryColsList"`
}
type FieldTBInfo struct {
	Name    string `json:"name"`
	Type    string `json:"type"`
	Comment string `json:"comment"`
	Len     int    `json:"len"`
	Num     int    `json:"num"`
	NotNull bool   `json:"notnull"`
}

type TableDistributeInfo struct {
	DisType     string        `json:"disType"`
	DisCols     string        `json:"disCols"`
	DisColsList []FieldTBInfo `json:"disColsList"`
}

// MetaSourceFieldInfo 存储源下的表的字段结构
type MetaSourceFieldInfo struct {
	MetaFieldInfo metasource.MetaSourceFieldInfo `json:"metafieldinfo"`
}

type CheckEngine struct {
	Num int `json:"num"`
}

type TableType struct {
	ID        string `json:"id"`
	TableType string `json:"tabletype"`
}

// 通过库--表--字段  获取代码表列表。
type GetDimTablesInfo struct {
	Dbname    string `json:"dbname"`            //db
	Tbname    string `json:"tbname"`            //tb
	Colname   string `json:"colname,omitempty"` //字段
	DimCol    string `json:"dimcol"`            // 代码字段
	DimValCol string `json:"dimvalcol"`         //代码值字段
}

type DimDataList struct {
	Dbname    string `json:"dbname"`    //db
	Tbname    string `json:"tbname"`    //tb
	DimCol    string `json:"dimcol"`    // 代码字段
	DimValCol string `json:"dimvalcol"` //代码值字段
}

// MetaList 表输入结构体
type MetaList struct {
	metasource.MetaLayer
	Children []metasource.SourceItem `json:"children"`
}

type SchemaList struct {
	SchemaID   string `json:"schemaid"`
	SchemaName string `json:"schemaname"`
}

type SourceList struct {
	metasource.MetaSourceDb
	Chileren []SchemaList `json:"children"`
}

// 查询结构体,用作 gettables 和 query
type QueryBody struct {
	ID           string `json:"id"`         //权限 id
	ProjectID    string `json:"projectid"`  //项目 ID
	Paging       bool   `json:"paging"`     //分页开关。若关闭则返回全量
	Page         int    `json:"page"`       //页码
	PerPage      int    `json:"perpage"`    //每页条数
	QueryType    int    `json:"querytype"`  //查询方式  -1 全部 1. 点击层 2. 点击库 3. 点击 schema 4. 点击编目 5. 点击标签目录 6. 点击标签
	QueryID      string `json:"queryid"`    //查询的 ID
	DropDetail   bool   `json:"Dropdetail"` //是否清空 detail 以节约资源
	TokenStr     string `json:"tokenstr"`   //token 字符串
	NotHandleTag bool   `json:"handletag"`  //修正表的标签文件夹
	FeatModule   string `json:"featmodule"` //使用的功能模块 tableinput: 表输入
}

// CatalogList 编目列表
type CatalogList struct {
	metasource.MetaDir
	Children []*CatalogList `json:"children"`
}

// TagList 标签列表
type TagDirList struct {
	metasource.MetaDir
	Tags     []metasource.MetaTag `json:"tags"`
	Children []*TagDirList        `json:"children"`
}

type MetaSourceTb struct {
	MetaTb metasource.MetaSourceTb `json:"metatb"`
}
