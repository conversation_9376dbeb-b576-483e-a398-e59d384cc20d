/*
Copyright (c) Datatom Software, Inc.(2021)
Author: 陈洪彪（<EMAIL>）
Creating Time: 2021-06-01

DS采集模块-->struct
*/

package source

import metadata "datatom.com/metadata/source"

// NewTask 完整采集任务信息
type NewTask struct {
	UserID           string           `json:"userid"`           // 用户id
	UserName         string           `json:"username"`         // 用户名
	ProjectID        string           `json:"projectid"`        // 项目id
	Notetype         string           `json:"notetype"`         // 任务类型
	Name             string           `json:"name"`             // 任务名称
	ID               string           `json:"id"`               // 标识符
	TaskID           string           `json:"taskid"`           // 任务id,即id
	TaskType         int              `json:"tasktype"`         // 任务类型（1.单例离线 2.单例实时 3.批量离线）
	DirID            string           `json:"dirid"`            // 任务目录id
	DirName          string           `json:"dirname"`          // 任务目录名称
	Tags             []Tag            `json:"tags"`             // 任务标签
	Description      string           `json:"description"`      // 任务描述
	Extractdb        GlobalExtractdb  `json:"globalextractdb"`  // 全局数据源信息
	Sourcedb         GlobalSourcedb   `json:"globalsourcedb"`   // 全局存储源信息
	PolicyType       int              `json:"policytype"`       // 策略规则(0.使用源表名 1.表存在则加前缀或后缀 2.所有表名加前缀或后缀)
	Prefix           string           `json:"prefix"`           // 前缀
	Suffix           string           `json:"suffix"`           // 后缀
	IsRetry          bool             `json:"isretry"`          // 是否重试,true：重试，false：不重试
	RetryNum         int              `json:"retrynum"`         // 重试次数
	RetryInterval    int              `json:"retryinterval"`    // 重试间隔时长，单位为分钟
	IsBan            bool             `json:"isban"`            // 重试失败后是否禁用：true：禁用，false：不禁用
	GlobalSubmitInfo GlobalSubmitInfo `json:"globalsubmitinfo"` // 全局任务配置信息
	Customize        []CustomizeTable `json:"customize"`        // 任务个性化设置，以表信息为基本单位的数组
	GlobalVars       GlobalVars       `json:"globalvars,omitempty"`
}

// 实时推送信息
type TaskIds struct {
	Ids []string `json:"ids"` //批量任务的id
}
type RunInfo struct {
	ID        string `json:"id"`
	JobID     string `json:"jobid"`
	UserID    string `json:"userid"`    // 用户id
	UserName  string `json:"username"`  // 用户名
	ProjectID string `json:"projectid"` // 项目id
	ReRunID   string `json:"rerunid"`   // api重跑的参数id
	RecordID  string `json:"recordid"`  //api record id
}

// 实时删除子任务信息
type DelSubTaskId struct {
	ID            string `json:"id"`            //批量任务的id
	SubId         string `json:"subid"`         //子任务的id
	Extracttbname string `json:"extracttbname"` //子任务抽取源表名
	IsDelTable    bool   `json:"isdeltable"`    //是否要删除存储表
	Souretbname   string `json:"souretbname"`   //子任务存储表名称
}

// GlobalExtractdb 全局数据源信息
type GlobalExtractdb struct {
	ID                     string       `json:"id"`                      // 数据源id
	DBName                 string       `json:"dbname"`                  // 数据源名称
	DBType                 string       `json:"dbtype"`                  // 数据源类型
	Schema                 string       `json:"schema"`                  // 模式名称
	SchemaID               string       `json:"schemaid"`                // 模式id
	BatchTables            []string     `json:"batchtables"`             // 批量抽取-表列表
	BatchTablesExtractdbID []string     `json:"batchtablesextractdbid""` // 批量抽取-数据源表id -- 前端需要 --记录用
	SameNameTBs            []SameNameTB `json:"samenametbs"`             // 批量同名策略数组,同名策略,0:不采集该表1:使用已有同名表
	FilterTableNull        bool         `json:"filtertablenull"`         // 是否过滤空表
	TablesInfo             []TableInfo  `json:"tablesinfo,omitempty"`    // 表数据量相关的信息
}

// GlobalSourcedb 全局存储源信息
type GlobalSourcedb struct {
	LayerID       string    `json:"layerid"`       // 分层id
	LayerName     string    `json:"layername"`     // 分层名称
	EngineID      string    `json:"engineid"`      // 引擎id
	OdsEngineName string    `json:"odsenginename"` // 引擎类型
	OdsDatabase   string    `json:"odsdatabase"`   // 数据库名称,值为dbname
	Schema        string    `json:"schema"`        // 模式名称，值一律赋值为sourcename即可
	DBType        string    `json:"dbtype"`        // 存储源类型，值为sourcetype
	SourceID      string    `json:"sourceid"`      // 存储源id
	CatalogID     string    `json:"catalogid"`     // 所属编目id
	TagList       []TagList `json:"taglist"`       // 表标签数组
	FileType      string    `json:"filetype"`      // hive文件格式
	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级
	//5.7.1默认分区
	PartitionField []PartitionFieldInfo `json:"partitionfield"` // 分区字段 `
	//5.8API
	ParsePath   []string `json:"parsepath"`   // 解析路径
	ParsePathID []string `json:"parsepathid"` // 解析路径

}

// GlobalSubmitInfo 全局任务配置信息
type GlobalSubmitInfo struct {
	ExtractOnlineStrategy int      `json:"extractonlinestrategy"` // 全局数据策略,0：覆盖存储数据，1：全量追加数据，2：增量追加数据 3.更新数据
	ConditionCover        string   `json:"conditioncover"`        // 条件覆盖： ExtractOnlineStrategy=0前提下，ConditionCover非空为条件覆盖，为空为全量覆盖
	PreID                 []string `json:"preid"`                 // 前置任务id列表
	ScheduleType          int      `json:"scheduletype"`          // 执行策略类型,0 每隔某些时间;1 每天某个时间;2 每周某个时间;3 每月某个时间;4 自定义高级 **/1***;5 无
	AssignNode            string   `json:"assignnode"`            // 运行节点ip
	ScheduleDetail        string   `json:"scheduledetail"`        // 执行策略详情
	ExecTimes             *int64   `json:"exectimes"`             // 执行次数，默认为-1(执行无限次)
	// 5.0新增
	IsStartNow     bool   `json:"isstartnow"`     //是否立即开始，true：立即执行，false：手动开始
	FirstStartTime string `json:"firststarttime"` //固定类型开始时间

	//5.8.0新增
	EffectStart     string       `json:"effectstart"`     //生效开始时间
	EffectEnd       string       `json:"effectend"`       //生效结束时间
	CrossDep        bool         `json:"crossdep"`        //跨周期自依赖
	CrossForce      bool         `json:"crossforce"`      //是否强制依赖，是标识自依赖作业成功后可继续，否标识自依赖运行结束后可继续（避免并行）
	UpDep           bool         `json:"updep"`           //上游依赖任务
	DepJobInfo      []DepJobInfo `json:"depjobinfo"`      //依赖任务
	JobLevel        string       `json:"joblevel"`        //任务优先级
	TimeoutStop     bool         `json:"timeoutstop"`     //超时中断
	TimeoutDuration []int        `json:"timeoutduration"` //超时时长
}

// CustomizeTable 任务个性化设置,以表信息为基本单位
type CustomizeTable struct {
	ID                    string            `json:"id"`                       // 标识符
	ExtractTable          string            `json:"extracttable"`             // 源表名
	ExtractTableNew       string            `json:"extracttablenew"`          // 进行过适配的源表名
	SourceTable           string            `json:"sourcetable"`              // 目标表名
	GatherMethod          int               `json:"gathermethod"`             // 采集方式,1:全量，2：增量
	ExtractOnlineStrategy int               `json:"extractonlinestrategy"`    // 单表数据策略,0：覆盖存储数据，1：全量追加数据，2：增量追加数据
	ExtractInfo           CustomExtractInfo `json:"extractinfo"`              // 自定义数据源表详情
	SourceInfo            CustomSourceInfo  `json:"sourceinfo"`               // 自定义存储源表详情
	TableRelation         TableRelation     `json:"tablerelation"`            // 字段映射：由前端提供映射规则
	HistoryData           bool              `json:"historydata"`              // 清空 true 不清空 false
	AdvancedConfig        AdvancedConfig    `json:"advancedconfig,omitempty"` // 采集任务高级配置
}

// CustomExtractInfo 自定义数据源表详情
type CustomExtractInfo struct {
	TableID          string            `json:"tableid"`          // 数据源表id
	AccIncreStatus   AccIncreStatus    `json:"accincrestatus"`   // 增量配置
	CreateField      string            `json:"createfield"`      // 增量字段
	TimeType         string            `json:"timetype"`         // 时间增量类型
	SupportDirtyData bool              `json:"supportdirtydata"` // 支持抽取脏数据
	WhereContent     string            `json:"wherecontent"`     // 过滤条件
	FirstRecord      [][]string        `json:"firstrecord"`      // 前三条数据
	FieldInfo        []RecordFieldInfo `json:"fieldinfo"`        // 字段信息数组
}

// CustomSourceInfo 自定义存储源表详情
type CustomSourceInfo struct {
	TableID             string               `json:"tableid"`             // 存储源表id
	CatalogID           string               `json:"catalogid"`           // 自定义编目id
	FileType            string               `json:"filetype"`            // 自定义hive文件格式
	TableComment        string               `json:"tablecomment"`        // 表描述
	TagList             []TagList            `json:"taglist"`             // 自定义表标签
	FieldInfo           []RecordFieldInfo    `json:"fieldinfo"`           // 字段信息数组
	DistributionMode    string               `json:"distributionmode"`    // MPP数据库分布方式
	DistributionBond    []string             `json:"distributionbond"`    // MPP数据库分布键
	PartitionField      []PartitionFieldInfo `json:"partitionfield"`      // 分区字段
	PartitionInfoFields []string             `json:"partitioninfofields"` // MG返回分区字段
	//5.1新增 列存和压缩 teryx
	Orientation   string `json:"orientation"`   //存储方式
	CompressType  string `json:"compresstype"`  //压缩格式
	CompressLevel int    `json:"compresslevel"` //压缩等级
}

// CmdContent 批量增强表命令及命令详情
type CmdContent struct {
	Table                 string         `json:"table"`                 // 抽取源表名
	AimID                 string         `json:"aimid"`                 // 目标表id
	AimTable              string         `json:"aimtable"`              // 目标表名称
	AccIncreStatus        AccIncreStatus `json:"accincrestatus"`        // 增量配置
	ExtractOnlineStrategy int            `json:"extractonlinestrategy"` // 单表数据策略,0：覆盖存储数据，1：全量追加数据，2：增量追加数据
	Cmd                   string         `json:"cmd"`                   // 执行命令
	//Content               string         `json:"content"`               // 执行的命令内容
	FileName  string `json:"filename"`  // json文件路径
	ShellName string `json:"shellname"` // 脚本文件路径
	//5.3 datax抽取和analyzesql异步执行
	AnalyzeSql string `json:"analyzesql"` //analyze sql语句//输出表
	SourceDB   string `json:"sourcedb"`   //存储源
	EngineID   string `json:"engineid"`   //引擎ID
	//5.3 子任务信息
	SubTaskID   string `json:"subtaskid"` //子任务id
	SubTaskName string `json:"subtaskname"`
}

// SplitBatch 拆分批量增强任务所需信息
type SplitBatch struct {
	JSONDir    string                        `json:"jsondir"`    // json文件夹路径
	TaskDir    string                        `json:"taskdir"`    // 任务脚本文件夹路径
	CustomMap  map[string]CustomizeTable     `json:"custommap"`  // 个性化设置表数组
	ExTableMap map[string]metadata.TableInfo `json:"extablemap"` // 全局表属性信息
	SameMap    map[string]int                `json:"samemap"`    // 同名表信息
	HdfsURL    string                        `json:"hdfsurl"`    // hive表文件类型
	DB         *metadata.AccDbInfo           `json:"dbinfo"`     // 数据源详情
	Token      string                        `json:"token"`      // 认证信息
}

//	type APiTaskInfo struct {
//		Name        string `json:"name"`        // 任务名称
//		ID          string `json:"id"`          // 标识符
//		DirID       string `json:"dirid"`       // 任务目录id
//		DirName     string `json:"dirname"`     // 任务目录名称
//		Tags        []Tag  `json:"tags"`        // 任务标签
//		Description string `json:"description"` // 任务描述
//		UserID      string `json:"userid"`      // 用户id
//		UserName    string `json:"username"`    // 用户名
//		ProjectID   string `json:"projectid"`   // 项目id
//
//		Extractdb     GlobalExtractdb  `json:"apiextractdb"`  // 数据源信息
//		Sourcedb      GlobalSourcedb   `json:"apisourcedb"`   // 存储源信息
//		TableRelation TableRelation `json:"tablerelation"` // 字段映射：由前端提供映射规则
//
//		GlobalSubmitInfo GlobalSubmitInfo `json:"globalsubmitinfo"`         // 全局任务配置信息
//		AdvancedConfig   AdvancedConfig   `json:"advancedconfig,omitempty"` // 采集任务高级配置
//
// }
type APiExtractdb struct {
	ApiDatabaseName string `json:"apidatabasename"` //api数据源名称
	ApiDatabaseID   string `json:"apidatabaseid"`   //api数据源id
	APiName         string `json:"apiname"`         //api名称
	APiID           string `json:"apiid"`           //APi ID
	HttpMethod      string `json:"httpmethod"`      //请求方式 "POST"
	Encoding        string `json:"encoding"`        // 编码格式 "UTF-8"
	AuthMethod      string `json:"authmethod"`      //认证方式 "no" ,"basic" ,"apikey" ,"token","jwt","oauth"
	AuthBasic       `json:"authbasic"`
	AuthApiKey      `json:"authapikey"`
	AuthBearerToken `json:"authbearertoken"`
	AuthJwt         `json:"authjwt"`
	AuthOauth       `json:"authoauth"`
	IncrementMethod int            `json:"incrementmethod"` // 采集方式,1:时间类型 2:自增序列
	IsPage          bool           `json:"ispage"`          //是否分页
	HeaderParam     []ServiceParam `json:"headerparam"`
	RequestParam    []ServiceParam `json:"requestparam"`
	ResponseParam   []ServiceParam `json:"responseparam"`
}
type APiSourcedb struct {
	ParsePath string `json:"parsepath"` // 解析路径
}
type AuthBasic struct {
	UserName string `json:"username"`
	Password string `json:"password"`
}
type AuthApiKey struct {
	Key   string `json:"key"`
	Value string `json:"value"`
	Site  int    `json:"site"` // 0请求头 1请求正文
}
type AuthBearerToken struct {
	Token string `json:"token"`
}
type AuthJwt struct {
	Algorithm string `json:"algorithm"`
	Secret    string `json:"secret"`
	Base64    bool   `json:"base64"`
}
type AuthOauth struct {
	TokenName      string `json:"tokenname"`      //访问令牌名称
	GrantType      int    `json:"granttype"`      //授权类型 0客户端 1密码
	AccessTokenURL string `json:"accesstokenurl"` //访问令牌URL
	ClientID       string `json:"clientid"`       //客户端id
	ClientSecret   string `json:"clientsecret"`   //客户端密码
	Scope          string `json:"scope"`          //作用域
	Site           int    `json:"site"`           // 0请求头 1请求正文
	UserName       string `json:"username"`
	Password       string `json:"password"`
}
type ServiceExternal struct {
	APiID          string         `json:"apiid"`
	ApiName        string         `json:"apiname"` //接口名称
	ProjectID      string         `json:"projectid"`
	APiDataBaseID  string         `json:"apidatabaseid"`    //API所属API数据库id
	ApiType        string         `json:"apitype"`          //http和https
	ExtUrl         string         `json:"exturl,omitempty"` //API服务访问的url
	Method         string         `json:"method"`           //第三方API的请求方式：get  post
	EncodingFormat string         `json:"encodingformat"`   //编码格式 UTF-8
	InputFormat    string         `json:"inputformat"`      //输入数据格式json
	OutputFormat   string         `json:"outputformat"`     //输出数据格式json
	HeaderParam    []ServiceParam `json:"headerparam"`
	RequestParam   []ServiceParam `json:"requestparam"`
	ResponseParam  []ServiceParam `json:"responseparam"`
	CreatedTime    string         `json:"createtime"` //创建时间
	UpdateTime     string         `json:"updatetime"` //更新时间
}

type ServiceParam struct {
	ParamID      string         `json:"paramid""`     //参数id标识用
	ParamName    string         `json:"paramname"`    //参数名称
	ArrNum       int            `json:"arrnum"`       //arr的层级 [][]string 2
	ArrType      string         `json:"arrtype"`      //arr最后一层的数据类型 string int object
	Type         string         `json:"type"`         //类型 array
	DefaultValue string         `json:"defaultvalue"` //默认值
	Required     bool           `json:"required"`     //是否必填，返回数据字段里为空
	Describe     string         `json:"describe"`     //参数说明
	IsDel        bool           `json:"isdel"`        //是否被删除，详情页面展示用
	IsNew        bool           `json:"isnew"`        //是否新增
	SubField     []ServiceParam `json:"subfield"`     //适合object
	//SubFields               [][]ServiceParam `json:"subfields"`               //适合[]object
	IncreFieldInfo []Increfield `json:"increfieldinfo"` // 前端要把增量加一层
	Value          string       `json:"value"`          //值或者动态参数脚本id
	//ValueForWeb int        `json:"valueforweb"` //前端保存的分页的参数值
	ParamType int `json:"paramtype"` //参数类型 0固定参数 1动态参数 2增量参数 3分页页码参数 4分页大小参数
	//重跑参数
	ReRun []ReRunInfo `json:"reruninfo"`
}
type ReRunInfo struct {
	ReRunID    string `json:"rerunid"`
	ReRenValue string `json:"rerunvalue"`
}

type Increfield struct {
	IncrementType           int      `json:"incrementtype"`           //增量方式 0基于上一次调度时间 1基于返回字段参数 2 基本本次调度时间
	IncrementPathID         []string `json:"incrementpathid"`         //返回字段路径ID
	IncrementPathName       []string `json:"incrementpathname"`       //返回字段路径
	TimeZone                string   `json:"timezone"`                //时区
	IncrementalInitialValue string   `json:"incrementalinitialvalue"` //增量初始值
	Offset                  int      `json:"offset"`                  //偏移量
	OffsetTime              string   `json:"offsettime"`              //偏移量单位 毫秒、秒、分、时、天
}

type TokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

type SimpleParam struct {
	ID               string           `json:"id"`
	GlobalSubmitInfo GlobalSubmitInfo `json:"globalsubmitinfo"`
	SchTypeStr       string           `json:"schtypestr"` //依赖任务的周期类型：年，月，周，日，时，分
}

type SimpleJob struct {
	Preid          []string      `json:"preid"`
	ScheduleType   int           `json:"scheduletype"`   // 执行策略类型,0 每隔某些时间;1 每天某个时间;2 每周某个时间;3 每月某个时间;4 自定义高级 **/1***;5 无
	ScheduleDetail string        `json:"scheduledetail"` // 执行策略详情
	ID             string        `json:"id"`
	AddSubmitInfo  AddSubmitInfo `json:"addsubmitinfo"`
	SchTypeStr     string        `json:"schtypestr"`     //依赖任务的周期类型：年，月，周，日，时，分
	FirstStartTime string        `json:"firststarttime"` //固定类型开始时间
}

type SimpleMetrics struct {
	ID               string           `json:"zbid"`
	GlobalSubmitInfo GlobalSubmitInfo `json:"globalsubmitinfo"`
	SchTypeStr       string           `json:"schtypestr"` //依赖任务的周期类型：年，月，周，日，时，分
}

type SimpleService struct {
	ID         string      `json:"id"`
	PushServer ServicePush `json:"pushserver"`
}

type ServicePush struct {
	PushStrategy LandStrategy `json:"pushstrategy"` //推送任务运行策略
}

type LandStrategy struct {
	DepJobInfo []DepJobInfo `json:"depjobinfo"` //依赖任务
}
