// Copyright (c) Datatom Software, Inc.(2017)
//
// Author:栾金阳（<EMAIL>）
//
// Creating Time:
package develop

import (
	"datatom.com/ants/httpdo/develop"
	. "datatom.com/ants/httphandler"
	"datatom.com/ants/source"
	"datatom.com/tools/common/code"
	"datatom.com/tools/common/tools"
	"datatom/gin.v1"
)

// 标签列表
func DevRecommendTags(c *gin.Context) {
	var sp source.DevRecommendTags
	err := c.BindJSON(&sp)

	userid, ok := c.Get("username")
	if ok {
		sp.User = userid.(string)
	}

	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if sp.Size == 0 {
		sp.Size = 200
	}
	results, err := develop.DevRecommendTags(sp)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code":   Success,
		"result": results,
	}

	c.<PERSON>(Success, res)
}

// 判断哪些脚本需要删除, 这个只有内部使用，没有判断权限,
// 拽出的时候如果是用户临时拽入产生的脚本，采用删除策略
func DevDelif(c *gin.Context) {
	var sp source.DevDelif
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	Authorization := c.Request.Header["Authorization"][0]
	if len(sp.Ids) == 0 {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "id is null"})
		return
	}

	result, err := develop.DevDelif(sp, Authorization)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code":   Success,
		"result": result,
	}

	c.JSON(Success, res)
}

// 判断哪些脚本需要恢复
func DevRecif(c *gin.Context) {
	var sp source.DevRecif
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if len(sp.Ids) == 0 {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "id is null"})
		return
	}

	result, err := develop.DevRecif(sp)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code":   Success,
		"result": result,
	}

	c.JSON(Success, res)
}

// 脚本生成版本
func GenerateVersion(c *gin.Context) {
	var sp source.DevId
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	err = develop.DevGenerateVersion(sp.Id)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code": code.Success,
	}
	c.JSON(code.Success, res)
}

// 列举版本
func ListHistory(c *gin.Context) {
	var sp source.DevId
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	devList, err := develop.ListDevHistory(sp.Id)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code":   code.Success,
		"result": devList,
	}
	c.JSON(code.Success, res)
}
