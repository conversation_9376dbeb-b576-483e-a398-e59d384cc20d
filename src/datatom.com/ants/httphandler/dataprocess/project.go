package dataprocess

import (
	common2 "datatom.com/tools/common/feedback"
	//"datatom.com/ants/gin-jwt"
	"errors"
	"fmt"
	"strings"

	"datatom/gin.v1"

	"datatom.com/ants/common"
	"datatom.com/ants/httpdo/datagovern"
	"datatom.com/ants/httpdo/dataprocess"
	"datatom.com/ants/httpdo/metacenter"
	. "datatom.com/ants/httphandler"
	"datatom.com/ants/logger"
	"datatom.com/ants/source"
	AssetMgm "datatom.com/asset/source"
	"datatom.com/tools/common/tools"
)

func CheckEngine(c *gin.Context) {
	var info source.CheckEngine
	err := c.BindJ<PERSON>(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	str, err := dataprocess.CheckEngine(info.Num)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  str,
	}

	if str != "" {
		response = gin.H{
			"code": Conflict,
			"msg":  str,
		}
	}
	c.<PERSON>(Success, response)
}

func ListLayer(c *gin.Context) {
	array, err := dataprocess.ListLayer()
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": array,
	}
	if array == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}

	c.JSON(Success, response)
}

func GetDatabase(c *gin.Context) {
	//改获取底层数据库到获取数据中心已添加的库
	var info source.EngineInfo
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Layerid == "" {
		CjsonParamError(c, errors.New("param 'engineid' is missing"))
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": []string{},
	}
	resp, err := dataprocess.GetAssetDatabase(info.Layerid)
	if err != nil {
		c.JSON(Success, response)
		return
	}

	response = gin.H{
		"code":   Success,
		"result": resp,
	}
	if resp == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}

	c.JSON(Success, response)
}

func CreateProject(c *gin.Context) {
	var info source.ProjectInfo
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--新建project"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	info.ProjectID = c.GetString("projectid")

	var searchtext source.SearchJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	msg, exist := IsSpe(info.ProjectName)
	if exist {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": msg})
		FeedBack(feed, "project名存在特殊字符", false)
		return
	}

	if len(info.DbInfo) == 0 {
		var response = gin.H{
			"code": Conflict,
			"msg":  "请选择可操作资源",
		}
		c.JSON(Conflict, response)
		FeedBack(feed, "操作资源不能为空", false)
		return
	}

	array := []source.ProjectInfo{}
	array, err = dataprocess.ListProject(searchtext)
	if err != nil {
		CeaglesError(c, err)
		return
	}
	var addtag bool
	addtag = true
	for i := 0; i < len(array); i++ {
		if array[i].ProjectName == info.ProjectName {
			addtag = false
			break
		}
	}
	if addtag == false {
		var response = gin.H{
			"code": Conflict,
			"msg":  "项目名已存在",
		}
		c.JSON(Conflict, response)
		FeedBack(feed, "project名已存在", false)
		return
	}

	err = dataprocess.CreatProject(info)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  "创建成功",
	}
	FeedBack(feed, "新建project成功", true)
	c.JSON(Success, response)
}

func ListUser(c *gin.Context) {
	array, err := dataprocess.GetUser()
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": array,
	}

	if array == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}

	c.JSON(Success, response)
}

func DeleteProject(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--删除project"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var info source.TaskUpdate
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	//-----------------------------------
	//检查用户是否有权限操作project
	//userid, _ := c.Get("userid")

	exist := dataprocess.Check(info.Id, userid.(string))
	if exist {
		c.JSON(PermissionDenied, gin.H{"code": PermissionDenied, "msg": "您没有权限删除该项目"})
		FeedBack(feed, "没有权限删除project", false)
		return
	}

	res, _ := dataprocess.CheckTask(info.Id, info.Projectid)
	if len(res) != 0 {
		var response = gin.H{
			"code": Conflict,
			"msg":  "该project存在task任务，不可删除",
		}
		c.JSON(Conflict, response)
		FeedBack(feed, "project存在task任务，删除失败", false)
		return
	}
	// for i := 0; i < len(res); i++ {
	// 	if res[i].Submitted == true {
	// 		var response = gin.H{
	// 			"code": Conflict,
	// 			"msg":  "该project存在上线任务，不可删除",
	// 		}
	// 		c.JSON(Conflict, response)
	// 		return
	// 	}
	// }

	err = dataprocess.DeleteProject(info.Id)
	if err != nil {
		CeaglesError(c, err)
		FeedBack(feed, "删除project失败", false)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  "删除成功",
	}
	FeedBack(feed, "删除project成功", true)
	c.JSON(Success, response)
}

func UpdateProject(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--修改project"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var info source.ProjectInfo
	var search source.SearchJson
	//获取projectid
	search.ProjectID = c.GetString("projectid")

	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	//-----------------------------------
	//检查用户是否有权限操作project
	//userid, _ := c.Get("userid")

	exist := dataprocess.Check(info.Id, userid.(string))
	if exist {
		c.JSON(PermissionDenied, gin.H{"code": PermissionDenied, "msg": "您没有权限操作该项目"})
		FeedBack(feed, "没有权限修改project", false)
		return
	}

	if info.ProjectName != "" {
		msg, exist := IsSpe(info.ProjectName)
		if exist {
			c.JSON(Conflict, gin.H{"code": Conflict, "msg": msg})
			FeedBack(feed, "project名存在特殊字符", false)
			return
		}
	}

	array := []source.ProjectInfo{}
	array, err = dataprocess.ListProject(search)
	search.Id = info.Id
	array1, _ := dataprocess.ListProject(search)

	if err != nil {
		CeaglesError(c, err)
		return
	}
	var addtag bool
	addtag = true
	for i := 0; i < len(array); i++ {
		if array1[0].ProjectName == info.ProjectName {
			addtag = true
		} else if array[i].ProjectName == info.ProjectName {
			addtag = false
			break
		}
	}
	if !addtag {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": "项目名已存在"})
		FeedBack(feed, "project名已存在", false)
		return
	}

	exist, _ = dataprocess.CheckDatabase(info.Id, array1[0].DbInfo, info.DbInfo)
	if exist {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": "资源正在被使用，不可修改"})
		FeedBack(feed, "资源正在被使用，不可修改", false)
		return
	}

	//var param []EngineInfo
	err = dataprocess.UpdateProject(info.Id, info.ProjectName, info.Describe, info.Userinfo)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	//数据源为空时报错
	if len(info.DbInfo) == 0 {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": "请选择可操作资源"})
		FeedBack(feed, "可操作资源不能为空", false)
		return
	}
	err = dataprocess.UpdateDb(info.Id, info.DbInfo)
	if err != nil {
		CeaglesError(c, err)
		FeedBack(feed, "修改失败", false)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  "修改成功",
	}
	FeedBack(feed, "修改成功", true)
	c.JSON(Success, response)
}

func SearchProject(c *gin.Context) {
	var info source.SearchJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	userid, ok := c.Get("userid")
	if ok {
		fmt.Println("用户id：", userid)
	}

	//获取projectid
	info.ProjectID = c.GetString("projectid")

	var array []source.ProjectInfo
	array, err = dataprocess.ListProjectByuid(info, userid.(string))
	if err != nil {
		CeaglesError(c, err)
		return
	}
	var response = gin.H{
		"code":   Success,
		"result": array,
	}
	if array == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}

	c.JSON(Success, response)
}

func CreateTask(c *gin.Context) {
	var info source.TaskInfo
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--新建task"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
		info.User = userid.(string)
	}

	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	exist := dataprocess.Check(info.Projectid, info.User)
	if exist {
		c.JSON(PermissionDenied, gin.H{"code": PermissionDenied, "msg": "您没有权限操作该项目"})
		return
	}

	var uname string
	username, ok := c.Get("username")
	if ok {
		uname = username.(string)
	}

	projectID := c.GetString("projectid")

	msg, exist := IsSpe(info.TaskName)
	if exist {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": msg})
		FeedBack(feed, "task名存在特殊字符", false)
		return
	}

	res, _ := dataprocess.CheckTask(info.Projectid, info.Id)
	var addtag bool
	addtag = true
	for i := 0; i < len(res); i++ {
		if res[i].TaskName == info.TaskName {
			addtag = false
			break
		}
	}
	if addtag == false {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": "任务名称已存在"})
		FeedBack(feed, "task名已存在", false)
		return
	}

	err = dataprocess.CreateTask(info, uname, projectID)
	if err != nil {
		CjsonUnkownError(c, err)
		FeedBack(feed, "新建task失败", false)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  "创建成功",
	}

	FeedBack(feed, "新建task成功", true)
	c.JSON(Success, response)
}

func DeleteTask(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--删除task"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var info source.TaskDelete
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	if len(info.Ids) == 0 {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	for _, id := range info.Ids {
		err := dataprocess.DeleteTask(id, info.DeleteBasic)
		if err != nil {
			FeedBack(feed, "删除task失败", false)
			CeaglesError(c, err)
			return
		}
	}
	var response = gin.H{
		"code": Success,
		"msg":  "删除成功",
	}

	FeedBack(feed, "删除task成功", true)
	c.JSON(Success, response)
}

func UpdateTask(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--修改task"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var info source.TaskUpdate
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	if info.Newname != "" {
		msg, exist := IsSpe(info.Newname)
		if exist {
			c.JSON(Conflict, gin.H{"code": Conflict, "msg": msg})
			FeedBack(feed, "task名存在特殊字符", false)
			return
		}
	}

	dplab, err := dataprocess.DPInfo(info.Id)
	if err != nil {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "ID不存在"})
		return
	}

	res, _ := dataprocess.CheckTask(info.Projectid, info.Id)
	var addtag bool
	addtag = true
	if res[0].TaskName == info.Newname {
		addtag = true
	} else {
		info.Projectid = res[0].Projectid
		resp, _ := dataprocess.CheckTask(info.Projectid, info.Id)
		for i := 0; i < len(resp); i++ {
			if resp[i].TaskName == info.Newname {
				addtag = false
				break
			}
		}
	}
	if addtag == false {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": "task名已存在"})
		FeedBack(feed, "task名已存在", false)
		return
	}

	err = dataprocess.UpdateTask(info, dplab)
	// if result == "conflict" {
	// 	c.JSON(Conflict, gin.H{"code": Conflict, "msg": err.Error()})
	// 	return
	// }
	if err != nil {
		CeaglesError(c, err)
		FeedBack(feed, "task修改失败", false)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  "修改成功",
	}
	FeedBack(feed, "task修改成功", true)
	c.JSON(Success, response)
}

func SearchTask(c *gin.Context) {
	var info source.TaskSearch
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	userid, ok := c.Get("userid")
	if ok {
		info.User = userid.(string)
		//feed.Userid = userid.(string)
	}

	exist := dataprocess.Check(info.Id, info.User)
	if exist {
		c.JSON(PermissionDenied, gin.H{"code": PermissionDenied, "msg": "您没有权限访问该项目"})
		return
	}

	msg, exist := IsSpe(info.SearchText)
	if exist {
		c.JSON(Conflict, gin.H{"code": Conflict, "msg": msg})
		return
	}

	if info.Page == 0 {
		info.Page = 1
	}
	if info.Perpage == 0 {
		info.Perpage = 10
	}

	result, total, err := dataprocess.SearchTask(info)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": result,
		"total":  total,
	}

	if result == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}

	c.JSON(Success, response)
}

func CountTask(c *gin.Context) {
	var info source.ProjectInfo
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}

	resp, err := dataprocess.CountTask(info.Id)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": resp,
	}
	c.JSON(Success, response)
}

func ListTags(c *gin.Context) {
	resp, err := dataprocess.ListTags()
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": resp,
	}

	c.JSON(Success, response)
}

func Submit(c *gin.Context) {
	var info source.WfSubmit
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--上线"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
		info.User = userid.(string)
	}

	err := c.BindJSON(&info)

	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	username, ok := c.Get("username")
	if ok {
		info.Username = username.(string)
	}

	if info.Id == "" {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "id is null"})
		FeedBack(feed, "请求参数id为空", false)
		return
	}

	dplab, err := dataprocess.DPInfo(info.Id)
	if err != nil {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "ID不存在"})
		// FeedBack(feed, err.Error(), false)
		return
	}

	results, err := dataprocess.DpSubmit(info, dplab)
	if err != nil {
		c.JSON(Success, gin.H{"code": SubmitFail, "msg": err.Error()})
		FeedBack(feed, err.Error(), false)
		return
	}

	res := gin.H{
		"code":   Success,
		"result": results,
	}

	c.JSON(Success, res)
	FeedBack(feed, "上线"+dplab.Name, true)
}

func Down(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--下线"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var sp source.DpId
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	Authorization := c.Request.Header["Authorization"][0]
	if sp.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}

	// 	failids := []gin.H{}

	//for _, v := range sp.Ids {
	dplab, err := dataprocess.DPInfo(sp.Id)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	res, err := dataprocess.DpDown(dplab, Authorization)
	if err != nil {
		var response = gin.H{
			"code":   Success,
			"result": res,
		}
		c.JSON(Success, response)
		FeedBack(feed, res, false)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": "下线成功",
	}
	FeedBack(feed, "下线"+dplab.Name, true)
	c.JSON(Success, response)
}

// 运维管理批量下线
func BatchDown(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--下线"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var sp source.DpIds
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	Authorization := c.Request.Header["Authorization"][0]
	if len(sp.Ids) == 0 {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}

	failids := []gin.H{}

	for _, v := range sp.Ids {
		dplab, err := dataprocess.DPInfo(v)
		if err != nil {
			logger.Info.Println("治理任务" + v + ":不存在")
			continue
		}
		res, err := dataprocess.DpDown(dplab, Authorization)
		if err != nil {
			r := gin.H{
				"name":   dplab.Name,
				"reason": res,
			}
			failids = append(failids, r)
			continue
		}
	}

	response := gin.H{
		"code":   Success,
		"result": "全部下线成功",
	}
	if len(failids) != 0 {
		response = gin.H{
			"code":   Success,
			"result": failids,
		}
	}
	FeedBack(feed, "下线成功", true)
	c.JSON(Success, response)
}

func ListDatabase(c *gin.Context) {
	var info source.ProjectInfo
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	resp, err := dataprocess.ListDatabase(info.Id)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": resp,
	}

	if resp == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}
	c.JSON(Success, response)

}

func ListTbinfo(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	tbinfo, err := dataprocess.ListTbinfo(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": tbinfo,
	}
	c.JSON(Success, res)
}

func ListSchema(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Engineid == "" {
		CjsonParamError(c, errors.New("param 'engineid' is missing"))
		return
	}
	if info.Dbname == "" {
		CjsonParamError(c, errors.New("param 'dbname' is missing"))
		return
	}
	result, err := dataprocess.ListSchema(info)
	if err != nil {
		CdbError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": result,
	}
	c.JSON(Success, res)
}

// func CheckTimefield(c *gin.Context) {
// 	var info source.FieldInfo
// 	err := c.BindJSON(&info)
// 	if err != nil {
// 		CjsonParamError(c, err)
// 		return
// 	}
// 	result := dataprocess.CheckTimefield(info)
// 	if err != nil {
// 		logger.Error.Println(err)
// 		CdbError(c, err)
// 		return
// 	}
// 	if result == false {
// 		c.JSON(Success, gin.H{"code": ParamError, "msg": "请选择时间字段作为增量字段"})
// 		return
// 	}
// 	res := gin.H{
// 		"code": Success,
// 	}
// 	c.JSON(Success, res)
// }

func ListFun(c *gin.Context) {
	var info source.SearchJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	result, err := dataprocess.ListFun(info.Id)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": result,
	}
	c.JSON(Success, res)

}

func Preview(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	if info.Engineid == "" {
		CjsonParamError(c, errors.New("param 'engineid' is missing"))
		return
	}
	if info.Dbname == "" {
		CjsonParamError(c, errors.New("param 'dbname' is missing"))
		return
	}
	// if info.Schema == "" {
	// 	CjsonParamError(c, errors.New("param 'schema' is missing"))
	// 	return
	// }
	if info.Tbname == "" {
		CjsonParamError(c, errors.New("param 'tbname' is missing"))
		return
	}
	result, err := dataprocess.Preview(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": result,
	}
	c.JSON(Success, res)

}

// -----------------------------------------------------------------------------------
func KeyDuplicate(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	if info.Engineid == "" {
		CjsonParamError(c, errors.New("param 'engineid' is missing"))
		return
	}
	if info.Dbname == "" {
		CjsonParamError(c, errors.New("param 'dbname' is missing"))
		return
	}
	if info.Tbname == "" {
		CjsonParamError(c, errors.New("param 'tbname' is missing"))
		return
	}
	result := dataprocess.KeyDuplicate(info)
	if err != nil {
		CdbError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": result,
	}
	c.JSON(Success, res)

}

//-----------------------------------------------------------------------------------

func SaveTable(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	if info.Engineid == "" {
		CjsonParamError(c, errors.New("param 'engineid' is missing"))
		return
	}
	if info.Dbname == "" {
		CjsonParamError(c, errors.New("param 'dbname' is missing"))
		return
	}
	// if info.Schema == "" {
	// 	CjsonParamError(c, errors.New("param 'schema' is missing"))
	// 	return
	// }
	if info.Tbname == "" {
		CjsonParamError(c, errors.New("param 'tbname' is missing"))
		return
	}

	Authorization := c.Request.Header["Authorization"][0]

	info.ProjectID = c.GetString("projectid")

	_, err = dataprocess.SaveTable(info, Authorization)
	if err != nil {
		CeaglesError(c, err)
		return
	}
	res := gin.H{
		"code": Success,
		"msg":  "保存成功",
	}
	c.JSON(Success, res)
}

func GetTbname(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}

	lab, err := dataprocess.ProDevInfo(info.Id)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	// result := gin.H{
	// 	"table": lab.Tbinfo,
	// }

	res := gin.H{
		"code":   Success,
		"result": lab.Tbinfo,
	}
	c.JSON(Success, res)
}

func WhereJudge(c *gin.Context) {
	var sp source.TbJson
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	//数据服务，查询引擎ID
	if sp.SourceTBType != 0 {
		if sp.SourceTBType == 2 {
			sourcetbid, err := AssetMgm.QuerySourceTBIDByInventory(sp.SourceTBID)
			if err != nil {
				tools.CommonError(c, err)
				return
			}
			//此处更新为资源表的ID用于后续表操作
			sp.SourceTBID = sourcetbid
		}
		//获取源端表信息：表连接、字段、类型等信息
		sourceTb, err := AssetMgm.QuerySourceTBInfoByTBID(sp.SourceTBID, "")
		if err != nil {
			tools.CommonError(c, err)
			return
		}
		sp.Engineid = sourceTb.Engineid
		sp.Tbname = sourceTb.TableName
		sp.Dbname = sourceTb.Dbname
		sp.Schema = sourceTb.Schame
	}
	result, err := dataprocess.WhereJudge(sp)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": result,
	}
	c.JSON(Success, res)
}

func ListTask(c *gin.Context) {
	var info source.TaskSearch
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}

	// exist := dataprocess.Check(info.Id, info.User)
	// if exist {
	// 	c.JSON(PermissionDenied, gin.H{"code": PermissionDenied, "msg": "您没有权限访问该项目"})
	// 	return
	// }

	result, err := dataprocess.ListTask(info)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": result,
	}

	if result == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}

	c.JSON(Success, response)
}

func ListModule(c *gin.Context) {
	var info source.DpId
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}

	result, err := dataprocess.ListModule(info.Id)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": result,
	}

	if result == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}

	c.JSON(Success, response)
}

func Save(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--保存"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var info source.DpSave
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Id == "" {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "param 'id' is missing"})
		return
	}

	lab, err := dataprocess.DPInfo(info.Id)

	var length int
	length = len(info.SubJobs)

	if length <= len(lab.Label.SubJobs) {
		err = dataprocess.OtherJudgeStatus(lab.Label.SubJobs, info.SubJobs)
	}

	sou, err := dataprocess.Save(info, lab)
	if err != nil {
		tools.CommonError(c, err)
		if !info.IsRealtime {
			FeedBack(feed, "保存失败", false)
		}
		return
	}

	res := gin.H{
		"code":   Success,
		"result": sou,
	}

	if !info.IsRealtime {
		FeedBack(feed, "保存成功", true)
	}

	c.JSON(Success, res)
}

func DpContent(c *gin.Context) {
	var sp source.DpId
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	//userid, _ := c.Get("userid")

	if sp.Id == "" {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "param 'id' is missing"})
		return
	}

	lab, err := dataprocess.DPInfo(sp.Id)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	var content []source.SubJob
	var array source.SubJob

	for i := 0; i < len(lab.Label.SubJobs); i++ {

		result, _ := dataprocess.SearchTags(lab.Label.SubJobs[i].Cid)

		modstatus := dataprocess.JudgeStatus(result.Status, result.Module, lab.Label.SubJobs[i].Prev)

		array = lab.Label.SubJobs[i]
		array.ModStatus = modstatus
		array.Tags = result.Tag
		array.Module = result.Module
		array.Cmd = result.Cmd

		content = append(content, array)
	}

	res := gin.H{
		"code": Success,
		"result": gin.H{
			"content": content,
			//"identify":  identify,
			"lock":      lab.Label.Star,
			"submitted": lab.Label.Submitted,
		},
	}

	c.JSON(Success, res)
}

// 运行
func DpRun(c *gin.Context) {
	// var feed common2.Feed
	// feed.Module = "workflow"
	// feed.Subject = "工作流--运行工作流"

	// userid, ok := c.Get("userid")
	// if ok {
	// 	feed.Userid = userid.(string)
	// }

	var sp source.DpRun
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		//FeedBack(feed, "请求参数解析有误", false)
		return
	}
	lab, err := dataprocess.DPInfo(sp.Id)
	sp.SubJobs = lab.SubJobs

	if sp.Id == "" {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "param 'id' is missing"})
		//FeedBack(feed, "请求参数id为空", false)
		return
	}

	// lab, err := dataprocess.DPInfo(sp.Id)
	// if err != nil {
	// 	c.JSON(UnknowError, gin.H{"msg": "ID不存在"})
	// 	FeedBack(feed, "请求参数id:"+sp.Id+"不存在", false)
	// 	return
	// }

	result, err := dataprocess.DpRun(sp)
	if err != nil {
		tools.CommonError(c, err)
		//FeedBack(feed, err.Error(), false)
		return
	}

	res := gin.H{
		"code":   Success,
		"result": result,
	}

	c.JSON(Success, res)
	//FeedBack(feed, "运行"+lab.Name+"成功", true)
}

// 停止
func DpStop(c *gin.Context) {
	// var feed common2.Feed
	// feed.Module = "workflow"
	// feed.Subject = "工作流--停止工作流"

	// userid, ok := c.Get("userid")
	// if ok {
	// 	feed.Userid = userid.(string)
	// }

	var sp source.DpId
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		//FeedBack(feed, "请求参数解析有误", false)
		return
	}

	if sp.Id == "" {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "param 'id' is missing"})
		//FeedBack(feed, "请求参数id为空", false)
		return
	}

	// lab, err := dataprocess.DPInfo(sp.Id)
	// if err != nil {
	// 	c.JSON(ParamError, gin.H{"msg": "ID不存在"})
	// 	//FeedBack(feed, "请求参数id:"+sp.Id+"不存在", false)
	// 	return
	// }

	sou, err := dataprocess.DpStop(sp.Id)
	if err != nil {
		tools.CommonError(c, err)
		//FeedBack(feed, err.Error(), false)
		return
	}

	res := gin.H{
		"code":   Success,
		"result": sou,
	}

	c.JSON(Success, res)
	//FeedBack(feed, "停止"+wflab.Name+"成功", true)
}

func UpdateModule(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common.GovernMD
	feed.Subject = "数据治理--修改模组"

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}

	var info source.UpModule
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}
	if info.Name != "" {
		msg, exist := IsSpe(info.Name)
		if exist {
			c.JSON(Success, gin.H{"code": Conflict, "msg": msg})
			FeedBack(feed, "模组名存在特殊字符", false)
			return
		}
	}

	//array := []source.UpModule{}
	res, _ := dataprocess.CheckModule(info)
	info.Module = res[0].Module
	info.Taskid = res[0].Taskid
	resp, _ := dataprocess.CheckModule(info)
	var addtag bool
	addtag = true
	for i := 0; i < len(resp); i++ {
		if res[0].Name == info.Name {
			addtag = true
		} else if resp[i].Name == info.Name {
			addtag = false
			break
		}
	}
	if addtag == false {
		var response = gin.H{
			"code": Conflict,
			"msg":  "模组名已存在",
		}
		c.JSON(Success, response)
		FeedBack(feed, "模组名已存在", false)
		return
	}

	err = dataprocess.UpdateModule(info)
	if err != nil {
		tools.CommonError(c, err)
		FeedBack(feed, "修改模组失败", false)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  "修改成功",
	}
	FeedBack(feed, "修改模组成功", true)
	c.JSON(Success, response)

}

func ListMTags(c *gin.Context) {
	var info source.DpId
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	resp, err := dataprocess.ListMTags(info.Id)
	if err != nil {
		CeaglesError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": resp,
	}

	c.JSON(Success, response)
}

func Lock(c *gin.Context) {
	var info source.DpId
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}

	dplab, err := dataprocess.DPInfo(info.Id)

	result, err := dataprocess.Lock(dplab)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code": Success,
		"msg":  result,
	}
	c.JSON(Success, res)
}

func ShowResult(c *gin.Context) {
	var info source.DpId
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	lab, err := dataprocess.ProDevInfo(info.Id)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	result, err := dataprocess.ShowResult(lab)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code": Success,
		"msg":  result,
	}
	c.JSON(Success, res)

}

func BatchSave(c *gin.Context) {
	var sp source.DpId
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if sp.Id == "" {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "param 'id' is missing"})
		return
	}

	lab, err := dataprocess.DPInfo(sp.Id)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	msg, err := dataprocess.BatchSave(lab)
	if err != nil {
		tools.CommonError(c, err)
		//FeedBack(feed, err.Error(), false)
		return
	}

	if msg != "保存成功" {
		c.JSON(Success, gin.H{"code": Conflict, "msg": msg})
		return
	}

	var res = gin.H{
		"code": Success,
		"msg":  msg,
	}

	c.JSON(Success, res)

}

func Listmodule(c *gin.Context) {
	var info source.TableJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Taskid == "" {
		c.JSON(ParamError, gin.H{"code": ParamError, "msg": "param 'taskid' is missing"})
		return
	}
	info.ProjectID = c.GetString("projectid")

	resp, err := dataprocess.Listmodule(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": resp,
	}

	c.JSON(Success, response)
}

func ListSubject(c *gin.Context) {
	resp, err := dataprocess.ListSubject()
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	var response = gin.H{
		"code":   Success,
		"result": resp,
	}

	c.JSON(Success, response)
}

func OutputPreview(c *gin.Context) {
	var info source.OutputSave
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Moduleid == "" {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "param 'moduleid' is missing"})
		return
	}

	lab, err := dataprocess.ProDevInfo(info.Moduleid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	engineinfo, err := dataprocess.GetInstance(lab.Newtbinfo.Engineid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	_, showsql, err := dataprocess.CreateSql(info, lab, engineinfo)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	cols, data, _, err := dataprocess.OutputPreview(info, lab, showsql, engineinfo)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	var response = gin.H{
		"code": Success,
		"result": gin.H{
			"columns": cols,
			"data":    data,
		},
	}

	c.JSON(Success, response)
}

// 6.0 版本落地模组保存
func OutputSaveNew(c *gin.Context) {
	var info source.OutputSave
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	logger.Info.Println("----info.Sourcedb.PartitionField:--------", info.Sourcedb.PartitionField)
	if info.Moduleid == "" {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "param 'moduleid' is missing"})
		return
	}

	for _, v := range info.Fieldinfo {
		if IsTableSpe(v.Newdescribe) {
			c.JSON(Success, gin.H{"code": ParamError, "msg": "字段注释存在特殊字符,请修改"})
			return
		}
	}

	//直接用接口中的引擎ID
	lab, err := dataprocess.ProDevInfo(info.Moduleid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	db := new(source.ProWatchTableInfo)
	db.Pre = info.Pre
	db, err = datagovern.InputInfoGet(db)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	lab.Tbinfo.Tbname = db.Table
	lab.Tbinfo.Dbname = db.Initdb
	lab.Tbinfo.Schema = db.Newtbinfo.Schema
	lab.LastModule = db.Module
	lab.StandardFieldinfos = db.StandardFieldinfos

	//此处生成 content 和 finalcmd
	err = dataprocess.OutputSaveNew(&info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	logger.Info.Println("----info.Sourcedb.PartitionField22:--------", info.Sourcedb.PartitionField)
	lab.Newtbinfo.Engineid = info.Finaltable.Engineid
	lab.Newtbinfo.Dbname = info.Finaltable.Dbname
	lab.Newtbinfo.Schema = info.Finaltable.SourceDB

	//获取引擎信息
	engineinfo, err := dataprocess.GetInstance(lab.Newtbinfo.Engineid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	lab.Newtbinfo.Tbtype = engineinfo.Enginename

	//创建 tmp_test_run 表统一放在外面，此时原表一定存在
	info.ProjectID = c.GetString("projectid")
	err = dataprocess.CreateTmpTB(info, lab)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	info.Content = strings.Replace(info.Content, common.XXN, "", -1)
	logger.Info.Println("----info.Content--------", info.Content)

	finaltbname := info.Finaltable.Tbname
	finaltbname_tmp_testrun := info.Finaltable.Tbname + "_tmp_testrun"
	//info.Content = strings.Replace(info.Content, finaltbname, finaltbname_tmp_testrun, -1)

	info.Content = strings.Replace(info.Content, info.Finaltable.Dbname+"."+finaltbname, info.ProjectID+"_basedb"+"."+finaltbname_tmp_testrun, -1)
	info.Content = strings.Replace(info.Content, `"`+info.Finaltable.SourceDB+`"."`+finaltbname+`"`, `"`+info.ProjectID+"_basedb"+`"."`+finaltbname_tmp_testrun+`"`, -1)

	info.Content = strings.Replace(info.Content, info.Finaltable.SourceDB+`.\"`+finaltbname+`\"`, `\"`+info.ProjectID+"_basedb"+`\".\"`+finaltbname_tmp_testrun+`\"`, -1)

	//保存后即为已有表
	info.UseMetaTB = true
	logger.Info.Println("----info.Sourcedb.PartitionField3:--------", info.Sourcedb.PartitionField)
	Authorization := c.Request.Header["Authorization"][0]
	msg, err := dataprocess.OutputSave(info, lab, engineinfo, Authorization)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	if msg == "" {
		tools.CommonError(c, err)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  msg,
	}

	c.JSON(Success, response)
	return
}

func OutputSave(c *gin.Context) {
	var info source.OutputSave
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Moduleid == "" {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "param 'moduleid' is missing"})
		return
	}

	////表字段描述、表描述特殊字符判断
	//if IsTableSpe(info.Finaltable.Describe) {
	//	c.JSON(Success, gin.H{"code": ParamError, "msg": "表描述存在特殊字符,请修改"})
	//	return
	//}

	for _, v := range info.Fieldinfo {
		if IsTableSpe(v.Newdescribe) {
			c.JSON(Success, gin.H{"code": ParamError, "msg": "字段注释存在特殊字符,请修改"})
			return
		}
	}

	//直接用接口中的引擎ID
	lab, err := dataprocess.ProDevInfo(info.Moduleid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	lab.Newtbinfo.Engineid = info.Finaltable.Engineid
	lab.Newtbinfo.Dbname = info.Finaltable.Dbname
	lab.Newtbinfo.Schema = info.Finaltable.SourceDB

	db := new(source.ProWatchTableInfo)
	db.Pre = info.Pre
	db, err = datagovern.InputInfoGet(db)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	//lab.Tbinfo.Tbname = fmt.Sprintf(`%s_basedb.%s`, c.GetString("projectid"), db.Table)
	lab.Tbinfo.Tbname = db.Table
	lab.Tbinfo.Dbname = db.Initdb
	lab.Tbinfo.Schema = db.Newtbinfo.Schema
	lab.LastModule = db.Module
	//获取引擎信息
	engineinfo, err := dataprocess.GetInstance(lab.Newtbinfo.Engineid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	//var isExist, needBuild bool

	// hive分区字段加入字段中
	if engineinfo.Databasetype == common.Hive {
		for _, v := range info.PartitionField {
			if v.Ispreset != "notpreset" {
				info.Outputfields = append(info.Outputfields, v.Name)
			}
		}
	}

	if !info.UseMetaTB { //新建表
		//直接新建，不考虑重名或删表
		id, err := dataprocess.AddTable(info, false, true, lab, engineinfo.Databasetype)
		if err != nil {
			tools.CommonError(c, err)
			return
		}

		//补充使用的元数据表ID
		info.MetaTBID = id

		//补充字段映射规则
		var fieldMap source.FieldMap
		fieldMap.Input = make([]string, len(info.Changedinfo))
		fieldMap.Output = make([]string, len(info.Changedinfo))

		for i, j := range info.Changedinfo {
			fieldMap.Input[i] = j.Fieldname
			fieldMap.Output[i] = j.Fieldname
		}
		info.FieldMap = fieldMap
	}
	//创建 tmp_test_run 表统一放在外面，此时原表一定存在
	info.ProjectID = c.GetString("projectid")
	err = dataprocess.CreateTmpTB(info, lab)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	/*	v4.7 逻辑
		输出表保存时，判断表是否重名
			不重名：提示“保存成功”
			重名：判断表结构是否符合
				符合：提示“保存成功”，用户自行选择覆盖或追加策略
				不符合：提示“同名输出表已存在且表结构不一致，继续保存会删除重建该表，是否保存？”
					保存，提示“保存成功”
					取消，关闭弹框停留在表输出页面
	*/

	/*  v4.7 逻辑
	1.不重名需要新建表
	2.重名表结构一致不需要新建表
	3.重名表结构不一致但用户选择了继续，删除再建表
	*/

	/* v5.1 逻辑
	   1.界面上可选择新建表或已有表，移除重名判定和表结构判定
	*/

	Authorization := c.Request.Header["Authorization"][0]

	info.Finaltable.Tbname = info.Finaltable.Tbname + common.TMPTestRun

	info.Content, info.OutputShowsql, err = dataprocess.CreateSql(info, lab, engineinfo)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	info.Content = strings.Replace(info.Content, common.XXN, "", -1)
	logger.Info.Println("----info.Content--------", info.Content)
	//试运行,真实表替换为临时表
	//finaltbname := `"` + info.Finaltable.SourceDB + `"."` + info.Finaltable.Tbname + `"`
	//finaltbname_tmp_testrun := `"` + info.Finaltable.SourceDB + `"."` + info.Finaltable.Tbname + "_tmp_testrun" + `"`
	//info.Content = strings.Replace(info.Content, finaltbname, finaltbname_tmp_testrun, -1)

	info.Finaltable.Tbname = strings.Replace(info.Finaltable.Tbname, common.TMPTestRun, "", -1)

	//finaltbname := info.Finaltable.Tbname
	finaltbname_tmp_testrun := info.Finaltable.Tbname + "_tmp_testrun"
	// info.Content = strings.Replace(info.Content, finaltbname, finaltbname_tmp_testrun, -1)

	info.Content = strings.Replace(info.Content, info.Finaltable.Dbname+"."+finaltbname_tmp_testrun, info.ProjectID+"_basedb"+"."+finaltbname_tmp_testrun, -1)
	info.Content = strings.Replace(info.Content, `"`+info.Finaltable.SourceDB+`"."`+finaltbname_tmp_testrun+`"`, `"`+info.ProjectID+"_basedb"+`"."`+finaltbname_tmp_testrun+`"`, -1)

	logger.Info.Println("----info.Content--finaltbname_tmp_testrun------", info.Content)
	//info.Finaltable.Schema = lab.Newtbinfo.Schema
	//info.Finaltable.Engineid = lab.Newtbinfo.Engineid

	//保存后即为已有表
	info.UseMetaTB = true

	msg, err := dataprocess.OutputSave(info, lab, engineinfo, Authorization)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	if msg == "" {
		tools.CommonError(c, err)
		return
	}

	var response = gin.H{
		"code": Success,
		"msg":  msg,
	}

	c.JSON(Success, response)

}

func OutputInfo(c *gin.Context) {
	var info source.TableJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Moduleid == "" {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "param 'moduleid' is missing"})
		return
	}

	lab, err := dataprocess.ProDevInfo(info.Moduleid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	if lab.Finaltable == nil {
		lab.Iscover = true
	}

	//var finaltable *source.OutputFinalTable
	if lab.Finaltable != nil {
		if lab.Finaltable.Dbname == "" {
			lab.Finaltable.Dbname = common.DWDDB
		}
	}

	//// 获取元数据信息
	//lab.Finaltable, err = metacenter.FinalMetaInfo(info.Moduleid, lab.Finaltable)
	//if err != nil {
	//	CeaglesError(c, err)
	//	return
	//}

	result := gin.H{
		"id":             lab.Id,
		"name":           lab.Notename,
		"finaltable":     lab.Finaltable,
		"outputfields":   lab.Outputfields,
		"outputcomment":  lab.Outputcomment,
		"outputprimkeys": lab.OutputPrimkeys,
		//"tags":          tags,
		"iscover": lab.Iscover,
	}

	res := gin.H{
		"code":   Success,
		"result": result,
	}
	c.JSON(Success, res)

}

func ModuleDelete(c *gin.Context) {
	var feed common2.Feed
	feed.Module = common2.DataDevelopMD
	feed.Subject = common2.ObjScript
	feed.ProjectID = c.GetString("projectid")
	feed.IP = c.ClientIP()
	feed.OperateType = common2.OpTypeDel
	feed.Subject = common2.ObjTask

	userid, ok := c.Get("userid")
	if ok {
		feed.Userid = userid.(string)
	}
	var sp source.ProDevInfo
	err := c.BindJSON(&sp)
	if err != nil {
		CjsonParamError(c, err)
		FeedBack(feed, "请求参数解析有误", false)
		return
	}
	if sp.Id == "" {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "ID为空"})
		FeedBack(feed, "请求参数解析有误", false)
		return
	}

	lab, err := dataprocess.ModuleDelete(sp.Id)
	labType := dataprocess.ModuleMap[lab.Module]
	feed.SubjectName = lab.Notename
	if err != nil {
		tools.CommonError(c, err)
		FeedBack(feed, fmt.Sprintf("删除%s模组%s失败", labType, lab.Notename), false)
		return
	}
	// 清除模组元数据
	err = metacenter.ModuleRemoveMetaInfo(sp.Id)
	if err != nil {
		tools.CommonError(c, err)
		FeedBack(feed, fmt.Sprintf("删除%s模组%s失败", labType, lab.Notename), false)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": "删除成功",
	}
	FeedBack(feed, fmt.Sprintf("删除%s模组%s成功", labType, lab.Notename), true)
	c.JSON(Success, res)
}

func OutputShowTable(c *gin.Context) {
	var info source.OutputSave
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Moduleid == "" {
		c.JSON(Success, gin.H{"code": ParamError, "msg": "param 'moduleid' is missing"})
		return
	}

	lab, err := dataprocess.ProDevInfo(info.Moduleid)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	engineinfo, _ := dataprocess.GetInstance(lab.Newtbinfo.Engineid)

	result, err := dataprocess.OutputShowTable(lab, engineinfo)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	res := gin.H{
		"code": Success,
		"result": gin.H{
			"fields":        result.Fields,
			"fieldtypes":    result.Fieldtypes,
			"fieldcomments": result.Fieldcomments,
			"records":       result.CookingFieldRecords,
			"outputfields":  lab.Outputfields,
			"outputcomment": lab.Outputcomment,
			"type":          engineinfo.Databasetype,
		},
	}
	c.JSON(Success, res)
}

func JudgeSubmit(c *gin.Context) {
	var info source.DpId
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	status, err := dataprocess.JudgeSubmit(info.Id)
	if err != nil {
		CeaglesError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": status,
	}
	c.JSON(Success, res)

}

func TimefieldJudge(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	// if info.Id == "" {
	// 	CjsonParamError(c, errors.New("param 'id' is missing"))
	// 	return
	// }
	if info.Engineid == "" {
		CjsonParamError(c, errors.New("param 'engineid' is missing"))
		return
	}
	if info.Dbname == "" {
		CjsonParamError(c, errors.New("param 'dbname' is missing"))
		return
	}
	if info.Tbname == "" {
		CjsonParamError(c, errors.New("param 'tbname' is missing"))
		return
	}

	status, err, format := dataprocess.TimefieldJudge(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	res := gin.H{
		"code":   Success,
		"result": status,
		"format": format,
	}
	c.JSON(Success, res)
}

// 检测项目是否可删除，1.没有权限不能删除；2.里面有任务不能删除。
func CheckProjectStatus(c *gin.Context) {
	userid, _ := c.Get("userid")
	var info source.TaskUpdate
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	//-----------------------------------
	//检查用户是否有权限操作project
	//userid, _ := c.Get("userid")
	exist := dataprocess.Check(info.Id, userid.(string))
	res, _ := dataprocess.CheckTask(info.Id, info.Projectid)
	if exist || len(res) != 0 {
		var response = gin.H{
			"code":   Success,
			"result": false,
		}
		c.JSON(Success, response)
		return
	} else {
		var response = gin.H{
			"code":   Success,
			"result": true,
		}
		c.JSON(Success, response)
		return
	}
}

// 预览版接口保存表输入
func SaveTablePre(c *gin.Context) {
	var info source.TbJson
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}

	if info.Id == "" {
		CjsonParamError(c, errors.New("param 'id' is missing"))
		return
	}
	if info.Engineid == "" {
		CjsonParamError(c, errors.New("param 'engineid' is missing"))
		return
	}
	if info.Dbname == "" {
		CjsonParamError(c, errors.New("param 'dbname' is missing"))
		return
	}
	// if info.Schema == "" {
	// 	CjsonParamError(c, errors.New("param 'schema' is missing"))
	// 	return
	// }
	if info.Tbname == "" {
		CjsonParamError(c, errors.New("param 'tbname' is missing"))
		return
	}

	Authorization := c.Request.Header["Authorization"][0]

	info.ProjectID = c.GetString("projectid")

	_, err = dataprocess.SaveTable(info, Authorization)
	if err != nil {
		tools.CommonError(c, err)
		return
	}
	//=================预览版start==================
	//无关spark ,对于 非 ods 的表 ，不进行该操作。
	if info.AITableType.TableType != "" {
		fmt.Println("进来了 ")
		err = dataprocess.ChangeTableType(info)
		if err != nil {
			tools.CommonError(c, err)
			return
		}
	}
	//==================预览版end===================
	res := gin.H{
		"code": Success,
		"msg":  "保存成功",
	}
	c.JSON(Success, res)
}

// 预览版 获取 表字段的代码表接口。
func GetDimTablespre(c *gin.Context) {
	var dimtabs source.GetDimTablesInfo
	err := c.BindJSON(&dimtabs)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	if dimtabs.Dbname == "" || dimtabs.Tbname == "" || dimtabs.Colname == "" {
		newarray := []string{}
		ret := gin.H{
			"code":   Success,
			"result": newarray,
			"errmsg": "param tbname or dbname or colname is missing",
		}
		c.JSON(Success, ret)
		return
	}
	res := dataprocess.GetDimTables(dimtabs) // dim tables

	//var ress []map[string]string
	var result []map[string]interface{}

	for i := 0; i < len(res); i++ {
		// v4.5.1 获取 stdcodeid
		stdcodeid := dataprocess.GetStdCodeID(dimtabs.Dbname, res[i])
		r1 := gin.H{
			"dimtablename":    res[i],
			"dimtablecomment": "",
			"dbname":          dimtabs.Dbname,
			"stdcodeid":       stdcodeid,
		}
		result = append(result, r1)
	}

	if result == nil {
		newarray := []string{}
		ret := gin.H{
			"code":   Success,
			"result": newarray,
		}
		c.JSON(Success, ret)
		return
	}
	ret := gin.H{
		"code":   Success,
		"result": result,
	}
	c.JSON(Success, ret)
	return
}

// 获取代码表的数据信息。
func GetDimData(c *gin.Context) {
	var info source.GetDimTablesInfo
	errs := c.BindJSON(&info)
	if errs != nil {
		CjsonParamError(c, errs)
		return
	}
	ret, _ := dataprocess.GetDimData(info)

	rets := gin.H{
		"code":   Success,
		"result": ret,
	}
	c.JSON(Success, rets)
	return
}

// 获取代码表的完整数据
// 根据 库 --- 表 --字段*2 获取。
func GetDimDataList(c *gin.Context) {
	var info source.DimDataList
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	ret, _ := dataprocess.GetDimDataList(info)

	rets := gin.H{
		"code":   Success,
		"result": ret,
	}
	c.JSON(Success, rets)
	return
}

func ListBasedb(c *gin.Context) {
	var info source.Extra
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	resp, err := dataprocess.ListBasedb(info)
	if err != nil {
		tools.CommonError(c, err)
		return
	}

	var response = gin.H{
		"code":   Success,
		"result": resp,
	}

	if resp == nil {
		newarray := []string{}
		response = gin.H{
			"code":   Success,
			"result": newarray,
		}
	}
	c.JSON(Success, response)
}

// 4.7 新增
func ListMetaData(c *gin.Context) {
	tokenStr := c.Request.Header["Authorization"][0]
	projectID := c.GetString("projectid")
	var info source.QueryBody
	err := c.BindJSON(&info)
	if err != nil {
		CjsonParamError(c, err)
		return
	}
	res, err := dataprocess.ListMetaData(tokenStr, projectID, info)
	if err != nil {
		ParsedEaglesError(c, err)
		return
	}
	response := gin.H{
		"code":   Success,
		"result": res,
	}
	c.JSON(Success, response)
	return
}
