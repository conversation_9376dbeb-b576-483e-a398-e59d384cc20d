// Copyright 2018 DATATOM Authors. All rights reserved.
//
// eagles.go
//
// 表的初始化

package thread

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	common2 "datatom.com/asset/common"
	toolscommon "datatom.com/tools/common"
	"datatom.com/tools/common/rediscli"
	"datatom.com/tools/common/tools"

	"datatom/gin.v1"

	"datatom.com/ants/common"
	"datatom.com/ants/httpclient"
	"datatom.com/ants/logger"
)

// Eagles
func Eagles() {
	go MappingInit() //表结构初始化
}

var GLOBODY = gin.H{
	"properties": gin.H{
		"globalvars": gin.H{
			"properties": gin.H{
				"timevars": gin.H{
					"properties": gin.H{
						"name": gin.H{
							"type":  "keyword",
							"store": true,
						},
						"type": gin.H{
							"type":  "keyword",
							"store": true,
						},
						"value": gin.H{
							"type":  "keyword",
							"store": true,
						},
					},
				},
				"constantvars": gin.H{
					"properties": gin.H{
						"name": gin.H{
							"type":  "keyword",
							"store": true,
						},
						"value": gin.H{
							"type":  "keyword",
							"store": true,
						},
					},
				},
			},
		},
	},
}

// MappingInit 用于初始化mapping以预设一些字段类型.
func MappingInit() {
	for {
		time.Sleep(1 * time.Second)
		//建立索引
		uri := fmt.Sprintf("/%s", common.DataBaseLab)
		res, err := httpclient.Put(uri, "")
		if err != nil {
			logger.Error.Println("put索引出现异常")
			logger.Error.Println(err)
			continue
		}
		time.Sleep(5 * time.Second)
		//--------------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbAccess)
		fmt.Println("开始建表:1")
		body := gin.H{
			"properties": gin.H{
				"content": gin.H{"store": true,
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
				"customize": gin.H{
					"properties": gin.H{
						"extractinfo": gin.H{
							"properties": gin.H{
								"firstrecord": gin.H{
									"type":  "keyword",
									"store": true,
									"fields": gin.H{
										"text": gin.H{
											"type":     "text",
											"store":    true,
											"analyzer": "ik_max_word",
										},
									},
								},
								"secondrecord": gin.H{
									"type":  "keyword",
									"store": true,
									"fields": gin.H{
										"text": gin.H{
											"type":     "text",
											"store":    true,
											"analyzer": "ik_max_word",
										},
									},
								},
								"thirdrecord": gin.H{
									"type":  "keyword",
									"store": true,
									"fields": gin.H{
										"text": gin.H{
											"type":     "text",
											"store":    true,
											"analyzer": "ik_max_word",
										},
									},
								},
							},
						},
					},
				},
				"updatetime": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"datecreated": gin.H{
					"type":  "keyword",
					"store": true,
				},
			},
		}
		bysbody, err := json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		//全局参数
		bysbody, err = json.Marshal(GLOBODY)

		logger.Info.Println("access表全局参数初始化：")
		res, err = httpclient.GloPost(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println("glopost执行结果:", err)
			continue
		}
		logger.Info.Println("access表全局参数初始化结束")

		//--------------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbSensitiveRank)
		body = gin.H{
			"properties": gin.H{
				"createtime": gin.H{
					"type": "keyword",
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		//--------------------------------------------------------------------------
		//数据质量
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbQualityRule)
		body = gin.H{
			"properties": gin.H{
				"name": gin.H{
					"type": "keyword",
					//"store":        true,
					//"ignore_above": 256.0,
					"normalizer": "lowercase_normalizer",
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"type":     "text",
						},
					},
				},
				"ruledesc": gin.H{
					"type": "keyword",
					//"store":        true,
					//"ignore_above": 256.0,
					"normalizer": "lowercase_normalizer",
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		//质量报告
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbQualityReportTemplate)
		body = gin.H{
			"properties": gin.H{
				"name": gin.H{
					"type": "keyword",
					//"store":        true,
					//"ignore_above": 256.0,
					"normalizer": "lowercase_normalizer",
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"type":     "text",
						},
					},
				},
				"describe": gin.H{
					"type": "keyword",
					//"store":        true,
					//"ignore_above": 256.0,
					"normalizer": "lowercase_normalizer",
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		//--------------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbSensitiveCheck)
		body = gin.H{
			"properties": gin.H{
				"createtime": gin.H{
					"type": "keyword",
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		//--------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbDataReport)
		body = gin.H{
			"properties": gin.H{
				"content": gin.H{
					// "type":  "string",
					// "index": "no_",
					"store":        true,
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
				"updatetime": gin.H{
					"type": "keyword",
				},
				"datecreated": gin.H{
					"type": "keyword",
				},
				"name": gin.H{
					"type": "keyword",
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"type":     "text",
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		//====================================================================
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbReportCheck)
		body = gin.H{
			"properties": gin.H{
				"updatetime": gin.H{
					"type": "keyword",
				},
				"createtime": gin.H{
					"type": "keyword",
				},
				"name": gin.H{
					"type": "keyword",
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"type":     "text",
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
		}
		//-------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbMetrics)
		body = gin.H{
			"properties": gin.H{
				"querypreview": gin.H{
					"properties": gin.H{
						"queryfilter": gin.H{
							"properties": gin.H{
								"value2": gin.H{
									"type": "keyword",
								},
								"value1": gin.H{
									"type": "keyword",
								},
							},
						},
					},
				},
				"updatetime": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"metricsvaluev1": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"resupdatetime": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"createtime": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"zbname": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
						"text_pinyin": gin.H{
							"analyzer": "pinyin",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"zbbusiness": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
						"text_pinyin": gin.H{
							"analyzer": "pinyin",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"zbunit": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
						"text_pinyin": gin.H{
							"analyzer": "pinyin",
							"store":    true,
							"type":     "text",
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)
		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		//metrics.UpdateMetricsValue()
		//------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbMetricsQuality)
		body = gin.H{
			"properties": gin.H{
				"updatetime": gin.H{
					"type":  "keyword",
					"store": true,
				},

				"resupdatetime": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"createtime": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"zbname": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
						"text_pinyin": gin.H{
							"analyzer": "pinyin",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"zbbusiness": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
						"text_pinyin": gin.H{
							"analyzer": "pinyin",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"zbunit": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
						"text_pinyin": gin.H{
							"analyzer": "pinyin",
							"store":    true,
							"type":     "text",
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)
		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		//-----------------------------------------------

		uri = fmt.Sprintf("/%s/%s/_mapping", common2.DanastudioAuth, common2.TbUserGroup)
		body = gin.H{
			"properties": gin.H{
				"content": gin.H{
					// "type":  "string",
					// "index": "no_",
					"store":        true,
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
				"usergroupname": gin.H{
					"type":  "keyword",
					"store": true,
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		//--------------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.BathcTbAccess)
		fmt.Println("开始建表:1")
		body = gin.H{
			"properties": gin.H{

				"content": gin.H{
					// "type":  "string",
					// "index": "no_",
					"store":        true,
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
				"customize": gin.H{
					"properties": gin.H{
						"extractinfo": gin.H{
							"properties": gin.H{
								"firstrecord": gin.H{
									"type":  "keyword",
									"store": true,
									"fields": gin.H{
										"text": gin.H{
											"type":     "text",
											"store":    true,
											"analyzer": "ik_max_word",
										},
									},
								},
								"secondrecord": gin.H{
									"type":  "keyword",
									"store": true,
									"fields": gin.H{
										"text": gin.H{
											"type":     "text",
											"store":    true,
											"analyzer": "ik_max_word",
										},
									},
								},
								"thirdrecord": gin.H{
									"type":  "keyword",
									"store": true,
									"fields": gin.H{
										"text": gin.H{
											"type":     "text",
											"store":    true,
											"analyzer": "ik_max_word",
										},
									},
								},
							},
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		//====================================================================
		//数据开发
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbDev)
		fmt.Println("开始建表:2")
		logger.Debug.Println("tb_dev表初始化")
		body = gin.H{
			"properties": gin.H{

				"content": gin.H{
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
				"fieldinfos": gin.H{
					"properties": gin.H{
						"firstrecord": gin.H{
							"type":  "keyword",
							"store": true,
							"fields": gin.H{
								"text": gin.H{
									"type":     "text",
									"store":    true,
									"analyzer": "ik_max_word",
								},
							},
						},
						"secondrecord": gin.H{
							"type":  "keyword",
							"store": true,
							"fields": gin.H{
								"text": gin.H{
									"type":     "text",
									"store":    true,
									"analyzer": "ik_max_word",
								},
							},
						},
						"thirdrecord": gin.H{
							"type":  "keyword",
							"store": true,
							"fields": gin.H{
								"text": gin.H{
									"type":     "text",
									"store":    true,
									"analyzer": "ik_max_word",
								},
							},
						},
					},
				},
				"standardfieldinfos": gin.H{
					"properties": gin.H{
						"firstrecords": gin.H{
							"type":  "keyword",
							"store": true,
							"fields": gin.H{
								"text": gin.H{
									"type":     "text",
									"store":    true,
									"analyzer": "ik_max_word",
								},
							},
						},
						"secondrecords": gin.H{
							"type":  "keyword",
							"store": true,
							"fields": gin.H{
								"text": gin.H{
									"type":     "text",
									"store":    true,
									"analyzer": "ik_max_word",
								},
							},
						},
						"thirdrecords": gin.H{
							"type":  "keyword",
							"store": true,
							"fields": gin.H{
								"text": gin.H{
									"type":     "text",
									"store":    true,
									"analyzer": "ik_max_word",
								},
							},
						},
					},
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println("打印一下dev的mapping结果1:")
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		if strings.Contains(string(res), `mapper [fieldinfos.firstrecord] has different [index] values]`) {
			uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbDev)
			fmt.Println("开始建表:2-1")
			logger.Debug.Println("tb_dev表初始化")
			body = gin.H{
				"properties": gin.H{
					"content": gin.H{
						"type":         "string",
						"index":        "not_analyzed",
						"ignore_above": 20,
					},
					"fieldinfos": gin.H{
						"properties": gin.H{
							"firstrecord": gin.H{
								"index": false,
								"type":  "keyword",
								"store": true,
								"fields": gin.H{
									"text": gin.H{
										"type":     "text",
										"store":    true,
										"analyzer": "ik_max_word",
									},
								},
							},
							"secondrecord": gin.H{
								"index": false,
								"type":  "keyword",
								"store": true,
								"fields": gin.H{
									"text": gin.H{
										"type":     "text",
										"store":    true,
										"analyzer": "ik_max_word",
									},
								},
							},
							"thirdrecord": gin.H{
								"type":  "keyword",
								"store": true,
								"fields": gin.H{
									"text": gin.H{
										"type":     "text",
										"store":    true,
										"analyzer": "ik_max_word",
									},
								},
							},
						},
					},
					"standardfieldinfos": gin.H{
						"properties": gin.H{
							"firstrecords": gin.H{
								"type":  "keyword",
								"store": true,
								"fields": gin.H{
									"text": gin.H{
										"type":     "text",
										"store":    true,
										"analyzer": "ik_max_word",
									},
								},
							},
							"secondrecords": gin.H{
								"type":  "keyword",
								"store": true,
								"fields": gin.H{
									"text": gin.H{
										"type":     "text",
										"store":    true,
										"analyzer": "ik_max_word",
									},
								},
							},
							"thirdrecords": gin.H{
								"type":  "keyword",
								"store": true,
								"fields": gin.H{
									"text": gin.H{
										"type":     "text",
										"store":    true,
										"analyzer": "ik_max_word",
									},
								},
							},
						},
					},
				},
			}
			bysbody, err = json.Marshal(body)
			res, err = httpclient.Post(uri, string(bysbody))
			logger.Debug.Println("打印一下dev的mapping结果:")
			logger.Debug.Println(string(res))
			if err != nil {
				logger.Error.Println(err)

				continue
			}
		}
		//----------------------------------------------------------------------------

		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbDataBase)
		fmt.Println("开始建表:3")
		body = gin.H{
			"properties": gin.H{

				"content": gin.H{
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		//---------------------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbCreate)
		fmt.Println("开始建表:4")
		body = gin.H{
			"properties": gin.H{
				"content": gin.H{
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		//-------------------------------------------------------------------------------
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbMiddle)
		fmt.Println("开始建表:5")
		body = gin.H{
			"properties": gin.H{

				"content": gin.H{
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.InOutManager)
		fmt.Println("开始建表:6")
		body = gin.H{
			"properties": gin.H{

				"content": gin.H{
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbSensitiveRank)
		fmt.Println("开始建表:7")
		body = gin.H{
			"properties": gin.H{
				"ranknum": gin.H{
					"type":  "long",
					"index": "not_analyzed",
					"store": true,
				},
			},
		}
		bysbody, err = json.Marshal(body)
		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbSensitiveCheck)
		fmt.Println("开始建表:8")
		body = gin.H{
			"properties": gin.H{},
		}
		bysbody, err = json.Marshal(body)
		res, err = httpclient.Post(uri, string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		body = gin.H{
			"index": gin.H{
				"max_result_window": "10000000",
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Put("/_settings", string(bysbody))
		logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
			continue
		}

		body = gin.H{
			"properties": gin.H{
				"notename": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"describe": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
			},
		}
		bysbody, _ = json.Marshal(body)
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DBAntsLab, common.RealMessage)
		_, err = httpclient.Post(uri, string(bysbody))
		//logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
		}

		//============================================================

		body = gin.H{
			"properties": gin.H{
				//"name": gin.H{
				//	"type":  "keyword",
				//	"store": true,
				//	"fields": gin.H{
				//		"text_ik": gin.H{
				//			"analyzer": "ik_max_word",
				//			"store":    true,
				//			"type":     "text",
				//		},
				//	},
				//},
				"taglist": gin.H{
					"properties": gin.H{
						"name": gin.H{
							"type":  "keyword",
							"store": true,
							"fields": gin.H{
								"text": gin.H{
									"type":     "text",
									"store":    true,
									"analyzer": "ik_max_word",
								},
							},
						},
					},
				},
				"encode": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"updatetime": gin.H{
					"type":  "keyword",
					"store": true,
				},
				"desc": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"username": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"notename": gin.H{
					"type":  "keyword",
					"store": true,
					"fields": gin.H{
						"text_ik": gin.H{
							"analyzer": "ik_max_word",
							"store":    true,
							"type":     "text",
						},
					},
				},
				"effectiveinfo": gin.H{
					"properties": gin.H{
						"effectivebegintime": gin.H{
							"type":  "keyword",
							"store": true,
						},
						"effectiveendtime": gin.H{
							"type":  "keyword",
							"store": true,
						},
					},
				},
			},
		}
		bysbody, _ = json.Marshal(body)
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbCodeTable)
		_, err = httpclient.Post(uri, string(bysbody))
		//logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
		}

		//====================================
		body = gin.H{
			"properties": gin.H{},
		}
		bysbody, _ = json.Marshal(body)
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbCtHis)
		_, err = httpclient.Post(uri, string(bysbody))
		//logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
		}
		//=======================
		body = gin.H{
			"properties": gin.H{},
		}
		bysbody, _ = json.Marshal(body)
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbStHis)
		_, err = httpclient.Post(uri, string(bysbody))
		//logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
		}
		//=======================
		body = gin.H{
			"properties": gin.H{},
		}
		bysbody, _ = json.Marshal(body)
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbCtRecension)
		_, err = httpclient.Post(uri, string(bysbody))
		//logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
		}
		//=======================
		body = gin.H{
			"properties": gin.H{},
		}
		bysbody, _ = json.Marshal(body)
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseMetaData, common.TbStRecension)
		_, err = httpclient.Post(uri, string(bysbody))
		//logger.Debug.Println(string(res))
		if err != nil {
			logger.Error.Println(err)
		}

		//脚本版本表
		uri = fmt.Sprintf("/%s/%s/_mapping", common.DataBaseLab, common.TbDevHis)
		logger.Debug.Println("tb_dev_his表初始化")
		body = gin.H{
			"properties": gin.H{

				"content": gin.H{
					"type":         "string",
					"index":        "not_analyzed",
					"ignore_above": 20,
				},
				"version": gin.H{
					"type": "long",
				},
			},
		}
		bysbody, err = json.Marshal(body)

		res, err = httpclient.Post(uri, string(bysbody))
		if err != nil {
			logger.Error.Println(err)
		}

		return
	}
}

func InitRedis(ip string, port int) {
	rediscli.RedisPool = rediscli.InitPool(ip, port)
}

func InitRedis3(ip string, port int) {
	rediscli.RedisPool3 = rediscli.InitPool3(ip, port)
}

func GetNodes() {
	toolscommon.Nodes, _ = tools.GetAliveIPs()
	fmt.Println("toolsnode", toolscommon.Nodes)
}
