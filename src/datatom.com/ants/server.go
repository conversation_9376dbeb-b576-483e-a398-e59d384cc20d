// Copyright (c) Datatom Software, Inc.(2017)
//
// Author:袁赛杰（<EMAIL>）
//
// Creating Time:
package main

import (
	"io/ioutil"
	"log"
	"strings"

	"datatom.com/ants/httpdo"
	"datatom.com/ants/httphandler/metrics"
	escli "datatom.com/tools/common/escli"

	"datatom.com/tools/middleware"

	"datatom.com/ants/common"
	"datatom.com/ants/httpclient"
	MgmSpark "datatom.com/ants/httpdo/spark"
	"datatom.com/ants/httphandler"
	"datatom.com/ants/httphandler/access"
	"datatom.com/ants/httphandler/analyse"
	"datatom.com/ants/httphandler/datagovern"
	"datatom.com/ants/httphandler/dataprocess"
	"datatom.com/ants/httphandler/develop"
	"datatom.com/ants/httphandler/folder"
	"datatom.com/ants/httphandler/metacenter"
	"datatom.com/ants/httphandler/spark"
	"datatom.com/ants/httphandler/workflow"
	"datatom.com/ants/logger"
	"datatom.com/ants/thread"
	tools "datatom.com/tools/httpdo"

	"datatom/gin.v1"

	ginjwt "datatom.com/ants/gin-jwt"
	"github.com/go-ini/ini"

	"encoding/json"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/exec"
	"strconv"
	//metadata "datatom.com/metadata/thread"
)

var (
	gitcommit string
	gitbranch string
	buildtime string
)

func Codeinfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"commit num": gitcommit,
		"git branch": gitbranch,
		"build time": buildtime,
	})
}

var publicKeyFile = "/etc/danastudio/public.crt"

func main() {
	var err error
	go func() {
		sp, _ := tools.SelfIp()
		log.Println(http.ListenAndServe(sp+":6062", nil))
	}()

	///加载配置文件
	LoadConf()

	//创建需要的文件目录
	MakeFile()

	///初始化数据库
	InitEaglesData()
	//日志归档-清理
	go thread.LogInit()
	if logger.ServerLogIsCheckFile {
		go thread.CheckLogSize()
	}

	{
		//在DS中初始化ES客户端,初始化加在前面防止后面的线程要使用es客户端查询
		for {
			if logger.Error != nil && logger.Info != nil {
				break
			}
		}
		escli.NewESClientCl, err = escli.InitEsClient(escli.ESClientLog4Ants, &logger.Error, &logger.Info)
		if err != nil {
			fmt.Println("ES客户端初始化异常,请检查elastic.log", err)
			logger.Info.Println("ES客户端初始化异常,请检查elastic.log")
		}
	}

	// danastudio模板初始化
	//go metadata.TemplateInit()
	//go thread.RealTimeOpsInfo() // 实时运维topic消费
	//go thread.RealTaskMonitor()  // 实时任务上线监控
	go thread.FlinkTaskMonitor() // 实时任务上线监控
	go thread.FailRetryMonitor() // 实时任务上线监控
	//go thread.KerberosRealTimeOpsInfo()
	MgmSpark.NewSpark()

	thread.InitRedis(common.REDIS_IP, common.REDIS_PORT)

	thread.InitRedis3(common.REDIS_IP, common.REDIS_PORT)

	thread.GetNodes()

	go escli.CheckElasticLogSize(logger.SERVER_LOG, &logger.Error, &logger.Info) //日志切割，清理
	go escli.CheckElasticLog(logger.SERVER_LOG, &logger.Error, &logger.Info)     //日志切割，清理

	//表数据量统计
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:获取表数据增量出现了问题！")
			}
		}()
		thread.TableCount()
	}()

	//定时删除中间表
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:删除中间表出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.MiddleTableDelete()
	}()

	//定时删除主题表
	// go func() {
	// 	defer func() {
	// 		if r := recover(); r != nil {
	// 			logger.Error.Println("线程:删除主题表出现了问题")
	// 			logger.Error.Println(r)
	// 		}
	// 	}()
	// 	thread.SubjectTableDelete()
	// }()

	//处理自定义的中间表
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:删除自定义表出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.CustomTableDel()
	}()
	//-----------------------------------
	//保存连接日志详情
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:更新抽取源日志出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.DbCheckLog()
	}()

	//----------------------------------------------
	//定时删除治理临时表
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:删除治理临时表出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.GovernTableDel()
	}()
	//------------------------------------
	//定时删除治理临时表
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:定时执行数据填报任务出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.ReportExec()
	}()
	//------------------------------------
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:处理指标任务出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.MExec()
	}()
	//复制一份库表推送文件夹
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:处理指标任务出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.ReptitonFolder()
	}()
	//复制一份库表推送文件夹
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Error.Println("线程:处理指标任务出现了问题")
				logger.Error.Println(r)
			}
		}()
		thread.UpdateRealTaskType()
	}()
	//-------------------------
	thread.ModifyReportStatusForRestart()

	thread.Eagles()
	escli.GetESInfo()

	gin.SetMode(gin.ReleaseMode) //全局设置环境，此为开发环境，线上环境为gin.ReleaseMode

	router := gin.Default() //获得路由实例
	router.Use(middleware.Cors())
	router.Use(middleware.ModifyJSONMiddleware)
	//	router.Use(httphandler.Check)
	//	router.NoMethod(httphandler.NoMethod)
	//	router.HandleMethodNotAllowed = true
	// get danastudio publickey
	publicKey, err := ioutil.ReadFile(publicKeyFile)
	if err != nil {
		log.Fatal()
		return
	}

	// init jwt-middleware
	Auths := ginjwt.InitAuth(publicKey)
	///web服务配置请求组

	commonRt := router.Group("/danastudio/ants/")
	{
		commonRt.GET("params", httphandler.ListParams)
		commonRt.GET("envs", httphandler.ListEnvs)
		commonRt.POST("codeinfo", Codeinfo) //获取代码编译信息
		commonRt.GET("officeip", httphandler.GetOnlyOfficeIp)
	}
	//版本号
	versionRt := router.Group("/danastudio/")
	{
		versionRt.POST("version", httphandler.GetVersion)
	}
	//-----------------------------
	//模板转移
	templeteRt := router.Group("danastudio/manager/batch/resource/")
	{
		templeteRt.POST("templete", Auths[0].MiddlewareFunc(), access.TempleteResource)
	}
	//-----------------------------
	//dodox测试
	dodoxrt := router.Group("/dsdatacollection/taskinfo/")
	{
		dodoxrt.POST("collectTable", access.Test)
	}
	//
	//接入模块
	accessRt := router.Group("/danastudio/ants/access/")
	//数据填报
	{
		accessRt.POST("datareport/sync", access.RunReport)                               //同步模板
		accessRt.POST("datareport/save", Auths[0].MiddlewareFunc(), access.AccNewReport) //新建保存模板
		accessRt.POST("datareport/search", Auths[0].MiddlewareFunc(), access.AccReportSearch)
		accessRt.POST("datareport/delete", Auths[0].MiddlewareFunc(), access.AccDelReport)      //删除
		accessRt.POST("datareport/detail", Auths[0].MiddlewareFunc(), access.AccReportDetail)   //详情
		accessRt.POST("datareport/publish", Auths[0].MiddlewareFunc(), access.AccReportPublish) //发布

		accessRt.GET("datareport/template/download", Auths[0].MiddlewareFunc(), access.AccReportMBDownload) //模板下载
		// 提交填报
		accessRt.POST("datareport/putdata", Auths[0].MiddlewareFunc(), access.ReportPutData)
		accessRt.POST("datareport/putdata/check", Auths[0].MiddlewareFunc(), access.ReportPutdataCheck)
		accessRt.POST("datareport/putdata/list", Auths[0].MiddlewareFunc(), access.ReportPutList)            // 提交填报列举
		accessRt.POST("datareport/putdata/query", Auths[0].MiddlewareFunc(), access.ReportDataQuery)         //查看填报数据
		accessRt.GET("datareport/putdata/download", Auths[0].MiddlewareFunc(), access.AccReportDataDownload) //下载填报数据

		accessRt.POST("datareport/authchange", Auths[0].MiddlewareFunc(), access.AccReportAuthChange) //修改授权

		accessRt.POST("datareport/putdata/tempcheck", Auths[0].MiddlewareFunc(), access.ReportTempCheck)

		//问题数据校验
		accessRt.GET("datareport/putdata/checkdownload", Auths[0].MiddlewareFunc(), access.ReportPutdataCheckDownload) //下载问题校验日志

		accessRt.POST("reportcheck/search", Auths[0].MiddlewareFunc(), datagovern.ReportCheckList)   //列举填报规则
		accessRt.POST("reportcheck/detail", Auths[0].MiddlewareFunc(), access.ReportCheckDetail)     //填报规则详情
		accessRt.POST("reportcheck/save", Auths[0].MiddlewareFunc(), datagovern.ReportCheckSave)     //新建/编辑填报规则
		accessRt.POST("reportcheck/delete", Auths[0].MiddlewareFunc(), datagovern.ReportCheckDelete) //删除填报规则

	}

	//指标管理模块
	MetricsRT := router.Group("/danastudio/ants/metrics/")
	{
		//增|改
		MetricsRT.POST("save", Auths[0].MiddlewareFunc(), metrics.MetrSave) //新建保存指标
		//删
		MetricsRT.POST("delete", Auths[0].MiddlewareFunc(), metrics.MetrDelTask)
		//查
		MetricsRT.POST("search", Auths[0].MiddlewareFunc(), metrics.MetricsSearch)
		//详情
		MetricsRT.POST("detail", Auths[0].MiddlewareFunc(), metrics.MetricsDetail) //详情
		//移动
		MetricsRT.POST("move", Auths[0].MiddlewareFunc(), metrics.MetricsMove)
		//指标上线
		MetricsRT.POST("submit", Auths[0].MiddlewareFunc(), metrics.MetricsSubmit) //详情
		//指标下线
		MetricsRT.POST("down", Auths[0].MiddlewareFunc(), metrics.MetricsDown) //详情

		MetricsRT.POST("giverun", metrics.MetricsGiveRun) //执行
		MetricsRT.POST("getsql", metrics.MetricsGetSql)   //执行

		MetricsRT.POST("getcode", Auths[0].MiddlewareFunc(), metrics.MetricsGetCode) //执行
	}

	//指标管理模块
	QualityMetricsRT := router.Group("/danastudio/ants/metrics/quality")
	{
		//增|改
		QualityMetricsRT.POST("save", Auths[0].MiddlewareFunc(), metrics.Q_MetrSave)
		//删
		QualityMetricsRT.POST("delete", Auths[0].MiddlewareFunc(), metrics.Q_MetrDelTask)
		//查
		QualityMetricsRT.POST("search", Auths[0].MiddlewareFunc(), metrics.Q_MetricsSearch)
		//详情
		QualityMetricsRT.POST("detail", Auths[0].MiddlewareFunc(), metrics.Q_MetricsDetail)
		//移动
		QualityMetricsRT.POST("move", Auths[0].MiddlewareFunc(), metrics.Q_MetricsMove)
		//指标上线
		QualityMetricsRT.POST("submit", Auths[0].MiddlewareFunc(), metrics.Q_MetricsSubmit)
		//指标下线
		QualityMetricsRT.POST("down", Auths[0].MiddlewareFunc(), metrics.Q_MetricsDown)

		QualityMetricsRT.POST("giverun", metrics.Q_MetricsGiveRun)
		QualityMetricsRT.POST("truncate", metrics.Q_MetricsTruncate)
		QualityMetricsRT.POST("updatebusiness", metrics.Q_MetrBusiness)

		QualityMetricsRT.POST("getcode", Auths[0].MiddlewareFunc(), metrics.MetricsGetCode) //执行
		//新增指标
		QualityMetricsRT.POST("operate", metrics.Q_MetrOperate)

	}

	{
		//		accessRt.Use(Auths[0].MiddlewareFunc())
		//功能
		//------------------------------------------------------------
		//数据集成-添加抽取源相关接口
		accessRt.POST("newdatabase", Auths[0].MiddlewareFunc(), access.AccNewDatabase)
		accessRt.POST("dbstatus", Auths[0].MiddlewareFunc(), access.AccDatabaseCheck)
		accessRt.POST("dblist", Auths[0].MiddlewareFunc(), access.AccDblist)
		accessRt.POST("dbdel", Auths[0].MiddlewareFunc(), access.AccDbDel)
		accessRt.POST("dbupdate", Auths[0].MiddlewareFunc(), access.AccDbUpdate)
		accessRt.POST("dbinfo", Auths[0].MiddlewareFunc(), access.AccDbInfo)
		accessRt.POST("logadd", Auths[0].MiddlewareFunc(), access.AccLogadd)
		accessRt.POST("dbsearch", Auths[0].MiddlewareFunc(), access.AccDbSearch)
		accessRt.POST("dbsourcelist", Auths[0].MiddlewareFunc(), metacenter.AccDbSourceList)
		accessRt.POST("dbsourcedetail", Auths[0].MiddlewareFunc(), metacenter.AccDbSourceDetail)

		//数据集成-数据同步-选择抽取源相关接口
		accessRt.POST("dbget", Auths[0].MiddlewareFunc(), access.AccDbGet)
		accessRt.POST("dbgetschemas", Auths[0].MiddlewareFunc(), access.AccDbGetSchemas)
		accessRt.POST("dbgettables", Auths[0].MiddlewareFunc(), access.AccDbGetTables)
		accessRt.POST("dbgettableinfo", Auths[0].MiddlewareFunc(), access.AccDbGetTableInfo)
		accessRt.POST("wherecontentjudge", Auths[0].MiddlewareFunc(), access.AccDbWhereJudge)
		accessRt.POST("createtimejudge", Auths[0].MiddlewareFunc(), access.AccCreateTimeJudge)
		//数据集成-数据同步-目的源相关接口
		accessRt.POST("odslist", Auths[0].MiddlewareFunc(), access.AccOdsList)
		accessRt.POST("dbodsget", Auths[0].MiddlewareFunc(), access.AccDbOdsGet)
		accessRt.POST("dbodsgetschemas", Auths[0].MiddlewareFunc(), access.AccDbOdsGetSchemas)
		accessRt.POST("dbodsgettables", Auths[0].MiddlewareFunc(), access.AccDbOdsGetTables)
		accessRt.POST("dbodsgettableinfo", Auths[0].MiddlewareFunc(), access.AccDbOdsGetTableInfo)
		accessRt.POST("dbodstableexist", Auths[0].MiddlewareFunc(), access.AccDbOdsTableExist)
		//数据集成-数据同步-主界面相关操作接口updatedatax
		accessRt.POST("newdatax", Auths[0].MiddlewareFunc(), access.AccNewDatax)
		accessRt.POST("usabledatax", Auths[0].MiddlewareFunc(), access.ListUsableDatax) // 根据注册引擎版本列举可用抽取方式(单例/批量/落地/自定义)
		accessRt.POST("delete", Auths[0].MiddlewareFunc(), access.AccDelete)
		accessRt.POST("search", Auths[0].MiddlewareFunc(), access.AccSearch)
		accessRt.POST("updatedatax", Auths[0].MiddlewareFunc(), access.AccUpdateDatax)
		accessRt.POST("dataxmove", Auths[0].MiddlewareFunc(), access.AccDataxMove)
		accessRt.POST("updatehistory", access.AccUpdateHistory)

		accessRt.POST("newtask", Auths[0].MiddlewareFunc(), access.NewTask)
		accessRt.POST("updatetask", Auths[0].MiddlewareFunc(), access.UpdateTask)
		accessRt.POST("updatesubtask", Auths[0].MiddlewareFunc(), access.UpdateSubTask)
		accessRt.POST("delsubtask", Auths[0].MiddlewareFunc(), access.DelSubTask)
		accessRt.POST("subtasklog", Auths[0].MiddlewareFunc(), access.SubTaskLog)
		accessRt.POST("subtaskcode", Auths[0].MiddlewareFunc(), access.SubTaskCode)
		accessRt.GET("subtaskstatus", access.SubTaskStatus) //实时推送信息

		//API
		accessRt.POST("newapi", Auths[0].MiddlewareFunc(), access.NewAPI)
		//accessRt.POST("apilist", Auths[0].MiddlewareFunc(), access.APIList)
		accessRt.POST("apiupdate", Auths[0].MiddlewareFunc(), access.APIUpdate)
		//accessRt.POST("apidel", Auths[0].MiddlewareFunc(), access.APIDel)
		//accessRt.POST("apiscript", Auths[0].MiddlewareFunc(), access.APIScript)
		accessRt.POST("apiconn", Auths[0].MiddlewareFunc(), access.APIConn)
		accessRt.POST("apipyrun", Auths[0].MiddlewareFunc(), access.APIPyRun)
		accessRt.POST("apipystop", Auths[0].MiddlewareFunc(), access.APIPyStop)
		accessRt.POST("apipyresult", Auths[0].MiddlewareFunc(), access.APIPyResult)

		accessRt.POST("dataxsearch", Auths[0].MiddlewareFunc(), access.AccDataxSearch)
		accessRt.POST("dataxdetail", Auths[0].MiddlewareFunc(), access.AccDataxDetail)
		accessRt.POST("createcount", Auths[0].MiddlewareFunc(), access.AccCreateCount)
		accessRt.POST("test", Auths[0].MiddlewareFunc(), access.AccTest)
		accessRt.POST("fieldtrans", Auths[0].MiddlewareFunc(), access.AccFieldTrans)

		//===============================================================
		accessRt.POST("list/dir", Auths[0].MiddlewareFunc(), access.AccListDir)
		accessRt.POST("dir/acc", Auths[0].MiddlewareFunc(), access.AccDirList)
		accessRt.POST("info", Auths[0].MiddlewareFunc(), access.AccInfo)
		accessRt.POST("new", Auths[0].MiddlewareFunc(), access.NewAcc)
		accessRt.POST("old", Auths[0].MiddlewareFunc(), access.OldAcc)
		accessRt.POST("singletodefine", Auths[0].MiddlewareFunc(), access.SingleToDefine)
		accessRt.POST("list/kjb", Auths[0].MiddlewareFunc(), access.AccGetKjb)
		accessRt.POST("replace", Auths[0].MiddlewareFunc(), access.AccReplace)
		accessRt.GET("download/:id", Auths[5].MiddlewareFunc(), access.AccDownload)
		accessRt.POST("list/tags", Auths[0].MiddlewareFunc(), access.AccListTags)
		accessRt.POST("save", Auths[0].MiddlewareFunc(), access.AccSave)
		accessRt.POST("run", Auths[0].MiddlewareFunc(), access.AccRun)
		accessRt.POST("stop", Auths[0].MiddlewareFunc(), access.AccStop)
		accessRt.POST("result", Auths[0].MiddlewareFunc(), access.AccResult)
		accessRt.POST("content", Auths[0].MiddlewareFunc(), access.AccContent)

		accessRt.POST("rename", Auths[0].MiddlewareFunc(), access.AccRename)
		accessRt.POST("spe", Auths[0].MiddlewareFunc(), access.AccSpe)

		accessRt.POST("ftp/create", Auths[5].MiddlewareFunc(), access.CreateFtp)
		accessRt.POST("ftp/update", Auths[5].MiddlewareFunc(), access.UpdateFtp)
		accessRt.POST("ftp/del", Auths[5].MiddlewareFunc(), access.DelFtp)
		accessRt.POST("ftp/getinfo", Auths[5].MiddlewareFunc(), access.GetFtpInfo)
		accessRt.POST("ftp/list", Auths[5].MiddlewareFunc(), access.ListFtpInfo)
		accessRt.POST("submit", Auths[0].MiddlewareFunc(), access.AccSubmit)
		accessRt.POST("batchsubmit", Auths[0].MiddlewareFunc(), access.BatchAccSubmit)
		accessRt.POST("down", Auths[5].MiddlewareFunc(), access.AccDown)
		accessRt.POST("conftosql", Auths[5].MiddlewareFunc(), access.Conf2SQL)
		accessRt.POST("submitsql", Auths[5].MiddlewareFunc(), access.SubmitSQL)
		accessRt.POST("goto", Auths[0].MiddlewareFunc(), access.AccGoto)
		accessRt.POST("kafkaFind", Auths[0].MiddlewareFunc(), access.KafkaFind)
		accessRt.POST("newkettle", Auths[0].MiddlewareFunc(), access.NewKettle)
		accessRt.POST("newkettlev2", Auths[0].MiddlewareFunc(), access.NewKettlev2)
		accessRt.POST("cayman/listobjects", Auths[0].MiddlewareFunc(), access.ListObjects)

		//开发者的数据地图需要的数据
		accessRt.POST("dev/datamap", Auths[0].MiddlewareFunc(), access.DataMap)
		// 数据质量报告导出接口
		accessRt.POST("dataquality/export", access.DataQualityExport)

		//Kafka实时开发
		accessRt.POST("kafka/registerstatus", Auths[0].MiddlewareFunc(), access.KafkaRegisterStatus)
		accessRt.POST("database/pkstatus", Auths[0].MiddlewareFunc(), access.DbPkstatus)
		accessRt.POST("dataquality/detailexport", access.DataQualityDetailExport)

		//查询治理字段探查规则
		//accessRt.POST("search/watchrules", access.SearchWatchRules)

		// 实时运维任务列表获取
		accessRt.POST("realtime/tasklists", Auths[0].MiddlewareFunc(), access.RealTimeTaskList)
		// 实时运维任务操作，上下线，暂停，重启...
		accessRt.POST("realtime/operate", Auths[0].MiddlewareFunc(), access.OperateTasks)
		// 实时运维 查看运行状态，速率，日志信息。
		//accessRt.POST("realtime/runninginfo", Auths[0].MiddlewareFunc(), access.RealTimeRunningInfo)

		accessRt.POST("realtime/submittasks", Auths[0].MiddlewareFunc(), access.SubmitTasks)
		accessRt.POST("realtime/taskstatus", Auths[0].MiddlewareFunc(), access.TaskStatus)
		accessRt.POST("realtime/quicksilver", Auths[0].MiddlewareFunc(), access.QuickSilverStatus)
		//accessRt.POST("realtime/taskresult", Auths[0].MiddlewareFunc(), access.TaskResult)
		//accessRt.POST("realtime/startconsume", Auths[0].MiddlewareFunc(), access.StartConsume)
		accessRt.POST("realtime/pushapi", Auths[0].MiddlewareFunc(), access.PushApi)
		accessRt.POST("realtime/settoken", Auths[0].MiddlewareFunc(), access.SetToken) //仅供自测，相关参数根据实际需要改动
		//accessRt.POST("realtime/kerberosdataformat", Auths[0].MiddlewareFunc(), access.KerberosDataFormat)//内部待定接口，与前端无对接
		//执行shell命令
		accessRt.POST("execshell", access.ExecShell)
		//flink

		accessRt.POST("flinksessions", access.FlinkSessions)
		accessRt.POST("flinkoperate", access.FlinkOperate)
		accessRt.POST("flinkindexinfo", access.FlinkIndexInfo)
		accessRt.POST("callbackurl:id", access.CallBackUrl)
		accessRt.POST("realtime/pushloglist", Auths[0].MiddlewareFunc(), access.PushLogList)
		accessRt.POST("realtime/realtaskdetail", Auths[0].MiddlewareFunc(), access.RealTaskDetail)
		accessRt.POST("realtime/realtasklog", Auths[0].MiddlewareFunc(), access.RealTaskLog)
	}

	//开发模块
	developRt := router.Group("/danastudio/ants/develop/")
	{
		//		developRt.Use(Auths[1].MiddlewareFunc())
		//实验操作
		developRt.POST("new/dev", Auths[1].MiddlewareFunc(), develop.NewDev)
		developRt.POST("new/devv2", Auths[1].MiddlewareFunc(), develop.NewDevv2)
		developRt.POST("star", Auths[1].MiddlewareFunc(), develop.DevStar)
		developRt.POST("top", Auths[1].MiddlewareFunc(), develop.DevTop)
		developRt.POST("run", Auths[1].MiddlewareFunc(), develop.DevRun)
		developRt.POST("stop", Auths[1].MiddlewareFunc(), develop.DevStop)
		developRt.POST("result", Auths[1].MiddlewareFunc(), develop.DevResult)
		developRt.POST("stopyarn", develop.StopYarn)
		//		developRt.GET("wsrun", develop.DevWSRun)
		developRt.POST("checksave", Auths[1].MiddlewareFunc(), develop.DevCheckSave)
		developRt.POST("save", Auths[1].MiddlewareFunc(), develop.DevSave)
		developRt.POST("savev2", Auths[1].MiddlewareFunc(), develop.DevSavev2)
		developRt.POST("detail", Auths[1].MiddlewareFunc(), develop.DevDetail)
		developRt.POST("rename", Auths[1].MiddlewareFunc(), develop.DevRename)
		developRt.POST("fetch", Auths[1].MiddlewareFunc(), develop.DevFetch)
		developRt.POST("search", Auths[1].MiddlewareFunc(), develop.DevSearch)
		developRt.POST("content", Auths[1].MiddlewareFunc(), develop.DevContent)
		developRt.POST("apipylist", Auths[1].MiddlewareFunc(), develop.APIPyList)
		developRt.POST("copy", Auths[1].MiddlewareFunc(), develop.DevCopy)
		developRt.POST("copyv2", Auths[1].MiddlewareFunc(), develop.DevCopyv2)
		developRt.POST("goto", Auths[1].MiddlewareFunc(), develop.DevGoto)

		developRt.POST("notelist", Auths[1].MiddlewareFunc(), develop.DevNoteList)
		//上线下线
		developRt.POST("submit", Auths[1].MiddlewareFunc(), develop.DevSubmit)
		developRt.POST("down", Auths[1].MiddlewareFunc(), develop.DevDown)

		//垃圾篓相关
		developRt.POST("delete", Auths[1].MiddlewareFunc(), develop.DevDelete)
		developRt.POST("deletev2", Auths[1].MiddlewareFunc(), develop.DevDeletev2)
		developRt.POST("trash/delete", Auths[1].MiddlewareFunc(), develop.DevTrashDelete)
		developRt.POST("trash/recover", Auths[1].MiddlewareFunc(), develop.DevTrashRecover)

		//列表相关
		developRt.POST("list/recent", Auths[1].MiddlewareFunc(), develop.DevListRecent) //列举最近修改的实验
		developRt.POST("list/trash", Auths[1].MiddlewareFunc(), develop.DevListTrash)   //列举垃圾篓的实验

		developRt.POST("recommend/tags", Auths[1].MiddlewareFunc(), develop.DevRecommendTags) //推荐使用频繁的标签
		developRt.POST("delif", Auths[1].MiddlewareFunc(), develop.DevDelif)
		//developRt.POST("recif", Auths[1].MiddlewareFunc(), develop.DevRecif)
		developRt.POST("move", Auths[1].MiddlewareFunc(), develop.DevMove)
		developRt.POST("movev2", Auths[1].MiddlewareFunc(), develop.DevMovev2)

		// 脚本导出功能
		developRt.GET("scriptsexport", Auths[1].MiddlewareFunc(), develop.ScriptsExport)
		developRt.POST("scriptsexportnew", Auths[1].MiddlewareFunc(), develop.ScriptsExportNew)
		developRt.POST("scriptsimportnew", Auths[1].MiddlewareFunc(), develop.ScriptsImportNew) //丢弃
		//脚本全局参数相关
		developRt.POST("globalget", Auths[1].MiddlewareFunc(), develop.DevGlobalFetch)
		developRt.POST("syncdata", Auths[1].MiddlewareFunc(), develop.SyncData)

		//5.9.1
		developRt.POST("generate/version", Auths[1].MiddlewareFunc(), develop.GenerateVersion) //生成版本
		developRt.POST("list/history", Auths[1].MiddlewareFunc(), develop.ListHistory)         //列举版本
	}

	//分析模块
	analyseRt := router.Group("/danastudio/ants/analyse/")
	{
		//		analyseRt.Use(Auths[2].MiddlewareFunc())
		//交互式部分
		analyseRt.POST("new/interpreter", Auths[2].MiddlewareFunc(), analyse.NewInterpreter)
		analyseRt.POST("list/interpreter", Auths[2].MiddlewareFunc(), analyse.ListInterpreter)

		//算法模型实验操作
		analyseRt.POST("new/ana", Auths[2].MiddlewareFunc(), analyse.NewAna)
		analyseRt.POST("star", Auths[2].MiddlewareFunc(), analyse.AnaStar)
		analyseRt.POST("top", Auths[2].MiddlewareFunc(), analyse.AnaTop)
		analyseRt.POST("run", Auths[2].MiddlewareFunc(), analyse.AnaRun)
		analyseRt.POST("stop", Auths[2].MiddlewareFunc(), analyse.AnaStop)
		analyseRt.POST("search", Auths[2].MiddlewareFunc(), analyse.AnaSearch)
		analyseRt.POST("detail", Auths[2].MiddlewareFunc(), analyse.AnaDetail) //查看详情
		analyseRt.POST("update", Auths[2].MiddlewareFunc(), analyse.AnaUpdate) //更新详情
		analyseRt.POST("rename", Auths[2].MiddlewareFunc(), analyse.AnaRename) //重命名
		analyseRt.POST("copy", Auths[2].MiddlewareFunc(), analyse.AnaCopy)

		//上线下线
		analyseRt.POST("list/paragraphs", Auths[2].MiddlewareFunc(), analyse.AnaListParagraph) //获取段落列表
		analyseRt.POST("paragraph/text", Auths[2].MiddlewareFunc(), analyse.AnaGetText)        //获取段落内容
		analyseRt.POST("submit", Auths[2].MiddlewareFunc(), analyse.DataAnaSubmit)
		analyseRt.POST("down", Auths[2].MiddlewareFunc(), analyse.DataAnaDown)

		//垃圾篓相关
		analyseRt.POST("delete", Auths[2].MiddlewareFunc(), analyse.AnaDelete)
		analyseRt.POST("trash/delete", Auths[2].MiddlewareFunc(), analyse.AnaTrashDelete)
		analyseRt.POST("trash/recover", Auths[2].MiddlewareFunc(), analyse.AnaTrashRecover)

		//列表相关
		analyseRt.POST("list/base", Auths[2].MiddlewareFunc(), analyse.AnaListBase)     //列出模型或者例子
		analyseRt.POST("list/recent", Auths[2].MiddlewareFunc(), analyse.AnaListRecent) //列举最近修改的实验
		analyseRt.POST("list/trash", Auths[2].MiddlewareFunc(), analyse.AnaListTrash)   //列举垃圾篓的实验

		//工作流相关
		//		analyseRt.POST("workflow/in", analyse.AnaFlowIn)
		//		analyseRt.POST("workflow/add", analyse.AnaFlowAdd)
		//		analyseRt.POST("workflow/subtract", analyse.AnaFlowSubtract)
		//		analyseRt.POST("workflow/submit", analyse.AnaFlowSubmit)
		//		analyseRt.POST("workflow/down", analyse.AnaFlowDown)
		//		analyseRt.POST("workflow/rename", analyse.AnaFlowRename)

		//		analyseRt.POST("workflow/out", Auths[2].MiddlewareFunc(), analyse.AnaFlowOut)

		//其他
		analyseRt.POST("recommend/tags", Auths[2].MiddlewareFunc(), analyse.AnaRecommendTags) //推荐使用频繁的标签
		analyseRt.POST("delif", Auths[2].MiddlewareFunc(), analyse.AnaDelif)
		//analyseRt.POST("recif", Auths[2].MiddlewareFunc(), analyse.AnaRecif)
	}

	//工作流模块
	wfRt := router.Group("/danastudio/ants/workflow/")
	{
		wfRt.Use(Auths[3].MiddlewareFunc())
		//算法模型实验操作
		wfRt.POST("new", workflow.NewWf)
		wfRt.POST("newv2", workflow.NewWfv2)
		wfRt.POST("star", workflow.WfStar)
		wfRt.POST("top", workflow.WfTop)
		wfRt.POST("run", workflow.WfRun)
		wfRt.POST("stop", workflow.WfStop)
		wfRt.POST("content", workflow.WfContent)
		wfRt.POST("contentv2", workflow.WfContentv2)
		wfRt.POST("save", workflow.WfSave)
		wfRt.POST("savev2", workflow.WfSavev2)
		wfRt.POST("result", workflow.WfResult)
		wfRt.POST("search", workflow.WfSearch)
		wfRt.POST("searchv2", workflow.WfSearchv2)
		wfRt.POST("detail", workflow.WfDetail) //获取详情
		wfRt.POST("update", workflow.WfUpdate) //修改详情
		wfRt.POST("rename", workflow.WfRename)
		wfRt.POST("move", workflow.WfMove) //文件移动
		wfRt.POST("movev2", workflow.WfMovev2)
		//上线下线
		wfRt.POST("submit", workflow.WfSubmit)
		wfRt.POST("down", workflow.WfDown)
		//4.6.1 新增 工作流上线策略保存
		wfRt.POST("schedule", workflow.WfSchedule)
		wfRt.POST("schedulev2", workflow.WfSchedulev2)

		//垃圾篓相关
		wfRt.POST("delete", workflow.WfDelete)
		wfRt.POST("deletev2", workflow.WfDeletev2)
		wfRt.POST("trash/delete", workflow.WfTrashDelete)
		wfRt.POST("trash/recover", workflow.WfTrashRecover)

		//列表相关
		wfRt.POST("list/recent", workflow.WfListRecent) //列举最近修改的实验
		wfRt.POST("list/trash", workflow.WfListTrash)   //列举垃圾篓的实验

		//其他
		wfRt.POST("recommend/tags", workflow.WfRecommendTags) //推荐使用频繁的标签

		//新增任务详情
		wfRt.POST("taskdetail", workflow.WFTaskDetail)

		//工作流导入
		wfRt.POST("import", workflow.WFImportNew)
		wfRt.POST("inout/details", workflow.WFIODetails)

		//工作流导出
		wfRt.GET("wfexport", workflow.WfExport)

		//列举已上线工作流、数据治理、数据同步任务
		wfRt.POST("list/submitjob", workflow.ListSubmitJob)
		wfRt.POST("list/submitjobv2", workflow.ListSubmitJobV2)
		//获取前置任务的执行策略
		wfRt.POST("get/precrontab", workflow.GetPreCrontab)
		//列举已上线数据治理、数据同步任务
		wfRt.POST("list/marjob", workflow.ListMarJob)
		//批量上线任务
		wfRt.POST("batchsubmit", workflow.WfBatchSubmit)
	}

	//数据治理模块
	dataproRT := router.Group("/danastudio/ants/dataprocess/")
	{
		//wfRt.Use(Auths[4].MiddlewareFunc())
		//project
		dataproRT.POST("check/engine", Auths[4].MiddlewareFunc(), dataprocess.CheckEngine)
		dataproRT.POST("list/layer", Auths[4].MiddlewareFunc(), dataprocess.ListLayer)
		dataproRT.POST("get/database", Auths[4].MiddlewareFunc(), dataprocess.GetDatabase)
		dataproRT.POST("create/project", Auths[4].MiddlewareFunc(), dataprocess.CreateProject)
		dataproRT.POST("list/user", Auths[4].MiddlewareFunc(), dataprocess.ListUser)
		dataproRT.POST("delete/project", Auths[4].MiddlewareFunc(), dataprocess.DeleteProject)
		dataproRT.POST("update/project", Auths[4].MiddlewareFunc(), dataprocess.UpdateProject)
		dataproRT.POST("search/project", Auths[4].MiddlewareFunc(), dataprocess.SearchProject)
		dataproRT.POST("count/task", Auths[4].MiddlewareFunc(), dataprocess.CountTask)
		dataproRT.POST("check/checkprojectstatus", Auths[4].MiddlewareFunc(), dataprocess.CheckProjectStatus) //判断project状态
		//task
		dataproRT.POST("create/task", Auths[4].MiddlewareFunc(), dataprocess.CreateTask)
		dataproRT.POST("search/task", Auths[4].MiddlewareFunc(), dataprocess.SearchTask)
		dataproRT.POST("delete/task", Auths[4].MiddlewareFunc(), dataprocess.DeleteTask)
		dataproRT.POST("update/task", Auths[4].MiddlewareFunc(), dataprocess.UpdateTask)
		dataproRT.POST("list/tags", Auths[4].MiddlewareFunc(), dataprocess.ListTags)
		dataproRT.POST("submit", Auths[4].MiddlewareFunc(), dataprocess.Submit)
		dataproRT.POST("down", Auths[4].MiddlewareFunc(), dataprocess.Down)
		dataproRT.POST("batchdown", Auths[4].MiddlewareFunc(), dataprocess.BatchDown)
		dataproRT.POST("submit/judge", Auths[4].MiddlewareFunc(), dataprocess.JudgeSubmit) //判断是否上线，上线模组不可操作
		//左侧菜单
		dataproRT.POST("list/task", Auths[4].MiddlewareFunc(), dataprocess.ListTask)
		dataproRT.POST("list/module", Auths[4].MiddlewareFunc(), dataprocess.ListModule)
		//流程
		dataproRT.POST("new/module", Auths[4].MiddlewareFunc(), datagovern.NewModule)
		dataproRT.POST("new/modulev2", Auths[4].MiddlewareFunc(), datagovern.NewModulev2)
		dataproRT.POST("query/module", Auths[4].MiddlewareFunc(), datagovern.QueryModule) //模组查询
		dataproRT.POST("module/delete", Auths[4].MiddlewareFunc(), dataprocess.ModuleDelete)
		dataproRT.POST("save", Auths[4].MiddlewareFunc(), dataprocess.Save)
		dataproRT.POST("content", Auths[4].MiddlewareFunc(), dataprocess.DpContent)
		dataproRT.POST("run", Auths[4].MiddlewareFunc(), dataprocess.DpRun)
		dataproRT.POST("stop", Auths[4].MiddlewareFunc(), dataprocess.DpStop)
		dataproRT.POST("update/module", Auths[4].MiddlewareFunc(), dataprocess.UpdateModule)
		dataproRT.POST("tags/list", Auths[4].MiddlewareFunc(), dataprocess.ListMTags)
		dataproRT.POST("lock", Auths[4].MiddlewareFunc(), dataprocess.Lock)
		dataproRT.POST("show/result", Auths[4].MiddlewareFunc(), dataprocess.ShowResult)
		dataproRT.POST("batch/save", Auths[4].MiddlewareFunc(), dataprocess.BatchSave) //一键保存加工池(状态置为正常)

		//表输入
		dataproRT.POST("list/database", Auths[4].MiddlewareFunc(), dataprocess.ListDatabase) //!!!已弃用
		dataproRT.POST("list/tbinfo", Auths[4].MiddlewareFunc(), dataprocess.ListTbinfo)
		dataproRT.POST("list/schema", Auths[4].MiddlewareFunc(), dataprocess.ListSchema) //!!!已弃用
		//dataproRT.POST("check/timefield", dataprocess.CheckTimefield)
		dataproRT.POST("preview", Auths[4].MiddlewareFunc(), dataprocess.Preview)
		// dataproRT.POST("table/save", Auths[4].MiddlewareFunc(), dataprocess.SaveTable)       //！！！该表输入保存接口已弃用
		dataproRT.POST("table/savepre", Auths[4].MiddlewareFunc(), dataprocess.SaveTablePre) // 表输入的保存
		dataproRT.POST("list/function", Auths[4].MiddlewareFunc(), dataprocess.ListFun)
		dataproRT.POST("get/tbname", Auths[4].MiddlewareFunc(), dataprocess.GetTbname)
		dataproRT.POST("where/judge", Auths[4].MiddlewareFunc(), dataprocess.WhereJudge)         //where语句判断
		dataproRT.POST("timefield/judge", Auths[4].MiddlewareFunc(), dataprocess.TimefieldJudge) //增量字段的判断
		dataproRT.POST("mainkey/duplicate", Auths[4].MiddlewareFunc(), dataprocess.KeyDuplicate)
		//dataproRT.POST("update/table", dataprocess.UpdateTable)
		//4.7 新增
		dataproRT.POST("list/metadata", Auths[4].MiddlewareFunc(), dataprocess.ListMetaData)

		dataproRT.POST("watch/info", Auths[4].MiddlewareFunc(), datagovern.WatchInfo)
		dataproRT.POST("watch/infov2", Auths[4].MiddlewareFunc(), datagovern.WatchInfo)
		dataproRT.POST("watch/tableget", Auths[4].MiddlewareFunc(), datagovern.WatchTableGet)
		dataproRT.POST("watch/save", Auths[4].MiddlewareFunc(), datagovern.WatchSave)
		dataproRT.POST("watch/savev2", Auths[4].MiddlewareFunc(), datagovern.WatchSaveV2) //5.7.0 探查模组保存
		dataproRT.POST("watch/showresult", Auths[4].MiddlewareFunc(), datagovern.WatchResult)
		//5.5.1新增
		dataproRT.GET("checkudf:id", datagovern.CheckUdf)
		dataproRT.GET("checkudfre:id", datagovern.CheckUdfRE) //5.7.0 udf
		dataproRT.POST("createwttb", datagovern.CreateWTTB)   //创建问题表
		dataproRT.POST("checkdup", datagovern.CheckDup)       //检测重复数据探查
		dataproRT.POST("rulesync", datagovern.RuleSync)

		dataproRT.POST("standard/tableget", Auths[4].MiddlewareFunc(), datagovern.StandardTableGet)
		dataproRT.POST("standard/fieldget", Auths[4].MiddlewareFunc(), datagovern.StandardFieldget)
		dataproRT.POST("standard/fieldgroup", Auths[4].MiddlewareFunc(), datagovern.StandardFieldGroup)
		dataproRT.POST("standard/info", Auths[4].MiddlewareFunc(), datagovern.StandardInfo)
		// dataproRT.POST("standard/save", Auths[4].MiddlewareFunc(), datagovern.StandardSave) //! ! ! 该标准化保存接口已启用
		dataproRT.POST("standard/savepre", Auths[4].MiddlewareFunc(), datagovern.StandardSavePre) // 标准化保存接口。
		dataproRT.POST("standard/showresult", Auths[4].MiddlewareFunc(), datagovern.StandardResult)
		// dataproRT.POST("standard/middletableexist", Auths[4].MiddlewareFunc(), datagovern.StandardMiddleTableExist)

		dataproRT.POST("cooking/listmodule", Auths[4].MiddlewareFunc(), dataprocess.Listmodule)
		dataproRT.POST("cooking/fieldget", Auths[4].MiddlewareFunc(), datagovern.CookingFieldget)
		dataproRT.POST("cooking/showresult", Auths[4].MiddlewareFunc(), datagovern.CookingShowResult)
		dataproRT.POST("cooking/fieldenum", Auths[4].MiddlewareFunc(), datagovern.CookingShowEnum)
		dataproRT.POST("cooking/inputsave", Auths[4].MiddlewareFunc(), datagovern.CookingInputSave)
		dataproRT.POST("cooking/inputinfo", Auths[4].MiddlewareFunc(), datagovern.CookingInputInfo)

		//数据加工表输出加工
		dataproRT.POST("cooking/outputableget", Auths[4].MiddlewareFunc(), datagovern.CookingOutputTableGet)  //数据加工-表输入的字段类型
		dataproRT.POST("cooking/outputfieldget", Auths[4].MiddlewareFunc(), datagovern.CookingOutputFieldGet) //数据加工时的字段预览
		dataproRT.POST("cooking/splitnum", Auths[4].MiddlewareFunc(), datagovern.CookingOutputFieldGet)       //获取字段分裂的结果个数
		//dataproRT.POST("cooking/outputfieldget", datagovern.CookingOutputFieldGet) //数据加工时的字段预览
		dataproRT.POST("cooking/outputsave", Auths[4].MiddlewareFunc(), datagovern.CookingOutputSave)
		dataproRT.POST("cooking/outputshowresult", Auths[4].MiddlewareFunc(), datagovern.CookingOutputShowResult)
		dataproRT.POST("cooking/middlecanuse", Auths[4].MiddlewareFunc(), datagovern.CookingMiddleCanUse)
		dataproRT.POST("cooking/sqltest", datagovern.SqlTest) //测试用
		//dataproRT.POST("cooking/sqlrefactor", datagovern.SqlRefactor) //测试用
		dataproRT.POST("cooking/parsejson", datagovern.ParseJson) //解析json
		//表输出
		dataproRT.POST("list/subject", Auths[4].MiddlewareFunc(), dataprocess.ListSubject) //列举主题
		dataproRT.POST("output/preview", Auths[4].MiddlewareFunc(), dataprocess.OutputPreview)
		dataproRT.POST("output/save", Auths[4].MiddlewareFunc(), dataprocess.OutputSave)      //自 6.0 起弃用
		dataproRT.POST("output/savev2", Auths[4].MiddlewareFunc(), dataprocess.OutputSaveNew) //落地模组保存
		dataproRT.POST("output/info", Auths[4].MiddlewareFunc(), dataprocess.OutputInfo)
		dataproRT.POST("output/showtable", Auths[4].MiddlewareFunc(), dataprocess.OutputShowTable)
		dataproRT.POST("list/basedb", Auths[4].MiddlewareFunc(), dataprocess.ListBasedb) //列举治理层和主题层存储源
		//自定义脚本保存
		dataproRT.POST("custom/save", Auths[4].MiddlewareFunc(), datagovern.CustomSave)
		dataproRT.POST("custom/info", Auths[4].MiddlewareFunc(), datagovern.CustomInfo)
		dataproRT.POST("custom/tableexist", Auths[4].MiddlewareFunc(), datagovern.CustomTableExist)

		//预览版
		// dataproRT.POST("table/savepre", Auths[4].MiddlewareFunc(), dataprocess.SaveTablePre)            // 表输入的保存
		dataproRT.POST("table/getdimtablespre", Auths[4].MiddlewareFunc(), dataprocess.GetDimTablespre) // 获取字段代码表列表
		dataproRT.POST("table/getdimdatapre", Auths[4].MiddlewareFunc(), dataprocess.GetDimData)
		dataproRT.POST("table/getdimdatalistpre", Auths[4].MiddlewareFunc(), dataprocess.GetDimDataList)

		//试运行后预览所有模组
		dataproRT.POST("module/showresult", Auths[4].MiddlewareFunc(), datagovern.ModuleShowResult)

		//4.8.0 新增模组
		dataproRT.POST("module/showtable", Auths[4].MiddlewareFunc(), datagovern.ShowTable)     //查询原始表
		dataproRT.POST("access/fieldget", Auths[4].MiddlewareFunc(), datagovern.AccessFieldGet) //数据接入-联表字段
		dataproRT.POST("access/save", Auths[4].MiddlewareFunc(), datagovern.AccessSave)         //数据接入保存
		//5.0新增
		dataproRT.POST("sensitive/get", Auths[4].MiddlewareFunc(), datagovern.SensiInfoGet)                          //脱敏组件信息获取
		dataproRT.POST("sensitive/save", Auths[4].MiddlewareFunc(), datagovern.SensitiveSave)                        //脱敏组件保存
		dataproRT.POST("sensitiverank/save", Auths[4].MiddlewareFunc(), datagovern.SensitiveRankSave)                //保存敏感等级
		dataproRT.POST("sensitiverank/list", Auths[4].MiddlewareFunc(), datagovern.SensitiveRankList)                //敏感等级列举
		dataproRT.POST("sensitiverank/delete", Auths[4].MiddlewareFunc(), datagovern.SensitiveRankDelete)            //敏感等级删除
		dataproRT.POST("sensitivecheck/save", Auths[4].MiddlewareFunc(), datagovern.SensitiveCheckSave)              //保存敏感识别规则
		dataproRT.POST("sensitivecheck/list", Auths[4].MiddlewareFunc(), datagovern.SensitiveCheckList)              //敏感识别列举
		dataproRT.POST("sensitivecheck/delete", Auths[4].MiddlewareFunc(), datagovern.SensitiveCheckDelete)          //敏感识别规则删除
		dataproRT.POST("sensitivecheck/module/save", Auths[4].MiddlewareFunc(), datagovern.SensitiveModuleCheckSave) //敏感识别组件保存
		dataproRT.POST("sensitivecheck/module/list", Auths[4].MiddlewareFunc(), datagovern.SensitiveModuleCheckList) //敏感识别列举
		dataproRT.POST("sensitivecheck/module/get", Auths[4].MiddlewareFunc(), datagovern.SessionCheckModuleInfo)    //敏感组件信息获取
		dataproRT.GET("sensitivecheck/module/givedodoxrun:id", datagovern.SensitiveModuleCheckRun)                   //TODO // 运行敏感组件
		dataproRT.GET("outdelete:id", datagovern.DeleteHistory)

		//5.1 新增
		dataproRT.POST("module/clean", Auths[4].MiddlewareFunc(), datagovern.Clean) //连线删除后清空指定模组

		//5.5.2 新增
		dataproRT.POST("module/changestatus", Auths[4].MiddlewareFunc(), datagovern.ChangeStatus) //字段变更后修改后续模组状态
	}

	//对内接口
	InsideRt := router.Group("/danastudio/ants/inside/")
	{
		//		InsideRt.POST("accdown", access.AccDown2)
		//		InsideRt.POST("anadown", analyse.AnaDown2)
		//		InsideRt.POST("devdown", develop.DevDown2)
		//		InsideRt.POST("wfdown", workflow.WfDown2)
		//获取dodox节点
		InsideRt.POST("dodox/getnode", access.DodoxNode)
		//获取zeppelin的映射端口号
		InsideRt.POST("zeppelin/port", analyse.ZeppelinPort)
		InsideRt.POST("localip", datagovern.Localip)
		InsideRt.POST("icanrun", datagovern.CanIRun)
		InsideRt.POST("setjobmax", access.AccSetJobMax)
		InsideRt.POST("getjobmax", access.AccGetJobMax)
		InsideRt.POST("createtable", access.AccPgCreateTable)
		InsideRt.POST("attachtable", access.FixAttachTableName)

	}

	testRt := router.Group("/danastudio/ants/test/")
	{
		testRt.POST("spark/execsql", spark.SparkTest)
	}

	//文件夹操作
	folderRt := router.Group("/danastudio/ants/folder/")
	{
		folderRt.POST("create", Auths[0].MiddlewareFunc(), folder.CreateFolder)
		folderRt.POST("createv2", Auths[0].MiddlewareFunc(), folder.CreateFolderv2) //5.1 起弃用
		folderRt.POST("delete", Auths[0].MiddlewareFunc(), folder.DeleteFolder)
		folderRt.POST("check", Auths[0].MiddlewareFunc(), folder.CheckFolder)
		folderRt.POST("update", Auths[0].MiddlewareFunc(), folder.UpdateFolder)
		folderRt.POST("list", Auths[0].MiddlewareFunc(), folder.ListFolder)
		folderRt.POST("listv2", Auths[0].MiddlewareFunc(), folder.ListFolderv2) //5.1 起弃用
		folderRt.POST("getinfo", folder.FolderInfo)
	}

	// 元数据中心
	metaRt := router.Group("/danastudio/ants/metacenter/")
	{
		// 元模型
		metaRt.POST("listfolder", Auths[0].MiddlewareFunc(), metacenter.ListFolder)       // 列举文件夹及模型
		metaRt.POST("delfolder", Auths[0].MiddlewareFunc(), metacenter.DelFolder)         // 删除文件夹及模型
		metaRt.POST("createinfo", Auths[0].MiddlewareFunc(), metacenter.ShowCreateInfo)   // 列举文件夹及模型名称
		metaRt.POST("createmodule", Auths[0].MiddlewareFunc(), metacenter.CreateModule)   // 新建模型
		metaRt.POST("delmodule", Auths[0].MiddlewareFunc(), metacenter.DelModule)         // 删除模型
		metaRt.POST("revisemodule", Auths[0].MiddlewareFunc(), metacenter.ReviseModule)   // 修改模型详情
		metaRt.POST("listmodule", Auths[0].MiddlewareFunc(), metacenter.ListModule)       // 根据条件查询模型
		metaRt.POST("revisecatalog", Auths[0].MiddlewareFunc(), metacenter.ReviseCatalog) // 修改编目
		metaRt.POST("delcatalog", Auths[0].MiddlewareFunc(), metacenter.DelCatalog)       // 删除指定编目
		metaRt.POST("addcatalog", Auths[0].MiddlewareFunc(), metacenter.AddCatalog)       // 添加编目
		metaRt.POST("getmodule", Auths[0].MiddlewareFunc(), metacenter.GetModule)         // 获取模型详情
		metaRt.POST("getcatalog", Auths[0].MiddlewareFunc(), metacenter.GetCatalog)       // 获取编目信息
		metaRt.POST("getcattag", Auths[0].MiddlewareFunc(), metacenter.GetCatTag)         // 列举数据采集、治理、专题生成等所需相关元数据信息
		metaRt.POST("addmetarecord", Auths[0].MiddlewareFunc(), metacenter.AddMetaRecord) // 元数据采集添加元数据表记录
		// 元数据
		metaRt.POST("createtable", Auths[0].MiddlewareFunc(), metacenter.CreateTable)                    // 新建元数据表
		metaRt.POST("deltable", Auths[0].MiddlewareFunc(), metacenter.DelTable)                          // 删除元数据表
		metaRt.POST("listtable", Auths[0].MiddlewareFunc(), metacenter.ListTable)                        // 根据条件查询元数据表
		metaRt.POST("gettable", Auths[0].MiddlewareFunc(), metacenter.GetTable)                          // 获取元数据表详情
		metaRt.POST("reviseshowinfo", Auths[0].MiddlewareFunc(), metacenter.ReviseBasicShowInfo)         // 修改元数据表基本信息-上方展示信息/管理信息
		metaRt.POST("revisebaseinfo", Auths[0].MiddlewareFunc(), metacenter.ReviseBasicBaseInfo)         // 修改元数据表基本信息-基础信息
		metaRt.POST("revisebusinessinfo", Auths[0].MiddlewareFunc(), metacenter.ReviseBasicBusinessInfo) // 修改元数据表基本信息-业务信息
		metaRt.POST("revisefield", Auths[0].MiddlewareFunc(), metacenter.ReviseField)                    // 修改元数据表字段信息
		metaRt.POST("reviseblood", Auths[0].MiddlewareFunc(), metacenter.ReviseBlood)                    // 修改元数据表血缘关系
		metaRt.POST("history", Auths[0].MiddlewareFunc(), metacenter.History)                            // 获取元数据表历史详情
		metaRt.POST("exporttable", Auths[0].MiddlewareFunc(), metacenter.ExportTable)                    // 导出元数据表
		metaRt.POST("importtable", Auths[0].MiddlewareFunc(), metacenter.ImportTable)                    // 导入元数据表

	}

	dataQuality := router.Group("/danastudio/ants/dataquality/")
	{
		dataQuality.POST("rule/list", Auths[0].MiddlewareFunc(), datagovern.RuleList)
		dataQuality.POST("rule/save", Auths[0].MiddlewareFunc(), datagovern.RuleSave)
		dataQuality.POST("rule/detail", Auths[0].MiddlewareFunc(), datagovern.RuleDetail)
		dataQuality.POST("rule/delete", Auths[0].MiddlewareFunc(), datagovern.RuleDelete)
		dataQuality.POST("rule/relatedtasks", Auths[0].MiddlewareFunc(), datagovern.RelatedTasks)
		dataQuality.POST("rule/getdimension", Auths[0].MiddlewareFunc(), datagovern.GetDemension)
		dataQuality.POST("rule/checkname", Auths[0].MiddlewareFunc(), datagovern.CheckName)
		// 质量报告配置
		dataQuality.POST("reportconf/save", Auths[0].MiddlewareFunc(), datagovern.QualityReportConfSave)
		dataQuality.POST("reportconf/get", Auths[0].MiddlewareFunc(), datagovern.QualityReportConfGet)
		dataQuality.POST("reportconf/save2", Auths[0].MiddlewareFunc(), datagovern.QualityReportConfPermissionSave) //弃用
		dataQuality.POST("report/create", Auths[0].MiddlewareFunc(), metrics.CreateMetricsReport)                   //默认报告，590 起弃用
		dataQuality.POST("report/createv2", Auths[0].MiddlewareFunc(), metrics.CreateMetricsReportV2)               //部门报告，590 起弃用
		dataQuality.POST("report/createv3", Auths[0].MiddlewareFunc(), metrics.CreateMetricsReportV3)               //总体报告，590 起弃用
		dataQuality.POST("report/createv4", Auths[0].MiddlewareFunc(), metrics.CreateMetricsReportV4)               //590 手动生成报告
		dataQuality.POST("report/dodoxcreate", metrics.DodoxCreateMetricsReport)                                    //590 起弃用
		dataQuality.POST("report/dodoxcreatev2", metrics.DodoxCreateMetricsReportV2)                                //590 版本自动生成报告
		dataQuality.POST("report/stop", Auths[0].MiddlewareFunc(), metrics.MetricsReportStop)
		dataQuality.POST("report/query", Auths[0].MiddlewareFunc(), metrics.QueryMetricsReport)
		dataQuality.GET("report/download", Auths[0].MiddlewareFunc(), metrics.DownLoadMetricsReport)
		dataQuality.POST("report/del", Auths[0].MiddlewareFunc(), metrics.DelMerticsReport)
		dataQuality.POST("report/innerdel", metrics.InnerDelReport)
		dataQuality.POST("report/upload", Auths[0].MiddlewareFunc(), metrics.UploadFirstPage)
		dataQuality.GET("report/firstdown", Auths[0].MiddlewareFunc(), metrics.DownLoadFirstPage)

		//质量报告模板
		dataQuality.POST("report/template/save", Auths[0].MiddlewareFunc(), metrics.UpdateTemplate)
		dataQuality.POST("report/template/add", Auths[0].MiddlewareFunc(), metrics.CreateTemplate)
		dataQuality.POST("report/template/delete", Auths[0].MiddlewareFunc(), metrics.DeleteTemplate)
		dataQuality.POST("report/template/query", Auths[0].MiddlewareFunc(), metrics.QueryTemplate)
		dataQuality.POST("report/template/list", Auths[0].MiddlewareFunc(), metrics.ListTemplate)
		dataQuality.GET("report/template/getdoc", Auths[0].MiddlewareFunc(), metrics.GetDoc)
		dataQuality.POST("report/template/savedoc", Auths[0].MiddlewareFunc(), metrics.DocUpload)
		dataQuality.POST("report/ruleweight/save", Auths[0].MiddlewareFunc(), datagovern.SaveRuleweight)   //保存规则权重
		dataQuality.POST("report/ruleweight/query", Auths[0].MiddlewareFunc(), datagovern.QueryRuleweight) //查询规则权重
	}
	///分析部分
	//		NewRt.POST("model", httphandler.NewModel)
	//		NewRt.POST("example", httphandler.NewExample)

	//		NewRt.POST("blankml", httphandler.NewAnalysis)
	//		NewRt.POST("build/get", httphandler.BuildGet)
	//		NewRt.POST("build/check", httphandler.BuildCheck)

	///监听端口
	logger.Info.Println("danastudio ants start ... ")

	fmt.Printf("[ INFO ] danastudio ants start [%v]... \n", common.SERVER_PORT)
	http.ListenAndServe(":"+strconv.Itoa(common.SERVER_PORT), router)

}
func LoadConf() {
	cfg, err := ini.Load(common.SERVER_CONF1)
	if err != nil {
		fmt.Println("[ ERROR ]", err)
		cfg, err = ini.Load(common.SERVER_CONF2)
		fmt.Println("[ ERROR ]", err)
	}

	secServer, _ := cfg.GetSection("server")
	dip, _ := secServer.GetKey("ip")
	common.SERVER_IP = dip.Value()
	keyPort, _ := secServer.GetKey("port")
	common.SERVER_PORT, _ = keyPort.Int()

	//secServer, _ = cfg.GetSection("zeppelin")
	//dip, _ = secServer.GetKey("zip")
	//common.ZEPPELIN_IP = dip.Value()
	//httpclient.ZEPPELIN_SERVER = dip.Value()
	//keyPort, _ = secServer.GetKey("zport")
	//common.ZEPPELIN_ZPORT, _ = keyPort.Int()
	//httpclient.ZEPPELIN_PORT, _ = keyPort.Int()
	//keyZPort, _ := secServer.GetKey("ztport")
	//common.ZEPPELIN_ZTPORT, _ = keyZPort.Int()

	secDatabase, _ := cfg.GetSection("database")
	eaglesip, _ := secDatabase.GetKey("eagles_ip")
	httpclient.EaglesIP = eaglesip.Value()
	httpclient.MetaIP = eaglesip.Value()
	common.EAGLES_IP = eaglesip.Value()

	eaglesport, _ := secDatabase.GetKey("eagles_port")
	httpclient.EaglesPort, _ = eaglesport.Int()
	common.EAGLES_PORT, _ = eaglesport.Int()

	secServer, _ = cfg.GetSection("jobmanager")
	dip, _ = secServer.GetKey("ip")
	common.JOB_IP = dip.Value()
	keyPort, _ = secServer.GetKey("port")
	common.JOB_PORT, _ = keyPort.Int()

	secServer, _ = cfg.GetSection("filemanager")
	dip, _ = secServer.GetKey("fip")
	common.FILE_IP = dip.Value()
	keyPort, _ = secServer.GetKey("fport")
	common.FILE_PORT, _ = keyPort.Int()

	secServer, _ = cfg.GetSection("platform")
	dip, _ = secServer.GetKey("platip")
	common.PLAT_IP = dip.Value()
	keyPort, _ = secServer.GetKey("platport")
	common.PLAT_PORT, _ = keyPort.Int()

	// [log] log level, log path, log day
	secLog, _ := cfg.GetSection("log")
	keyPath, _ := secLog.GetKey("path")
	if len(keyPath.Value()) == 0 || keyPath == nil {
		logger.SERVER_LOG = "/var/dana/log/danastudio/ants/"
	} else {
		logger.SERVER_LOG = keyPath.Value()
	}

	keylogdays, _ := secLog.GetKey("log_cleanup_time")
	if len(keylogdays.Value()) == 0 || keylogdays == nil {
		logger.SERVER_LOG_DAYS = 1
	} else {
		logger.SERVER_LOG_DAYS, _ = keylogdays.Int()
	}
	keyloglevel, _ := secLog.GetKey("level")
	if len(keyloglevel.Value()) == 0 || keyloglevel == nil {
		logger.SERVER_LOG_LEVEL = 2
	} else {
		logger.SERVER_LOG_LEVEL, _ = keyloglevel.Int()
	}
	//新增参数
	keyServerLogSplitTime, _ := secLog.GetKey("log_renew_time")
	if len(keyServerLogSplitTime.Value()) == 0 || keyServerLogSplitTime == nil {
		logger.ServerLogSplitSize = 100
	} else {
		logger.ServerLogSplitSize, _ = keyServerLogSplitTime.Int64()
	}
	keyServerLogIsCheckFile, _ := secLog.GetKey("logischeckfile")
	if len(keyServerLogIsCheckFile.Value()) == 0 || keyServerLogIsCheckFile == nil {
		logger.ServerLogIsCheckFile = true
	} else {
		logger.ServerLogIsCheckFile, _ = keyServerLogIsCheckFile.Bool()
	}
	keyServerLogCheck, _ := secLog.GetKey("logcheck")
	if len(keyServerLogCheck.Value()) == 0 || keyServerLogCheck == nil {
		logger.ServerLogCheck = 1
	} else {
		logger.ServerLogCheck, _ = keyServerLogCheck.Int()
	}
	keyServerLogCheckTime, _ := secLog.GetKey("logchecktime")
	if len(keyServerLogCheckTime.Value()) == 0 || keyServerLogCheckTime == nil {
		logger.ServerLogCheckTime = "hour"
	} else {
		logger.ServerLogCheckTime = keyServerLogCheckTime.String()
	}
	keyServerLogSplitSize, _ := secLog.GetKey("log_size")
	if len(keyServerLogSplitSize.Value()) == 0 || keyServerLogSplitSize == nil {
		logger.ServerLogSplitSize = 100
	} else {
		logger.ServerLogSplitSize, _ = keyServerLogSplitSize.Int64()
	}
	keyServerLogSplitSizeUnit, _ := secLog.GetKey("logsplitsizeunit")
	if len(keyServerLogSplitSizeUnit.Value()) == 0 || keyServerLogSplitSizeUnit == nil {
		logger.ServerLogSplitSizeUnit = "MB"
	} else {
		logger.ServerLogSplitSizeUnit = keyServerLogSplitSizeUnit.String()
	}
	keyServerLogSplitFileIsHandle, _ := secLog.GetKey("logsplitfileishandle")
	if len(keyServerLogSplitFileIsHandle.Value()) == 0 || keyServerLogSplitFileIsHandle == nil {
		logger.ServerLogSplitFileIsHandle = true
	} else {
		logger.ServerLogSplitFileIsHandle, _ = keyServerLogSplitFileIsHandle.Bool()
	}
	keyServerLogSplitFileHandle, _ := secLog.GetKey("logsplitfilehandle")
	if len(keyServerLogSplitFileHandle.Value()) == 0 || keyServerLogSplitFileHandle == nil {
		logger.ServerLogSplitFileHandle = 1
	} else {
		logger.ServerLogSplitFileHandle, _ = keyServerLogSplitFileHandle.Int()
	}
	dataxDir, _ := secLog.GetKey("dataxdir")
	common.DataxDir = dataxDir.String()
	thread.LogCreat()

	//日志文件
	logfileLimit, err := secLog.GetKey("logfilelimit")
	if err != nil || logfileLimit == nil {
		common.LogFileLimit = 1
	} else {
		common.LogFileLimit, _ = logfileLimit.Float64()
	}
	logfileMax, err := secLog.GetKey("logfilemax")
	if err != nil || logfileLimit == nil {
		common.LogFileMax = 50
	} else {
		common.LogFileMax, _ = logfileMax.Float64()
	}

	secRealTime, _ := cfg.GetSection("realtime")
	KafkaInfoPath, _ := secRealTime.GetKey("kafkainfopath")
	common.KafkaInfoPath = KafkaInfoPath.Value()

	storkInfo, err := cfg.GetSection("stork")
	if err != nil {
		fmt.Println("[ ERROR ] 获取配置信息[stork]失败")
	} else {
		storkUserPassword, _ := storkInfo.GetKey("user_stork_password")
		sshPort, _ := storkInfo.GetKey("ssh_port")
		sIntV, _ := sshPort.Int()
		if sIntV != 0 {
			common.RStorkDetail.SSH_PORT = sIntV
		}

		if storkUserPassword.String() != "" {
			common.RStorkDetail.StorkPassWord = storkUserPassword.String()
		}
	}
	//----------
	//新增配置DrealDb配置
	realInfo, err := cfg.GetSection("drealdb")
	if err != nil {
		fmt.Println("[ ERROR ] 获取配置信息[drealdb]失败")
	} else {

		ip, _ := realInfo.GetKey("dreal_ip")
		if ip.String() != "" {
			common.DRealDbDetail.IP = ip.String()
		}

		port, _ := realInfo.GetKey("dreal_port")
		sIntV, _ := port.Int()
		if sIntV != 0 {
			common.DRealDbDetail.PORT = sIntV
		}

		username, _ := realInfo.GetKey("dreal_username")
		if username.String() != "" {
			common.DRealDbDetail.UserName = username.String()
		}

		password, _ := realInfo.GetKey("dreal_password")
		if password.String() != "" {
			common.DRealDbDetail.PassWord = password.String()
		}

		catalog, _ := realInfo.GetKey("dreal_catalogname")
		if catalog.String() != "" {
			catalogList := strings.Split(catalog.String(), ",")
			switch len(catalogList) {
			case 1:
				common.DRealDbDetail.CatalogName = catalogList[0]
			case 2:
				common.DRealDbDetail.CatalogName = catalogList[0]
				common.DRealDbDetail.PaimonCatalog = catalogList[1]
			}
			//common.DRealDbDetail.CatalogName = catalog.String()
		}
	}
	//-------------

	secServer, err = cfg.GetSection("datax")
	if err != nil {
		fmt.Println("[ ERROR ] 获取配置信息datax record失败")
	} else {
		record, _ := secServer.GetKey("Errlimit")
		common.ErrorLimit, _ = record.Int()
		// fmt.Println("--------------------------common.ErrorLimit--------------------", common.ErrorLimit)
	}
	secServer, err = cfg.GetSection("tdh")
	if err != nil {
		fmt.Println("[ ERROR ] 获取配置信息tdh clientpath失败")
	} else {
		client, _ := secServer.GetKey("clientpath")
		common.ClientPath = client.Value()
		haveKRB, _ := secServer.GetKey("havekrb")
		common.TDHHaveKRB = haveKRB.Value()
		KeytabPath, _ := secServer.GetKey("kerberosKeytabFilePath")
		common.TDHKerberosKeytabFilePath = KeytabPath.Value()
		KeytabPrin, _ := secServer.GetKey("kerberosPrincipal")
		common.TDHKerberosPrincipal = KeytabPrin.Value()
		TDHConfPath, _ := secServer.GetKey("TDHConfPath")
		common.TDHConfPath = TDHConfPath.Value()
	}

	secServer, err = cfg.GetSection("extract")
	if err != nil {
		fmt.Println("[ ERROR ] 获取配置批量并发数batchsyncnum 失败")
	} else {
		syncnum, _ := secServer.GetKey("batchsyncnum")
		common.BatchSyncNum, _ = syncnum.Int()
		fmt.Println("[ INFO ]", common.BatchSyncNum)
		dataxsyncnum, _ := secServer.GetKey("dataxsyncnum")
		common.DataxSyncNum, _ = dataxsyncnum.Int()
		fmt.Println("[ INFO ]", common.DataxSyncNum)
		//namenode
		activeNode, _ := secServer.GetKey("activenode")
		common.ActiveNode = activeNode.Value()
		standbyNode, _ := secServer.GetKey("standbynode")
		common.StandbyNode = standbyNode.Value()

		defaultrealm, err := secServer.GetKey("defaultrealm")
		if err != nil { //空值兼容
			common.DefaultRealm = ""
		} else {
			common.DefaultRealm = defaultrealm.Value()
		}
		fmt.Println("[ INFO ]", common.ActiveNode, common.StandbyNode, common.DefaultRealm)
	}
	//---------------------------------
	//添加临时表时间策略
	secServer, err = cfg.GetSection("tbdel")
	if err != nil {
		fmt.Println("[ ERROR ] 未进行临时表删除时间策略配置,将使用默认配置")
	} else {
		starttime, _ := secServer.GetKey("delstarttime")
		common.Delstarttime, _ = starttime.Int()
		deldays, _ := secServer.GetKey("deldays")
		common.Deldays, _ = deldays.Int()
	}

	//redis配置
	redisServer, err := cfg.GetSection("redis")
	if err != nil {
		common.REDIS_IP = "127.0.0.1"
		common.REDIS_PORT = 21633
	} else {
		redisIP, err := redisServer.GetKey("redisip")
		if err != nil {
			common.REDIS_IP = "127.0.0.1"
		} else {
			common.REDIS_IP = redisIP.Value()
		}
		redisPort, err := redisServer.GetKey("redisport")
		if err != nil {
			common.REDIS_PORT = 21633
		} else {
			common.REDIS_PORT, _ = redisPort.Int()
		}
	}

	//新增onlyoffice配置
	onlyoffice, err := cfg.GetSection("onlyoffice")
	if err != nil {
		common.OnlyOffice_IP = "127.0.0.1"
	} else {
		tempIP, err := onlyoffice.GetKey("onlyoffice_deploy_ip")
		if err != nil {
			common.OnlyOffice_IP = "127.0.0.1"
		} else {
			common.OnlyOffice_IP = tempIP.Value()
		}
		tempHost, err := onlyoffice.GetKey("onlyoffice_deploy_host")
		if err != nil {
			common.OnlyOffice_HOST = "127.0.0.1"
		} else {
			common.OnlyOffice_HOST = tempHost.Value()
		}
	}
	//报告的配置，新增是否开启数据标准指标
	reportConf, err := cfg.GetSection("report")
	if err != nil {
		common.Open_DataStandards = false
	} else {
		tempconf, err := reportConf.GetKey("open_datastandards")
		if err != nil {
			common.Open_DataStandards = false
		} else {
			if strings.ToLower(tempconf.Value()) == "true" {
				common.Open_DataStandards = true
			} else {
				common.Open_DataStandards = false
			}
		}
	}
	//负载均衡
	//err = CreateLoadFile()
	//if err != nil {
	//	fmt.Println("[ ERROR ] 创建负载均衡文件失败")
	//}
	//loadServer, err := cfg.GetSection("loadbalance")
	//if err != nil {
	//	fmt.Println("[ ERROR ] 加载负载均衡文件失败")
	//} else {
	//	nginxport, _ := loadServer.GetKey("nginxport")
	//	common.NginxPort, _ = nginxport.Int()
	//	loadnode, _ := loadServer.GetKey("node")
	//	common.LoadNode = loadnode.String()
	//	keepalived, _ := loadServer.GetKey("keepalived")
	//	common.Keepalived = keepalived.String()
	//}
	logger.Info.Println("load danastudio ants conf succeful ")
	fmt.Println("[ INFO ] load danastudio ants conf succeful ")
	return
}

func MakeFile() {

	os.MkdirAll(common.SUBMITTED, os.ModeDir)
	os.Chmod(common.SUBMITTED, 0777)

	os.MkdirAll(common.UNSUBMITTED, os.ModeDir)
	os.Chmod(common.UNSUBMITTED, 0777)

	os.MkdirAll(common.UNSUBMITTEDAPI, os.ModeDir)
	os.Chmod(common.UNSUBMITTEDAPI, 0777)

	os.MkdirAll(common.UNSUBMITTEDAPIERRFILE, os.ModeDir)
	os.Chmod(common.UNSUBMITTEDAPIERRFILE, 0777)

	os.MkdirAll(common.UNSUBMITTEDAPICSV, os.ModeDir)
	os.Chmod(common.UNSUBMITTEDAPICSV, 0777)

	os.MkdirAll(common.UNSUBMITTEDAPIJSON, os.ModeDir)
	os.Chmod(common.UNSUBMITTEDAPIJSON, 0777)

	os.MkdirAll(common.UNSUBMITTEDAPITEMPRUN, os.ModeDir)
	os.Chmod(common.UNSUBMITTEDAPITEMPRUN, 0777)
	sourceFile1 := "/etc/dt.d/danastudio/getglobalvar.py"
	sourceFile2 := "/etc/dt.d/danastudio/globalvar.py"
	sourceFile5 := "/etc/dt.d/danastudio/var.cfg"
	cmd1 := exec.Command("cp", sourceFile1, common.UNSUBMITTEDAPITEMPRUN)
	cmd2 := exec.Command("cp", sourceFile2, common.UNSUBMITTEDAPITEMPRUN)
	cmd5 := exec.Command("cp", sourceFile5, common.UNSUBMITTEDAPITEMPRUN)
	cmd1.Run()
	cmd2.Run()
	cmd5.Run()

	os.MkdirAll(common.UNSUBMITTEDAPIPYRUN, os.ModeDir)
	os.Chmod(common.UNSUBMITTEDAPIPYRUN, 0777)
	sourceFile3 := "/etc/dt.d/danastudio/getglobalvar.py"
	sourceFile4 := "/etc/dt.d/danastudio/globalvar.py"
	cmd3 := exec.Command("cp", sourceFile3, common.UNSUBMITTEDAPIPYRUN)
	cmd4 := exec.Command("cp", sourceFile4, common.UNSUBMITTEDAPIPYRUN)
	cmd3.Run()
	cmd4.Run()

	os.MkdirAll(common.TemPath, os.ModeDir)
	os.Chmod(common.TemPath, 0777)

	os.MkdirAll(common.ImportPath, os.ModeDir)
	os.Chmod(common.ImportPath, 0777)

	os.MkdirAll(common.ReportPath, os.ModeDir)
	os.Chmod(common.ReportPath, 0777)

	os.MkdirAll(common.QualityReportPath, os.ModeDir)
	os.Chmod(common.QualityReportPath, 0777)

	// 创建软链接，将/var/dana/report链接到/var/danastudio/report/insidereportpath
	cmd := exec.Command("ln", "-s", common.QualityReportPath, "/var/danastudio/report/insidereportpath")
	if err := cmd.Run(); err != nil {
		logger.Error.Printf("创建软链接失败: %v\n", err)
	} else {
		logger.Info.Println("成功创建软链接: /var/dana/report -> /var/danastudio/report/insidereportpath")
	}

	os.MkdirAll(common.QualityReportOutputPath, os.ModeDir)
	os.Chmod(common.QualityReportOutputPath, 0777)

	os.MkdirAll(common.QualityReportDocPath, os.ModeDir)
	os.Chmod(common.QualityReportDocPath, 0777)

	os.MkdirAll(common.QualityReportDepPath, os.ModeDir)
	os.Chmod(common.QualityReportDepPath, 0777)

	os.MkdirAll(common.QualityReportTemplatePath, os.ModeDir)
	os.Chmod(common.QualityReportTemplatePath, 0777)

	httpdo.CreateDataxFile("")
}

// InitEaglesData()初始化案例数据库
func InitEaglesData() {
	uri := fmt.Sprintf("/%s/%s/_mapping", common.DataBaseModel, common.TbModel)
	response, err := httpclient.Get(uri, "")
	if err != nil {
		var sour interface{}
		err = json.Unmarshal(response, &sour)
		if err != nil {
			logger.Error.Println(err)
			return
		}

		if sour.(map[string]interface{})["status"] == nil || sour.(map[string]interface{})["status"].(float64) != 404 {
			fmt.Println("[ ERROR ] eagle status wrong")
			return
		}

		command := fmt.Sprintf("sh /etc/danastudio/initdata.sh %s %s %s %s", common.EAGLES_IP, strconv.Itoa(common.EAGLES_PORT), common.DataBaseModel, common.TbModel)
		resp, err := exec.Command("/bin/sh", "-c", command).Output()
		if err != nil {
			logger.Error.Println("Initial eagles data for examples failed ", string(resp)+err.Error())
			return
		}
		logger.Info.Println("Initial eagles data for examples successful ")
		fmt.Println("[ ERROR ] Initial eagles data for examples successful ")
		return
	}

	fmt.Println("[ INFO ] eagles data for examples has been initialed already ")
	return
}

// 错件抽取错误文件文件夹
// 目的：单例抽取使用hive用户，不创建加权限的话创建失败
func CreateErrorData() {
	c := "mkdir -p /opt/dana/datax/errorData/"
	cmd := exec.Command("sh", "-c", c)
	_, _ = cmd.Output()
	c1 := "chmod 777 -R /opt/dana/datax/errorData/"
	cmd1 := exec.Command("sh", "-c", c1)
	_, _ = cmd1.Output()

}
func CreateLoadFile() error {
	c := "mkdir -p /etc/nginx/nginx-cluster/conf"
	os.MkdirAll(c, os.ModeDir)
	os.Chmod(c, 0777)
	c2 := "cp /etc/dt.d/danastudio/danastudio-cluster.conf  /etc/nginx/nginx-cluster/conf"
	cmd2 := exec.Command("sh", "-c", c2)
	_, err := cmd2.Output()
	if err != nil {
		return err
	}
	return nil
}
