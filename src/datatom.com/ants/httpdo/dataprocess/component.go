package dataprocess

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	escli "datatom.com/tools/common/escli"

	"datatom/gin.v1"

	"datatom.com/metadata/httpdo/collect"
	"datatom.com/metadata/httpdo/dbutil"

	"datatom.com/metadata/httpdo/devcenter"
	"datatom.com/metadata/httpdo/esutil"

	"datatom.com/ants/common"
	"datatom.com/ants/httpclient"
	. "datatom.com/ants/httpdo"

	//"datatom.com/ants/httpdo/access"

	"datatom.com/ants/httpdo/spark"
	"datatom.com/ants/logger"
	"datatom.com/ants/source"
	"datatom.com/tools/common/tools"
	httpdotools "datatom.com/tools/httpdo"

	//hive "github.com/dazheng/gohive"
	"errors"

	asset "datatom.com/asset/source"
	metadev "datatom.com/metadata/httpdo/devcenter"
	metasource "datatom.com/metadata/source"
	"github.com/go-ini/ini"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	_ "github.com/lib/pq"

	//_ "github.com/mattn/go-oci8"
	_ "github.com/prestodb/presto-go-client/presto"
	"github.com/tidwall/gjson"
)

func PrestoConnect(sp source.InstanceInfo, database string) (*sql.DB, error) {
	s := "http://" + sp.User + "@" + sp.Ip + ":" + strconv.Itoa(sp.Port) + "?catalog=hive&schema=" + database

	s = "http://root@*************:9988?catalog=hive&schema=test"
	//通过配置的presto获取连接信息
	//---------------------------------------------------------------------------------------
	cfg, err := ini.Load(common.PRESTO_CONF)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	secServer, _ := cfg.GetSection("config")
	keyip, _ := secServer.GetKey("presto_ip")
	ip := keyip.Value()
	keyPort, _ := secServer.GetKey("presto_port")
	port := keyPort.Value()
	s = "http://root@" + ip + ":" + port + "?catalog=hive&schema=" + database
	//fmt.Println(s)
	//fmt.Println("打印默认连接信息")
	//---------------------------------------------------------------------------------------
	db, err := sql.Open("presto", s)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	err = db.Ping()
	if err != nil {
		fmt.Println("连接失败")
		return nil, err
	}
	return db, nil
}

func Recordget(i int, fields []string, record []map[string]interface{}) ([]interface{}, error) {
	var records []interface{}
	i = i - 1
	for _, f := range fields {
		if len(record) > i {
			str, err := json.Marshal(record[i])
			if err != nil {
				logger.Error.Println(err)
				return records, err
			}
			f = strings.Replace(f, ".", "\\.", 1)
			value := gjson.Get(string(str), f).Value()
			records = append(records, value)
		}

	}
	return records, nil
}

func getDBcnn(info source.InstanceInfo) (*sql.DB, error) {
	var db *sql.DB
	pgLink := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		info.User,
		info.Passwd,
		info.Ip,
		info.Port,
		info.Database)

	db, err := sql.Open("postgres", pgLink)
	if err != nil {
		db.Close()
		return db, err
	}

	return db, nil
}

func sqlrows2Maps(rws *sql.Rows) ([]map[string]interface{}, error) {
	var rowMaps []map[string]interface{}
	var columns []string

	columns, err := rws.Columns()
	if err != nil {
		return rowMaps, err
	}

	values := make([]sql.RawBytes, len(columns))
	scans := make([]interface{}, len(columns))
	for i := range values {
		scans[i] = &values[i]
	}

	for rws.Next() {
		_ = rws.Scan(scans...)
		each := map[string]interface{}{}
		for i, col := range values {
			each[columns[i]] = string(col)
		}

		rowMaps = append(rowMaps, each)
	}

	return rowMaps, nil
}

func GetInstance(id string) (source.InstanceInfo, error) {
	var array source.InstanceInfo
	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"engineid": id,
					},
				},
			},
		},
		"size": 100,
	}
	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/danastudio_platform/tb_engine/_search")
	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return array, err
	}

	array.Databasetype = gjson.Get(string(response), "hits.hits.0._source.enginename").String()
	array.Engineid = gjson.Get(string(response), "hits.hits.0._source.engineid").String()
	array.Ip = gjson.Get(string(response), "hits.hits.0._source.ip").String()
	Port := gjson.Get(string(response), "hits.hits.0._source.port").Int()
	array.Port = int(Port)
	array.User = gjson.Get(string(response), "hits.hits.0._source.username").String()
	array.Passwd = gjson.Get(string(response), "hits.hits.0._source.password").String()
	array.Database = gjson.Get(string(response), "hits.hits.0._source.initdb").String()
	array.Enginename = gjson.Get(string(response), "hits.hits.0._source.name").String()

	return array, nil

}

// EngineInfo 根据引擎id获取引擎详细信息
func EngineInfo(engineid string) (metasource.EngineInfo, error) {
	var info metasource.EngineInfo
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"engineid": engineid,
					},
				},
			},
		},
	}
	fmt.Println(body)
	res, err := escli.NewESClientCl.SearchByTerm(common.DataBasePlatform, common.TableEngine, body)
	if err != nil {
		logger.Error.Printf("查询引擎信息失败:%v,%s", body, err.Error())
		return info, err
	}

	var infoHits metasource.EngineInfoHits
	err = json.Unmarshal(res, &infoHits)
	if err != nil {
		logger.Error.Printf("引擎信息解析失败:%s", err.Error())
		return info, err
	}
	if infoHits.Hits1.Total == 0 {
		errinfo := errors.New("平台管理该引擎已注销")
		fmt.Printf("平台管理该引擎已注销:%s", errinfo)
		//logger.Error.Printf("平台管理该引擎已注销:%s", errinfo)
		return info, errinfo
	}
	for _, hits := range infoHits.Hits1.Hits2 {
		info = hits.EngineInfoSource
	}
	// ----------------start 获取驱动表里面的驱动的路径----------------------
	if info.DriverInfo.Id != "" {
		bytesd, err := escli.NewESClientCl.SearchByID(common.DataBasePlatform, common.TbDriver, info.DriverInfo.Id)
		if err != nil {
			logger.Error.Printf("获取驱动详情失败:%s", err.Error())
		}
		var driverinfo metasource.DriverInfo
		_ = json.Unmarshal([]byte(bytesd), &driverinfo)
		info.DriverInfo.DriverPath = driverinfo.DriverPath
	}
	// ----------------end 获取驱动表里面的驱动的路径----------------------

	return info, nil
}

func GetLayername(id string) string {
	var name string
	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": id,
					},
				},
			},
		},
	}
	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/danastudio-asset/db_layer/_search")
	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return name
	}
	name = gjson.Get(string(response), "hits.hits.0._source.layername").String()
	return name

}

func ListDatabase(id string) ([]source.EngineInfo, error) {
	var dbinfo []source.EngineInfo
	var array source.EngineInfo
	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": id,
					},
				},
			},
		},
	}
	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/%s/%s/_search", common.DataBaseLab, common.TbProject)
	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return dbinfo, err
	}
	e := gjson.Get(string(response), "hits.hits.0._source.dbinfo").Array()
	//fmt.Println(e)
	for _, v := range e {
		array.EngineId = v.Get("engineid").String()
		array.DbName = v.Get("dbname").String()
		array.Layerid = v.Get("layerid").String()
		array.Layername = GetLayername(array.Layerid)
		engineinfo, _ := GetInstance(array.EngineId)
		array.Dbtype = engineinfo.Databasetype
		array.Onlykey = array.Layerid + array.DbName
		dbinfo = append(dbinfo, array)
	}

	return dbinfo, nil
}

func ListSchema(sou source.TbJson) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	engineinfo, _ := GetInstance(sou.Engineid)
	fmt.Printf("%d", engineinfo.Port)
	//Port := strconv.Itoa(engineinfo.Port)
	engine, err := PgConnect(engineinfo.User, engineinfo.Passwd, sou.Dbname, engineinfo.Ip, engineinfo.Port)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}
	sqltxt := fmt.Sprintf(`select schema_name FROM information_schema.schemata 
						  where schema_name!= 'pg_catalog' AND  schema_name!= 'information_schema' 
						  AND  schema_name!= 'pg_toast' AND  schema_name!= 'pg_toast_temp_1' 
						  AND  schema_name!= 'pg_temp_1' AND  schema_name!= 'gp_toolkit' 
						  AND schema_name!= 'pg_bitmapindex' AND  schema_name!= 'madlib' 
						  AND schema_name!= 'pg_aoseg' GROUP BY schema_name;`)

	rets, err := engine.QueryString(sqltxt)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}
	for i := 0; i < len(rets); i++ {
		data := rets[i]
		schema, _ := data["schema_name"]
		record := gin.H{
			"schema": schema,
			"dbname": sou.Dbname,
		}
		result = append(result, record)
	}
	return result, nil
}

func PgConnect(user string, password string, database string, ip string, port int) (*xorm.Engine, error) {
	info := fmt.Sprintf("user=%s password=%s dbname=%s host=%s port=%d sslmode=disable", user, password, database, ip, port)
	engine, err := xorm.NewEngine("postgres", info)
	if err != nil {
		return nil, err
	}
	if err = engine.Ping(); err != nil {
		return nil, err
	}
	return engine, nil
}

// 480todo 前四条数据可以删了
func ListTbinfo(sou source.TbJson) (source.Tbinfo, error) {
	var tbinfo source.Tbinfo
	//获取引擎信息
	engineinfo, _ := EngineInfo(sou.Engineid)
	dbid := engineinfo.EngineID + sou.Dbname
	engineinfo.InitDB = sou.Dbname
	err := collect.RegisterMG(engineinfo, dbid)
	if err != nil {
		logger.Error.Println("ListTbinfo RegisterMG, err:", err)
		return tbinfo, err
	}

	var tb source.MgTbInfo
	tburi := fmt.Sprintf(`/meta-gateway/table/struct?datasourceId=%s&databaseName=%s&schemaName=%s&tableName=%s`, engineinfo.EngineID+sou.Dbname, sou.Dbname, sou.Schema, sou.Tbname)
	body := "[]"
	tburi = strings.ReplaceAll(tburi, "#", "%23") //url 中 # 号后的字符会被忽略，需要转义
	tbres, err := collect.MGGET(tburi, body)
	if err != nil {
		logger.Error.Println(err, tburi)
		return tbinfo, err
	}
	//fmt.Println("tburi: ", tburi)
	err = json.Unmarshal(tbres, &tb)
	if err != nil {
		logger.Error.Println(err.Error())
		return tbinfo, err
	}

	// fmt.Println("tbres: ", string(tbres))
	// fmt.Println("tb: ", tb)

	if tb.Code != 200 {
		logger.Error.Println(tb.Message)
		return tbinfo, errors.New(tb.Message)
	}

	for _, i := range tb.Data {
		tbinfo.Field = append(tbinfo.Field, i.ColName)
		tbinfo.Fieldtype = append(tbinfo.Fieldtype, i.DataType)
		tbinfo.Fieldcomment = append(tbinfo.Fieldcomment, strings.Replace(i.Comment, "`", "\\`", -1))
	}

	if engineinfo.EngineName == common.DMDB {
		var dmtb source.MGResTB
		dmtburi := fmt.Sprintf(`/meta-gateway/table/metadata?datasourceId=%s&databaseName=%s&schemaName=%s&tableName=%s`, engineinfo.EngineID+sou.Dbname, sou.Dbname, sou.Schema, sou.Tbname)
		dmbody := "[]"
		dmtburi = strings.ReplaceAll(dmtburi, "#", "%23") //url 中 # 号后的字符会被忽略，需要转义
		dmtbres, err := collect.MGGET(dmtburi, dmbody)
		if err != nil {
			logger.Error.Println(err, dmtburi)
			return tbinfo, err
		}

		err = json.Unmarshal(dmtbres, &dmtb)
		if err != nil {
			logger.Error.Println(err.Error())
			return tbinfo, err
		}

		typeLen := make(map[string]int)
		for _, i := range dmtb.TBInfo.Cols {
			typeLen[i.Name] = i.Len
		}

		for i := range tbinfo.Fieldtype {
			//为达梦的类型添加长度
			if typeLen[tbinfo.Field[i]] > 0 && (strings.ToLower(tbinfo.Fieldtype[i]) == "char" || strings.ToLower(tbinfo.Fieldtype[i]) == "binary") {
				tbinfo.Fieldtype[i] = tbinfo.Fieldtype[i] + fmt.Sprintf(`(%d)`, typeLen[tbinfo.Field[i]])
			}
		}
	}

	// switch engineinfo.Databasetype {
	// case common.STORK, common.TERYX, common.GAUSSDB,common.GREENPLUM,common.POSTGRES, common.UXDB, common.DMDB:
	// 	des, err := MgTableDesc(sou.Engineid, sou.Dbname, sou.Schema, sou.Tbname)
	// 	if err != nil {
	// 		logger.Error.Println(err)
	// 		return tbinfo, err
	// 	}
	// 	tbinfo.Field = des.Columnname
	// 	tbinfo.Fieldtype = des.Columntype
	// 	tbinfo.Fieldcomment = des.Columncomment

	// 	for i := 0; i < len(tbinfo.Fieldcomment); i++ {
	// 		tbinfo.Fieldcomment[i] = strings.Replace(tbinfo.Fieldcomment[i], "`", "\\`", -1)
	// 	}
	// case common.Hive:
	// 	sqltxt := fmt.Sprintf(`desc %s."%s"`, sou.Dbname, sou.Tbname)
	// 	re, err := MgExec(sou.Engineid, sqltxt)
	// 	if err != nil {
	// 		logger.Error.Println(err)
	// 		return tbinfo, err
	// 	}
	// 	des := MgHiveDesc(re.Values)
	// 	tbinfo.Field = des.Columnname
	// 	tbinfo.Fieldtype = des.Columntype
	// 	tbinfo.Fieldcomment = des.Columncomment
	// 	for i := 0; i < len(tbinfo.Fieldcomment); i++ {
	// 		tbinfo.Fieldcomment[i] = strings.Replace(tbinfo.Fieldcomment[i], "`", "", -1)
	// 		tbinfo.Fieldcomment[i] = strings.Replace(tbinfo.Fieldcomment[i], "\\", "", -1)
	// 	}
	// }

	uri := fmt.Sprintf("/%s/%s/%s", common.DataBaseLab, common.TbDev, sou.Id)
	res, err := httpclient.Get(uri, "")
	if err != nil {
		logger.Error.Println(err)
		return tbinfo, err
	}

	var result source.Sources
	json.Unmarshal(res, &result)
	// fmt.Println(result)
	tbinfo.Table = result.SaveSource.Table
	tbinfo.FieldContent = result.SaveSource.FieldContent

	tbinfo.Tbtype, _ = GetTableTypeByDBandTB(sou.Dbname, sou.Tbname) // 获取表类型。AI推荐，表输入部分展示。

	// 去除字段注释
	for i := 0; i < len(tbinfo.Fieldcomment); i++ {
		tbinfo.Fieldcomment[i] = dbutil.CommentFormat(tbinfo.Fieldcomment[i])
		tbinfo.Fieldcomment[i] = strings.Replace(tbinfo.Fieldcomment[i], `\"`, `"`, -1)
		//tbinfo.Fieldcomment[i] = strings.Replace(tbinfo.Fieldcomment[i], "`", "\\`", -1)
	}

	tbinfo.OdsType = engineinfo.EngineName
	return tbinfo, nil
}

func Preview(sou source.TbJson) ([]map[string]interface{}, error) {
	var result []map[string]interface{}

	//Port := strconv.Itoa(engineinfo.Port)

	var sqltxt1 string
	var sqltxt2 string
	var sqltxt3 string
	var sqltxt string

	// 选用引擎类型
	engineinfo, _ := GetInstance(sou.Engineid)

	var sort string
	switch engineinfo.Databasetype {
	case common.Stork, common.Teryx, common.GaussDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.GaussDBA, common.TDSQL:
		//增量字段
		if sou.TimeField.Field != "" && sou.TimeField.Field != "无" {
			//string类型
			if strings.Contains(sou.TimeField.Fieldtype, "varchar") || strings.Contains(sou.TimeField.Fieldtype, "char") || strings.Contains(sou.TimeField.Fieldtype, "clob") || strings.Contains(sou.TimeField.Fieldtype, "text") {

				sqltxt1 = fmt.Sprintf(` where "%s" < cast((CURRENT_TIMESTAMP) as text)`, sou.TimeField.Field)

			} else {
				sqltxt1 = fmt.Sprintf(` where cast ("%s" as timestamp) < (CURRENT_TIMESTAMP)`, sou.TimeField.Field)

			}
		}

		//排序字段
		if sou.SortField.Field != "" {
			if sou.SortField.Sorttype == 2 {
				sort = "desc"
			}
			sqltxt2 = fmt.Sprintf(` order by "%s" %s`, sou.SortField.Field, sort)
		}

		//where过滤条件
		if sou.Sqltxt != "" && sou.TimeField.Field != "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(` and %s`, sou.Sqltxt)
		} else if sou.Sqltxt != "" && sou.TimeField.Field == "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(` where %s`, sou.Sqltxt)
		}

		// 重复数据
		var mainkey string
		var mdf string //合成MD5字符串
		// md5(COALESCE ( cast(name as string) , '' ))
		if len(sou.Mainkeyfield) == 1 {
			mdf = fmt.Sprintf(` md5(COALESCE ( cast("%s" as text)  , cast('' as text) ) ) as dwd_zjid `, sou.Mainkeyfield[0])
		} else if len(sou.Mainkeyfield) > 1 {
			//md5( concat (COALESCE ( cast(name as string) , '' ),COALESCE ( cast(id as string), '' )))
			for i := 0; i < len(sou.Mainkeyfield)-1; i++ {
				mainkey = mainkey + "coalesce( cast ( " + `"` + sou.Mainkeyfield[i] + `"` + " as text) ,cast('' as text) ), "
			}
			mainkey = mainkey + "coalesce(  cast(" + `"` + sou.Mainkeyfield[len(sou.Mainkeyfield)-1] + `"` + " as text) ,cast('' as text) ) "
			mdf = `md5(concat(` + mainkey + `)) as dwd_zjid `
		}

		if len(sou.Mainkeyfield) != 0 {
			//这边进行去重处理
			//sqltxt = "select  *," + mdf + " from " + sou.Schema + "." + "`" + sou.Tbname + "`" + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			sqltxt = fmt.Sprintf(`select  *,%s from "%s"."%s" %s %s %s limit 100`, mdf, sou.Schema, sou.Tbname, sqltxt1, sqltxt3, sqltxt2)
			tbinfo, _ := ListTbinfo(sou)
			//1.如果重复的都放入问题库里面（把不存在重复的数据都拿出来展示）

			// #选择的主键字段
			// #原生表里面有哪些字段
			var mkfields string
			var fields string
			// #直接操作表的那一层
			var normal string

			mkfields = Marryfields(sou.Mainkeyfield)
			fields = Marryfields(tbinfo.Field)
			if sou.Duproles.Problem {
				normal = fmt.Sprintf(` ( SELECT * FROM ( SELECT *, LEAD ( a.ROWROW, 1 ) OVER 
					( PARTITION BY %s order by 1=1) AS NEXT FROM ( SELECT *, ROW_NUMBER () OVER 
					( PARTITION BY %s order by 1=1) ROWROW FROM "%s"."%s" ) a ) b WHERE CAST ( NEXT as text ) 
					IS NULL and b.ROWROW =1 ) cnmdf`, mkfields, mkfields, sou.Schema, sou.Tbname)
				fmt.Println("条件一: ", normal)
				sqltxt = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			}
			//2.重复数据只取一条数据
			if sou.Duproles.Dataclean {
				normal = fmt.Sprintf(`(select distinct * from "%s"."%s") cnmdf`, sou.Schema, sou.Tbname)
				sqltxt = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			}
			//3.根据指定条件去重
			if len(sou.Duproles.Extraclean) != 0 {
				withstr, selectstr := TeryxPreviewduplicate(sou, mdf, sqltxt1, sqltxt3, sqltxt2)
				sqltxt = withstr + selectstr
				// sqltxt = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			}

		} else {
			//sqltxt = "select *  from " + sou.Schema + "." + sou.Tbname + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			sqltxt = fmt.Sprintf(`select  * from "%s"."%s" %s %s %s limit 100`, sou.Schema, sou.Tbname, sqltxt1, sqltxt3, sqltxt2)

		}

		//sqltxt = "select * from " + sou.Schema + "." + "\"" + sou.Tbname + "\"" + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
		rows, err := MgExec(sou.Engineid, sqltxt, sou.Dbname)
		if err != nil {
			return result, err
		}

		result = rows.Values

	case common.Hive, common.Inceptor:
		if sou.TimeField.Field != "" && sou.TimeField.Field != "无" {
			//string类型
			if strings.Contains(sou.TimeField.Fieldtype, "varchar") || strings.Contains(sou.TimeField.Fieldtype, "char") || strings.Contains(sou.TimeField.Fieldtype, "varbinary") || strings.Contains(sou.TimeField.Fieldtype, "json") || strings.Contains(sou.TimeField.Fieldtype, "string") {

				sqltxt1 = fmt.Sprintf(` where %s < cast((CURRENT_TIMESTAMP) as string)`, sou.TimeField.Field)

			} else {
				sqltxt1 = fmt.Sprintf(` where cast (%s as timestamp) < (CURRENT_TIMESTAMP)`, sou.TimeField.Field)

			}
		}

		if sou.SortField.Field != "" {
			if sou.SortField.Sorttype == 2 {
				sort = "desc"
			}
			sqltxt2 = fmt.Sprintf(" order by `%s` %s", sou.SortField.Field, sort)
		}
		if sou.Sqltxt != "" && sou.TimeField.Field != "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(` and %s`, sou.Sqltxt)
		} else if sou.Sqltxt != "" && sou.TimeField.Field == "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(` where %s`, sou.Sqltxt)
		}
		//---------------------------------
		var mainkey string
		var mdf string //合成MD5字符串
		// md5(COALESCE ( cast(name as string) , '' ))
		if len(sou.Mainkeyfield) == 1 {
			mdf = fmt.Sprintf(" md5(COALESCE ( cast(`%s` as string)  , cast('' as string) ) ) as dwd_zjid ", sou.Mainkeyfield[0])
		} else if len(sou.Mainkeyfield) > 1 {
			//md5( concat (COALESCE ( cast(name as string) , '' ),COALESCE ( cast(id as string), '' )))
			for i := 0; i < len(sou.Mainkeyfield)-1; i++ {
				mainkey = mainkey + "coalesce( cast ( `" + sou.Mainkeyfield[i] + "` as string) ,cast('' as string) ), "
			}
			mainkey = mainkey + "coalesce(  cast(`" + sou.Mainkeyfield[len(sou.Mainkeyfield)-1] + "` as string) ,cast('' as string) ) "
			mdf = `md5(concat(` + mainkey + `)) as dwd_zjid `
		}
		//-----------------------------------
		//判断是否有随机主键字段
		if len(sou.Mainkeyfield) != 0 {
			//这边进行去重处理
			sqltxt = "select  *," + mdf + " from " + sou.Dbname + "." + "`" + sou.Tbname + "`" + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"

			tbinfo, _ := ListTbinfo(sou)
			//1.如果重复的都放入问题库里面（把不存在重复的数据都拿出来展示）

			// #选择的主键字段
			// #原生表里面有哪些字段
			var mkfields string
			var fields string
			// #直接操作表的那一层
			var normal string

			mkfields = Marryfields(sou.Mainkeyfield)
			fields = Marryfields(tbinfo.Field)
			if sou.Duproles.Problem {
				normal = fmt.Sprintf(` ( SELECT * FROM ( SELECT *, LEAD ( a.ROWROW, 1 ) OVER 
					( PARTITION BY %s order by 1=1) AS NEXT FROM ( SELECT *, ROW_NUMBER () OVER 
					( PARTITION BY %s order by 1=1) ROWROW FROM %s."%s" ) a ) b WHERE CAST ( NEXT as string ) 
					IS NULL and b.ROWROW =1 ) cnmdf`, mkfields, mkfields, sou.Dbname, sou.Tbname)
				fmt.Println("条件一: ", normal)
				sqltxt = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			}
			//2.重复数据只取一条数据
			if sou.Duproles.Dataclean {
				normal = fmt.Sprintf("(select distinct * from %s.\"%s\") cnmdf", sou.Dbname, sou.Tbname)
				sqltxt = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			}
			//3.根据指定条件去重
			if len(sou.Duproles.Extraclean) != 0 {
				withstr, selectstr := SparkPreviewduplicate(sou, mdf, sqltxt1, sqltxt3, sqltxt2)
				sqltxt = withstr + selectstr
				// sqltxt = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"
			}

		} else {
			sqltxt = "select *  from " + sou.Dbname + "." + "`" + sou.Tbname + "`" + sqltxt1 + sqltxt3 + sqltxt2 + " limit 100"

		}

		fmt.Printf("\n查询最终的sql脚本为 :  %s\n", sqltxt)
		// rows, err := SparkExec(sqltxt)
		// if err != nil {
		// 	return result, err
		// }
		// sqltxt = strings.Replace(sqltxt, `varchar`, `string`, -1)
		rows, err := MgExec(sou.Engineid, sqltxt)
		if err != nil {
			return result, err
		}

		result = rows.Values

	}

	return result, nil
}

// -------------------------------------------------------------------------------
func KeyDuplicate(sou source.TbJson) bool {

	engineinfo, _ := GetInstance(sou.Engineid)

	if engineinfo.Databasetype == "hive" {
		//-----------------------------------
		//得到随机主键字段
		var mainkey string

		if len(sou.Mainkeyfield) != 0 {
			for _, v := range sou.Mainkeyfield {
				mainkey = mainkey + v + ","
			}
		} else {
			return false
		}
		mainkey = strings.TrimSuffix(mainkey, ",")
		//-------------------------------------
		conn, err := PrestoConnect(engineinfo, sou.Dbname)
		if err != nil {
			fmt.Println(err)
			logger.Error.Println(err)
			return false
		}

		sql := fmt.Sprintf("SELECT count(%s) a  FROM %s GROUP BY %s HAVING COUNT(*) > 1", sou.Mainkeyfield[0], sou.Tbname, mainkey)
		rows, err := conn.Query(sql)
		if err != nil {
			fmt.Println(err)
			logger.Error.Println(err)
			return false
		}

		result, err := sqlrows2Maps(rows)
		if err != nil {
			fmt.Println(err)
			logger.Error.Println(err)
			return false
		}
		for _, v := range result {
			if value, ok := v["a"]; ok {
				if value.(string) != "0" {
					return true
				}
			}
		}

		fmt.Println(result)
	}

	return false
}

// --------------------------------------------------------------------------------
func SaveTable(sou source.TbJson, Authorization string) (interface{}, error) {

	var sqltxt1 string    //增量
	var sqltxt2 string    //排序
	var sqltxt3 string    //where
	var tempsqltxt string //临时表专用
	var content string
	var execstr string //脚本执行的sql
	var tempstr string //临时表执行的sql
	var sort string
	var orgin = fmt.Sprintf(``)      //原表的字段名+字段类型
	var tmpcomment = fmt.Sprintf(``) //临时表的字段备注
	var comment = fmt.Sprintf(``)    //字段备注
	var fieldinfo = fmt.Sprintf(``)  //hive 字段+字段类型+字段备注
	var withstr, selectstr string    //用于数据指定条件去重拼接使用

	var newtbname string //临时表
	var savename string  //脚本生成表

	var schema string //临时表的schema

	//时间表（增量用）
	timetable := "increment_" + sou.Id

	lab, err := ProDevInfo(sou.Id)
	if err != nil {
		return "", err
	}
	lab.Initdb = sou.ProjectID + "_" + "basedb"

	engineinfo, _ := GetInstance(sou.Engineid)

	//-----------------------------------------------------
	//再查一遍表获取元数据过于耗费时间，改为从元数据获取

	// tbinfo, _ := ListTbinfo(sou)
	tbinfo, err := GetMetaTbinfo(sou)
	if err != nil {
		logger.Error.Println(err)
		fmt.Println(err)
		return "", err
	}
	//--------------------------------------------------------

	switch engineinfo.Databasetype {
	case common.Stork, common.Teryx, common.GaussDB, common.GREENPLUM, common.POSTGRES, common.UXDB, common.GaussDBA, common.TDSQL:
		timetable = sou.ProjectID + "_basedb." + `"` + timetable + `"`
		engineinfo.Database = sou.ProjectID + "_" + "basedb"
		//临时表的名称
		newtbname = "xxn_" + sou.Id
		//用于脚本中保存的名字
		savename = sou.Id

		//临时表的schema
		schema = sou.ProjectID + "_basedb"

		//拼接字段名+字段类型，字段描述
		for i := 0; i < len(tbinfo.Field); i++ {
			//拼接字段名、类型、描述
			orgin = fmt.Sprintf(`%s "%s" %s,`, orgin, tbinfo.Field[i], tbinfo.Fieldtype[i])

			if tbinfo.Fieldcomment[i] != "" {
				tmpcomment = fmt.Sprintf(`%s comment on column "%s"."%s"."%s" is '%s';`, tmpcomment, schema, newtbname, tbinfo.Field[i], tbinfo.Fieldcomment[i])
				comment = fmt.Sprintf(`%s comment on column "%s"."%s"."%s" is '%s';`, comment, schema, savename, tbinfo.Field[i], tbinfo.Fieldcomment[i])
			}
		}

		//如果有随机主键传入
		if len(sou.Mainkeyfield) != 0 {
			tmpZJComment := fmt.Sprintf(`comment on column "%s"."%s".dwd_zjid is  '随机主键';`, schema, newtbname)
			zjComment := fmt.Sprintf(`comment on column "%s"."%s".dwd_zjid is  '随机主键';`, schema, savename)
			orgin = orgin + " dwd_zjid  text "
			tmpcomment = tmpcomment + tmpZJComment
			comment = comment + zjComment
		} else {
			orgin = strings.TrimRight(orgin, ",")
		}

		var dirtydata string
		var dirtydatadi string
		switch sou.TimeField.Format {
		case "yyyy-MM-dd HH:mm:ss":
			sou.TimeField.Format = "yyyy-MM-dd HH24:MI:ss"
			dirtydatadi = fmt.Sprintf(` and ("%s" ~* '^[0-9]{4}-[0-9]{2}-[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$')`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(` and case when cast(t12qwertime.incrementtime as text)='0' then 1=1 else ("%s" ~* '^[0-9]{4}-[0-9]{2}-[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$') end `, sou.TimeField.Field)
		case "yyyy/MM/dd HH:mm:ss":
			sou.TimeField.Format = "yyyy/MM/dd HH24:MI:ss"
			dirtydatadi = fmt.Sprintf(` and ("%s" ~* '^[0-9]{4}/[0-9]{2}/[0-9]{2} +[0-6]{2}:[0-9]{2}:[0-9]{2}$')`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and case when cast(t12qwertime.incrementtime as text)='0' then 1=1 else ("%s" ~* '^[0-9]{4}/[0-9]{2}/[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$') end `, sou.TimeField.Field)
		case "yyyyMMddHHmmss":
			sou.TimeField.Format = "yyyyMMddHH24MIss"
			dirtydatadi = fmt.Sprintf(` and ("%s" ~* '^[0-9]{14}$')`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and case when cast(t12qwertime.incrementtime as text)='0' then 1=1 else ("%s" ~* '^[0-9]{14}$') end `, sou.TimeField.Field)
		case "yyyy-MM-dd":
			dirtydatadi = fmt.Sprintf(` and ("%s" ~* '^[0-9]{4}-[0-9]{2}-[0-9]{2}$')`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and case when cast(t12qwertime.incrementtime as text)='0' then 1=1 else  ("%s" ~* '^[0-9]{4}-[0-9]{2}-[0-9]{2}$') end `, sou.TimeField.Field)
		case "yyyy/MM/dd":
			dirtydatadi = fmt.Sprintf(` and ("%s" ~* '^[0-9]{4}/[0-9]{2}/[0-9]{2}$')`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and case when cast(t12qwertime.incrementtime as text)='0' then 1=1 else  ("%s" ~* '^[0-9]{4}/[0-9]{2}/[0-9]{2}$') end `, sou.TimeField.Field)
		case "yyyyMMdd":
			dirtydatadi = fmt.Sprintf(` and ("%s" ~* '^[0-9]{8}$')`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and case when cast(t12qwertime.incrementtime as text)='0' then 1=1 else ("%s" ~* '^[0-9]{8}$') end `, sou.TimeField.Field)
		default:
			dirtydata = " and 1=1 "
		}

		if sou.TimeField.Field != "" {
			if strings.Contains(sou.TimeField.Fieldtype, "varchar") || strings.Contains(sou.TimeField.Fieldtype, "char") || strings.Contains(sou.TimeField.Fieldtype, "clob") || strings.Contains(sou.TimeField.Fieldtype, "text") {

				//sqltxt1 = fmt.Sprintf(`WHERE "%s" > cast((SELECT MAX("incrementtime") FROM "%s" limit 1 ) as string)
				sqltxt1 = fmt.Sprintf(` ,(SELECT MAX(incrementtime) as incrementtime  FROM %s limit 1 ) t12qwertime WHERE "%s" > cast(t12qwertime.incrementtime as text) 
					and "%s" < to_char(current_timestamp, '%s') %s `,
					timetable, sou.TimeField.Field, sou.TimeField.Field, sou.TimeField.Format, dirtydata)

				tempsqltxt = fmt.Sprintf(`where "%s" < to_char(current_timestamp, '%s')  `,
					sou.TimeField.Field, sou.TimeField.Format)

			} else {
				sqltxt1 = fmt.Sprintf(` ,(SELECT MAX(incrementtime) as incrementtime FROM %s limit 1) t12qwertime  WHERE cast ("%s" as timestamp) > cast(t12qwertime.incrementtime  as timestamp) and cast ("%s" as timestamp) < cast (CURRENT_TIMESTAMP as timestamp)`,
					timetable, sou.TimeField.Field, sou.TimeField.Field)
				tempsqltxt = fmt.Sprintf(`where cast ( "%s" as timestamp) < (CURRENT_TIMESTAMP)`, sou.TimeField.Field)
			}
		}

		//排序语句生成
		if sou.SortField.Field != "" {
			if sou.SortField.Sorttype == 2 {
				sort = "desc"
			}
			sqltxt2 = fmt.Sprintf(` order by "%s" %s`, sou.SortField.Field, sort)
		}

		//where语句生成
		if sou.Sqltxt != "" && sou.TimeField.Field != "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(`and %s`, sou.Sqltxt)
		} else if sou.Sqltxt != "" && sou.TimeField.Field == "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(`where %s`, sou.Sqltxt)
		}

		//主键--业务主键
		var mainkey string
		var mdf string //合成MD5字符串
		// md5(COALESCE ( cast(name as string) , '' ))
		if len(sou.Mainkeyfield) == 1 {
			mdf = fmt.Sprintf(` md5(COALESCE ( cast("%s" as text)  , cast('' as text) ) ) as dwd_zjid `, sou.Mainkeyfield[0])
		} else if len(sou.Mainkeyfield) > 1 {
			//md5( concat (COALESCE ( cast(name as string) , '' ),COALESCE ( cast(id as string), '' )))
			for i := 0; i < len(sou.Mainkeyfield)-1; i++ {
				mainkey = mainkey + `coalesce( cast ( ` + `"` + sou.Mainkeyfield[i] + `"` + ` as text) ,cast('' as text) ), `
			}
			mainkey = mainkey + `coalesce(  cast( ` + `"` + sou.Mainkeyfield[len(sou.Mainkeyfield)-1] + `"` + ` as text) ,cast('' as text) ) `
			mdf = `md5(concat(` + mainkey + `)) as dwd_zjid `
		}

		//问题数据--重复数据
		var problemstr string
		var problemtablename string
		//判断是否已经有了随机主键
		if len(sou.Mainkeyfield) != 0 {
			//==============================================================================
			//1.如果重复的都放入问题库里面（
			// #选择的主键字段
			// var mkfields string
			// #原生表里面有哪些字段
			var fields string
			// #直接操作表的那一层
			var normal string

			fields = Marryfields(tbinfo.Field)

			content = fmt.Sprintf(` select %s , %s from "%s"."%s" %s %s %s`, fields, mdf, sou.Schema, sou.Tbname, sqltxt1, sqltxt3, sqltxt2)

			// 全部--放入问题库
			if sou.Duproles.Problem {
				//I.把不存在重复的数据都拿出来使用
				normal = TeryxSaveduplicate(sou, 1)
				fmt.Println("条件一: ", normal)
				content = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2
				//II.问题数据
				problemtablename = fmt.Sprintf("xxn_cf_%s_%s", sou.Schema, sou.Tbname)
				problemstr = TeryxSaveduplicate(sou, 11)
				problemstr = fmt.Sprintf(`drop table if exists %s_wt."%s"; create table  %s_wt."%s" as  %s ;`, sou.ProjectID, problemtablename, sou.ProjectID, problemtablename, problemstr)
				//problemstr = fmt.Sprintf("drop table if exists "+sou.ProjectID+`_`+"wt.%s; create table  "+sou.ProjectID+`_`+"wt.`%s` as  %s ;", problemtablename, problemtablename, problemstr)
			}
			//------------------------------------------------------------------------------
			//2.重复数据只取一条数据，不放入问题库
			// 去除完全重复数据。
			if sou.Duproles.Dataclean {
				normal = fmt.Sprintf(`(select distinct * from "%s"."%s") cnmdf`, sou.Schema, sou.Tbname)
				content = " select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2
			}
			//------------------------------------------------------------------------------
			//3.重复数据按照指定规则再进行比对,并放入问题库
			if len(sou.Duproles.Extraclean) != 0 {
				withstr, selectstr = TeryxPreviewduplicate(sou, mdf, sqltxt1, sqltxt3, sqltxt2)
				content = withstr + selectstr
				//下面是原来的语句
				// normal = SparkPreviewduplicate(sou)
				// content = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2
				// //II.问题数据
				problemtablename = fmt.Sprintf("cf_%s_%s", sou.Schema, sou.Tbname)
				problemstr = TeryxSaveduplicate(sou, 3)
				//
				//	problemstr = fmt.Sprintf(`drop table if exists basedb.%s ;
				//create table basedb.%s as select * ,rand(100000000) as tempid from %s.%s limit 1000 ;
				//drop table if exists wt.%s; create table  wt.%s as  %s ;
				//drop table if exists `+sou.ProjectID+`_basedb.%s ;`, sou.Id+"_preview", sou.Id+"_preview", sou.Dbname, sou.Tbname, problemtablename, problemtablename, problemstr, sou.Id+"_preview")
				//
				problemstr = fmt.Sprintf(`drop table if exists `+sou.ProjectID+`_basedb."%s" ;
				create table `+sou.ProjectID+`_basedb."%s" as select * ,rand(100000000) as tempid from %s."%s" limit 1000 ;
				drop table if exists `+sou.ProjectID+`_`+`wt."%s";   ; create table  `+sou.ProjectID+`_`+`wt."%s" as  %s ;
				drop table if exists `+sou.ProjectID+`_basedb."%s" ;`,
					sou.Id+"_preview",
					sou.Id+"_preview", sou.Schema, sou.Tbname,
					problemtablename, problemtablename, problemstr,
					sou.Id+"_preview")
			}
			//==============================================================================

		} else {
			content = fmt.Sprintf(` select *  from "%s"."%s"  %s %s %s `, sou.Schema, sou.Tbname, sqltxt1, sqltxt3, sqltxt2)
		}

		tempstr := " DROP TABLE IF EXISTS " + sou.ProjectID + "_basedb." + `"` + newtbname + `"`
		_, err = MgExec(sou.Engineid, tempstr, sou.Dbname)
		if err != nil {
			logger.Error.Println(err)
			fmt.Println(err)
			return "", err
		}

		tempstr = "CREATE TABLE " + sou.ProjectID + "_basedb." + `"` + newtbname + `"` + " (" + orgin + ");" + tmpcomment
		//tempstr = strings.Replace(tempstr, `varchar`, `string`, -1)
		_, err = MgExec(sou.Engineid, tempstr, sou.Dbname)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		// fprint(problemstr, "问题库处理合集")
		problemstr = strings.Replace(problemstr, "xxn_", "", -1)
		//problemstr = strings.Replace(problemstr, "limit 1000", "", -1)

		//------------------------------------------------------

		sou.TimeField.Flag = true
		switch sou.TimeField.Flag && sou.TimeField.Field != "" {
		case true:
			tempname := sou.Tbname
			tempsql := fmt.Sprintf(`  select *,%s from "%s"."%s" %s %s %s`, mdf, sou.Schema, tempname, tempsqltxt, sqltxt3, sqltxt2)

			tempstr = "insert into " + sou.ProjectID + "_basedb." + `"` + newtbname + `"` + tempsql + " limit 1000"

			//content = strings.Replace(content, "limit 1000", "", -1)

			exectable := savename

			//=============4.3.8start============
			var timetablecreate string
			var insertdate string
			if strings.Contains(sou.TimeField.Fieldtype, "varchar") || strings.Contains(sou.TimeField.Fieldtype, "char") || strings.Contains(sou.TimeField.Fieldtype, "clob") || strings.Contains(sou.TimeField.Fieldtype, "text") {
				timetablecreate = ` create table if not exists ` + timetable + `("incrementtime" text ) ;` + `  insert into ` + timetable + ` values ('0') ;`

				//` as select from_unixtime(unix_timestamp('1970-01-01 00:00:00'),'%s') ;`, sou.TimeField.Format)

				insertdate = fmt.Sprintf(` insert into %s  select max("%s") from "%s"."%s" where %s<  to_char(current_timestamp, '%s')  %s limit 1;`,
					timetable, sou.TimeField.Field, sou.Schema, sou.Tbname, sou.TimeField.Field, sou.TimeField.Format, dirtydatadi)

			} else {
				timetablecreate = ` create table if not exists ` + timetable + `("incrementtime" timestamp ) ;` + `  insert into ` + timetable + ` values ('1970-01-01 00:00:00') ;`

				insertdate = fmt.Sprintf(` insert into %s  select max("%s") from "%s"."%s"  where %s< cast((current_timestamp) as timestamp) ;`,
					timetable, sou.TimeField.Field, sou.Schema, sou.Tbname, sou.TimeField.Field)
			}
			//=============4.3.8 end ============

			execstr = timetablecreate +
				` DROP TABLE IF EXISTS ` + sou.ProjectID + "_basedb." + `"` + exectable + `";` +
				` CREATE TABLE ` + sou.ProjectID + "_basedb." + `"` + exectable + `" ( ` + orgin + `);` + comment +
				` insert into ` + sou.ProjectID + "_basedb." + `"` + exectable + `"` + content + `;` + insertdate

			execstr = execstr + problemstr
		case false:
			// content = strings.Replace(content, "\"", "", -1)
			//--------------------------------------------------
			//只有当指定条件生效
			if withstr != "" && selectstr != "" {
				tempstr = withstr + " insert into " + sou.ProjectID + "_basedb." + `"` + newtbname + `"` + " " + selectstr
			} else {
				tempstr = " insert into " + sou.ProjectID + "_basedb." + `"` + newtbname + `"` + " " + content + " limit 1000"
			}
			//---------------------------------------------------

			execstr = ` DROP TABLE IF EXISTS ` + sou.ProjectID + "_basedb." + `"` + savename + `";` +
				` CREATE TABLE ` + sou.ProjectID + "_basedb." + `"` + savename + `" ( ` + orgin + `);` + comment +
				` insert into ` + sou.ProjectID + "_basedb." + `"` + savename + `"` + content + `;`
			execstr = execstr + problemstr
		}

		_, err = MgExec(sou.Engineid, tempstr, sou.Dbname)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}

		//teryx 原表和中间表同库
		engineinfo.Database = sou.Dbname

	case common.Hive, common.Inceptor:

		//如果用的引擎是spark
		//if engine == "spark" {
		//-------------------------
		engineinfo.Database = sou.ProjectID + "_" + "basedb"
		//临时表的名称
		newtbname = "xxn_" + sou.Id
		//用于脚本中保存的名字
		savename = sou.Id

		for i := 0; i < len(tbinfo.Field); i++ {
			fieldinfo = fmt.Sprintf(`%s "%s" %s comment'%s',`, fieldinfo, tbinfo.Field[i], tbinfo.Fieldtype[i], dbutil.StrToUnicode(tbinfo.Fieldcomment[i]))
		}
		fieldinfo = strings.TrimRight(fieldinfo, ",")

		//如果有随机主键传入
		if len(sou.Mainkeyfield) != 0 {
			fieldinfo = fieldinfo + " ,dwd_zjid  string comment '随机主键' "
		} else {
			//不用做处理
		}

		var dirtydata string
		var dirtydatadi string
		switch sou.TimeField.Format {
		case "yyyy-MM-dd HH:mm:ss":
			dirtydatadi = fmt.Sprintf(` and "%s" rlike '^[0-9]{4}-[0-9]{2}-[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$' `, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(` and if(cast(t12qwertime.incrementtime as string)='0' ,1=1, "%s" rlike '^[0-9]{4}-[0-9]{2}-[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$' )`, sou.TimeField.Field)
		case "yyyy/MM/dd HH:mm:ss":
			dirtydatadi = fmt.Sprintf(` and "%s" rlike '^[0-9]{4}/[0-9]{2}/[0-9]{2} +[0-6]{2}:[0-9]{2}:[0-9]{2}$' `, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and if(cast(t12qwertime.incrementtime as string)='0' ,1=1,  "%s" rlike '^[0-9]{4}/[0-9]{2}/[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$')  `, sou.TimeField.Field)
		case "yyyyMMddHHmmss":
			dirtydatadi = fmt.Sprintf(` and "%s" rlike '^[0-9]{14}$'`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and if(cast(t12qwertime.incrementtime as string)='0' ,1=1,  "%s" rlike '^[0-9]{14}$') `, sou.TimeField.Field)
		case "yyyy-MM-dd":
			dirtydatadi = fmt.Sprintf(` and "%s" rlike '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' `, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and if(cast(t12qwertime.incrementtime as string)='0' ,1=1,  "%s" rlike '^[0-9]{4}-[0-9]{2}-[0-9]{2}$') `, sou.TimeField.Field)
		case "yyyy/MM/dd":
			dirtydatadi = fmt.Sprintf(` and "%s" rlike '^[0-9]{4}/[0-9]{2}/[0-9]{2}$'`, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and if(cast(t12qwertime.incrementtime as string)='0' ,1=1,  "%s" rlike '^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  `, sou.TimeField.Field)
		case "yyyyMMdd":
			dirtydatadi = fmt.Sprintf(` and "%s" rlike '^[0-9]{8}$' `, sou.TimeField.Field)
			dirtydata = fmt.Sprintf(`  and if(cast(t12qwertime.incrementtime as string)='0' ,1=1,  "%s" rlike '^[0-9]{8}$'  ) `, sou.TimeField.Field)
		default:
			dirtydata = " and 1=1 "
		}

		if sou.TimeField.Field != "" {
			if strings.Contains(sou.TimeField.Fieldtype, "string") || strings.Contains(sou.TimeField.Fieldtype, "char") || strings.Contains(sou.TimeField.Fieldtype, "varbinary") || strings.Contains(sou.TimeField.Fieldtype, "json") {

				//sqltxt1 = fmt.Sprintf(`WHERE "%s" > cast((SELECT MAX("incrementtime") FROM "%s" limit 1 ) as string)
				sqltxt1 = fmt.Sprintf(` ,(SELECT MAX(incrementtime) as incrementtime  FROM %s limit 1 ) t12qwertime WHERE "%s" > cast(t12qwertime.incrementtime as string) 
					and "%s" < cast( from_unixtime(unix_timestamp(current_timestamp),'%s') as string)  %s `,
					sou.ProjectID+"_basedb."+timetable, sou.TimeField.Field, sou.TimeField.Field, sou.TimeField.Format, dirtydata)

				tempsqltxt = fmt.Sprintf(`where "%s" < cast( from_unixtime(unix_timestamp(current_timestamp),'%s') as string)  `,
					sou.TimeField.Field, sou.TimeField.Format)

			} else {
				sqltxt1 = fmt.Sprintf(` ,(SELECT MAX(incrementtime) as incrementtime FROM %s limit 1) t12qwertime  WHERE cast ("%s" as timestamp) > cast(t12qwertime.incrementtime  as timestamp) and cast ("%s" as timestamp) < cast (CURRENT_TIMESTAMP as timestamp)`,
					sou.ProjectID+"_basedb."+timetable, sou.TimeField.Field, sou.TimeField.Field)
				tempsqltxt = fmt.Sprintf(`where cast ( "%s" as timestamp) < (CURRENT_TIMESTAMP)`, sou.TimeField.Field)
			}
		}

		//排序语句生成
		if sou.SortField.Field != "" {
			if sou.SortField.Sorttype == 2 {
				sort = "desc"
			}
			sqltxt2 = fmt.Sprintf(` order by "%s" %s`, sou.SortField.Field, sort)
		}

		//where语句生成
		if sou.Sqltxt != "" && sou.TimeField.Field != "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(` and %s`, sou.Sqltxt)
		} else if sou.Sqltxt != "" && sou.TimeField.Field == "" {
			sou.Sqltxt = strings.TrimRight(sou.Sqltxt, ";")
			sqltxt3 = fmt.Sprintf(` where %s`, sou.Sqltxt)
		}

		//------------------------------------------------
		var mainkey string
		var mdf string //合成MD5字符串
		// md5(COALESCE ( cast(name as string) , '' ))
		if len(sou.Mainkeyfield) == 1 {
			mdf = fmt.Sprintf(` md5(COALESCE ( cast("%s" as string)  , cast('' as string) ) ) as dwd_zjid `, sou.Mainkeyfield[0])
		} else if len(sou.Mainkeyfield) > 1 {
			//md5( concat (COALESCE ( cast(name as string) , '' ),COALESCE ( cast(id as string), '' )))
			for i := 0; i < len(sou.Mainkeyfield)-1; i++ {
				mainkey = mainkey + `coalesce( cast ( ` + `"` + sou.Mainkeyfield[i] + `"` + ` as string) ,cast('' as string) ), `
			}
			mainkey = mainkey + `coalesce(  cast( "` + sou.Mainkeyfield[len(sou.Mainkeyfield)-1] + `" as string) ,cast('' as string) ) `
			mdf = `md5(concat(` + mainkey + `)) as dwd_zjid `
		}

		//----------------------------------------------
		var problemstr string
		var problemtablename string
		//判断是否已经有了随机主键
		if len(sou.Mainkeyfield) != 0 {
			//==============================================================================
			//1.如果重复的都放入问题库里面（
			// #选择的主键字段
			// var mkfields string
			// #原生表里面有哪些字段
			var fields string
			// #直接操作表的那一层
			var normal string
			// mkfields = Marryfields(sou.Mainkeyfield)
			fields = Marryfields(tbinfo.Field)

			content = fmt.Sprintf(` select %s , %s from %s."%s" %s %s %s `, fields, mdf, sou.Dbname, sou.Tbname, sqltxt1, sqltxt3, sqltxt2)

			// 全部--放入问题库
			if sou.Duproles.Problem {
				//I.把不存在重复的数据都拿出来使用
				normal = SparkSaveduplicate(sou, 1)
				fmt.Println("条件一: ", normal)
				content = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2
				//II.问题数据
				problemtablename = fmt.Sprintf("xxn_cf_%s_%s", sou.Dbname, sou.Tbname)
				problemstr = SparkSaveduplicate(sou, 11)
				problemstr = fmt.Sprintf("drop table if exists "+sou.ProjectID+`_`+"wt.%s; create table  "+sou.ProjectID+`_`+"wt.`%s` as select * from  %s ;", problemtablename, problemtablename, problemstr)
			}
			//------------------------------------------------------------------------------
			//2.重复数据只取一条数据，不放入问题库
			// 去除完全重复数据。
			if sou.Duproles.Dataclean {
				normal = fmt.Sprintf(`(select distinct * from %s."%s") cnmdf`, sou.Dbname, sou.Tbname)
				content = " select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2
			}
			//------------------------------------------------------------------------------
			//3.重复数据按照指定规则再进行比对,并放入问题库
			if len(sou.Duproles.Extraclean) != 0 {
				withstr, selectstr = SparkPreviewduplicate(sou, mdf, sqltxt1, sqltxt3, sqltxt2)
				content = withstr + selectstr
				//下面是原来的语句
				// normal = SparkPreviewduplicate(sou)
				// content = "select " + fields + " ," + mdf + " from " + normal + sqltxt1 + sqltxt3 + sqltxt2
				// //II.问题数据
				problemtablename = fmt.Sprintf("cf_%s_%s", sou.Dbname, sou.Tbname)
				problemstr = SparkSaveduplicate(sou, 3)
				//
				//	problemstr = fmt.Sprintf(`drop table if exists basedb.%s ;
				//create table basedb.%s as select * ,rand(100000000) as tempid from %s.%s limit 1000 ;
				//drop table if exists wt.%s; create table  wt.%s as  %s ;
				//drop table if exists `+sou.ProjectID+`_basedb.%s ;`, sou.Id+"_preview", sou.Id+"_preview", sou.Dbname, sou.Tbname, problemtablename, problemtablename, problemstr, sou.Id+"_preview")
				//
				problemstr = fmt.Sprintf(`drop table if exists `+sou.ProjectID+`_basedb."%s" ;
				create table `+sou.ProjectID+`_basedb."%s" as select * ,rand(100000000) as tempid from %s."%s" limit 1000 ;
				drop table if exists `+sou.ProjectID+`_`+`wt."%s";   ; create table  `+sou.ProjectID+`_`+`wt."%s" as  %s ;
				drop table if exists `+sou.ProjectID+`_basedb."%s" ;`,
					sou.Id+"_preview",
					sou.Id+"_preview", sou.Dbname, sou.Tbname,
					problemtablename, problemtablename, problemstr,
					sou.Id+"_preview")
			}
			//==============================================================================

		} else {
			content = fmt.Sprintf(` select *  from %s."%s"  %s %s %s `, sou.Dbname, sou.Tbname, sqltxt1, sqltxt3, sqltxt2)
		}

		tempstr = " DROP TABLE IF EXISTS " + sou.ProjectID + "_basedb." + `"` + newtbname + `"`
		_, err = MgExec(sou.Engineid, tempstr)
		if err != nil {
			logger.Error.Println(err)
			fmt.Println(err)
			return "", err
		}

		tempstr := "CREATE TABLE " + sou.ProjectID + "_basedb." + `"` + newtbname + `"` + " (" + fieldinfo + ")"
		//tempstr = strings.Replace(tempstr, `varchar`, `string`, -1)
		_, err = MgExec(sou.Engineid, tempstr)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		// fprint(problemstr, "问题库处理合集")
		problemstr = strings.Replace(problemstr, "xxn_", "", -1)
		//problemstr = strings.Replace(problemstr, "limit 1000", "", -1)

		//------------------------------------------------------

		sou.TimeField.Flag = true
		switch sou.TimeField.Flag && sou.TimeField.Field != "" {
		case true:
			tempname := sou.Tbname
			tempsql := fmt.Sprintf(`  select *,%s from %s."%s" %s %s %s`, mdf, sou.Dbname, tempname, tempsqltxt, sqltxt3, sqltxt2)
			tempstr = "insert into " + sou.ProjectID + "_basedb." + `"` + newtbname + `"` + tempsql + " limit 1000"
			//content = strings.Replace(content, "limit 1000", "", -1)

			exectable := savename

			//=============4.3.8start============
			var timetablecreate string
			var insertdate string
			if strings.Contains(sou.TimeField.Fieldtype, "string") || strings.Contains(sou.TimeField.Fieldtype, "varchar") {
				timetablecreate = fmt.Sprintf(` create table if not exists `+sou.ProjectID+"_basedb."+timetable+`("incrementtime" string ) ;`+`  insert into %s values ('0') ;`, timetable)

				//` as select from_unixtime(unix_timestamp('1970-01-01 00:00:00'),'%s') ;`, sou.TimeField.Format)

				insertdate = fmt.Sprintf(` insert into %s  select max("%s") from %s."%s" where %s<  cast( from_unixtime(unix_timestamp(current_timestamp),'%s') as string) %s limit 1;`,
					sou.ProjectID+"_basedb."+timetable, sou.TimeField.Field, sou.Dbname, sou.Tbname, sou.TimeField.Field, sou.TimeField.Format, dirtydatadi)

			} else {
				timetablecreate = ` create table if not exists ` + sou.ProjectID + "_basedb." + timetable + ` as select cast ('1970-01-01 00:00:00' as timestamp) as incrementtime ;`

				insertdate = fmt.Sprintf(` insert into %s  select max(%s) from %s."%s"  where %s< cast(  from_unixtime(unix_timestamp(current_timestamp),'yyyy-MM-dd HH:mm:ss') as timestamp) ;`,
					sou.ProjectID+"_basedb."+timetable, sou.TimeField.Field, sou.Dbname, sou.Tbname, sou.TimeField.Field)
			}
			//=============4.3.8 end ============

			execstr = timetablecreate +
				` DROP TABLE IF EXISTS ` + sou.ProjectID + "_basedb." + exectable + `;` +
				` CREATE TABLE ` + sou.ProjectID + "_basedb." + exectable + ` ( ` + fieldinfo + `);` +
				` insert into ` + sou.ProjectID + "_basedb." + exectable + content + `;` + insertdate

			execstr = execstr + problemstr
		case false:
			// content = strings.Replace(content, "\"", "", -1)
			//--------------------------------------------------
			//只有当指定条件生效
			if withstr != "" && selectstr != "" {
				tempstr = withstr + " insert into " + sou.ProjectID + "_basedb." + newtbname + " " + selectstr
			} else {
				tempstr = " insert into " + sou.ProjectID + "_basedb." + newtbname + " " + content + " limit 1000"
			}
			//---------------------------------------------------

			execstr = " DROP TABLE IF EXISTS " + sou.ProjectID + "_basedb." + savename + "; " +
				" CREATE TABLE " + sou.ProjectID + "_basedb." + savename + "  ( " + fieldinfo + ");" +
				"insert into " + sou.ProjectID + "_basedb." + savename + " " + content + `;`
			execstr = execstr + problemstr
		}
		/*注释以提升速度
		_, err = MgExec(sou.Engineid, tempstr)
		if err != nil {
			logger.Error.Println(err)
			return "", err
		}
		*/

	}

	var finalcmd string
	if sou.Dbname != "" {
		cmdpath := common.UNSUBMITTED + "/" + lab.Id
		var sqlbash string
		if engineinfo.Databasetype == common.Stork || engineinfo.Databasetype == common.Teryx || engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.DMDB || engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL {
			sqlbash = common.MG_BASH
			finalcmd = fmt.Sprintf(`python %s exec -e %s -d %s  -f %s `, sqlbash, engineinfo.Engineid, sou.Dbname, cmdpath)
		} else if engineinfo.Databasetype == common.Hive || engineinfo.Databasetype == common.Inceptor {
			/*switch engine {
			case "spark":
				sqlbash = common.SPARK_BASH
				finalcmd = fmt.Sprintf("python %s exec -p %d -f %s", sqlbash, spark.Spc.Port, cmdpath)
			default:
				sqlbash = common.HIVE_BASH
				finalcmd = fmt.Sprintf("python %s %s %s ", sqlbash, lab.Initdb, cmdpath)
				finalcmd = fmt.Sprintf("python %s %s %s ", sqlbash, lab.Initdb, cmdpath)
			}*/
			sqlbash = common.MG_BASH
			finalcmd = fmt.Sprintf(`python %s exec -e %s -d %s  -f %s `, sqlbash, engineinfo.Engineid, lab.Initdb, cmdpath)

		}

	}
	//------------------------------------
	lab.Preserve = sou.Preserve
	status := JudgeEdit(lab)

	var tbroles source.CopyTbDupRoles
	if sou.Duproles.Problem == true {
		tbroles.Problem = true
	}
	if sou.Duproles.Dataclean == true {
		tbroles.Dataclean = true
	}
	if sou.Duproles.Extraclean != nil {
		tbroles.Extraclean = sou.Duproles.Extraclean
	}

	var body = gin.H{
		"doc": gin.H{
			"content": execstr,
			"tbinfo": gin.H{
				"engineid": sou.Engineid,
				"dbname":   sou.Dbname,
				"schema":   sou.Schema,
				"tbname":   sou.Tbname,
				"layerid":  sou.Layerid,
			},
			"newtbinfo": gin.H{
				"engineid": sou.Engineid,
				"dbname":   engineinfo.Database,
				"schema":   schema,
				"tbname":   newtbname,
			},
			"fieldinfo": gin.H{
				"timefield":    sou.TimeField.Field,
				"timeterm":     sou.TimeField.Timeterm,
				"fieldtype":    sou.TimeField.Fieldtype,
				"format":       sou.TimeField.Format,
				"sortfield":    sou.SortField.Field,
				"sorttype":     sou.SortField.Sorttype,
				"wherecontent": sou.Sqltxt,
				"flag":         sou.TimeField.Flag,
				"mainkeyfield": sou.Mainkeyfield,
				"duproles":     tbroles,
			},
			"finalcmd": finalcmd,
			"initdb":   engineinfo.Database,
			"sqlid":    engineinfo.Engineid,
			"status":   status,
		},
	}

	byteBody, _ := json.Marshal(body)
	uri := fmt.Sprintf("/%s/%s/%s/_update", common.DataBaseLab, common.TbDev, sou.Id)
	_, err = httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}

	GenerateFile(execstr, common.UNSUBMITTED+"/"+lab.Id, Authorization)

	//查看是否有已上线的task
	for _, k := range lab.Dags {
		if k.DagSubmit {
			xpath := fmt.Sprintf("%s/%s/%s", common.SUBMITTED, k.Dagid, lab.Id)
			GenerateFile(execstr, xpath, Authorization)
		}
	}
	return "", nil

}

func WhereJudge(sp source.TbJson) (bool, error) {
	engineinfo, _ := GetInstance(sp.Engineid)
	newEngineinfo, _ := dbutil.EngineInfo(sp.Engineid)

	var sqltxt = fmt.Sprintf(``)
	if !strings.Contains(strings.ToLower(sp.Sqltxt), "limit") {
		sp.Sqltxt = sp.Sqltxt + ` limit 1`
	}
	if engineinfo.Databasetype == common.Stork || engineinfo.Databasetype == common.Teryx || engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.DMDB || engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL {

		sp.Sqltxt = strings.TrimRight(sp.Sqltxt, ";")
		sqltxt = fmt.Sprintf(`select * from  "%s"."%s" where %s `, sp.Schema, sp.Tbname, sp.Sqltxt)
		_, err := MgExec(sp.Engineid, sqltxt, sp.Dbname)
		if err != nil {
			if strings.Contains(err.Error(), "bad SQL grammar") {
				return false, nil
			}
			logger.Error.Println(err)
			return false, err
		}
	}
	if engineinfo.Databasetype == "hive" || engineinfo.Databasetype == common.Inceptor {
		//table := sp.Dbname + "." + sp.Tbname
		sp.Sqltxt = strings.TrimRight(sp.Sqltxt, ";")
		//sqltxt = fmt.Sprintf("select * from  %s where %s ", table, tools.QouteReplace(sp.Sqltxt))
		//_, err := MgExec(sp.Engineid, sqltxt)
		_, err := collect.MGExecuteQuerySql(newEngineinfo, sp.Dbname, fmt.Sprintf("select * from  `%s`.`%s` where %s ", sp.Dbname, sp.Tbname, sp.Sqltxt))
		if err != nil {
			if strings.Contains(err.Error(), "bad SQL grammar") {
				return false, nil
			}
			logger.Error.Println(err)
			return false, err
		}

	}

	return true, nil
}

// 给下一个模组传参
func Listmodule(info source.TableJson) (interface{}, error) {
	result := []gin.H{}
	var sourcedb, tbname string
	if len(info.Pre) != 0 {
		for _, v := range info.Pre {
			var terms []map[string]interface{}
			// term1 := gin.H{
			// 	"term": gin.H{
			// 		"taskid": info.Taskid,
			// 	},
			// }
			// terms = append(terms, term1)
			term2 := gin.H{
				"term": gin.H{
					"subid": v,
				},
			}
			terms = append(terms, term2)
			body := gin.H{
				"query": gin.H{
					"bool": gin.H{
						"must": terms,
					},
				},
				"size": 1000,
			}
			byteBody, _ := json.Marshal(body)

			url := fmt.Sprintf("/%s/%s/_search", common.DataBaseLab, common.TbDev)
			res, err := httpclient.Post(url, string(byteBody))
			if err != nil {
				logger.Error.Println(err)
				return "", err
			}

			modulename := gjson.Get(string(res), "hits.hits.0._source.notename").String()
			middletablename := gjson.Get(string(res), "hits.hits.0._source.middletable.tablename").String()
			moduleid := gjson.Get(string(res), "hits.hits.0._source.id").String()
			temptable := gjson.Get(string(res), "hits.hits.0._source.newtbinfo.tbname").String()
			module := gjson.Get(string(res), "hits.hits.0._source.module").Int()

			sourcedb = gjson.Get(string(res), "hits.hits.0._source.newtbinfo.schema").String()
			if sourcedb == "" {
				sourcedb = gjson.Get(string(res), "hits.hits.0._source.newtbinfo.dbname").String()
			}
			if sourcedb == "" {
				sourcedb = fmt.Sprintf(`%s_basedb`, info.ProjectID)
			}

			tbname = gjson.Get(string(res), "hits.hits.0._source.newtbinfo.tbname").String()
			if tbname == "" {
				tbname = moduleid
			}
			if module != 10 && module != 0 {
				tbname = strings.ReplaceAll(tbname, "xxn_", "")
			}

			record := gin.H{
				"modulename":      modulename,
				"moduleid":        moduleid,
				"middletablename": middletablename,
				"temptable":       temptable,
				"module":          module,
				"sourcedb":        sourcedb,
				"tbname":          tbname,
			}

			result = append(result, record)

		}
	}
	return result, nil
}

func ListSubject() (interface{}, error) {
	record := []gin.H{}
	body := gin.H{
		"size": 10000,
	}
	byteBody, err := json.Marshal(body)

	uri := fmt.Sprintf("/danastudio-asset/dwd_info/_search")
	resp, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return record, err
	}
	e := gjson.Get(string(resp), "hits.hits").Array()
	for _, v := range e {
		subjectname := v.Get("_source.elementname").String()
		result := gin.H{
			"subject": subjectname,
		}
		record = append(record, result)
	}
	return record, nil
}

func OutputPreview(info source.OutputSave, lab source.ProDevInfo, showsql string, engineinfo source.InstanceInfo) ([]string, [][]interface{}, []map[string]interface{}, error) {
	var result []map[string]interface{}
	var cols []string
	var data [][]interface{}

	switch {
	case engineinfo.Databasetype == common.Stork || engineinfo.Databasetype == common.Teryx, engineinfo.Databasetype == common.GaussDB, engineinfo.Databasetype == common.UXDB, engineinfo.Databasetype == common.GREENPLUM, engineinfo.Databasetype == common.POSTGRES, engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL:
		rows, err := MgExec(engineinfo.Engineid, showsql, lab.Newtbinfo.Dbname)
		if err != nil {
			logger.Error.Println(err)
			return cols, data, result, err
		}
		for _, j := range rows.Cols {
			for k := range j {
				cols = append(cols, k)
				continue
			}
		}
		data = tools.ParseData(rows.Values, cols)
		result = rows.Values

	case engineinfo.Databasetype == "hive" || engineinfo.Databasetype == common.Inceptor:
		sparkRes, err := MgExec(engineinfo.Engineid, showsql)
		if err != nil {
			logger.Error.Println(err)
			return cols, data, result, err
		}

		for _, j := range sparkRes.Cols {
			for k := range j {
				cols = append(cols, k)
				continue
			}
		}
		data = tools.ParseData(sparkRes.Values, cols)
		result = sparkRes.Values

	}

	return cols, data, result, nil
}

func CreateSql(info source.OutputSave, lab source.ProDevInfo, engineinfo source.InstanceInfo) (string, string, error) {
	//fieldPart := fmt.Sprintf(``)
	field := fmt.Sprintf(``)
	//values := fmt.Sprintf(``)
	//comment := fmt.Sprintf(``)
	sqltxt := fmt.Sprintf(``)
	showsql := fmt.Sprintf(``)

	var inputField, outputField string //左 Input, 右 Output
	// inputField = strings.Join(info.FieldMap.Output, ",")
	// outputField = strings.Join(info.FieldMap.Input, ",")

	switch {
	case engineinfo.Databasetype == "stork" || engineinfo.Databasetype == common.Teryx || engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.DMDB || engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL:
		//输出表：schema.tbname
		inputField = `"` + strings.Join(info.FieldMap.Input, `","`) + `"`
		outputField = `"` + strings.Join(info.FieldMap.Output, `","`) + `"`
		finaltbname := `"` + info.Finaltable.SourceDB + `"."` + info.Finaltable.Tbname + `"`
		//输入设置生成的表
		//fromtable := lab.ProjectID + "_" + common.BASEDB + `."` + lab.Id + `"`
		var fromtable string
		if lab.LastModule == 0 {
			fromtable = fmt.Sprintf(`"%s"."%s"`, lab.Tbinfo.Schema, lab.Tbinfo.Tbname)
		} else {
			fromtable = fmt.Sprintf(`"%s_%s"."%s"`, lab.ProjectID, common.BASEDB, lab.Tbinfo.Tbname)
		}
		var orderPart string
		if info.SortField != "" {
			orderPart = fmt.Sprintf(`order by "%s" %s`, info.SortField, info.SortType)
		}

		//拼接字段名+字段类型，字段描述

		//改为从字段映射中取字段，在 switch 上面

		// for _, v := range info.Changedinfo {
		// 	field = fmt.Sprintf(`%s "%s",`, field, v.Fieldname)
		// 	values = fmt.Sprintf(`%s "%s" %s,`, values, v.Fieldname, v.Fieldtype)
		// 	comment = fmt.Sprintf(`%s comment on column %s."%s" is '%s';`, comment, finaltbname, v.Fieldname, v.Fieldtype)
		// }
		// field = strings.TrimRight(field, ",")
		// values = strings.TrimRight(values, ",")

		//----------------------------------------------------
		//4.6.4版本暂不实现teryx分区

		if info.Iscover {
			//sqltxt = fmt.Sprintf(`drop table if exists %s;
			//create table %s(%s) as select %s from %s;%s`,
			//	finaltbname, finaltbname, values, field, fromtable, comment)
			sqltxt = fmt.Sprintf(`TRUNCATE table %s;insert into %s(%s) select %s from %s %s;`, finaltbname, finaltbname, outputField, inputField, fromtable, orderPart)
		} else {
			// sqltxt = fmt.Sprintf(`create table if not exists %s(%s);
			// %s;
			// insert into %s select %s from %s;`,
			// 	finaltbname, values, comment, finaltbname, field, fromtable)

			sqltxt = fmt.Sprintf(`
				--tmp_test_run
				truncate table %s;
				insert into %s(%s) select %s from %s %s;`, finaltbname, finaltbname, outputField, inputField, fromtable, orderPart)
		}

		// showsql = fmt.Sprintf(`select %s from "%s"."%s" limit 100;`,
		// 	field, lab.Newtbinfo.Schema, lab.Newtbinfo.Tbname)
		//fmt.Println(showsql)
	case engineinfo.Databasetype == "hive" || engineinfo.Databasetype == common.Inceptor:
		inputField = "`" + strings.Join(info.FieldMap.Input, "`,`") + "`"
		outputField = "`" + strings.Join(info.FieldMap.Output, "`,`") + "`"
		finaldbname := info.Finaltable.Dbname
		if finaldbname == "" {
			finaldbname = common.DWDDB
		}
		var orderPart string
		if info.SortField != "" {
			for i := range info.Outputfields {
				if info.SortField == info.Outputfields[i] {
					if engineinfo.Databasetype == "hive" {
						orderPart = fmt.Sprintf("order by `_col%d` %s", i, info.SortType)
					} else {
						orderPart = fmt.Sprintf("order by `%s` %s", info.SortField, info.SortType)
					}
				}
			}

		}

		lab.Newtbinfo.Tbname = tools.AddBasedb(lab.ProjectID, lab.Newtbinfo.Tbname)
		//var fieldList []string
		//for _, v := range info.Fieldinfo {
		//	fieldList = append(fieldList, v.Oldfieldname)
		//}
		//fieldname := strings.Join(fieldList, `,`)

		for _, v := range info.Changedinfo {
			var isPar bool
			for _, vv := range info.PartitionField {
				if v.Fieldname == vv.Name {
					isPar = true
					break
				}
			}
			if !isPar {
				field = fmt.Sprintf("%s `%s`,", field, v.Fieldname)
				//values = fmt.Sprintf(`%s "%s" %s comment'%s',`, values, v.Fieldname, v.Fieldtype, v.Fielddescribe)
			}
		}
		for _, v := range info.PartitionField {
			field = fmt.Sprintf("%s `%s`,", field, v.Name)
		}

		field = strings.TrimRight(field, ",")
		//values = strings.TrimRight(values, ",")

		finaltbname := info.Finaltable.Tbname
		//输入设置生成的表
		//fromtable := lab.Tbinfo.Tbname
		var fromtable string
		if lab.LastModule == 0 {
			fromtable = fmt.Sprintf("`%s`.`%s`", lab.Tbinfo.Dbname, lab.Tbinfo.Tbname)
		} else {
			fromtable = fmt.Sprintf("`%s_%s`.`%s`", lab.ProjectID, common.BASEDB, lab.Tbinfo.Tbname)
		}

		////分区用的临时表
		//temptable := common.BASEDB + "tmp_" + lab.Id
		////字段名+字段类型
		//var valuesPT = fmt.Sprintf(``)
		//if strings.Contains(values, "\"pt\" timestamp") {
		//	valuesPT = values
		//	index := strings.LastIndex(field, ",")
		//	if index != -1 {
		//		field = field[:index]
		//	}
		//} else {
		//	valuesPT = values + ", pt timestamp"
		//}

		//valuesPT := values + ", pt timestamp"
		var fieldSql, tempSql, tbSql string
		var pFields []string //分区字段列表
		//区分ds创建的表和已有分区表的区分
		if len(info.PartitionField) > 0 {
			//只有新建表会走到这步
			for _, v := range info.Changedinfo {
				var isPar bool
				for _, vv := range info.PartitionField {
					if v.Fieldname == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v.Fieldname)
				}
			}

			fieldSql = fieldSql[1:]
			tbSql = fieldSql
			tempSql = fieldSql
			//分区设计--离线采集
			//已有字段 -- 根据已有字段名填入
			//系统预设 -- 一级分区：补全时间格式yyyyMMdd
			//        --多级分区：纯系统预设 -- yyyy/MM/dd
			//        --多级分区：混合设置 -- 补全时间格式
			var isMix = 0
			for _, v := range info.PartitionField {
				if v.Ispreset == "notpreset" {
					if isMix == 2 {
						isMix = 3
						break
					} else {
						isMix = 1
					}
				} else {
					if isMix == 1 {
						isMix = 3
						break
					} else {
						isMix = 2
					}
				}
			}
			for _, v := range info.PartitionField {
				pFields = append(pFields, "`"+v.Name+"`")
				if v.Ispreset == "notpreset" {
					tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
					tempSql = fmt.Sprintf("%s,`%s`", tempSql, v.Name)
				} else {
					tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
					if len(info.PartitionField) == 1 || isMix == 3 {
						//系统预设一级分区
						switch v.Ispreset {
						case "year":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
						case "month":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM') as ", v.Name)
						case "day":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd') as ", v.Name)
						}
					} else {
						if isMix == 2 {
							switch v.Ispreset {
							case "year":
								tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
							case "month":
								tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'MM') as ", v.Name)
							case "day":
								tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'dd') as ", v.Name)
							}
						}
					}
				}
			}
		}

		////iscover 为true 表输出数据覆盖 false 表输出数据追加
		//if info.Iscover {
		//	sqltxt = fmt.Sprintf(`
		//		set hive.exec.dynamic.partition.mode=nonstrict;
		//		drop table if exists %s;
		//		create table %s(%s);
		//		insert into %s select %s,cast(date_format(CURRENT_TIMESTAMP(),'yyyy-MM-dd HH:mm:00') as timestamp) from %s;
		//		drop table if exists %s.%s;
		//		create table if not exists %s.%s(%s) partitioned by (pt timestamp);
		//		INSERT INTO %s.%s partition(pt) SELECT %s, pt AS pt FROM %s;`,
		//		temptable, temptable, valuesPT, temptable, field, fromtable, finaldbname, finaltbname, finaldbname, finaltbname, values, finaldbname, finaltbname, field, temptable)
		//} else {
		//	sqltxt = fmt.Sprintf(`
		//		set hive.exec.dynamic.partition.mode=nonstrict;
		//		drop table if exists %s;
		//		create table %s(%s);
		//		insert into %s select %s,cast(date_format(CURRENT_TIMESTAMP(),'yyyy-MM-dd HH:mm:00') as timestamp) from %s;
		//		create table if not exists %s.%s(%s) partitioned by (pt timestamp);
		//		INSERT INTO %s.%s partition(pt) SELECT %s, pt AS pt FROM %s;`,
		//		temptable, temptable, valuesPT, temptable, field, fromtable, finaldbname, finaltbname, values, finaldbname, finaltbname, field, temptable)
		//}
		//iscover 为true 表输出数据覆盖 false 表输出数据追加
		//480todo 覆盖逻辑改为 truncate 删数据；不再建表，直接插数据

		//--tmp_test_run 后的 truncate table，前方固定留 4 个 tab
		if len(info.PartitionField) > 0 {
			if engineinfo.Databasetype == common.Inceptor { //tdh 分区语法
				if info.Iscover {
					sqltxt = fmt.Sprintf(`
					set hive.exec.dynamic.partition=true;
					set hive.exec.dynamic.partition.mode=nonstrict;
					truncate table %s.%s;
					INSERT INTO %s.%s PARTITION (%s) SELECT %s FROM %s %s;`,
						finaldbname, finaltbname, finaldbname, finaltbname, strings.Join(pFields, ","), tempSql, fromtable, orderPart)
				} else {
					sqltxt = fmt.Sprintf(`
					set hive.exec.dynamic.partition=true;
					set hive.exec.dynamic.partition.mode=nonstrict;
					--tmp_test_run
				truncate table %s.%s;
					INSERT INTO %s.%s PARTITION (%s) SELECT %s FROM %s %s;`,
						finaldbname, finaltbname,
						finaldbname, finaltbname, strings.Join(pFields, ","), tempSql, fromtable, orderPart)
				}
			} else {
				if info.Iscover {
					sqltxt = fmt.Sprintf(`
					set hive.exec.dynamic.partition.mode=nonstrict;
					truncate table %s.%s;
					INSERT INTO %s.%s (%s) SELECT %s FROM %s %s;`,
						finaldbname, finaltbname, finaldbname, finaltbname, tbSql, tempSql, fromtable, orderPart)
				} else {
					sqltxt = fmt.Sprintf(`
					set hive.exec.dynamic.partition.mode=nonstrict;
					--tmp_test_run
				truncate table %s.%s;
					INSERT INTO %s.%s (%s) SELECT %s FROM %s %s;`,
						finaldbname, finaltbname,
						finaldbname, finaltbname, tbSql, tempSql, fromtable, orderPart)
				}
			}

		} else {
			if info.Iscover {
				sqltxt = fmt.Sprintf(`
				set hive.exec.dynamic.partition.mode=nonstrict;
				truncate table %s.%s;
				INSERT INTO %s.%s (%s) SELECT %s FROM %s %s;`,
					finaldbname, finaltbname, finaldbname, finaltbname, outputField, inputField, fromtable, orderPart)
			} else {
				sqltxt = fmt.Sprintf(`
				set hive.exec.dynamic.partition.mode=nonstrict;
				--tmp_test_run
				truncate table %s.%s;
				INSERT INTO %s.%s (%s) SELECT %s FROM %s %s;`,
					finaldbname, finaltbname,
					finaldbname, finaltbname, outputField, inputField, fromtable, orderPart)
			}
		}

		//构建输出预览的 sql

		showsql = fmt.Sprintf(`select %s from %s limit 100`, field, lab.Newtbinfo.Tbname)
	}

	return sqltxt, showsql, nil
}

func OutputSaveNew(sp *source.OutputSave) error {
	var err error
	var db metasource.AccDbInfo

	sp.NewAcc.IsWorkflow = true

	//db 构建
	db, err = fixExtractDbInfo(sp.NewAcc)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	//var tmppartitionfield []source.PartitionFieldInfo

	var tmppartitionfield []*source.PartitionFieldInfo
	//sp2 := new(source.NewAcc)
	httpdotools.DeepCopy(&tmppartitionfield, &sp.Sourcedb.PartitionField)
	err = fixSpSourcedb(&sp.NewAcc)
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	var dataStrategy int //1 覆盖，2 追加
	if sp.Sourcedb.IsCover {
		if sp.Sourcedb.ConditionCover == "" {
			dataStrategy = 1 //全量覆盖
		} else {
			dataStrategy = 0 //条件覆盖
		}
	} else {
		dataStrategy = 2
	}
	//补充 finaltable
	if sp.Sourcedb.Schema == "" {
		sp.Sourcedb.Schema = sp.Sourcedb.Odsdatabase
	}

	err = handleFinaltable(sp)
	if err != nil {
		logger.Error.Println(err)
		return err
	}

	//sql 构建
	inputSql(sp)

	content, _, _, err := asset.AccCreateJson(&sp.NewAcc, &db, dataStrategy)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	// fmt.Println("---content: ", content)
	// fmt.Println("---dbpath: ", dbpath)
	// fmt.Println("---analyzeSql: ", analyzeSql)
	// fmt.Println("---err: ", err)
	content = strings.Replace(content, "\\u003c", "<", -1)
	content = strings.Replace(content, "\\u003e", ">", -1)
	content = strings.Replace(content, "\\u0026", "&", -1)
	content = strings.Replace(content, `u0000u`, `\\`, -1)
	content = strings.Replace(content, `\\\\`, `\\`, -1) //上层 json 拼接逻辑全部已在 python 脚本中为前提，对反引号有两层转义，此处只需要一层

	sp.Content = content
	httpdotools.DeepCopy(&sp.Sourcedb.PartitionField, &tmppartitionfield)
	logger.Info.Println("sp.Sourcedb.PartitionField77 ", sp.Sourcedb.PartitionField, tmppartitionfield)
	return nil
}

// 构建 finaltable
func handleFinaltable(sp *source.OutputSave) error {
	var queryBody metasource.TbStatusReq
	queryBody.ID = sp.Sourcedb.TableID
	queryBody.Status = 1

	metatb, _ := metadev.TbDetail(queryBody)
	var f source.OutputFinalTable
	s := sp.Sourcedb
	f.Tbname = s.Table
	f.Dbname = s.Odsdatabase
	f.Schema = s.Schema
	f.Engineid = s.Engineid
	//f.MetaTBID
	f.SourceID = s.Sourceid
	f.TbType = s.Dbtype
	f.SourceDB = s.Schema
	f.SourceID = s.Sourceid
	f.CatalogID = s.CatalogID
	f.LayerID = s.Layerid
	f.FileFormat = s.FileType
	f.Engineid = s.Engineid
	f.Describe = metatb.Describe
	f.Chinese = metatb.Chinese
	sp.Finaltable = f

	//newtbinfo

	//补充其他属性
	sp.Sourcedb.FileType = metatb.FileFormat

	//修正Sourcedb.Field、Sourcedb.Fieldtype、Sourcedb.Fieldcomment
	if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) || len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldcomment) {
		return fmt.Errorf("sourcedb 字段长度与类型长度不一致")
	}
	m := make(map[string][2]string)
	for i, j := range sp.Sourcedb.Field {
		m[j] = [2]string{sp.Sourcedb.Fieldtype[i], sp.Sourcedb.Fieldcomment[i]}
	}
	fieldLen := len(sp.FieldMap.Output)
	fieldType := make([]string, fieldLen)
	fieldComment := make([]string, fieldLen)
	for i, j := range sp.FieldMap.Output {
		fieldType[i] = m[j][0]
		fieldComment[i] = m[j][1]
	}
	sp.Sourcedb.TempSourceField.FieldName = sp.Sourcedb.Field
	sp.Sourcedb.TempSourceField.FieldType = sp.Sourcedb.FieldType
	sp.Sourcedb.TempSourceField.FieldComment = sp.Sourcedb.FieldComment

	sp.Sourcedb.Field = sp.FieldMap.Output
	sp.Sourcedb.Fieldtype = fieldType
	sp.Sourcedb.Fieldcomment = fieldComment

	if len(metatb.PartitionField) > 0 {
		b, _ := json.Marshal(metatb.PartitionField)
		json.Unmarshal(b, &sp.Sourcedb.PartitionField)
	}
	sp.Iscover = sp.Sourcedb.IsCover
	sp.DeleteHistory = sp.Sourcedb.Deletehistory
	return nil
}

// 构建查询sql
func inputSql(sp *source.OutputSave) {
	var qoute string
	switch sp.Extractdb.DBType {
	case common.Hive, common.Inceptor:
		qoute = "`"
	default:
		qoute = `"`
	}
	var fieldList []string

	//内置分区字段放到末尾
	if len(sp.Sourcedb.PartitionField) > 0 {
		ptField := make(map[string]bool)
		fieldMap := make(map[string]string)

		for i, j := range sp.FieldMap.Input {
			fieldMap[sp.FieldMap.Output[i]] = j
		}

		var ptList []string
		for _, i := range sp.Sourcedb.PartitionField {
			if _, ok := fieldMap[i.Name]; ok && i.Ispreset == "notpreset" {
				ptField[fieldMap[i.Name]] = true
				ptList = append(ptList, fieldMap[i.Name])
			}
		}

		for _, i := range sp.FieldMap.Input {
			if ptField[i] {
				continue
			}
			fieldList = append(fieldList, i)
		}
		fieldList = append(fieldList, ptList...)
	} else {
		fieldList = sp.FieldMap.Input
	}

	//select "b" from "test_basedb"."c5T4WX5XR2R"
	sp.Extractdb.SqlTxt = fmt.Sprintf(`select %s%s%s from %s%s%s.%s%s%s`, qoute, strings.Join(fieldList, fmt.Sprintf(`%s%s%s`, qoute, ",", qoute)), qoute, qoute, sp.Extractdb.Schema, qoute, qoute, sp.Extractdb.Table, qoute)
	return
}

// 查询存储源引擎相关信息
func fixSpSourcedb(res *source.NewAcc) error {
	//根据引擎ID查询连接信息
	engineinfo, err := dbutil.EngineInfo(res.Sourcedb.Engineid)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	res.Sourcedb.Odspassword = engineinfo.Password
	res.Sourcedb.Odsuser = engineinfo.Username
	res.Sourcedb.Odsip = engineinfo.IP
	res.Sourcedb.Odsport = strconv.Itoa(engineinfo.Port)
	res.Sourcedb.RegisterWay = engineinfo.RegisterWay
	res.Sourcedb.JdbcURL = engineinfo.JdbcURL
	return nil
}

// 抽取的引擎信息查询
func fixExtractDbInfo(sp source.NewAcc) (metasource.AccDbInfo, error) {
	var result metasource.AccDbInfo
	result.DBType = sp.Extractdb.DBType
	//根据引擎ID查询连接信息
	engineinfo, err := dbutil.EngineInfo(sp.Extractdb.EngineID)
	if err != nil {
		logger.Error.Println(err)
		return result, err
	}
	if result.DBType == common.Hive || result.DBType == common.Inceptor {
		result.HiveConn.HiveURL = engineinfo.JdbcURL //TODO 可能不一致
		result.HiveConn.HiveUser = engineinfo.Username
		result.HiveConn.HivePassword = engineinfo.Password
		if engineinfo.KerberosInfo.Id != "" {
			result.HiveConn.AuthMethod = 2
		}
		result.HiveConn.KeytabFilePath = engineinfo.KerberosInfo.KeyTabPath
		result.HiveConn.Principal = engineinfo.KerberosInfo.Principal
		result.HiveConn.Krb5ConfPath = engineinfo.KerberosInfo.Krb5ConfPath
		// add driver path
		result.HiveConn.DriveInfo.DriverPath = engineinfo.DriverInfo.DriverPath
	} else if result.DBType == "greenplum" || result.DBType == "postgres" || result.DBType == common.Teryx ||
		result.DBType == common.GaussDB || result.DBType == common.GaussDBA || result.DBType == common.Stork || result.DBType == common.UXDB || result.DBType == common.TDSQL {
		result.NormalConn.DBIP = engineinfo.IP
		result.NormalConn.DBPort = strconv.Itoa(engineinfo.Port)
		result.NormalConn.Database = sp.Extractdb.Dbname
		result.NormalConn.DBUser = engineinfo.Username
		result.NormalConn.DBPassword = engineinfo.Password
	} else if result.DBType == common.DMDB {
		result.NormalConn.DBIP = engineinfo.IP
		result.NormalConn.DBPort = strconv.Itoa(engineinfo.Port)
		result.NormalConn.Database = sp.Extractdb.Dbname
		result.NormalConn.DBUser = engineinfo.Username
		result.NormalConn.DBPassword = engineinfo.Password
		result.NormalConn.JdbcUrl = engineinfo.JdbcURL
		result.NormalConn.RegisterWay = engineinfo.RegisterWay
	} else if result.DBType == common.ODPS {
		result.NormalConn.DBUser = engineinfo.Username
		result.NormalConn.DBPassword = engineinfo.Password
		result.NormalConn.Database = engineinfo.OdpsProject
	}
	return result, nil
}

func OutputSave(info source.OutputSave, lab source.ProDevInfo, engineinfo source.InstanceInfo, Authorization string) (string, error) {
	//curEng, _ := tools.CurEngine()
	//curEng = "spark"
	var finalcmd string
	if lab.Initdb != "" {
		cmdpath := common.UNSUBMITTED + "/" + lab.Id
		var sqlbash string
		if engineinfo.Databasetype == common.Stork || engineinfo.Databasetype == common.Teryx || engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.DMDB || engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL {
			Joinsql := strings.Replace(lab.CookingTempcontent, "xxn_", "", -1)
			info.Content = Joinsql + info.Content

			//删除关联表的全量表
			if len(lab.CookingInput) != 0 {
				var delids []string
				for i := 0; i < len(lab.CookingInput); i++ {
					delids = append(delids, lab.CookingInput[i].Moduleid)
				}
				for _, v := range delids {
					info.Content = info.Content + fmt.Sprintf(`drop table if exists "%s"."%s" ;`, lab.ProjectID+"_"+common.BASEDB, v)
				}
				info.Content = info.Content + fmt.Sprintf(`drop table if exists "%s"."%s" ;`, lab.ProjectID+"_"+common.BASEDB, info.Moduleid)
			}

			sqlbash = common.MG_BASH
			finalcmd = fmt.Sprintf(`python %s exec -e %s -d %s  -f %s `, sqlbash, engineinfo.Engineid, lab.Initdb, cmdpath)
		} else if engineinfo.Databasetype == common.Hive || engineinfo.Databasetype == common.Inceptor {
			//switch curEng {
			//case common.EngSpark:
			Joinsql := strings.Replace(lab.CookingTempcontent, "xxn_", "", -1)
			info.Content = Joinsql + info.Content
			// fmt.Println("这里打印一下info。content")
			// fmt.Println(info.Content)
			//-----------------------------------------
			//这部分需要删除关联表的全量表
			if len(lab.CookingInput) != 0 {
				var delids []string
				for i := 0; i < len(lab.CookingInput); i++ {
					delids = append(delids, lab.CookingInput[i].Moduleid)
				}
				for _, v := range delids {
					info.Content = info.Content + fmt.Sprintf(`drop table if exists %s.%s ;`, lab.ProjectID+"_"+common.BASEDB, v)
				}
				info.Content = info.Content + fmt.Sprintf(`drop table if exists %s.%s ;`, lab.ProjectID+"_"+common.BASEDB, info.Moduleid)
			}
			//sqlbash = common.MG_BASH
			//finalcmd = fmt.Sprintf(`python %s exec -e %s -d %s  -f %s `, sqlbash, engineinfo.Engineid, lab.Initdb, cmdpath)
			//default: // presto , ""
			//	Joinsql := strings.Replace(lab.CookingTempcontent, "xxn_", "", -1)
			//	info.Content = Joinsql + info.Content
			//	// fmt.Println("这里打印一下info。content")
			//	// fmt.Println(info.Content)
			//	//-----------------------------------------
			//	//这部分需要删除关联表的全量表
			//	if len(lab.CookingInput) != 0 {
			//		var delids []string
			//		for i := 0; i < len(lab.CookingInput); i++ {
			//			delids = append(delids, lab.CookingInput[i].Moduleid)
			//		}
			//		for _, v := range delids {
			//			info.Content = info.Content + fmt.Sprintf(`drop table if exists %s ;`, v)
			//		}
			//		info.Content = info.Content + fmt.Sprintf(`drop table if exists %s ;`, info.Moduleid)
			//	}
			//	sqlbash = common.HIVE_BASH
			//	finalcmd = fmt.Sprintf("python %s %s %s ", sqlbash, lab.Initdb, cmdpath)
			//}
			sqlbash = common.MG_BASH
			finalcmd = fmt.Sprintf(`python %s exec -e %s -d %s  -f %s `, sqlbash, engineinfo.Engineid, lab.Initdb, cmdpath)
			//finalcmd = fmt.Sprintf("python %s %s %s ", sqlbash, lab.Initdb, cmdpath)
		}

	}
	cmdpath := common.UNSUBMITTED + "/" + lab.Id
	// var sqlbash string
	// sqlbash = common.MG_BASH
	//finalcmd = fmt.Sprintf(`python %s exec -e %s -d %s  -f %s `, sqlbash, engineinfo.Engineid, info.Finaltable.Dbname, cmdpath)

	//落地模组改用 datax 脚本
	//pytyon /opt/dana/datax/bin/datax.py c5T4WX5XR2R
	finalcmd = fmt.Sprintf(`python %s %s `, common.DATAX_PATH, cmdpath)
	//fmt.Println(finalcmd)

	//otuputbname := "\"" + info.Finaltable.Tbname + "\""
	// Joinsql := strings.Replace(lab.CookingTempcontent, "xxn_", "", -1)
	// content := Joinsql + info.Content
	//comment := fmt.Sprintf(`drop table if exists %s; %s`, otuputbname, info.Content)
	updatetime := time.Now().Format("2006-01-02 15:04:05")

	outputcomment := []string{}
	outputfieldtypes := []string{}
	for i := 0; i < len(info.Changedinfo); i++ {
		outputcomment = append(outputcomment, info.Fieldinfo[i].Newdescribe)
		outputfieldtypes = append(outputfieldtypes, info.Fieldinfo[i].Newfieldtype)
	}
	for _, parField := range info.PartitionField {
		if parField.Ispreset != "notpreset" {
			outputcomment = append(outputcomment, parField.Annotation)
			outputfieldtypes = append(outputfieldtypes, parField.FieldType)
		}
	}
	//--------------------------------------------------------------------
	//状态
	lab.Preserve = info.Preserve
	status := JudgeEdit(lab)

	lab.Newtbinfo.Tbname = info.Finaltable.Tbname
	var commentsMap = make(map[string]string)
	for _, v := range info.Changedinfo {
		commentsMap[v.Fieldname] = v.Fielddescribe
	}
	//处理数据策略
	if info.DeleteHistory {
		finalcmd = fmt.Sprintf(`curl -XGET 'http://127.0.0.1:%d/danastudio/ants/dataprocess/outdelete:%s' -d{}  && `, common.SERVER_PORT, info.Moduleid) + finalcmd
	}
	body := gin.H{
		"doc": gin.H{
			"finalcmd":           finalcmd,
			"content":            info.Content,
			"finaltable":         info.Finaltable,
			"outputfields":       info.Outputfields,
			"outputprimkeys":     info.OutputPrimkeys,
			"outputcomment":      outputcomment,
			"outputfieldtypes":   outputfieldtypes,
			"fieldinfo":          info.Fieldinfo,
			"sorttype":           info.SortType,
			"sortfield":          info.SortField,
			"status":             status,
			"outputshowsql":      info.OutputShowsql,
			"updatetime":         updatetime,
			"initdb":             lab.Initdb,
			"changedinfo":        info.Changedinfo,
			"iscover":            info.Iscover,
			"partitionfield":     info.PartitionField,
			"distributionbond":   info.DistributionBond,
			"distributionmode":   info.DistributionMode,
			"newtbinfo":          lab.Newtbinfo,
			"commentsmap":        commentsMap,
			"usemetatb":          info.UseMetaTB,
			"metatbid":           info.MetaTBID,
			"fieldmap":           info.FieldMap,
			"orientation":        info.Orientation,
			"compresstype":       info.CompressType,
			"compresslevel":      info.CompressLevel,
			"deletehistory":      info.DeleteHistory,
			"extractdb":          info.Extractdb,
			"sourcedb":           info.Sourcedb,
			"standardfieldinfos": lab.StandardFieldinfos,
		},
	}

	fmt.Println("update body : ", tools.M2J(body))
	err := httpclient.EUpdate(common.DataBaseLab, common.TbDev, body, info.Moduleid)
	if err != nil {
		logger.Error.Println(err)
		return "", err
	}
	GenerateFile(info.Content, common.UNSUBMITTED+"/"+lab.Id, Authorization)

	//查看是否有已上线的工作流
	for _, k := range lab.Dags {
		if k.DagSubmit {
			xpath := fmt.Sprintf("%s/%s/%s", common.SUBMITTED, k.Dagid, lab.Id)
			GenerateFile(info.Content, xpath, Authorization)
		}
	}

	return "保存成功", nil
}

func OutputShowTable(lab source.ProDevInfo, engineinfo source.InstanceInfo) (source.ProWatchTableInfo, error) {
	var tb source.ProWatchTableInfo

	if engineinfo.Databasetype == common.Stork || engineinfo.Databasetype == common.Teryx || engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.DMDB || engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL {
		tbName := `"` + lab.Newtbinfo.Schema + `"."` + lab.Newtbinfo.Tbname + `"`

		//字段
		des, err := MgTableDesc(engineinfo.Engineid, lab.Newtbinfo.Dbname, lab.Newtbinfo.Schema, lab.Newtbinfo.Tbname)
		if err != nil {
			return tb, err
		}

		//数据
		records, err := spark.MgShowResult(engineinfo.Engineid, lab.Newtbinfo.Dbname, tbName, des.Columnname)
		if err != nil {
			logger.Error.Println(err)
			return tb, err
		}
		tb.CookingFieldRecords = records

		if lab.Changedinfo != nil {
			for _, v := range lab.Changedinfo {
				tb.Fields = append(tb.Fields, v.Fieldname)
				tb.Fieldtypes = append(tb.Fieldtypes, v.Fieldtype)
				tb.Fieldcomments = append(tb.Fieldcomments, v.Fielddescribe)
			}
		} else {
			tb.Fields = des.Columnname
			tb.Fieldcomments = des.Columntype
			tb.Fieldtypes = des.Columntype
		}

	} else if engineinfo.Databasetype == common.Hive || engineinfo.Databasetype == common.Inceptor {

		lab.Newtbinfo.Tbname = tools.AddBasedb(lab.ProjectID, lab.Newtbinfo.Tbname)

		//字段
		re, err := MgExec(engineinfo.Engineid, fmt.Sprintf(` desc %s`, lab.Newtbinfo.Tbname))
		if err != nil {
			return tb, err
		}
		fieldInfo := MgHiveDesc(re.Values)

		//数据
		records, err := spark.MgShowResult(engineinfo.Engineid, common.EmptyString, lab.Newtbinfo.Tbname, fieldInfo.Columnname)
		if err != nil {
			logger.Error.Println(err)
			return tb, err
		}
		tb.CookingFieldRecords = records

		if lab.Changedinfo != nil {
			for _, v := range lab.Changedinfo {
				tb.Fields = append(tb.Fields, v.Fieldname)
				tb.Fieldtypes = append(tb.Fieldtypes, v.Fieldtype)
				tb.Fieldcomments = append(tb.Fieldcomments, v.Fielddescribe)
			}
		} else {
			tb.Fields = fieldInfo.Columnname
			tb.Fieldcomments = fieldInfo.Columntype
			tb.Fieldtypes = fieldInfo.Columntype
		}
	}

	for i := 0; i < len(tb.Fields); i++ {
		k := 1
		for j := i + 1; j < len(tb.Fields); j++ {
			if tb.Fields[j] == tb.Fields[i] {
				tb.Fields[j] = tb.Fields[j] + "_" + strconv.Itoa(k)
				k = k + 1
			}
		}
	}

	return tb, nil
}

// 定期删除主题表
func ProSubjectTable() {
	jbody := `{
    "query": {
        "wildcard": {
            "finaltable.tbname": "*" 
        }
    }
}`
	uri := fmt.Sprintf("/%s/%s/_search", "danastudio_ants_lab", "tb_dev")
	resp, err := httpclient.Post(uri, string(jbody))
	if err != nil {
		logger.Error.Println(err)
		return
	}
	e := gjson.Get(string(resp), "hits.hits").Array()

	if len(e) != 0 {
		for _, v := range e {
			id := v.Get("_source.id")
			fmt.Println("id是 :", id)
			updatetime := v.Get("_source.updatetime").String()
			subjecttablename := v.Get("_source.finaltable.tbname").String()
			parsetime, _ := time.Parse("2006-01-02 15:04:05", updatetime)

			days := v.Get("_source.finaltable.days").Int()
			fmt.Println("天数是：", days)
			fmt.Printf("相隔%v 天 \n", time.Now().Sub(parsetime).Hours()/24)
			if days != -1 && (time.Now().Sub(parsetime).Hours()/24) > float64(days) {
				engineid := v.Get("_source.newtbinfo.engineid").String()
				dbname := v.Get("_source.finaltable.dbname").String()
				schema := v.Get("_source.finaltable.schema").String()
				if engineid != "" && dbname != "" && schema != "" {
					engineinfo, err := GetInstance(engineid)
					if err != nil {
						logger.Error.Println(err)
					}
					if engineinfo.Databasetype == "stork" || engineinfo.Databasetype == "teryx" ||
						engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM ||
						engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL {
						engine, err := PgConnect(engineinfo.User, engineinfo.Passwd, dbname, engineinfo.Ip, engineinfo.Port)
						if err != nil {
							logger.Error.Println(err)
						}
						_, err = engine.Exec("drop table if exists " + schema + "." + "\"" + subjecttablename + "\"")
						if err != nil {
							logger.Error.Println(err)
						}
					} else if engineinfo.Databasetype == "hive" || engineinfo.Databasetype == common.Inceptor {
						conn, err := PrestoConnect(engineinfo, dbname)
						if err != nil {
							logger.Error.Println(err)
						}
						_, err = conn.Query("drop table if exists " + subjecttablename)
						if err != nil {
							logger.Error.Println(err)
						}
					}
				}
			}
		}
	}
}

func JudgeSubmit(id string) (bool, error) {
	url := fmt.Sprintf("/%s/%s/%s", common.DataBaseJob, common.TbJob, id)
	r, err := httpclient.Get(url, "")
	if err != nil {
		logger.Error.Println(err)
		return false, err
	}
	status := gjson.Get(string(r), "_source.label.submitted").Bool()
	return status, nil
}

func TimefieldJudge(info source.TbJson) (bool, error, string) {
	engineinfo, _ := GetInstance(info.Engineid)
	info.Fieldtype = strings.ToLower(info.Fieldtype) //转为全小写，适配达梦
	if engineinfo.Databasetype == common.Stork || engineinfo.Databasetype == common.Teryx || engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.DMDB || engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL {

		// 对于 date 和timestamp类型直接判断可以作为增量条件
		if strings.Contains(info.Fieldtype, "date") || strings.Contains(info.Fieldtype, "timestamp") {
			return true, nil, ""
		} else if strings.Contains(info.Fieldtype, "varchar") || strings.Contains(info.Fieldtype, "char") || strings.Contains(info.Fieldtype, "text") || strings.Contains(info.Fieldtype, "clob") {
			querytxt := fmt.Sprintf(`select "%s" from "%s"."%s" limit 10`, info.Field, info.Schema, info.Tbname)
			res, err := MgExec(engineinfo.Engineid, querytxt, info.Dbname)
			if err != nil {
				logger.Error.Println(err)
				return false, err, ""
			}

			record := res.Values
			for _, v := range record {
				if v[info.Field] == nil {
					continue
				}
				success, format := JudgeDateFormat(v[info.Field].(string))
				//reg, _ := regexp.Compile(`^\d{4}([.\-/]?\d{1,2}){1,2}`)
				//if reg.MatchString(v[info.Field].(string)) {
				if success {
					return true, nil, format
				} else {
					//return false, nil, ""
				}
			}
			return false, nil, ""

		} else {
			return false, nil, ""
		}

	}
	if engineinfo.Databasetype == "hive" || engineinfo.Databasetype == common.Inceptor {

		// 对于 date 和timestamp类型直接判断可以作为增量条件
		if strings.Contains(info.Fieldtype, "date") || strings.Contains(info.Fieldtype, "timestamp") {
			return true, nil, ""
		} else if strings.Contains(info.Fieldtype, "varchar") || strings.Contains(info.Fieldtype, "char") || strings.Contains(info.Fieldtype, "varbinary") || strings.Contains(info.Fieldtype, "json") || strings.Contains(info.Fieldtype, "string") {
			querytxt := fmt.Sprintf(`select %s from "%s"."%s" limit 10`, info.Field, info.Dbname, info.Tbname)
			res, err := MgExec(engineinfo.Engineid, querytxt)
			if err != nil {
				logger.Error.Println(err)
				return false, err, ""
			}

			record := res.Values
			for _, v := range record {
				if v[info.Field] == nil {
					continue
				}
				success, format := JudgeDateFormat(v[info.Field].(string))
				//reg, _ := regexp.Compile(`^\d{4}([.\-/]?\d{1,2}){1,2}`)
				//if reg.MatchString(v[info.Field].(string)) {
				if success {
					return true, nil, format
				} else {
					//return false, nil, ""
				}
			}
			return false, nil, ""

		} else {
			return false, nil, ""
		}
	}
	return false, nil, ""
}

func getCurBash() string {
	var res string
	curEng, _ := tools.CurEngine()
	switch curEng {
	case common.EngPresto, "":
		res = common.HIVE_BASH
	case common.EngSpark:
		res = common.SparkBash
	default:
		res = common.HIVE_BASH
	}
	return res
}

func dateFormat(p string) string {
	switch p {
	case "hour":
		return "yyyy-MM-dd HH"
	case "day":
		return "yyyy-MM-dd"
	case "month":
		return "yyyy-MM"
	default:
		return "yyyy-MM-dd"
	}
}

func dateAdd(p string, n int, d string) string { //d 为 timestamp 类型的 spark sql 表达式
	unixTime := fmt.Sprintf(`to_unix_timestamp(%s)`, d)
	var diff int
	pHour := 60 * 60
	pDay := pHour * 24
	pMonth := pDay * 30
	switch p {
	case "hour":
		diff = pHour
	case "day":
		diff = pDay
	case "month":
		diff = pMonth
	}
	res := fmt.Sprintf(`from_unixtime(%s+%d,'yyyy-MM-dd HH:mm:ss')`, unixTime, diff)
	return res

}

// 预览版 修改表类型
// TODO 如果表不存在的话，修改表类型 应该如何确定。。。。。。。。。。。。。。。。。。。
// 无 spark
func ChangeTableType(sou source.TbJson) (er error) {

	bodyid := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": sou.AITableType.ID,
					},
				},
			},
		},
	}
	byteBody, _ := json.Marshal(bodyid)

	url := fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.TbOdsTableInfo)
	res, err := httpclient.Post(url, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return nil
	}
	e := gjson.Get(string(res), "hits.hits").Array()
	cnt := gjson.Get(string(res), "hits.total").Int()

	if cnt == 0 {
		body := gin.H{
			"storagesource": sou.Dbname,
			"tablename":     sou.Tbname,
			"isaiflag":      "0",
			"aitabletype":   sou.AITableType.TableType,
			"createtime":    time.Now().Format("2006-01-02 15:04:05"), // 创建时间
		}
		id, err := httpclient.EAdd(common.DataBaseAssert, common.TbOdsTableInfo, body)
		fmt.Println("insert:", id)
		if err != nil {
			logger.Error.Println(err)
			return err
		}

	} else {
		aitype := e[0].Get("_source.aitabletype").String()
		flg := ""
		if aitype != sou.AITableType.TableType {
			flg = "0"
		} else {
			flg = "1"
		}

		var body = gin.H{
			"doc": gin.H{
				"aitabletype": sou.AITableType.TableType,
				"isaiflag":    flg,
			},
		}
		byteBody1, _ := json.Marshal(body)
		uri := fmt.Sprintf("/%s/%s/%s/_update", common.DataBaseAssert, common.TbOdsTableInfo, sou.AITableType.ID)
		_, err = httpclient.Post(uri, string(byteBody1))
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	}

	return nil
}

// 无 spark
func GetDimTables(dimtabs source.GetDimTablesInfo) (re []string) {
	var musts []interface{}
	must := gin.H{
		"term": gin.H{
			"storagesource": dimtabs.Dbname,
		},
	}
	must1 := gin.H{
		"term": gin.H{
			"tablename": dimtabs.Tbname,
		},
	}
	musts = append(musts, must, must1)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
	}
	uri := fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.TbOdsTableInfo)
	byteBody, _ := json.Marshal(body)
	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
	}
	js := gjson.Get(string(response), "hits.hits").Array()
	var res []string
	for _, m := range js {
		dimtables := m.Get("_source.colinfo").Array()
		for _, j := range dimtables {
			if j.Get("colname").String() == dimtabs.Colname {
				for _, m := range j.Get("aidimtables").Array() {
					res = append(res, m.String())
				}
			}
		}
	}
	return res
}

func GetDimData(sou source.GetDimTablesInfo) (tb source.Tbinfo, er error) {
	engine, err := tools.CurEngine()
	fmt.Println("引擎：engine", engine)
	if err != nil {
		fmt.Println("AI无可用查询引擎！")
	}
	var tbinfo source.Tbinfo

	//engine = "spark"

	switch engine {
	case common.EngPresto:
		var sp source.InstanceInfo
		conn, err := PrestoConnect(sp, sou.Dbname)
		if err != nil {
			fmt.Println("Presto连接失败：", err.Error())
			return
		}
		sqltxt := "select * from " + "\"" + sou.Tbname + "\"" + " limit 1"
		fmt.Println(sqltxt)
		res, err := conn.Query(sqltxt)

		if err != nil {
			logger.Error.Println(err)
			return tbinfo, err
		}
		fields, err := res.Columns()
		fmt.Println("fields:  ", fields)

		if err != nil {
			logger.Error.Println(err)
			return tbinfo, err
		}
		tbinfo.Field = fields
		typeinfos, err := res.ColumnTypes()
		if err != nil {
			logger.Error.Println(err)
			return tbinfo, err
		}
		var fieldtype []string
		for _, v := range typeinfos {
			fieldtype = append(fieldtype, v.DatabaseTypeName())
		}

		tbinfo.Fieldtype = fieldtype
		record, err := sqlrows2Maps(res)
		if err != nil {
			logger.Error.Println(err)
			return tbinfo, err
		}

		tbinfo.Fieldfirst, err = Recordget(1, fields, record)
		if err != nil {
			return tbinfo, err
		}

		var comments []string
		res, err = conn.Query("desc " + `"` + sou.Tbname + `"`)
		if err != nil {
			logger.Error.Println(err)
			return tbinfo, err
		}
		record, err = sqlrows2Maps(res)
		if err != nil {
			logger.Error.Println(err)
			return tbinfo, err
		}
		for _, v := range record {
			comments = append(comments, v["Comment"].(string))
		}
		tbinfo.Fieldcomment = comments
		_ = conn.Close()

	case common.EngSpark:
		fmt.Println("进入spark")
		sqltxt := fmt.Sprintf("desc %s.%s", sou.Dbname, sou.Tbname)
		re, err := SparkExec(sqltxt)
		if err != nil {
			return tbinfo, err
		}
		des := SparkDescTable(re.Values)
		tbinfo.Field = des.Columnname
		tbinfo.Fieldtype = des.Columntype
		tbinfo.Fieldcomment = des.Columncomment

		re, err = SparkExec(fmt.Sprintf("select * from %s.%s limit 1", sou.Dbname, sou.Tbname))
		if err != nil {
			return tbinfo, err
		}
		//fmt.Printf("查询结果打印%v", re)
		tbinfo.Fieldfirst, err = Recordget(1, des.Columnname, re.Values)
		if err != nil {
			return tbinfo, err
		}
	}

	return tbinfo, nil
}

func GetDimDataList(info source.DimDataList) (dt []map[string]interface{}, er error) {
	engine, err := tools.CurEngine()
	fmt.Println("引擎：engine", engine)
	if err != nil {
		fmt.Println("AI无可用查询引擎！")
	}

	// 更新 ES的代码表的的字段
	var musts []interface{}
	must1 := gin.H{
		"term": gin.H{
			"storagesource": info.Dbname,
		},
	}
	must2 := gin.H{
		"term": gin.H{
			"tablename": info.Tbname,
		},
	}
	musts = append(musts, must1, must2)
	bodyid := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
	}
	byteBody, _ := json.Marshal(bodyid)

	url := fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.TbOdsTableInfo)
	res, err := httpclient.Post(url, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
	}
	e := gjson.Get(string(res), "hits.hits").Array()
	id := e[0].Get("_source.id").String()

	var body = gin.H{
		"doc": gin.H{
			"dimcolname":    info.DimCol,
			"dimvalcolname": info.DimValCol,
			"dimflag":       "1",
		},
	}
	byteBody1, _ := json.Marshal(body)
	uri := fmt.Sprintf("/%s/%s/%s/_update", common.DataBaseAssert, common.TbOdsTableInfo, id)
	_, err = httpclient.Post(uri, string(byteBody1))
	if err != nil {
		logger.Error.Println(err)
	}

	var rowMaps []map[string]interface{}
	//engine = "spark"
	switch engine {
	case common.EngPresto:
		var sp source.InstanceInfo
		conn, err := PrestoConnect(sp, info.Dbname)
		if err != nil {
			fmt.Println("presto 不可用:", err.Error())
		}
		sqltxt := fmt.Sprintf("select %s as dimcol,%s as dimvalcol from %s", info.DimCol, info.DimValCol, info.Tbname)
		rest, err := conn.Query(sqltxt)
		rowMaps, err = sqlrows2Maps(rest)
		return rowMaps, err

	case common.EngSpark:
		sqltxt := fmt.Sprintf("select %s as dimcol,%s as dimvalcol from %s.%s", info.DimCol, info.DimValCol, info.Dbname, info.Tbname)
		rest, err := SparkExec(sqltxt)
		rowMaps = rest.Values
		return rowMaps, err
	}
	return rowMaps, nil
}

func GetTableTypeByDBandTB(db string, tb string) (tp string, id string) {

	var musts []interface{}
	must1 := gin.H{
		"term": gin.H{
			"storagesource": db,
		},
	}
	must2 := gin.H{
		"term": gin.H{
			"tablename": tb,
		},
	}
	musts = append(musts, must1, must2)

	bodyid := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
	}
	byteBody, _ := json.Marshal(bodyid)

	url := fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.TbOdsTableInfo)
	res, err := httpclient.Post(url, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return "", ""
	}
	e := gjson.Get(string(res), "hits.hits").Array()
	if gjson.Get(string(res), "hits.total").Int() == 1 {
		aitype := e[0].Get("_source.aitabletype").String()
		id := e[0].Get("_source.id").String()
		return aitype, id
	}
	return "", ""
}

func ListBasedb(info source.Extra) ([]string, error) {
	var dbname []string
	var term interface{}
	if info.Module == 5 {
		term = gin.H{
			"colornum": 1100,
		}
	} else {
		term = gin.H{
			"colornum": 1011,
		}
	}
	var body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": term,
				},
			},
		},
	}
	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/danastudio-asset/db_layer/_search")
	res, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return dbname, err
	}
	layerid := gjson.Get(string(res), "hits.hits.0._source.id").String()

	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"layerid": layerid,
					},
				},
			},
		},
	}
	byteBody, _ = json.Marshal(body)

	uri = fmt.Sprintf("/danastudio-asset/db_newasset/_search")
	r, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return dbname, err
	}
	e := gjson.Get(string(r), "hits.hits").Array()
	for _, v := range e {
		dbname = append(dbname, v.Get("_source.databasename").String())
	}

	dbname = tools.DeleteSlice("wt", dbname)
	return dbname, nil
}

func JudgeDateFormat(datestr string) (b bool, format string) {
	//const (
	//	datetime1   = "2006-01-02 15:04:05"
	//	datetime2   = "2006/01/02 15:04:05"
	//	datetime3   = "20060102150405"
	//	date1      = "2006-01-02"
	//	date2       = "2006/01/02"
	//	date3       = "20060102"
	//)
	dt := map[string]string{
		"yyyy-MM-dd HH:mm:ss": "2006-01-02 15:04:05",
		"yyyy/MM/dd HH:mm:ss": "2006/01/02 15:04:05",
		"yyyyMMddHHmmss":      "20060102150405",
		"yyyy-MM-dd":          "2006-01-02",
		"yyyy/MM/dd":          "2006/01/02",
		"yyyyMMdd":            "20060102",
	}

	for key, value := range dt {
		if _, err := time.Parse(value, datestr); err == nil {
			return true, key
		}
	}
	return false, ""
}

func JudgeTbExist(info source.OutputSave, engineinfo source.InstanceInfo, initDBName string) (bool, bool, error) { //第一个bool:存在，第二个:新建
	var fieldname []string
	var fieldtype []string

	////info字段加上pt
	//var pt source.FieldsInfo
	//pt.Newfieldname = "pt"
	//pt.Newfieldtype = "timestamp"
	//info.Fieldinfo = append(info.Fieldinfo, pt)

	//先判断表是否存在
	if engineinfo.Databasetype == "stork" || engineinfo.Databasetype == common.Teryx || engineinfo.Databasetype == common.GaussDB || engineinfo.Databasetype == common.UXDB || engineinfo.Databasetype == common.DMDB || engineinfo.Databasetype == common.POSTGRES || engineinfo.Databasetype == common.GREENPLUM || engineinfo.Databasetype == common.GaussDBA || engineinfo.Databasetype == common.TDSQL {
		//sqltxt := "select * from " + info.Finaltable.Schema + "." + "\"" + info.Finaltable.Tbname + "\"" + " limlt 1"
		var sqltxt, count string
		if engineinfo.Databasetype == common.DMDB { //达梦的 sql 单独处理
			sqltxt = fmt.Sprintf("select count(*) as count from all_tables where owner='%s' and TABLE_NAME='%s'", info.Finaltable.SourceDB, info.Finaltable.Tbname)
		} else {
			sqltxt = fmt.Sprintf("select count(*) from pg_stat_user_tables where schemaname = '%s' and relname = '%s'", info.Finaltable.SourceDB, info.Finaltable.Tbname)
		}

		res, err := MgExec(engineinfo.Engineid, sqltxt, initDBName)
		if err != nil {
			logger.Error.Println(err)
			return false, false, err
		}

		if engineinfo.Databasetype == common.DMDB { //达梦的 sql 单独处理
			count = fmt.Sprintf("%v", res.Values[0]["COUNT"]) //count:1表存在 0表不存在
		} else {
			count = fmt.Sprintf("%v", res.Values[0]["count"]) //count:1表存在 0表不存在
		}

		if count == "1" {
			//表存在再进行表结构的比对
			tbdesc, err := MgTableDesc(engineinfo.Engineid, initDBName, info.Finaltable.SourceDB, info.Finaltable.Tbname)
			if err != nil {
				logger.Error.Println(err)
				return false, false, err
			}
			fieldname = tbdesc.Columnname
			fieldtype = tbdesc.Columntype
			var isChange bool
			if len(info.Changedinfo) == len(fieldname) {
				//字段数量相同再进行字段名及字段类型的比较
				for i := 0; i < len(info.Changedinfo); i++ {
					if info.Changedinfo[i].Fieldname != fieldname[i] || info.Changedinfo[i].Fieldtype != fieldtype[i] {
						//return true, true, nil
						isChange = true
					}
				}
			} else {
				//return true, true, nil
				isChange = true
			}
			//获取主键
			var oldTbInfo source.MGResTB
			uri := fmt.Sprintf(`/meta-gateway/table/metadata?datasourceId=%s&databaseName=%s&schemaName=%s&tableName=%s`, engineinfo.Engineid+initDBName, initDBName, info.Finaltable.SourceDB, info.Finaltable.Tbname)
			body := "[]"
			res, err := collect.MGGET(uri, body)
			if err != nil {
				logger.Error.Println(err, uri)
				return false, false, err
			}
			err = json.Unmarshal(res, &oldTbInfo)
			if err != nil {
				logger.Error.Println(err.Error())
				return false, false, err
			}
			var oldPrimary []string
			if len(oldTbInfo.TBInfo.TablePrimaryKeyInfo.PrimaryColsList) > 0 {
				for _, v := range oldTbInfo.TBInfo.TablePrimaryKeyInfo.PrimaryColsList {
					oldPrimary = append(oldPrimary, v.Name)
				}
			}
			if len(oldPrimary) == len(info.OutputPrimkeys) {
				for k, v := range info.OutputPrimkeys {
					if v != oldPrimary[k] {
						isChange = true
					}
				}
			} else {
				isChange = true
			}
			//分布方式
			var oldDisMode string
			var oldDisBond []string
			oldDisMode = strings.ToUpper(oldTbInfo.TBInfo.TableDistributeInfo.DisType)
			for k := range oldTbInfo.TBInfo.TableDistributeInfo.DisColsList {
				oldDisBond = append(oldDisBond, oldTbInfo.TBInfo.TableDistributeInfo.DisColsList[k].Name)
			}
			if oldDisMode == info.DistributionMode {
				if len(oldDisBond) == len(info.DistributionBond) {
					for k, v := range info.DistributionBond {
						if v != oldDisBond[k] {
							isChange = true
						}
					}
				} else {
					isChange = true
				}
			} else {
				isChange = true
			}
			if isChange {
				return true, true, nil
			} else {
				return true, false, nil
			}
		}
	} else if engineinfo.Databasetype == "hive" || engineinfo.Databasetype == common.Inceptor {
		if info.Finaltable.Dbname == "" {
			info.Finaltable.Dbname = common.DWDDB
		}
		sqltxt := "show tables in " + info.Finaltable.Dbname

		rets, err := MgExec(engineinfo.Engineid, sqltxt)
		if err != nil {
			logger.Error.Println(err)
			return false, false, err
		}
		for _, v := range rets.Values {
			if v["tab_name"] == info.Finaltable.Tbname {
				//表存在再进行表结构的比对
				sqltxt = fmt.Sprintf(`desc %s.%s`, info.Finaltable.Dbname, info.Finaltable.Tbname)
				re, err := MgExec(engineinfo.Engineid, sqltxt)
				if err != nil {
					logger.Error.Println(err)
					return false, false, err
				}
				tbdesc := MgHiveDesc(re.Values)
				fieldname = tbdesc.Columnname
				fieldtype = tbdesc.Columntype
				if len(info.Changedinfo) == len(fieldname) {
					//字段数量相同再进行字段名及字段类型的比较
					for i := 0; i < len(info.Changedinfo); i++ {
						if info.Changedinfo[i].Fieldname != fieldname[i] || info.Changedinfo[i].Fieldtype != fieldtype[i] {
							return true, true, nil
						}
					}
				} else {
					var pt source.Changedfield
					pt.Fieldname = "pt"
					pt.Fieldtype = "timestamp"
					info.Changedinfo = append(info.Changedinfo, pt)
					if len(info.Changedinfo) == len(fieldname) {
						//字段数量相同再进行字段名及字段类型的比较
						for i := 0; i < len(info.Changedinfo); i++ {
							if info.Changedinfo[i].Fieldname != fieldname[i] || info.Changedinfo[i].Fieldtype != fieldtype[i] {
								return true, true, nil
							}
						}
					} else {
						return true, true, nil
					}
				}
				return true, false, nil
			}
		}
	}

	return false, true, nil
}

// v4.5.1 更新 AI 标识 原始代码表 对应 标准代码表
func UpdateDimStdID(sp source.ProWatchTableInfo) (su bool, msg error) {
	colarr := sp.StandardFieldinfos
	// 循环每个字段
	for i := 0; i < len(colarr); i++ {
		// if aidiminfo 的 tbname 非空表示 做了标准 映射
		if colarr[i].AIDimInfo.Tbname != "" {
			stdcodeid := colarr[i].Fieldaddcolumn[0].Id
			_, id := GetTableTypeByDBandTB(colarr[i].AIDimInfo.Dbname, colarr[i].AIDimInfo.Tbname)
			var body = gin.H{
				"doc": gin.H{
					"stdcodeid": stdcodeid,
				},
			}
			byteBody1, _ := json.Marshal(body)
			uri := fmt.Sprintf("/%s/%s/%s/_update", common.DataBaseAssert, common.TbOdsTableInfo, id)
			_, err := httpclient.Post(uri, string(byteBody1))
			if err != nil {
				return false, fmt.Errorf("DB：%s,TB:%s,stdcodeid：%s ,更新到ES失败，失败信息：%s ",
					colarr[i].AIDimInfo.Dbname, colarr[i].AIDimInfo.Tbname, id, err.Error())
			}
		}
	}
	return true, nil
}

func GetStdCodeID(db string, tb string) (stdid string) {

	var musts []interface{}
	must1 := gin.H{
		"term": gin.H{
			"storagesource": db,
		},
	}
	must2 := gin.H{
		"term": gin.H{
			"tablename": tb,
		},
	}
	musts = append(musts, must1, must2)

	bodyid := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": musts,
			},
		},
	}
	byteBody, _ := json.Marshal(bodyid)

	url := fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.TbOdsTableInfo)
	res, err := httpclient.Post(url, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return ""
	}
	e := gjson.Get(string(res), "hits.hits").Array()
	if gjson.Get(string(res), "hits.total").Int() == 1 {
		stdcodeid := e[0].Get("_source.stdcodeid").String()
		return stdcodeid
	}
	return ""
}

func AddTable(info source.OutputSave, needDel, needNew bool, lab source.ProDevInfo, engineType string) (string, error) {
	//需要底层建表
	var TBID string //元数据表ID
	initDBName := lab.Newtbinfo.Dbname
	if engineType == common.Hive {
		info.Finaltable.Schema = common.EmptyString
	} else {
		info.Finaltable.Schema = info.Finaltable.SourceDB
		//info.Finaltable.Dbname = initDBName
	}

	info.Finaltable.Describe = dbutil.CommentFormat(info.Finaltable.Describe)
	if needNew {
		if needDel {
			//删除表

			switch engineType {
			case common.Hive:
				delSQL := fmt.Sprintf("drop table if exists `%s.%s`", info.Finaltable.Dbname, info.Finaltable.Tbname)
				_, err := MgExec(lab.Newtbinfo.Engineid, delSQL)
				if err != nil {
					logger.Error.Println(err)
					return TBID, err
				}

				delSQL = fmt.Sprintf("drop table if exists `%s.%s_tmp_testrun`", info.Finaltable.Dbname, info.Finaltable.Tbname)
				_, err = MgExec(lab.Newtbinfo.Engineid, delSQL)
				if err != nil {
					logger.Error.Println(err)
					return TBID, err
				}
			case common.DMDB:
				delSQL := fmt.Sprintf("drop table if exists \"%s\".\"%s\"", info.Finaltable.SourceDB, info.Finaltable.Tbname)
				_, err := MgExec(lab.Newtbinfo.Engineid, delSQL, initDBName)
				if err != nil {
					logger.Error.Println(err)
					return TBID, err
				}

				delSQL = fmt.Sprintf("drop table if exists \"%s\".\"%s_tmp_testrun\"", info.Finaltable.SourceDB, info.Finaltable.Tbname)
				_, err = MgExec(lab.Newtbinfo.Engineid, delSQL, initDBName)
				if err != nil {
					logger.Error.Println(err)
					return TBID, err
				}
			default: //pg 系的
				delSQL := fmt.Sprintf("drop table if exists \"%s\".\"%s\".\"%s\"", info.Finaltable.Dbname, info.Finaltable.SourceDB, info.Finaltable.Tbname)
				_, err := MgExec(lab.Newtbinfo.Engineid, delSQL, initDBName)
				if err != nil {
					logger.Error.Println(err)
					return TBID, err
				}

				delSQL = fmt.Sprintf("drop table if exists \"%s\".\"%s\".\"%s_tmp_testrun\"", info.Finaltable.Dbname, info.Finaltable.SourceDB, info.Finaltable.Tbname)
				_, err = MgExec(lab.Newtbinfo.Engineid, delSQL, initDBName)
				if err != nil {
					logger.Error.Println(err)
					return TBID, err
				}
			}

			//删除元数据es数据
			var must []map[string]interface{}
			term1 := gin.H{
				"term": gin.H{
					"engineid": lab.Newtbinfo.Engineid,
				},
			}
			term2 := gin.H{
				"term": gin.H{
					"dbname": initDBName,
				},
			}
			term3 := gin.H{
				"term": gin.H{
					"tbname": info.Finaltable.Tbname,
				},
			}
			term4 := gin.H{
				"term": gin.H{
					"sourcedb": info.Finaltable.SourceDB,
				},
			}
			term5 := gin.H{
				"term": gin.H{
					"projectid": lab.ProjectID,
				},
			}
			must = append(must, term1, term2, term3, term4, term5)
			body := gin.H{
				"query": gin.H{
					"bool": gin.H{
						"must": must,
					},
				},
			}
			fmt.Println("must:", body)
			err := escli.NewESClientCl.DelByQuery(common.DataBaseMetaData, common.TbMetaTable, body)
			if err != nil {
				logger.Error.Println(err)
				return TBID, err
			}
		}
		var disMode string
		var disBond []string
		//建表
		fmt.Println("--分布信息--")
		fmt.Println(info.DistributionMode, info.DistributionBond)
		if info.Finaltable.TbType == common.Hive || info.Finaltable.TbType == common.Inceptor { //hive
			values := fmt.Sprintf(``)
			for _, v := range info.Changedinfo {
				//去除分区键
				var isPar bool
				for _, vv := range info.PartitionField {
					if v.Fieldname == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					v.Fielddescribe = dbutil.CommentFormat(v.Fielddescribe)
					values = fmt.Sprintf("%s `%s` %s comment'%s',", values, v.Fieldname, v.Fieldtype, tools.QouteReplace(v.Fielddescribe))
				}
			}
			values = strings.TrimRight(values, ",")
			var fileformat string
			if info.Finaltable.FileFormat == "Orcfile" {
				fileformat = "orc"
			} else {
				fileformat = "textfile"
			}
			//分区键sql
			partitionSql := "partitioned by"
			var partitionFieldSql string
			for _, v := range info.PartitionField {
				if v.Ispreset == "notpreset" {
					partitionFieldSql = fmt.Sprintf("%s, `%s` %s COMMENT '%s'", partitionFieldSql, v.Name, v.FieldType, tools.QouteReplace(v.Annotation))
				} else {
					partitionFieldSql = fmt.Sprintf("%s, `%s` %s", partitionFieldSql, v.Name, v.FieldType)
				}
			}
			if len(partitionFieldSql) > 0 {
				partitionSql = fmt.Sprintf(`%s (%s) `, partitionSql, partitionFieldSql[2:len(partitionFieldSql)])
			} else {
				partitionSql = ""
			}

			buildSQL := fmt.Sprintf(`use %s; create table %s.%s(%s) %s stored as %s; alter table %s.%s set tblproperties('comment' = '%s')`,
				info.Finaltable.Dbname, info.Finaltable.Dbname, info.Finaltable.Tbname, values, partitionSql, fileformat, info.Finaltable.Dbname, info.Finaltable.Tbname, info.Finaltable.Describe)

			_, err := MgExec(lab.Newtbinfo.Engineid, buildSQL)
			if err != nil {
				logger.Error.Println(err)
				return TBID, err
			}
			//试运行-临时表
			//finaltbname := `"` + info.Finaltable.SourceDB + `"."` + info.Finaltable.Tbname + `"`
			//finaltbname_tmp_testrun := `"` + info.Finaltable.SourceDB + `"."` + info.Finaltable.Tbname + "_tmp_testrun" + `"`
			//buildSQL_test := strings.Replace(buildSQL, finaltbname, finaltbname_tmp_testrun, -1)
			// buildSQL_test := strings.Replace(buildSQL, info.Finaltable.Tbname, info.Finaltable.Tbname+"_tmp_testrun", -1)
			// logger.Info.Println("-----buildSQL_test--------", buildSQL_test)
			// _, err = MgExec(lab.Newtbinfo.Engineid, buildSQL_test)
			// if err != nil {
			// 	logger.Error.Println(err)
			// 	return TBID, err
			// }
		} else { //非hive
			describeSQL := fmt.Sprintf(``)
			var distributedSql, fieldSql string
			var primField []string
			//获取非主键时建表的第一个字段
			var firstField string
			for i, v := range info.Changedinfo {
				if v.Fielddescribe != "" {
					v.Fielddescribe = dbutil.CommentFormat(v.Fielddescribe)
					describeSQL = fmt.Sprintf(`%sCOMMENT ON COLUMN "%s"."%s"."%s" IS '%s';`,
						describeSQL, info.Finaltable.SourceDB, info.Finaltable.Tbname, v.Fieldname, v.Fielddescribe)
				}
				if i == 0 {
					fieldSql = fmt.Sprintf(`"%s" %s`, v.Fieldname, v.Fieldtype)
					firstField = v.Fieldname
					continue
				}
				fieldSql = fmt.Sprintf(`%s, "%s" %s`, fieldSql, v.Fieldname, v.Fieldtype)
			}

			//添加主键
			// 判定主键
			//isPrimaru 记录主键
			var primkeySQL string
			var isPrimary = false
			keyfield := []string{}
			keyfield = info.OutputPrimkeys //主键列表
			if len(keyfield) > 0 {
				isPrimary = true
			}

			if info.Finaltable.TbType == common.DMDB { //达梦的主键单独处理
				var keystr string
				if len(keyfield) != 0 {
					for i, v := range keyfield {
						if i == 0 {
							keystr = fmt.Sprintf(`"%s" `, v)
							continue
						}
						keystr = fmt.Sprintf(`%s, "%s" `, keystr, v)
					}
					primkeySQL = fmt.Sprintf(`CLUSTER PRIMARY KEY(%s) `, keystr)
				}
				if keystr != "" { //keystr 不为空时，primkeySQL 也一定不为空
					fieldSql = fmt.Sprintf("%s, %s", fieldSql, primkeySQL)
				}
			} else {
				if len(keyfield) != 0 {
					keys := ""
					pks := ""
					for i, v := range keyfield {
						if i == 0 {
							keys = fmt.Sprintf(`"%s"`, v)
							pks = v
							continue
						}
						keys = fmt.Sprintf(`%s,"%s"`, keys, v)
						pks = fmt.Sprintf("%s_%s", pks, v)
					}
					pks = fmt.Sprintf("%s_%s", info.Finaltable.Tbname, pks)
					primkeySQL = fmt.Sprintf("Constraint pk_%s Primary Key(%s)", pks, keys)
				}
				if primkeySQL != "" {
					fieldSql = fmt.Sprintf("%s, %s", fieldSql, primkeySQL)
				}
			}

			//判断分布键是否在主键中
			var isExist bool
			if len(primField) > 0 {
				for _, f1 := range info.DistributionBond {
					for _, f2 := range primField {
						if f1 == f2 {
							isExist = true
						}
					}
					if !isExist {
						logger.Error.Println("分布键未在主键中")
						return TBID, fmt.Errorf("分布键未在主键中")
					}
					isExist = false
				}
			}

			var createSql string
			if info.Finaltable.TbType == common.DMDB {
				createSql = fmt.Sprintf(`create table "%s"."%s"`, info.Finaltable.SourceDB, info.Finaltable.Tbname)
			} else {
				createSql = fmt.Sprintf(`create table %s."%s"`, info.Finaltable.SourceDB, info.Finaltable.Tbname)
			}

			//初始化
			disMode = ""
			disBond = []string{}

			// lab.Newtbinfo.Engineid
			eng, _ := dbutil.EngineInfo(lab.Newtbinfo.Engineid)
			var uxSql string
			switch info.Finaltable.TbType {
			case common.Teryx, common.GaussDB, common.GREENPLUM, common.GaussDBA:
				switch info.DistributionMode {
				case common.HASH:
					if strings.Contains(eng.HiveVersion, "GaussDB") {
						distributedSql = "DISTRIBUTE BY "
					} else {
						distributedSql = "DISTRIBUTED BY"
					}
					disMode = info.DistributionMode
					disSql := fmt.Sprintf(``)
					if len(info.DistributionBond) > 0 {
						disBond = info.DistributionBond
						for _, v := range info.DistributionBond {
							disSql = fmt.Sprintf(`%s "%s" ,`, disSql, v)
						}
						if strings.Contains(eng.HiveVersion, "GaussDB") {
							distributedSql = fmt.Sprintf("%s HASH(%s)", distributedSql, disSql[0:len(disSql)-1])
						} else {
							distributedSql = fmt.Sprintf(` %s (%s)`, distributedSql, disSql[0:len(disSql)-1])
						}

					} else {
						logger.Error.Println("分布键数量为0")
						return TBID, fmt.Errorf("分布键数量为0")
					}
				case common.RANDOMLY:
					disMode = info.DistributionMode
					disBond = []string{}
					if !isPrimary {
						distributedSql = "DISTRIBUTED RANDOMLY"
					} else {
						return TBID, fmt.Errorf("该表存在主键，不可使用随机分布创建表")
					}
				case common.REPLICATED:
					disMode = info.DistributionMode
					disBond = []string{}
					if strings.Contains(eng.HiveVersion, "GaussDB") {
						distributedSql = "DISTRIBUTE BY REPLICATION "
					} else {
						distributedSql = "DISTRIBUTED REPLICATED"
					}

				default:
					//logger.Error.Println("无分布方式信息")
					//return fmt.Errorf("无分布方式信息")
					logger.Info.Println("未确认分布方式,采用默认方式")
					distributedSql = ""
					disMode = common.HASH
					if !isPrimary {
						disBond = append(disBond, firstField)
					} else {
						for _, v := range keyfield {
							disBond = append(disBond, v)
						}
						//fmt.Println("测试分布键的值:", req.DistributionBond)
					}
				}
			case common.UXDB:
				if len(info.DistributionBond) > 1 {
					logger.Error.Println("UXDB的分布键有且只能是一个")
					return TBID, fmt.Errorf("UXDB的分布键有且只能是一个")
				}
				switch info.DistributionMode {
				case common.HASH:
					fieldname := info.DistributionBond[0]
					fmt.Println("bond : ", info.DistributionBond[0])
					disMode = info.DistributionMode
					disBond = append(disBond, info.DistributionBond[0])
					uxSql = fmt.Sprintf(`SELECT create_distributed_table ('"%s"."%s"','%s','hash');`, info.Finaltable.SourceDB, info.Finaltable.Tbname, fieldname)
				case common.APPEND:
					logger.Error.Print("UX不支持append分布方式，请修改为hash分布方式进行创建")
					return TBID, fmt.Errorf("UX不支持append分布方式，请修改为hash分布方式进行创建")
				default:
					uxSql = ""
					disMode = ""
					disBond = []string{}
				}
			case common.DMDB:
				var distributionBond string
				switch info.DistributionMode {
				case common.HASH:
					disMode = info.DistributionMode
					disBond = info.DistributionBond
					distributedSql = "DISTRIBUTED BY "
					//判断分布键是否为空
					if len(info.DistributionBond) == 0 {
						logger.Error.Println("分布键为空，创建HASH分布错误")
						return TBID, fmt.Errorf("分布键为空，创建HASH分布错误")
					}
					//DM的字段类型是否符合要求
					fieldTypes := []string{"blob", "clob", "image", "text", "longvarchar", "bit", "binary", "varbinary", "longvarbinary"}
					var fileTypeMaps = make(map[string]string)
					for _, fieldName := range info.DistributionBond {
						for _, fieldInfo := range info.Changedinfo {
							if fieldName == fieldInfo.Fieldname {
								fileTypeMaps[fieldName] = strings.ToLower(fieldInfo.Fieldtype)
							}
						}
					}
					for _, fieldType := range fileTypeMaps {
						for _, unFileType := range fieldTypes {
							if fieldType == unFileType {
								logger.Error.Println("不可以使用DM分布方式不支持的字段类型作为分布键")
								return TBID, fmt.Errorf("不可以使用DM分布方式不支持的字段类型作为分布键")
							}
						}
					}
					//是否存在主键并且分布键是否属于主键的子集
					if !isPrimary {
						for _, v := range info.DistributionBond {
							//distributionBond = distributionBond + v + ","
							distributionBond = fmt.Sprintf(`%s"%s",`, distributionBond, v)
						}
					} else {
						var isBeyond bool
						for _, vt := range info.DistributionBond {
							isBeyond = false
							for _, vname := range keyfield {
								if vt == vname {
									isBeyond = true
								}
							}
							if !isBeyond {
								fmt.Println("不可以使用不在主键子集内的分布键")
								return TBID, fmt.Errorf("不可以使用不在主键子集内的分布键")
							}
							//distributionBond = distributionBond + vt + ","
							distributionBond = fmt.Sprintf(`%s"%s",`, distributionBond, vt)
						}
					}
					distributedSql = fmt.Sprintf(`%s(%s)`, distributedSql, distributionBond[0:len(distributionBond)-1])
					//fmt.Println("distributionsql is ", distributionSql)
				case common.RANDOMLY:
					disBond = []string{}
					disMode = info.DistributionMode
					if !isPrimary {
						distributedSql = "DISTRIBUTED RANDOMLY"
					} else {
						//logger.Error.Println("该表存在主键，不可使用随机分布创建表")
						//fmt.Println("该表存在主键，不可使用随机分布创建表")
						return TBID, fmt.Errorf("该表存在主键，不可使用随机分布创建表")
					}
				case common.REPLICATED:
					disBond = []string{}
					disMode = info.DistributionMode
					distributedSql = "DISTRIBUTED FULLY"
					info.DistributionMode = common.FULLY
				case common.DEFAULT:
					distributedSql = ""
					info.DistributionMode = common.HASH
					disMode = info.DistributionMode
					if !isPrimary {
						info.DistributionBond = append(info.DistributionBond, firstField)
					} else {
						for _, v := range keyfield {
							info.DistributionBond = append(info.DistributionBond, v)
						}
						//fmt.Println("测试分布键的值:", req.DistributionBond)
					}
					disBond = info.DistributionBond
				default:
					logger.Info.Println("未确认分布方式,采用默认方式")
					//return sqltxt, fmt.Errorf("未确认分布方式，分布方式为:HAHS,RANDOMLY,REPLICATED,DEFAULT")
					distributedSql = ""
					disMode = common.RANDOMLY
					//if !isPrimary {
					//	req.DistributionBond = append(req.DistributionBond, firstField)
					//} else {
					//	for _, v := range keyfield {
					//		req.DistributionBond = append(req.DistributionBond, v)
					//	}
					//	//fmt.Println("测试分布键的值:", req.DistributionBond)
					//}
					disBond = []string{}
				}
			}
			//teryx 列存储 ZLIB压缩方式，压缩比为1
			var columnSql string
			if info.Orientation == "column" && (info.Finaltable.TbType == common.Teryx || info.Finaltable.TbType == common.GREENPLUM) {
				if info.CompressType == "" {
					info.CompressType = "zlib"
				}
				if info.CompressLevel == 0 {
					info.CompressLevel = 1
				}
				columnSql = fmt.Sprintf(`with (appendonly = true,orientation = %s,compresstype = %s,COMPRESSLEVEL = %d)`,
					info.Orientation, info.CompressType, info.CompressLevel)
			}

			commentSql := fmt.Sprintf(`comment on table "%s"."%s" is '%s'`, info.Finaltable.SourceDB, info.Finaltable.Tbname, info.Finaltable.Describe)
			var buildSQL string
			if info.Finaltable.TbType == common.Teryx || info.Finaltable.TbType == common.GREENPLUM {
				buildSQL = fmt.Sprintf(`%s(%s)%s %s;%s %s`, createSql, fieldSql, columnSql, distributedSql, describeSQL, commentSql)
			} else {
				buildSQL = fmt.Sprintf(`%s(%s)%s;%s %s`, createSql, fieldSql, distributedSql, describeSQL, commentSql)
			}
			if info.Finaltable.TbType == common.UXDB {
				buildSQL = fmt.Sprintf(`%s;%s`, buildSQL, uxSql)
			}
			_, err := MgExec(lab.Newtbinfo.Engineid, buildSQL, initDBName)
			if err != nil {
				logger.Error.Println(err)
				return TBID, err
			}
			//试运行-临时表
			// logger.Info.Println("-----buildSQL-------", buildSQL)
			// buildSQL_test := strings.Replace(buildSQL, info.Finaltable.Tbname, info.Finaltable.Tbname+"_tmp_testrun", -1)
			// logger.Info.Println("-----buildSQL_test-333-------", buildSQL_test)
			// _, err = MgExec(lab.Newtbinfo.Engineid, buildSQL_test, initDBName)
			// if err != nil {
			// 	logger.Error.Println(err)
			// 	return TBID, err
			// }
		}

		var err error
		//hive分区预设字段增加
		if len(info.PartitionField) > 0 {
			for _, v := range info.PartitionField {
				if v.Ispreset != "notpreset" {
					var f source.Changedfield
					f.Fieldname = v.Name
					f.Fieldtype = v.FieldType
					f.IsPartition = true
					info.Changedinfo = append(info.Changedinfo, f)

					// var field source.FieldsInfo
					// field.Newfieldname = v.Name
					// field.Newfieldtype = v.FieldType
					// field.IsPartition = true
					// info.Fieldinfo = append(info.Fieldinfo, field)
				}
			}
		}
		var MGParInfo source.PartitionInfos
		//初次新增表的时候的分区信息处理
		if len(MGParInfo.PartitionInfo.Fields) == 0 && len(info.PartitionField) > 0 {
			for _, v := range info.PartitionField {
				MGParInfo.PartitionInfo.Fields = append(MGParInfo.PartitionInfo.Fields, v.Name)
			}
		}
		//新增元数据es数据
		var metaTB source.MetaSourceTb

		metaTB.MetaTb.Created = time.Now().Format(common.TimeFormat)
		metaTB.MetaTb.DistributionMode = disMode
		metaTB.MetaTb.DistributionBond = disBond
		metaTB.MetaTb.Modified = metaTB.MetaTb.Created
		metaTB.MetaTb.Updated = metaTB.MetaTb.Created
		metaTB.MetaTb.Schema = info.Finaltable.Schema
		metaTB.MetaTb.LayerName = info.Finaltable.LayerName
		metaTB.MetaTb.LayerID = info.Finaltable.LayerID
		metaTB.MetaTb.EngineID = lab.Newtbinfo.Engineid
		metaTB.MetaTb.DbName = initDBName
		metaTB.MetaTb.SourceDb = info.Finaltable.SourceDB
		metaTB.MetaTb.SourceID = info.Finaltable.SourceID
		metaTB.MetaTb.DBType = engineType
		metaTB.MetaTb.FileFormat = info.Finaltable.FileFormat
		metaTB.MetaTb.TBName = info.Finaltable.Tbname
		metaTB.MetaTb.Chinese = info.Finaltable.Chinese
		metaTB.MetaTb.Describe = info.Finaltable.Describe
		metaTB.MetaTb.TBType = "未分类"
		metaTB.MetaTb.CatalogID = info.Finaltable.CatalogID
		metaTB.MetaTb.TagList = info.Finaltable.TagList
		metaTB.MetaTb.ProjectID = lab.ProjectID
		metaTB.MetaTb.Username = lab.Username
		metaTB.MetaTb.UserID = lab.User
		metaTB.MetaTb.PartitionField = info.PartitionField
		metaTB.MetaTb.PartitionInfo = MGParInfo.PartitionInfo
		metaTB.MetaTb.CompressType = info.CompressType
		metaTB.MetaTb.CompressLevel = info.CompressLevel
		metaTB.MetaTb.Orientation = info.Orientation
		//获取表底层存储位置
		metaTB.MetaTb.Location, _ = devcenter.TbLocation(lab.Newtbinfo.Engineid, info.Finaltable.Dbname, info.Finaltable.Tbname)

		primMap := make(map[string]bool)
		for _, p := range info.OutputPrimkeys {
			primMap[p] = true
		}
		//整合表字段信息
		var metaField source.MetaSourceFieldInfo
		for i := 0; i < len(info.Changedinfo); i++ {
			metaField.MetaFieldInfo.Number = i + 1
			metaField.MetaFieldInfo.Name = info.Changedinfo[i].Fieldname
			metaField.MetaFieldInfo.FieldType = info.Changedinfo[i].Fieldtype
			metaField.MetaFieldInfo.Annotation = info.Changedinfo[i].Fielddescribe
			metaField.MetaFieldInfo.IsPrim = primMap[metaField.MetaFieldInfo.Name]
			metaField.MetaFieldInfo.IsPartition = info.Changedinfo[i].IsPartition

			metaTB.MetaTb.FieldInfo = append(metaTB.MetaTb.FieldInfo, metaField.MetaFieldInfo)
		}

		TBID, err = esutil.AddSingleWithRefresh(common.DataBaseMetaData, common.TbMetaTable, "id", common.RefreshWait, metaTB.MetaTb)
		if err != nil {
			logger.Error.Printf("添加表失败:%s", err.Error())
		}

	}

	//5.1 中不修改已有表任何信息

	// 标签引用次数增加
	for _, tag := range info.Finaltable.TagList {
		devcenter.EditTagCitations(tag.ID, 1)
	}

	return TBID, nil
}

// CommentFormat sql建表语句表/表字段注释修正
func CommentFormat(comment string) string {
	for _, dirty := range common.DirtyComment {
		comment = strings.Replace(comment, dirty, "", -1)
	}
	return comment
}
func GetMetaTbinfo(sou source.TbJson) (source.Tbinfo, error) {
	var tbinfo source.Tbinfo
	//获取引擎信息
	engineinfo, err := GetInstance(sou.Engineid)
	if err != nil {
		return tbinfo, err
	}

	var must []map[string]interface{}
	term1 := gin.H{
		"term": gin.H{
			"engineid": sou.Engineid,
		},
	}
	term2 := gin.H{
		"term": gin.H{
			"dbname": sou.Dbname,
		},
	}
	term3 := gin.H{
		"term": gin.H{
			"tbname": sou.Tbname,
		},
	}
	must = append(must, term1, term2, term3)

	if engineinfo.Databasetype != common.Hive {
		term4 := gin.H{
			"term": gin.H{
				"schema": sou.Schema,
			},
		}
		must = append(must, term4)
	}

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": must,
			},
		},
	}
	res, err := escli.NewESClientCl.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
	if err != nil {
		return tbinfo, err
	}

	var sous metasource.MetaSourceTbHits
	err = json.Unmarshal(res, &sous)
	if err != nil {
		logger.Error.Println(err)
		return tbinfo, err
	}
	if len(sous.Hits1.Hits2) != 1 {
		return tbinfo, errors.New("获取元数据表不唯一")
	}
	for _, v := range sous.Hits1.Hits2[0].MetaSourceTbSource.FieldInfo {
		tbinfo.Field = append(tbinfo.Field, v.Name)
		tbinfo.Fieldtype = append(tbinfo.Fieldtype, v.FieldType)
		tbinfo.Fieldcomment = append(tbinfo.Fieldcomment, v.Annotation)
	}

	switch engineinfo.Databasetype {

	case common.Hive:

		for i := 0; i < len(tbinfo.Fieldcomment); i++ {
			tbinfo.Fieldcomment[i] = strings.Replace(tbinfo.Fieldcomment[i], "`", "", -1)
			tbinfo.Fieldcomment[i] = strings.Replace(tbinfo.Fieldcomment[i], "\\", "", -1)
		}

	}

	// 去除字段注释
	for i := 0; i < len(tbinfo.Fieldcomment); i++ {
		tbinfo.Fieldcomment[i] = dbutil.CommentFormat(tbinfo.Fieldcomment[i])
	}
	return tbinfo, nil

}

// CreateTmpTB 创建数据落地的 tmp_test_run 表，存放于 basedb 中，避免在资产中心展示
func CreateTmpTB(info source.OutputSave, lab source.ProDevInfo) error {
	var err error
	initDBName := lab.Newtbinfo.Dbname
	basedb := fmt.Sprintf("%s_basedb", info.ProjectID)
	if info.Finaltable.TbType == common.Hive || info.Finaltable.TbType == common.Inceptor {
		createSQL := fmt.Sprintf(` drop table if exists "%s"."%s"; create table  "%s"."%s" like "%s"."%s";`,
			basedb, info.Finaltable.Tbname+common.TMPTestRun, basedb, info.Finaltable.Tbname+common.TMPTestRun, info.Finaltable.Dbname, info.Finaltable.Tbname)
		_, err = MgExec(lab.Newtbinfo.Engineid, createSQL)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	} else {
		createSQL := fmt.Sprintf(` drop table if exists "%s"."%s"; create table  "%s"."%s" as select * from "%s"."%s" where 2=3;`,
			basedb, info.Finaltable.Tbname+common.TMPTestRun, basedb, info.Finaltable.Tbname+common.TMPTestRun, info.Finaltable.SourceDB, info.Finaltable.Tbname)
		_, err = MgExec(lab.Newtbinfo.Engineid, createSQL, initDBName)
		if err != nil {
			logger.Error.Println(err)
			return err
		}
	}

	return nil
}
