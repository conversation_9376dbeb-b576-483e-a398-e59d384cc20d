// Copyright (c) Datatom Software, Inc.(2017)
//
// Author:栾金阳（<EMAIL>）
//
// Creating Time:
package develop

import (
	"datatom.com/ants/httpdo"
	escli "datatom.com/tools/common/escli"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"datatom.com/ants/common"
	"datatom.com/ants/httpclient"
	"datatom.com/ants/logger"

	"datatom.com/ants/source"
	"datatom/gin.v1"
	//"errors"
)

// 推荐使用频率较高的标签
func DevRecommendTags(sp source.DevRecommendTags) ([]source.Tag, error) {

	results := make([]source.Tag, 0)
	var terms []interface{}

	if !sp.Listall {
		term1 := gin.H{
			"term": gin.H{
				"username": sp.User,
			},
		}

		terms = append(terms, term1)
	}
	//
	body := gin.H{
		"size": 0,
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
				"must_not": gin.H{
					"exists": gin.H{
						"field": "module",
					},
				},
			},
		},
		"aggregations": gin.H{
			"group_by_state": gin.H{
				"terms": gin.H{
					"field": "concat",
					"order": gin.H{
						"_count": "desc",
					},
					"size": sp.Size,
				},
			},
		},
	}

	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/%s/%s/_search", common.DataBaseLab, common.TbDev)
	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return results, nil
	}

	var sour source.GroupStatsJson
	err = json.Unmarshal(response, &sour)
	if err != nil {
		logger.Error.Println(err)
		return results, err
	}

	tags := sour.Aggregations.GroupByState.Buckets
	if len(tags) == 0 {
		return results, nil
	}

	for _, i := range tags {
		n := strings.LastIndex(i.Key, "#")
		var tag source.Tag
		tag.Name = i.Key[:n]
		tag.Color = i.Key[n:]
		results = append(results, tag)
	}
	return results, nil
}

// 判断哪些脚本需要删除
func DevDelif(sp source.DevDelif, Authorization string) (interface{}, error) {
	var ids []interface{}
	for _, id := range sp.Ids {
		lab, err := DevInfo(id)
		if err != nil {
			return "", err
		}

		if lab.Delif && len(lab.Dags) == 1 {
			ids = append(ids, lab.Id)
		}
	}
	uri := "/danastudio/ants/develop/delete"

	body := gin.H{
		"ids":     ids,
		"force":   true,
		"isdel":   sp.Isdel,
		"fromdag": sp.Fromdag,
	}

	byteBody, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	_, err = httpclient.PostAuthorization("127.0.0.1", common.SERVER_PORT, uri, string(byteBody), Authorization)
	if err != nil {
		return "", err
	}

	return "开发部分删除拽入脚本成功", nil
}

// 判断哪些脚本需要恢复
func DevRecif(sp source.DevRecif) (interface{}, error) {
	var ids []interface{}
	for _, id := range sp.Ids {
		lab, err := DevInfo(id)
		if err != nil {
			return "", err
		}

		if lab.Delif && len(lab.Dags) == 1 && lab.Dags[0].Dagid == sp.Fromdag {
			ids = append(ids, lab.Id)
		}
	}

	uri := "/danastudio/ants/develop/trash/recover"

	body := gin.H{
		"ids": ids,
	}

	byteBody, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	_, err = httpclient.PostJson("127.0.0.1", common.SERVER_PORT, uri, string(byteBody))
	if err != nil {
		return "", err
	}

	return "开发部分恢复拽入脚本成功", nil
}

func DevGenerateVersion(id string) error {
	bytes, err := escli.NewESClientCl.SearchByID(common.DataBaseLab, common.TbDev, id)
	if err != nil {
		logger.Error.Println(err)
		return err
	}
	var devInfo source.DevInfoHis
	err = json.Unmarshal([]byte(bytes), &devInfo)
	if err != nil {
		logger.Error.Printf("查询脚本失败:%s", err.Error())
		return err
	}

	devInfo.DevID = devInfo.Id
	devInfo.Id = httpdo.Idrand(12, 3)

	if devInfo.Notetype == "sql" || devInfo.Notetype == "stream" {
		if devInfo.Sqlid != "" {
			devInfo.Name, devInfo.EngineName, _ = GetEngine(devInfo.Sqlid)

			if devInfo.SourceDBName == "" && devInfo.EngineName == common.Hive {
				devInfo.SourceDBName = devInfo.Initdb
			}

			devInfo.EngineID = devInfo.Sqlid
		}

	}

	devInfo.GenerateTime = time.Now().Format(common.TimeFormat)

	//获取版本号
	version := 1
	devHis, _ := ListDevHistory(id)
	if len(devHis) != 0 {
		version = devHis[0].Version + 1
	}

	devInfo.Version = version

	err = escli.NewESClientCl.AddWithAimID(common.DataBaseLab, common.TbDevHis, devInfo.Id, devInfo)
	if err != nil {
		logger.Error.Printf("脚本历史生成失败:%s", err.Error())
		return err
	}

	return nil
}

func ListDevHistory(id string) ([]source.DevInfoHis, error) {
	devHistory := make([]source.DevInfoHis, 0)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"devid": id,
					},
				},
			},
		},
		"sort": gin.H{
			"version": "desc",
		},
		"size": common.UnlimitSize,
	}

	res, err := escli.NewESClientCl.SearchByTerm(common.DataBaseLab, common.TbDevHis, body)
	if err != nil {
		logger.Error.Printf("脚本历史查询失败:%s", err.Error())
		return devHistory, err
	}
	sou := escli.NewESClientCl.GetSource(res)

	for _, v := range sou {
		var one source.DevInfoHis
		_ = json.Unmarshal([]byte(v), &one)
		devHistory = append(devHistory, one)
	}
	return devHistory, nil
}
