// Copyright (c) Datatom Software, Inc.(2017)
//
// Author: 祝林（<EMAIL>）
//
// Creating Time: 2018-04-02
package access

import (
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"
	"unicode"

	escli "datatom.com/tools/common/escli"

	"datatom.com/metadata/httpdo/collect"
	"datatom.com/metadata/httpdo/dbutil"
	toolsource "datatom.com/tools/source"

	"datatom.com/ants/common"
	"datatom.com/ants/httpclient"
	"datatom.com/ants/logger"
	"datatom.com/ants/source"

	//metadatacommon "datatom.com/metadata/common"
	"datatom/gin.v1"

	"datatom.com/metadata/httpdo/devcenter"
	metadata "datatom.com/metadata/source"
	tools "datatom.com/tools/httpdo"
	simplejson "github.com/bitly/go-simplejson"
	"github.com/go-ini/ini"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

func escape(t string) string {
	return strings.Replace(t, `\`, `\\`, -1)
}
func UpdateConfig(info source.FtpInfo) error {
	cfg, err := ini.Load(common.CONFIG_INI)
	if err != nil {
		fmt.Println(err)
		return err
	}
	secServer, _ := cfg.GetSection("server")
	port, _ := secServer.GetKey("port")

	port.SetValue(info.ServerPort)

	name, _ := secServer.GetKey("name")
	name.SetValue(info.ServerName)

	err = cfg.SaveTo(common.CONFIG_INI)
	if err != nil {
		fmt.Println(err)
		return err
	}

	cfg2, err := ini.Load(common.CUSTOM_INI)
	if err != nil {
		fmt.Println(err)
		return err
	}

	secAdmin, _ := cfg2.GetSection("admin")
	user, _ := secAdmin.GetKey("user")
	user.SetValue(info.CustomUser)
	pass, _ := secAdmin.GetKey("pass")
	pass.SetValue(info.CustomPass)

	//	secCayman, _ := cfg2.GetSection("cayman")
	//	addr, _ := secCayman.GetKey("addr")
	//	addr.SetValue(info.CaymanAddr)
	//	userid, _ := secCayman.GetKey("userid")
	//	userid.SetValue(info.CaymanUserid)

	err = cfg2.SaveTo(common.CUSTOM_INI)
	if err != nil {
		fmt.Println(err)
		return err
	}

	return nil
}
func StartFtp() error {
	_, err := shell("systemctl start goftp ")
	if err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}
func StopFtp() error {
	_, err := shell("systemctl stop goftp")
	if err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}
func GetFtpInfo() (source.FtpInfo, error) {
	var info source.FtpInfo
	cfg, err := ini.Load(common.CONFIG_INI)
	if err != nil {
		fmt.Println(err)
		return info, err
	}
	secServer, _ := cfg.GetSection("server")
	port, _ := secServer.GetKey("port")

	info.ServerPort = port.Value()

	name, _ := secServer.GetKey("name")
	info.ServerName = name.Value()

	err = cfg.SaveTo(common.CONFIG_INI)
	if err != nil {
		fmt.Println(err)
		return info, err
	}

	cfg2, err := ini.Load(common.CUSTOM_INI)
	if err != nil {
		fmt.Println(err)
		return info, err
	}

	secAdmin, _ := cfg2.GetSection("admin")
	user, _ := secAdmin.GetKey("user")
	info.CustomUser = user.Value()
	pass, _ := secAdmin.GetKey("pass")
	info.CustomPass = pass.Value()

	//	secCayman, _ := cfg2.GetSection("cayman")
	//	addr, _ := secCayman.GetKey("addr")
	//	info.CaymanAddr = addr.Value()
	//	userid, _ := secCayman.GetKey("userid")
	//	info.CaymanUserid = userid.Value()

	err = cfg2.SaveTo(common.CUSTOM_INI)
	if err != nil {
		fmt.Println(err)
		return info, err
	}

	return info, nil
}
func shell(str string) (string, error) {

	fmt.Println(str)
	resp, err := exec.Command("/bin/sh", "-c", str).Output()
	if err != nil {
		return "", err
	}
	return string(resp), nil
	//	return "", nil
}

// List list all s3 storage info.
func List() ([]source.FtpInfo, error) {

	body := gin.H{
		"size": 20,
	}

	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/%s/%s/_search", common.DataBaseFtp, common.TbFtp)
	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {

		return []source.FtpInfo{}, err
	}

	var sour source.HitsResult
	err = json.Unmarshal(response, &sour)
	if err != nil {

		return []source.FtpInfo{}, err
	}

	var array []source.FtpInfo

	for i := 0; i < len(sour.Hits.HitsT); i++ {
		array = append(array, sour.Hits.HitsT[i].EaglesInfo)
	}

	return array, nil
}
func Add(info source.FtpInfo) (string, error) {

	byteBody, err := json.Marshal(info)
	uri := fmt.Sprintf("/%s/%s", common.DataBaseFtp, common.TbFtp)

	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {

		return "", err
	}

	info.Id, err = GetDocId(response)
	if err != nil {

		return "", err
	}

	var body = gin.H{
		"doc": gin.H{
			"id": info.Id,
		},
	}
	byteBody, _ = json.Marshal(body)
	//todo

	//id, err := mgm.GetDocId(res)

	uri = fmt.Sprintf("/%s/%s/%s/_update", common.DataBaseFtp, common.TbFtp, info.Id)

	response, err = httpclient.Post(uri, string(byteBody))
	if err != nil {

		return "", err
	}

	return info.Id, nil
}
func Delete(id string) error {

	uri := fmt.Sprintf("/%s/%s/%s", common.DataBaseFtp, common.TbFtp, id)
	_, err := httpclient.Delete(uri, "")
	if err != nil {

		return err
	}
	return nil
}
func Update(info source.FtpInfo) error {
	var body = gin.H{
		"doc": info,
	}
	byteBody, _ := json.Marshal(body)

	uri := fmt.Sprintf("/%s/%s/%s/_update", common.DataBaseFtp, common.TbFtp, info.Id)

	_, err := httpclient.Post(uri, string(byteBody))
	if err != nil {

		return err
	}

	return nil
}

type doc struct {
	Id      string `json:"_id"`
	Created bool   `json:"created"`
}

// GetDocId
func GetDocId(str []byte) (string, error) {
	var t doc
	err := json.Unmarshal(str, &t)
	if err != nil {

		return "", err
	}
	return t.Id, nil
}

// WriterJSON 抽取任务构建datax writer json结构体
func WriterJSON(sp *source.NewAcc, batch bool) (string, string, string, error) {
	// 存储源表字段修正--仅当为hive时全部转为小写
	if sp.Sourcedb.Dbtype == "hive" || sp.Sourcedb.Dbtype == common.ODPS {
		for i := 0; i < len(sp.Sourcedb.Field); i++ {
			sp.Sourcedb.Field[i] = strings.ToLower(sp.Sourcedb.Field[i])
		}
	}
	//已经做了类型转换
	//if batch { //批量 类型自动转换
	//	var cast toolsource.TypeCast
	//	cast.ExtrType = sp.Extractdb.DBType
	//	cast.SourceType = sp.Sourcedb.Dbtype
	//	cast.FieldType = sp.Sourcedb.Fieldtype
	//	//fmt.Printf("WriterJSON--cast:%#v\n", cast)
	//	sp.Sourcedb.Fieldtype = tools.FieldTrans(cast)
	//}
	writerjson, dbpath, analyzeSql := "", "", ""
	var err error
	switch sp.Sourcedb.Dbtype {
	case "stork", "teryx", "greenplum", "postgres", common.GaussDB, common.UXDB, common.GaussDBA, common.TDSQL:
		writerjson, analyzeSql = storkWriterJSON(sp)
	case "hive":
		switch sp.Sourcedb.HiveVersion {
		case common.HiveTDC:
			if sp.TDCLoad == common.TDCLoadExtract {
				writerjson, dbpath, err = TDCLoadWriterJSON(sp.Sourcedb.Table)

			} else {
				writerjson, err = TDCWriterJSON(sp)
			}
		default:
			fmt.Println("-------------------------------hive.sp.Sourcedb.Field-----------------------------------------")
			fmt.Println(sp.Sourcedb.Field)
			fmt.Println(sp.Sourcedb.PartitionField)
			writerjson, dbpath, analyzeSql, err = hiveWriterJSON(sp)
			logger.Info.Println("ceshijson2:", writerjson)
			if err != nil {
				return writerjson, dbpath, analyzeSql, err
			}
		}

	case common.Inceptor:
		writerjson, dbpath, analyzeSql, err = tdhWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
	case common.DMDB:
		writerjson, analyzeSql = daMengWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
	case common.ODPS:
		writerjson, analyzeSql, err = odpsWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
	case common.DRealDB:
		writerjson, analyzeSql = drealdbWriterJSON(sp)
	default:
	}
	return writerjson, dbpath, analyzeSql, nil
}

// WriterJSON 抽取任务构建datax writer json结构体
func ReportWriterJSON(sp *source.DataReport, batch bool) (string, string, string, error) {
	// 存储源表字段修正--仅当为hive时全部转为小写
	if sp.Sourcedb.Dbtype == "hive" || sp.Sourcedb.Dbtype == common.ODPS {
		for i := 0; i < len(sp.Sourcedb.Field); i++ {
			sp.Sourcedb.Field[i] = strings.ToLower(sp.Sourcedb.Field[i])
		}
	}
	//已经做了类型转换
	//if batch { //批量 类型自动转换
	//	var cast toolsource.TypeCast
	//	cast.ExtrType = sp.Extractdb.DBType
	//	cast.SourceType = sp.Sourcedb.Dbtype
	//	cast.FieldType = sp.Sourcedb.Fieldtype
	//	//fmt.Printf("WriterJSON--cast:%#v\n", cast)
	//	sp.Sourcedb.Fieldtype = tools.FieldTrans(cast)
	//}
	writerjson, dbpath, analyzeSql := "", "", ""
	var err error
	switch sp.Sourcedb.Dbtype {
	case "stork", "teryx", "greenplum", "postgres", common.GaussDB, common.UXDB, common.GaussDBA:
		writerjson, analyzeSql = storkReportWriterJSON(sp)
	case "hive":
		switch sp.Sourcedb.HiveVersion {
		/*		case common.HiveTDC:
				if sp.TDCLoad == common.TDCLoadExtract {
					writerjson, dbpath, err = TDCLoadWriterJSON(sp.Sourcedb.Table)

				} else {
					writerjson, err = TDCWriterJSON(sp)
				}*/
		default:
			fmt.Println("-------------------------------hive.sp.Sourcedb.Field-----------------------------------------")
			fmt.Println(sp.Sourcedb.Field)
			fmt.Println(sp.Sourcedb.PartitionField)
			writerjson, dbpath, analyzeSql, err = hiveReportWriterJSON(sp)
			if err != nil {
				return writerjson, dbpath, analyzeSql, err
			}
		}

	case common.Inceptor:
		writerjson, dbpath, analyzeSql, err = tdhReportWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
	case common.DMDB:
		writerjson, analyzeSql = daMengReportWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
	case common.ODPS:
		writerjson, analyzeSql, err = odpsReportWriterJSON(sp)
		if err != nil {
			return writerjson, dbpath, analyzeSql, err
		}
	case common.DRealDB:
		writerjson, analyzeSql = reportDrealdbWriterJSON(sp)
	default:
	}
	return writerjson, dbpath, analyzeSql, nil
}

func DaMengWriterJSON(sp *source.NewAcc) (string, string) {
	return daMengWriterJSON(sp)
}

func daMengWriterJSON(sp *source.NewAcc) (string, string) {
	daMengWriters := common.DaMengWriters
	daMengWriters, _ = sjson.Set(daMengWriters, "parameter.username", sp.Sourcedb.Odsuser)
	dbpasswd := strings.Replace(sp.Sourcedb.Odspassword, `\u003e`, ">", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u0026`, "&", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u003c`, "<", -1)
	dbpasswd = strings.Replace(dbpasswd, `\`, `\\`, -1)
	dbpasswd = strings.Replace(dbpasswd, `\\`, "u0000u", -1)
	daMengWriters, _ = sjson.Set(daMengWriters, "parameter.password", dbpasswd)
	if len(sp.Sourcedb.Field) != 0 {
		for i := 0; i < len(sp.Sourcedb.Field); i++ {
			if sp.Extractdb.Createfield == "" {
				if len(sp.TableRelation.Edges) > 0 {
					for j, v := range sp.TableRelation.Edges {
						daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(j),
							"\""+strings.TrimRight(v.Target, "\r")+"\"")
					}
					break
				} else {
					daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(i),
						"\""+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"\"")
				}
			} else {
				if len(sp.TableRelation.Edges) > 0 {
					for j, v := range sp.TableRelation.Edges {
						daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(j),
							`\"`+strings.TrimRight(v.Target, "\r")+`\"`)
					}
					break
				} else {
					daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(i),
						`\"`+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+`\"`)
				}
			}
		}
	}
	/*	if sp.Extractdb.Createfield == "" {
			daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.table.0",
				"\""+sp.Sourcedb.Schema+"\""+"."+"\""+sp.Sourcedb.Table+"\"")
		} else {
			daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.table.0",
				`\"`+sp.Sourcedb.Schema+`\"`+"."+`\"`+sp.Sourcedb.Table+`\"`)
		}*/
	postsql := fmt.Sprintf("SP_TAB_STAT_INIT('%s','%s')", sp.Sourcedb.Schema, sp.Sourcedb.Table)
	//daMengWriters, _ = sjson.Set(daMengWriters, "parameter.postSql.0", postsql)
	if sp.Extractdb.Createfield == "" {
		if sp.Tasktype == 4 {
			presql := "delete from @table"
			daMengWriters, _ = sjson.Set(daMengWriters, "parameter.preSql.0", presql)
		}
	}

	if sp.Datastrategy == 1 {
		presql := fmt.Sprintf(`truncate table "%s"."%s"`, sp.Sourcedb.Schema, sp.Sourcedb.Table)
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.preSql.0", presql)
	}

	/*	if sp.Sourcedb.RegisterWay == "colony" && sp.Sourcedb.JdbcURL != "" {
			daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.jdbcUrl", sp.Sourcedb.JdbcURL)
		} else {
			daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.jdbcUrl",
				fmt.Sprintf("jdbc:dm://%s:%s", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport))
		}*/

	if sp.Extractdb.Createfield == "" {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.table.0",
			"\""+sp.Sourcedb.Schema+"\""+"."+"\""+sp.Sourcedb.Table+"\"")
	} else {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.table.0",
			`\"`+sp.Sourcedb.Schema+`\"`+"."+`\"`+sp.Sourcedb.Table+`\"`)
	}

	if sp.Sourcedb.RegisterWay == "colony" && sp.Sourcedb.JdbcURL != "" {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.jdbcUrl", sp.Sourcedb.JdbcURL)
	} else {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.jdbcUrl",
			fmt.Sprintf("jdbc:dm://%s:%s", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport))
	}
	return daMengWriters, postsql
}

func daMengReportWriterJSON(sp *source.DataReport) (string, string) {
	daMengWriters := common.DaMengWriters
	daMengWriters, _ = sjson.Set(daMengWriters, "parameter.username", sp.Sourcedb.Odsuser)
	dbpasswd := strings.Replace(sp.Sourcedb.Odspassword, `\u003e`, ">", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u0026`, "&", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u003c`, "<", -1)
	dbpasswd = strings.Replace(dbpasswd, `\`, `\\`, -1)
	dbpasswd = strings.Replace(dbpasswd, `\\`, "u0000u", -1)
	daMengWriters, _ = sjson.Set(daMengWriters, "parameter.password", dbpasswd)
	if len(sp.Sourcedb.Field) != 0 {
		for i := 0; i < len(sp.Sourcedb.Field); i++ {
			if sp.Extractdb.Createfield == "" {
				if len(sp.TableRelation.Edges) > 0 {
					for j, v := range sp.TableRelation.Edges {
						daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(j),
							"\""+strings.TrimRight(v.Target, "\r")+"\"")
					}
					break
				} else {
					daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(i),
						"\""+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"\"")
				}
			} else {
				if len(sp.TableRelation.Edges) > 0 {
					for j, v := range sp.TableRelation.Edges {
						daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(j),
							`\"`+strings.TrimRight(v.Target, "\r")+`\"`)
					}
					break
				} else {
					daMengWriters, _ = sjson.Set(daMengWriters, "parameter.column."+strconv.Itoa(i),
						`\"`+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+`\"`)
				}
			}
		}
	}
	if sp.Extractdb.Createfield == "" {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.table.0",
			"\""+sp.Sourcedb.Schema+"\""+"."+"\""+sp.Sourcedb.Table+"\"")
	} else {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.table.0",
			`\"`+sp.Sourcedb.Schema+`\"`+"."+`\"`+sp.Sourcedb.Table+`\"`)
	}
	postsql := fmt.Sprintf("SP_TAB_STAT_INIT('%s','%s')", sp.Sourcedb.Schema, sp.Sourcedb.Table)
	//daMengWriters, _ = sjson.Set(daMengWriters, "parameter.postSql.0", postsql)
	if sp.Extractdb.Createfield == "" {

	}
	if sp.Sourcedb.RegisterWay == "colony" && sp.Sourcedb.JdbcURL != "" {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.jdbcUrl", sp.Sourcedb.JdbcURL)
	} else {
		daMengWriters, _ = sjson.Set(daMengWriters, "parameter.connection.0.jdbcUrl",
			fmt.Sprintf("jdbc:dm://%s:%s", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport))
	}
	return daMengWriters, postsql
}

func odpsWriterJSON(sp *source.NewAcc) (string, string, error) {
	var sqlTxtTemp, sqlTxt string
	if sp.Tasktype == 4 {
		return "", "", fmt.Errorf("ODPS暂不支持实时采集")
	}
	//logger.Info.Println("tablename:", sp.Sourcedb.Table)
	if !TableRename(sp.Sourcedb.Table) {
		return "", "", fmt.Errorf("表名不合法: %s", sp.Sourcedb.Table)
	}
	var err error
	//writer的组建
	//----------------------------------
	oDPSWriters := common.ODPSWriters

	//tbname := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.table", sp.Sourcedb.Table)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.project", sp.Sourcedb.OdpsProject)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.accessId", sp.Sourcedb.Odsuser)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.accessKey", sp.Sourcedb.Odspassword)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.truncate", true)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.odpsServer", sp.Sourcedb.OdpsUrl)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.tunnelServer", sp.Sourcedb.TunnelUrl)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.accountType", "aliyun")

	//从临时表插入存储表sql
	var insertSql, analyzesql, dropSqlTemp, dropSql string
	dropSql = fmt.Sprintf("drop table  `%s`.`%s`;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
	//tempTbName := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	//connect := gin.H{
	//	"jdbcUrl": collect.InsteadDBname(sp.Sourcedb.JdbcURL, sp.Sourcedb.Odsdatabase),
	//	"table":   []string{tempTbName},
	//}
	//区分ds创建的表和已有分区表的区分
	if len(sp.Sourcedb.PartitionField) > 0 {
		var fieldSql, tempSql, tbSql, parSql string
		for _, v := range sp.Sourcedb.Field {
			var isPar bool
			for _, vv := range sp.Sourcedb.PartitionField {
				if v == vv.Name {
					isPar = true
					break
				}
			}
			if !isPar {
				fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
			}
		}
		fieldSql = fieldSql[1:]
		tbSql = fieldSql
		tempSql = fieldSql
		//分区表组合
		for _, v := range sp.Sourcedb.PartitionField {
			parSql = fmt.Sprintf("%s,`%s`", parSql, v.Name)
		}
		parSql = parSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		var isMix = 0
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				if isMix == 2 {
					isMix = 3
					break
				} else {
					isMix = 1
				}
			} else {
				if isMix == 1 {
					isMix = 3
					break
				} else {
					isMix = 2
				}
			}
		}
		fmt.Println("temsql1:", tempSql)
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				tempSql = fmt.Sprintf("%s,`%s`", tempSql, v.Name)
				fmt.Println("temsql2:", tempSql)
			} else {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				if len(sp.Sourcedb.PartitionField) == 1 || isMix == 3 {
					//系统预设一级分区
					switch v.Ispreset {
					case "year":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyy') as", v.Name)
					case "month":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyyMM') as", v.Name)
					case "day":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyyMMdd') as", v.Name)
					}
				} else {
					if isMix == 2 {
						switch v.Ispreset {
						case "year":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyy') as", v.Name)
						case "month":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'MM') as", v.Name)
						case "day":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'dd') as", v.Name)
						}
					}
				}
			}
		}
		fmt.Println("temsql3:", tempSql)
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s`.`%s` partition (%s) select %s from `%s` ;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table, parSql, tempSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s`.`%s` COMPUTE STATISTICS;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
		//分区表删除临时表使用
		dropSqlTemp = fmt.Sprintf("drop table `%s`.`%s`;", sp.Sourcedb.OdpsProject, tempTb)
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		var fieldSql, parSql string
		for _, v := range sp.Sourcedb.Field {
			fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
		}
		fieldSql = fieldSql[1:]
		//分区表组合
		for _, v := range sp.Sourcedb.PartitionInfoFields {
			parSql = fmt.Sprintf("%s,`%s`", parSql, v)
		}
		parSql = parSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s`.`%s` partition (%s) select %s from `%s` ;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table, parSql, fieldSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s`.`%s` COMPUTE STATISTICS;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
		//分区表删除临时表使用
		dropSqlTemp = fmt.Sprintf("drop table  `%s`.`%s`;", sp.Sourcedb.OdpsProject, tempTb)
	} else {
		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s`.`%s` COMPUTE STATISTICS;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
	}
	//getSql := []string{insertSql, analyzesql}
	if len(sp.Sourcedb.PartitionField) > 0 || len(sp.Sourcedb.PartitionInfoFields) > 0 {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(0), insertSql)
		//oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(1), analyzesql)
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(1), dropSqlTemp)
		// 获取临时表sqltxt
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": sp.Sourcedb.TableID,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		resp, err := escli.NewESClientCl.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			fmt.Printf("例举存储源表失败:%s", err.Error())
			return oDPSWriters, "", err
		}
		var tbhit metadata.MetaSourceTbHits
		err = json.Unmarshal([]byte(resp), &tbhit)
		if err != nil {
			fmt.Printf("解析表数据失败:%s", err.Error())
			return oDPSWriters, "", err
		}
		var tbinfoNew metadata.MetaSourceTb
		for _, v := range tbhit.Hits1.Hits2 {
			//fmt.Println("--------------------------------")
			//fmt.Println(sp.Sourcedb.Table)
			//fmt.Println(v.MetaSourceTbSource.TBName)
			//fmt.Println(v.MetaSourceTbSource.SqlTxtTemp)
			if sp.Sourcedb.Table == v.MetaSourceTbSource.TBName {
				sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTemp
				if sqlTxtTemp == "" {
					sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTempNew
				}
				sqlTxt = v.MetaSourceTbSource.SqlTxt
				if sqlTxt == "" {
					sqlTxt = v.MetaSourceTbSource.SqlTxtNew
				}
				tbinfoNew = v.MetaSourceTbSource
			}
		}
		//如果非ds创建分区表，sqltxttemp无内容，需要组合sql语句
		if sqlTxtTemp == "" || sqlTxt == "" {
			eninfo, err := dbutil.EngineInfo(tbinfoNew.EngineID)
			if err != nil {
				//logger.Error.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				fmt.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				return "", "", fmt.Errorf("根据engineid获取引擎信息失败:%s", err.Error())
			}
			//获取topic -- 分区创建表的路径
			tbinfoNew.RealTimeTB.SourcedbTableName = tbinfoNew.TBName
			tbinfoNew.RealTimeTB.SourcedbType = tbinfoNew.DBType
			tbinfoNew.RealTimeTB.SourcedbTableID = tbinfoNew.ID
			var topics string
			if tbinfoNew.IsRT {
				info := collect.TopicInfo(&tbinfoNew)
				topics = info.DbServerName
			}
			sqltxt, sqltxtTemp := dbutil.HiveTbSQL(&tbinfoNew, eninfo.EngineName, topics, common.ODPS)
			sqltxt = strings.Replace(sqltxt, "\"", "", -1)
			sqltxtTemp = strings.Replace(sqltxtTemp, "\"", "", -1)
			sqltxt = strings.Replace(sqltxt, "\\", "", -1)
			sqltxtTemp = strings.Replace(sqltxtTemp, "\\", "", -1)
			if sqltxt == "" {
				fmt.Printf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
				return "", "", fmt.Errorf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
			}
			sqlTxtTemp = sqltxtTemp
			sqlTxt = sqltxt
		}
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.preSql."+strconv.Itoa(0), sqlTxtTemp)
	} else {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(0), analyzesql)
	}

	//oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.connection."+strconv.Itoa(0), connect)

	fileType := "text"
	if strings.Contains(sp.Sourcedb.FileType, "rc") {
		fileType = common.TypeOrc
	}
	if fileType == "text" {
		fileType = "csv"
	}
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.fileType", fileType)
	//var lenField int
	if len(sp.Sourcedb.Field) != 0 && sp.Tasktype != 4 {
		if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) {
			return oDPSWriters, "", fmt.Errorf("目的源字段数量与字段类型数量不一致：%d, %d", len(sp.Sourcedb.Field), len(sp.Sourcedb.Fieldtype))
		}
		if len(sp.TableRelation.Edges) > 0 {
			j := 0
			for _, v := range sp.TableRelation.Edges { //映射字段 key weight length
				for i := 0; i < len(sp.Sourcedb.Field); i++ { //目的端所有字段
					if v.Target == sp.Sourcedb.Field[i] {
						//预设分区键不写入
						var isPar bool
						for _, parFieldInfo := range sp.Sourcedb.PartitionField {
							if v.Target == parFieldInfo.Name && parFieldInfo.Ispreset != "notpreset" {
								isPar = true
							}
						}
						if !isPar {
							oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(j), sp.Sourcedb.Field[i])
						}
					}
				}
				j = j + 1
				//hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j),
				//	"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}

		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				//预设分区键不写入
				var isPar bool
				for _, parFieldInfo := range sp.Sourcedb.PartitionField {
					if sp.Sourcedb.Field[i] == parFieldInfo.Name && parFieldInfo.Ispreset != "notpreset" {
						isPar = true
					}
				}
				if !isPar {
					oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(i), sp.Sourcedb.Field[i])
				}
			}

		}
		/*for i := 0; i < len(sp.Sourcedb.Field); i++ {
			//record := gin.H{
			//	"name": sp.Sourcedb.Field[i],
			//	"type": sp.Sourcedb.Fieldtype[i],
			//}
			oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(i), sp.Sourcedb.Field[i])
		}*/
		//lenField = len(sp.Sourcedb.Field)
	}
	//hive分区的设置
	//由于只是传给datax的，不需要进行规则修改
	//var parRecord = gin.H{}
	if len(sp.Sourcedb.PartitionField) > 0 {
		//for _, v := range sp.Sourcedb.PartitionField {
		//	if v.Ispreset != "notpreset" {
		//		switch v.Ispreset {
		//		case "year":
		//			parRecord = gin.H{
		//				"name": "to_char(from_unixtime(unix_timestamp()),'yyyy')",
		//				"type": v.FieldType,
		//			}
		//		case "month":
		//			parRecord = gin.H{
		//				"name": "to_char(from_unixtime(unix_timestamp()),'yyyymm')",
		//				"type": v.FieldType,
		//			}
		//		case "day":
		//			parRecord = gin.H{
		//				"name": "to_char(from_unixtime(unix_timestamp()),'yyyymmdd')",
		//				"type": v.FieldType,
		//			}
		//		}
		//		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(lenField), parRecord)
		//		lenField++
		//	}
		//}
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.havePartition", "true")
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.havePartition", "true")
	}
	//新增分区表适配命令（覆盖=删除+重建）oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.preSql."+strconv.Itoa(0), sqlTxtTemp)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.partitionSql."+strconv.Itoa(0), dropSql)
	sqlTxts := strings.Split(sqlTxt, ";")
	if len(sqlTxts) > 0 {
		for k, v := range sqlTxts {
			oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.partitionSql."+strconv.Itoa(k+1), v+";")
		}
	} else {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.partitionSql."+strconv.Itoa(1), sqlTxt)
	}
	//
	js, err := simplejson.NewJson([]byte(oDPSWriters))
	if err != nil {
		return oDPSWriters, "", err
	}
	encode, _ := js.Encode()
	oDPSWriters = string(encode)
	return oDPSWriters, analyzesql, nil

}
func odpsReportWriterJSON(sp *source.DataReport) (string, string, error) {
	var sqlTxtTemp, sqlTxt string

	logger.Info.Println("tablename:", sp.Sourcedb.Table)
	if !TableRename(sp.Sourcedb.Table) {
		return "", "", fmt.Errorf("表名不合法: %s", sp.Sourcedb.Table)
	}
	var err error
	//writer的组建
	//----------------------------------
	oDPSWriters := common.ODPSWriters

	//tbname := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.table", sp.Sourcedb.Table)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.project", sp.Sourcedb.OdpsProject)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.accessId", sp.Sourcedb.Odsuser)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.accessKey", sp.Sourcedb.Odspassword)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.truncate", true)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.odpsServer", sp.Sourcedb.OdpsUrl)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.tunnelServer", sp.Sourcedb.TunnelUrl)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.accountType", "aliyun")

	//从临时表插入存储表sql
	var insertSql, analyzesql, dropSqlTemp, dropSql string
	dropSql = fmt.Sprintf("drop table  `%s`.`%s`;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
	//tempTbName := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	//connect := gin.H{
	//	"jdbcUrl": collect.InsteadDBname(sp.Sourcedb.JdbcURL, sp.Sourcedb.Odsdatabase),
	//	"table":   []string{tempTbName},
	//}
	//区分ds创建的表和已有分区表的区分
	if len(sp.Sourcedb.PartitionField) > 0 {
		var fieldSql, tempSql, tbSql, parSql string
		for _, v := range sp.Sourcedb.Field {
			var isPar bool
			for _, vv := range sp.Sourcedb.PartitionField {
				if v == vv.Name {
					isPar = true
					break
				}
			}
			if !isPar {
				fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
			}
		}
		fieldSql = fieldSql[1:]
		tbSql = fieldSql
		tempSql = fieldSql
		//分区表组合
		for _, v := range sp.Sourcedb.PartitionField {
			parSql = fmt.Sprintf("%s,`%s`", parSql, v.Name)
		}
		parSql = parSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		var isMix = 0
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				if isMix == 2 {
					isMix = 3
					break
				} else {
					isMix = 1
				}
			} else {
				if isMix == 1 {
					isMix = 3
					break
				} else {
					isMix = 2
				}
			}
		}
		fmt.Println("temsql1:", tempSql)
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				tempSql = fmt.Sprintf("%s,`%s`", tempSql, v.Name)
				fmt.Println("temsql2:", tempSql)
			} else {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				if len(sp.Sourcedb.PartitionField) == 1 || isMix == 3 {
					//系统预设一级分区
					switch v.Ispreset {
					case "year":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyy') as", v.Name)
					case "month":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyyMM') as", v.Name)
					case "day":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyyMMdd') as", v.Name)
					}
				} else {
					if isMix == 2 {
						switch v.Ispreset {
						case "year":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'yyyy') as", v.Name)
						case "month":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'MM') as", v.Name)
						case "day":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "to_char(from_utc_timestamp(unix_timestamp()*1000,'UTC'), 'dd') as", v.Name)
						}
					}
				}
			}
		}
		fmt.Println("temsql3:", tempSql)
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s`.`%s` partition (%s) select %s from `%s` ;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table, parSql, tempSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s`.`%s` COMPUTE STATISTICS;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
		//分区表删除临时表使用
		dropSqlTemp = fmt.Sprintf("drop table `%s`.`%s`;", sp.Sourcedb.OdpsProject, tempTb)
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		var fieldSql, parSql string
		for _, v := range sp.Sourcedb.Field {
			fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
		}
		fieldSql = fieldSql[1:]
		//分区表组合
		for _, v := range sp.Sourcedb.PartitionInfoFields {
			parSql = fmt.Sprintf("%s,`%s`", parSql, v)
		}
		parSql = parSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s`.`%s` partition (%s) select %s from `%s` ;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table, parSql, fieldSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s`.`%s` COMPUTE STATISTICS;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
		//分区表删除临时表使用
		dropSqlTemp = fmt.Sprintf("drop table  `%s`.`%s`;", sp.Sourcedb.OdpsProject, tempTb)
	} else {
		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s`.`%s` COMPUTE STATISTICS;", sp.Sourcedb.OdpsProject, sp.Sourcedb.Table)
	}
	//getSql := []string{insertSql, analyzesql}
	if len(sp.Sourcedb.PartitionField) > 0 || len(sp.Sourcedb.PartitionInfoFields) > 0 {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(0), insertSql)
		//oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(1), analyzesql)
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(1), dropSqlTemp)
		// 获取临时表sqltxt
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": sp.Sourcedb.TableID,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		resp, err := escli.NewESClientCl.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("例举存储源表失败:%s", err.Error())
			return oDPSWriters, "", err
		}
		var tbhit metadata.MetaSourceTbHits
		err = json.Unmarshal([]byte(resp), &tbhit)
		if err != nil {
			logger.Error.Printf("解析表数据失败:%s", err.Error())
			return oDPSWriters, "", err
		}
		var tbinfoNew metadata.MetaSourceTb
		for _, v := range tbhit.Hits1.Hits2 {
			//fmt.Println("--------------------------------")
			//fmt.Println(sp.Sourcedb.Table)
			//fmt.Println(v.MetaSourceTbSource.TBName)
			//fmt.Println(v.MetaSourceTbSource.SqlTxtTemp)
			if sp.Sourcedb.Table == v.MetaSourceTbSource.TBName {
				sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTemp
				if sqlTxtTemp == "" {
					sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTempNew
				}
				sqlTxt = v.MetaSourceTbSource.SqlTxt
				if sqlTxt == "" {
					sqlTxt = v.MetaSourceTbSource.SqlTxtNew
				}
				tbinfoNew = v.MetaSourceTbSource
			}
		}
		//如果非ds创建分区表，sqltxttemp无内容，需要组合sql语句
		if sqlTxtTemp == "" || sqlTxt == "" {
			eninfo, err := dbutil.EngineInfo(tbinfoNew.EngineID)
			if err != nil {
				//logger.Error.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				fmt.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				return "", "", fmt.Errorf("根据engineid获取引擎信息失败:%s", err.Error())
			}
			//获取topic -- 分区创建表的路径
			tbinfoNew.RealTimeTB.SourcedbTableName = tbinfoNew.TBName
			tbinfoNew.RealTimeTB.SourcedbType = tbinfoNew.DBType
			tbinfoNew.RealTimeTB.SourcedbTableID = tbinfoNew.ID
			var topics string
			if tbinfoNew.IsRT {
				info := collect.TopicInfo(&tbinfoNew)
				topics = info.DbServerName
			}
			sqltxt, sqltxtTemp := dbutil.HiveTbSQL(&tbinfoNew, eninfo.EngineName, topics, common.ODPS)
			sqltxt = strings.Replace(sqltxt, "\"", "", -1)
			sqltxtTemp = strings.Replace(sqltxtTemp, "\"", "", -1)
			sqltxt = strings.Replace(sqltxt, "\\", "", -1)
			sqltxtTemp = strings.Replace(sqltxtTemp, "\\", "", -1)
			if sqltxt == "" {
				fmt.Printf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
				return "", "", fmt.Errorf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
			}
			sqlTxtTemp = sqltxtTemp
			sqlTxt = sqltxt
		}
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.preSql."+strconv.Itoa(0), sqlTxtTemp)
	} else {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.postSql."+strconv.Itoa(0), analyzesql)
	}

	//oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.connection."+strconv.Itoa(0), connect)

	fileType := "text"
	if strings.Contains(sp.Sourcedb.FileType, "rc") {
		fileType = common.TypeOrc
	}
	if fileType == "text" {
		fileType = "csv"
	}
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.fileType", fileType)
	//var lenField int
	if len(sp.Sourcedb.Field) != 0 {
		if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) {
			return oDPSWriters, "", fmt.Errorf("目的源字段数量与字段类型数量不一致：%d, %d", len(sp.Sourcedb.Field), len(sp.Sourcedb.Fieldtype))
		}
		if len(sp.TableRelation.Edges) > 0 {
			j := 0
			for _, v := range sp.TableRelation.Edges { //映射字段 key weight length
				for i := 0; i < len(sp.Sourcedb.Field); i++ { //目的端所有字段
					if v.Target == sp.Sourcedb.Field[i] {
						//预设分区键不写入
						var isPar bool
						for _, parFieldInfo := range sp.Sourcedb.PartitionField {
							if v.Target == parFieldInfo.Name && parFieldInfo.Ispreset != "notpreset" {
								isPar = true
							}
						}
						if !isPar {
							oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(j), sp.Sourcedb.Field[i])
						}
					}
				}
				j = j + 1
				//hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j),
				//	"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}

		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				//预设分区键不写入
				var isPar bool
				for _, parFieldInfo := range sp.Sourcedb.PartitionField {
					if sp.Sourcedb.Field[i] == parFieldInfo.Name && parFieldInfo.Ispreset != "notpreset" {
						isPar = true
					}
				}
				if !isPar {
					oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(i), sp.Sourcedb.Field[i])
				}
			}

		}
		/*for i := 0; i < len(sp.Sourcedb.Field); i++ {
			//record := gin.H{
			//	"name": sp.Sourcedb.Field[i],
			//	"type": sp.Sourcedb.Fieldtype[i],
			//}
			oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(i), sp.Sourcedb.Field[i])
		}*/
		//lenField = len(sp.Sourcedb.Field)
	}
	//hive分区的设置
	//由于只是传给datax的，不需要进行规则修改
	//var parRecord = gin.H{}
	if len(sp.Sourcedb.PartitionField) > 0 {
		//for _, v := range sp.Sourcedb.PartitionField {
		//	if v.Ispreset != "notpreset" {
		//		switch v.Ispreset {
		//		case "year":
		//			parRecord = gin.H{
		//				"name": "to_char(from_unixtime(unix_timestamp()),'yyyy')",
		//				"type": v.FieldType,
		//			}
		//		case "month":
		//			parRecord = gin.H{
		//				"name": "to_char(from_unixtime(unix_timestamp()),'yyyymm')",
		//				"type": v.FieldType,
		//			}
		//		case "day":
		//			parRecord = gin.H{
		//				"name": "to_char(from_unixtime(unix_timestamp()),'yyyymmdd')",
		//				"type": v.FieldType,
		//			}
		//		}
		//		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.column."+strconv.Itoa(lenField), parRecord)
		//		lenField++
		//	}
		//}
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.havePartition", "true")
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.havePartition", "true")
	}
	//新增分区表适配命令（覆盖=删除+重建）oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.preSql."+strconv.Itoa(0), sqlTxtTemp)
	oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.partitionSql."+strconv.Itoa(0), dropSql)
	sqlTxts := strings.Split(sqlTxt, ";")
	if len(sqlTxts) > 0 {
		for k, v := range sqlTxts {
			oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.partitionSql."+strconv.Itoa(k+1), v+";")
		}
	} else {
		oDPSWriters, _ = sjson.Set(oDPSWriters, "parameter.partitionSql."+strconv.Itoa(1), sqlTxt)
	}
	//
	js, err := simplejson.NewJson([]byte(oDPSWriters))
	if err != nil {
		return oDPSWriters, "", err
	}
	encode, _ := js.Encode()
	oDPSWriters = string(encode)
	return oDPSWriters, analyzesql, nil

}

// 是否存在二进制类型
func HaveBianry(fieldtype []string) bool {
	for _, v := range fieldtype {
		switch v {
		case "bytea":
			return true
		}
	}
	return false
}
func BatchHaveBianry(id, table string) bool {
	var terms []interface{}
	dbid_term := gin.H{
		"term": gin.H{
			"fatherid": id,
		},
	}
	terms = append(terms, dbid_term)

	table_term := gin.H{
		"term": gin.H{
			"extractdb.table": table,
		},
	}
	terms = append(terms, table_term)
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": terms,
			},
		},
		"size": common.UnlimitSize,
	}
	taskStr, err := escli.NewESClientCl.SearchByTerm(common.DataBaseLab, common.BathcTbAccess, body)
	if err != nil {
		logger.Error.Printf("获取任务信息失败:%s", err.Error())
		return false
	}
	e := gjson.Get(string(taskStr), "hits.hits").Array()
	for _, v := range e {
		fieldinfo := v.Get("_source.sourcedb.fieldinfo").Array()
		for _, f := range fieldinfo {
			switch f.Get("fieldtype").String() {
			case "bytea":
				return true
			}
		}
	}
	return false
}

// storkWriterJSON 抽取任务构建datax writer json结构体 存储源：stork
func storkWriterJSON(sp *source.NewAcc) (string, string) {
	storkwriter := common.Storkwriters
	if sp.Sourcedb.Dbtype == common.UXDB {
		storkwriter = common.UXDBwriters
	}
	if sp.Sourcedb.Dbtype == common.DRealDB {
		storkwriter = common.DrealDBWriters
	}
	// 5.0-2.4需求 gaussdb、优炫改用与teryx相同writer
	if sp.Sourcedb.Dbtype == common.Teryx || sp.Sourcedb.Dbtype == common.GREENPLUM || sp.Sourcedb.Dbtype == common.GaussDB {
		storkwriter = common.Teryxwriters

		if sp.AdvancedConfig.Prioritymode == 1 {
			storkwriter = common.Storkwriters
		}
		//pgwrite支持二进制
		//if HaveBianry(sp.Sourcedb.Fieldtype) {
		//	storkwriter = common.Storkwriters
		//}
	} else {
		if sp.AdvancedConfig.Prioritymode == 2 {
			storkwriter = common.Teryxwriters
		}
	}
	storkwriter, _ = sjson.Set(storkwriter, "parameter.username", sp.Sourcedb.Odsuser)
	dbpasswd := strings.Replace(sp.Sourcedb.Odspassword, `\u003e`, ">", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u0026`, "&", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u003c`, "<", -1)
	dbpasswd = strings.Replace(dbpasswd, `\`, `\\`, -1)
	dbpasswd = strings.Replace(dbpasswd, `\\`, "u0000u", -1)
	storkwriter, _ = sjson.Set(storkwriter, "parameter.password", dbpasswd)
	var postsql string
	if sp.Extractdb.Createfield != "" {
		if len(sp.TableRelation.Edges) > 0 {
			for j, v := range sp.TableRelation.Edges {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(j),
					`\"`+strings.TrimRight(v.Target, "\r")+`\"`)
			}
		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(i),
					`\"`+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+`\"`)
			}
		}
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.table.0",
			sp.Sourcedb.Schema+"."+`\"`+sp.Sourcedb.Table+`\"`)
		postsql = "ANALYZE " + `\"` + sp.Sourcedb.Schema + `\"` + "." + `\"` + sp.Sourcedb.Table + `\"`
	} else {
		if len(sp.TableRelation.Edges) > 0 {
			for j, v := range sp.TableRelation.Edges {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(j),
					"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}
		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(i),
					"\""+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"\"")
			}
		}
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.table.0",
			sp.Sourcedb.Schema+"."+"\""+sp.Sourcedb.Table+"\"")
		postsql = "ANALYZE " + "\"" + sp.Sourcedb.Schema + "\"" + "." + "\"" + sp.Sourcedb.Table + "\""
	}

	//storkwriter, _ = sjson.Set(storkwriter, "parameter.postSql.0", postsql)
	//实时抽取只有覆盖策略
	if sp.Tasktype == 4 {
		presql := "delete from @table"
		storkwriter, _ = sjson.Set(storkwriter, "parameter.preSql.0", presql)
	}

	storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
		fmt.Sprintf("*****************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
			sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))
	if sp.Sourcedb.Dbtype == common.UXDB {
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
			fmt.Sprintf("***********************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
				sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))
	}
	if sp.Sourcedb.Dbtype == common.DRealDB {
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
			fmt.Sprintf("************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
				sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))
	}
	if sp.Sourcedb.Dbtype == common.TDSQL {
		if sp.Sourcedb.RegisterWay == "colony" && sp.Sourcedb.JdbcURL != "" {
			storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl", sp.Sourcedb.JdbcURL)
		} else {
			storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
				fmt.Sprintf("*****************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport))
		}
	}
	//postsql := fmt.Sprintf(`ANALYZE \"%s\".\"%s\"`, sp.Sourcedb.Schema, sp.Sourcedb.Table)

	return storkwriter, postsql
}

func DRealdbWriterJSON(sp *source.NewAcc) (string, string) {
	return drealdbWriterJSON(sp)
}

func drealdbWriterJSON(sp *source.NewAcc) (string, string) {

	drealdbwriter := common.DrealDBWriters

	drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.username", sp.Sourcedb.Odsuser)
	dbpasswd := strings.Replace(sp.Sourcedb.Odspassword, `\u003e`, ">", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u0026`, "&", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u003c`, "<", -1)
	dbpasswd = strings.Replace(dbpasswd, `\`, `\\`, -1)
	dbpasswd = strings.Replace(dbpasswd, `\\`, "u0000u", -1)
	drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.password", dbpasswd)
	var postsql string
	if sp.Extractdb.Createfield != "" {
		if len(sp.TableRelation.Edges) > 0 {
			for j, v := range sp.TableRelation.Edges {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(j),
					"`"+strings.TrimRight(v.Target, "\r")+"`")
			}
		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(i),
					"`"+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"`")
			}
		}
		drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.connection.0.table.0",
			"`"+sp.Sourcedb.Schema+"`"+"."+"`"+sp.Sourcedb.Table+"`")
		postsql = "ANALYZE TABLE" + "`" + sp.Sourcedb.Schema + "`" + "." + "`" + sp.Sourcedb.Table + "`"
	} else {
		if len(sp.TableRelation.Edges) > 0 {
			for j, v := range sp.TableRelation.Edges {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(j),
					"`"+strings.TrimRight(v.Target, "\r")+"`")
			}
		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(i),
					"`"+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"`")
			}
		}
		drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.connection.0.table.0",
			"`"+sp.Sourcedb.Schema+"`"+"."+"`"+sp.Sourcedb.Table+"`")
		postsql = "ANALYZE TABLE" + "`" + sp.Sourcedb.Schema + "`" + "." + "`" + sp.Sourcedb.Table + "`"
	}

	//drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.postSql.0", postsql)
	//实时抽取只有覆盖策略
	if sp.Tasktype == 4 {
		presql := "delete from @table"
		drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.preSql.0", presql)
	}

	drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.connection.0.jdbcUrl",
		fmt.Sprintf("************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
			sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))

	//postsql := fmt.Sprintf(`ANALYZE \"%s\".\"%s\"`, sp.Sourcedb.Schema, sp.Sourcedb.Table)

	return drealdbwriter, postsql
}

func reportDrealdbWriterJSON(sp *source.DataReport) (string, string) {

	drealdbwriter := common.DrealDBWriters

	drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.username", sp.Sourcedb.Odsuser)
	dbpasswd := strings.Replace(sp.Sourcedb.Odspassword, `\u003e`, ">", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u0026`, "&", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u003c`, "<", -1)
	dbpasswd = strings.Replace(dbpasswd, `\`, `\\`, -1)
	dbpasswd = strings.Replace(dbpasswd, `\\`, "u0000u", -1)
	drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.password", dbpasswd)
	var postsql string
	if sp.Extractdb.Createfield != "" {
		if len(sp.TableRelation.Edges) > 0 {
			for j, v := range sp.TableRelation.Edges {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(j),
					"`"+strings.TrimRight(v.Target, "\r")+"`")
			}
		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(i),
					"`"+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"`")
			}
		}
		drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.connection.0.table.0",
			"`"+sp.Sourcedb.Schema+"`"+"."+"`"+sp.Sourcedb.Table+"`")
		postsql = "ANALYZE TABLE" + "`" + sp.Sourcedb.Schema + "`" + "." + "`" + sp.Sourcedb.Table + "`"
	} else {
		if len(sp.TableRelation.Edges) > 0 {
			for j, v := range sp.TableRelation.Edges {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(j),
					"`"+strings.TrimRight(v.Target, "\r")+"`")
			}
		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.column."+strconv.Itoa(i),
					"`"+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"`")
			}
		}
		drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.connection.0.table.0",
			"`"+sp.Sourcedb.Schema+"`"+"."+"`"+sp.Sourcedb.Table+"`")
		postsql = "ANALYZE TABLE" + "`" + sp.Sourcedb.Schema + "`" + "." + "`" + sp.Sourcedb.Table + "`"
	}

	drealdbwriter, _ = sjson.Set(drealdbwriter, "parameter.connection.0.jdbcUrl",
		fmt.Sprintf("************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
			sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))

	//postsql := fmt.Sprintf(`ANALYZE \"%s\".\"%s\"`, sp.Sourcedb.Schema, sp.Sourcedb.Table)

	return drealdbwriter, postsql
}

// storkWriterJSON 抽取任务构建datax writer json结构体 存储源：stork
func storkReportWriterJSON(sp *source.DataReport) (string, string) {
	storkwriter := common.Storkwriters
	if sp.Sourcedb.Dbtype == common.UXDB {
		storkwriter = common.UXDBwriters
	}

	if sp.Sourcedb.Dbtype == common.DRealDB {
		storkwriter = common.DrealDBWriters
	}

	// 5.0-2.4需求 gaussdb、优炫改用与teryx相同writer
	if sp.Sourcedb.Dbtype == common.Teryx || sp.Sourcedb.Dbtype == common.GREENPLUM || sp.Sourcedb.Dbtype == common.GaussDB {
		storkwriter = common.Teryxwriters
		//pgwrite支持二进制
		if HaveBianry(sp.Sourcedb.Fieldtype) {
			storkwriter = common.Storkwriters
		}
	}
	storkwriter, _ = sjson.Set(storkwriter, "parameter.username", sp.Sourcedb.Odsuser)
	dbpasswd := strings.Replace(sp.Sourcedb.Odspassword, `\u003e`, ">", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u0026`, "&", -1)
	dbpasswd = strings.Replace(dbpasswd, `\u003c`, "<", -1)
	dbpasswd = strings.Replace(dbpasswd, `\`, `\\`, -1)
	dbpasswd = strings.Replace(dbpasswd, `\\`, "u0000u", -1)
	storkwriter, _ = sjson.Set(storkwriter, "parameter.password", dbpasswd)
	var postsql string
	{
		if len(sp.TableRelation.Edges) > 0 {
			for j, v := range sp.TableRelation.Edges {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(j),
					"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}
		} else {
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				storkwriter, _ = sjson.Set(storkwriter, "parameter.column."+strconv.Itoa(i),
					"\""+strings.TrimRight(sp.Sourcedb.Field[i], "\r")+"\"")
			}
		}
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.table.0",
			sp.Sourcedb.Schema+"."+"\""+sp.Sourcedb.Table+"\"")
		postsql = "ANALYZE " + "\"" + sp.Sourcedb.Schema + "\"" + "." + "\"" + sp.Sourcedb.Table + "\""
	}

	storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
		fmt.Sprintf("*****************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
			sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))
	if sp.Sourcedb.Dbtype == common.UXDB {
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
			fmt.Sprintf("***********************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
				sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))
	}

	if sp.Sourcedb.Dbtype == common.DRealDB {
		storkwriter, _ = sjson.Set(storkwriter, "parameter.connection.0.jdbcUrl",
			fmt.Sprintf("************************", sp.Sourcedb.Odsip, sp.Sourcedb.Odsport,
				sp.Sourcedb.Odsdatabase, sp.Sourcedb.Schema))
	}

	//postsql := fmt.Sprintf(`ANALYZE \"%s\".\"%s\"`, sp.Sourcedb.Schema, sp.Sourcedb.Table)

	return storkwriter, postsql
}

// TDCLoadWriterJSON 构建hive类型为tdc 抽取类型为落地抽取 的datax writer json结构体
func TDCLoadWriterJSON(tablename string) (string, string, error) {
	var tdcwtriter, path string
	// 如果本地指定文件夹不存在则新建
	command := fmt.Sprintf(`
		if [ ! -d "%s" ]; then
			mkdir "%s"
			fi`,
		common.TDCLoadPath, common.TDCLoadPath)
	_, err := tools.ExeCommand(command)
	if err != nil {
		return tdcwtriter, path, err
	}
	now := time.Now().Unix()
	tablepath := fmt.Sprintf("%s_%d", tablename, now)
	// 构建 datax writer json体
	tdcwtriter = common.CsvWriter
	tdcwtriter, _ = sjson.Set(tdcwtriter, "parameter.path", common.TDCLoadPath)
	tdcwtriter, _ = sjson.Set(tdcwtriter, "parameter.fileName", tablepath)
	path = tablepath
	return tdcwtriter, path, nil
}

// TDCWriterJSON 构建hive类型为tdc 的datax writer json结构体
func TDCWriterJSON(sp *source.NewAcc) (string, error) {
	var tdcwtriter string
	tdcwtriter = common.TDCWriter
	// tdcwtriter, _ = sjson.Set(tdcwtriter, "parameter.username", eninfo.Username)
	// tdcwtriter, _ = sjson.Set(tdcwtriter, "parameter.password", eninfo.Password)
	if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) {
		return tdcwtriter, fmt.Errorf("目的源字段数量与字段类型数量不一致：%d, %d", len(sp.Sourcedb.Field), len(sp.Sourcedb.Fieldtype))
	}
	for i := 0; i < len(sp.Sourcedb.Field); i++ {
		tdcwtriter, _ = sjson.Set(tdcwtriter,
			"parameter.column."+strconv.Itoa(i), sp.Sourcedb.Field[i])
	}
	tdcwtriter, _ = sjson.Set(tdcwtriter,
		"parameter.connection.0.table.0", sp.Sourcedb.Table)
	jdbcurl := sp.Sourcedb.JdbcURL
	tdcwtriter, _ = sjson.Set(tdcwtriter,
		"parameter.connection.0.jdbcUrl", jdbcurl)
	tdcwtriter, _ = sjson.Set(tdcwtriter, "parameter.batchSize", 8192)
	return tdcwtriter, nil
}

func HiveWriterJSON(sp *source.NewAcc) (string, string, string, error) {
	return hiveWriterJSON(sp)
}

// hiveWriterJSON 抽取任务构建datax writer json结构体 存储源：hive
func hiveWriterJSON(sp *source.NewAcc) (string, string, string, error) {
	//logger.Info.Println("tablename:", sp.Sourcedb.Table)
	if !TableRename(sp.Sourcedb.Table) {
		return "", "", "", fmt.Errorf("表名不合法: %s", sp.Sourcedb.Table)
	}
	var hdfsURL, delimiter string
	var err error
	// 查询引擎信息放在外面了。。。
	engineinfo, err := dbutil.EngineInfo(sp.Sourcedb.Engineid)
	hiveDriverPath := engineinfo.DriverInfo.DriverPath
	// 获取hdfsURl
	if len(sp.Extractdb.Batchtables) == 0 {
		// 单例
		if err != nil {
			fmt.Printf("单例抽取获取引擎信息失败:%s", err.Error())
			return "", "", "", err
		}
		//sqltxt := fmt.Sprintf("desc database `%s`", sp.Sourcedb.Odsdatabase)
		sqltxt := fmt.Sprintf("show create table `%s`", sp.Sourcedb.Table)
		engineinfo.InitDB = sp.Sourcedb.Odsdatabase
		hdfsRes, err := collect.MGExecuteQuerySql(engineinfo, engineinfo.InitDB, sqltxt)
		if err != nil {
			fmt.Printf("批量抽取获取hdfs信息失败:%s,sql为%s", err.Error(), sqltxt)
			return "", "", "", err
		}
		for _, s := range hdfsRes {
			for _, m := range s {
				mstr := fmt.Sprintf("%v", m)
				if strings.Contains(mstr, "hdfs://") {
					hdfsURL = mstr
					break
				}
			}
		}
		fmt.Println("hdfsURL:", hdfsURL)
		hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
		//switch sp.Sourcedb.Dbtype {
		//case common.Hive:
		//	hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		//case common.Inceptor:
		//	hdfsURL = fmt.Sprintf("%s/%s/%s", hdfsURL, "hive", strings.ToLower(sp.Sourcedb.Table))
		//}
		//hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		sp.Sourcedb.HdfsURL = hdfsURL
		if sp.Sourcedb.JdbcURL == "" {
			sp.Sourcedb.JdbcURL = engineinfo.JdbcURL
		}

		if sp.Sourcedb.HiveFilePath == "" {
			sp.Sourcedb.HiveFilePath = engineinfo.HiveFilePath
		}
		if sp.Sourcedb.IsConfigKbs == "" {
			sp.Sourcedb.IsConfigKbs = engineinfo.IsConfigKbs
			sp.Sourcedb.KerberosInfo.KeyTabPath = engineinfo.KerberosInfo.KeyTabPath
			sp.Sourcedb.KerberosInfo.Principal = engineinfo.KerberosInfo.Principal
			sp.Sourcedb.KerberosInfo.Krb5ConfPath = engineinfo.KerberosInfo.Krb5ConfPath
			//sp.Sourcedb.Odsuser = engineinfo.Username
		}

	}
	hdfsURL = sp.Sourcedb.HdfsURL
	//writer的组建
	fmt.Println("hdfsURL:", hdfsURL)
	//把限制符都置为,
	delimiter = ","
	//----------------------------------
	//我也不知道为什么要加
	fmt.Println("看一下这个类型:", sp.FileType)
	if strings.Contains(sp.FileType, "Text") || strings.Contains(sp.FileType, "text") {
		delimiter = ","
	}

	fmt.Printf("delimiter:%#v\n", delimiter)
	hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
	// location:hdfs://dn100:8020/apps/hive/warehouse/odsdb.db/testmysql
	index := strings.Index(hdfsURL, "hdfs://")
	subres := hdfsURL[index+7:]
	pre := strings.Index(subres, "/")
	// hdfs数据库表路径
	hdfsURL = subres[pre:]
	// fmt.Println("url:", hdfsURL)
	hivewriter := common.HiveWriters
	sitePath := fmt.Sprintf("%s/hdfs-site.xml,%s/core-site.xml", sp.Sourcedb.HiveFilePath, sp.Sourcedb.HiveFilePath)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.hadoopConfigFilePath", sitePath)
	// hive writer 添加驱动的路径
	hivewriter, _ = sjson.Set(hivewriter, "parameter.driverJarPath", hiveDriverPath)
	// ---------------end------------------
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileName", sp.Sourcedb.Table)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fieldDelimiter", delimiter)
	if sp.Tasktype == 4 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.writeMode", "truncate")
		topic := fmt.Sprintf("db.%s.%s", sp.Sourcedb.Dbtype, sp.Sourcedb.Table)
		hdfsURL = hdfsURL + "/topics/" + topic
		hivewriter, _ = sjson.Set(hivewriter, "parameter.path", hdfsURL)
	} else {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.path", hdfsURL)
	}
	username := sp.Sourcedb.Odsuser
	if username == "" {
		// 使用默认用户
		username = "hive"
	}
	hivewriter, _ = sjson.Set(hivewriter, "parameter.username", username)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.password", sp.Sourcedb.Odspassword)
	//hivewrite
	tempTbName := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	connect := gin.H{
		"jdbcUrl": collect.InsteadDBname(sp.Sourcedb.JdbcURL, sp.Sourcedb.Odsdatabase),
		"table":   []string{tempTbName},
	}
	//从临时表插入存储表sql
	var insertSql, analyzesql string
	//区分ds创建的表和已有分区表的区分
	//如果选择了字段映射，要保证字段和临时表的字段顺序一致
	var createTemp, fieldsqltemp, dropTemp string
	//qnmd
	var dropOrisql string
	tb := sp.Sourcedb.Table + "_temp"
	dropTemp = fmt.Sprintf("DROP TABLE if exists `%s`.`%s`", sp.Sourcedb.Odsdatabase, tb)

	if sp.Extractdb.Createfield == "" && sp.GlobalSubmitInfo.ExtractOnlineStrategy == 0 && !sp.IsWorkflow {
		if sp.GlobalSubmitInfo.ConditionCover == "" {
			dropOrisql = fmt.Sprintf("truncate table `%s`.`%s`", sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table)
		}
	}
	createTemp = fmt.Sprintf("CREATE TABLE if not exists `%s`.`%s`", sp.Sourcedb.Odsdatabase, tb)
	if len(sp.Sourcedb.PartitionField) > 0 {
		var fieldSql1, tempSql, tbSql string
		if len(sp.TableRelation.Edges) > 0 {
			for _, v := range sp.TableRelation.Edges {
				if sp.Extractdb.DBType == "csv" || sp.Extractdb.DBType == "localdb" {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v.Target)
					for k, v1 := range sp.Sourcedb.Field {
						if v.Target == v1 {
							fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Target, sp.Sourcedb.Fieldtype[k])
						}
					}
				} else {
					var isPar bool
					for _, vv := range sp.Sourcedb.PartitionField {
						if v.Target == vv.Name {
							isPar = true
							break
						}
					}
					if !isPar {
						fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v.Target)
						//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v.Source)
						for k, v1 := range sp.Sourcedb.Field {
							if v.Target == v1 {
								fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Target, sp.Sourcedb.Fieldtype[k])
							}
						}

					}
				}
			}

		} else {
			for k, v := range sp.Sourcedb.Field {
				if sp.Extractdb.DBType == "csv" || sp.Extractdb.DBType == "localdb" {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v)
					//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v)
					fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v, sp.Sourcedb.Fieldtype[k])
				} else {
					var isPar bool
					for _, vv := range sp.Sourcedb.PartitionField {
						if v == vv.Name {
							isPar = true
							break
						}
					}
					if !isPar {
						fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v)
						//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v)
						fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v, sp.Sourcedb.Fieldtype[k])
					}
				}

			}
		}
		fieldSql1 = fieldSql1[1:]
		//fieldSql2 = fieldSql2[1:]
		tbSql = fieldSql1
		tempSql = fieldSql1
		fieldsqltemp = fieldsqltemp[2:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		var isMix = 0 //1:已有字段设置 2：系统设置 3：混合设置
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" || v.Ispreset == "customize" {
				if isMix == 2 {
					isMix = 3
					break
				} else {
					isMix = 1
				}
			} else {
				if isMix == 1 {
					isMix = 3
					break
				} else {
					isMix = 2
				}
			}
		}
		fmt.Println("temsql1:", tempSql)
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				if sp.Extractdb.DBType == "csv" || sp.Extractdb.DBType == "localdb" {

				} else {
					tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
					tempSql = fmt.Sprintf("%s,`%s`", tempSql, v.Name)
					fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
				}
				fmt.Println("temsql2:", tempSql)
			} else if v.Ispreset == "customize" {
				if sp.Extractdb.DBType == "csv" || sp.Extractdb.DBType == "localdb" {

				} else {
					tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
					tempSql = fmt.Sprintf("%s,%s as `%s`", tempSql, v.CustomizeRule.FormatSql, v.Name)
					// = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
				}
			} else {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				//fieldsqltemp = fmt.Sprintf(`%s, %s %s`, fieldsqltemp, v.Name, v.FieldType)
				if len(sp.Sourcedb.PartitionField) == 1 || isMix == 3 {
					//系统预设一级分区
					switch v.Ispreset {
					case "year":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
					case "month":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM') as ", v.Name)
					case "day":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd') as ", v.Name)
					case "customize":
						tempSql = fmt.Sprintf("%s,%s as `%s`", tempSql, v.CustomizeRule.FormatSql, v.Name)
					}
				} else {
					if isMix == 2 {
						switch v.Ispreset {
						case "year":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
						case "month":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'MM') as ", v.Name)
						case "day":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'dd') as ", v.Name)
						}
					}
				}
			}
		}
		// 临时表的预设字段放后面
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset != "notpreset" {
				fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
			}
		}
		fmt.Println("temsql3:", tempSql)
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		if sp.Sourcedb.IsCover {
			//
			if sp.Sourcedb.ConditionCover != "" {
				//条件覆盖
				hivewriter, _ = sjson.Set(hivewriter, "parameter.coverMode", "partition")
				insertSql = fmt.Sprintf("alter table `%s` drop if exists PARTITION (%s);insert overwrite table `%s` partition(%s) select %s from `%s` ", sp.Sourcedb.Table, sp.Sourcedb.ConditionCover, sp.Sourcedb.Table, sp.Sourcedb.ConditionCover, tempSql, tempTb)
			} else {
				//全量覆盖
				hivewriter, _ = sjson.Set(hivewriter, "parameter.coverMode", "all")
				insertSql = fmt.Sprintf("insert into table `%s` (%s) select %s from `%s` ", sp.Sourcedb.Table, tbSql, tempSql, tempTb)
			}

		} else if sp.GlobalSubmitInfo.ExtractOnlineStrategy == 0 {
			//单表采集
			if sp.GlobalSubmitInfo.ConditionCover != "" {
				//条件覆盖
				hivewriter, _ = sjson.Set(hivewriter, "parameter.coverMode", "partition")
				insertSql = fmt.Sprintf("alter table `%s` drop if exists PARTITION (%s);insert overwrite table `%s` partition(%s) select %s from `%s` ", sp.Sourcedb.Table, sp.Sourcedb.ConditionCover, sp.Sourcedb.Table, sp.Sourcedb.ConditionCover, tempSql, tempTb)
			} else {
				//全量覆盖
				hivewriter, _ = sjson.Set(hivewriter, "parameter.coverMode", "all")
				insertSql = fmt.Sprintf("insert into table `%s` (%s) select %s from `%s` ", sp.Sourcedb.Table, tbSql, tempSql, tempTb)
			}
		} else {
			insertSql = fmt.Sprintf("insert into table `%s` (%s) select %s from `%s` ", sp.Sourcedb.Table, tbSql, tempSql, tempTb)
		}
		//insertSql = fmt.Sprintf("insert into table `%s` (%s) select %s from `%s` ", sp.Sourcedb.Table, tbSql, tempSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		var fieldSql string
		for _, v := range sp.Sourcedb.Field {
			fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
		}
		fieldSql = fieldSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s` (%s) select %s from `%s` ", sp.Sourcedb.Table, fieldSql, fieldSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else {
		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	}
	//getSql := []string{insertSql, analyzesql}
	createTemp = fmt.Sprintf(`%s (%s) %s LOCATION '%s'`, createTemp, fieldsqltemp, " stored as orc", hdfsURL+"_temp")
	if len(sp.Sourcedb.PartitionField) > 0 || len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), insertSql)
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(1), analyzesql)
		// 获取临时表sqltxt
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": sp.Sourcedb.TableID,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		resp, err := escli.NewESClientCl.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			fmt.Printf("例举存储源表失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var tbhit metadata.MetaSourceTbHits
		err = json.Unmarshal([]byte(resp), &tbhit)
		if err != nil {
			fmt.Printf("解析表数据失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var sqlTxtTemp string
		var tbinfoNew metadata.MetaSourceTb
		for _, v := range tbhit.Hits1.Hits2 {
			//fmt.Println("--------------------------------")
			//fmt.Println(sp.Sourcedb.Table)
			//fmt.Println(v.MetaSourceTbSource.TBName)
			//fmt.Println(v.MetaSourceTbSource.SqlTxtTemp)
			if sp.Sourcedb.Table == v.MetaSourceTbSource.TBName {
				sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTemp
				if sqlTxtTemp == "" {
					sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTempNew
				}
				tbinfoNew = v.MetaSourceTbSource
			}
		}
		//如果非ds创建分区表，sqltxttemp无内容，需要组合sql语句
		if sqlTxtTemp == "" {
			eninfo, err := dbutil.EngineInfo(tbinfoNew.EngineID)
			if err != nil {
				//logger.Error.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				fmt.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				return "", "", "", fmt.Errorf("根据engineid获取引擎信息失败:%s", err.Error())
			}
			//获取topic -- 分区创建表的路径
			tbinfoNew.RealTimeTB.SourcedbTableName = tbinfoNew.TBName
			tbinfoNew.RealTimeTB.SourcedbType = tbinfoNew.DBType
			tbinfoNew.RealTimeTB.SourcedbTableID = tbinfoNew.ID
			var topics string
			if tbinfoNew.IsRT {
				info := collect.TopicInfo(&tbinfoNew)
				topics = info.DbServerName
			}
			sqltxt, sqltxtTemp := dbutil.HiveTbSQL(&tbinfoNew, eninfo.EngineName, topics, common.Hive)
			if sqltxt == "" {
				fmt.Printf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
				return hivewriter, hdfsURL, analyzesql, fmt.Errorf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
			}
			sqlTxtTemp = sqltxtTemp
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.preSql."+strconv.Itoa(0), dropTemp)
		hivewriter, _ = sjson.Set(hivewriter, "parameter.preSql."+strconv.Itoa(1), createTemp)
		if dropOrisql != "" {
			hivewriter, _ = sjson.Set(hivewriter, "parameter.preSql."+strconv.Itoa(2), dropOrisql)
		}
	} else {
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), analyzesql)
	}

	hivewriter, _ = sjson.Set(hivewriter, "parameter.connection."+strconv.Itoa(0), connect)

	fileType := "text"
	if strings.Contains(sp.Sourcedb.FileType, "rc") {
		fileType = common.TypeOrc
	}
	if fileType == "text" {
		fileType = "csv"
	}
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileType", fileType)
	var lenField int
	if len(sp.Sourcedb.Field) > 0 {
		if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) {
			return hivewriter, hdfsURL, analyzesql, fmt.Errorf("目的源字段数量与字段类型数量不一致：%d, %d", len(sp.Sourcedb.Field), len(sp.Sourcedb.Fieldtype))
		}
		if len(sp.TableRelation.Edges) > 0 {
			j := 0
			for i, v := range sp.TableRelation.Edges { //映射字段 key weight length
				if sp.Extractdb.DBType == "csv" || sp.Extractdb.DBType == "localdb" {
					for i := 0; i < len(sp.Sourcedb.Field); i++ { //目的端所有字段
						if v.Target == sp.Sourcedb.Field[i] {
							record := gin.H{
								"name": v.Target,
								"type": sp.Sourcedb.Fieldtype[i],
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
						}
					}
					j = j + 1
				} else {
					var isPar bool
					for _, v1 := range sp.Sourcedb.PartitionField {
						if v.Target == v1.Name {
							isPar = true
						}
					}
					if !isPar {
						for i := 0; i < len(sp.Sourcedb.Field); i++ { //目的端所有字段
							if v.Target == sp.Sourcedb.Field[i] {
								record := gin.H{
									"name": v.Target,
									"type": sp.Sourcedb.Fieldtype[i],
								}
								hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							}
						}
						j = j + 1
					}
				}

				if i+1 == len(sp.TableRelation.Edges) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				//hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j),
				//	"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}
			lenField = len(sp.TableRelation.Edges)
		} else {
			j := 0
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				if sp.Extractdb.DBType == "csv" || sp.Extractdb.DBType == "localdb" {
					record := gin.H{
						"name": sp.Sourcedb.Field[i],
						"type": sp.Sourcedb.Fieldtype[i],
					}
					hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
					j = j + 1
				} else {
					var isPar bool
					//这里所有分区字段不加入parameter.column结构体中
					for _, v1 := range sp.Sourcedb.PartitionField {
						if sp.Sourcedb.Field[i] == v1.Name {
							isPar = true
						}
					}
					if !isPar {
						record := gin.H{
							"name": sp.Sourcedb.Field[i],
							"type": sp.Sourcedb.Fieldtype[i],
						}
						hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
						j = j + 1
					}
				}

				if i+1 == len(sp.Sourcedb.Field) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				/*record := gin.H{
					"name": sp.Sourcedb.Field[i],
					"type": sp.Sourcedb.Fieldtype[i],
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(i), record)*/
			}
			// lenField = len(sp.Sourcedb.Field)
			lenField = j
		}
	}
	//hive分区的设置
	//由于只是传给datax的，不需要进行规则修改
	var parRecord = gin.H{}
	if len(sp.Sourcedb.PartitionField) > 0 {
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset != "notpreset" {
				switch v.Ispreset {
				case "year":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy')",
						"type": v.FieldType,
					}
				case "month":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM')",
						"type": v.FieldType,
					}
				case "day":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd')",
						"type": v.FieldType,
					}
				case "customize":
					parRecord = gin.H{
						"name": fmt.Sprintf("%s", v.Name),
						"type": "string",
					}
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(lenField), parRecord)
				lenField++
			}
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	}
	//
	js, err := simplejson.NewJson([]byte(hivewriter))
	if err != nil {
		return hivewriter, hdfsURL, analyzesql, err
	}
	if sp.Sourcedb.IsConfigKbs == "1" {
		js.SetPath([]string{"parameter", "haveKerberos"}, true)
		js.SetPath([]string{"parameter", "kerberosKeytabFilePath"}, sp.Sourcedb.KerberosInfo.KeyTabPath)
		js.SetPath([]string{"parameter", "kerberosPrincipal"}, sp.Sourcedb.KerberosInfo.Principal)
		js.SetPath([]string{"parameter", "krb5ConfigFilePath"}, sp.Sourcedb.KerberosInfo.Krb5ConfPath)
	}
	encode, _ := js.Encode()
	hivewriter = string(encode)
	return hivewriter, hdfsURL, analyzesql, nil
}

// hiveWriterJSON 抽取任务构建datax writer json结构体 存储源：hive
func hiveReportWriterJSON(sp *source.DataReport) (string, string, string, error) {
	logger.Info.Println("tablename:", sp.Sourcedb.Table)
	if !TableRename(sp.Sourcedb.Table) {
		return "", "", "", fmt.Errorf("表名不合法: %s", sp.Sourcedb.Table)
	}
	var hdfsURL, delimiter string
	var err error
	// 查询引擎信息放在外面了。。。
	engineinfo, err := dbutil.EngineInfo(sp.Sourcedb.Engineid)
	hiveDriverPath := engineinfo.DriverInfo.DriverPath
	// 获取hdfsURl
	if len(sp.Extractdb.Batchtables) == 0 {
		// 单例
		if err != nil {
			logger.Error.Printf("单例抽取获取引擎信息失败:%s", err.Error())
			return "", "", "", err
		}
		sqltxt := fmt.Sprintf("desc database `%s`", sp.Sourcedb.Odsdatabase)
		engineinfo.InitDB = sp.Sourcedb.Odsdatabase
		hdfsRes, err := collect.MGExecuteQuerySql(engineinfo, engineinfo.InitDB, sqltxt)
		if err != nil {
			logger.Error.Printf("批量抽取获取hdfs信息失败:%s,sql为%s", err.Error(), sqltxt)
			return "", "", "", err
		}
		for _, s := range hdfsRes {
			for _, m := range s {
				mstr := fmt.Sprintf("%v", m)
				if strings.Contains(mstr, "hdfs://") {
					hdfsURL = mstr
					break
				}
			}
		}
		fmt.Println("hdfsURL:", hdfsURL)
		hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
		switch sp.Sourcedb.Dbtype {
		case common.Hive:
			hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		case common.Inceptor:
			hdfsURL = fmt.Sprintf("%s/%s/%s", hdfsURL, "hive", strings.ToLower(sp.Sourcedb.Table))
		}
		//hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		sp.Sourcedb.HdfsURL = hdfsURL
		if sp.Sourcedb.JdbcURL == "" {
			sp.Sourcedb.JdbcURL = engineinfo.JdbcURL
		}

		if sp.Sourcedb.HiveFilePath == "" {
			sp.Sourcedb.HiveFilePath = engineinfo.HiveFilePath
		}
		if sp.Sourcedb.IsConfigKbs == "" {
			sp.Sourcedb.IsConfigKbs = engineinfo.IsConfigKbs
			sp.Sourcedb.KerberosInfo.KeyTabPath = engineinfo.KerberosInfo.KeyTabPath
			sp.Sourcedb.KerberosInfo.Principal = engineinfo.KerberosInfo.Principal
			sp.Sourcedb.KerberosInfo.Krb5ConfPath = engineinfo.KerberosInfo.Krb5ConfPath
			//sp.Sourcedb.Odsuser = engineinfo.Username
		}

	}
	hdfsURL = sp.Sourcedb.HdfsURL
	//writer的组建
	logger.Info.Println("hdfsURL:", hdfsURL)
	//把限制符都置为,
	delimiter = ","
	//----------------------------------
	//我也不知道为什么要加

	fmt.Printf("delimiter:%#v\n", delimiter)
	hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
	// location:hdfs://dn100:8020/apps/hive/warehouse/odsdb.db/testmysql
	index := strings.Index(hdfsURL, "hdfs://")
	subres := hdfsURL[index+7:]
	pre := strings.Index(subres, "/")
	// hdfs数据库表路径
	hdfsURL = subres[pre:]
	// fmt.Println("url:", hdfsURL)
	hivewriter := common.HiveWriters
	sitePath := fmt.Sprintf("%s/hdfs-site.xml,%s/core-site.xml", sp.Sourcedb.HiveFilePath, sp.Sourcedb.HiveFilePath)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.hadoopConfigFilePath", sitePath)
	// hive writer 添加驱动的路径
	hivewriter, _ = sjson.Set(hivewriter, "parameter.driverJarPath", hiveDriverPath)
	// ---------------end------------------
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileName", sp.Sourcedb.Table)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fieldDelimiter", delimiter)
	{
		hivewriter, _ = sjson.Set(hivewriter, "parameter.path", hdfsURL)
	}
	username := sp.Sourcedb.Odsuser
	if username == "" {
		// 使用默认用户
		username = "hive"
	}
	hivewriter, _ = sjson.Set(hivewriter, "parameter.username", username)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.password", sp.Sourcedb.Odspassword)
	//hivewrite
	tempTbName := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	connect := gin.H{
		"jdbcUrl": collect.InsteadDBname(sp.Sourcedb.JdbcURL, sp.Sourcedb.Odsdatabase),
		"table":   []string{tempTbName},
	}
	//从临时表插入存储表sql
	var insertSql, analyzesql string
	//区分ds创建的表和已有分区表的区分
	//如果选择了字段映射，要保证字段和临时表的字段顺序一致
	var createTemp, fieldsqltemp string
	tb := sp.Sourcedb.Table + "_temp"
	createTemp = fmt.Sprintf("CREATE TABLE if not exists `%s`.`%s`", sp.Sourcedb.Odsdatabase, tb)
	if len(sp.Sourcedb.PartitionField) > 0 {
		var fieldSql1, tempSql, tbSql string
		if len(sp.TableRelation.Edges) > 0 {
			for _, v := range sp.TableRelation.Edges {
				var isPar bool
				for _, vv := range sp.Sourcedb.PartitionField {
					if v.Target == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v.Target)
					//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v.Source)
					for k, v1 := range sp.Sourcedb.Field {
						if v.Target == v1 {
							fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Target, sp.Sourcedb.Fieldtype[k])
						}
					}

				}
			}

		} else {
			for k, v := range sp.Sourcedb.Field {
				var isPar bool
				for _, vv := range sp.Sourcedb.PartitionField {
					if v == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v)
					//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v)
					fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v, sp.Sourcedb.Fieldtype[k])
				}
			}
		}
		fieldSql1 = fieldSql1[1:]
		//fieldSql2 = fieldSql2[1:]
		tbSql = fieldSql1
		tempSql = fieldSql1
		fieldsqltemp = fieldsqltemp[2:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		var isMix = 0
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				if isMix == 2 {
					isMix = 3
					break
				} else {
					isMix = 1
				}
			} else {
				if isMix == 1 {
					isMix = 3
					break
				} else {
					isMix = 2
				}
			}
		}
		fmt.Println("temsql1:", tempSql)
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				tempSql = fmt.Sprintf("%s,`%s`", tempSql, v.Name)
				fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
				fmt.Println("temsql2:", tempSql)
			} else {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				//fieldsqltemp = fmt.Sprintf(`%s, %s %s`, fieldsqltemp, v.Name, v.FieldType)
				if len(sp.Sourcedb.PartitionField) == 1 || isMix == 3 {
					//系统预设一级分区
					switch v.Ispreset {
					case "year":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
					case "month":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM') as ", v.Name)
					case "day":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd') as ", v.Name)
					case "customize":
						tempSql = fmt.Sprintf("%s,%s as `%s`", tempSql, v.CustomizeRule.FormatSql, v.Name)
					}
				} else {
					if isMix == 2 {
						switch v.Ispreset {
						case "year":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
						case "month":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'MM') as ", v.Name)
						case "day":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'dd') as ", v.Name)
						case "customize":
							tempSql = fmt.Sprintf("%s,%s as `%s`", tempSql, v.CustomizeRule.FormatSql, v.Name)
						}
					}
				}
			}
		}
		// 临时表的预设字段放后面
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset != "notpreset" {
				fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
			}
		}
		fmt.Println("temsql3:", tempSql)
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)

		insertSql = fmt.Sprintf("insert into table `%s` (%s) select %s from `%s` ", sp.Sourcedb.Table, tbSql, tempSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		var fieldSql string
		for _, v := range sp.Sourcedb.Field {
			fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
		}
		fieldSql = fieldSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s` (%s) select %s from `%s` ", sp.Sourcedb.Table, fieldSql, fieldSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else {
		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	}
	//getSql := []string{insertSql, analyzesql}
	createTemp = fmt.Sprintf(`%s (%s) %s LOCATION '%s'`, createTemp, fieldsqltemp, " stored as orc", hdfsURL+"_temp")
	if len(sp.Sourcedb.PartitionField) > 0 || len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), insertSql)
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(1), analyzesql)
		// 获取临时表sqltxt
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": sp.Sourcedb.TableID,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		resp, err := escli.NewESClientCl.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("例举存储源表失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var tbhit metadata.MetaSourceTbHits
		err = json.Unmarshal([]byte(resp), &tbhit)
		if err != nil {
			logger.Error.Printf("解析表数据失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var sqlTxtTemp string
		var tbinfoNew metadata.MetaSourceTb
		for _, v := range tbhit.Hits1.Hits2 {
			//fmt.Println("--------------------------------")
			//fmt.Println(sp.Sourcedb.Table)
			//fmt.Println(v.MetaSourceTbSource.TBName)
			//fmt.Println(v.MetaSourceTbSource.SqlTxtTemp)
			if sp.Sourcedb.Table == v.MetaSourceTbSource.TBName {
				sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTemp
				if sqlTxtTemp == "" {
					sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTempNew
				}
				tbinfoNew = v.MetaSourceTbSource
			}
		}
		//如果非ds创建分区表，sqltxttemp无内容，需要组合sql语句
		if sqlTxtTemp == "" {
			eninfo, err := dbutil.EngineInfo(tbinfoNew.EngineID)
			if err != nil {
				//logger.Error.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				fmt.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				return "", "", "", fmt.Errorf("根据engineid获取引擎信息失败:%s", err.Error())
			}
			//获取topic -- 分区创建表的路径
			tbinfoNew.RealTimeTB.SourcedbTableName = tbinfoNew.TBName
			tbinfoNew.RealTimeTB.SourcedbType = tbinfoNew.DBType
			tbinfoNew.RealTimeTB.SourcedbTableID = tbinfoNew.ID
			var topics string
			if tbinfoNew.IsRT {
				info := collect.TopicInfo(&tbinfoNew)
				topics = info.DbServerName
			}
			sqltxt, sqltxtTemp := dbutil.HiveTbSQL(&tbinfoNew, eninfo.EngineName, topics, common.Hive)
			if sqltxt == "" {
				fmt.Printf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
				return hivewriter, hdfsURL, analyzesql, fmt.Errorf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
			}
			sqlTxtTemp = sqltxtTemp
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.preSql."+strconv.Itoa(0), createTemp)
	} else {
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), analyzesql)
	}

	hivewriter, _ = sjson.Set(hivewriter, "parameter.connection."+strconv.Itoa(0), connect)

	fileType := "text"
	if strings.Contains(sp.Sourcedb.FileType, "rc") {
		fileType = common.TypeOrc
	}
	if fileType == "text" {
		fileType = "csv"
	}
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileType", fileType)
	var lenField int
	if len(sp.Sourcedb.Field) > 0 {
		if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) {
			return hivewriter, hdfsURL, analyzesql, fmt.Errorf("目的源字段数量与字段类型数量不一致：%d, %d", len(sp.Sourcedb.Field), len(sp.Sourcedb.Fieldtype))
		}
		if len(sp.TableRelation.Edges) > 0 {
			j := 0
			for i, v := range sp.TableRelation.Edges { //映射字段 key weight length
				var isPar bool
				for _, v1 := range sp.Sourcedb.PartitionField {
					if v.Target == v1.Name {
						isPar = true
					}
				}
				if !isPar {
					for i := 0; i < len(sp.Sourcedb.Field); i++ { //目的端所有字段
						if v.Target == sp.Sourcedb.Field[i] {
							record := gin.H{
								"name": v.Target,
								"type": sp.Sourcedb.Fieldtype[i],
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
						}
					}
					j = j + 1
				}
				if i+1 == len(sp.TableRelation.Edges) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				//hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j),
				//	"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}
			lenField = len(sp.TableRelation.Edges)
		} else {
			j := 0
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				var isPar bool
				for _, v1 := range sp.Sourcedb.PartitionField {
					if sp.Sourcedb.Field[i] == v1.Name {
						isPar = true
					}
				}
				if !isPar {
					record := gin.H{
						"name": sp.Sourcedb.Field[i],
						"type": sp.Sourcedb.Fieldtype[i],
					}
					hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
					j = j + 1
				}
				if i+1 == len(sp.Sourcedb.Field) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				/*record := gin.H{
					"name": sp.Sourcedb.Field[i],
					"type": sp.Sourcedb.Fieldtype[i],
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(i), record)*/
			}
			lenField = len(sp.Sourcedb.Field)
		}
	}
	//hive分区的设置
	//由于只是传给datax的，不需要进行规则修改
	var parRecord = gin.H{}
	if len(sp.Sourcedb.PartitionField) > 0 {
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset != "notpreset" {
				switch v.Ispreset {
				case "year":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy')",
						"type": v.FieldType,
					}
				case "month":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM')",
						"type": v.FieldType,
					}
				case "day":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd')",
						"type": v.FieldType,
					}
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(lenField), parRecord)
				lenField++
			}
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	}
	//
	js, err := simplejson.NewJson([]byte(hivewriter))
	if err != nil {
		return hivewriter, hdfsURL, analyzesql, err
	}
	if sp.Sourcedb.IsConfigKbs == "1" {
		js.SetPath([]string{"parameter", "haveKerberos"}, true)
		js.SetPath([]string{"parameter", "kerberosKeytabFilePath"}, sp.Sourcedb.KerberosInfo.KeyTabPath)
		js.SetPath([]string{"parameter", "kerberosPrincipal"}, sp.Sourcedb.KerberosInfo.Principal)
		js.SetPath([]string{"parameter", "krb5ConfigFilePath"}, sp.Sourcedb.KerberosInfo.Krb5ConfPath)
	}
	encode, _ := js.Encode()
	hivewriter = string(encode)
	return hivewriter, hdfsURL, analyzesql, nil
}

func TdhWriterJSON(sp *source.NewAcc) (string, string, string, error) {
	return tdhWriterJSON(sp)
}

// tdh 6.2版本较老，insert into 语句适配
func tdhWriterJSON(sp *source.NewAcc) (string, string, string, error) {
	//logger.Info.Println("tablename:", sp.Sourcedb.Table)
	if !TableRename(sp.Sourcedb.Table) {
		return "", "", "", fmt.Errorf("表名不合法: %s", sp.Sourcedb.Table)
	}
	var hdfsURL, delimiter string
	var err error
	// 查询引擎信息放在外面了。。。
	engineinfo, err := dbutil.EngineInfo(sp.Sourcedb.Engineid)
	hiveDriverPath := engineinfo.DriverInfo.DriverPath
	// 获取hdfsURl
	if len(sp.Extractdb.Batchtables) == 0 {
		// 单例
		if err != nil {
			fmt.Printf("单例抽取获取引擎信息失败:%s", err.Error())
			return "", "", "", err
		}
		sqltxt := fmt.Sprintf("desc database `%s`", sp.Sourcedb.Odsdatabase)
		engineinfo.InitDB = sp.Sourcedb.Odsdatabase
		hdfsRes, err := collect.MGExecuteQuerySql(engineinfo, engineinfo.InitDB, sqltxt)
		if err != nil {
			fmt.Printf("批量抽取获取hdfs信息失败:%s,sql为%s", err.Error(), sqltxt)
			return "", "", "", err
		}
		for _, s := range hdfsRes {
			for _, m := range s {
				mstr := fmt.Sprintf("%v", m)
				if strings.Contains(mstr, "hdfs://") {
					hdfsURL = mstr
					break
				}
			}
		}
		fmt.Println("hdfsURL:", hdfsURL)
		hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
		switch sp.Sourcedb.Dbtype {
		case common.Hive:
			hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		case common.Inceptor:
			hdfsURL = fmt.Sprintf("%s/%s/%s", hdfsURL, "hive", strings.ToLower(sp.Sourcedb.Table))
		}
		//hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		sp.Sourcedb.HdfsURL = hdfsURL
	}
	hdfsURL = sp.Sourcedb.HdfsURL
	//writer的组建
	fmt.Println("hdfsURL:", hdfsURL)
	//把限制符都置为,
	delimiter = ","
	//----------------------------------
	//我也不知道为什么要加
	fmt.Println("看一下这个类型:", sp.FileType)
	if strings.Contains(sp.FileType, "Text") || strings.Contains(sp.FileType, "text") {
		delimiter = ","
	}

	fmt.Printf("delimiter:%#v\n", delimiter)
	hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
	// location:hdfs://dn100:8020/apps/hive/warehouse/odsdb.db/testmysql
	index := strings.Index(hdfsURL, "hdfs://")
	subres := hdfsURL[index+7:]
	pre := strings.Index(subres, "/")
	// hdfs数据库表路径
	hdfsURL = subres[pre:]
	// fmt.Println("url:", hdfsURL)
	hivewriter := common.HiveWriters
	sitePath := fmt.Sprintf("%s/hdfs-site.xml,%s/core-site.xml", sp.Sourcedb.HiveFilePath, sp.Sourcedb.HiveFilePath)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.hadoopConfigFilePath", sitePath)
	// hive writer 添加驱动的路径
	hivewriter, _ = sjson.Set(hivewriter, "parameter.driverJarPath", hiveDriverPath)
	// ---------------end------------------
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileName", sp.Sourcedb.Table)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fieldDelimiter", delimiter)
	if sp.Tasktype == 4 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.writeMode", "truncate")
		topic := fmt.Sprintf("db.%s.%s", sp.Sourcedb.Dbtype, sp.Sourcedb.Table)
		hdfsURL = hdfsURL + "/topics/" + topic
		hivewriter, _ = sjson.Set(hivewriter, "parameter.path", hdfsURL)
	} else {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.path", hdfsURL)
	}
	username := sp.Sourcedb.Odsuser
	if username == "" {
		// 使用默认用户
		username = "hive"
	}
	hivewriter, _ = sjson.Set(hivewriter, "parameter.username", username)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.password", sp.Sourcedb.Odspassword)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.clusterName", "tdh")
	//hivewrite
	tempTbName := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	connect := gin.H{
		"jdbcUrl": collect.InsteadDBname(sp.Sourcedb.JdbcURL, sp.Sourcedb.Odsdatabase),
		"table":   []string{tempTbName},
	}
	//从临时表插入存储表sql
	var insertSql, analyzesql string
	//区分ds创建的表和已有分区表的区分
	//如果选择了字段映射，要保证字段和临时表的字段顺序一致
	var createTemp, fieldsqltemp string
	tb := sp.Sourcedb.Table + "_temp"
	createTemp = fmt.Sprintf("CREATE TABLE if not exists `%s`.`%s`", sp.Sourcedb.Odsdatabase, tb)
	if len(sp.Sourcedb.PartitionField) > 0 {
		var fieldSql1, tempSql, tbSql string
		if len(sp.TableRelation.Edges) > 0 {
			for _, v := range sp.TableRelation.Edges {
				var isPar bool
				for _, vv := range sp.Sourcedb.PartitionField {
					if v.Target == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v.Target)
					//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v.Source)
					for k, v1 := range sp.Sourcedb.Field {
						if v.Target == v1 {
							fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Target, sp.Sourcedb.Fieldtype[k])
						}
					}

				}
			}

		} else {
			for k, v := range sp.Sourcedb.Field {
				var isPar bool
				for _, vv := range sp.Sourcedb.PartitionField {
					if v == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v)
					//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v)
					fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v, sp.Sourcedb.Fieldtype[k])
				}
			}
		}
		fieldSql1 = fieldSql1[1:]
		//fieldSql2 = fieldSql2[1:]
		//tbSql = fieldSql1
		tempSql = fieldSql1
		fieldsqltemp = fieldsqltemp[2:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		var isMix = 0
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				if isMix == 2 {
					isMix = 3
					break
				} else {
					isMix = 1
				}
			} else {
				if isMix == 1 {
					isMix = 3
					break
				} else {
					isMix = 2
				}
			}
		}
		fmt.Println("temsql1:", tempSql)
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				tempSql = fmt.Sprintf("%s,`%s`", tempSql, v.Name)
				fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
				fmt.Println("temsql2:", tempSql)
			} else {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
				if len(sp.Sourcedb.PartitionField) == 1 || isMix == 3 {
					//系统预设一级分区
					switch v.Ispreset {
					case "year":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
					case "month":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM') as ", v.Name)
					case "day":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd') as ", v.Name)
					}
				} else {
					if isMix == 2 {
						switch v.Ispreset {
						case "year":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
						case "month":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'MM') as ", v.Name)
						case "day":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'dd') as ", v.Name)
						}
					}
				}
			}
		}
		fmt.Println("temsql3:", tempSql)
		tbSql = tbSql[1:]
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s` partition(%s) select %s from `%s` ", sp.Sourcedb.Table, tbSql, tempSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		var fieldSql string
		for _, v := range sp.Sourcedb.PartitionField {
			fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
		}
		fieldSql = fieldSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s` partition(%s) select %s from `%s` ", sp.Sourcedb.Table, fieldSql, fieldSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else {
		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	}
	//getSql := []string{insertSql, analyzesql}
	createTemp = fmt.Sprintf(`%s (%s) %s`, createTemp, fieldsqltemp, " stored as orc")
	if len(sp.Sourcedb.PartitionField) > 0 || len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), "set hive.exec.dynamic.partition = true")
		hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(1), insertSql)
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(1), analyzesql)
		// 获取临时表sqltxt
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": sp.Sourcedb.TableID,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		resp, err := escli.NewESClientCl.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			fmt.Printf("例举存储源表失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var tbhit metadata.MetaSourceTbHits
		err = json.Unmarshal([]byte(resp), &tbhit)
		if err != nil {
			fmt.Printf("解析表数据失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var sqlTxtTemp string
		var tbinfoNew metadata.MetaSourceTb
		for _, v := range tbhit.Hits1.Hits2 {
			//fmt.Println("--------------------------------")
			//fmt.Println(sp.Sourcedb.Table)
			//fmt.Println(v.MetaSourceTbSource.TBName)
			//fmt.Println(v.MetaSourceTbSource.SqlTxtTemp)
			if sp.Sourcedb.Table == v.MetaSourceTbSource.TBName {
				sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTemp
				if sqlTxtTemp == "" {
					sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTempNew
				}
				tbinfoNew = v.MetaSourceTbSource
				//字段保持一致
				var tmp []metadata.MetaSourceFieldInfo
				for _, source1 := range sp.Sourcedb.Field {
					for _, source2 := range v.MetaSourceTbSource.FieldInfo {
						if source1 == source2.Name {
							tmp = append(tmp, source2)
							break
						}
					}
				}
				tbinfoNew.FieldInfo = tmp
			}
		}
		//如果非ds创建分区表，sqltxttemp无内容，需要组合sql语句
		if sqlTxtTemp == "" {
			eninfo, err := dbutil.EngineInfo(tbinfoNew.EngineID)
			if err != nil {
				//logger.Error.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				fmt.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				return "", "", "", fmt.Errorf("根据engineid获取引擎信息失败:%s", err.Error())
			}
			//获取topic -- 分区创建表的路径
			tbinfoNew.RealTimeTB.SourcedbTableName = tbinfoNew.TBName
			tbinfoNew.RealTimeTB.SourcedbType = tbinfoNew.DBType
			tbinfoNew.RealTimeTB.SourcedbTableID = tbinfoNew.ID
			var topics string
			if tbinfoNew.IsRT {
				info := collect.TopicInfo(&tbinfoNew)
				topics = info.DbServerName
			}
			sqltxt, sqltxtTemp := dbutil.HiveTbSQL(&tbinfoNew, eninfo.EngineName, topics, common.Hive)
			if sqltxt == "" {
				fmt.Printf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
				return hivewriter, hdfsURL, analyzesql, fmt.Errorf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
			}
			sqlTxtTemp = sqltxtTemp
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.preSql."+strconv.Itoa(0), createTemp)
	} else {
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), analyzesql)
	}

	hivewriter, _ = sjson.Set(hivewriter, "parameter.connection."+strconv.Itoa(0), connect)

	fileType := "text"
	if strings.Contains(sp.Sourcedb.FileType, "rc") {
		fileType = common.TypeOrc
	}
	// filetype 就是text
	/*	if fileType == "text" {
		fileType = "csv"
	}*/
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileType", fileType)
	var lenField int
	if len(sp.Sourcedb.Field) > 0 {
		if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) {
			return hivewriter, hdfsURL, analyzesql, fmt.Errorf("目的源字段数量与字段类型数量不一致：%d, %d", len(sp.Sourcedb.Field), len(sp.Sourcedb.Fieldtype))
		}
		if len(sp.TableRelation.Edges) > 0 {
			j := 0
			for i, v := range sp.TableRelation.Edges { //映射字段 key weight length
				var isPar bool
				for _, v1 := range sp.Sourcedb.PartitionField {
					if v.Target == v1.Name {
						isPar = true
					}
				}
				if !isPar {
					for i := 0; i < len(sp.Sourcedb.Field); i++ { //目的端所有字段
						if v.Target == sp.Sourcedb.Field[i] {
							record := gin.H{
								"name": v.Target,
								"type": sp.Sourcedb.Fieldtype[i],
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
						}
					}
					j = j + 1
				}
				if i+1 == len(sp.TableRelation.Edges) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				//hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j),
				//	"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}
			lenField = len(sp.TableRelation.Edges)
		} else {
			j := 0
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				var isPar bool
				for _, v1 := range sp.Sourcedb.PartitionField {
					if sp.Sourcedb.Field[i] == v1.Name {
						isPar = true
					}
				}
				if !isPar {
					record := gin.H{
						"name": sp.Sourcedb.Field[i],
						"type": sp.Sourcedb.Fieldtype[i],
					}
					hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
					j = j + 1
				}
				if i+1 == len(sp.Sourcedb.Field) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				/*record := gin.H{
					"name": sp.Sourcedb.Field[i],
					"type": sp.Sourcedb.Fieldtype[i],
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(i), record)*/
			}
			lenField = len(sp.Sourcedb.Field)
		}
	}
	//hive分区的设置
	//由于只是传给datax的，不需要进行规则修改
	var parRecord = gin.H{}
	if len(sp.Sourcedb.PartitionField) > 0 {
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset != "notpreset" {
				switch v.Ispreset {
				case "year":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy')",
						"type": v.FieldType,
					}
				case "month":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM')",
						"type": v.FieldType,
					}
				case "day":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd')",
						"type": v.FieldType,
					}
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(lenField), parRecord)
				lenField++
			}
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	}
	//
	js, err := simplejson.NewJson([]byte(hivewriter))
	if err != nil {
		return hivewriter, hdfsURL, analyzesql, err
	}
	if sp.Sourcedb.IsConfigKbs == "1" {
		js.SetPath([]string{"parameter", "haveKerberos"}, true)
		js.SetPath([]string{"parameter", "kerberosKeytabFilePath"}, sp.Sourcedb.KerberosInfo.KeyTabPath)
		js.SetPath([]string{"parameter", "kerberosPrincipal"}, sp.Sourcedb.KerberosInfo.Principal)
		js.SetPath([]string{"parameter", "krb5ConfigFilePath"}, sp.Sourcedb.KerberosInfo.Krb5ConfPath)
	}
	encode, _ := js.Encode()
	hivewriter = string(encode)
	return hivewriter, hdfsURL, analyzesql, nil
}

// tdh 6.2版本较老，insert into 语句适配
func tdhReportWriterJSON(sp *source.DataReport) (string, string, string, error) {
	logger.Info.Println("tablename:", sp.Sourcedb.Table)
	if !TableRename(sp.Sourcedb.Table) {
		return "", "", "", fmt.Errorf("表名不合法: %s", sp.Sourcedb.Table)
	}
	var hdfsURL, delimiter string
	var err error
	// 查询引擎信息放在外面了。。。
	engineinfo, err := dbutil.EngineInfo(sp.Sourcedb.Engineid)
	hiveDriverPath := engineinfo.DriverInfo.DriverPath
	// 获取hdfsURl
	if len(sp.Extractdb.Batchtables) == 0 {
		// 单例
		if err != nil {
			logger.Error.Printf("单例抽取获取引擎信息失败:%s", err.Error())
			return "", "", "", err
		}
		sqltxt := fmt.Sprintf("desc database `%s`", sp.Sourcedb.Odsdatabase)
		engineinfo.InitDB = sp.Sourcedb.Odsdatabase
		hdfsRes, err := collect.MGExecuteQuerySql(engineinfo, engineinfo.InitDB, sqltxt)
		if err != nil {
			logger.Error.Printf("批量抽取获取hdfs信息失败:%s,sql为%s", err.Error(), sqltxt)
			return "", "", "", err
		}
		for _, s := range hdfsRes {
			for _, m := range s {
				mstr := fmt.Sprintf("%v", m)
				if strings.Contains(mstr, "hdfs://") {
					hdfsURL = mstr
					break
				}
			}
		}
		fmt.Println("hdfsURL:", hdfsURL)
		hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
		switch sp.Sourcedb.Dbtype {
		case common.Hive:
			hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		case common.Inceptor:
			hdfsURL = fmt.Sprintf("%s/%s/%s", hdfsURL, "hive", strings.ToLower(sp.Sourcedb.Table))
		}
		//hdfsURL = fmt.Sprintf("%s/%s", hdfsURL, strings.ToLower(sp.Sourcedb.Table))
		sp.Sourcedb.HdfsURL = hdfsURL
	}
	hdfsURL = sp.Sourcedb.HdfsURL
	//writer的组建
	logger.Info.Println("hdfsURL:", hdfsURL)
	//把限制符都置为,
	delimiter = ","
	//----------------------------------
	//我也不知道为什么要加

	fmt.Printf("delimiter:%#v\n", delimiter)
	hdfsURL = strings.Replace(hdfsURL, "'", "", -1)
	// location:hdfs://dn100:8020/apps/hive/warehouse/odsdb.db/testmysql
	index := strings.Index(hdfsURL, "hdfs://")
	subres := hdfsURL[index+7:]
	pre := strings.Index(subres, "/")
	// hdfs数据库表路径
	hdfsURL = subres[pre:]
	// fmt.Println("url:", hdfsURL)
	hivewriter := common.HiveWriters
	sitePath := fmt.Sprintf("%s/hdfs-site.xml,%s/core-site.xml", sp.Sourcedb.HiveFilePath, sp.Sourcedb.HiveFilePath)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.hadoopConfigFilePath", sitePath)
	// hive writer 添加驱动的路径
	hivewriter, _ = sjson.Set(hivewriter, "parameter.driverJarPath", hiveDriverPath)
	// ---------------end------------------
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileName", sp.Sourcedb.Table)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fieldDelimiter", delimiter)
	{
		hivewriter, _ = sjson.Set(hivewriter, "parameter.path", hdfsURL)
	}
	username := sp.Sourcedb.Odsuser
	if username == "" {
		// 使用默认用户
		username = "hive"
	}
	hivewriter, _ = sjson.Set(hivewriter, "parameter.username", username)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.password", sp.Sourcedb.Odspassword)
	hivewriter, _ = sjson.Set(hivewriter, "parameter.clusterName", "tdh")
	//hivewrite
	tempTbName := fmt.Sprintf("`%s`", sp.Sourcedb.Table)
	connect := gin.H{
		"jdbcUrl": collect.InsteadDBname(sp.Sourcedb.JdbcURL, sp.Sourcedb.Odsdatabase),
		"table":   []string{tempTbName},
	}
	//从临时表插入存储表sql
	var insertSql, analyzesql string
	//区分ds创建的表和已有分区表的区分
	//如果选择了字段映射，要保证字段和临时表的字段顺序一致
	var createTemp, fieldsqltemp string
	tb := sp.Sourcedb.Table + "_temp"
	createTemp = fmt.Sprintf("CREATE TABLE if not exists `%s`.`%s`", sp.Sourcedb.Odsdatabase, tb)
	if len(sp.Sourcedb.PartitionField) > 0 {
		var fieldSql1, tempSql, tbSql string
		if len(sp.TableRelation.Edges) > 0 {
			for _, v := range sp.TableRelation.Edges {
				var isPar bool
				for _, vv := range sp.Sourcedb.PartitionField {
					if v.Target == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v.Target)
					//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v.Source)
					for k, v1 := range sp.Sourcedb.Field {
						if v.Target == v1 {
							fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Target, sp.Sourcedb.Fieldtype[k])
						}
					}

				}
			}

		} else {
			for k, v := range sp.Sourcedb.Field {
				var isPar bool
				for _, vv := range sp.Sourcedb.PartitionField {
					if v == vv.Name {
						isPar = true
						break
					}
				}
				if !isPar {
					fieldSql1 = fmt.Sprintf("%s,`%s`", fieldSql1, v)
					//fieldSql2 = fmt.Sprintf("%s,`%s`", fieldSql2, v)
					fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v, sp.Sourcedb.Fieldtype[k])
				}
			}
		}
		fieldSql1 = fieldSql1[1:]
		//fieldSql2 = fieldSql2[1:]
		//tbSql = fieldSql1
		tempSql = fieldSql1
		fieldsqltemp = fieldsqltemp[2:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		var isMix = 0
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				if isMix == 2 {
					isMix = 3
					break
				} else {
					isMix = 1
				}
			} else {
				if isMix == 1 {
					isMix = 3
					break
				} else {
					isMix = 2
				}
			}
		}
		fmt.Println("temsql1:", tempSql)
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset == "notpreset" {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				tempSql = fmt.Sprintf("%s,`%s`", tempSql, v.Name)
				fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
				fmt.Println("temsql2:", tempSql)
			} else {
				tbSql = fmt.Sprintf("%s,`%s`", tbSql, v.Name)
				fieldsqltemp = fmt.Sprintf("%s, `%s` %s", fieldsqltemp, v.Name, v.FieldType)
				if len(sp.Sourcedb.PartitionField) == 1 || isMix == 3 {
					//系统预设一级分区
					switch v.Ispreset {
					case "year":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
					case "month":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM') as ", v.Name)
					case "day":
						tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd') as ", v.Name)
					}
				} else {
					if isMix == 2 {
						switch v.Ispreset {
						case "year":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy') as ", v.Name)
						case "month":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'MM') as ", v.Name)
						case "day":
							tempSql = fmt.Sprintf("%s,%s `%s`", tempSql, "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'dd') as ", v.Name)
						}
					}
				}
			}
		}
		fmt.Println("temsql3:", tempSql)
		tbSql = tbSql[1:]
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s` partition(%s) select %s from `%s` ", sp.Sourcedb.Table, tbSql, tempSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		var fieldSql string
		for _, v := range sp.Sourcedb.PartitionField {
			fieldSql = fmt.Sprintf("%s,`%s`", fieldSql, v)
		}
		fieldSql = fieldSql[1:]
		//分区设计--离线采集
		//已有字段 -- 根据已有字段名填入
		//系统预设 -- 一级分区：补全时间格式yyyyMMdd
		//        --多级分区：纯系统预设 -- yyyy/MM/dd
		//        --多级分区：混合设置 -- 补全时间格式
		tempTb := fmt.Sprintf(`%s_temp`, sp.Sourcedb.Table)
		insertSql = fmt.Sprintf("insert into table `%s` partition(%s) select %s from `%s` ", sp.Sourcedb.Table, fieldSql, fieldSql, tempTb)

		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	} else {
		analyzesql = fmt.Sprintf("ANALYZE TABLE `%s` COMPUTE STATISTICS", sp.Sourcedb.Table)
	}
	//getSql := []string{insertSql, analyzesql}
	createTemp = fmt.Sprintf(`%s (%s) %s`, createTemp, fieldsqltemp, " stored as orc")
	if len(sp.Sourcedb.PartitionField) > 0 || len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), "set hive.exec.dynamic.partition = true")
		hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(1), insertSql)
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(1), analyzesql)
		// 获取临时表sqltxt
		body := gin.H{
			"query": gin.H{
				"bool": gin.H{
					"must": gin.H{
						"term": gin.H{
							"id": sp.Sourcedb.TableID,
						},
					},
				},
			},
			"size": common.UnlimitSize,
		}
		resp, err := escli.NewESClientCl.SearchByTerm(common.DataBaseMetaData, common.TbMetaTable, body)
		if err != nil {
			logger.Error.Printf("例举存储源表失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var tbhit metadata.MetaSourceTbHits
		err = json.Unmarshal([]byte(resp), &tbhit)
		if err != nil {
			logger.Error.Printf("解析表数据失败:%s", err.Error())
			return hivewriter, hdfsURL, analyzesql, err
		}
		var sqlTxtTemp string
		var tbinfoNew metadata.MetaSourceTb
		for _, v := range tbhit.Hits1.Hits2 {
			//fmt.Println("--------------------------------")
			//fmt.Println(sp.Sourcedb.Table)
			//fmt.Println(v.MetaSourceTbSource.TBName)
			//fmt.Println(v.MetaSourceTbSource.SqlTxtTemp)
			if sp.Sourcedb.Table == v.MetaSourceTbSource.TBName {
				sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTemp
				if sqlTxtTemp == "" {
					sqlTxtTemp = v.MetaSourceTbSource.SqlTxtTempNew
				}
				tbinfoNew = v.MetaSourceTbSource
				//字段保持一致
				var tmp []metadata.MetaSourceFieldInfo
				for _, source1 := range sp.Sourcedb.Field {
					for _, source2 := range v.MetaSourceTbSource.FieldInfo {
						if source1 == source2.Name {
							tmp = append(tmp, source2)
							break
						}
					}
				}
				tbinfoNew.FieldInfo = tmp
			}
		}
		//如果非ds创建分区表，sqltxttemp无内容，需要组合sql语句
		if sqlTxtTemp == "" {
			eninfo, err := dbutil.EngineInfo(tbinfoNew.EngineID)
			if err != nil {
				//logger.Error.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				fmt.Printf("根据engineid获取引擎信息失败:%s", err.Error())
				return "", "", "", fmt.Errorf("根据engineid获取引擎信息失败:%s", err.Error())
			}
			//获取topic -- 分区创建表的路径
			tbinfoNew.RealTimeTB.SourcedbTableName = tbinfoNew.TBName
			tbinfoNew.RealTimeTB.SourcedbType = tbinfoNew.DBType
			tbinfoNew.RealTimeTB.SourcedbTableID = tbinfoNew.ID
			var topics string
			if tbinfoNew.IsRT {
				info := collect.TopicInfo(&tbinfoNew)
				topics = info.DbServerName
			}
			sqltxt, sqltxtTemp := dbutil.HiveTbSQL(&tbinfoNew, eninfo.EngineName, topics, common.Hive)
			if sqltxt == "" {
				fmt.Printf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
				return hivewriter, hdfsURL, analyzesql, fmt.Errorf("该表无创建字段，表名：%s\n", tbinfoNew.TBName)
			}
			sqlTxtTemp = sqltxtTemp
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.preSql."+strconv.Itoa(0), createTemp)
	} else {
		//hivewriter, _ = sjson.Set(hivewriter, "parameter.postSql."+strconv.Itoa(0), analyzesql)
	}

	hivewriter, _ = sjson.Set(hivewriter, "parameter.connection."+strconv.Itoa(0), connect)

	fileType := "text"
	if strings.Contains(sp.Sourcedb.FileType, "rc") {
		fileType = common.TypeOrc
	}
	// filetype 就是text
	/*	if fileType == "text" {
		fileType = "csv"
	}*/
	hivewriter, _ = sjson.Set(hivewriter, "parameter.fileType", fileType)
	var lenField int
	if len(sp.Sourcedb.Field) > 0 {
		if len(sp.Sourcedb.Field) != len(sp.Sourcedb.Fieldtype) {
			return hivewriter, hdfsURL, analyzesql, fmt.Errorf("目的源字段数量与字段类型数量不一致：%d, %d", len(sp.Sourcedb.Field), len(sp.Sourcedb.Fieldtype))
		}
		if len(sp.TableRelation.Edges) > 0 {
			j := 0
			for i, v := range sp.TableRelation.Edges { //映射字段 key weight length
				var isPar bool
				for _, v1 := range sp.Sourcedb.PartitionField {
					if v.Target == v1.Name {
						isPar = true
					}
				}
				if !isPar {
					for i := 0; i < len(sp.Sourcedb.Field); i++ { //目的端所有字段
						if v.Target == sp.Sourcedb.Field[i] {
							record := gin.H{
								"name": v.Target,
								"type": sp.Sourcedb.Fieldtype[i],
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
						}
					}
					j = j + 1
				}
				if i+1 == len(sp.TableRelation.Edges) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				//hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j),
				//	"\""+strings.TrimRight(v.Target, "\r")+"\"")
			}
			lenField = len(sp.TableRelation.Edges)
		} else {
			j := 0
			for i := 0; i < len(sp.Sourcedb.Field); i++ {
				var isPar bool
				for _, v1 := range sp.Sourcedb.PartitionField {
					if sp.Sourcedb.Field[i] == v1.Name {
						isPar = true
					}
				}
				if !isPar {
					record := gin.H{
						"name": sp.Sourcedb.Field[i],
						"type": sp.Sourcedb.Fieldtype[i],
					}
					hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
					j = j + 1
				}
				if i+1 == len(sp.Sourcedb.Field) {
					for _, v2 := range sp.Sourcedb.PartitionField {
						if v2.Ispreset == "notpreset" {
							record := gin.H{
								"name": v2.Name,
								"type": v2.FieldType,
							}
							hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(j), record)
							j++
						}
					}
				}
				/*record := gin.H{
					"name": sp.Sourcedb.Field[i],
					"type": sp.Sourcedb.Fieldtype[i],
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(i), record)*/
			}
			lenField = len(sp.Sourcedb.Field)
		}
	}
	//hive分区的设置
	//由于只是传给datax的，不需要进行规则修改
	var parRecord = gin.H{}
	if len(sp.Sourcedb.PartitionField) > 0 {
		for _, v := range sp.Sourcedb.PartitionField {
			if v.Ispreset != "notpreset" {
				switch v.Ispreset {
				case "year":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyy')",
						"type": v.FieldType,
					}
				case "month":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMM')",
						"type": v.FieldType,
					}
				case "day":
					parRecord = gin.H{
						"name": "date_format(from_utc_timestamp(unix_timestamp()*1000,'PRC'), 'yyyyMMdd')",
						"type": v.FieldType,
					}
				}
				hivewriter, _ = sjson.Set(hivewriter, "parameter.column."+strconv.Itoa(lenField), parRecord)
				lenField++
			}
		}
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	} else if len(sp.Sourcedb.PartitionInfoFields) > 0 {
		hivewriter, _ = sjson.Set(hivewriter, "parameter.havePartition", "true")
	}
	//
	js, err := simplejson.NewJson([]byte(hivewriter))
	if err != nil {
		return hivewriter, hdfsURL, analyzesql, err
	}
	if sp.Sourcedb.IsConfigKbs == "1" {
		js.SetPath([]string{"parameter", "haveKerberos"}, true)
		js.SetPath([]string{"parameter", "kerberosKeytabFilePath"}, sp.Sourcedb.KerberosInfo.KeyTabPath)
		js.SetPath([]string{"parameter", "kerberosPrincipal"}, sp.Sourcedb.KerberosInfo.Principal)
		js.SetPath([]string{"parameter", "krb5ConfigFilePath"}, sp.Sourcedb.KerberosInfo.Krb5ConfPath)
	}
	encode, _ := js.Encode()
	hivewriter = string(encode)
	return hivewriter, hdfsURL, analyzesql, nil
}

// TableRename hive表名验证，合法为true，否则为false
func TableRename(tableName string) bool {
	res := false
	tableRune := []rune(tableName)
	// 表名是否包含特殊字符，若包含则替换为字符索引+1
	speRune := []rune{'~', '`', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '=', '+', '.'}
	for i := range tableRune {
		// 是否包含中文
		if unicode.Is(unicode.Han, tableRune[i]) {
			return res
		}
		for _, spe := range speRune {
			if spe == tableRune[i] {
				return res
			}
		}
	}
	res = true
	return res
}

// TBExists 判断表是否存在，不存在则创建
func TBExists(sp *source.NewAcc, extractTB source.AccExtractDb) (source.AccSourceTB, error) {
	var err error
	var cast toolsource.TypeCast
	cast.ExtrType = sp.Extractdb.DBType
	cast.SourceType = sp.Sourcedb.Dbtype
	cast.FieldType = extractTB.Fieldtype
	//fmt.Printf("TBExists--cast:%#v\n", cast)
	extractTB.Fieldtype = tools.FieldTrans(cast)
	// 存储源表字段修正--全部转为小写(hive)
	if sp.Sourcedb.Dbtype == common.Hive || sp.Sourcedb.Dbtype == common.ODPS {
		for i := 0; i < len(extractTB.Field); i++ {
			extractTB.Field[i] = strings.ToLower(extractTB.Field[i])
		}
	}
	//  fieldstr, fieldtype []string, v string
	// var tbname string
	var sourceTB source.AccSourceTB
	if sp.Sourcedb.Dbtype == "postgres" || sp.Sourcedb.Dbtype == "stork" || sp.Sourcedb.Dbtype == "teryx" || sp.Sourcedb.Dbtype == "greenplum" || sp.Sourcedb.Dbtype == common.GaussDB || sp.Sourcedb.Dbtype == common.UXDB {
		sourceTB, err = storkTBExists(sp, extractTB)
		if err != nil {
			return sourceTB, err
		}
		sp.Sourcedb.Field = extractTB.Field
		//fmt.Println("这个时候的creatablesql")
		//fmt.Println(sourceTB.CreSQL)
	} else if sp.Sourcedb.Dbtype == "hive" {
		sourceTB, err = hiveTBExists(sp, extractTB)
		if err != nil {
			return sourceTB, err
		}
	} else if sp.Sourcedb.Dbtype == common.DMDB {
		sourceTB, err = DaMengCreSql(sp, extractTB)
		if err != nil {
			return sourceTB, err
		}
	}
	return sourceTB, err
}

// hiveTBExists 判断hive 表是否存在，不存在则新建
func hiveTBExists(sp *source.NewAcc, extractTB source.AccExtractDb) (source.AccSourceTB, error) {
	fieldstr := extractTB.Field
	fieldtype := extractTB.Fieldtype
	tableName := extractTB.OdsTBname
	var sourceTB source.AccSourceTB
	sourceTB.Table = tableName
	if !TableRename(tableName) {
		return sourceTB, fmt.Errorf("表名不合法: %s", tableName)
	}
	for _, v := range fieldstr {
		if !TableRename(v) {
			return sourceTB, fmt.Errorf("表 '%s' 的字段 '%s' 不应包含中文和特殊字符", tableName, v)
		}
	}
	// var tbExist bool
	// var tableSqltxt string
	/*
		获取表结构方案：
		1、覆盖：只获需取表结构（根据抽取表结构映射而来）
		2、非覆盖：表是否存在于存储源中
			a、存在，获取存储表结构，不管源表结构
			b、不存在，表结构-根据抽取表结构映射
	*/

	//覆盖只需要表结构
	if extractTB.ExtractStrategy == 1 {
		sourceTB = FieldCastToHive(extractTB, sp)
		return sourceTB, nil
	}
	// 从元数据处获取字段信息
	metafield, ismeta, err := devcenter.CheckTbColumn(sp.Sourcedb.Odsdatabase, tableName)
	if err != nil {
		logger.Error.Printf("从元数据处获取字段信息失败:%s", err.Error())
		return sourceTB, err
	}
	if ismeta {
		// 表已存入元数据存储源
		//源库字段数>存储源字段数,抽取不了
		odsFedLen := len(metafield)
		if len(fieldtype) > odsFedLen {
			sourceTB.ErrTB = 1
			return sourceTB, fmt.Errorf("表字段数异常")
		}
		for _, v := range metafield {
			sourceTB.Field = append(sourceTB.Field, v.Name)
			sourceTB.FieldType = append(sourceTB.FieldType, v.FieldType)
			v.Annotation = dbutil.ExtractTbCommentFormat(v.Annotation)
			sourceTB.FieldComment = append(sourceTB.FieldComment, v.Annotation)
		}
		//已有表且不是追加，不需执行建表sql
		return sourceTB, nil
	}
	sourceTB = FieldCastToHive(extractTB, sp)
	//logger.Info.Println("sql:", sourceTB.CreSQL)
	return sourceTB, nil
}

// 抽取源字段类型转为hive的+生成建表语句
func FieldCastToHive(extractTB source.AccExtractDb, sp *source.NewAcc) source.AccSourceTB {
	fieldstr := extractTB.Field
	for i := 0; i < len(fieldstr); i++ {
		fieldstr[i] = strings.ToLower(fieldstr[i])
	}
	fieldtype := extractTB.Fieldtype
	for i := 0; i < len(extractTB.Fieldcomment); i++ {
		extractTB.Fieldcomment[i] = dbutil.ExtractTbCommentFormat(extractTB.Fieldcomment[i])
	}
	fieldComment := extractTB.Fieldcomment
	tableName := extractTB.OdsTBname
	var sourceTB source.AccSourceTB
	sourceTB.Field = fieldstr
	sourceTB.FieldType = fieldtype
	sourceTB.FieldComment = fieldComment
	sourceTB.ExtractStrategy = extractTB.ExtractStrategy
	sourceTB.Table = tableName
	sourceTB.Comment = dbutil.ExtractTbCommentFormat(extractTB.Comment)
	return sourceTB
}

func CoverTBSQL(s source.AccSourceDb) (string, string, error) {
	hiveVersion := s.HiveVersion
	fieldstr := s.Field
	fieldtype := s.Fieldtype
	fieldcomment := s.Fieldcomment
	tableName := s.Table
	var delTBSQL string
	var createTBSQL string
	fileType := "textfile"
	// tableName = sp.Sourcedb.Odsdatabase + "." + tableName
	if strings.Contains(s.FileType, "rc") {
		fileType = common.TypeOrc
	}
	tableComment := dbutil.ExtractTbCommentFormat(s.TableComment)
	tableComment = strings.Replace(tableComment, `\`, `\\`, -1)
	tableComment = strings.Replace(tableComment, `"`, `\"`, -1)
	comSql := fmt.Sprintf("alter table `%s`.`%s` set tblproperties(\\u0027comment\\u0027 = \\u0027%s\\u0027)",
		s.Odsdatabase, tableName, tableComment)
	delTBSQL = fmt.Sprintf("drop table `%s`.`%s`", s.Odsdatabase, tableName)
	createTBSQL = fmt.Sprintf("create table if not exists `%s`.`%s` (", s.Odsdatabase, tableName)
	for i := 0; i < len(fieldstr); i++ {
		tempsql := ""
		// 对注释进行处理，分号、逗号都替换为空格,换行符则去除
		if fieldcomment[i] != "" {
			fieldcomment[i] = dbutil.ExtractTbCommentFormat(fieldcomment[i])
			fieldcomment[i] = strings.Replace(fieldcomment[i], `\`, `\\`, -1)
			fieldcomment[i] = strings.Replace(fieldcomment[i], `"`, `\"`, -1)
		}
		if i == len(fieldstr)-1 {
			tempname := fieldstr[i]
			tempsql = fmt.Sprintf("`%s` %s COMMENT \\u0027%s\\u0027)", tempname, fieldtype[i], fieldcomment[i])
		} else {
			tempname := fieldstr[i]
			tempsql = fmt.Sprintf("`%s` %s COMMENT \\u0027%s\\u0027,", tempname, fieldtype[i], fieldcomment[i])
		}

		createTBSQL += tempsql
	}
	logger.Info.Printf("sql-------------------------sql:%s", createTBSQL)
	if hiveVersion == common.HiveTDC {
		firstColumn := fieldstr[0]
		createTBSQL += fmt.Sprintf(" clustered by (`%s`)  into 3 buckets stored as orc TBLPROPERTIES ('transactional'='true')", firstColumn)
	} else {
		fmt.Println("覆盖的格式类型:", fileType)
		switch s.FileType {

		case "text", "textfile", "Textfile":
			createTBSQL += `ROW FORMAT SERDE \u0027org.apache.hadoop.hive.serde2.OpenCSVSerde\u0027 WITH SERDEPROPERTIES (\"separatorChar\" = \",\",\"quoteChar\"     = \"\\\"\",\"escapeChar\"    = \"\\\\\") stored as  INPUTFORMAT  \u0027com.datatom.OpenCsvInputFormat\u0027  OUTPUTFORMAT  \u0027org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat\u0027 `
		default:
			createTBSQL += `  stored as  orc`

		}

	}
	createTBSQL = fmt.Sprintf(`%s; %s`, createTBSQL, comSql)
	return delTBSQL, createTBSQL, nil
}

// storkTBExists 判断stork 表是否存在，不存在则新建
func storkTBExists(sp *source.NewAcc, extractTB source.AccExtractDb) (source.AccSourceTB, error) {
	var cretablesql string
	var sourceTB source.AccSourceTB
	fieldstr := extractTB.Field
	fieldtype := extractTB.Fieldtype
	fieldcomment := extractTB.Fieldcomment
	tableComment := dbutil.ExtractTbCommentFormat(extractTB.Comment)
	comSql := fmt.Sprintf(`comment on table "%s"."%s"  is '%s'`, sp.Sourcedb.Schema, extractTB.OdsTBname, tableComment)
	//fileprim := extractTB.Fieldprim
	for i := 0; i < len(fieldcomment); i++ {
		if fieldcomment[i] != "" {
			fieldcomment[i] = dbutil.ExtractTbCommentFormat(fieldcomment[i])
		}
	}
	cretablesql = fmt.Sprintf(`CREATE TABLE if not exists "%s"."%s"`, sp.Sourcedb.Schema, extractTB.OdsTBname)
	var describeSQL, fieldSQL string
	for i := 0; i < len(fieldstr); i++ {
		if fieldcomment[i] != "" {
			describeSQL = fmt.Sprintf(`%sCOMMENT ON COLUMN "%s"."%s"."%s" IS '%s';`,
				describeSQL, sp.Sourcedb.Schema, extractTB.OdsTBname, fieldstr[i], fieldcomment[i])
		}
		if i == 0 {
			fieldSQL = fmt.Sprintf(`"%s" %s`, fieldstr[i], fieldtype[i])
			continue
		}
		fieldSQL = fmt.Sprintf(`%s, "%s" %s`, fieldSQL, fieldstr[i], fieldtype[i])
	}
	//MPP数据库添加分布方式和分布键
	var distributionSql string
	var distributionUXSql string
	var distributionBond string
	var distributionmodeup string
	var distributionbondup []string

	//fmt.Println("sp.Sourcedb.HiveVersion:", sp.Sourcedb.HiveVersion)
	//fmt.Println("sp.Sourcedb.Engineid：", sp.Sourcedb.Engineid)
	//fmt.Println("-----metadatacommon.GAUSSDBVERSION", metadatacommon.GAUSSDBVERSION)

	switch sp.Sourcedb.Dbtype {
	case common.Teryx, common.GaussDB, common.GREENPLUM:
		//通过传参创建表

		switch extractTB.DistributionMode {
		case common.HASH:
			// guassdb 300 分布键语法兼容
			if strings.Contains(sp.Sourcedb.HiveVersion, "GaussDB") {
				//if sp.Sourcedb.HiveVersion == metadatacommon.GAUSSDB300 { // 300
				distributionSql = "DISTRIBUTE BY "
			} else {
				distributionSql = "DISTRIBUTED BY " // 其他 gaussdb  比如200 ，但是没测
			}
			//判断分布键是否为空
			if len(extractTB.DistributionBond) == 0 {
				logger.Error.Println("分布键为空，创建HASH分布错误")
				return sourceTB, fmt.Errorf("分布键为空，创建HASH分布错误")
			}
			for _, v := range extractTB.DistributionBond {
				distributionbondup = append(distributionbondup, v)
				//distributionBond = distributionBond + v + ","
				distributionBond = fmt.Sprintf(`%s "%s" ,`, distributionBond, v)
			}

			if strings.Contains(sp.Sourcedb.HiveVersion, "GaussDB") {
				distributionSql = fmt.Sprintf("%s HASH(%s)", distributionSql, distributionBond[0:len(distributionBond)-1])
			} else {
				distributionSql = fmt.Sprintf("%s(%s)", distributionSql, distributionBond[0:len(distributionBond)-1])
			}
			// distributionSql = fmt.Sprintf(`%s(%s)`, distributionSql, distributionBond[0:len(distributionBond)-1])
			distributionmodeup = "HASH"
		case common.RANDOMLY:
			distributionSql = "DISTRIBUTED RANDOMLY"
			distributionmodeup = "RANDOMLY"
			distributionbondup = []string{}
		case common.REPLICATED:
			if strings.Contains(sp.Sourcedb.HiveVersion, "GaussDB") {
				distributionSql = "DISTRIBUTE BY REPLICATION "
			} else {
				distributionSql = "DISTRIBUTED REPLICATED"
			}
			distributionmodeup = "REPLICATED"
			distributionbondup = []string{}
		default:
			//防止意外，以第一个字段作为HASH分布键
			distributionSql = "DISTRIBUTED BY "
			distributionSql = fmt.Sprintf(`%s("%s")`, distributionSql, fieldstr[0])
			distributionmodeup = "HASH"
			distributionbondup = []string{fieldstr[0]}
		}
	case common.UXDB:
		if len(extractTB.DistributionBond) > 1 {
			logger.Error.Println("UXDB的分布键有且只能是一个")
			return sourceTB, fmt.Errorf("UXDB的分布键有且只能是一个")
		}
		switch extractTB.DistributionMode {
		case common.HASH:
			fieldname := extractTB.DistributionBond[0]
			distributionUXSql = fmt.Sprintf(`SELECT create_distributed_table ('"%s"."%s"','%s','hash');`, sp.Sourcedb.Schema, extractTB.OdsTBname, fieldname)
			distributionmodeup = "HASH"
			//distributionbondup = append(distributionbondup, strings.ToLower(extractTB.DistributionBond[0]))
			distributionbondup = append(distributionbondup, extractTB.DistributionBond[0])
		case common.APPEND:
			distributionmodeup = ""
			distributionbondup = []string{}
			logger.Error.Print("UX不支持append分布方式，请修改为hash分布方式进行创建")
			return sourceTB, fmt.Errorf("UX不支持append分布方式，请修改为hash分布方式进行创建")
			//fieldname := extractTB.DistributionBond[0]
			//distributionUXSql = fmt.Sprintf(`SELECT create_distributed_table ('"%s"."%s"','%s','append');`, sp.Sourcedb.Schema, extractTB.OdsTBname, fieldname)
			//distributionmodeup = "APPEND"
			//distributionbondup = append(distributionbondup, strings.ToLower(extractTB.DistributionBond[0]))
		default:
			//fieldname := req.DistributionBond[0]
			//uxSql = fmt.Sprintf(`SELECT create_distributed_table ('%s.%s','%s');`, req.SourceDb, req.TBName, fieldname)
			distributionUXSql = ""
			distributionmodeup = ""
			distributionbondup = []string{}
		}

	default:
		distributionSql = ""
		distributionmodeup = ""
		distributionbondup = []string{}
	}

	cretablesql = fmt.Sprintf(`%s (%s)%s; %s; %s`, cretablesql, fieldSQL, distributionSql, describeSQL, comSql)
	if sp.Sourcedb.Dbtype == common.UXDB {
		cretablesql = fmt.Sprintf(`%s;%s`, cretablesql, distributionUXSql)
	}
	sp.Sourcedb.DistributionMode = distributionmodeup
	sp.Sourcedb.DistributionBond = distributionbondup
	logger.Info.Println("建表语句sqltxt：", cretablesql)
	sourceTB.Table = extractTB.OdsTBname
	sourceTB.Comment = extractTB.Comment
	sourceTB.Field = extractTB.Field
	sourceTB.FieldType = extractTB.Fieldtype
	sourceTB.FieldComment = extractTB.Fieldcomment
	sourceTB.FieldPrim = extractTB.Fieldprim
	//if extractTB.ChoseWay == 1 {
	//	sourceTB.CreSQL = ";"
	//} else {
	//	sourceTB.CreSQL = cretablesql
	//}
	sourceTB.CreSQL = cretablesql
	return sourceTB, nil
}

// storkTBExists 判断stork 表是否存在，不存在则新建
func DaMengCreSql(sp *source.NewAcc, extractTB source.AccExtractDb) (source.AccSourceTB, error) {
	var cretablesql string
	var sourceTB source.AccSourceTB
	fieldstr := extractTB.Field
	fieldtype := extractTB.Fieldtype
	fieldcomment := extractTB.Fieldcomment
	tableComment := dbutil.ExtractTbCommentFormat(extractTB.Comment)
	comSql := fmt.Sprintf(`comment on table "%s"."%s"  is '%s'`, sp.Sourcedb.Schema, extractTB.OdsTBname, tableComment)
	for i := 0; i < len(fieldcomment); i++ {
		if fieldcomment[i] != "" {
			fieldcomment[i] = dbutil.ExtractTbCommentFormat(fieldcomment[i])
		}
	}
	cretablesql = fmt.Sprintf(`CREATE TABLE  "%s"."%s"`, sp.Sourcedb.Schema, extractTB.OdsTBname)
	var describeSQL, fieldSQL string
	for i := 0; i < len(fieldstr); i++ {
		if fieldcomment[i] != "" {
			describeSQL = fmt.Sprintf(`%sCOMMENT ON COLUMN "%s"."%s"."%s" IS '%s';`,
				describeSQL, sp.Sourcedb.Schema, extractTB.OdsTBname, fieldstr[i], fieldcomment[i])
		}
		if i == 0 {
			fieldSQL = fmt.Sprintf(`"%s" %s`, fieldstr[i], fieldtype[i])
			continue
		}
		fieldSQL = fmt.Sprintf(`%s, "%s" %s`, fieldSQL, fieldstr[i], fieldtype[i])
	}
	//MPP数据库添加分布方式和分布键
	var distributionSql string
	var distributionBond string
	var distributionmodeup string
	var distributionbondup []string
	switch sp.Sourcedb.Dbtype {
	case common.DMDB:
		//通过传参创建表
		switch extractTB.DistributionMode {
		case common.HASH:
			distributionSql = "DISTRIBUTED BY "
			//判断分布键是否为空
			if len(extractTB.DistributionBond) == 0 {
				logger.Error.Println("分布键为空，创建HASH分布错误")
				return sourceTB, fmt.Errorf("分布键为空，创建HASH分布错误")
			}
			//DM的字段类型是否符合要求
			fieldTypes := []string{"blob", "clob", "image", "text", "longvarchar", "bit", "binary", "varbinary", "longvarbinary"}
			var fileTypeMaps = make(map[string]string)
			for _, fieldName := range extractTB.DistributionBond {
				for i, fieldInfo := range extractTB.Field {
					if fieldName == fieldInfo {
						//fileTypeMaps[fieldName] = strings.ToLower(extractTB.Fieldtype[i])
						fileTypeMaps[fieldName] = extractTB.Fieldtype[i]
					}
				}
			}
			for _, fieldType := range fileTypeMaps {
				for _, unFileType := range fieldTypes {
					if fieldType == unFileType {
						logger.Error.Println("不可以使用DM分布方式不支持的字段类型作为分布键")
						return sourceTB, fmt.Errorf("不可以使用DM分布方式不支持的字段类型作为分布键")
					}
				}
			}
			for _, v := range extractTB.DistributionBond {
				distributionbondup = append(distributionbondup, v)
				//distributionBond = distributionBond + v + ","
				distributionBond = fmt.Sprintf(`%s"%s",`, distributionBond, v)
			}
			distributionSql = fmt.Sprintf(`%s(%s)`, distributionSql, distributionBond[0:len(distributionBond)-1])
			distributionmodeup = "HASH"
		case common.RANDOMLY:
			distributionSql = "DISTRIBUTED RANDOMLY"
			distributionmodeup = "RANDOMLY"
			distributionbondup = []string{}
		case common.REPLICATED:
			distributionSql = "DISTRIBUTED FULLY"
			distributionmodeup = "REPLICATED"
			distributionbondup = []string{}
		default:
			//防止意外，以第一个字段作为HASH分布键
			distributionSql = "DISTRIBUTED BY "
			distributionSql = fmt.Sprintf(`%s("%s")`, distributionSql, fieldstr[0])
			distributionmodeup = "HASH"
			distributionbondup = []string{fieldstr[0]}
		}
	default:
		distributionSql = ""
		distributionmodeup = ""
		distributionbondup = []string{}
	}
	cretablesql = fmt.Sprintf(`%s (%s)%s; %s; %s`, cretablesql, fieldSQL, distributionSql, describeSQL, comSql)
	sp.Sourcedb.DistributionMode = distributionmodeup
	sp.Sourcedb.DistributionBond = distributionbondup
	logger.Info.Println("建表语句sqltxt：", cretablesql)
	sourceTB.Table = extractTB.OdsTBname
	sourceTB.Comment = extractTB.Comment
	sourceTB.Field = extractTB.Field
	sourceTB.FieldType = extractTB.Fieldtype
	sourceTB.FieldComment = extractTB.Fieldcomment
	sourceTB.FieldPrim = extractTB.Fieldprim
	//if extractTB.ChoseWay == 1 {
	//	sourceTB.CreSQL = ";"
	//} else {
	//	sourceTB.CreSQL = cretablesql
	//}
	sourceTB.CreSQL = cretablesql
	return sourceTB, nil
}

// DBType 抽取任务存储源数据库类型
func DBType() (string, string, error) {
	dbtype := ""
	hiveversion := ""
	// 汇聚层固定colornum=1010
	colorNum := 1010
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"colornum": colorNum,
					},
				},
			},
		},
	}
	byteBody, _ := json.Marshal(body)
	uri := fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.DatabaseLayer)
	response, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return dbtype, hiveversion, err
	}
	layResult := source.LayerResHits{}
	json.Unmarshal(response, &layResult)
	t := layResult.Hits1.Total
	if t != 1 {
		return dbtype, hiveversion, fmt.Errorf("层信息出错:%d", colorNum)
	}
	layerid := layResult.Hits1.Hits2[0].ResSource.ID
	body = gin.H{
		"query": gin.H{
			"regexp": gin.H{
				"layerid": layerid,
			},
		},
	}
	byteBody, _ = json.Marshal(body)
	uri = fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.NewDbAsset)
	response, err = httpclient.Post(uri, string(byteBody))
	if err != nil {
		return dbtype, hiveversion, err
	}
	EngResult := source.EngineResHits{}
	err = json.Unmarshal(response, &EngResult)
	if err != nil {
		return dbtype, hiveversion, err
	}
	if EngResult.Hits1.Total <= 0 {
		return dbtype, hiveversion, fmt.Errorf("引擎信息出错:%s", layerid)
	}
	dbtype = EngResult.Hits1.Hits2[0].ResSource.DatabaseType
	hiveversion = EngResult.Hits1.Hits2[0].ResSource.HiveVersion
	fmt.Println("dbtype:", dbtype)
	return dbtype, hiveversion, nil
}

// ListEngDetails 列举引擎细节
func ListEngDetails() ([]source.EngInfo, error) {
	info := []source.EngInfo{}
	var array = [6]string{0: "stork", 1: "teryx", 2: "eagles", 3: "hive", 4: "greenplum", 5: "postgres"}
	var must = gin.H{
		"enginename": array,
	}
	var mustNot = gin.H{
		"enginestatus": "UNKNOWN",
	}

	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"terms": must,
				},
				"must_not": gin.H{
					"term": mustNot,
				},
			},
		},
		"size": 1000,
	}
	byteBody, _ := json.Marshal(body)
	uri := fmt.Sprintf("/danastudio_platform/tb_engine/_search")
	resp, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return info, err
	}
	result := source.EnHitsRes1{}
	err = json.Unmarshal(resp, &result)
	if err != nil {
		logger.Error.Println(err)
		return info, err
	}
	metaList := result.Hits1.Hits2
	for _, v := range metaList {
		info = append(info, v.Instanceinfo)
	}
	return info, nil

}

// Commondb2 创建db2 datax格式json体， reader：db2， writer：stork、hdfs（hive，tdh）
func Commondb2(sp *source.NewAcc, db *metadata.AccDbInfo, readercreate func(*source.NewAcc, *metadata.AccDbInfo) string, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting
	schemaTable := sp.Extractdb.Schema + "." + "\"" + sp.Extractdb.Table + "\""
	if sp.Extractdb.Createfield != "" {
		schemaTable = sp.Extractdb.Schema + "." + `\"` + sp.Extractdb.Table + `\"`
	}
	db2reader := readercreate(sp, db)
	db2reader, _ = sjson.Set(db2reader, "name", "rdbmsreader")
	// db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.table.0", "\""+sp.Extractdb.Table+"\"")
	db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.table.0", schemaTable)
	//db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.jdbcUrl.0", fmt.Sprintf("jdbc:db2://%s:%s/%s", db.NormalConn.DBIP, db.NormalConn.DBPort, db.NormalConn.Database))
	db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	//-----------------------------
	//如果有增量的话
	if sp.Extractdb.Createfield != "" {
		engineid, err := GetEngineid(sp.Sourcedb.Layerid)
		if err != nil {
			return templete, err
		}

		var z int
		var istime, isdate bool
		var timetrans string
		var isint bool

		if len(sp.Extractdb.Field) > 0 {
			for i, v := range sp.Extractdb.Field {
				if v == sp.Extractdb.Createfield {
					z = i
				}
			}
			if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATE") {
				isdate = true
			}

			if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") {
				istime = true
			}
			//是否是整型的增量
			if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") {
				isint = true
			}
		}

		//抽取脏数据----------------------------------------------------------------
		var dirtydata string
		var nodirtytrans string
		var nodirtyreader string
		var nodirtytemp string
		//*第一次抽取不加入初始时间
		var firsttimetrans string
		if sp.Extractdb.Supportdirtydata == true {
			switch sp.Extractdb.Timetype {
			case "yyyyMMdd":
				dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=8  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy-MM-dd":
				dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=10 or \"%s\" not  like '%%-%%-%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy/MM/dd":
				dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=10 or  \"%s\" not  like '%%/%%/%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
				dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%-%%-%% %%:%%:%%' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
				dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%/%%/%% %%:%%:%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
				dirtydata = fmt.Sprintf(` or    LENGTH(RTRIM(\"%s\"))!=14  or \"%s\" is null `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			default:
				dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			}
			//脏数据
			if isint {
				dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			}
		}
		//------------------------------------------------------------------------------------------------------------

		//1.如果字段是字符串类型的
		// sp.Extractdb.Timetype = "yyyyMMdd"
		/*switch sp.Extractdb.Timetype {
				case "yyyyMMdd":
					timetrans = fmt.Sprintf(`(case when LENGTH(to_char(\"%s\"))=8  and  LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0   THEN  to_date(\"%s\",'YYYYMMDD')    END)
		>to_date(substr('${starttime}',1,8),'YYYYMMDD')  AND
		(case when LENGTH(to_char(\"%s\"))=8 and  LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  THEN  to_date(\"%s\",'YYYYMMDD')    END)
		<=(to_date(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`(case when LENGTH(to_char(\"%s\"))=8  and LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  THEN  to_date(\"%s\",'YYYYMMDD')    END)
		<=(to_date(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyyMMdd")
				case "yyyy-MM-dd":
					timetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))=10 and \"%s\" like '%%-%%-%%' AND LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  THEN  to_date(\"%s\",'YYYYMMDD')    END)
		>to_date(substr('${starttime}',1,8),'YYYYMMDD')  AND
		(case when   LENGTH(to_char(\"%s\"))=10 and  \"%s\"  like '%%-%%-%%' AND LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  THEN  to_date(\"%s\",'YYYYMMDD')    END)
		<=(to_date(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

					firsttimetrans = fmt.Sprintf(`(case when   LENGTH(to_char(\"%s\"))=10 and  \"%s\"  like '%%-%%-%%' AND LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  THEN  to_date(\"%s\",'YYYYMMDD')    END)
		<=(to_date(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy-MM-dd")

				case "yyyy/MM/dd":
					timetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))=10 and  \"%s\"  like '%%/%%/%%' AND LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0 THEN  to_date(\"%s\",'YYYYMMDD')    END)
		>to_date(substr('${starttime}',1,8),'YYYYMMDD')  AND
		(case when  LENGTH(to_char(\"%s\"))=10  and  \"%s\"  like '%%/%%/%%' AND LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0 THEN  to_date(\"%s\",'YYYYMMDD')    END)
		<=(to_date(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))=10  and  \"%s\"  like '%%/%%/%%' AND LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  THEN  to_date(\"%s\",'YYYYMMDD')    END)
		<=(to_date(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy/MM/dd")
				case "yyyy-MM-dd HH:mm:ss":
					timetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))>=14  and  \"%s\"  like '%%-%%-%% %%:%%:%%' THEN   to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		>to_date('${starttime}','YYYYMMDDHH24MISS')  AND
		(case when  LENGTH(to_char(\"%s\"))>=14 and \"%s\"  like '%%-%%-%% %%:%%:%%' THEN  to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		<to_date('${endtime}','YYYYMMDDHH24MISS')`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))>=14 and \"%s\"  like '%%-%%-%% %%:%%:%%' THEN  to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		<to_date('${endtime}','YYYYMMDDHH24MISS')`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy-MM-dd HH:mm:ss")

				case "yyyy/MM/dd HH:mm:ss":
					timetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))>=14 and \"%s\"  like '%%/%%/%% %%:%%:%%' THEN   to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		>to_date('${starttime}','YYYYMMDDHH24MISS')  AND
		(case when  LENGTH(to_char(\"%s\"))>=14 and \"%s\"  like '%%/%%/%% %%:%%:%%' THEN  to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		<to_date('${endtime}','YYYYMMDDHH24MISS')`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))>=14 and \"%s\"  like '%%/%%/%% %%:%%:%%' THEN  to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		<to_date('${endtime}','YYYYMMDDHH24MISS')`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy/MM/dd HH:mm:ss")

				case "yyyyMMddHHmmss":
					timetrans = fmt.Sprintf(`(case  when LENGTH(to_char(\"%s\"))=14 THEN   to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		>to_date('${starttime}','YYYYMMDDHH24MISS')  AND
		(case when  LENGTH(to_char(\"%s\"))=14 THEN  to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		<to_date('${endtime}','YYYYMMDDHH24MISS')`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`(case when  LENGTH(to_char(\"%s\"))=14 THEN  to_date(SUBSTRING(to_char(\"%s\"),1,20),'YYYYMMDDHH24MISS')   END)
		<to_date('${endtime}','YYYYMMDDHH24MISS')`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyyMMddHHmmss")

						}*/

		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8  and  LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8  and  LENGTH( replace(translate(\"%s\",'','0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyyMMdd")
		case "yyyy-MM-dd":

			timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=10 and \"%s\" like '%%-%%-%%'  and  LENGTH( replace(translate(\"%s\",'','-0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8 and \"%s\" like '%%-%%-%%' and  LENGTH( replace(translate(\"%s\",'','-0123456789'),' ','') )=0  and  \"%s\" < '${endtime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy-MM-dd")

		case "yyyy/MM/dd":
			timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=10 and \"%s\" like '%%/%%/%%'  and  LENGTH( replace(translate(\"%s\",'','/0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))=8 and \"%s\" like '%%/%%/%%' and  LENGTH( replace(translate(\"%s\",'','/0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", "yyyy/MM/dd")

		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":

			timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%-%%-%% %%:%%:%%'   and  LENGTH( replace(translate(\"%s\",'',':-.0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%-%%-%% %%:%%:%%'  and  LENGTH( replace(translate(\"%s\",'',':-.0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.Incre_col_hyphen)
		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%/%%/%% %%:%%:%%'   and  LENGTH( replace(translate(\"%s\",'',':/.0123456789'),' ','') )=0  and  \"%s\" >'${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=19 and \"%s\" like  '%%/%%/%% %%:%%:%%'  and  LENGTH( replace(translate(\"%s\",'',':/.0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.Incre_col_slash)
		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			timetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=14    and  LENGTH( replace(translate(\"%s\",'','.0123456789'),' ','') )=0  and  \"%s\" > '${starttime}'   AND \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` LENGTH(to_char(\"%s\"))>=14   and  LENGTH( replace(translate(\"%s\",'','.0123456789'),' ','') )=0  and  \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.Incre_col_origin)
		case common.AutoIdCreate:
			timetrans = fmt.Sprintf(` \"%s\" >'${starttime}' `, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` \"%s\" is not null `, sp.Extractdb.Createfield)
			db2reader, _ = sjson.Set(db2reader, "parameter.increColFormat", common.AutoIdCreate)
		}

		if isdate {
			timetrans = fmt.Sprintf(`\"%s\" >cast('${starttime}' as timestamp)  AND \"%s\" <cast('${endtime}' as timestamp)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`\"%s\" < cast('${endtime}' as timestamp)`, sp.Extractdb.Createfield)
		}

		if istime {
			//2.如果增量字段是时间类型的
			timetrans = fmt.Sprintf(`\"%s\" >cast('${starttime}' as timestamp)  AND \"%s\" <cast('${endtime}' as timestamp)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`\"%s\" <cast('${endtime}' as timestamp)`, sp.Extractdb.Createfield)
		}

		if isint {
			timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
			firsttimetrans = " 1=1 "
		}
		nodirtytrans = timetrans
		firsttimetrans = firsttimetrans + dirtydata
		if sp.Extractdb.Wherecontent != "" {
			sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "

		}
		db2reader, _ = sjson.Set(db2reader, "parameter.firstDataCollection", sp.Extractdb.Supportdirtydata)
		db2reader, _ = sjson.Set(db2reader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
		db2reader, _ = sjson.Set(db2reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
		nodirtyreader, _ = sjson.Set(db2reader, "parameter.where", sp.Extractdb.Wherecontent+nodirtytrans)

		templete := fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, db2reader, writerjson)

		//-----------------------------
		//拿到不脏的template
		// fmt.Println(dirtydata)
		// nodirtytemp := strings.Replace(templete, dirtydata, "", 1)
		// fmt.Println(nodirtytemp)
		nodirtytemp = fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)

		//---------------------------------------------------
		if sp.Tasktype == 3 {
			templete = GetCreatedataForBatch(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table,
				sp.Extractdb.Createfield, engineid, sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
		} else {
			templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table,
				sp.Extractdb.Createfield, engineid, sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
		}

		return templete, nil
	}
	//=============================
	templete = fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, db2reader, writerjson)
	fmt.Println("----单例抽取templete-----")
	fmt.Println(templete)
	fmt.Println("----单例抽取templete-----")
	return templete, nil
}

// 生成SQLServer抽取json
func CommonSQLServer(sp *source.NewAcc, db *metadata.AccDbInfo, readercreate func(*source.NewAcc, *metadata.AccDbInfo) string, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting
	sqlServerReader := readercreate(sp, db)
	sqlServerReader, _ = sjson.Set(sqlServerReader, "name", "sqlserverreader")
	//sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.connection.0.jdbcUrl.0", fmt.Sprintf("**************************************", db.NormalConn.DBIP, db.NormalConn.DBPort, db.NormalConn.Database))
	sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	//-----------------------------

	//增量

	if sp.Extractdb.Createfield != "" {
		engineid, err := GetEngineid(sp.Sourcedb.Layerid)
		if err != nil {
			return templete, err
		}

		var z int
		var istime, isdate bool
		var timetrans string
		var isint bool
		//*第一次抽取不加入初始时间
		var firsttimetrans string

		if len(sp.Extractdb.Field) > 0 {
			for i, v := range sp.Extractdb.Field {
				if v == sp.Extractdb.Createfield {
					z = i
				}
			}

			if strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "DATE" {
				isdate = true
			}
			if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") || strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
				istime = true
			}
			//是否是整型的增量
			if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") {
				isint = true
			}
		}

		//抽取脏数据----------------------------------------------------------------
		var dirtydata string
		var nodirtytrans string
		var nodirtyreader string
		var nodirtytemp string

		if sp.Extractdb.Supportdirtydata == true {
			switch sp.Extractdb.Timetype {
			case "yyyyMMdd":
				dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=8  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy-MM-dd":
				dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=10 or \"%s\" not  like '%%-%%-%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy/MM/dd":
				dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=10 or  \"%s\" not  like '%%/%%/%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy-MM-dd HH:mm:ss":
				dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%-%%-%% %%:%%:%%' or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy/MM/dd HH:mm:ss":
				dirtydata = fmt.Sprintf(` or  \"%s\"  not  like '%%/%%/%% %%:%%:%%'  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyyMMddHHmmss":
				dirtydata = fmt.Sprintf(` or    LEN(cast (\"%s\" as varchar))!=14  or \"%s\" is null `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			default:
				dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			}
			//脏数据
			if isint {
				dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			}
		}
		//------------------------------------------------------------------------------------------------------------
		/*		var starttime, endtime string
				switch len(sp.Extractdb.Timetype) {
				case 8, 10:
					// starttime = ` cast(left('${starttime}',8) as datetime)`
					// endtime = `cast(left('${endtime}',8) as datetime)`
					starttime = ` cast(cast(left('${starttime}',8) as int) as varchar)`
					endtime = `cast(cast(left('${endtime}',8) as int)-1 as varchar)`
				default:
					// starttime = ` cast( (SUBSTRING('${starttime}',1,4)+'-'+SUBSTRING('${starttime}',5,2)+'-'+SUBSTRING('${starttime}',7,2)+' '+SUBSTRING('${starttime}',9,2)+':'+SUBSTRING('${starttime}',11,2)+':'+SUBSTRING('${starttime}',13,2)) as datetime)`
					// endtime = ` cast( (SUBSTRING('${endtime}',1,4)+'-'+SUBSTRING('${endtime}',5,2)+'-'+SUBSTRING('${endtime}',7,2)+' '+SUBSTRING('${endtime}',9,2)+':'+SUBSTRING('${endtime}',11,2)+':'+SUBSTRING('${endtime}',13,2)) as datetime)`
					starttime = ` cast( (SUBSTRING('${starttime}',1,4)+'-'+SUBSTRING('${starttime}',5,2)+'-'+SUBSTRING('${starttime}',7,2)+' '+SUBSTRING('${starttime}',9,2)+':'+SUBSTRING('${starttime}',11,2)+':'+SUBSTRING('${starttime}',13,2)) as varchar)`
					endtime = ` cast( (SUBSTRING('${endtime}',1,4)+'-'+SUBSTRING('${endtime}',5,2)+'-'+SUBSTRING('${endtime}',7,2)+' '+SUBSTRING('${endtime}',9,2)+':'+SUBSTRING('${endtime}',11,2)+':'+SUBSTRING('${endtime}',13,2)) as varchar)`

					starttime = `'${starttime}'`
					endtime = `'${endtime}'`
						}*/

		//1.如果字段是字符串类型的
		switch sp.Extractdb.Timetype {
		/*case "yyyyMMdd":
						timetrans = fmt.Sprintf(`(case when LEN(\"%s\")=8   THEN  cast(\"%s\" as datetime)    END)
		>%s  AND (case when LEN(\"%s\")=8  THEN  cast(\"%s\" as datetime)    END)
		<=(%s-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, starttime, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						firsttimetrans = fmt.Sprintf(` (case when LEN(\"%s\")=8  THEN  cast(\"%s\" as datetime)    END)
		<=(%s-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyyMMdd")

					case "yyyy-MM-dd":
						timetrans = fmt.Sprintf(`(case when  LEN(\"%s\")=10 and \"%s\" like '%%-%%-%%'  THEN cast(\"%s\" as datetime)    END)
		>%s  AND (case when   LEN(\"%s\")=10 and  \"%s\"  like '%%-%%-%%'  THEN cast(\"%s\" as datetime)    END)
		<=(%s-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, starttime, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						firsttimetrans = fmt.Sprintf(` (case when   LEN(\"%s\")=10 and  \"%s\"  like '%%-%%-%%'  THEN cast(\"%s\" as datetime)    END)
		<=(%s-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy-MM-dd")

					case "yyyy/MM/dd":
						timetrans = fmt.Sprintf(`(case when  LEN(\"%s\")=10 and  \"%s\"  like '%%/%%/%%' THEN cast(\"%s\" as datetime)    END)
		>%s  AND (case when  LEN(\"%s\")=10  and  \"%s\"  like '%%/%%/%%' THEN cast(\"%s\" as datetime)    END)
		<=(%s-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, starttime, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						firsttimetrans = fmt.Sprintf(` (case when  LEN(\"%s\")=10  and  \"%s\"  like '%%/%%/%%' THEN cast(\"%s\" as datetime)    END)
		<=(%s-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy/MM/dd")

					case "yyyy-MM-dd HH:mm:ss":
						timetrans = fmt.Sprintf(`(case when  LEN(\"%s\")>=14  and  \"%s\"  like '%%-%%-%% %%:%%:%%' THEN  cast(\"%s\" as datetime)    END)
		>%s  AND (case when  LEN(\"%s\")>=14 and \"%s\"  like '%%-%%-%% %%:%%:%%' THEN   cast(\"%s\" as datetime)   END)
		<%s`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, starttime, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						firsttimetrans = fmt.Sprintf(` (case when  LEN(\"%s\")>=14 and \"%s\"  like '%%-%%-%% %%:%%:%%' THEN   cast(\"%s\" as datetime)   END)
		<%s`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)
						sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy-MM-dd HH:mm:ss")

					case "yyyy/MM/dd HH:mm:ss":
						timetrans = fmt.Sprintf(`(case when  LEN(\"%s\")>=14 and \"%s\"  like '%%/%%/%% %%:%%:%%' THEN  cast(\"%s\" as datetime)   END)
		>%s  AND(case when  LEN(\"%s\")>=14 and \"%s\"  like '%%/%%/%% %%:%%:%%' THEN  cast(\"%s\" as datetime)   END)
		<%s`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, starttime, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)

						firsttimetrans = fmt.Sprintf(`(case when  LEN(\"%s\")>=14 and \"%s\"  like '%%/%%/%% %%:%%:%%' THEN  cast(\"%s\" as datetime)   END)
		<%s`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, starttime, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, endtime)

						sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy/MM/dd HH:mm:ss")

					case "yyyyMMddHHmmss":
						concat := fmt.Sprintf(`cast( (SUBSTRING(\"%s\",1,4)+'-'+SUBSTRING(\"%s\",5,2)+'-'+SUBSTRING(\"%s\",7,2)+' '+SUBSTRING(\"%s\",9,2)+':'+SUBSTRING(\"%s\",11,2)+':'+SUBSTRING(\"%s\",13,2)) as datetime)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
						timetrans = fmt.Sprintf(`(case  when LEN(\"%s\")=14 THEN   \"%s\"    END)
		>%s  AND (case when  LEN(\"%s\")=14 THEN %s    END)
		<%s`, sp.Extractdb.Createfield, concat, starttime, sp.Extractdb.Createfield, concat, endtime)
						firsttimetrans = fmt.Sprintf(` (case when  LEN(\"%s\")=14 THEN  %s    END)
		<%s`, sp.Extractdb.Createfield, concat, endtime)
						sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyyMMddHHmmss")*/
		case "yyyyMMdd":
			timetrans = fmt.Sprintf(` LEN(\"%s\")=8 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")=8 and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyyMMdd")

		case "yyyy-MM-dd":
			timetrans = fmt.Sprintf(` LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%-%%-%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\"  like '%%-%%-%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy-MM-dd")

		case "yyyy/MM/dd":
			timetrans = fmt.Sprintf(` LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%/%%/%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")=10 and \"%s\" < '${endtime}' and \"%s\"  like '%%/%%/%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", "yyyy-MM-dd")

		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
			timetrans = fmt.Sprintf(` LEN(\"%s\")>=19 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%-%%-%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")>=19  and \"%s\" < '${endtime}' and \"%s\"  like '%%-%%-%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.Incre_col_hyphen)

		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			timetrans = fmt.Sprintf(` LEN(\"%s\")>=19 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%/%%/%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")>=19  and \"%s\" < '${endtime}' and \"%s\"  like '%%/%%/%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.Incre_col_slash)

		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			timetrans = fmt.Sprintf(` LEN(\"%s\")>=14 and \"%s\" < '${endtime}' and \"%s\" > '${starttime}' and \"%s\"  like '%%/%%/%% %%:%%:%%'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			firsttimetrans = fmt.Sprintf(`  LEN(\"%s\")>=14  and \"%s\" < '${endtime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

			sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.Incre_col_origin)
		case common.AutoIdCreate:
			timetrans = fmt.Sprintf(` \"%s\" >'${starttime}' `, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` \"%s\" is not null `, sp.Extractdb.Createfield)
			sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increColFormat", common.AutoIdCreate)

		}

		//---------------------
		//获取sqlserver版本，默认datetime2
		var is2low bool
		info, err := MetaAccDbInfo(sp.Extractdb.Id)
		if err != nil {
			//logger.Error.Printf("根据engineid获取引擎信息失败:%s", err.Error())
			logger.Error.Printf("db2引擎信息获取失败")

		}
		err = AddMGSource(info)
		if err != nil {
			logger.Error.Printf("db2向mg添加数据源失败:%s\n", err.Error())
		}
		uri2 := fmt.Sprintf("/meta-gateway/executeQuery?datasourceId=%s", sp.Extractdb.Id)
		s := `SELECT
  CASE 
     WHEN CONVERT(VARCHAR(128), SERVERPROPERTY ('productversion')) like '8%' THEN 'islow'
     WHEN CONVERT(VARCHAR(128), SERVERPROPERTY ('productversion')) like '9%' THEN 'islow'
     ELSE 'isbig'
  END AS MajorVersion `
		res, err := collect.MGPOST(uri2, s)
		if err != nil {
			logger.Error.Println("MG请求出错:%s", err)
		}

		if strings.Contains(string(res), `"MajorVersion":"islow"`) {
			is2low = true
		}
		logger.Info.Println("mg db2查询的信息是什么", string(res), is2low)

		//-------------------------

		if isdate {

			/*			timetrans = fmt.Sprintf(` \"%s\"  > cast('${starttime}' as date) and \"%s\" < cast('${endtime}' as date) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
						firsttimetrans = fmt.Sprintf(` \"%s\" < cast('${endtime}' as date) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			*/

			timetrans = fmt.Sprintf(` \"%s\"   >cast('${starttime}' as datetime2) and \"%s\" < cast('${endtime}' as datetime2) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  \"%s\" <cast('${endtime}' as datetime2) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		}
		if istime {
			//2.如果增量字段是时间类型的

			timetrans = fmt.Sprintf(` \"%s\"   >cast('${starttime}' as datetime2) and \"%s\" < cast('${endtime}' as datetime2) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  \"%s\" <cast('${endtime}' as datetime2) and \"%s\"  is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		}

		if is2low {
			timetrans = strings.Replace(timetrans, `as datetime2`, `as datetime`, -1)
			firsttimetrans = strings.Replace(firsttimetrans, `as datetime2`, `as datetime`, -1)
		}

		//---------------
		//语句处理
		if isint {
			timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
			firsttimetrans = " 1=1 "
		}
		//-------------------
		nodirtytrans = timetrans
		firsttimetrans = firsttimetrans + dirtydata
		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.firstDataCollection", sp.Extractdb.Supportdirtydata)
		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, `"`, "", -1))
		var wherecont string
		if sp.Extractdb.Wherecontent != "" {
			wherecont = sp.Extractdb.Wherecontent + " and "
		}
		sqlServerReader, _ = sjson.Set(sqlServerReader, "parameter.where", wherecont+firsttimetrans)

		nodirtyreader, _ = sjson.Set(sqlServerReader, "parameter.where", wherecont+nodirtytrans)

		templete := fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, sqlServerReader, writerjson)

		//-----------------------------
		//拿到不脏的template
		// fmt.Println(dirtydata)
		// nodirtytemp := strings.Replace(templete, dirtydata, "", 1)
		// fmt.Println(nodirtytemp)
		nodirtytemp = fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)

		//---------------------------------------------------
		//获取原表中增量目的表的对应字段
		var num int
		for k, v := range sp.Extractdb.Field {
			if v == sp.Extractdb.Createfield {
				num = k
			}
		}

		//---------------------------------------------------
		if sp.Tasktype == 3 {
			templete = GetCreatedataForBatch(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table,
				sp.Sourcedb.Field[num], engineid, sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
		} else {
			templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table,
				sp.Sourcedb.Field[num], engineid, sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
		}

		return templete, nil

	}

	//=============================
	templete = fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, sqlServerReader, writerjson)
	fmt.Println("----sqlserver单例抽取templete-----")
	fmt.Println(templete)
	fmt.Println("----sqlserver单例抽取templete-----")
	return templete, nil
}

func GetTDHKRB(layerid string) (source.TdhKbs, error) {
	fmt.Println("------11111111111111111111layerid-------", layerid)
	var tdhkrb source.TdhKbs
	body := gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"id": layerid,
					},
				},
			},
		},
	}
	byteBody, _ := json.Marshal(body)
	uri := fmt.Sprintf("/%s/%s/_search", common.DataBaseAssert, common.TbLayer)
	resp, err := httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return tdhkrb, err
	}
	engineid := gjson.Get(string(resp), "hits.hits.0._source.engineid").String()
	body = gin.H{
		"query": gin.H{
			"bool": gin.H{
				"must": gin.H{
					"term": gin.H{
						"engineid": engineid,
					},
				},
			},
		},
	}
	byteBody, _ = json.Marshal(body)
	uri = fmt.Sprintf("/%s/%s/_search", common.DBPlatform, common.TBEngine)
	resp, err = httpclient.Post(uri, string(byteBody))
	if err != nil {
		logger.Error.Println(err)
		return tdhkrb, err
	}
	fmt.Println("resp:", string(resp))
	tdhkrb.IsKbs = gjson.Get(string(resp), "hits.hits.0._source.tdhkbsinfo.iskbs").Bool()
	tdhkrb.Keytab = gjson.Get(string(resp), "hits.hits.0._source.tdhkbsinfo.keytab").String()
	tdhkrb.VerifiedUser = gjson.Get(string(resp), "hits.hits.0._source.tdhkbsinfo.verifieduser").String()
	tdhkrb.TdhPath = gjson.Get(string(resp), "hits.hits.0._source.tdhkbsinfo.tdhpath").String()
	return tdhkrb, nil
}

// CommonHive hive抽取源单例抽取处理
func CommonHive(sp *source.NewAcc, db *metadata.AccDbInfo, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting
	reader := common.HiveReader
	username := db.HiveConn.HiveUser
	if username == "" {
		username = "hive"
	}
	password := db.HiveConn.HivePassword
	if password == "" {
		password = common.DefaultPwd
	}
	reader, _ = sjson.Set(reader, "name", "hivereader")
	reader, _ = sjson.Set(reader, "parameter.username", username)
	reader, _ = sjson.Set(reader, "parameter.password", password)
	if sp.AdvancedConfig.SplitKey != "" {
		reader, _ = sjson.Set(reader, "parameter.splitPk", sp.AdvancedConfig.SplitKey)
	}
	for i, v := range sp.Extractdb.Field {
		reader, _ = sjson.Set(reader, "parameter.column."+strconv.Itoa(i), "`"+v+"`")
	}
	reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent)
	reader, _ = sjson.Set(reader, "parameter.connection.0.table.0", "`"+sp.Extractdb.Table+"`")
	//reader, _ = sjson.Set(reader, "parameter.connection.0.jdbcUrl.0", db.HiveConn.HiveURL)
	reader, _ = sjson.Set(reader, "parameter.connection.0.jdbcUrl.0", collect.URLHive(*db))
	if db.HiveConn.AuthMethod == 2 {
		// 带kerberos认证
		reader, _ = sjson.Set(reader, "parameter.haveKerberos", true)
		reader, _ = sjson.Set(reader, "parameter.kerberosKeytabFilePath", db.HiveConn.KeytabFilePath)
		reader, _ = sjson.Set(reader, "parameter.kerberosPrincipal", db.HiveConn.Principal)
		reader, _ = sjson.Set(reader, "parameter.krb5ConfigFilePath", db.HiveConn.Krb5ConfPath)
	}
	reader, _ = sjson.Set(reader, "parameter.driverJarPath", db.HiveConn.DriveInfo.DriverPath)
	// 单例抽取
	if sp.Extractdb.Createfield == "" {
		templete = fmt.Sprintf(`{
             %s
			"job": {
					%s
				"content": [
					{
						"reader": 
						 %s, 
						"writer": 
						 %s
					}
				]
			}
		}`, sp.AdvancedConfig.Core, setting, reader, writerjson)
		return templete, nil
	}
	// 增量抽取
	// 是否符合标准日期格式
	var istime bool
	// 增量字段
	var addfield, addFieldType string
	var isint bool
	for i, v := range sp.Extractdb.Field {
		// 匹配增量时间字段
		if v == sp.Extractdb.Createfield {
			addfield = v
			addFieldType = strings.ToUpper(sp.Extractdb.Fieldtype[i])
			if strings.Contains(addFieldType, "TIMESTAMP") ||
				strings.Contains(addFieldType, "DATE") {
				istime = true
			}
			if strings.Contains(addFieldType, "INT") {
				isint = true
			}
			break
		}
	}
	// 增量字段--关键字/非标准字符处理
	sp.Extractdb.Createfield = strings.Replace(sp.Extractdb.Createfield, "`", "", -1)
	sp.Extractdb.Createfield = fmt.Sprintf("`%s`", sp.Extractdb.Createfield)
	// 脏数据处理
	var dirtydata string
	if sp.Extractdb.Supportdirtydata == true {
		// 字符串类型-增量脏数据处理
		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{8}$' or %s is null`,
				sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' or %s is null`,
				sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy/MM/dd":
			dirtydata = fmt.Sprintf(` or %s not  regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' or %s is null`,
				sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		case "yyyy-MM-dd HH:mm:ss":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null`,
				sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
		case "yyyy/MM/dd HH:mm:ss":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null`,
				sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
		case "yyyyMMddHHmmss":
			dirtydata = fmt.Sprintf(` or %s not  regexp '%s' or %s is null `,
				sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
		default:
			// 时间类型date/timestamp--脏数据处理
			dirtydata = fmt.Sprintf(`  or %s is null  `, sp.Extractdb.Createfield)
		}
		//脏数据
		if isint {
			dirtydata = fmt.Sprintf(`  or %s is null  `, sp.Extractdb.Createfield)
		}
	}
	// 增量处理
	// 抽取策略--不包含第一次抽取
	var timetrans string
	// 第一次抽取策略
	var firsttimetrans string
	// 变量说明(从前往后)：时间类型、增量时间转换、任务开始时间转换、任务结束时间转换、时间正则
	var timeType, addDate, startDate, endDate, timeExp string
	timeType = sp.Extractdb.Timetype
	// 转换变量处理
	switch timeType {
	// 字符串类型
	case "yyyyMMdd":
		addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyyMMdd'), 'yyyy-MM-dd')", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		endDate = fmt.Sprintf("date_add(%s, -1)", endDate)
	case "yyyy-MM-dd":
		addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy-MM-dd'), 'yyyy-MM-dd')", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		endDate = fmt.Sprintf("date_add(%s, -1)", endDate)
	case "yyyy/MM/dd":
		addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy/MM/dd'), 'yyyy-MM-dd')", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyy-MM-dd')")
		endDate = fmt.Sprintf("date_add(%s, -1)", endDate)
	case "yyyy-MM-dd HH:mm:ss":
		addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy-MM-dd HH:mm:ss'), 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
	case "yyyy/MM/dd HH:mm:ss":
		addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyy/MM/dd HH:mm:ss'), 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
	case "yyyyMMddHHmmss":
		addDate = fmt.Sprintf("from_unixtime(unix_timestamp(%s,'yyyyMMddHHmmss'), 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
	default:
		// 时间类型
		addDate = fmt.Sprintf("date_format(regexp_replace(cast(%s as string), '/', '-') , 'yyyyMMddHHmmss')", sp.Extractdb.Createfield)
		startDate = fmt.Sprintf("from_unixtime(unix_timestamp('${starttime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss'), 'yyyyMMddHHmmss')")
		//if strings.Contains(strings.ToUpper(addFieldType), "DATE") {
		//	endDate = fmt.Sprintf("date_add(%s, -1)", endDate)
		//}
		endDate = fmt.Sprintf("from_unixtime(unix_timestamp('${endtime}','yyyyMMddHHmmss')-86400)")
		if strings.Contains(strings.ToUpper(addFieldType), "DATE") {
			endDate = fmt.Sprintf("date_format(%s, 'yyyyMMddHHmmss')", endDate)
		}
	}
	// 时间正则处理
	switch timeType {
	case "yyyyMMdd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{8}$' ", sp.Extractdb.Createfield)
	case "yyyy-MM-dd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyy/MM/dd":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyy-MM-dd HH:mm:ss":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}-[0-9]{2}-[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyy/MM/dd HH:mm:ss":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{4}/[0-9]{2}/[0-9]{2} +[0-9]{2}:[0-9]{2}:[0-9]{2}$' ", sp.Extractdb.Createfield)
	case "yyyyMMddHHmmss":
		timeExp = fmt.Sprintf("%s regexp '^[0-9]{14}$' ", sp.Extractdb.Createfield)
	default:
	}
	fmt.Println(addDate)
	var timeExpStr = timeExp + " and "
	if istime {
		// 时间类型不包含正则
		timeExpStr = ""
	}
	/*	timetrans = fmt.Sprintf(" %s %s >%s and %s<=%s  ", timeExpStr, addDate, startDate, addDate, endDate)
		firsttimetrans = fmt.Sprintf(" %s  %s<=%s  ", timeExpStr, addDate, endDate)*/
	switch timeType {
	// 字符串类型
	case "yyyyMMdd":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
	case "yyyy-MM-dd":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
	case "yyyy/MM/dd":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		startDate = `'${starttime}'`
		endDate = `'${endtime}'`
	default:
		// 时间类型
		startDate = ` cast('${starttime}' as timestamp)`
		endDate = ` cast('${endtime}' as timestamp) `
	}
	timetrans = fmt.Sprintf(" %s %s >%s and %s<%s  ", timeExpStr, sp.Extractdb.Createfield, startDate, sp.Extractdb.Createfield, endDate)
	firsttimetrans = fmt.Sprintf(" %s  %s<%s  ", timeExpStr, sp.Extractdb.Createfield, endDate)
	//语句处理
	if isint {
		timetrans = fmt.Sprintf(` %s >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = " 1=1 "
	}

	if timeType == common.AutoIdCreate {
		timetrans = fmt.Sprintf(` %s >'${starttime}' `, sp.Extractdb.Createfield)
		firsttimetrans = fmt.Sprintf(` %s  is not null `, sp.Extractdb.Createfield)
	}

	switch timeType {
	case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
		timeType = common.Incre_col_hyphen
	case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
		timeType = common.Incre_col_slash
	case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
		timeType = common.Incre_col_origin
	case common.AutoIdCreate:
		timeType = common.AutoIdCreate
	}
	reader, _ = sjson.Set(reader, "parameter.increColFormat", timeType)

	firsttimetrans = firsttimetrans + dirtydata
	if sp.Extractdb.Wherecontent != "" {
		sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "
	}
	reader, _ = sjson.Set(reader, "parameter.firstDataCollection", sp.Extractdb.Supportdirtydata)
	reader, _ = sjson.Set(reader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
	// 第一次抽取reader
	reader, _ = sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
	// 抽取reader--不包含第一次抽取
	nodirtyreader, _ := sjson.Set(reader, "parameter.where", sp.Extractdb.Wherecontent+timetrans)
	// 第一次抽取模板
	templete = fmt.Sprintf(`{
         %s
		"job": {
				%s
			"content": [
				{
					"reader": 
					%s, 
					"writer": 
					%s
				}
			]
		}
		}`, sp.AdvancedConfig.Core, setting, reader, writerjson)

	// 抽取模板--不包含第一次抽取
	nodirtytemp := fmt.Sprintf(`{
        %s
		"job": {
				%s
			"content": [
				{
					"reader": 
					%s, 
					"writer": 
					%s
				}
			]
		}
		}`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)
	// 模板修正
	if sp.Tasktype == 3 {
		templete = GetCreatedataForBatch(templete, sp.Sourcedb.Odsdatabase,
			sp.Sourcedb.Table, addfield, sp.Sourcedb.Engineid,
			sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
	} else {
		templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase,
			sp.Sourcedb.Table, addfield, sp.Sourcedb.Engineid,
			sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
	}
	sp.Extractdb.Createfield = strings.Replace(sp.Extractdb.Createfield, "`", "", -1)
	return templete, nil
}

func CommonDaMeng(sp *source.NewAcc, db *metadata.AccDbInfo, readercreate func(*source.NewAcc, *metadata.AccDbInfo) string, writerjson string) (string, error) {
	var templete string
	var setting = sp.AdvancedConfig.Setting
	//schemaTable := "\""+sp.Extractdb.Schema +"\""+ "." + "\""+sp.Extractdb.Table+"\""
	dmreader := readercreate(sp, db)
	dmreader, _ = sjson.Set(dmreader, "name", "rdbmsreader")
	// db2reader, _ = sjson.Set(db2reader, "parameter.connection.0.table.0", "\""+sp.Extractdb.Table+"\"")
	//dmreader, _ = sjson.Set(dmreader, "parameter.connection.0.table.0", schemaTable)
	//dmreader, _ = sjson.Set(dmreader, "parameter.connection.0.jdbcUrl.0", fmt.Sprintf("jdbc:dm://%s:%s", db.NormalConn.DBIP, db.NormalConn.DBPort))
	dmreader, _ = sjson.Set(dmreader, "parameter.connection.0.jdbcUrl.0", collect.URLNormalDB(*db))
	//-----------------------------
	//如果有增量的话
	if sp.Extractdb.Createfield != "" {
		engineid, err := GetEngineid(sp.Sourcedb.Layerid)
		if err != nil {
			return templete, err
		}

		var z int
		var istime, isdate bool
		var timetrans string
		var isint bool

		if len(sp.Extractdb.Field) > 0 {
			for i, v := range sp.Extractdb.Field {
				if v == sp.Extractdb.Createfield {
					z = i
				}
			}
			if strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "DATE" {
				isdate = true
			}
			if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "TIMESTAMP") || strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "DATETIME") {
				istime = true
			}
			//是否是整型的增量
			if strings.Contains(strings.ToUpper(sp.Extractdb.Fieldtype[z]), "INT") || strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "DEC" || strings.ToUpper(sp.Extractdb.Fieldtype[z]) == "BYTE" {
				isint = true
			}
		}

		//抽取脏数据----------------------------------------------------------------
		var dirtydata string
		var nodirtytrans string
		var nodirtyreader string
		var nodirtytemp string
		//*第一次抽取不加入初始时间
		var firsttimetrans string
		if sp.Extractdb.Supportdirtydata == true {
			switch sp.Extractdb.Timetype {
			case "yyyyMMdd":
				dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{8}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy-MM-dd":
				dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy/MM/dd":
				dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  or \"%s\" is null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
				dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
			case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
				dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null`, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
			case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
				dirtydata = fmt.Sprintf(` or  not  regexp_like(\"%s\",'%s')  or \"%s\" is null `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
			default:
				dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			}
			//脏数据
			if isint {
				dirtydata = fmt.Sprintf(`  or \"%s\" is null`, sp.Extractdb.Createfield)
			}
		}
		//------------------------------------------------------------------------------------------------------------

		//1.如果字段是字符串类型的
		// sp.Extractdb.Timetype = "yyyyMMdd"
		/*switch sp.Extractdb.Timetype {
				case "yyyyMMdd":
					timetrans = fmt.Sprintf(`(  (case when length(replace(\"%s\",' ',''))=8  and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{8}$') then to_Date(replace(\"%s\",' ',''), 'YYYYMMDD') end)
		>TO_DATE(substr('${starttime}',1,8),'YYYYMMDD') and (case when length(replace(\"%s\",' ',''))=8 and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{8}$') then to_Date(replace(\"%s\",' ',''), 'YYYYMMDD') end ) <=TO_DATE(substr('${endtime}',1,8),'YYYYMMDD')-1)  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`( (case when length(replace(\"%s\",' ',''))=8 and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{8}$') then to_Date(replace(\"%s\",' ',''), 'YYYYMMDD') end ) <=TO_DATE(substr('${endtime}',1,8),'YYYYMMDD')-1)  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyyMMdd")
				case "yyyy-MM-dd":
					timetrans = fmt.Sprintf(`(  (case when  length(replace(\"%s\",' ',''))=10 and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{4}-[0-9]{2}-[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDD') end)
		>TO_DATE(substr('${starttime}',1,8),'YYYYMMDD') and (case when length(replace(\"%s\",' ',''))=10 and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{4}-[0-9]{2}-[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDD') end ) <=TO_DATE(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`(  (case when length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDD') end ) <=TO_DATE(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy-MM-dd")
				case "yyyy/MM/dd":
					timetrans = fmt.Sprintf(`(  (case when length(replace(\"%s\",' ',''))=10 and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{4}/[0-9]{2}/[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDD') end)
		>TO_DATE(substr('${starttime}',1,8),'YYYYMMDD') and (case when length(replace(\"%s\",' ',''))=10 and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{4}/[0-9]{2}/[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDD') end ) <=TO_DATE(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`(  (case when length(replace(\"%s\",' ',''))=10 and  regexp_like(replace(\"%s\",' ',''),'^[0-9]{4}/[0-9]{2}/[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDD') end ) <=TO_DATE(substr('${endtime}',1,8),'YYYYMMDD')-1)`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy/MM/dd")
				case "yyyy-MM-dd HH:mm:ss":
					timetrans = fmt.Sprintf(`(( case when regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end )
		>TO_DATE('${starttime}','YYYYMMDDHH24MISS') and ( case when regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end ) <=TO_DATE('${endtime}','YYYYMMDDHH24MISS'))`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`( ( case when regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end ) <=TO_DATE('${endtime}','YYYYMMDDHH24MISS'))`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy-MM-dd HH:mm:ss")
				case "yyyy/MM/dd HH:mm:ss":
					timetrans = fmt.Sprintf(`(( case when regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end )
		>TO_DATE('${starttime}','YYYYMMDDHH24MISS') and ( case when regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end ) <=TO_DATE('${endtime}','YYYYMMDDHH24MISS'))`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					firsttimetrans = fmt.Sprintf(`( ( case when regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') then to_Date(REGEXP_REPLACE(to_char(replace(\"%s\",' ','')),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end ) <=TO_DATE('${endtime}','YYYYMMDDHH24MISS'))`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy/MM/dd HH:mm:ss")
				case "yyyyMMddHHmmss":
					timetrans = fmt.Sprintf(`(( case when regexp_like(\"%s\",'^[0-9]{14}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end )
		>TO_DATE('${starttime}','YYYYMMDDHH24MISS') and ( case when regexp_like(\"%s\",'^[0-9]{14}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end ) <=TO_DATE('${endtime}','YYYYMMDDHH24MISS'))`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)

					firsttimetrans = fmt.Sprintf(`( ( case when regexp_like(\"%s\",'^[0-9]{14}$') then to_Date(REGEXP_REPLACE(to_char(\"%s\"),'-|\/|\\s|:',''), 'YYYYMMDDHH24MISS') end ) <=TO_DATE('${endtime}','YYYYMMDDHH24MISS'))`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
					dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyyMMddHHmmss")
						}*/
		switch sp.Extractdb.Timetype {
		case "yyyyMMdd":
			timetrans = fmt.Sprintf(`length(\"%s\")=8 and  regexp_like(\"%s\",'^[0-9]{8}$') and \"%s\" <'${endtime}' and \"%s\" >'${starttime}'  `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` length(\"%s\")=8 and  regexp_like(\"%s\",'^[0-9]{8}$') and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyyMMdd")
		case "yyyy-MM-dd":
			timetrans = fmt.Sprintf(` length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and \"%s\" < '${endtime}' and \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}-[0-9]{2}-[0-9]{2}$')  and \"%s\" <  '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy-MM-dd")
		case "yyyy/MM/dd":
			timetrans = fmt.Sprintf(` length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and \"%s\" < '${endtime}'  and \"%s\" > '${starttime}'`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  length(\"%s\")=10 and  regexp_like(\"%s\",'^[0-9]{4}/[0-9]{2}/[0-9]{2}$')  and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", "yyyy/MM/dd")
		case "yyyyMMddHHmmss", "yyyyMMddHHmmss[.fff]":
			timetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" <'${endtime}' and \"%s\" >'${starttime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(`  regexp_like(\"%s\",'%s') and \"%s\" <'${endtime}'  `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_origin), sp.Extractdb.Createfield)
			dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.Incre_col_origin)
		case "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss[.fff]":
			timetrans = fmt.Sprintf(`regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' and  \"%s\" > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_hyphen), sp.Extractdb.Createfield)
			dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.Incre_col_hyphen)
		case "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss[.fff]":
			timetrans = fmt.Sprintf(`regexp_like(\"%s\",'%s') and \"%s\" <  '${endtime}' and  \"%s\" > '${starttime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` regexp_like(\"%s\",'%s') and \"%s\" < '${endtime}' `, sp.Extractdb.Createfield, escape(common.Regex_ymdhms_slash), sp.Extractdb.Createfield)
			dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.Incre_col_slash)
		case common.AutoIdCreate:
			timetrans = fmt.Sprintf(` \"%s\" >'${starttime}' `, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` \"%s\" is not null `, sp.Extractdb.Createfield)
			dmreader, _ = sjson.Set(dmreader, "parameter.increColFormat", common.AutoIdCreate)
		}

		if isdate {
			/*			timetrans = fmt.Sprintf(` \"%s\" >to_date('${starttime}', 'YYYY-MM-DD')  and \"%s\" <  to_date('${endtime}', 'YYYY-MM-DD')       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
						firsttimetrans = fmt.Sprintf(`  \"%s\" < to_date('${endtime}', 'YYYY-MM-DD') and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			*/
			timetrans = fmt.Sprintf(` \"%s\"  >to_timestamp('${starttime}', 'YYYY-MM-DD HH24:MI:SS')  and \"%s\" <=  to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS')       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` \"%s\" <=to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS') and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		}

		if istime {
			//2.如果增量字段是时间类型的
			timetrans = fmt.Sprintf(` \"%s\"  >to_timestamp('${starttime}', 'YYYY-MM-DD HH24:MI:SS.FF')  and \"%s\" <=  to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS.FF')       and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
			firsttimetrans = fmt.Sprintf(` \"%s\" <=to_timestamp('${endtime}', 'YYYY-MM-DD HH24:MI:SS.FF') and  \"%s\" is not null`, sp.Extractdb.Createfield, sp.Extractdb.Createfield)
		}

		if isint {
			timetrans = fmt.Sprintf(` \"%s\" >${starttime} `, sp.Extractdb.Createfield)
			firsttimetrans = " 1=1 "
		}
		nodirtytrans = timetrans
		firsttimetrans = firsttimetrans + dirtydata
		if sp.Extractdb.Wherecontent != "" {
			sp.Extractdb.Wherecontent = sp.Extractdb.Wherecontent + " and "

		}
		dmreader, _ = sjson.Set(dmreader, "parameter.firstDataCollection", sp.Extractdb.Supportdirtydata)
		dmreader, _ = sjson.Set(dmreader, "parameter.increCol", strings.Replace(sp.Extractdb.Createfield, "`", "", -1))
		dmreader, _ = sjson.Set(dmreader, "parameter.where", sp.Extractdb.Wherecontent+firsttimetrans)
		nodirtyreader, _ = sjson.Set(dmreader, "parameter.where", sp.Extractdb.Wherecontent+nodirtytrans)

		templete := fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, dmreader, writerjson)

		//-----------------------------
		//拿到不脏的template
		// fmt.Println(dirtydata)
		// nodirtytemp := strings.Replace(templete, dirtydata, "", 1)
		// fmt.Println(nodirtytemp)
		nodirtytemp = fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, nodirtyreader, writerjson)

		//---------------------------------------------------
		if sp.Tasktype == 3 {
			templete = GetCreatedataForBatch(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table,
				sp.Extractdb.Createfield, engineid, sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
		} else {
			templete = GetCreatedata(templete, sp.Sourcedb.Odsdatabase, sp.Sourcedb.Table,
				sp.Extractdb.Createfield, engineid, sp.Extractdb.Timetype, nodirtytemp, sp.AdvancedConfig.RAM)
		}

		return templete, nil
	}
	//=============================
	templete = fmt.Sprintf(`{
        %s
	    "job": {
	            %s
	        "content": [
	            {
	                "reader": 
					 %s, 
	                "writer": 
					 %s
	            }
	        ]
	    }
	}`, sp.AdvancedConfig.Core, setting, dmreader, writerjson)
	fmt.Println("----单例抽取templete-----")
	fmt.Println(templete)
	fmt.Println("----单例抽取templete-----")
	return templete, nil
}
