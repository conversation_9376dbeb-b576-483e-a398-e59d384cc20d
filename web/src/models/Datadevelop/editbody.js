/* eslint-disable @typescript-eslint/no-unused-expressions */
import { notification } from 'antd';
import { DatabaseIcon, IconFont } from '@dana/ui';
import { getAllMetaData } from '@/services/Workflow/Flow/flow';
import { correctName } from '@/utils';
import { encode, decode } from 'js-base64';
import { omit } from 'lodash';
import {
  queryContent,
  listDir,
  addScript,
  saveScript,
  queryTags,
  // detailFetch,
  runScript,
  getRunscript,
  stopScript,
  databaseList,
  goOperation,
  getVersion,
} from '../../services/develop';
import { modAccessGetScriptContent } from '../../services/workflow';
import { getWorkflowEngine } from '../../services/Platform/pfEngines';
import { listDatabase, listEngines } from '../../services/datacenter';

// 根据engineData提供的类型遍历请求engine
const engineData = [
  'stork',
  'teryx',
  'hive',
  'gaussdb',
  'postgres',
  'greenplum',
  'tdh_inceptor',
  'gaussdba',
  'tdsql',
];

export default {
  namespace: 'editbody',

  state: {
    content: '',
    // 是否可编辑，新建的脚本默认可编辑，？？？即 ban = true？？？
    ban: null,
    star: null,
    identify: true,
    workflows: [],
    initdb: '',
    sqlid: '',
    sqlname: '',
    enginename: '',
    variables: [],
    dirlist: [],
    result: {},
    pid: '',
    runYarnlog: '',
    runResponse: null,
    runResult: '',
    runFinish: false,
    runOutArray: [], // v5.6.1 标示返回结果是否超出1M
    showDataList: [],
    goOperationList: [],
    tags: [],
    curEngine: '',
    dataBaseOption: engineData.map((item) => {
      return {
        value: item.value,
        label: item.label,
      };
    }),
    newDataBaseOption: [],
    // modelRecord: {}
    finish: true, // #21939，判段当前脚本（刷新重进入后）上一次执行状态
  },

  effects: {
    *fetchDataxContent({ payload }, { call, put }) {
      const response = yield call(modAccessGetScriptContent, payload);
      // console.log(response, 'response');
      if (response.result) {
        const {
          result: { content, identify },
        } = response;
        yield put({
          type: 'save',
          payload: {
            content,
            identify,
          },
        });
        // 返回请求数据
        return response.result;
      } else return response;
    },
    *fetchContent({ payload }, { call, put }) {
      const { names, ...others } = payload;
      // delete payload.names;
      const response = yield call(queryContent, { ...others });
      if (response.result) {
        const {
          result: {
            content,
            ban,
            star,
            identify,
            initdb,
            enginename,
            name,
            engineid,
            variables,
            dags,
            finish,
            pid,
          },
        } = response;
        let newContent = content;

        names &&
          names.forEach((item) => {
            const { scriptName, noteName } = item;
            const realName = getRealName(scriptName);
            if (
              realName &&
              noteName &&
              !newContent.includes(`${realName}(${noteName})`)
            ) {
              // 不带括号就换成带括号
              const reg = new RegExp(realName, 'g');
              newContent = newContent.replace(reg, `${realName}(${noteName})`);
            }
          });
        yield put({
          type: 'save',
          payload: {
            content: newContent,
            // content,
            ban,
            star,
            identify,
            workflows: dags || [],
            initdb,
            enginename,
            sqlname: name,
            sqlid: engineid,
            variables,
            finish,
            pid,
          },
        });
        // 返回请求数据
        response.result.content = newContent;
        return response.result;
      } else return response;
    },
    *listDir({ payload }, { call, put }) {
      const { result } = yield call(listDir, payload);
      yield put({
        type: 'save',
        payload: {
          dirlist: result,
        },
      });
    },
    *addWorkflowScript({ payload }, { call }) {
      const { code, result } = yield call(addScript, payload);
      if (code === 200) {
        return result;
      }
    },
    *addScript({ payload }, { call, put }) {
      const {
        numberOfRetries,
        independentExecute,
        notetype: type,
        ...others
      } = payload;
      const body = {
        ...omit(others, ['retry', 'retryInterval']),
        notetype: type === 'sh' ? 'shell' : type,
        isown: independentExecute ?? others?.isown,
        retrynum: numberOfRetries ?? others?.retry,
        retryinterval: others?.retryInterval,
      };
      const response = yield call(addScript, body);
      if (response) {
        const { result } = response;
        yield put({
          type: 'save',
          payload: {
            result,
          },
        });
        return 'success';
      }
      return 'error';
    },
    *cleanLog({ payload }, { put, select }) {
      const runResponse = yield select((state) => state.editbody.runResponse);
      // const runYarnlog = yield select((state) => state.editbody.runYarnlog);
      yield put({
        type: 'save',
        payload: {
          runResponse: {
            ...runResponse,
            [payload.id]: undefined,
          },
          runYarnlog: '',
        },
      });
    },
    *saveScript({ payload }, { call, put }) {
      const { content, names, enginename, ...others } = payload;
      const newContent = cleanContent(
        names,
        others?.encode ? decode(content) : content,
      );
      // console.log(newContent, 'newContent');
      const { code } = yield call(saveScript, {
        ...others,
        content: others?.encode ? encode(newContent) : newContent,
      });
      if (code === 200) {
        if (enginename) {
          yield put({
            type: 'save',
            payload: {
              enginename,
            },
          });
        }
        return 'success';
      }
      return 'error';
    },
    *listDataBase({ payload }, { call, put }) {
      const { types = engineData } = payload;
      const dataBaseOption = [];
      // 请求当前引擎
      const { code, result } = yield call(getWorkflowEngine);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            curEngine: result,
          },
        });
      }
      for (let i = 0; i < types.length; i++) {
        const type = types[i];
        const level1 = {
          value: type,
          label: correctName(type),
        };
        const {
          code: code1,
          result: engines,
          msg,
        } = yield call(listEngines, {
          enginename: type,
          page: 1,
          perpage: 10000,
          type,
        });
        if (code1 === 200) {
          const engineList = engines && engines !== null ? engines : [];
          // console.log(engineList, 'engineList');
          const level2 = engineList.map((engine) => {
            return {
              disabled: engine.enginestatus === 'RED',
              value: engine.engineid,
              label:
                engine.enginestatus !== 'RED'
                  ? engine.name
                  : `${engine.name}(故障)`,
            };
          });
          for (let j = 0; j < level2.length; j++) {
            const engine = level2[j];
            if (!engine.disabled) {
              const {
                code: code2,
                result: database,
                msg: msg2,
              } = yield call(listDatabase, {
                engineid: engine.value,
                instance: engine.value,
                parentvalue: type,
              });
              if (code2 === 200) {
                engine.children = database.map((item) => {
                  return {
                    value: item.datname,
                    label: item.datname,
                    isLeaf: true,
                  };
                });
              } else {
                notification.error({
                  message: `[网络请求错误]-${code2}`,
                  description: msg2,
                });
              }
            }
            level1.children = level2;
          }
          dataBaseOption.push(level1);
        } else {
          notification.error({
            message: `[网络请求错误]-${code1}`,
            description: msg,
          });
        }
      }
      yield put({
        type: 'save',
        payload: {
          dataBaseOption,
        },
      });
    },
    *listDataBase2(_, { call, put }) {
      // const dataBaseOption = [];
      // 请求当前引擎
      const { code, result } = yield call(getWorkflowEngine);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            curEngine: result,
          },
        });
      }
      const { code: code2, result: dataBaseData } = yield call(getAllMetaData, {
        querytype: 1,
      });

      if (code2 === 200) {
        const dataBaseOption = dataBaseData.map((item) => {
          const engineid = item.id; // todo item.engineid
          return {
            ...item,
            value: engineid,
            title: item.name,
            icon: <DatabaseIcon type="teryx" />,
            children:
              item.children?.map((child) => {
                const datname = child.sourcename; // todo child.datname
                return {
                  ...child,
                  icon: <IconFont type="icon-database" />,
                  value: `${engineid},${datname},${
                    item.enginetype || item.enginename
                  }`,
                  title: datname,
                  engineid,
                  sqlid: engineid,
                  enginename: item.enginename,
                  enginetype: item.enginetype,
                  initdb: datname,
                  children: undefined,
                };
              }) || [],
          };
        });
        yield put({
          type: 'save',
          payload: {
            dataBaseOption,
          },
        });
      }
    },
    *listNewDataBase(_, { call, put }) {
      // const dataBaseOption = [];
      // 请求当前引擎
      const { code, result } = yield call(getWorkflowEngine);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            curEngine: result,
          },
        });
      }
      const { code: code2, result: dataBaseData } = yield call(getAllMetaData, {
        querytype: 1,
      });

      if (code2 === 200) {
        const dataBaseOption = dataBaseData.map((item) => {
          return {
            ...item,
            value: item.id,
            title: item.name,
            children:
              item.children?.map((child) => {
                return {
                  ...child,
                  value: child.id,
                  title: child.sourcename,
                  engineid: item.engineid,
                  enginename: item.enginename,
                  initdb: child.dbname,
                  children: undefined,
                };
              }) || [],
          };
        });
        yield put({
          type: 'save',
          payload: {
            newDataBaseOption: dataBaseOption,
          },
        });
      }
    },
    *databaseList(_, { call, put }) {
      const response = yield call(databaseList);
      if (response) {
        const { result } = response;
        yield put({
          type: 'save',
          payload: {
            showDataList: result,
          },
        });
        return 'success';
      }
      return 'error';
    },
    // * detailFetch({ payload }, { call, put }) {
    //     const response = yield call(detailFetch, payload);
    //     if (response) {
    //         const { result } = response;
    //         yield put({
    //             type: 'save',
    //             payload: {
    //                 modelRecord: result,
    //             },
    //         });
    //     }
    // },
    *getTagslist({ payload }, { call, put }) {
      const { code, result } = yield call(queryTags, payload);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            tags: result || [],
          },
        });
      }
    },
    *runScript({ payload }, { call, put }) {
      const { content, names, ...others } = payload;
      const newContent = cleanContent(
        names,
        others?.encode ? decode(content) : content,
      );
      // console.log(newContent, 'newContent');
      if (payload.names) {
        delete payload.names;
      }
      const { code, result } = yield call(
        runScript,
        {
          ...payload,
          content: others?.encode ? encode(newContent) : newContent,
        },
        { skip: 'all' },
      );

      if (code && code === 200) {
        yield put({
          type: 'save',
          payload: {
            pid: result,
          },
        });
        return 'success';
      }
      if (code && code === 10007) {
        return code;
      }
      return result;
      // if (code && code === 10004) {
      //   return result;
      // }
      // return 'error';
    },
    *getRunscript({ payload }, { call, put, select }) {
      const response = yield call(getRunscript, payload);
      const runResponse = yield select((state) => state.editbody.runResponse);
      if (response) {
        const {
          result,
          yarnlog = '',
          finish,
          fieldtype = [],
          outarray = [],
        } = response;
        yield put({
          type: 'save',
          payload: {
            runResult: result,
            runFinish: finish,
            runOutArray: outarray,
            runYarnlog: yarnlog,
            runResponse: {
              ...runResponse,
              [payload.id]: response,
            },
          },
        });
        return {
          ...response,
          runResult: result,
          runFinish: finish,
          runFieldType: fieldtype,
          runOutArray: outarray,
          runYarnlog: yarnlog,
        };
      }
    },
    *stopScript({ payload }, { call, put, select }) {
      const response = yield call(stopScript, payload);
      const { runResult } = yield select((state) => state.wEditbody);
      const runResponse = yield select((state) => state.editbody.runResponse);
      if (response) {
        yield put({
          type: 'save',
          payload: {
            runResponse: {
              ...runResponse,
              [payload.id]: {
                ...runResponse[payload.id],
                finish: true,
              },
            },
            runResult: {
              ...runResult,
              finish: true,
            },
            runFinish: true,
          },
        });
        return 'success';
      }
      return 'error';
    },
    *goOperation({ payload }, { call, put }) {
      const { code, result } = yield call(goOperation, payload);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            goOperationList: result,
          },
        });
        return 'success';
      }
      return 'error';
    },
    *getVersion({ payload }, { call }) {
      const res = yield call(getVersion, payload);
      return res;
    },
    // * add({ payload, callback }, { call, put }) {
    //   const response = yield call(addDevelop, payload);
    //   yield put({
    //     type: 'save',
    //     payload: response,
    //   });
    //   if (callback) callback();
    // },
    // * remove({ payload, callback }, { call, put }) {
    //   const response = yield call(removeDevelop, payload);
    //   yield put({
    //     type: 'save',
    //     payload: response,
    //   });
    //   if (callback) callback();
    // },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};

function cleanContent(names, content) {
  let newContent = content;
  names &&
    names.forEach((name) => {
      const { scriptName, noteName } = name;
      const realName = getRealName(scriptName);
      if (newContent.includes(`${realName}(${noteName})`)) {
        // 带括号就换成不带括号
        const reg = new RegExp(`${realName}[(]${noteName}[)]`, 'g');
        newContent = newContent.replace(reg, realName);
      }
    });
  return newContent;
}

function getRealName(scriptName) {
  let realName = scriptName;
  if (scriptName.includes('\n')) {
    const arr = scriptName.split(' ');
    realName = arr[arr.length - 1];
  } else if (scriptName.includes('/')) {
    const arr = scriptName.split('/');
    realName = arr[arr.length - 1];
  }
  return realName;
}
