import { transform } from '@dana/ui';
import { notification } from 'antd';
import { cloneDeep, omit } from 'lodash';
import { sortTreeData } from '@/utils';
import {
  developList,
  searchList,
  moveToNewFolder,
  queryRecent,
  queryTags,
  listDir,
  detailFetch,
  editDetail,
  copyDetail,
  saveScript,
  deleteScript,
  clickStar,
  queryContent,
  onlineScript,
  goOperation,
  createFolder,
  editFolder,
  checkFolder,
  deleteFolder,
  exportFile,
  paramsFetch,
  getHistoryVersion,
} from '../../services/develop';

export default {
  namespace: 'develop',
  state: {
    tags: [],
    list: [],
    globalVars: {
      constVars: [],
      dynamicVars: [],
      timeVars: [],
      workflows: [],
    },
    workflowList: [], // 所涉及工作流名称数组
    dirChildren: [],
    recentopenList: [],
    dirlist: [],
    treeList: [],
    dirScriptList: [],
    myDirs: [],
    //! dirlist一般文件夹列表，也是全部树数据
    //! treelist树列表（含搜索，可能为部分数据）
    //! myDirs我自己的文件夹列表
    names: [],
    total: 0,
    cmd: '',
    star: false,
    pagination: {},
    modelRecord: {},
    ipList: [],
    modelDelete: [],
    identify: true,
    goOperationList: [],
    curHistoryList: [],
    curVersion: {},
  },

  effects: {
    *developList({ payload }, { call, put }) {
      const { result } = yield call(developList, payload);
      const { total, list } = result;
      yield put({
        type: 'save',
        payload: {
          list: [...list],
          total,
        },
      });
    },
    *getWorkflows({ payload }, { call, put }) {
      // 获取当前脚本所涉及的工作流列表
      const param = {
        sorttype: 0,
        listall: payload.listall,
        dirid: '',
        page: 1,
        perpage: 1000,
      };
      const { code, result } = yield call(searchList, param);
      if (code === 200) {
        const { list } = result;
        const workflows = [];
        list.forEach((item) => {
          item.dags?.forEach((dag) => {
            if (!workflows.includes(dag.dagname)) {
              workflows.push(dag.dagname);
            }
          });
        });
        yield put({
          type: 'save',
          payload: {
            workflowList: workflows,
          },
        });
      }
    },
    // TODO 该方法在统一检索也有使用，修改次方法请事先通知
    *dirSearch({ payload }, { call, put }) {
      // const { match } = payload;
      payload.module = 'develop';
      payload.page = 1;
      payload.perpage = 10000;
      payload.sorttype = 2;
      payload.sort = 0;
      const { code: code1, result: dirList } = yield call(listDir, payload);

      let dirScriptList = [];

      if (payload.listScript) {
        const {
          code: code2,
          result: { list },
        } = yield call(searchList, payload);
        if (code1 !== 200 || code2 !== 200) {
          console.error('接口出错，错误码', code1, code2);
        }
        dirScriptList = getTreeData(list, cloneDeep(dirList));
        dirScriptList.unshift({ id: 'listAll', name: '全部' });
      } else if (code1 !== 200) {
        console.error('接口出错，错误码', code1);
      }

      const newList = sortTreeData(getTreeData([], cloneDeep(dirList)), 'name');
      newList.unshift({ id: 'listAll', name: '全部' });

      yield put({
        type: 'save',
        payload: {
          dirlist: newList,
          treeList: newList,
          dirScriptList,
        },
      });
      return newList;
    },
    *searchList({ payload }, { call, put }) {
      const {
        search,
        selectDir,
        // page = 1,
        // perpage = 15,
        // sorttype: { sorttype, sort, notetype },
        ...others
      } = payload;
      const body = {
        ...others,
        dirid: selectDir,
        page: 1, // 全量请求1，屏蔽payload.page
        perpage: 10000, // 直接写成10000，屏蔽payload.perpage
        sorttype: 0, // 0默认排序
        sort: 1, // 1默认降序
      };
      if (search) {
        body.match = search;
      }
      // if (notetype) {
      //   body.notetype = notetype;
      // }
      const { code, result } = yield call(searchList, body);
      if (code === 200) {
        const { total, list } = result;
        yield put({
          type: 'save',
          payload: {
            list,
            total,
            names: list.map((item) => {
              return {
                scriptName: item.scriptname,
                noteName: item.notename,
              };
            }),
          },
        });
      } else {
        notification.error({
          message: '脚本数据请求失败，请检查服务',
          description: result,
        });
      }
      return { code, result };
    },
    *searchScript({ payload }, { call }) {
      const {
        search,
        selectDir,
        // page = 1,
        // perpage = 15,
        // sorttype: { sorttype, sort, notetype },
        ...others
      } = payload;
      const body = {
        ...others,
        dirid: selectDir,
        page: 1, // 全量请求1，屏蔽payload.page
        perpage: 10000, // 直接写成10000，屏蔽payload.perpage
        sorttype: 0, // 0默认排序
        sort: 1, // 1默认降序
      };
      if (search) {
        body.match = search;
      }
      const { code, result } = yield call(searchList, body);
      if (code !== 200) {
        notification.error({
          message: '脚本数据请求失败，请检查服务',
          description: result,
        });
      }
      return { code, result };
    },
    *getNames({ payload }, { call, put }) {
      const body = {};
      const { listall } = payload;
      body.sorttype = 0;
      body.listall = listall;
      body.dirid = '';
      body.page = 1;
      body.perpage = 10000;
      const { code, result } = yield call(searchList, body);
      const { list } = result;
      const names = list.map((item) => {
        return {
          // scriptName: item.scriptname,
          scriptName: item.scriptname || item.id,
          noteName: item.notename,
        };
      });
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            names,
          },
        });
        return names;
      }
    },

    *moveToNewFolder({ payload }, { call }) {
      const { code } = yield call(moveToNewFolder, payload, { skip: 'all' });
      return code;
    },

    *recentopenList({ payload }, { call, put }) {
      const { result } = yield call(queryRecent, payload);
      const { list } = result;
      yield put({
        type: 'save',
        payload: {
          recentopenList: list,
        },
      });
    },
    *getTagslist({ payload }, { call, put }) {
      const { code, result } = yield call(queryTags, payload);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            tags: result || [],
          },
        });
      }
    },
    *createFolder({ payload }, { call }) {
      const res = yield call(createFolder, payload);
      if (res && res.code === 200) {
        return 'success';
      }
      return 'error';
    },
    *editFolder({ payload }, { call }) {
      const { code } = yield call(editFolder, payload);
      if (code === 200) {
        return 'success';
      }
      return 'error';
    },
    *checkFolder({ payload }, { call }) {
      const { result } = yield call(checkFolder, payload);
      const { status } = result;
      return status;
    },
    *deleteFolder({ payload }, { call }) {
      const { code } = yield call(deleteFolder, payload);
      if (code === 200) {
        return 'success';
      }
      return 'error';
    },

    *clickStar({ payload }, { call }) {
      const { code } = yield call(clickStar, payload);
      if (code === 200) {
        return 'success';
      }
      return 'error';
    },

    // 星标接口供 DS v4.2 自定义脚本锁定逻辑使用
    // 后台不愿意新写接口，只能沿用v4.1接口
    *scriptToggleLock({ payload }, { call }) {
      const { code, result } = yield call(clickStar, payload);
      if (code === 200) {
        return result;
      }
      return 'error';
    },

    *paramsFetch({ payload }, { call, put }) {
      // @ts-ignore
      const transformated = transform(
        paramsFetch,
        (value) => {
          const { dags: workflows } = value;
          // mgmStates['globalVars']
          const globalVars = {
            constVars: [],
            dynamicVars: [],
            timeVars: [],
            workflows: [],
          };
          // workflowParamType
          workflows.forEach((workflow) => {
            const { globalvars, dagid, dagname } = workflow;
            globalVars.workflows?.push(dagid);
            if (globalvars?.constantvars) {
              const { constantvars } = globalvars;
              const newConstVars = constantvars.map((item) => {
                return {
                  id: item.name + dagid,
                  name: item.name,
                  value: item.value,
                  belong: [dagname],
                };
              });
              globalVars.constVars?.push(...newConstVars);
            }
            if (globalvars?.dynamicvars) {
              const { dynamicvars } = globalvars;
              const newDynamicVars = dynamicvars.map((item) => {
                return {
                  id: item.name + dagid,
                  name: item.name,
                  belong: [dagname],
                };
              });
              globalVars.dynamicVars?.push(...newDynamicVars);
            }
            if (globalvars?.timevars) {
              const { timevars } = globalvars;
              const newTimeVars = timevars.map((item) => {
                const isAdd = item.datecalc >= 0;
                return {
                  id: item.name + dagid,
                  name: item.name,
                  format: item.type,
                  detail: {
                    isAdd,
                    value: Math.abs(item.datecalc),
                    unit: 'day', // 一级需求写死，为day
                  },
                  belong: [dagname],
                };
              });
              globalVars.timeVars?.push(...newTimeVars);
            }
          });
          return globalVars;
        },
        {
          constVars: [],
          dynamicVars: [],
          timeVars: [],
          workflows: [],
        },
      );
      const res = yield call(transformated, payload);
      // console.log(result, 'result');
      if (res.code === 200) {
        yield put({
          type: 'save',
          payload: {
            globalVars: res.result,
          },
        });
      }
    },

    *detailFetch({ payload }, { call, put }) {
      const { code, result } = yield call(detailFetch, payload);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            modelRecord: { ...result },
          },
        });
      }
    },
    // eslint-disable-next-line
    *listDir({ payload }, { call, put }) {
      const { result } = yield call(listDir, payload);
      yield put({
        type: 'save',
        payload: {
          dirlist: sortTreeData(result, 'name'),
        },
      });
    },
    *copyDetail({ payload }, { call }) {
      const { code } = yield call(copyDetail, payload);
      if (code && code === 200) {
        return 'success';
      }
      return 'error';
    },
    *editWorkflowScriptDetail({ payload }, { call, put }) {
      const { top, star, notetype: type, ...others } = payload;
      const body = {
        ...others,
        top: top ? 'true' : 'false',
        star: star ? 'true' : 'false',
        notetype: type === 'sh' ? 'shell' : type,
      };
      const { result, code } = yield call(editDetail, body);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            result,
          },
        });
        return 'success';
      }
      return 'error';
    },
    *editDetail({ payload }, { call, put }) {
      const {
        numberOfRetries,
        independentExecute,
        top,
        star,
        notetype: type,
        ...others
      } = payload;
      const body = {
        ...omit(others, ['retry', 'retryInterval']),
        top: top ? 'true' : 'false',
        star: star ? 'true' : 'false',
        notetype: type === 'sh' ? 'shell' : type,
        isown: independentExecute ?? others?.isown,
        retrynum: numberOfRetries ?? others?.retry,
        retryinterval: others?.retryInterval,
      };
      const response = yield call(editDetail, body);
      if (response) {
        const { result } = response;
        yield put({
          type: 'save',
          payload: {
            result,
          },
        });
        return 'success';
      }
      return 'error';
    },
    *deleteScript({ payload }, { call, put }) {
      const response = yield call(deleteScript, payload);
      if (response) {
        const { result } = response;
        if (typeof result === 'string') {
          return 'successN';
        }
        yield put({
          type: 'save',
          payload: {
            modelDelete: result,
          },
        });
        return 'successY';
      }
      return 'error';
    },
    *udfCmd({ payload, callback }, { call, put }) {
      const { result } = yield call(queryContent, payload);
      const { udfcmd, identify, star } = result;
      // console.log(result, 'result');
      yield put({
        type: 'save',
        payload: {
          cmd: udfcmd,
          star,
          identify,
        },
      });
      if (callback) callback();
    },
    *saveUdf({ payload }, { call }) {
      const body = {
        id: payload.id,
        udfcmd: payload.cmd,
      };
      const response = yield call(saveScript, body);
      if (response) {
        return 'success';
      }
      return 'error';
    },
    *saveOnline({ payload }, { call }) {
      const response = yield call(onlineScript, payload);
      if (response) {
        return 'success';
      }
      return 'error';
    },
    *goOperation({ payload }, { call, put }) {
      const { code, result } = yield call(goOperation, payload);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            goOperationList: result,
          },
        });
        return 'success';
      }
      return 'error';
    },
    *exportFile({ payload }, { call }) {
      const result = yield call(exportFile, payload);

      return result;
    },
    // * remove({ payload, callback }, { call, put }) {
    //     const response = yield call(removeDevelop, payload);
    //     yield put({
    //         type: 'save',
    //         payload: response,
    //     });
    //     if (callback) callback();
    // },
    *getHistoryVersion({ payload }, { call, put }) {
      const { code, result } = yield call(getHistoryVersion, payload);
      if (code === 200) {
        yield put({
          type: 'save',
          payload: {
            curHistoryList: result || [],
          },
        });
      }
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};

function getTreeData(list, dirList) {
  dirList.forEach((item) => {
    item.children = item.children || [];
    item.isDir = true;
  });
  const innerDir = dirList.filter((it) => it.fatherid);
  const outDir = dirList.filter((it) => !it.fatherid);
  innerDir.forEach((it) => {
    innerDir.forEach((item) => {
      if (item.fatherid === it.id) {
        it.children = [...it.children, item];
        item.isDir = true;
      }
    });
  });

  if (list.length > 0) {
    const scriptList = list.map((table) => {
      return {
        ...table,
        name: table.notename,
      };
    });

    dirList.forEach((item) => {
      scriptList.forEach((table) => {
        if (table.dirid === item.id) {
          item.children = [...item.children, table];
        }
      });
    });
  }

  const newList = outDir.map((item) => {
    const children = innerDir.filter((it) => it.fatherid === item.id);

    return {
      ...item,
      children: [...children, ...item.children],
      isDir: true,
    };
  });
  return newList;
}
