// import { stringify } from 'qs';
import request from '../utils/request';
import { SERVER } from '../config/server';

const DATA_DEVELOP = `${SERVER}/danastudio/ants/develop/`;

// 脚本开发
// Develop

export async function searchList(params, options) {
  return request(`${DATA_DEVELOP}search`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function moveToNewFolder(params, options) {
  return request(`${DATA_DEVELOP}move`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function developList(params, options) {
  return request(`${DATA_DEVELOP}list/dev`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function queryTags(params, options) {
  return request(`${DATA_DEVELOP}recommend/tags`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

/*
 * TODO 星标/置顶 功能取消
 * */
export async function clickTop(params, options) {
  return request(`${DATA_DEVELOP}top`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function clickStar(params, options) {
  return request(`${DATA_DEVELOP}star`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function editDetail(params, options) {
  return request(`${DATA_DEVELOP}detail`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function copyDetail(params, options) {
  return request(`${DATA_DEVELOP}copy`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

// develop&&editbody
export async function goOperation(params, options) {
  return request(`${DATA_DEVELOP}goto`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function queryContent(params, options) {
  return request(`${DATA_DEVELOP}content`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function detailFetch(params, options) {
  return request(`${DATA_DEVELOP}fetch`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function paramsFetch(params, options) {
  return request(`${DATA_DEVELOP}globalget`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

// TODO 上传UDF脚本？？？
export async function addScript(params, options) {
  return request(`${DATA_DEVELOP}new/dev`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function saveScript(params, options) {
  return request(`${DATA_DEVELOP}save`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function runScript(params, options) {
  return request(`${DATA_DEVELOP}run`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function getRunscript(params, options) {
  return request(`${DATA_DEVELOP}result`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function stopScript(params, options) {
  return request(`${DATA_DEVELOP}stop`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function deleteScript(params, options) {
  return request(`${DATA_DEVELOP}delete`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function createFolder(params, options) {
  return request(`${SERVER}/danastudio/ants/folder/create`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function editFolder(params, options) {
  return request(`${SERVER}/danastudio/ants/folder/update`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function checkFolder(params, options) {
  return request(`${SERVER}/danastudio/ants/folder/check`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function deleteFolder(params, options) {
  return request(`${SERVER}/danastudio/ants/folder/delete`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
// develop&&editbody
export async function listDir(params, options) {
  return request(`${SERVER}/danastudio/ants/folder/list`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function getOnlineIp(body, options) {
  return request(`${SERVER}/danastudio/ants/inside/dodox/getnode`, {
    method: 'POST',
    body,
    ...options,
  });
}
export async function onlineScript(params, options) {
  return request(`${DATA_DEVELOP}submit`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function offlineScript(params, options) {
  return request(`${DATA_DEVELOP}down`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function databaseList(body, options) {
  return request(`${SERVER}/danastudio/asset/search/database`, {
    method: 'POST',
    body: {
      databasetype: 'stork',
    },
    ...options,
  });
}

// 最近打开

export async function queryRecent(params, options) {
  return request(`${DATA_DEVELOP}list/recent`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
// 文件库

export async function queryFilelist(params, options) {
  return request(`${SERVER}/danastudio/dodox/filemanager/file/list`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
export async function getMem(params, options) {
  return request(`${SERVER}/danastudio/dodox/filemanager/memory/left`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

// TODO 上传文件
export async function addFiles({ file, filepath, force, donotsave }, options) {
  const formData = new FormData();
  formData.append('force', force);
  // formData.append('absdir', '/' + absdir);
  formData.append('filepath', filepath);
  formData.append('file', file);
  if (donotsave) {
    formData.append('donotsave', donotsave);
  }

  return request(`${SERVER}/danastudio/dodox/filemanager/file/upload`, {
    method: 'POST',
    body: formData,
    ...options,
  });
}

export async function addFolder(params, options) {
  return request(`${SERVER}/danastudio/dodox/filemanager/dir/make`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function deletePath(params, options) {
  return request(`${SERVER}/danastudio/dodox/filemanager/file/delete`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

// export async function downloadFile({ path }) {
//     return request(`${SERVER}/danastudio/dodox/filemanager/file/download?path=${path}`);
// }

// 废纸篓
export async function trashList(params, options) {
  return request(`${DATA_DEVELOP}list/trash`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function trashRecover(params, options) {
  return request(`${DATA_DEVELOP}trash/recover`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

export async function trashDelete(params, options) {
  return request(`${DATA_DEVELOP}trash/delete`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

// 导出文件
export async function exportFile(params, options) {
  return request(`${DATA_DEVELOP}scriptsexport`, {
    method: 'POST',
    body: params,
    ...options,
    // responseType: 'blob',
  });
}

/// danastudio/ants/develop/generate/version
export async function getVersion(params, options) {
  return request(`${DATA_DEVELOP}generate/version`, {
    method: 'POST',
    body: params,
    ...options,
  });
}

// getHistoryVersion-/danastudio/ants/develop/list/history
export async function getHistoryVersion(params, options) {
  return request(`${DATA_DEVELOP}list/history`, {
    method: 'POST',
    body: params,
    ...options,
  });
}
