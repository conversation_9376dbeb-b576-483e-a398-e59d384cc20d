@font-face {
  font-family: "iconfont"; /* Project id 862751 */
  src: url('iconfont.woff2?t=1755135104292') format('woff2'),
       url('iconfont.woff?t=1755135104292') format('woff'),
       url('iconfont.ttf?t=1755135104292') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-versionmanage:before {
  content: "\e7ff";
}

.icon-shenTong:before {
  content: "\e7fd";
}

.icon-jinCang:before {
  content: "\e7fe";
}

.icon-Greenplum1:before {
  content: "\e60c";
}

.icon-generateVersion:before {
  content: "\e7fb";
}

.icon-DanaStudio:before {
  content: "\e7fa";
}

.icon-Sybase:before {
  content: "\e7f9";
}

.icon-Druid:before {
  content: "\e6be";
}

.icon-Redis:before {
  content: "\e7f8";
}

.icon-lightning:before {
  content: "\e600";
}

.icon-FTPS:before {
  content: "\e7f5";
}

.icon-kafka:before {
  content: "\e7f6";
}

.icon-flume1:before {
  content: "\e7f7";
}

.icon-xieyuanbiao2:before {
  content: "\e7f4";
}

.icon-shujuzichan:before {
  content: "\e7ef";
}

.icon-kubiaotuisongfuwu:before {
  content: "\e7f0";
}

.icon-wenjianxiazaifuwu:before {
  content: "\e7f1";
}

.icon-a-KafkaTopic1:before {
  content: "\e7f2";
}

.icon-a-APIfuwu1:before {
  content: "\e7f3";
}

.icon-tablePush:before {
  content: "\e6b4";
}

.icon-fileDownload:before {
  content: "\e6b2";
}

.icon-dataAssets:before {
  content: "\e6b1";
}

.icon-bloodTable:before {
  content: "\e6b0";
}

.icon-a-KafkaTopic:before {
  content: "\e7ed";
}

.icon-a-APIfuwu:before {
  content: "\e7ee";
}

.icon-dataStandards:before {
  content: "\e6af";
}

.icon-expandFields:before {
  content: "\e6a1";
}

.icon-collapseFields:before {
  content: "\e6a7";
}

.icon-cancelPublishing:before {
  content: "\e690";
}

.icon-increaseID:before {
  content: "\e6a0";
}

.icon-visualComponent:before {
  content: "\e69f";
}

.icon-fileSource:before {
  content: "\e697";
}

.icon-libraryTable:before {
  content: "\e69e";
}

.icon-stop:before {
  content: "\e696";
}

.icon-application:before {
  content: "\e695";
}

.icon-analyzeExcel:before {
  content: "\e691";
}

.icon-fileLabel:before {
  content: "\e692";
}

.icon-parsingFiles:before {
  content: "\e693";
}

.icon-praseXML:before {
  content: "\e694";
}

.icon-resolveFilename:before {
  content: "\e68c";
}

.icon-fileFiltering:before {
  content: "\e68d";
}

.icon-analyzeCSV:before {
  content: "\e68e";
}

.icon-setDefault:before {
  content: "\e68f";
}

.icon-addChat:before {
  content: "\e7ec";
}

.icon-sending:before {
  content: "\e61c";
}

.icon-aiAssistant:before {
  content: "\e623";
}

.icon-placeholder1:before {
  content: "\e87d";
}

.icon-placeholder2:before {
  content: "\ec6b";
}

.icon-copyInfo:before {
  content: "\e8b0";
}

.icon-placeholder3:before {
  content: "\e6bd";
}

.icon-placeholder4:before {
  content: "\e61b";
}

.icon-reset:before {
  content: "\e648";
}

.icon-zidong1:before {
  content: "\e68a";
}

.icon-shoudong:before {
  content: "\e683";
}

.icon-RealTimeS:before {
  content: "\e7eb";
}

.icon-DRealDB:before {
  content: "\e7e9";
}

.icon-APIJ:before {
  content: "\e7ea";
}

.icon-APIS:before {
  content: "\e7e8";
}

.icon-lowCode:before {
  content: "\e7e7";
}

.icon-drag:before {
  content: "\e7e5";
}

.icon-drive:before {
  content: "\e7e6";
}

.icon-qualityControl:before {
  content: "\e7e4";
}

.icon-bracketRight:before {
  content: "\e7e3";
}

.icon-bracketLeft:before {
  content: "\e7e2";
}

.icon-FTPC:before {
  content: "\e7de";
}

.icon-qualityIndicators:before {
  content: "\e7df";
}

.icon-qualityRules:before {
  content: "\e7e0";
}

.icon-qualityReports:before {
  content: "\e7e1";
}

.icon-derivedMetrics:before {
  content: "\e7db";
}

.icon-atomicIndicators:before {
  content: "\e7dc";
}

.icon-compositeMetrics:before {
  content: "\e7dd";
}

.icon-localFiles:before {
  content: "\e7da";
}

.icon-openData:before {
  content: "\e7d9";
}

.icon-openCatalog:before {
  content: "\e7d8";
}

.icon-resourceRegistration:before {
  content: "\e7d7";
}

.icon-TDSQL:before {
  content: "\e7d6";
}

.icon-new:before {
  content: "\e7d4";
}

.icon-noPermissions:before {
  content: "\e7d5";
}

.icon-java:before {
  content: "\e7d3";
}

.icon-excel:before {
  content: "\e7d2";
}

.icon-helpCenter:before {
  content: "\e7d1";
}

.icon-dataFilling:before {
  content: "\e7d0";
}

.icon-word:before {
  content: "\e7cd";
}

.icon-PDF:before {
  content: "\e7ce";
}

.icon-Image:before {
  content: "\e7cf";
}

.icon-Vastbase:before {
  content: "\e7ca";
}

.icon-Tbase:before {
  content: "\e7cb";
}

.icon-KingbaseES:before {
  content: "\e7cc";
}

.icon-shenqing:before {
  content: "\e7c6";
}

.icon-kaifang:before {
  content: "\e7c7";
}

.icon-shenpi:before {
  content: "\e7c8";
}

.icon-jiekou:before {
  content: "\e7c9";
}

.icon-a-right:before {
  content: "\e7c4";
}

.icon-a-left:before {
  content: "\e7c5";
}

.icon-proprietor:before {
  content: "\e7c3";
}

.icon-retrieval:before {
  content: "\e7bd";
}

.icon-file:before {
  content: "\e7be";
}

.icon-underReview:before {
  content: "\e7bf";
}

.icon-reviewRejection:before {
  content: "\e7c0";
}

.icon-approved:before {
  content: "\e7c1";
}

.icon-API:before {
  content: "\e7c2";
}

.icon-processed:before {
  content: "\e7bb";
}

.icon-pending:before {
  content: "\e7bc";
}

.icon-toDo:before {
  content: "\e7b9";
}

.icon-pentagram:before {
  content: "\e7ba";
}

.icon-a-35:before {
  content: "\e7b8";
}

.icon-a-34:before {
  content: "\e7b7";
}

.icon-a-32:before {
  content: "\e7b5";
}

.icon-a-33:before {
  content: "\e7b6";
}

.icon-call:before {
  content: "\e7b4";
}

.icon-visits:before {
  content: "\e7b2";
}

.icon-downloads:before {
  content: "\e7b3";
}

.icon-essentialInformation:before {
  content: "\e7a4";
}

.icon-examine:before {
  content: "\e7a5";
}

.icon-dataRetrieval:before {
  content: "\e7a6";
}

.icon-evaluate:before {
  content: "\e7a7";
}

.icon-serviceMonitoring:before {
  content: "\e7a8";
}

.icon-calculation:before {
  content: "\e7a9";
}

.icon-serviceManagement:before {
  content: "\e7aa";
}

.icon-errorCorrection:before {
  content: "\e7ab";
}

.icon-release:before {
  content: "\e7ac";
}

.icon-collection:before {
  content: "\e7ad";
}

.icon-demandApplication:before {
  content: "\e7ae";
}

.icon-apply:before {
  content: "\e7af";
}

.icon-assetOverview:before {
  content: "\e7b0";
}

.icon-mount:before {
  content: "\e7b1";
}

.icon-a-31:before {
  content: "\e7a3";
}

.icon-GUID:before {
  content: "\e7a2";
}

.icon-TDH:before {
  content: "\e7a1";
}

.icon-ItemList:before {
  content: "\e79f";
}

.icon-UserRights:before {
  content: "\e7a0";
}

.icon-Time:before {
  content: "\e79c";
}

.icon-Triangle:before {
  content: "\e79d";
}

.icon-News:before {
  content: "\e79e";
}

.icon-conversation:before {
  content: "\e79b";
}

.icon-a-30:before {
  content: "\e79a";
}

.icon-a-29:before {
  content: "\e799";
}

.icon-user:before {
  content: "\e797";
}

.icon-Vcode:before {
  content: "\e798";
}

.icon-KunDB:before {
  content: "\e78c";
}

.icon-WebHDFS:before {
  content: "\e796";
}

.icon-TSDB:before {
  content: "\e795";
}

.icon-OpenTSDB:before {
  content: "\e794";
}

.icon-Elasticsearch:before {
  content: "\e793";
}

.icon-Cassandra:before {
  content: "\e792";
}

.icon-KUDU:before {
  content: "\e791";
}

.icon-GDB:before {
  content: "\e790";
}

.icon-OceanBase:before {
  content: "\e78f";
}

.icon-KingBase:before {
  content: "\e78e";
}

.icon-CASC:before {
  content: "\e78d";
}

.icon-ArgoDB:before {
  content: "\e78b";
}

.icon-UpdateKey:before {
  content: "\e78a";
}

.icon-GBase:before {
  content: "\e788";
}

.icon-HWOBS:before {
  content: "\e789";
}

.icon-Teradata:before {
  content: "\e743";
}

.icon-a-19:before {
  content: "\e73f";
}

.icon-a-17:before {
  content: "\e740";
}

.icon-a-22:before {
  content: "\e741";
}

.icon-a-16:before {
  content: "\e742";
}

.icon-a-9:before {
  content: "\e77b";
}

.icon-a-15:before {
  content: "\e77c";
}

.icon-a-13:before {
  content: "\e77d";
}

.icon-a-20:before {
  content: "\e77e";
}

.icon-a-24:before {
  content: "\e77f";
}

.icon-a-21:before {
  content: "\e780";
}

.icon-a-23:before {
  content: "\e781";
}

.icon-a-28:before {
  content: "\e782";
}

.icon-a-18:before {
  content: "\e783";
}

.icon-a-26:before {
  content: "\e784";
}

.icon-a-25:before {
  content: "\e785";
}

.icon-a-27:before {
  content: "\e786";
}

.icon-a-10:before {
  content: "\e787";
}

.icon-a-6:before {
  content: "\e733";
}

.icon-a-8:before {
  content: "\e734";
}

.icon-a-5:before {
  content: "\e738";
}

.icon-a-3:before {
  content: "\e739";
}

.icon-a-14:before {
  content: "\e73a";
}

.icon-a-2:before {
  content: "\e73b";
}

.icon-a-4:before {
  content: "\e73c";
}

.icon-a-11:before {
  content: "\e73d";
}

.icon-a-7:before {
  content: "\e73e";
}

.icon-a-1:before {
  content: "\e732";
}

.icon-tuozhuai:before {
  content: "\e646";
}

.icon-increment:before {
  content: "\e731";
}

.icon-compile:before {
  content: "\e730";
}

.icon-MaxCompute:before {
  content: "\e72f";
}

.icon-platformSecurity:before {
  content: "\e72e";
}

.icon-noGrade:before {
  content: "\e72d";
}

.icon-dataSecurity:before {
  content: "\e72c";
}

.icon-V10:before {
  content: "\e722";
}

.icon-V8:before {
  content: "\e723";
}

.icon-V7:before {
  content: "\e724";
}

.icon-V5:before {
  content: "\e725";
}

.icon-V6:before {
  content: "\e726";
}

.icon-V2:before {
  content: "\e727";
}

.icon-V9:before {
  content: "\e728";
}

.icon-V3:before {
  content: "\e729";
}

.icon-V1:before {
  content: "\e72a";
}

.icon-V4:before {
  content: "\e72b";
}

.icon-dataTraceability:before {
  content: "\e721";
}

.icon-sensitiveDataIdentification:before {
  content: "\e71e";
}

.icon-desensitization:before {
  content: "\e71f";
}

.icon-watermark:before {
  content: "\e720";
}

.icon-noPermissionTable:before {
  content: "\e71d";
}

.icon-center:before {
  content: "\e716";
}

.icon-11:before {
  content: "\e717";
}

.icon-partition:before {
  content: "\e718";
}

.icon-dataTemplate:before {
  content: "\e719";
}

.icon-interfacePush:before {
  content: "\e71a";
}

.icon-sort:before {
  content: "\e71b";
}

.icon-adaptationWindow:before {
  content: "\e71c";
}

.icon-jiazaizhong:before {
  content: "\e715";
}

.icon-uniqueKey:before {
  content: "\e6fa";
}

.icon-libraryTablePush:before {
  content: "\e6f8";
}

.icon-Standardization:before {
  content: "\e710";
}

.icon-tableInput:before {
  content: "\e711";
}

.icon-qualityExploration:before {
  content: "\e712";
}

.icon-tableOutput:before {
  content: "\e713";
}

.icon-dataProcessing:before {
  content: "\e714";
}

.icon-Youxuan:before {
  content: "\e70f";
}

.icon-DM7:before {
  content: "\e70e";
}

.icon-chenggong:before {
  content: "\e70d";
}

.icon-GaussDB:before {
  content: "\e70c";
}

.icon-special:before {
  content: "\e70b";
}

.icon-workflowTab:before {
  content: "\e70a";
}

.icon-notificationManagement:before {
  content: "\e705";
}

.icon-Kerberos:before {
  content: "\e709";
}

.icon-desensitizationRules:before {
  content: "\e706";
}

.icon-automaticLayout:before {
  content: "\e707";
}

.icon-boxSelection:before {
  content: "\e708";
}

.icon-dataPermission:before {
  content: "\e704";
}

.icon-daimageshihua:before {
  content: "\e703";
}

.icon-Kafka:before {
  content: "\e702";
}

.icon-view:before {
  content: "\e701";
}

.icon-dots:before {
  content: "\e700";
}

.icon-realtime-collection1:before {
  content: "\e6fe";
}

.icon-flume:before {
  content: "\e6fd";
}

.icon-operator:before {
  content: "\e6fc";
}

.icon-government:before {
  content: "\e6fb";
}

.icon-Hive:before {
  content: "\e6f7";
}

.icon-layered:before {
  content: "\e6f6";
}

.icon-empower:before {
  content: "\e6f5";
}

.icon-realtime-collection:before {
  content: "\e6f2";
}

.icon-realtime-job:before {
  content: "\e6f3";
}

.icon-data-authority:before {
  content: "\e6f4";
}

.icon-Mustang:before {
  content: "\e6f1";
}

.icon-globalConfig:before {
  content: "\e6f0";
}

.icon-DRDS:before {
  content: "\e6db";
}

.icon-Cayman:before {
  content: "\e6e5";
}

.icon-DodoX:before {
  content: "\e6e6";
}

.icon-Spark:before {
  content: "\e6e7";
}

.icon-Phoenix:before {
  content: "\e6e8";
}

.icon-Leopard:before {
  content: "\e6e9";
}

.icon-Logstash:before {
  content: "\e6ea";
}

.icon-Presto:before {
  content: "\e6eb";
}

.icon-Eagles:before {
  content: "\e6ec";
}

.icon-EEL:before {
  content: "\e6ee";
}

.icon-Hadoop:before {
  content: "\e6ef";
}

.icon-FTP:before {
  content: "\e6dc";
}

.icon-Teryx:before {
  content: "\e6dd";
}

.icon-OSS:before {
  content: "\e6de";
}

.icon-Stork:before {
  content: "\e6df";
}

.icon-ODPS:before {
  content: "\e6e0";
}

.icon-MongoDB:before {
  content: "\e6e1";
}

.icon-OTS:before {
  content: "\e6e2";
}

.icon-HBase:before {
  content: "\e6e3";
}

.icon-HDFS:before {
  content: "\e6e4";
}

.icon-Greenplum:before {
  content: "\e6da";
}

.icon-TXTFile:before {
  content: "\e6cd";
}

.icon-DB2:before {
  content: "\e6ce";
}

.icon-RDBMS:before {
  content: "\e6d0";
}

.icon-ADS:before {
  content: "\e6d1";
}

.icon-Oracle:before {
  content: "\e6d2";
}

.icon-CSV:before {
  content: "\e6d3";
}

.icon-OCS:before {
  content: "\e6d4";
}

.icon-MySQL:before {
  content: "\e6d5";
}

.icon-Stream:before {
  content: "\e6d6";
}

.icon-SQL-Server:before {
  content: "\e6d7";
}

.icon-Xlsx:before {
  content: "\e6d8";
}

.icon-PostgreSQL:before {
  content: "\e6d9";
}

.icon-switch1:before {
  content: "\e6bc";
}

.icon-switch:before {
  content: "\e6bb";
}

.icon-filled-code:before {
  content: "\e6ba";
}

.icon-withdraw:before {
  content: "\e6b8";
}

.icon-Audit:before {
  content: "\e6b9";
}

.icon-landingExtraction:before {
  content: "\e6b7";
}

.icon-bihe:before {
  content: "\e6b5";
}

.icon-open:before {
  content: "\e6b6";
}

.icon-bucket:before {
  content: "\e6b3";
}

.icon-dataSharing:before {
  content: "\e68b";
}

.icon-zidingyiCaiji:before {
  content: "\e689";
}

.icon-waiting:before {
  content: "\e688";
}

.icon-datax:before {
  content: "\e687";
}

.icon-cmd:before {
  content: "\e686";
}

.icon-loading:before {
  content: "\e685";
}

.icon-dataPortals:before {
  content: "\e684";
}

.icon-hot:before {
  content: "\e682";
}

.icon-standard:before {
  content: "\e681";
}

.icon-metamodelManagement:before {
  content: "\e67f";
}

.icon-metadataDevelopment:before {
  content: "\e680";
}

.icon-enlarge:before {
  content: "\e67d";
}

.icon-narrow:before {
  content: "\e67e";
}

.icon-ziduan:before {
  content: "\e67c";
}

.icon-schema:before {
  content: "\e67b";
}

.icon-copy:before {
  content: "\e67a";
}

.icon-card-title:before {
  content: "\e679";
}

.icon-key:before {
  content: "\e678";
}

.icon-integer:before {
  content: "\e778";
}

.icon-string:before {
  content: "\e779";
}

.icon-decimal:before {
  content: "\e77a";
}

.icon-check:before {
  content: "\e777";
}

.icon-safety-certificate:before {
  content: "\e776";
}

.icon-share-alt:before {
  content: "\e775";
}

.icon-port-circle:before {
  content: "\e774";
}

.icon-question:before {
  content: "\e773";
}

.icon-empty:before {
  content: "\e772";
}

.icon-bulb:before {
  content: "\e770";
}

.icon-tags:before {
  content: "\e771";
}

.icon-theme:before {
  content: "\e76f";
}

.icon-update:before {
  content: "\e76e";
}

.icon-last-clock:before {
  content: "\e76d";
}

.icon-down:before {
  content: "\e769";
}

.icon-left:before {
  content: "\e76a";
}

.icon-right:before {
  content: "\e76b";
}

.icon-up:before {
  content: "\e76c";
}

.icon-check-status:before {
  content: "\e768";
}

.icon-export:before {
  content: "\e766";
}

.icon-import:before {
  content: "\e767";
}

.icon-link:before {
  content: "\e765";
}

.icon-close-circle:before {
  content: "\e764";
}

.icon-plus-circle:before {
  content: "\e75c";
}

.icon-block:before {
  content: "\e75d";
}

.icon-question-circle:before {
  content: "\e75e";
}

.icon-clock-circle:before {
  content: "\e75f";
}

.icon-page-add:before {
  content: "\e760";
}

.icon-list-all:before {
  content: "\e761";
}

.icon-data:before {
  content: "\e762";
}

.icon-paper-clip:before {
  content: "\e763";
}

.icon-history:before {
  content: "\e74c";
}

.icon-double-screen:before {
  content: "\e74d";
}

.icon-ellipsis:before {
  content: "\e74e";
}

.icon-forward:before {
  content: "\e74f";
}

.icon-calendar:before {
  content: "\e750";
}

.icon-pic-list:before {
  content: "\e751";
}

.icon-calendar-date:before {
  content: "\e752";
}

.icon-menu:before {
  content: "\e753";
}

.icon-fullscreen:before {
  content: "\e754";
}

.icon-fullscreen-exit:before {
  content: "\e755";
}

.icon-arrow-down:before {
  content: "\e756";
}

.icon-pic-text-list:before {
  content: "\e757";
}

.icon-arrow-up:before {
  content: "\e758";
}

.icon-setting:before {
  content: "\e759";
}

.icon-single-screen:before {
  content: "\e75a";
}

.icon-zu71:before {
  content: "\e75b";
}

.icon-edit:before {
  content: "\e6ed";
}

.icon-close:before {
  content: "\e6f9";
}

.icon-profile:before {
  content: "\e6ff";
}

.icon-eye:before {
  content: "\e735";
}

.icon-eye-invisible:before {
  content: "\e736";
}

.icon-file-search:before {
  content: "\e737";
}

.icon-examineScale:before {
  content: "\e744";
}

.icon-typecount:before {
  content: "\e745";
}

.icon-datacount:before {
  content: "\e746";
}

.icon-folder-add:before {
  content: "\e747";
}

.icon-login:before {
  content: "\e748";
}

.icon-search:before {
  content: "\e749";
}

.icon-unordered-list:before {
  content: "\e74a";
}

.icon-filled-appstore:before {
  content: "\e74b";
}

.icon-info-circle:before {
  content: "\e6cf";
}

.icon-upload:before {
  content: "\e6ad";
}

.icon-poweroff:before {
  content: "\e6ae";
}

.icon-download:before {
  content: "\e6bf";
}

.icon-upgrade:before {
  content: "\e6c0";
}

.icon-file-zip:before {
  content: "\e6c1";
}

.icon-break:before {
  content: "\e6c2";
}

.icon-downgrade:before {
  content: "\e6c3";
}

.icon-to-down:before {
  content: "\e6c4";
}

.icon-to-top:before {
  content: "\e6c5";
}

.icon-minus-circle:before {
  content: "\e6c6";
}

.icon-replay:before {
  content: "\e6c7";
}

.icon-detach:before {
  content: "\e6c8";
}

.icon-duplicate-data:before {
  content: "\e6c9";
}

.icon-star:before {
  content: "\e6ca";
}

.icon-go:before {
  content: "\e6cb";
}

.icon-clear:before {
  content: "\e6cc";
}

.icon-plus:before {
  content: "\e6a2";
}

.icon-pause-circle:before {
  content: "\e6a3";
}

.icon-delete:before {
  content: "\e6a4";
}

.icon-play-circle:before {
  content: "\e6a5";
}

.icon-save:before {
  content: "\e6a6";
}

.icon-publish:before {
  content: "\e6a8";
}

.icon-move:before {
  content: "\e6a9";
}

.icon-reload:before {
  content: "\e6aa";
}

.icon-rollback:before {
  content: "\e6ab";
}

.icon-preview:before {
  content: "\e6ac";
}

.icon-single-extraction:before {
  content: "\e69a";
}

.icon-unlock:before {
  content: "\e69b";
}

.icon-lock:before {
  content: "\e69c";
}

.icon-batch-extraction:before {
  content: "\e69d";
}

.icon-filled-folder-open:before {
  content: "\e671";
}

.icon-filled-folder:before {
  content: "\e672";
}

.icon-code:before {
  content: "\e673";
}

.icon-database:before {
  content: "\e674";
}

.icon-tree-all:before {
  content: "\e675";
}

.icon-customize:before {
  content: "\e676";
}

.icon-page:before {
  content: "\e677";
}

.icon-table:before {
  content: "\e698";
}

.icon-file-text:before {
  content: "\e699";
}

.icon-kettle:before {
  content: "\e66a";
}

.icon-php:before {
  content: "\e66b";
}

.icon-sql:before {
  content: "\e66c";
}

.icon-shell:before {
  content: "\e66d";
}

.icon-danlichouqu2:before {
  content: "\e66e";
}

.icon-piliangchouqu2:before {
  content: "\e66f";
}

.icon-python:before {
  content: "\e670";
}

.icon-zu3:before {
  content: "\e665";
}

.icon-zu41:before {
  content: "\e666";
}

.icon-zu42:before {
  content: "\e667";
}

.icon-zu43:before {
  content: "\e669";
}

.icon-add:before {
  content: "\e661";
}

.icon-multiply:before {
  content: "\e662";
}

.icon-minus:before {
  content: "\e663";
}

.icon-divide:before {
  content: "\e664";
}

.icon-fenlieziduanneironghui1:before {
  content: "\e657";
}

.icon-jiequzhidingweizhineirong1:before {
  content: "\e658";
}

.icon-shanjianzhidingneironghui1:before {
  content: "\e659";
}

.icon-zhidingweizhitianjianeironghui1:before {
  content: "\e65a";
}

.icon-replace:before {
  content: "\e65b";
}

.icon-ziduanneirongpinjiehui1:before {
  content: "\e65c";
}

.icon-ziduanneirongyunsuanhui1:before {
  content: "\e65d";
}

.icon-hebingziduanneironghui1:before {
  content: "\e65e";
}

.icon-tianjiamorenzhihui1:before {
  content: "\e65f";
}

.icon-add-default:before {
  content: "\e660";
}

.icon-ziduanleixingzhuanhuanhui1:before {
  content: "\e668";
}

.icon-bushuwendang1:before {
  content: "\e64a";
}

.icon-chanpinwendang1:before {
  content: "\e64b";
}

.icon-caozuowendang1:before {
  content: "\e64c";
}

.icon-FAQwendang1:before {
  content: "\e64d";
}

.icon-ditujiansuo1:before {
  content: "\e64e";
}

.icon-releasewendang1:before {
  content: "\e64f";
}

.icon-yinpinchuli1:before {
  content: "\e650";
}

.icon-shipinchuli1:before {
  content: "\e651";
}

.icon-wenbenzhuanma1:before {
  content: "\e652";
}

.icon-zhongwenfenci1:before {
  content: "\e653";
}

.icon-zuoyetiaodu1:before {
  content: "\e654";
}

.icon-shiliwendang1:before {
  content: "\e655";
}

.icon-quanwensousuo1:before {
  content: "\e656";
}

.icon-filled-close-circle:before {
  content: "\e645";
}

.icon-exclamation-circle:before {
  content: "\e647";
}

.icon-check-circle:before {
  content: "\e649";
}

.icon-logout:before {
  content: "\e632";
}

.icon-hosts:before {
  content: "\e633";
}

.icon-resManagement:before {
  content: "\e634";
}

.icon-log-audit:before {
  content: "\e635";
}

.icon-role:before {
  content: "\e636";
}

.icon-log-operate:before {
  content: "\e637";
}

.icon-idcard:before {
  content: "\e638";
}

.icon-union-search:before {
  content: "\e639";
}

.icon-assetIndex:before {
  content: "\e63a";
}

.icon-source:before {
  content: "\e63b";
}

.icon-zu26:before {
  content: "\e63c";
}

.icon-page-manage:before {
  content: "\e63d";
}

.icon-license:before {
  content: "\e63e";
}

.icon-db-dashboard:before {
  content: "\e63f";
}

.icon-project:before {
  content: "\e640";
}

.icon-team:before {
  content: "\e641";
}

.icon-obj-store:before {
  content: "\e642";
}

.icon-skin:before {
  content: "\e643";
}

.icon-overview:before {
  content: "\e644";
}

.icon-filter:before {
  content: "\e624";
}

.icon-hourglass:before {
  content: "\e626";
}

.icon-extract:before {
  content: "\e627";
}

.icon-standard-management:before {
  content: "\e628";
}

.icon-workflow:before {
  content: "\e629";
}

.icon-script:before {
  content: "\e62a";
}

.icon-dashboard:before {
  content: "\e62b";
}

.icon-cloud-upload:before {
  content: "\e62c";
}

.icon-function:before {
  content: "\e62d";
}

.icon-cycle-job:before {
  content: "\e62e";
}

.icon-execute-log:before {
  content: "\e62f";
}

.icon-task:before {
  content: "\e630";
}

.icon-queue:before {
  content: "\e631";
}

.icon-bell:before {
  content: "\e625";
}

.icon-home:before {
  content: "\e622";
}

