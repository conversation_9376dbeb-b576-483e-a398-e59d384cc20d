import { useState, useRef, useEffect } from 'react';
import {
  Button,
  Space,
  Drawer,
  Form,
  Col,
  Row,
  Select,
  Empty,
  notification,
  Tooltip,
  Spin,
} from 'antd';
import {
  FetchSelect,
  FieldMap,
  SearchTreeSelect,
  IconFont,
  Ellipsis,
} from '@dana/ui';
import { useDispatch, useSelector } from 'umi';
import { ConnectState, Dispatch, AnyAction } from '@/models/connect.d';
import transformToChinese from '@/pages/DataAssets/DBAssetsCenter/AssetsDetails/FieldInfo/utils';
import colors from '@/theme/color.less';
import { useRequest } from '@/utils';
import { cloneDeep, uniqBy } from 'lodash';
import { equalsObj } from './utils';

import styles from './index.less';
import type { ResourceType } from '../index';

const ellipsisStyle: React.CSSProperties = {
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
};

type EdgesType = {
  source: string;
  target: string;
}[];

interface MountResourceProps {
  mountResourceVisible: boolean;
  afterResourceVisibleCallback: () => void;
  resource: ResourceType;
}

const MountResource = (props: MountResourceProps) => {
  const { mountResourceVisible, afterResourceVisibleCallback, resource } =
    props;
  // fieldmap映射
  const fieldMapRef = useRef<any>(null);
  const [form] = Form.useForm();
  const dispatch = useDispatch<Dispatch<AnyAction>>();
  const { editMountResource } = useSelector(
    (state: ConnectState) => state.assetMount,
  );
  // fieldmap右侧数据
  const [rightDataSource, setRightDataSource] = useState<any[]>([]);
  // fieldmap左侧数据
  const [leftDataSource, setLeftDataSource] = useState<any>(null);
  // 类目表
  const [cateId, setCateId] = useState<string>('');
  // 类目数据
  const [cateData, setCateData] = useState<any[]>([]);
  // 更新表状态
  const [isRefresh, setIsRefresh] = useState<boolean>(false);
  // 是否更换表
  const [isChangeTable, setIsChangeTable] = useState<boolean>(false);
  // tableRelation
  const [editTableRelationData, setEditTableRelationData] = useState<any>(null);

  // 编目过来的有手动添加的信息 需要默认不连上 其余正常展示节点及连线
  const [needEmpty, setNeedEmpty] = useState<boolean>(false);
  // 资源类目数据
  const { catalogsTree } = useSelector(
    (state: ConnectState) => state.catalogTree,
  );
  const loadingEffect = useSelector((state: ConnectState) => state.loading);
  const treeLoading = loadingEffect.effects['catalogTree/listCatalogs'];

  // 挂载表格loading
  const mountTableLoading =
    loadingEffect.effects['assetMount/listTableFieldInfo'];

  /**
   *
   * @description 保存挂载信息
   */
  const { loading: saveLoading, run: saveMountData } = useRequest(
    'modeling/moneyattach/attachsave',
    {
      onSuccess: () => {
        // 保存成功
        notification.success({
          message: '保存成功',
        });
        clearMap();
        // 保存成功后  关闭弹窗
        afterResourceVisibleCallback();
      },
    },
  );

  /**
   *
   *
   * @description 查看挂载信息
   *
   * */

  const { run: getMountInfo, loading: mountInfoLoading } = useRequest(
    'modeling/moneyattach/attachinfo',
    {
      onSuccess: ({ result, istabledel }) => {
        if (istabledel) {
          notification.warning({
            message: '资产目录挂载的资源表已被删除，请重新挂载',
          });
          form.resetFields();
          form.setFieldsValue({
            mountCategory: undefined,
            table: undefined,
          });
          // 信息项是不变的
          setLeftDataSource(result.assetinfo);
        } else {
          // try {
          // 信息项是不变的
          setLeftDataSource(result.assetinfo);
          // 是不是保存后的数据
          const savedAlready = !!result?.attachinfo?.attachtableinfo?.id;
          const tempData = editMountResource
            ? editMountResource.get(resource.id)?.data
            : null;
          // 有暂存数据
          const hasTempData = tempData?.categoryid || tempData?.tableid;
          if (savedAlready) {
            // 永存
            //  上次编辑没有存数据 或者是组件已经销毁了
            const { id, catalogid } = result.attachinfo.attachtableinfo;
            handleSelectTable(id);
            setCateId(catalogid);

            form.setFieldsValue({
              mountCategory: catalogid,
              // * 直接用列表数据的关联资源表名称
              table: { value: id, label: resource?.attachtbname },
            });

            //! 看是不是有手动新建的  手动新建的信息项不连线
            const { hascustomadd } = result.attachinfo;
            setNeedEmpty(hascustomadd);

            //  比較兩個對象是否相同  若是不同 意味著曾經在保存的基礎上編輯過
            // 需要展示暫存的數據 否則 展示永存的數據

            if (tempData && tempData.tableid) {
              const flag = equalsObj(
                tempData,
                result.attachinfo.mountFieldMapData,
              );
              if (flag) {
                // 永存后没编辑数据

                // const { edges } = result.attachinfo.mountFieldMapData;
                // 现在的边点数据
                // setMapEdges(edges);
                setEditTableRelationData(result.attachinfo.mountFieldMapData);
              } else {
                // 永存后编辑了数据
                // const { edges } = tempData.tableRelation;
                // 现在编辑的边点数据
                // setMapEdges(edges);
                // 暂存的时候 可能 最开始在挂载的时候 只有一条信息项
                //   点击暂存后 又去编目新增了 导致 暂存的一项和后台返回的不一致
                //  导致信息项的顺序 展示不正确 未按照1234顺序排列 需要前端手动处理

                // ?? v5.6.3 编辑新增过来的信息项全部特殊处理 全部采用后台返回的数据
                setEditTableRelationData(
                  hascustomadd ? result.attachinfo.mountFieldMapData : tempData,
                );
              }
            } else {
              // const { edges } = result.attachinfo.mountFieldMapData;
              // 现在编辑的边点数据
              // setMapEdges(edges);
              // 这个主要是存isSort
              setEditTableRelationData(result.attachinfo.mountFieldMapData);
            }
          } else if (hasTempData) {
            // 暂存
            const {
              tableid: id,
              categoryid: catalogid,
              tempTableName,
            } = tempData;
            // 如果tableid和 categoryid 都没有 就直接返回
            if (!id && !catalogid) {
              form.setFieldsValue({
                mountCategory: undefined,
                table: undefined,
              });
              setEditTableRelationData(null);
              return;
            }
            // 若是有数据才进行下一步
            if (catalogid) {
              setCateId(catalogid);
              form.setFieldsValue({
                mountCategory: catalogid,
              });
            }
            if (id) {
              handleSelectTable(id);
              form.setFieldsValue({
                // * 暂存用暂存表数据
                table: { value: id, label: tempTableName },
              });
            }

            // 前端拼接信息项数据
            if (tempData.tableRelation.edges?.length > 0) {
              const { nodes } = tempData.tableRelation;
              // const { edges, nodes } = tempData.tableRelation;
              const savedNodes = cloneDeep(nodes);

              // 暂存进来也要设置信息项 且为了保证数据最新 要取后台返回的信息项
              // 拼接开始 👇
              const totalItemInfo = formatLeftData(result.assetinfo);
              const data = totalItemInfo.map((item) => {
                return {
                  positon: 'left',
                  ...item,
                };
              });
              // 截取nodes里面全部是left的数据 用最新的数据替代
              const tempResult = savedNodes.filter(
                (item) => item.position === 'left',
              );
              const tempRightData = tempData.tableRelation.nodes.slice(
                tempResult.length,
              );
              tempData.tableRelation.nodes = [];
              tempData.tableRelation.nodes = data.concat(tempRightData);
              // 拼接结束 👆
              setEditTableRelationData(tempData);
              // 现在编辑的边点数据
              // setMapEdges(edges);
            }
          } else {
            // 新增
            form.setFieldsValue({
              mountCategory: undefined,
              table: undefined,
            });
            setEditTableRelationData(null);
          }
        }
      },
      onError: () => {
        // 页面空调用 报错 直接赋值为空
        form.setFieldsValue({
          mountCategory: undefined,
          table: undefined,
        });
      },
    },
  );

  // 匹配左右两侧的数据
  function getOrderRules(position, data) {
    const { nodes } = data;
    // 正常连线情况
    const orderRulesResult = nodes
      .filter((item) => item.position === position)
      .map((item) => item.fieldname);
    return orderRulesResult;
  }

  function formatLeftData(leftData) {
    if (!leftData) {
      return [];
    }
    let result: any[] = [];
    result = leftData.map((item: any) => {
      return {
        fieldName: item.infoitemsname,
        fieldType: item.infoitemstype,
        ...item,
      };
    });

    return result;
  }

  const sliceEdges = (op) => {
    return op.slice(0, op.length - 2);
  };

  const checkFieldMapIsError = () => {
    const fieldError = fieldMapRef.current?.fieldError;
    return Object.values(fieldError).some((item) => item);
  };

  const formatFieldInfo = (originFieldInfo, edges: EdgesType) => {
    const result = cloneDeep(originFieldInfo);
    edges.forEach((v, i) => {
      const { target } = v;
      const ci = result.findIndex((c) => c.fieldname === target);
      if (ci !== i) {
        // 如果不是在目标位置就交换位置
        result.splice(i, 0, ...result.splice(ci, 1));
      }
    });
    return result;
  };

  const onFinish = () => {
    form
      .validateFields()
      .then((values) => {
        // * 检测字段映射是否有误（字段映射、主键映射、增量字段映射、信息项全部映射）
        const isError = checkFieldMapIsError();
        if (isError) {
          form.scrollToField('fieldMap', { block: 'center' });
          return;
        }

        // !!! 可以通过ref 拿到G6的实例，从而可以调用实例的一些方法，比如当前的节点、边状态等（很重要）
        const graph = fieldMapRef.current?.graph;
        //* （新增的）
        const isSort = fieldMapRef.current?.isSort;
        const nodes = graph?.getNodes().map((item) => item?.getModel());

        // * 拿到边后，需要外部处理下后缀值(传参时去掉后缀&1、&2)，可参考此处的处理
        const curEdges = graph?.getEdges().map((item) => {
          const { source, target } = item.getModel();
          return {
            source: sliceEdges(source),
            target: sliceEdges(target),
          };
        });

        // * 组装数据(接口需要的数据结构)，比如：
        const tableRelation = {
          nodes: nodes.map((item) => ({
            fieldname: item.fieldName,
            fieldtype: item.fieldType,
            number: item.number,
            position: item.position,
            name: item.fieldName,
            annotation: item.comment,
            isprim: item.isPrim,
            isunique: item.isUnique,
            ispartition: item.isPartition,
            ranknum: item.rankNum,
            rankname: item.rankName,
          })),
          edges: curEdges,
        };

        const resParams = {
          inventoryid: resource.id,
          attachtype: '挂载',
          assetinfo: leftDataSource, // 信息项数据
          attachinfo: {
            attachtype: 'table',
            attachtableinfo: {
              id: values.table.value, // 挂载的表id  {value:string,label:string}
              catalogid: cateId, // 表的文件夹id 左侧资源类目的id
              fieldinfo: formatFieldInfo(
                tableRelation.nodes.slice(leftDataSource.length),
                curEdges,
              ), // 右侧的字段信息 正确对应上信息项后的数据
              // fieldinfo: tableRelation.nodes.slice(leftDataSource.length), // 右侧的字段信息 正确对应上信息项后的数据
            },
            mountFieldMapData: {
              tableid: values.table.value,
              isSort,
              tableRelation,
            },
          },
        };
        //  接口传参
        saveMountData(resParams);
      })
      .catch((err) => {
        console.info(err);
      });
  };

  function clearMap() {
    // 点击取消的时候 若是当前数据有暂存 则清除暂存数据
    const key = resource.id;
    const map = editMountResource || new Map();
    if (!map.has(key)) {
      map.set(key, {});
    }
    map.get(key).data = null;
  }

  const cancel = () => {
    clearMap();
    afterResourceVisibleCallback();
  };
  const save = () => {
    onFinish();
  };

  // *此函数用于调整数据源为 SearchTreeSelect 需要的格式
  function mapTreeData(data) {
    return data.map((item) => ({
      value: item.id,
      id: item.id,
      count: item.count,
      name: item.name,
      title: item.name,
      extra: (
        <span style={{ position: 'absolute', right: 8 }}>({item.count})</span>
      ),
      children: item.children && mapTreeData(item.children),
    }));
  }

  function onSelectTree(key) {
    setCateId(key);
    // 更改类目项后应该重新更新 资源表数据
    form.setFieldsValue({
      table: undefined,
    });
  }

  /**
   *
   *
   *  @description 选中某个table的数据
   *
   *  */
  function handleSelectTable(id) {
    // 如果没有id  直接不执行后续操作
    if (!id) return;
    return (
      dispatch({
        type: 'assetMount/listTableFieldInfo',
        payload: {
          id,
          // 获取资源表字段信息的时候  isattach传false
          isattach: false,
          // 需要处理前三条数据
          needrecord: true,
        },
      }) as any
    ).then(({ code, result }) => {
      if (code === 200) {
        const INIT_RULE = {
          ruleid: '',
          ruletype: '',
          rulename: '',
          ruledesc: '',
          rulesql: '',
        };
        const fieldInfo = result.fieldinfo.map((item) => {
          return {
            selected: true,
            number: item.number,
            key: item.name,
            fieldName: item.name,
            originName: item.name,
            fieldType: item.fieldtype,
            data: result.recordinfos.map((v) => v[item.name]),
            isPartition: item.ispartition,
            isPrim: item.isprim,
            isUnique: item.isunique,
            rankName: item.rankname,
            rankNum: item.ranknum,
            comment: item.annotation,
            conversiontype: result.conversiontype[item.name],
            ruleinfo: INIT_RULE,
            ...(item.ispartition && {
              partitionNumber:
                // eslint-disable-next-line no-unsafe-optional-chaining
                (result.partitioninfo || []).findIndex(
                  (p) => p.name === item.name,
                ) + 1,
            }),
            ...item,
          };
        });
        setRightDataSource(fieldInfo);
      }
    });
  }

  /**
   *
   *  @description 请求右侧资源表数据
   *
   *
   * */
  const fetchOptions = (payload) => {
    const {
      searchValue = '',
      page,
      pageSize,
      key = '',
      ...restPayload
    } = payload;

    return (
      dispatch({
        type: 'assetMount/listMountTable',
        payload: {
          catalogdirid: cateId,
          page,
          ignoreop: true,
          perpage: pageSize,
          tableid: key, // key 这个传参的作用是会将初始值拼到第一页的数据后面，这样就不会显示id了
          ...(searchValue
            ? {
                filterset: {
                  fields: ['tbname', 'describe'],
                  match: searchValue,
                },
              }
            : {}),

          // 更新时间 倒序展示
          sort: {
            sortfield: 'modified',
            sorttype: 1,
          },
          ...restPayload,
        },
      }) as any
    ).then((data) => {
      if (data.code === 200) {
        const { result: tempResult, total = 0 } = data;
        // 给result去下重
        // @ts-ignore
        const result =
          tempResult.length > 0 ? uniqBy(tempResult, (item) => item.id) : [];

        const firstItem = {
          value: 'title',
          label: <span style={{ color: '#333' }}>表名称</span>,
          extraRender: <span className={styles.describe}>表描述</span>,
          disabled: true,
        };
        const optionList = result.map((item) => ({
          value: item.id, // 最小改动 value保持id原样
          id: item.id,
          label: <span className={styles.hiddenLabel}>{item.tbname}</span>,
          extraRender: (
            <>
              <span style={{ display: 'inline-block', width: '55%' }}>
                <Ellipsis lines={1} tooltip>
                  {item.tbname}
                </Ellipsis>
              </span>
              <span className={styles.describe}>
                <Ellipsis lines={1} tooltip style={{ maxWidth: 150 }}>
                  {item.describe || '--'}
                </Ellipsis>
              </span>
            </>
          ),
        }));

        return {
          optionList: optionList.length > 0 ? [firstItem, ...optionList] : [],
          total,
        };
      } else {
        notification.warning({
          message: '获取资源表失败',
        });
      }
    });
  };

  useEffect(() => {
    // 资源类目数据
    dispatch({
      type: 'catalogTree/listCatalogs',
    });
  }, []);

  useEffect(() => {
    getMountInfo({
      inventoryid: resource.id,
    });
  }, []);

  useEffect(() => {
    if (catalogsTree) {
      const data = mapTreeData(catalogsTree);
      setCateData(data);
    }
  }, [catalogsTree]);

  return (
    <Spin spinning={mountInfoLoading} style={{ height: '100%' }}>
      <Drawer
        title={resource.name}
        placement="right"
        visible={mountResourceVisible}
        onClose={() => {
          // 只要关闭都重置为false
          setNeedEmpty(false);
          // 抽屉关闭的时候 将页面操作状态存至model
          // 以便下次打开的时候 先调用
          // 如果是组件切换展示详情或者是别的编辑页面 点击路由需回置到列表状态

          // !!! 可以通过ref 拿到G6的实例，从而可以调用实例的一些方法，比如当前的节点、边状态等（很重要）
          const graph = fieldMapRef.current?.graph;
          //* （新增的）
          const isSort = fieldMapRef.current?.isSort;
          const nodes = graph?.getNodes().map((item) => item?.getModel());

          // * 拿到边后，需要外部处理下后缀值(传参时去掉后缀&1、&2)，可参考此处的处理
          const curEdges = graph?.getEdges().map((item) => {
            const { source, target } = item.getModel();
            return {
              source: sliceEdges(source),
              target: sliceEdges(target),
            };
          });

          // * 组装数据(接口需要的数据结构)，比如：
          const tableRelation = {
            nodes: nodes
              ? nodes.map((item) => ({
                  fieldname: item.fieldName,
                  fieldtype: item.fieldType,
                  number: item.number,
                  position: item.position,
                  name: item.fieldName,
                  annotation: item.comment,
                  isprim: item.isPrim,
                  isunique: item.isUnique,
                  ispartition: item.isPartition,
                  ranknum: item.rankNum,
                  rankname: item.rankName,
                }))
              : [],
            edges: curEdges,
          };

          // 可能通过操作 allowclear  导致现在的表单没有赋值 且状态没更新
          // 所以用 form.getFieldValue 来取表单最新的值
          const categoryid = form.getFieldValue('mountCategory');
          // * 如果直接解构的话 可能只选择了资源类目   没有选资源表  form getvalue会undefined
          const tempTableDetail = form.getFieldValue('table');
          // 暂存数据
          const temp = {
            inventoryid: resource.id,
            tableid: tempTableDetail ? tempTableDetail.value : '',
            categoryid,
            isSort,
            tableRelation,
            tempTableName: tempTableDetail ? tempTableDetail.label : '',
          };

          // 如果有 就用以前的map 如果没有就新建
          const map = editMountResource || new Map();
          // 唯一键
          const key = resource.id;

          if (!map.has(key)) {
            map.set(key, {});
          }
          map.get(key).data = temp;
          dispatch({
            type: 'assetMount/save',
            payload: {
              editMountResource: map,
            },
          });

          // 暂存完数据后 关闭弹窗
          afterResourceVisibleCallback();
        }}
        closable={false}
        width={1080}
        extra={
          <Space>
            <Button onClick={() => cancel()}>取消</Button>
            <Button loading={saveLoading} onClick={save} type="primary">
              保存
            </Button>
          </Space>
        }
      >
        <div
          style={{ height: '100%', position: 'relative' }}
          className={styles['mount-resource-wrap']}
        >
          <Form
            name="mountForm"
            id="mountForm"
            className="container"
            style={{
              height: '100%',
              overflowY: 'auto',
            }}
            form={form}
          >
            <div style={{ display: 'flex', width: '100%' }}>
              <div
                style={{
                  width: '50%',
                  boxSizing: 'border-box',
                  marginRight: 16,
                }}
              >
                <Form.Item
                  label="资源类目"
                  name="mountCategory"
                  rules={[{ required: true, message: '请选择类目信息' }]}
                >
                  <SearchTreeSelect
                    loading={treeLoading}
                    treeData={cateData}
                    showSearch
                    allowClear
                    placeholder="请选择或搜索类目信息"
                    onSelect={onSelectTree}
                  />
                </Form.Item>
              </div>
              <div style={{ width: '49%' }}>
                <Form.Item
                  label="资源表"
                  name="table"
                  rules={[{ required: true, message: '请选择资源表' }]}
                >
                  {cateId ? (
                    <FetchSelect
                      placeholder="请选择或搜索资源表"
                      style={{ width: '100%' }}
                      showSearch
                      labelInValue
                      fetchParams={{ catalogdirid: cateId }}
                      dropdownClassName={styles.tableDropdown}
                      fetchOptions={fetchOptions}
                      tableMode
                      // @ts-ignore
                      getPopupContainer={() =>
                        document.getElementById('mountForm')
                      }
                      // @ts-ignore
                      onChange={(valueObj: {
                        value: string;
                        label: string;
                      }) => {
                        // 成功更换表 此时edges应该为【】
                        setIsChangeTable(true);
                        // 刷新FieldMap
                        setIsRefresh(!isRefresh);
                        // 请求资源表数据
                        handleSelectTable(valueObj?.value);
                        // 只要换了表  原来的信息项规则失效
                        setNeedEmpty(false);
                      }}
                    />
                  ) : (
                    <Select
                      placeholder="请选择或搜索资源表"
                      style={{ width: '100%' }}
                      notFoundContent={
                        <div>
                          <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description="请先选择资源类目"
                          />
                        </div>
                      }
                    />
                  )}
                </Form.Item>
              </div>
            </div>
            <br />

            <Row>
              <Col span={24}>
                <Spin
                  style={{ width: '100%', height: '100%' }}
                  spinning={!!mountTableLoading}
                >
                  <Form.Item
                    shouldUpdate={(pre, cur) => {
                      // * table 栏位 配合FetchSelect 调整了数据结构   string => {value:string,label:string}
                      return pre?.table?.value !== cur?.table?.value;
                    }}
                  >
                    {({ getFieldValue }) => {
                      return (
                        getFieldValue('table') && (
                          <FieldMap
                            ref={fieldMapRef}
                            showButton
                            loading={
                              mountTableLoading || false || mountInfoLoading
                            }
                            sameNameConfig={{
                              tooltip: {
                                title: '根据信息项名称和字段注释信息匹配关联',
                              },
                              name: ['', 'comment'],
                            }}
                            leftDataSource={
                              leftDataSource
                                ? leftDataSource.map((item: any) => {
                                    return {
                                      fieldName: item.infoitemsname,
                                      fieldType: item.infoitemstype,
                                      ...item,
                                    };
                                  })
                                : []
                            }
                            rightDataSource={rightDataSource}
                            scrollEl={
                              document.querySelectorAll('.container')[0]
                            }
                            isEdit={needEmpty}
                            // error={errMsg}
                            tableRelation={
                              editTableRelationData && !isChangeTable
                                ? {
                                    edges:
                                      editTableRelationData?.tableRelation
                                        ?.edges,
                                    ...(editTableRelationData.isSort && {
                                      sortRules: {
                                        left: getOrderRules(
                                          'left',
                                          editTableRelationData?.tableRelation,
                                        ),
                                        right: getOrderRules(
                                          'right',
                                          editTableRelationData?.tableRelation,
                                        ),
                                      },
                                    }),
                                  }
                                : {
                                    edges: [],
                                  }
                            }
                            // tableRelation={{
                            //   edges: mapEdges,
                            //   ...(editTableRelationData?.isSort && {
                            //     sortRules: {
                            //       left: getOrderRules(
                            //         'left',
                            //         editTableRelationData?.tableRelation,
                            //       ),
                            //       right: getOrderRules(
                            //         'right',
                            //         editTableRelationData?.tableRelation,
                            //       ),
                            //     },
                            //   }),
                            // }}
                            isMount
                            // operateEdges={operateEdges}
                            isRefresh={isRefresh}
                            columns={{
                              left: [
                                {
                                  title: '序号',
                                  dataIndex: 'number',
                                  render: (val) => {
                                    return (
                                      <div style={{ width: 40 }}>
                                        {Number(val)}
                                      </div>
                                    );
                                  },
                                },
                                {
                                  title: '字段名称',
                                  dataIndex: 'infoitemsname',
                                  width: 106,
                                  render: (text) => {
                                    return (
                                      <div
                                        style={{
                                          width: '106px',
                                          ...ellipsisStyle,
                                        }}
                                      >
                                        <Tooltip title={text}>{text}</Tooltip>
                                      </div>
                                    );
                                  },
                                },
                                {
                                  title: '类型',
                                  dataIndex: 'infoitemstype',
                                  render: (text) => {
                                    return (
                                      <div style={{ width: '106px' }}>
                                        <Tooltip title={text}>
                                          {text.length > 11
                                            ? `${text.substr(0, 7)}...`
                                            : text}
                                        </Tooltip>
                                      </div>
                                    );
                                  },
                                },
                              ],
                              right: [
                                {
                                  title: '序号',
                                  dataIndex: 'number',
                                  render: (val) => {
                                    return (
                                      <div style={{ width: '40px' }}>
                                        {Number(val)}
                                      </div>
                                    );
                                  },
                                },
                                {
                                  dataIndex: 'name',
                                  title: '字段英文名称',
                                  render: (val, record) => {
                                    const {
                                      section,
                                      isunique,
                                      isprim,
                                      ranknum,
                                      rankname,
                                      ispartition,
                                    } = record;
                                    const isIcon =
                                      section ||
                                      isprim ||
                                      isunique ||
                                      ranknum ||
                                      ispartition;
                                    return (
                                      <div
                                        style={{
                                          width: '140px',
                                          display: 'flex',
                                          overflow: 'hidden',
                                        }}
                                      >
                                        <div
                                          style={
                                            isIcon
                                              ? {
                                                  width: 60,
                                                  ...ellipsisStyle,
                                                }
                                              : {
                                                  width: '100%',
                                                  ...ellipsisStyle,
                                                }
                                          }
                                        >
                                          <Tooltip
                                            placement="topLeft"
                                            title={val}
                                          >
                                            {val}
                                          </Tooltip>
                                        </div>
                                        {isIcon ? (
                                          <Space
                                            size={4}
                                            style={{ marginLeft: 8 }}
                                          >
                                            {section && (
                                              <Tooltip
                                                title={`${transformToChinese(
                                                  section,
                                                )}级分区`}
                                              >
                                                <IconFont type="icon-partition" />
                                              </Tooltip>
                                            )}
                                            {isprim && (
                                              <Tooltip title="主键">
                                                <IconFont
                                                  type="icon-key"
                                                  style={{
                                                    color: colors.blue6,
                                                  }}
                                                />
                                              </Tooltip>
                                            )}
                                            {isunique && (
                                              <Tooltip title="唯一键">
                                                <IconFont
                                                  type="icon-uniqueKey"
                                                  style={{
                                                    color: colors.green6,
                                                  }}
                                                />
                                              </Tooltip>
                                            )}
                                            {ranknum !== 0 &&
                                              ranknum !== 99 && (
                                                <Tooltip title={rankname}>
                                                  <IconFont
                                                    type={`icon-V${ranknum}`}
                                                  />
                                                </Tooltip>
                                              )}
                                          </Space>
                                        ) : null}
                                      </div>
                                    );
                                  },
                                },
                                {
                                  dataIndex: 'fieldType',
                                  title: '字段类型',
                                  render: (text) => {
                                    return (
                                      <div style={{ width: '62px' }}>
                                        <Tooltip title={text}>
                                          {text.length > 10
                                            ? `${text.substr(0, 7)}...`
                                            : text}
                                        </Tooltip>
                                      </div>
                                    );
                                  },
                                },
                                {
                                  dataIndex: 'annotation',
                                  title: '字段注释',
                                  render: (text) => {
                                    const lineRex = /[\r\n]/g;
                                    const data = text.replace(lineRex, '');
                                    return (
                                      <div style={{ width: '160px' }}>
                                        <Tooltip title={text}>
                                          {data.length > 14
                                            ? `${data.substr(0, 12)}...`
                                            : data || '--'}
                                        </Tooltip>
                                      </div>
                                    );
                                  },
                                },
                                {
                                  dataIndex: 'rankName',
                                  title: '敏感等级',
                                  render: (val, record) => {
                                    const { ranknum } = record;
                                    return (
                                      <div style={{ width: 76 }}>
                                        <IconFont
                                          type={
                                            ranknum === 0 || ranknum === 99
                                              ? 'icon-noGrade'
                                              : `icon-V${ranknum}`
                                          }
                                          style={{
                                            marginRight: 8,
                                            color:
                                              ranknum === 99
                                                ? colors.blue6
                                                : ranknum === 0
                                                ? '#999'
                                                : 'inherit',
                                          }}
                                        />
                                        {val}
                                      </div>
                                    );
                                  },
                                },
                              ],
                            }}
                          />
                        )
                      );
                    }}
                  </Form.Item>
                </Spin>
              </Col>
            </Row>
          </Form>
        </div>
      </Drawer>
    </Spin>
  );
};

export default MountResource;
