import { useEffect, useState, useCallback, CSSProperties } from 'react';
import { IconFont, InputSearch, TextButton } from '@dana/ui';
// import { LayoutHelper as Layout, IconFont } from '@dana/ui';
import { useRequest } from '@/utils';
import { Tooltip, Space, Table } from 'antd';
import colors from '@/theme/color.less';
import transformToChinese from '@/pages/DataAssets/DBAssetsCenter/AssetsDetails/FieldInfo/utils';
import StandardDetailModal from '@/pages/MetadataManage/SpecsCenter/Standard/Modal/StandardDetailModal';
import CodetbDetailModal from '@/pages/MetadataManage/CodesAndStandards/CodesManagement/components/CodetbDetailModal';
import styles from './index.less';
// import Header from '../../../MainTable/TableHeader';
import { fakeResult, totalData } from './data';
import FlexContent from '../../../components/FlexContent';
import { filterData } from './util';

// const { InnerBox, StrechBox, Content } = Layout;

const HEADER_SEARCH_PLACEHOLDRE = '请输入字段名称/字段英文名称搜索';
const DEFAULT_ASSETINFO = {
  num: '',
  infoitemsname: 'string',
  infoitemstype: 'string',
};

const DEFAULT_FIELDINFO = {
  number: 1,
  name: '',
  annotation: '',
  fieldtype: '',
  isprim: false,
  isunique: false,
  ispartition: false,
  ranknum: 0,
  rankname: '',
};

type StringOrNum = string | number;
type FlexExtraContentType = {
  fieldNum: StringOrNum;
  highLevel: StringOrNum;
  middleLevel: StringOrNum;
  unRecognized: StringOrNum;
  nonSensitive: StringOrNum;
};

type ItemInfoType = {
  inventoryId: string;
  // isSeparated?: boolean;
  isAttached?: string; // * 是否是待挂载状态
  pageFrom?: 'Mount' | 'Audit';
  itemData?: DataSourceType[];
};

const ellipsisStyle: React.CSSProperties = {
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
};

// 表格数据类型
type DataSourceType = typeof totalData;
type DataSourceFake = typeof fakeResult;

const ItemInfo = (props: ItemInfoType) => {
  const { inventoryId, isAttached, pageFrom, itemData } = props;
  const [searchData, setSearchData] = useState<string>('');
  // 这俩是为了搜索及列表展示
  const [dataSource, setDataSource] = useState<DataSourceType>([]);
  const [tempDataSource, setTempDataSource] = useState<DataSourceType>([]);
  // 大数据量loading
  const [mountLoading, setMountLoading] = useState<boolean>(false);

  // 这俩是为了右上角的信息展示
  const [extraContent, setExtraContent] = useState<FlexExtraContentType>({
    fieldNum: 0,
    highLevel: 0,
    middleLevel: 0,
    unRecognized: 0,
    nonSensitive: 0,
  });

  const [modalProps, setModalProps] = useState<{
    visible: boolean;
    id?: string;
  }>({
    visible: false,
  });
  const [codetbModalProps, setCodetbModalProps] = useState<{
    visible: boolean;
    id?: string;
  }>({
    visible: false,
  });

  const goStandardPage = (type, id) => {
    if (type === 'standard') {
      setModalProps({
        visible: true,
        id,
      });
    } else {
      setCodetbModalProps({
        visible: true,
        id,
      });
    }
  };

  const closeModal = () => {
    setModalProps({
      visible: false,
    });
    setCodetbModalProps({
      visible: false,
    });
  };

  const tableColumns = [
    {
      title: '序号',
      key: 'num',
      dataIndex: 'num',
      width: 40,
      render: (val) => {
        return Number(val);
      },
    },
    {
      dataIndex: 'infoitemsname',
      key: 'infoitemsname',
      title: '字段名称',
      ellipsis: {
        showTitle: false,
      },
      render: (val) => {
        return <Tooltip title={val}>{val || '--'}</Tooltip>;
      },
    },
    {
      dataIndex: 'infoitemstype',
      key: 'infoitemstype',
      title: '类型',
      ellipsis: {
        showTitle: false,
      },
      render: (val) => {
        return <Tooltip title={val}>{val || '--'}</Tooltip>;
      },
    },
    {
      dataIndex: 'name',
      key: 'name',
      title: '字段英文名称',
      // ellipsis: true,
      skipCheckEmpty: true,
      render: (val, record) => {
        const { section, isunique, isprim, ranknum, rankname } = record;
        const isIcon = section || isprim || isunique || ranknum;
        return (
          <div style={{ display: 'flex' }}>
            <div style={{ maxWidth: 160, ...ellipsisStyle }}>
              <Tooltip title={val}>{val || '--'}</Tooltip>
            </div>
            {/* <div style={{ maxWidth: 160 }}>
              <Ellipsis lines={1} tooltip showCenter>
                {val || '--'}
              </Ellipsis>
            </div> */}
            {isIcon ? (
              <Space style={{ marginLeft: 8 }}>
                {section && (
                  <Tooltip title={`${transformToChinese(section)}级分区`}>
                    <IconFont type="icon-partition" />
                  </Tooltip>
                )}
                {isprim && (
                  <Tooltip title="主键">
                    <IconFont type="icon-key" style={{ color: colors.blue6 }} />
                  </Tooltip>
                )}
                {isunique && (
                  <Tooltip title="唯一键">
                    <IconFont
                      type="icon-uniqueKey"
                      style={{ color: colors.green6 }}
                    />
                  </Tooltip>
                )}
                {ranknum !== 0 && ranknum !== 99 && (
                  <Tooltip title={rankname}>
                    <IconFont type={`icon-V${ranknum}`} />
                  </Tooltip>
                )}
              </Space>
            ) : (
              ''
            )}
          </div>
        );
      },
    },
    {
      dataIndex: 'fieldtype',
      key: 'fieldtype',
      title: '字段类型',
      ellipsis: {
        showTitle: false,
      },
      render: (val) => {
        return <Tooltip title={val}>{val || '--'}</Tooltip>;
      },
    },
    {
      dataIndex: 'rankname',
      key: 'rankname',
      title: '敏感等级',
      skipCheckEmpty: true,
      render: (val, record) => {
        const { ranknum } = record;
        if (!val) return '--';
        return (
          <div>
            <IconFont
              type={
                ranknum === 0 || ranknum === 99
                  ? 'icon-noGrade'
                  : `icon-V${ranknum}`
              }
              style={{
                marginRight: 8,
                color:
                  ranknum === 99
                    ? colors.blue6
                    : ranknum === 0
                    ? '#999'
                    : 'inherit',
              }}
            />
            {val}
          </div>
        );
      },
    },
    {
      dataIndex: 'annotation',
      title: '字段注释',
      ellipsis: {
        showTitle: false,
      },
      render: (val) => {
        return <Tooltip title={val}>{val || '--'}</Tooltip>;
      },
    },
    {
      title: '关联标准字段名',
      ellipsis: true,
      dataIndex: 'standard',
      render: (_, record) => {
        console.info('record', record);
        const standardData = record?.customstandardinfo?.standards;
        // * array 备用 先写成一个
        const standard = standardData ? standardData[0] : null;
        return standard?.name ? (
          <TextButton onClick={() => goStandardPage('standard', standard?.id)}>
            {standard?.name}
          </TextButton>
        ) : (
          '--'
        );
      },
    },
    {
      title: '关联代码表',
      ellipsis: true,
      dataIndex: 'codeTb',
      render: (_, record) => {
        const codeData = record?.customstandardinfo?.codetables;
        const codeTb = codeData ? codeData[0] : null;
        // * array 备用 先写成一个
        return codeTb?.name ? (
          <TextButton onClick={() => goStandardPage('codeTb', codeTb?.id)}>
            {codeTb?.name}
          </TextButton>
        ) : (
          '--'
        );
      },
    },
  ];
  const getExtraContent = useCallback((fieldinfo) => {
    /*  const defaultLevelData = [
       { rankname: '非敏感', ranknum: 99, rankid: 'noGrade' },
       { rankname: '未识别', ranknum: 0, rankid: 'noGrade' },
     ];
     rankname 高等级 中等级 */

    const obj = {
      fieldNum: fieldinfo.length,
      highLevel: 0,
      middleLevel: 0,
      unRecognized: 0,
      nonSensitive: 0,
    };
    const result = fieldinfo.reduce((tol, val) => {
      const { ranknum, rankname } = val;
      if (ranknum === 0) {
        tol.unRecognized += 1;
      } else if (ranknum === 99) {
        tol.nonSensitive += 1;
      }
      if (rankname === '中等级') {
        tol.middleLevel += 1;
      } else if (rankname === '高等级') {
        tol.highLevel += 1;
      }
      return tol;
    }, obj);
    setExtraContent((prev) => ({
      ...prev,
      ...result,
    }));
  }, []);

  const { run: getList } = useRequest('metadata/moneycenter/tablefieldinfo', {
    onSuccess: (res) => {
      const dataSourceTemp = formatDatasource(res.result);
      setDataSource(dataSourceTemp);
      setTempDataSource(dataSourceTemp);
      if (
        Array.isArray(res.result.fieldinfo) &&
        res.result.fieldinfo.length > 0
      ) {
        getExtraContent(res.result.fieldinfo);
      }
      setMountLoading(false);
    },
    onError: () => {
      setMountLoading(false);
    },
  });

  // eslint-disable-next-line react/no-unstable-nested-components
  const ItemRight = () => {
    const wrapper: CSSProperties = { fontSize: '12px', color: '#333333' };
    const redStyle: CSSProperties = {
      color: '#EA1E1E',
    };
    const orangeStyle: CSSProperties = {
      color: '#FFC446',
    };
    const blueStyle: CSSProperties = {
      color: '#1980FF',
    };

    return (
      <Space style={wrapper}>
        {Number(extraContent.fieldNum) > 0 ? (
          <span>{`字段数：${extraContent.fieldNum}`}</span>
        ) : (
          ''
        )}
        {Number(extraContent.highLevel) > 0 ? (
          <span style={redStyle}>{`高等级：${extraContent.highLevel}`}</span>
        ) : (
          ''
        )}

        {Number(extraContent.middleLevel) > 0 ? (
          <span style={orangeStyle}>
            {`中等级：${extraContent.middleLevel}`}
          </span>
        ) : (
          ''
        )}

        {Number(extraContent.nonSensitive) > 0 ? (
          <span style={blueStyle}>
            {`非敏感：${extraContent.nonSensitive}`}
          </span>
        ) : (
          ''
        )}

        {Number(extraContent.unRecognized) > 0 ? (
          <span>{`未识别：${extraContent.unRecognized}`}</span>
        ) : (
          ''
        )}
      </Space>
    );
  };

  // const refreshList = () => {
  //   setMountLoading(true);
  //   initList();
  // };

  // 组装前端数据

  function formatDatasource(data: DataSourceFake) {
    const result: Array<any> = [];

    // assetinfo.length === fieldinfo.length ,且 partitioninfo 可能有可能没有
    const LEN = data.assetinfo.length || data.fieldinfo.length;

    for (let i = 0; i < LEN; i++) {
      const assetinfo = data.assetinfo ? data.assetinfo[i] : DEFAULT_ASSETINFO;
      const fieldinfo = data.fieldinfo ? data.fieldinfo[i] : DEFAULT_FIELDINFO;

      const tempObj = {
        ...assetinfo,
        ...fieldinfo,
      };
      result.push(tempObj);
    }
    return result;
  }
  function initList() {
    getList({
      isattach: true,
      inventoryid: inventoryId,
    });
  }
  useEffect(() => {
    // 需要前端从元数据里筛选
    if (searchData) {
      const tempResult = filterData(searchData, dataSource);
      setDataSource(tempResult);
    } else {
      // 将数据置为原始数据
      setDataSource(tempDataSource);
    }
  }, [searchData]);
  useEffect(() => {
    setMountLoading(true);

    if (!isAttached && pageFrom === 'Mount') {
      // @ts-ignore
      setDataSource(itemData);
      // @ts-ignore
      setTempDataSource(itemData);
      setMountLoading(false);
    } else {
      initList();
    }
  }, []);
  return (
    // 单独作为信息项模块的样式
    // <Layout>
    //   <Content style={{ padding: 0 }}>
    //     <InnerBox style={{ padding: 0 }}>
    //     <FlexContent flex title="信息项" right={ItemRight()} />
    //   <Header
    //     searchPlaceHolder={HEADER_SEARCH_PLACEHOLDRE}
    //     setSearchData={setSearchData}
    //     refreshList={refreshList}
    //   />
    //     </InnerBox>

    //     <StrechBox>
    //       {(height) => {
    //         return (
    //           <div style={{ margin: '16px 0' }}>
    //             <Table
    //               dataSource={dataSource}
    //               columns={tableColumns}
    //               loading={mountLoading}
    //               // style={{ height: height - 16 - 100 }}
    //               scroll={{ y: height - 64 - 100 }}
    //               pagination={false}
    //             />
    //           </div>
    //         );
    //       }}
    //     </StrechBox>
    //   </Content>
    // </Layout>

    // 被资产信息复用的样式
    <div className={styles.ItemWrapper}>
      <FlexContent
        flex
        title="数据字典"
        right={ItemRight()}
        className={styles.outerFlexContentWrapper}
      />
      <br />
      {/* <Header
        searchPlaceHolder={HEADER_SEARCH_PLACEHOLDRE}
        setSearchData={setSearchData}
        refreshList={refreshList}
      
      /> */}
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <InputSearch
          onSearch={(value) => {
            setSearchData(value);
          }}
          style={{ width: '280px' }}
          placeholder={HEADER_SEARCH_PLACEHOLDRE}
        />
      </div>

      <div style={{ margin: '16px 0' }}>
        <Table
          dataSource={dataSource}
          columns={tableColumns}
          loading={mountLoading}
          pagination={false}
        />
      </div>
      <StandardDetailModal modalProps={modalProps} onClose={closeModal} />
      <CodetbDetailModal modalProps={codetbModalProps} onClose={closeModal} />
    </div>
  );
};

export default ItemInfo;
