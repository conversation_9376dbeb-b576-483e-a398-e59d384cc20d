import React, { useLayoutEffect, useMemo, useState } from 'react';
import { Input, Switch } from 'antd';
import { TableForm, Ellipsis } from '@dana/ui';
import CS from 'classnames';
import ResizableObserver from 'rc-resize-observer';
import { Block } from '@/components/ContentBlock';
import styles from './index.less';

const ExportComp = (props) => {
  return (
    <Input
      {...props}
      placeholder="请输入导出表头名"
      className={CS({
        [styles.viewInput]: props.disabled,
      })}
    />
  );
};

const otherHeaderColumns = [
  '字段名称',
  '类型',
  '字段英文名称',
  '资产发布时间',
  '数据最近更新时间',
];
// 默认展示 资产名称、信息项、信息项类型、字段名称、共享方式、数据最近更新时间
const defaultShowItems = [
  'ZCMC',
  '字段名称',
  '类型',
  '字段英文名称',
  'GXFS',
  '数据最近更新时间',
];

export default function EditMode(props) {
  const { form, attributeInfo, canEdit, drawerType, detailList } = props;
  const isAdd = drawerType === 'add';
  const [scrollWidth, setScrollWidth] = useState(0);

  const addColumns = attributeInfo.concat(
    otherHeaderColumns.map((v) => ({
      proname: v,
      prosign: v,
    })),
  );
  const columnsData = isAdd ? addColumns : detailList.headername || [];

  const getDataSource = () => {
    return columnsData.map((v) => {
      // 资产名称默认展示，且不可修改
      const isDisabled = v.prosign === 'ZCMC' || v.oriheadersign === 'ZCMC';
      return {
        oriheadername: v.proname || v.oriheadername,
        exportheadername: v.proname || v.exportheadername,
        displayed: isAdd ? defaultShowItems.includes(v.prosign) : v.displayed,
        disabled: isDisabled,
        oriheadersign: v.prosign || v.oriheadersign,
      };
    });
  };

  const columns = [
    {
      dataIndex: 'oriheadername',
      title: '原表头名',
      width: 100,
      align: 'left',
      render(text) {
        return <Ellipsis lines={1}>{text}</Ellipsis>;
      },
    },
    {
      dataIndex: 'exportheadername',
      title: '导出表头名',
      width: 100,
      align: 'left',
      valueType: 'string',
      component: [ExportComp],
      disabled: (record) => record.disabled,
      rules: [
        {
          validator: (_, value) =>
            value
              ? Promise.resolve()
              : Promise.reject(new Error('请输入导出表头名')),
        },
      ],
    },
    {
      dataIndex: 'displayed',
      title: '是否显示',
      width: 100,
      align: 'left',
      valueType: 'boolean',
      component: [Switch],
      disabled: (record) => record.disabled,
    },
  ];
  const viewColumns = [
    {
      dataIndex: 'oriheadername',
      title: '原表头名',
      width: 100,
      align: 'left',
      render(text) {
        return <Ellipsis lines={1}>{text}</Ellipsis>;
      },
    },
    {
      dataIndex: 'exportheadername',
      title: '导出表头名',
      width: 100,
      align: 'left',
      valueType: 'string',
      render(text) {
        return <Ellipsis lines={1}>{text}</Ellipsis>;
      },
    },
    {
      dataIndex: 'displayed',
      title: '是否显示',
      width: 100,
      align: 'left',
      valueType: 'boolean',
      render(text) {
        return text ? '是' : '否';
      },
    },
  ];

  const initialValues = useMemo(() => {
    return getDataSource();
  }, [JSON.stringify(getDataSource())]);

  useLayoutEffect(() => {
    document.body.classList.add(styles.sortableTableRow);

    return () => {
      document.body.classList.remove(styles.sortableTableRow);
    };
  }, []);

  return (
    <Block title="配置表头">
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: 520,
          width: '100%',
        }}
      >
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <TableForm.Form
            form={form}
            // onValuesChange={(values) => {
            //   console.log('chagnge', values);
            // }}
          >
            {() => (
              <ResizableObserver
                onResize={(res) => {
                  const { offsetWidth } = res;
                  setScrollWidth(offsetWidth - 10);
                }}
              >
                <div style={{ flex: 1 }}>
                  {canEdit ? (
                    <TableForm
                      scroll={{ x: scrollWidth, y: 520 }}
                      selectable={false}
                      hasIndex
                      initialValues={initialValues}
                      form={props.form}
                      operators={null}
                      columns={columns}
                    />
                  ) : (
                    <TableForm
                      scroll={{ x: scrollWidth, y: 520 }}
                      selectable={false}
                      sortable={false}
                      hasIndex
                      initialValues={initialValues}
                      form={props.form}
                      operators={null}
                      columns={viewColumns}
                    />
                  )}
                </div>
              </ResizableObserver>
            )}
          </TableForm.Form>
        </div>
      </div>
    </Block>
  );
}
