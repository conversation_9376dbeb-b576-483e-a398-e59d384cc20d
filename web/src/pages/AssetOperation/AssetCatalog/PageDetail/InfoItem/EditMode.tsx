import { useState, useMemo, useLayoutEffect, useEffect } from 'react';
import { Input, Select, Tooltip, Divider } from 'antd';
import { TableForm, getStandardRules } from '@dana/ui';
import { useModel } from 'umi';
import { IconFont } from '@/components';
import ResizableObserver from 'rc-resize-observer';

import { ALL_SPACE_REG } from '@/utils';

export default function EditMode(props) {
  const { infoItemTypeOptions, setAttachTableInfo } = useModel(
    'AssetOperation.assetCatalog',
  );
  const { dataSource, length, disabled, detailList } = props;
  const [scrollWidth, setScrollWidth] = useState(0);

  useLayoutEffect(() => {
    if (document.querySelector('#tableForm_fix .ant-table-cell-scrollbar')) {
      const cols = document.querySelectorAll('#tableForm_fix col');
      const arr = Array.from(cols);
      if (arr.length > 2) {
        arr[arr.length - 1].style.display = 'none';
        arr[arr.length - 2].style.width = '104px';
      }
    }
  });

  useEffect(() => {
    if (detailList.attachinfo) {
      setAttachTableInfo(detailList.attachinfo.attachtableinfo);
    } else {
      setAttachTableInfo({});
    }
  }, [detailList]);

  const columns = [
    {
      dataIndex: 'infoitemsname',
      title: '字段名称',
      width: 100,
      align: 'left',
      component: [
        Input,
        {
          placeholder: '请输入字段名称',
          disabled,
        },
      ],
      valueType: 'string',
      deduplicate: '名称已存在, 请重命名',
      rules: getStandardRules(
        '请输入字段名称',
        [],
        [ALL_SPACE_REG.message, ALL_SPACE_REG.patten],
      ),
    },
    {
      dataIndex: 'infoitemstype',
      title: '类型',
      width: 100,
      align: 'left',
      valueType: 'string',
      component: [
        Select,
        {
          options: infoItemTypeOptions.map((item) => {
            return { label: item, value: item, key: item };
          }),
          disabled,
        },
      ],
    },
  ];

  const initialValues = useMemo(() => {
    return dataSource;
  }, [dataSource]);

  return (
    <div
      style={{
        height: 'calc(100% - 48px)',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <ResizableObserver
        onResize={(res) => {
          const { offsetWidth } = res;
          // console.log(offsetWidth, offsetHeight)
          setScrollWidth(offsetWidth - 10);
        }}
      >
        <div
          id="tableForm_fix"
          style={{ flex: '1', display: 'flex', flexDirection: 'column' }}
        >
          <TableForm
            // scroll={{ x: scrollWidth, y: props.height - 112 }}
            scroll={{ x: scrollWidth, y: 480 }}
            hasIndex
            selectable
            rowSelection={{
              onChange(selectedData) {
                // console.log('table form selected', selectedData)
                props.onSelectTable(selectedData);
              },
            }}
            sortable={false}
            initialValues={initialValues}
            form={props.form}
            columns={columns}
            operators={(record, index, { remove, moveUp, moveDown }) => {
              return (
                <div>
                  <IconFont
                    type="icon-arrow-up"
                    disabled={index === 0 || disabled}
                    style={{ fontSize: 16, color: '#1980FF' }}
                    onClick={() => moveUp(index)}
                  />
                  <Divider type="vertical" />
                  <IconFont
                    type="icon-arrow-down"
                    disabled={index === length - 1 || disabled}
                    style={{ fontSize: 16, color: '#1980FF' }}
                    onClick={() => moveDown(index)}
                  />
                  <Divider type="vertical" />
                  <Tooltip title={length === 1 ? '至少保留一条字段' : ''}>
                    <IconFont
                      type="icon-delete"
                      style={{ fontSize: 16, color: '#1980FF' }}
                      disabled={length === 1 || disabled}
                      onClick={() => remove(index)}
                    />
                  </Tooltip>
                </div>
              );
            }}
          />
        </div>
      </ResizableObserver>
    </div>
  );
}
