import * as React from 'react';
import {
  TableForm,
  getStandardRules,
  Ellipsis,
  TableProvider,
  useTableSearch,
} from '@dana/ui';
import { Block } from '@/components/ContentBlock';

import { ALL_SPACE_REG } from '@/utils';
import styles from '../index.less';

const { Form } = TableForm;

interface ViewModeProps {
  data: any[];
  isAudit?: boolean;
}
export default function ViewMode(props: ViewModeProps) {
  const { data, isAudit, ...otherProps } = props;

  const [searchedDataSource, searchEl] = useTableSearch(
    data,
    ['infoitemsname'],
    {
      placeholder: '请输入字段名称搜索',
    },
  );
  const initialValues = React.useMemo(() => {
    return searchedDataSource;
  }, [searchedDataSource]);

  const columns = [
    {
      dataIndex: 'infoitemsname',
      title: '字段名称',
      width: 100,
      align: 'left',
      render: (text, record) => {
        const isChanged = isAudit && record?.ischange;
        return (
          <div style={{ width: isChanged ? 'calc(100% - 48px)' : '100%' }}>
            <Ellipsis
              tooltip
              isResize
              isAuto
              lines={1}
              style={{ display: 'inline-block' }} // 加上好像可以修复偶现长度超出问题
            >
              {text}
            </Ellipsis>
            {isChanged && <span style={{ color: '#08B156' }}>（变更）</span>}
          </div>
        );
      },
      valueType: 'string',
      deduplicate: '名称已存在, 请重命名',
      rules: getStandardRules(
        '请输入字段名称',
        [],
        [ALL_SPACE_REG.message, ALL_SPACE_REG.patten],
      ),
    },
    {
      dataIndex: 'infoitemstype',
      title: '类型',
      width: 100,
      align: 'left',
    },
  ];

  return (
    <Block className={styles.infoItem} title="数据字典" {...otherProps}>
      <TableProvider>
        <Form>
          {() => (
            <>
              {!isAudit && <div className={styles.bar}>{searchEl}</div>}
              <TableForm
                scroll={{ y: 480 }}
                hasIndex
                selectable={false}
                sortable={false}
                initialValues={initialValues}
                // form={form}
                columns={columns}
                operators={null}
              />
            </>
          )}
        </Form>
      </TableProvider>
    </Block>
  );
}
