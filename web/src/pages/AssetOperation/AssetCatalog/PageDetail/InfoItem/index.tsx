import React, { useEffect, useState, useRef } from 'react';
import { Button, Space } from 'antd';
import { useSelector, useModel } from 'umi';
import { ConnectState } from '@/models/connect.d';
import { TableForm } from '@dana/ui';

import { Block } from '@/components/ContentBlock';

import ImportModal from './ImportModal';
import OperableMode from './EditMode';
import ViewMode from './ViewMode';
import styles from '../index.less';

const { Form } = TableForm;

const Bar = (props) => {
  const { form, disabled, selectedData } = props;
  const canDel = selectedData.length > 0;

  return (
    <div className={styles.bar}>
      <Space>
        <Button
          type="primary"
          onClick={() => {
            props.addItem({ infoitemsname: '', infoitemstype: '字符型' });
          }}
          disabled={disabled}
        >
          新建
        </Button>
        <Button onClick={() => props.setModalVisible(true)} disabled={disabled}>
          元数据导入
        </Button>
        <Button
          danger
          onClick={() => form.removeSelected()}
          disabled={disabled || !canDel}
        >
          删除
        </Button>
      </Space>
    </div>
  );
};

const InfoItem = (props) => {
  const { detailType, setCanLeave } = useModel('AssetOperation.assetCatalog');
  const {
    detailList,
    detailList: {
      assetinfo,
      attachinfo: { attachstatus },
    },
  } = useSelector((state: ConnectState) => state.assetCatalog);

  const disabled =
    detailType === 'change' &&
    (attachstatus === '已挂载' || attachstatus.includes('审核中'));

  const initData = [
    { infoitemsname: '', infoitemstype: '字符型' },
    { infoitemsname: '', infoitemstype: '字符型' },
    { infoitemsname: '', infoitemstype: '字符型' },
  ];
  const dataSourceRef = useRef(initData);
  const showRef = useRef(false);
  const pushRef = useRef<(v: any) => void>();

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedData, setSelectedData] = useState([]);
  const [length, setLength] = useState(1);

  useEffect(() => {
    const data = detailType === 'add' ? initData : assetinfo;
    dataSourceRef.current = data;
    setLength(data.length);
  }, [detailType]);

  useEffect(() => {
    if (length === 0) {
      pushRef.current?.({ infoitemsname: '', infoitemstype: '字符型' });
    }
  }, [length]);

  useEffect(() => {
    showRef.current = props.show;
  }, [props.show]);

  const loading = useSelector((state: ConnectState) => state.loading);
  const detailLoading =
    loading.effects['metaDevelopTable/getTableDetail'] || false;

  const afterImportData = (importData) => {
    const data = importData.filter((item) => !!item.infoitemsname);
    dataSourceRef.current = data;
    props.form.resetFields();
    props.form.validateFields();
  };

  const onSelectTable = (data) => {
    setSelectedData(data);
  };

  const display = props.show ? 'block' : 'none';
  const isView = detailType === 'view';

  if (detailLoading) return null;

  return (
    <div style={{ display, flex: 1, height: 480 }}>
      {isView ? (
        <ViewMode data={assetinfo} />
      ) : (
        <Block className={styles.infoItem} title="数据字典">
          <Form
            form={props.form}
            onValuesChange={(values) => {
              setLength(values.length);
              const valuesName = values.map((item) => item.infoitemsname);
              const valuesType = values.map((item) => item.infoitemstype);
              const valuesInfo = valuesName.concat(valuesType).join(',');
              const dataSourceName = dataSourceRef.current.map(
                (item) => item.infoitemsname,
              );
              const dataSourceType = dataSourceRef.current.map(
                (item) => item.infoitemstype,
              );
              const dataSourceInfo = dataSourceName
                .concat(dataSourceType)
                .join(',');
              if (valuesInfo !== dataSourceInfo && showRef.current) {
                setCanLeave(false);
              }
            }}
          >
            {({ push }) => {
              pushRef.current = push;
              return (
                <>
                  <Bar
                    form={props.form}
                    addItem={push}
                    setModalVisible={setModalVisible}
                    disabled={disabled}
                    selectedData={selectedData}
                  />
                  <OperableMode
                    form={props.form}
                    dataSource={dataSourceRef.current}
                    length={length}
                    disabled={disabled}
                    detailList={detailList}
                    onSelectTable={onSelectTable}
                    height={props.height}
                  />
                </>
              );
            }}
          </Form>
          <ImportModal
            setModalVisible={setModalVisible}
            visible={modalVisible}
            afterImportData={afterImportData}
          />
        </Block>
      )}
    </div>
  );
};

export default InfoItem;
