import {
  Table,
  Form,
  DatePicker,
  Input,
  InputNumber,
  Button,
  notification,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState, useCallback, useEffect } from 'react';
import { disableOr } from '@/pages/DataService/ServiceManage/utils';
import { RadioButton, Filter } from '@dana/ui';
import {
  NUMBER_TYPE,
  STRING_TYPE,
  TIME_TYPE,
} from '@/pages/DataService/ServiceManage/conf';
import { useRequest } from '@/utils';
import { useSelector, useDispatch } from 'umi';
import useSourceDataSqlTest from '@/pages/AssetOperation/model/useSourceDataSqlTest';
import styles from './index.less';
import SourceTableForm from './SourceTableForm';
import AceEditor from './AceEditor';
import { getFilterParams } from './utils';

type DataType = {
  key: React.Key;
  name: string;
  field: string;
};

const { Item } = Form;
const { RangePicker } = DatePicker;
const customFormat = 'YYYY-MM-DD HH:mm:ss';

const SetDataSource = (props) => {
  const { sourceFilterForm } = props;
  const { commonset } = useSelector((state: any) => state.dataIndexOnlineModel);
  const { inventoryset } = commonset;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [fieldTableDatas, setFieldTableDatas] = useState<any>([]);
  // 当前选中的资产id
  const [currentRowRecord, setCurrentRowRecord] = useState<string | null>(null);

  const { sqlTestLoading, sqlTestNet } = useSourceDataSqlTest();

  const dispatch = useDispatch();
  const save = (payload) => {
    dispatch({
      type: 'dataIndexOnlineModel/save',
      payload,
    });
  };
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    // 全取消时，保留第一行数据选中
    if (!newSelectedRowKeys.length) {
      setSelectedRowKeys([fieldTableDatas[0].infoitemsname]);
    } else {
      setSelectedRowKeys([...newSelectedRowKeys]);
    }
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record) => {
      return {
        disabled:
          selectedRowKeys.length === 1 &&
          selectedRowKeys[0] === record.infoitemsname,
      };
    },
  };
  async function updateFilterData() {
    const tempArr: any = [];
    selectedRowKeys.forEach((item) => {
      const findItem = fieldTableDatas.find((it) => it.infoitemsname === item);
      if (findItem) {
        tempArr.push(findItem);
      }
    });

    const { filters: filtersData = [] } =
      await sourceFilterForm.getFieldsValue();
    if (!tempArr.length) return;
    // 如果取消的筛选字段，在数据筛选中已选择，则需将其清空去掉
    filtersData.forEach((item, index) => {
      if (!item) {
        filtersData[index] = {
          condition: undefined,
          name: undefined,
          value: undefined,
        };
      } else {
        const find = tempArr.find((it) => it.originname === item.name);
        if (!find) {
          filtersData[index] = {
            condition: undefined,
            name: undefined,
            value: undefined,
          };
        }
      }
    });
    sourceFilterForm.setFieldsValue({
      filters: Filter.formValuesToRes(filtersData || [], {
        format: customFormat,
      }),
    });
    filterChange();
  }
  useEffect(() => {
    if (fieldTableDatas.length) {
      updateFilterData();
    }
    const findIndex = inventoryset.zcarray.findIndex(
      (item) => item.inventoryid === currentRowRecord,
    );
    if (findIndex !== -1) {
      inventoryset.zcarray[findIndex].infoitemsnames = selectedRowKeys;
      save({ commonset: { ...commonset, inventoryset: { ...inventoryset } } });
    }
  }, [selectedRowKeys, fieldTableDatas]);

  function filterChange() {
    // 用计时器的目的，因为在筛选模式切换时，getFieldsValue获取不到表单的值，需等表单渲染完成后才行
    setTimeout(async () => {
      const {
        filters = [],
        querypreview,
        filterModel,
      } = await sourceFilterForm.getFieldsValue();
      const findIndex = inventoryset.zcarray.findIndex(
        (item) => item.inventoryid === currentRowRecord,
      );

      if (findIndex !== -1) {
        let tempQuerypreview = {};
        if (filterModel === 1) {
          tempQuerypreview = {
            queryfilter: getFilterParams(filters.filter((v) => v) || []),
            filtertype: querypreview?.filtertype,
            filtercustom: '',
          };
        } else {
          tempQuerypreview = {
            queryfilter: [],
            filtercustom: querypreview?.filtercustom,
            filtertype: 'or',
          };
        }
        inventoryset.zcarray[findIndex].querypreview = tempQuerypreview;
        save({
          commonset: {
            ...commonset,
            inventoryset: { ...inventoryset },
          },
        });
      }
    }, 100);
  }
  const changeFiltersField = useCallback(() => {
    const filtertype = ['querypreview', 'filtertype'];
    const disabled = disableOr(sourceFilterForm.getFieldValue);
    const isOr = sourceFilterForm.getFieldValue(filtertype) === 'or';
    if (disabled && isOr) {
      sourceFilterForm.setFieldsValue({ querypreview: { filtertype: 'and' } });
    }
  }, []);

  const getFiltersFields = (fields) => {
    const getAbstractType = (type) => {
      const format = type.toUpperCase();
      if (NUMBER_TYPE.includes(format)) return 'number';
      if (STRING_TYPE.includes(format)) return 'string';
      if (TIME_TYPE.includes(format)) return 'time';
      return type;
    };
    return fields.map((item) => {
      return {
        label: <span>{`${item.fieldInfoName}（${item.fieldName}）`}</span>,
        labelPlainText: `${item.fieldInfoName}${item.fieldName}`,
        name: item.fieldName,
        type: getAbstractType(item.fieldType),
      };
    });
  };
  const getTransformFields = (datas) => {
    if (!datas) return [];
    return getFiltersFields(
      datas.map(({ originname, infoitemsname, infoitemstype }) => ({
        fieldName: originname,
        fieldInfoName: infoitemsname,
        fieldType: infoitemstype,
      })),
    );
  };
  const columns: ColumnsType<DataType> = [
    {
      title: '字段名称',
      dataIndex: 'infoitemsname',
      ellipsis: true,
      width: 300,
      render: (text) => {
        return text || '--';
      },
    },
    {
      title: '字段英文名称',
      dataIndex: 'originname',
      width: 200,
      ellipsis: true,
    },
  ];
  const { loading, run: getTableDetail } = useRequest(
    'metadata/moneycenter/tablefieldinfo',
    {
      onSuccess: (res) => {
        const { assetinfo, fieldinfo } = res.result;
        const fieldinfoData: any = [];
        assetinfo.forEach((item: any, index) => {
          const { infoitemsname } = item;
          const { fieldtype, name } = fieldinfo[index];
          fieldinfoData.push({
            originname: name,
            infoitemsname,
            infoitemstype: fieldtype,
          });
        });

        setFieldTableDatas([...fieldinfoData]);
      },
    },
  );
  const onClickRow = (asset) => {
    const { source, infoitemsnames, querypreview } = asset;
    getTableDetail({ inventoryid: source.inventoryid, isattach: true });
    setSelectedRowKeys(infoitemsnames);

    const { queryfilter, filtercustom, filtertype } = querypreview || {};
    const mapQueryfilter = queryfilter.map((item) => {
      const { fieldname, option, value1, value2 } = item;
      let value = value1;
      if (value1 && value2) {
        value = [value1, value2];
      }
      return { name: fieldname, condition: option, value };
    });

    sourceFilterForm.setFieldsValue({
      querypreview: { filtertype, filtercustom },
      filters: Filter.formValuesToRes(mapQueryfilter || [], {
        format: customFormat,
      }),
      filterModel: filtercustom ? 2 : 1,
    });
  };
  const filtercustomStr = ['querypreview', 'filtercustom'];
  const onClickSqlTest = async (sql) => {
    const findItem = inventoryset.zcarray.find(
      (item) => item.inventoryid === currentRowRecord,
    );
    const { code, checkres } = await sqlTestNet({
      wherestr: sql,
      inventoryid: findItem.inventoryid,
    });
    if (code === 200 && checkres) {
      notification.success({
        message: 'where语句检测通过',
      });
    } else {
      notification.warning({
        message: 'where语句不合法',
      });
    }
  };

  return (
    <>
      <SourceTableForm
        {...props}
        onClickRow={onClickRow}
        currentRowRecord={currentRowRecord}
        setCurrentRowRecord={setCurrentRowRecord}
      />
      {currentRowRecord && (
        <>
          {/* 展示字段设置 */}
          <div className={styles.showFieldSet}>
            <div className={styles.head}>
              <span>展示字段设置</span>
              {selectedRowKeys.length === 1 && (
                <span className={styles.tip}>至少保留一个展示字段</span>
              )}
            </div>
            <Table
              loading={loading}
              style={{ width: '100%' }}
              columns={columns}
              rowSelection={rowSelection}
              dataSource={fieldTableDatas}
              rowKey="infoitemsname"
              pagination={false}
              // 超过10行数据展示滚动条
              scroll={{
                x: true,
                y: !fieldTableDatas.length
                  ? 200
                  : fieldTableDatas.length >= 10
                  ? 10 * 48
                  : fieldTableDatas.length * 48,
              }}
            />
          </div>

          {/* 展示数据筛选设置 */}
          <div className={styles.showFieldSet}>
            <div className={styles.head}>
              <span>展示数据范围设置</span>
            </div>
            <Form form={sourceFilterForm} onValuesChange={filterChange}>
              <Item label="数据筛选" name="filterModel" initialValue={1}>
                <RadioButton
                  options={[
                    { label: '可视化模式', value: 1 },
                    { label: 'SQL模式', value: 2 },
                  ]}
                />
              </Item>
              <Item dependencies={['filterModel']} noStyle>
                {({ getFieldValue }) => {
                  if (getFieldValue('filterModel') === 1) {
                    return (
                      <>
                        <Item
                          name="filter"
                          initialValue={{
                            name: 'date',
                            filters: Filter.resToFormValues([], {
                              format: customFormat,
                            }),
                          }}
                          style={{ marginBottom: 0 }}
                        >
                          <Filter
                            fields={getTransformFields(fieldTableDatas)}
                            mode="multiple"
                            config={{
                              left: {
                                style: { width: 240 },
                                onChange: changeFiltersField,
                              },
                              center: {
                                style: { width: 120 },
                              },
                              right: {
                                style: { width: 240 },
                                stringCustomComponent: (
                                  <Input placeholder="请输入筛选内容" />
                                ),
                                numberCustomComponent: (
                                  <InputNumber placeholder="请输入筛选内容" />
                                ),
                                rangeCustomComponent: {
                                  rangePicker: (
                                    <RangePicker
                                      showTime
                                      format={customFormat}
                                    />
                                  ),
                                },
                              },
                            }}
                          />
                        </Item>
                        <Item
                          label="条件逻辑"
                          name={['querypreview', 'filtertype']}
                          initialValue="or"
                        >
                          <RadioButton
                            options={[
                              {
                                label: '满足任一条件',
                                value: 'or',
                                disabled: disableOr(
                                  sourceFilterForm.getFieldValue,
                                ),
                                ...(disableOr(
                                  sourceFilterForm.getFieldValue,
                                ) && {
                                  toolTip: {
                                    title: (
                                      <>
                                        <div>满足任一条件：</div>
                                        <div>只限于筛选的字段为同一字段</div>
                                      </>
                                    ),
                                  },
                                }),
                              },
                              {
                                label: '满足所有条件',
                                value: 'and',
                              },
                            ]}
                          />
                        </Item>
                      </>
                    );
                  } else {
                    return (
                      <div
                        id="fieldSqlEditor"
                        className={styles.fieldSqlEditor}
                      >
                        <Item name={filtercustomStr}>
                          <AceEditor
                            style={{
                              width: '100%',
                              height: '200px',
                            }}
                          />
                        </Item>
                        <Item noStyle dependencies={filtercustomStr}>
                          {() => {
                            return (
                              <Button
                                style={{ marginTop: '12px' }}
                                onClick={() =>
                                  onClickSqlTest(getFieldValue(filtercustomStr))
                                }
                                disabled={!getFieldValue(filtercustomStr)}
                                loading={sqlTestLoading}
                              >
                                SQL检测
                              </Button>
                            );
                          }}
                        </Item>
                      </div>
                    );
                  }
                }}
              </Item>
            </Form>
          </div>
        </>
      )}
    </>
  );
};

export default SetDataSource;
