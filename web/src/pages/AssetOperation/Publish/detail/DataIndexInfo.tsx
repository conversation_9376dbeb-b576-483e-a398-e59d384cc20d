import { useModel } from 'umi';
import { useEffect, useState } from 'react';
import { Block } from '@/components/ContentBlock';
import { Ellipsis, Table, Filter } from '@dana/ui';
import { Image, Checkbox, Spin } from 'antd';
import TitleWrapper from '@/pages/AssetOperation/Publish/components/TitleWrapper';
import error from '@/pages/Portal/DataIndex/images/error.png';
import { str } from '@/pages/DataService/ServiceManage/utils';
import useChartData from '@/pages/AssetOperation/model/useChartData';
import TableList from '@/pages/AssetOperation/Publish/components/DataIndexOnline/TableList';
import AceEditor from '@/pages/AssetOperation/Publish/components/DataIndexOnline/AceEditor';
import { useRequest } from '@/utils';
import PreviewItem from '../components/DataIndexOnline/PreviewItem';
import { getIcon } from '../components/DataIndexOnline/utils';
import styles from './DataIndexInfo.less';
import {
  dayOptions,
  warningRuleOptions,
  chartSortOptions,
  chartTypeOptions,
  mergeOptions,
} from '../components/DataIndexOnline/data';
import DataIndexChart from '../components/DataIndexOnline/DataIndexChart';

const customFormat = 'YYYY-MM-DD HH:mm:ss';

const DataIndexInfo = () => {
  const { data: detailData, loading } = useModel(
    'Portal.DataIndex.dataIndexDetail',
  );

  const {
    zbdetail,
    putinfo: { putmetricsinfo },
    inventoryname,
    inventoryid,
  } = detailData!;
  const {
    hidecomma,
    ispreview,
    mpicture,
    warningset,
    compareset,
    commonset,
    customset,
    markset,
    querypreview,
  } = putmetricsinfo;
  const {
    metricsvaluev1,
    metricsvaluev999,
    zbunit,
    fieldset,
    hascomparevalue,
    comparevalue,
  } = zbdetail;
  const zbValue = metricsvaluev999 || metricsvaluev1;
  const itemValue = {
    name: inventoryname,
    value: zbValue,
    unit: zbunit,
    hidecomma,
    icon: getIcon(mpicture),
    isShowNew: markset.clickset,
    isShowWaring: warningset.clickset,
    isShowCompare: compareset.clickset,
    isShowValue: ispreview && !!zbValue,
    compareDesc: compareset.comparemessage,
    compareValue: comparevalue,
    isShowCompareValue: hascomparevalue,
    comparesymbol: compareset.comparesymbol,
    warningTip: warningset.warningmessage,
  };
  const { xcompareset, inventoryset, chartset } = commonset;
  const {
    chartsort,
    charttype,
    clickset,
    sortfield,
    chartwd,
    chartgroup,
    chartdl,
    querypreview: chartQuerypreview,
  } = chartset;
  const getChartSort = () => {
    return chartSortOptions.find((item) => item.value === chartsort)?.label;
  };
  const getChartType = () => {
    return chartTypeOptions.find((item) => item.value === charttype)?.label;
  };
  const getChartTransacttype = (type) => {
    return mergeOptions.find((item) => item.value === type)?.label;
  };

  // 指标列是字符型指标展示图表
  const isChartType = () => {
    const metricsrow = fieldset.find((item) => item.ismetricsrow);
    return !metricsrow || str.includes(metricsrow.fieldtype);
  };

  const {
    chartOptionData,
    isGroup,
    getChartDatas,
    tableListData,
    getChartDataLoading,
  } = useChartData();
  const getChartDataNet = () => {
    const commonParams = {
      inventoryid,
      chartset,
      querypreview: chartQuerypreview,
    };
    if (charttype === 4) {
      getChartDatas({ ...commonParams, needbtable: true });
    } else {
      const grow = charttype === 3 ? '' : chartgroup;
      getChartDatas({
        ...commonParams,
        queryterm: { xrow: chartwd, yrow: chartdl, grow },
        needquery: true,
      });
    }
  };
  useEffect(() => {
    if (clickset && !isChartType()) {
      getChartDataNet();
    }
  }, [detailData]);

  const xcompareColumns = [
    {
      title: '序号',
      width: '50px',
      render(text, record, index) {
        return index + 1;
      },
    },
    {
      title: '指标名称',
      dataIndex: 'zbname',
      key: 'zbname',
      ellipsis: true,
    },
    {
      title: '指标数值',
      dataIndex: 'zbvalue',
      key: 'zbvalue',
    },
    {
      title: '指标图标',
      dataIndex: 'zbpicture',
      key: 'zbpicture',
      width: '100px',
      render(text, record) {
        return (
          <Image
            width={28}
            height={28}
            src={getIcon(record.zbpicture) || error}
            fallback={error}
            preview={false}
          />
        );
      },
    },
  ];
  const inventoryColumns = [
    {
      title: '资产类目',
      dataIndex: 'categoryname',
      key: 'categoryname',
      ellipsis: true,
    },
    {
      title: '资产名称',
      dataIndex: 'inventoryname',
      key: 'inventoryname',
      ellipsis: true,
    },
    {
      key: 'putstatus',
      dataIndex: 'putstatus',
      title: '上架状态',
      align: 'left',
      width: 100,
    },
    {
      key: 'sharedtype',
      dataIndex: 'sharedtype',
      title: '共享类型',
      align: 'left',
      width: 100,
    },
    {
      key: 'opentype',
      dataIndex: 'opentype',
      title: '开放类型',
      align: 'left',
      width: 100,
    },
  ];
  const filtersData = (queryfilter) => {
    const mapQueryfilter = queryfilter.map((item) => {
      const { fieldname, option, value1, value2 } = item;
      let value = value1;
      if (value1 && value2) {
        value = [value1, value2];
      }
      const find = fieldset.find(
        (it) => it.orifieldname === fieldname || it.nickfieldname === fieldname,
      );

      const name = `${fieldname}（${find.fieldcomment}）`;
      return { name, condition: option, value };
    });
    return Filter.formValuesToRes(mapQueryfilter || [], {
      format: customFormat,
    });
  };

  const columns = [
    {
      title: '字段名称',
      dataIndex: 'infoitemsname',
      ellipsis: true,
      width: 300,
      render: (text) => {
        return text || '--';
      },
    },
    {
      title: '字段英文名称',
      dataIndex: 'originname',
      width: 200,
      ellipsis: true,
    },
  ];
  const [fieldTableDatas, setFieldTableDatas] = useState<any>([]);
  const [currentRowData, setCurrentRowData] = useState<any>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const { loading: tableLoading, run: getTableDetail } = useRequest(
    'metadata/moneycenter/tablefieldinfo',
  );

  const onTableRow = (record: any) => {
    return {
      style: {
        background:
          currentRowData && currentRowData.inventoryid === record.inventoryid
            ? '#E6F1FF'
            : undefined,
      },
      onClick: async () => {
        const { assettype, opentype } = record;
        setCurrentRowData(record);
        if (assettype === '数据库' && opentype === '完全开放') {
          const { result } = await getTableDetail({
            inventoryid: record.inventoryid,
            isattach: true,
          });
          const { assetinfo, fieldinfo } = result;
          const fieldinfoData: any = [];
          const temp = assetinfo || [];
          const infoitemsnames = temp.map((item) => item.infoitemsname);
          // 如果展示字段设置为空，则默认展示全部字段
          if (!record.infoitemsnames) {
            setSelectedRowKeys(infoitemsnames);
          } else {
            setSelectedRowKeys(record.infoitemsnames || []);
          }
          temp.forEach((item: any, index) => {
            const { infoitemsname } = item;
            const { fieldtype, name } = fieldinfo[index];
            fieldinfoData.push({
              originname: name,
              infoitemsname,
              infoitemstype: fieldtype,
            });
          });
          setFieldTableDatas([...fieldinfoData]);
        } else {
          setSelectedRowKeys([]);
        }
      },
    };
  };
  const isShowDetail = () => {
    return (
      (customset.clickset && customset.customiframe) ||
      xcompareset.clickset ||
      chartset.clickset ||
      inventoryset.clickset
    );
  };

  const getFieldComment = (fieldName) => {
    if (!fieldName) {
      return '';
    } else {
      const find = fieldset.find(
        (item) =>
          item.orifieldname === fieldName || item.nickfieldname === fieldName,
      );
      if (find) return find.fieldcomment;
    }
  };
  return (
    <Spin spinning={loading}>
      <div className={styles.infoContainer}>
        <Block title="指标预览效果">
          <div className={styles.previewContainer}>
            <div className={styles.info}>
              <div className={styles.icon}>
                <span>指标名称：</span>
                <div className={styles.img}>
                  <Image
                    src={getIcon(mpicture)}
                    width="100%"
                    height="100%"
                    preview={false}
                  />
                </div>
              </div>
              <div className={styles.row}>
                <div className={styles.item}>
                  <Checkbox
                    className={styles.checkbox}
                    checked={ispreview}
                    disabled
                  />
                  <span>指标预览值</span>
                </div>
                <div className={styles.item}>
                  <Checkbox
                    className={styles.checkbox}
                    checked={!hidecomma}
                    disabled
                  />
                  <span>千分位分隔符</span>
                </div>
              </div>
              {ispreview && (
                <>
                  {querypreview.filtercustom ? (
                    <div className={styles.row}>
                      <span>数据筛选：</span>
                      <AceEditor
                        value={querypreview.filtercustom}
                        style={{ width: '90%', height: '120px' }}
                        readOnly
                      />
                    </div>
                  ) : (
                    <>
                      <div className={styles.row}>
                        <span>数据筛选：</span>
                        <Filter
                          status="view"
                          filters={filtersData(querypreview.queryfilter || [])}
                        />
                      </div>
                      <div className={styles.row}>
                        <span>条件逻辑：</span>
                        <span>
                          {querypreview.filtertype === 'or'
                            ? '满足任一条件'
                            : '满足所有条件'}
                        </span>
                      </div>
                    </>
                  )}
                  <div className={styles.row}>
                    <span>指标聚合：</span>
                    <span>
                      {querypreview.transacttype
                        ? getChartTransacttype(querypreview.transacttype)
                        : '--'}
                    </span>
                  </div>
                </>
              )}

              <div className={styles.row}>
                <Checkbox
                  className={styles.checkbox}
                  checked={markset.clickset}
                  disabled
                />
                <span>指标发布</span>
                <span style={{ margin: '0 8px' }}>
                  {
                    dayOptions.find((item) => item.value === markset.markdays)
                      ?.label
                  }
                </span>
                <span>标识 “NEW”</span>
              </div>
              {compareset.clickset && (
                <div className={styles.row}>
                  <Checkbox
                    className={styles.checkbox}
                    checked={compareset.clickset}
                    disabled
                  />
                  <div>
                    <Ellipsis tooltip showCenter={false} lines={1}>
                      {`${
                        compareset.comparetype === 1 ? '数据同比' : '数据环比'
                      }：${compareset.comparemessage}--${compareset.dirname}--${
                        compareset.zbname
                      }--${compareset.comparesymbol}`}
                    </Ellipsis>
                  </div>
                </div>
              )}
              {warningset.clickset && (
                <div className={styles.row}>
                  <Checkbox
                    className={styles.checkbox}
                    checked={warningset.clickset}
                    disabled
                  />
                  <div>
                    <Ellipsis tooltip showCenter={false} lines={1}>
                      {`指标告警规则--${
                        warningRuleOptions.find(
                          (item) => item.value === warningset.warningtype,
                        )?.label
                      }--${warningset.warningtarget}--${
                        warningset.warningmessage
                      }`}
                    </Ellipsis>
                  </div>
                </div>
              )}
            </div>
            <div className={styles.preview}>
              <PreviewItem {...itemValue} onlyShow />
            </div>
          </div>
        </Block>
        {isShowDetail() && (
          <Block title="指标详情界面">
            {customset.clickset ? (
              // 自定义页面
              <div className={styles.customPanel}>
                <div className={styles.input}>{customset.customiframe}</div>
                <div className={styles.customPreview}>
                  <div className={styles.title}>预览效果：</div>
                  <div
                    className={styles.preview}
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      __html: customset.customiframe,
                    }}
                  />
                </div>
              </div>
            ) : (
              //  通用界面
              <div className={styles.detailContainer}>
                {xcompareset.clickset && (
                  <>
                    <div className={styles.subTitle}>横向对比指标</div>
                    <div className={styles.xComparelist}>
                      <Table
                        dataSource={xcompareset.zbarray}
                        columns={xcompareColumns}
                        pagination={false}
                      />
                    </div>
                  </>
                )}
                {chartset.clickset && !isChartType() && (
                  <>
                    <div className={styles.subTitle}>详情图表呈现</div>
                    <div className={styles.chartPanel}>
                      <div className={styles.chartset}>
                        <div className={styles.item}>
                          <div className={styles.label}>图表类型：</div>
                          <div className={styles.value}>{getChartType()}</div>
                        </div>
                        {(charttype === 1 || charttype === 2) && (
                          <div className={styles.item}>
                            <div className={styles.label}>分组：</div>
                            <div className={styles.value}>
                              <Ellipsis tooltip showCenter={false} lines={1}>
                                <>
                                  <span>{chartgroup || '--'}</span>
                                  {chartgroup && (
                                    <span>
                                      {`（${getFieldComment(chartgroup)}）`}
                                    </span>
                                  )}
                                </>
                              </Ellipsis>
                            </div>
                          </div>
                        )}
                        {charttype !== 4 && (
                          <>
                            <div className={styles.item}>
                              <div className={styles.label}>维度：</div>
                              <div className={styles.value}>
                                <Ellipsis tooltip showCenter={false} lines={1}>
                                  <>
                                    <span>{chartwd || '--'}</span>
                                    {chartwd && (
                                      <span>
                                        {`（${getFieldComment(chartwd)}）`}
                                      </span>
                                    )}
                                  </>
                                </Ellipsis>
                              </div>
                            </div>
                            <div className={styles.item}>
                              <div className={styles.label}>度量：</div>
                              <div className={styles.value}>
                                <Ellipsis tooltip showCenter={false} lines={1}>
                                  <>
                                    <span>{chartdl || '--'}</span>
                                    {chartdl && (
                                      <span>
                                        {`（${getFieldComment(chartdl)}）`}
                                      </span>
                                    )}
                                  </>
                                </Ellipsis>
                              </div>
                            </div>
                          </>
                        )}

                        <div className={styles.item}>
                          <div className={styles.label}>排序：</div>
                          <div className={styles.value}>
                            <Ellipsis tooltip showCenter={false} lines={1}>
                              <>
                                <span>{sortfield || '--'}</span>
                                {sortfield && (
                                  <>
                                    <span>
                                      {`（${getFieldComment(sortfield)}）`}
                                    </span>
                                    <span>{`--  ${getChartSort()}`}</span>
                                  </>
                                )}
                              </>
                            </Ellipsis>
                          </div>
                        </div>
                        {chartQuerypreview.filtercustom ? (
                          <div className={styles.item}>
                            <span>数据筛选：</span>
                            <AceEditor
                              value={chartQuerypreview.filtercustom}
                              style={{ width: '75%', height: '120px' }}
                              readOnly
                            />
                          </div>
                        ) : (
                          <>
                            <div className={styles.item}>
                              <span>数据筛选：</span>
                              <Filter
                                status="view"
                                filters={filtersData(
                                  chartQuerypreview.queryfilter || [],
                                )}
                              />
                            </div>
                            <div className={styles.item}>
                              <span>条件逻辑：</span>
                              <span>
                                {chartQuerypreview.filtertype === 'or'
                                  ? '满足任一条件'
                                  : '满足所有条件'}
                              </span>
                            </div>
                          </>
                        )}
                      </div>
                      <div className={styles.chart}>
                        <Spin spinning={getChartDataLoading}>
                          {charttype === 4 ? (
                            <TableList
                              datas={tableListData}
                              fieldset={fieldset}
                            />
                          ) : (
                            <DataIndexChart
                              width={800}
                              chartType={charttype}
                              optionDatas={chartOptionData}
                              isGroup={isGroup}
                            />
                          )}
                        </Spin>
                      </div>
                    </div>
                  </>
                )}
                <div className={styles.dataSource}>
                  {inventoryset.clickset && (
                    <>
                      <div className={styles.subTitle}>指标数据来源</div>
                      <Table
                        dataSource={inventoryset.zcarray}
                        columns={inventoryColumns}
                        pagination={false}
                        onRow={onTableRow}
                      />
                      {currentRowData &&
                        currentRowData.assettype === '数据库' &&
                        currentRowData.opentype === '完全开放' && (
                          <>
                            <TitleWrapper
                              title="展示字段设置"
                              style={{ margin: '16px 0' }}
                            >
                              <Table
                                loading={tableLoading}
                                style={{ width: '600px' }}
                                columns={columns}
                                rowSelection={{
                                  selectedRowKeys,
                                  getCheckboxProps: () => ({
                                    disabled: true,
                                  }),
                                }}
                                dataSource={fieldTableDatas}
                                rowKey="infoitemsname"
                                pagination={false}
                                scroll={{ x: true, y: '200px' }}
                              />
                            </TitleWrapper>
                            <TitleWrapper title="展示数据范围设置">
                              {currentRowData?.querypreview?.filtercustom ? (
                                <div className={styles.item}>
                                  <span>数据筛选：</span>
                                  <AceEditor
                                    value={
                                      currentRowData?.querypreview?.filtercustom
                                    }
                                    style={{ width: '93%', height: '120px' }}
                                    readOnly
                                  />
                                </div>
                              ) : (
                                <>
                                  <div className={styles.item}>
                                    <span>数据筛选：</span>
                                    <Filter
                                      status="view"
                                      filters={filtersData(
                                        currentRowData?.querypreview
                                          ?.queryfilter || [],
                                      )}
                                    />
                                  </div>
                                  <div className={styles.item}>
                                    <span>条件逻辑：</span>
                                    <span>
                                      {currentRowData?.querypreview
                                        ?.filtertype === 'or'
                                        ? '满足任一条件'
                                        : '满足所有条件'}
                                    </span>
                                  </div>
                                </>
                              )}
                            </TitleWrapper>
                          </>
                        )}
                    </>
                  )}
                </div>
              </div>
            )}
          </Block>
        )}
      </div>
    </Spin>
  );
};

export default DataIndexInfo;
