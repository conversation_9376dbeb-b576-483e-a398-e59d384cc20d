.applyUseTableWrapper {
  :global {
    .ant-table-body {
      max-height: 495px !important;
    }

    .ant-table-tbody > tr > td:last-child {
      padding-right: 8px !important;
      // background-color: #fff !important;
      // text-align: right !important;
    }

    .ant-pagination-options {
      display: none;
    }
  }

  .statusTag {
    color: #08b156;
    margin-left: 4px;
  }
}

.innerSearchEmpty {
  margin-top: 40px;

  img {
    width: 320px;
    height: 320px;
  }

  :global {
    .ant-empty-description {
      margin-top: 160px;
      font-size: 24px;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
      margin-bottom: 80px;
    }
  }
}

.applyUseDropdown {
  :global {
    .ant-dropdown-menu-item {
      min-width: 80px;
      height: 32px;
      line-height: 32px;
      padding: 0;
      text-align: center;

      span {
        font-size: 14px;
        // color: @portal-font-color;
      }
    }
  }
}

.toggleColumnsBox {
  width: 80px;
  box-shadow: 0 2px 4px 1px rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;

  span {
    width: 100%;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: @portal-font-color;
    text-align: center;
    background: #fff;
    display: inline-block;
    cursor: pointer;

    &.selected {
      background-color: @custom-primary-1;
    }
  }
}

.portalMyApplyTitleText {
  width: 120px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
}

.applyListModal {
  :global {
    .ant-modal-body {
      padding: 24px !important;
    }
  }
}
