import { IconFont } from '@dana/ui';
import { CustomColumnProps } from '@dana/ui/dist/types/src/components/Table/Table';
import { Card, Table, Tooltip } from 'antd';
import { useState } from 'react';
import { useModel } from 'umi';
import { RawResourceItemDetail } from '@/pages/Portal/Resource/model/resourceDetail.model';
import { DS_SCENE } from '@/config/projectInfo';
import { basicColumns } from '@/pages/AssetMounting/MainTable/components/MountMessageFlow';
import Preview from '../Component/Preview';
import styles from './index.less';

const isNingBo = DS_SCENE === 'ningbo';

const ellipsisStyle: React.CSSProperties = {
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
};

const getCardTitle = (isMessage?: boolean) => {
  return (
    <span style={{ marginLeft: '8px', color: 'rgba(51, 51, 51, 1)' }}>
      {isMessage ? '字段信息' : '数据字典'}
    </span>
  );
};
const newColumns: CustomColumnProps<RawResourceItemDetail['infoitem'][0]>[] = [
  {
    title: '序号',
    dataIndex: 'num',
    width: 56,
  },
  {
    title: '字段名称',
    dataIndex: 'infoitemsname',
    width: 240,
    ellipsis: {
      showTitle: false,
    },
    render: (val) => (
      <Tooltip placement="topLeft" title={val}>
        {val || '--'}
      </Tooltip>
    ),
  },
  {
    title: '字段英文名称',
    dataIndex: 'name',
    width: 240,
    ellipsis: true,
    render: (val, record) => {
      return (
        <div>
          <div
            style={{
              width: !record.primarykey && !record.partition ? '100%' : '170px',
              display: 'inline-block',
              ...ellipsisStyle,
            }}
          >
            <Tooltip title={val}>{val || '--'}</Tooltip>
          </div>
          {record.primarykey && (
            <IconFont
              type="icon-key"
              style={{ color: '#1980ff', marginLeft: 8 }}
            />
          )}
          {record.partition && (
            <IconFont
              type="icon-uniqueKey"
              style={{ color: 'rgba(8, 177, 86, 1)', marginLeft: 8 }}
            />
          )}
        </div>
      );
    },
  },
  {
    title: '类型',
    dataIndex: 'infoitemstype',
    width: 120,
    ellipsis: {
      showTitle: false,
    },
    render: (val) => (
      <Tooltip placement="topLeft" title={val}>
        {val || '--'}
      </Tooltip>
    ),
  },
  {
    title: '字段类型',
    dataIndex: 'fieldtype',
    width: 120,
    ellipsis: {
      showTitle: false,
    },
    render: (val) => (
      <Tooltip placement="topLeft" title={val}>
        {val || '--'}
      </Tooltip>
    ),
  },
  {
    title: '字段注释',
    dataIndex: 'annotation',
    ellipsis: {
      showTitle: false,
    },
    render: (val) => (
      <Tooltip placement="topLeft" title={val}>
        {val || '--'}
      </Tooltip>
    ),
  },
];

/**
 * Description placeholder 【宁波定制】13.3 门户-资产详情展示分类分级信息
 *
 * @type {{}}
 */
const ningboCustomizedColumns = newColumns.concat([
  {
    title: '二级分类',
    dataIndex: 'secondaryclassify',
    width: 120,
    ellipsis: {
      showTitle: false,
    },
    render: (val) => (
      <Tooltip placement="topLeft" title={val}>
        {val || '--'}
      </Tooltip>
    ),
  },
  {
    title: '安全等级',
    dataIndex: 'securitylevel',
    width: 120,
    ellipsis: {
      showTitle: false,
    },
    render: (val) => (
      <Tooltip placement="topLeft" title={val}>
        {val || '--'}
      </Tooltip>
    ),
  },
]);

function TableInfo() {
  const [previewVisible, setPreviewVisible] = useState(false);
  const data = useModel('Portal.Resource.resourceDetail').data!;

  const isDataBase = data.assettype === '数据库';
  // todo 消息流API  信息项不支持【数据预览】
  const isMessageFlow = data.assettype === '消息流';

  function getColumns() {
    if (isNingBo && isDataBase) {
      return ningboCustomizedColumns;
    }
    if (isMessageFlow) {
      return basicColumns();
    }
    return newColumns;
  }
  return (
    <>
      <Card
        title={getCardTitle(isMessageFlow)}
        headStyle={{
          height: 24,
          padding: 0,
          minHeight: '24px !important',
          marginBottom: '16px',
          borderBottom: 'none',
        }}
        className={styles.commonCard}
        bordered={false}
        extra={
          !isMessageFlow ? (
            <span
              className={styles.extraTitle}
              onClick={() => {
                setPreviewVisible(true);
              }}
            >
              <IconFont type="icon-file-search" style={{ marginRight: 8 }} />
              数据预览
            </span>
          ) : undefined
        }
      >
        <div>
          <Table
            className={styles.tableInfo}
            dataSource={
              isMessageFlow
                ? data?.attachinfo?.attachrealmessageinfo?.fieldinfo?.map(
                    (item, index) => {
                      return {
                        ...item,
                        key: index + 1,
                      };
                    },
                  )
                : data.infoitem
            }
            // 宁波定制栏位展示
            columns={getColumns()}
            custom
            bordered
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
              showTotal: (size) => `共${size}条`,
            }}
            scroll={false}
          />
        </div>
      </Card>
      {previewVisible && (
        <Preview
          name={data.inventoryname}
          id={data.inventoryid}
          visible={previewVisible}
          handleCancel={() => setPreviewVisible(false)}
        />
      )}
    </>
  );
}

export default TableInfo;
