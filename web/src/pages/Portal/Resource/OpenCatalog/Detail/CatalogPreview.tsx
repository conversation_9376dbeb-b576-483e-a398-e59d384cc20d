import { Table, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { useRequest } from '@/utils';
import styles from './index.less';

interface CatalogPreviewType {
  catalogData: {
    assetcategoryid: string;
    assetcategoryname: string;
    inventoryid: string;
    assettype: string; // 资产类型
  };
}

const CatalogPreview = (props: CatalogPreviewType) => {
  const { catalogData } = props;

  const [columns, setColumns] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const { loading, run: getList } = useRequest('portal/dir/form/detail', {
    onSuccess: ({ code, result }) => {
      if (code === 200) {
        setDataSource(formatDataSource(result));
        setColumns(formatColumns(result));
      }
    },
    onError: () => {
      console.error('error');
    },
  });

  function getMultilineEllipsisTooltip(text) {
    return (
      <Tooltip title={text}>
        <div className={styles['multiline-ellipsis']}>{text || '--'}</div>
      </Tooltip>
    );
  }

  function formatTitle(name) {
    const renameNameMap = {
      信息项: '字段名称',
      字段名称: '字段英文名称',
      信息项类型: '类型',
    };
    return renameNameMap[name] || name || '--';
  }

  // 格式化栏位信息
  function formatColumns(response) {
    // 先按照num排序

    const tempData = JSON.parse(JSON.stringify(response));

    // 按照num 正序
    tempData.sort((a, b) => {
      return a.num > b.num ? 1 : -1;
    });
    const formatData = formatDataSource(tempData);

    const tempResult: Array<any> = [];
    const LEN = tempData.length || 0;
    // 如果没有栏位返回 则保持空数据
    if (!response || tempData.length === 0) {
      return [];
    }

    for (let i = 0; i < LEN; i++) {
      const { exportheadername } = tempData[i];
      tempResult.push({
        title: formatTitle(exportheadername),
        width: 160,
        key: exportheadername,
        ellipsis: true,
        render: (val, _, index) => {
          const target = val[exportheadername];
          const isMulti = typeof target !== 'string';
          const obj: any = {
            children: getMultilineEllipsisTooltip(
              isMulti ? target[index] : target,
            ),
            props: {},
          };
          obj.props.rowSpan = mergeCells(
            isMulti ? target[index] : target,
            formatData,
            exportheadername,
            index,
          );
          return obj;
        },
      });
    }

    return tempResult;
  }

  function formatDataSource(originData) {
    const temp = JSON.parse(JSON.stringify(originData));
    // 按照num 正序
    temp.sort((a, b) => {
      return a.num > b.num ? 1 : -1;
    });
    const tempResult = [];

    const tempObj = {};

    // * 文件与指标资产没有信息项、信息项类型、字段名称，如在资产运营-目录版本管理-新建/编辑目录版本时，勾选了以上3个属性，内容显示为 --

    if (catalogData.assettype === '数据库') {
      temp.forEach((item) => {
        tempObj[item.exportheadername] =
          item.exportheadervalues && item.exportheadervalues?.length > 1
            ? item.exportheadervalues
            : item.exportheadervalues[0];
      });
    } else {
      const isNeedEmptySign = ['信息项', '信息项类型', '字段名称'];
      //! 针对数据文件和数据指标
      temp.forEach((item) => {
        if (isNeedEmptySign.indexOf(item.oriheadername) > -1) {
          tempObj[item.exportheadername] = '--';
        } else {
          tempObj[item.exportheadername] =
            item.exportheadervalues && item.exportheadervalues?.length > 1
              ? item.exportheadervalues
              : item.exportheadervalues[0];
        }
      });
    }

    //! 应该按照最长的栏位来组装数据
    // ! 例如信息项有5条 那么dataSource就应该有5条
    let longest = 1;
    const LEN = Object.entries(tempObj).length;

    for (let i = 0; i < LEN; i++) {
      const ele = Object.entries(tempObj)[i];
      if (typeof ele[1] !== 'string') {
        // @ts-ignore
        if (longest < ele[1].length) {
          //! 只取最大的这个
          // @ts-ignore
          longest = ele[1].length;
        }
      }
    }

    for (let i = 0; i < longest; i++) {
      const copyObj = { ...tempObj, sID: `sid**${i}` }; // 通过展开运算符创建一个新对象并赋值原始对象的属性
      // @ts-ignore
      tempResult.push(copyObj); // 将复制后的对象添加到数组中
    }
    if (tempResult.length === longest) {
      return tempResult;
    }
  }

  useEffect(() => {
    // assetcategoryid 资产目录版本id  +  inventoryid  资产编目id
    getList({
      assetcategoryid: catalogData.assetcategoryid,
      inventoryid: catalogData.inventoryid,
    });
  }, []);

  return (
    <div className={styles['detail-table-wrapper']}>
      <Table
        loading={loading}
        columns={columns}
        rowKey="sID"
        dataSource={dataSource}
        bordered
        style={{ height: '100%' }}
        pagination={false}
        scroll={{
          x: 1000,
          // * antd-table body  max-height
          y: `calc(100% - 48px)`,
        }}
      />
    </div>
  );
};

export default CatalogPreview;

/**
 * 动态合并表格方法
 * @param {*} text 表格每列对应的值
 * @param {*} data 后台返回的展示数据数组, 数据需要按字段排序
 * @param {*} key 表格每列表头字段
 */
export function mergeCells(text, data, key, index) {
  // * 共享开放目录  暂时没有多字段单独合并场景
  if (index !== 0 && text === data[index - 1][key]) {
    return 0;
  }
  let rowSpan = 1;
  // 判断下一行是否相等
  for (let i = index + 1; i < data.length; i++) {
    if (text !== data[i][key]) {
      break;
    }
    rowSpan += 1;
  }
  return rowSpan;
}
