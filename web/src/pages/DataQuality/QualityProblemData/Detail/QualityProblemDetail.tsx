/* eslint-disable @typescript-eslint/naming-convention */
import { useState, useEffect, useRef } from 'react';
import {
  Typography,
  Space,
  Table,
  Spin,
  Button,
  Tooltip,
  notification,
} from 'antd';
import { Select } from 'antd-new5';
import { useRequest, useDispatch } from '@/utils';
import * as echarts from 'echarts';
import 'echarts-liquidfill';
import ReactECharts from 'echarts-for-react';
import {
  LayoutHelper as Layout,
  TagsAdd,
  IconFont,
  useInterval,
} from '@dana/ui';
import FlexContent from '@/pages/AssetMounting/components/FlexContent';
import { getThousandSeparator } from '@/utils/lib/otherTool';

import { useSelector } from 'umi';
import { ConnectState } from '@/models/connect.d';
import { TIMELIESS_REPEAT_LIST } from '@/pages/DataQuality/QualityRule/components/TimelinessForm';
import {
  WEEK_OPTIONS,
  DAY_OPTIONS,
  MONTH_OPTIONS,
} from '@/pages/DataQuality/QualityRule/data';
import NumCard from '../components/NumCard';
import {
  MOCK_DETAIL,
  basicInfoData,
  initialStatus,
  COLUMNS_WITH_RATE,
  NEED_MERGE_DATA,
} from '../data';
import styles from './index.less';

const { Text } = Typography;
const { Content, InnerBox } = Layout;
const { Option } = Select;
const dataNumStyle: React.CSSProperties = {
  textAlign: 'right',
  display: 'inline-block',
  width: '100%',
};

// 0代表数据接入 1代表数据监控 2代表不配置

const DATA_ENTRY_MAP = {
  0: '数据接入',
  1: '质量监控',
} as const;

interface QualityProblemDetailTypes {
  tbrecord: any;
  catalogMiddleTb: boolean;
  detailBackToList: () => void;
}
const QualityProblemDetail = (props: QualityProblemDetailTypes) => {
  const { catalogMiddleTb, tbrecord, detailBackToList } = props;

  const radarChartRef = useRef<any>(null);
  const liquidChartRef = useRef<any>(null);
  // * 针对历史数据单独处理
  const { ishistory } = tbrecord;
  // todo  数据需接口组装处理
  const [hasDataEntry, setHasDataEntry] = useState<boolean>(false);
  const [basicInfo, setBasicInfo] =
    useState<typeof basicInfoData>(basicInfoData);
  const [columns, setColumns] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [allQualityProblemDetail, setAllQualityProblemDetail] =
    useState<typeof MOCK_DETAIL>();

  const [currentDetail, setCurrentDetail] =
    useState<(typeof MOCK_DETAIL.historyloginfo)[0]>();
  // * 月份选择
  const [monthRange, setMonthRange] = useState([]);
  // * 初始值就是最新月
  const [currentMonth, setCurrentMonth] = useState<string>();

  // * 数据更新
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  // * 更新是否完成标识
  const [isLoad, setIsLoad] = useState(false);

  // * 六维数据
  const [tableVerifyStatus, setTableVerifyStatus] = useState(initialStatus);
  const dispatch = useDispatch();
  //* 详情页用的组件展示 所以若是在详情页点击左侧路由菜单不会回置到列表页 需手动重置
  const { backFromMenu } = useSelector(
    (state: ConnectState) => state.assetMount,
  );
  // * 取model层数据
  const { canCurrentPageEdit: canEdit } = useSelector(
    (state: ConnectState) => state.user,
  );
  const { run: getInitData, loading: detailLoading } = useRequest(
    'metadata/devcenter/qualityproblemdetail',
    {
      onSuccess: ({ result, code }) => {
        if (code !== 200) {
          return;
        }

        // * 总的详情数据
        setAllQualityProblemDetail(result);

        // * 处理 最新问题月份 👇
        const totalRange = JSON.parse(JSON.stringify(result.historytime));
        setMonthRange(totalRange);
        const last = JSON.parse(JSON.stringify(totalRange))?.pop();
        setCurrentMonth(last);
        // * 处理 最新问题月份 👆

        // * 最新问题版本 详情数据
        const nowDetail = result?.historyloginfo?.filter(
          (item) => item.yearmonth === last,
        )[0];

        setCurrentDetail(nowDetail);
        // * 赋值表质量分
        liquidChartOptions(nowDetail?.rulerscore?.tableQualityScore);
        // * 赋值雷达信息
        const tempRadar = getRadarData(nowDetail?.rulerscore);
        radarChartOptions(tempRadar);

        // * 赋值六维信息
        const tempStatus = {
          ...tableVerifyStatus,
        };
        tempStatus['完整性'] = nowDetail?.rulerscore?.integrity;
        tempStatus['规范性'] = nowDetail?.rulerscore?.normative;
        tempStatus['准确性'] = nowDetail?.rulerscore?.accuracy;
        tempStatus['一致性'] = nowDetail?.rulerscore?.uniformity;
        tempStatus['时效性'] = nowDetail?.rulerscore?.timeliness;
        tempStatus['可访问性'] = nowDetail?.rulerscore?.accessibility;

        setTableVerifyStatus(tempStatus);
        // * 基础信息在最外层不影响
        const target = {
          ...basicInfoData,
        };

        target['字段数'] = result?.fieldnum;
        target['引擎名称'] = result?.enginename;
        target['业务来源库名'] = result?.sourcedbname;
        target['引擎类型'] = result?.enginetype;
        target['业务来源表'] = result?.sourcetable;
        setBasicInfo(target);

        //* 是否有数据接入 或者是数据监控
        // *  0代表数据接入 1代表数据监控 2代表不配置
        // * 后端原话【前端界面重复数据必有主键吧？  根据主键是否为0判断】
        const hasEntry =
          nowDetail?.duproles?.repetitionmainkey &&
          nowDetail?.duproles?.repetitionmainkey?.length !== 0 &&
          nowDetail?.duproles?.inputtype !== 2;
        setHasDataEntry(hasEntry);

        // * 全部都有合格率栏位
        const tempCo = formatColumns(
          nowDetail?.fieldinfo,
          Object.entries(COLUMNS_WITH_RATE),
        );
        const tempDa = formatDataSource(nowDetail?.fieldinfo);

        setDataSource(tempDa);
        setColumns(tempCo);
      },
    },
  );

  function getRadarData(obj) {
    const isDataEntryRadar = currentDetail?.duproles?.inputtype === 0;
    if (obj && !isDataEntryRadar) {
      const {
        integrity, // * 完整性
        normative, //* 规范性
        accuracy, //* 准确性
        uniformity, //* 一致性
        timeliness, //* 时效性
        accessibility, //* 可访问性
      } = obj;
      // * 该值 有顺序
      const radarScore = [
        normative,
        integrity,
        accessibility,
        timeliness,
        uniformity,
        accuracy,
      ];

      return radarScore;
    } else {
      // * 数据接入只展示底图
      return [0, 0, 0, 0, 0, 0];
    }
  }

  useInterval(
    () => {
      setIsUpdating(true);
      const params = {
        id: tbrecord.id,
        middletabletype: Number(catalogMiddleTb),
      };
      dispatch({
        type: 'qualityProblem/getSyncQualityTableMonth',
        payload: params,
      })
        .then(
          (res?: {
            isLoading?: boolean;

            code?: number;
            result?: string;
          }) => {
            setIsUpdating(false);
            // true 运行中；false 停止完成
            if (res?.isLoading === false) {
              setIsLoad(false);
              notification.success({
                message: '最新问题数据已同步完成',
              });
              // 调用接口刷新页面数据
              initPage();
            }
            if (res?.code === 500) {
              setIsLoad(false);
              notification.error({
                message: res?.result,
              });
            }
          },
        )
        .catch((err) => {
          setIsLoad(false);
          notification.error({
            message: err,
          });
        });
    },
    isLoad && !isUpdating ? 2000 : null,
  );

  /**
   * @description 雷达 图表配置项信息
   *
   * @returns {{ radar: { splitArea: { areaStyle: { color: {}; shadowColor: string; shadowBlur: number; }; }; splitLine: { lineStyle: { color: {}; width: number; }; }; indicator: {}; name: { textStyle: { color: string; }; }; }; series: {}; }}
   */
  function radarChartOptions(radarScore?: number[]) {
    const option = {
      radar: {
        center: ['50%', '50%'], //* 中心
        radius: '80%', //* 半径
        startAngle: 60, // * 倾斜角度
        splitNumber: 4, // * 几个格子 雷达图圈数设置
        splitArea: {
          areaStyle: {
            color: '#fff',
          },
        },
        axisLine: {
          lineStyle: {
            // * 灰色
            color: 'rgba(225, 225, 225, 1)',
          },
        },
        splitLine: {
          lineStyle: {
            // * 灰色
            color: 'rgba(225, 225, 225, 1)',
          },
        },
        // * 逆时针排序
        indicator: [
          { name: '规范性', max: 100 },
          { name: '完整性', max: 100 },
          { name: '可访问性', max: 100 },
          { name: '时效性', max: 100 },
          { name: '一致性', max: 100 },
          { name: '准确性', max: 100 },
        ],
        // 统一设置name的样式（也可以在每个name上单独设置）
        name: {
          // 雷达图各类别名称文本颜色
          textStyle: {
            color: '#000',
          },
        },
      },
      series: [
        {
          name: '表核验情况',
          type: 'radar',
          symbol: 'none', // * UI不需要焦点展示 这个焦点其实就是交点
          // symbol: 'circle', //* 焦点的样式
          // symbolSize: 6, // * 焦点的大小
          data: [
            {
              value: radarScore || [0, 0, 0, 0, 0, 0],
              name: '表数据展示',
              lineStyle: {
                color: 'rgba(25, 128, 255, 1)', // 设置多边形边框颜色
                width: 1, // 设置多边形边框宽度
              },
              areaStyle: {
                normal: {
                  opacity: 0.3,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      color: '#27D9c8',
                      offset: 0,
                    },
                    {
                      color: '#428BFA',
                      offset: 0,
                    },
                  ]),
                  // shadowColor: '#428BFA',
                  // shadowBlur: 15,
                  borderColor: 'rgba(25, 128, 255, 1)', // 边框颜色
                },
              },
            },
          ],
        },
      ],
    };
    return option;
  }

  /**
   * @description 水球 图表配置项信息
   *
   * @returns {{ series: {}; }}
   */
  function liquidChartOptions(score?: number) {
    const valueB = '表质量分';
    const valueA = score ? score / 100 : 0;

    const liquidOptions = {
      series: [
        {
          type: 'liquidFill',
          data: [valueA],
          color: ['rgba(25, 128, 255, 1)'], //* 波浪的颜色 #1980ff
          radius: '70%', // 水球图的半径
          center: ['50%', '50%'], // 水球图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          // * 外面框
          outline: {
            show: true, //* 轮廓样式
            borderDistance: 0, // 边框线与图表的距离 数字
            itemStyle: {
              opacity: 0.01, // 边框的透明度   默认为 1
              borderWidth: 2, // 边框的宽度
              shadowBlur: 1, // 边框的阴影范围 一旦设置了内外都有阴影
              shadowColor: '#fff', // 边框的阴影颜色,
              borderColor: '#fff', // 边框颜色
            },
          },
          label: {
            formatter: () => {
              return score !== undefined ? `{a|${score}}\n{b|${valueB}}` : '--';
            },
            fontSize: 40,
            color: 'rgba(25, 128, 255, 1)', //* 字的颜色 #1980ff
            rich: {
              a: {
                fontSize: 48,
                lineHeight: 69,
                fontWeight: 700,
              },
              b: {
                fontSize: 16,
                lineHeight: 23,
              },
            },
          },
          backgroundStyle: {
            // color: 'rgba(0, 0, 0, 0)', // 设置波浪未到达区域的背景色为透明
            color: 'rgba(249, 249, 249, 0.6)', // 水球未到的背景颜色  #f9f9f9 rgba(249, 249, 249, 1)
            opacity: 0.8, // 背景色的透明度
            borderWidth: 0,
            borderColor: 'transparent', // * 不要边框色
          },
          itemStyle: {
            color: 'rgba(25, 128, 255, 1)', // 波浪颜色更浅或更透明
            opacity: 1, // 减少波浪的透明度，避免和背景叠加过深
            shadowBlur: 20, //! 波浪的阴影范围  这个值居然至关重要  会影响水球未到颜色的 颜色展示  记录下  后续继续跟踪
          },
        },
      ],
    };

    return liquidOptions;
  }

  function initPage() {
    // * 页面初始化
    getInitData({
      id: tbrecord.id, // * 任务id
      engineid: tbrecord.engineid, //* 引擎id
      initdb: tbrecord.initdb, //* 初始化库
      tablename: tbrecord.tbname, //* 表名
      middletabletype: Number(catalogMiddleTb),
    });
  }
  useEffect(() => {
    initPage();
  }, []);

  /**
   * @description 根据不同的栏位类型展示不同的宽度
   *
   * @param {*} type
   * @returns {(120 | 200 | 80)}
   */
  function getWidth(type) {
    if (type === 'name' || type === 'fieldtype') {
      return 120;
    } else if (type === 'annotation' || type === 'ruleFront') {
      return 200;
    } else {
      return 80;
    }
  }

  // * 切换月份
  const changeMonthRange = (value) => {
    // * 重新请求表格数据
    setCurrentMonth(value);
    // * 选中月份数据
    const target = allQualityProblemDetail?.historyloginfo.filter(
      (item) => item.yearmonth === value,
    )[0];

    setCurrentDetail(target);

    // * 赋值六维信息
    const tempStatus = {
      ...tableVerifyStatus,
    };
    tempStatus['完整性'] = target?.rulerscore?.integrity;
    tempStatus['规范性'] = target?.rulerscore?.normative;
    tempStatus['准确性'] = target?.rulerscore?.accuracy;
    tempStatus['一致性'] = target?.rulerscore?.uniformity;
    tempStatus['时效性'] = target?.rulerscore?.timeliness;
    tempStatus['可访问性'] = target?.rulerscore?.accessibility;

    setTableVerifyStatus(tempStatus);
    // * 赋值表质量分
    liquidChartOptions(target?.rulerscore?.tableQualityScore);
    // * 赋值雷达分数
    const tempRadar = getRadarData(target?.rulerscore);
    radarChartOptions(tempRadar);

    // * 更新表格信息
    const tempDa = formatDataSource(target?.fieldinfo);

    setDataSource(tempDa);
  };

  /**
   * @description 数据更新 同步数据
   */
  function syncData() {
    setIsLoad(true);
    const params = {
      id: tbrecord.id,
      problemversion: currentMonth,
      middletabletype: Number(catalogMiddleTb),
    };
    dispatch({
      type: 'qualityProblem/updateDetailData',
      payload: params,
    }).then(() => {
      notification.info({
        message: '数据更新中...',
      });
    });
  }

  /**
   * @description 当重复数据来源是质量监控时，
   *              六维度表质量分可计算出规范性（重复数据校验属于规范性规则）的得分，
   *              进而可计算出表质量分，其他展示为「0」
   *               0代表数据接入 1代表数据监控 2代表不配置
   * @param {*} typeData
   * @param {*} detailData
   */
  function getScore(typeData, detailData) {
    const isDataEntryScore = detailData?.duproles.inputtype === 0;
    const trueScore = typeData[1];
    // * 数据接入 直接展示 --
    return isDataEntryScore ? '--' : trueScore;
  }

  // 格式化栏位信息
  function formatColumns(response, tempColumns) {
    const tempData = JSON.parse(JSON.stringify(response));

    const formatData = formatDataSource(tempData);

    const tempResult: Array<any> = [];
    const LEN = tempColumns.length || 0;
    // 如果没有栏位返回 则保持空数据
    if (!response || tempColumns.length === 0) {
      return [];
    }

    for (let i = 0; i < LEN; i++) {
      const main = tempColumns[i];
      const title = main[1];
      const dataIndexKey = main[0];
      if (
        dataIndexKey !== 'exploratoryrule' &&
        dataIndexKey !== 'qualitydimension'
      ) {
        tempResult.push({
          title,
          width: getWidth(dataIndexKey),
          key: dataIndexKey,
          ellipsis: true,
          onCell: (record, rowIndex) => {
            const { name } = record;
            const target = record[dataIndexKey];
            let rowSpan = 0;
            let firstSameIndex = rowIndex;

            if (NEED_MERGE_DATA.indexOf(dataIndexKey) > -1) {
              // * 只考虑合并  字段名称  字段类型 字段注释栏位
              for (let k = rowIndex - 1; k >= 0; k--) {
                const item = formatData[k];
                if (item.name === name && item?.[dataIndexKey] === target) {
                  firstSameIndex -= 1;
                } else {
                  break;
                }
              }

              for (let j = firstSameIndex; j < formatData.length; j++) {
                const item = formatData[j];
                if (item.name === name && item?.[dataIndexKey] === target) {
                  rowSpan += 1;
                } else {
                  break;
                }
              }

              if (Number(firstSameIndex) === rowIndex) {
                // 当前行就是第一个含相同字段值的行时设置rowSpan
                return { rowSpan };
              } else if (
                Number(firstSameIndex) + rowSpan > rowIndex &&
                Number(firstSameIndex) < rowIndex
              ) {
                // 连续出现相同字段值的行rowSpan设置为0
                return { rowSpan: 0 };
              }
            }
            return {};
          },
          render: (val) => {
            const target = val[dataIndexKey];

            // * 数值 展示成  有千分位 和 居右
            const innerChild =
              dataIndexKey === 'frontverifyrecord' ||
              dataIndexKey === 'frontproblemrecord' ? (
                <span style={dataNumStyle}>
                  {target ? getThousandSeparator(target) : '--'}
                </span>
              ) : (
                <Text ellipsis={{ tooltip: target }}>{target || '--'}</Text>
              );
            return innerChild;
          },
        });
      }
    }

    return tempResult;
  }

  function formatDataSource(originData) {
    const temp = JSON.parse(JSON.stringify(originData));
    const LEN = temp.length;
    const tempResult = [];
    // const tempObj = {};
    for (let i = 0; i < LEN; i++) {
      const ele = temp[i];
      //* 如果探测规则存在  如果不存在就直接是一条本身的数据
      if (ele?.exploratoryrule && ele?.exploratoryrule?.length > 0) {
        const INNER_LEN = ele.exploratoryrule.length;
        // 如果每一项的探查规则也是多个的话  数据长度按照探查规则的长度走
        for (let j = 0; j < INNER_LEN; j++) {
          const sonRule = ele.exploratoryrule ? ele.exploratoryrule[j] : '';
          const sonMension = ele.qualitydimension
            ? ele.qualitydimension[j]
            : '';
          const sonverifyrecord = ele.verifyrecords
            ? ele.verifyrecords[j]
            : null;
          const sonproblemrecord = ele.problemrecords
            ? ele.problemrecords[j]
            : null;
          const sonqualifyrate = ele.qualifyrate ? ele.qualifyrate[j] : null;
          const tempObj = {
            ...ele,
            ruleFront: sonRule,
            mensionFront: sonMension,
            frontverifyrecord: sonverifyrecord,
            frontproblemrecord: sonproblemrecord,
            frontqualifyrate: sonqualifyrate ? `${sonqualifyrate}%` : '--',
            sID: Math.random() * 100000,
          };
          // @ts-ignore
          tempResult.push(tempObj);
        }
      } else {
        const sonRule = ele?.exploratoryrule ? ele.exploratoryrule[0] : '';
        const sonMension = ele?.qualitydimension ? ele.qualitydimension[0] : '';
        const sonverifyrecord = ele?.verifyrecords
          ? ele.verifyrecords[0]
          : null;
        const sonproblemrecord = ele?.problemrecords
          ? ele.problemrecords[0]
          : null;
        const sonqualifyrate = ele.qualifyrate ? ele.qualifyrate[0] : null;

        const tempObj = {
          ...ele,
          ruleFront: sonRule,
          mensionFront: sonMension,
          frontverifyrecord: sonverifyrecord,
          frontproblemrecord: sonproblemrecord,
          frontqualifyrate: sonqualifyrate ? `${sonqualifyrate}%` : '--',
          sID: Math.random() * 100000,
        };
        // @ts-ignore
        tempResult.push(tempObj);
      }
    }
    return tempResult;
  }

  useEffect(() => {
    if (backFromMenu) {
      // 点击路由 要回到列表页
      detailBackToList();
      dispatch({
        type: 'assetMount/save',
        payload: {
          backFromMenu: false,
        },
      });
    }
  }, [backFromMenu]);

  const renderTimelinessText = () => {
    const {
      timelinessrepeat,
      timelinessstart,
      timelinessend,
      repeatsection = [[]],
      timelinesstype,
      timelinessregex,
    } = currentDetail?.timeliness || {};

    if (timelinesstype === 'regex') {
      return timelinessregex;
    }
    const repeat = TIMELIESS_REPEAT_LIST.find(
      (v) => v.value === timelinessrepeat,
    )?.label;
    let repeatValue;
    if (repeat === '永不') {
      repeatValue = (
        <>
          <span style={{ marginRight: 12 }}>
            开始时间：
            {timelinessstart || '--'}
          </span>
          <span>
            结束时间：
            {timelinessend || '永久'}
          </span>
        </>
      );
    }
    if (repeat === '每周' || repeat === '每月') {
      const list = repeat === '每周' ? WEEK_OPTIONS : MONTH_OPTIONS;
      const value1 = repeatsection[0][0];
      const value2 = repeatsection[0][1];
      const text1 = list.find((v) => v.value === value1)?.label;
      const text2 = list.find((v) => v.value === value2)?.label;
      repeatValue = (
        <>
          <span>{text1}</span>
          <span style={{ margin: '0 8px' }}>-</span>
          <span>{text2}</span>
        </>
      );
    }
    if (repeat === '每年') {
      repeatValue = repeatsection.map((item) => {
        const monthValue = item[0].slice(0, 2);
        const day1Value = item[0].slice(2, 4);
        const day2Value = item[1].slice(2, 4);

        const month = MONTH_OPTIONS.find((v) => v.value === monthValue)?.label;
        const day1 = DAY_OPTIONS.find((v) => v.value === day1Value)?.label;
        const day2 = DAY_OPTIONS.find((v) => v.value === day2Value)?.label;

        return (
          <div style={{ marginRight: 12 }}>
            <span style={{ marginRight: 8 }}>{month}</span>
            <span>{day1}</span>
            <span style={{ margin: '0 8px' }}>-</span>
            <span>{day2}</span>
          </div>
        );
      });
    }
    return (
      <>
        <span style={{ marginRight: 12 }}>{repeat}</span>
        <span>{repeatValue}</span>
      </>
    );
  };

  const hasTableTimeliness = !!currentDetail?.timeliness;

  return (
    <Spin spinning={detailLoading}>
      <Layout className={styles['quality-problem-detail-wrap']}>
        <Content>
          <InnerBox>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <div
                  style={{ color: '#1980FF', fontWeight: 'bold', fontSize: 16 }}
                >
                  {tbrecord.tbname}
                </div>
                {!catalogMiddleTb && (
                  <div className={styles.problemDetailRange}>
                    <Select
                      variant="borderless"
                      style={{ width: 92, left: '0px', top: '2px' }}
                      value={currentMonth}
                      onChange={changeMonthRange}
                    >
                      {monthRange.map((item) => (
                        <Option value={item} key={item}>
                          {item}
                        </Option>
                      ))}
                    </Select>
                  </div>
                )}
              </Space>
              <div>
                <Space>
                  {/* 仅查看权限 不展示 数据更新 */}
                  {canEdit && (
                    <Tooltip title="点击此处对数据表进行数据更新可能需要一定时间">
                      <div style={{ cursor: 'default' }}>
                        {/* Button loading状态下Tooltip不生效，因此嵌套了一层div */}
                        <Button
                          type="default"
                          onClick={syncData}
                          loading={isLoad}
                        >
                          <IconFont type="icon-last-clock" text="数据更新" />
                        </Button>
                      </div>
                    </Tooltip>
                  )}
                  <Button type="default" onClick={detailBackToList}>
                    <IconFont type="icon-rollback" text="返回" />
                  </Button>
                </Space>
              </div>
            </div>
          </InnerBox>
          <Content isScroll style={{ padding: 0 }}>
            <div>
              {/* 基本信息 */}
              <FlexContent title="基本信息" />
              <div className={styles.verticalItemContent}>
                {/* 指标详情 */}
                {Object.entries(basicInfo).map((item) => {
                  return (
                    <div className={styles.contentInner}>
                      <div className={styles.itemName}>{`${item[0]}:`}</div>
                      <div style={{ maxWidth: '310px' }}>
                        <Text ellipsis={{ tooltip: item[1] }}>
                          {item[1] || '--'}
                        </Text>
                      </div>
                    </div>
                  );
                })}
              </div>

              {!ishistory && !catalogMiddleTb && (
                <>
                  {/* 表核验情况 */}
                  <FlexContent title="表核验情况" />
                  <div className={styles.chartsWrap}>
                    <div className={styles.chartsLeft}>
                      <ReactECharts
                        option={liquidChartOptions(
                          currentDetail?.rulerscore?.tableQualityScore,
                        )}
                        className={styles.liquidChart}
                        ref={liquidChartRef}
                      />
                      {/* <LiquidBox /> */}
                    </div>
                    <div className={styles.chartsRight}>
                      <div className={styles.innerLeft}>
                        <div className={styles.leftWrap}>
                          {Object.entries(tableVerifyStatus).map((item) => {
                            return (
                              <div className={styles.contentInner}>
                                <div className={styles.itemName}>
                                  {`${item[0]}:`}
                                </div>
                                <div style={{ color: 'rgba(25, 128, 255, 1)' }}>
                                  {getScore(item, currentDetail)}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                      <div className={styles.innerRight}>
                        <ReactECharts
                          className={styles.radarChart}
                          ref={radarChartRef}
                          option={radarChartOptions(
                            getRadarData(currentDetail?.rulerscore),
                          )}
                        />
                      </div>
                    </div>
                  </div>
                  <div className={styles.cardWrap}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <NumCard
                        cardType="fieldNum"
                        value={currentDetail?.verifyfieldnumber || 0}
                      />
                      <NumCard
                        cardType="ruleNum"
                        value={currentDetail?.verifyrulernumber || 0}
                      />
                      <NumCard
                        cardType="normalRecordNum"
                        value={currentDetail?.verifyrecord || 0}
                      />
                      <NumCard
                        cardType="problemRecordNum"
                        value={currentDetail?.problemrecord || 0}
                      />
                      <NumCard
                        cardType="passRate"
                        value={currentDetail?.problemqualifyrate || 0}
                      />
                    </div>
                  </div>
                  {hasTableTimeliness && (
                    <>
                      <div style={{ marginTop: 12, fontWeight: 'bold' }}>
                        表级时效性校验
                      </div>
                      {!!currentDetail?.timeliness?.showtimeliness && (
                        <div style={{ margin: '8px 0' }}>
                          表数据更新检测：
                          <span style={{ color: '#1980ff' }}>
                            {currentDetail?.timeliness?.tabletimelinessres
                              ? '有更新'
                              : '无更新'}
                          </span>
                        </div>
                      )}
                      <div style={{ color: '#999', display: 'flex' }}>
                        <span>
                          {currentDetail?.timeliness?.timelinesstype ===
                          'visiable'
                            ? '重复：'
                            : '正则表达式：'}
                        </span>
                        <span>{renderTimelinessText()}</span>
                      </div>
                    </>
                  )}

                  {/* 字段核验情况 */}
                  <FlexContent title="字段核验情况" />
                  {hasDataEntry ? (
                    <div>
                      <div style={{ marginBottom: '12px' }}>
                        {
                          DATA_ENTRY_MAP[
                            currentDetail?.duproles?.inputtype || 0
                          ]
                        }
                      </div>

                      <Space size={24}>
                        <div>
                          <span> 重复主键：</span>
                          <span
                            style={{
                              display: 'inline-block',
                            }}
                          >
                            {currentDetail?.duproles?.repetitionmainkey &&
                            currentDetail?.duproles?.repetitionmainkey?.length >
                              0 ? (
                              <TagsAdd
                                collapsible
                                wrapperWidth={300}
                                tags={
                                  currentDetail?.duproles.repetitionmainkey?.map(
                                    (item) => {
                                      return {
                                        name: item,
                                      };
                                    },
                                  ) || []
                                }
                              />
                            ) : (
                              '--'
                            )}
                          </span>
                        </div>
                        <div>
                          <span>处理方案：</span>
                          <span>全部放入问题库</span>
                        </div>
                        <div>
                          <span>重复数据量：</span>
                          <span style={{ color: '#1980FF' }}>
                            {currentDetail
                              ? `${currentDetail?.duproles.repetitionnumber}条`
                              : '--'}
                          </span>
                        </div>
                        <div>
                          <span>重复率：</span>
                          <span style={{ color: '#08B156' }}>
                            {currentDetail
                              ? `${currentDetail?.duproles.repetitionrate}%`
                              : '--'}
                          </span>
                        </div>
                      </Space>
                    </div>
                  ) : null}
                  <br />
                  <Table
                    dataSource={dataSource}
                    bordered
                    columns={columns}
                    rowKey="sID"
                    pagination={false}
                    scroll={{
                      y: 480, // 超过10条数据滚动
                    }}
                  />
                </>
              )}
            </div>
          </Content>
        </Content>
      </Layout>
    </Spin>
  );
};

export default QualityProblemDetail;
