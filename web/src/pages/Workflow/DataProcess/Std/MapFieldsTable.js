import React, { PureComponent } from 'react';
import { DropTarget } from 'react-dnd';

import { Input, Col, Row, Button, Tooltip } from 'antd';
import { isEmpty } from 'lodash';
// import Ellipsis from 'ant-design-pro/lib/Ellipsis';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import DSTable from '@/components/Antd/Table';
import { IconFont, InputSearch, Ellipsis } from '@/components';

import styles from './index.less';

import DropBodyRowItem from './include/DropBodyRowItem';

const STDCODETABLE_MAXLENGTH = 1000; // 代码表数据量过大时提示边界值

const Types = {
  card: 'row',
};

const targetSpec = {
  drop(props, monitor) {
    // dropTarget 行的值
    const { record } = props.children[0].props;
    // dragSource 行的值
    const dragItem = monitor.getItem();
    const { value, name } = dragItem;
    // console.log(props, dragItem, record, 'drop...拿到的dragSource');

    /*
     * TODO 处理数据
     * 每拖拽一次，修改该行的“映射标准”字段值，
     * 并修改外层 dataSource 值。
     * */
    // NOTE 预览版，在表格最后一行有一个可操作性行，设id为特殊值-1，该行不可被拖拽
    // 在左侧搜索且拖拽时，dataSource为搜索后的值数组，所以assign时要特殊处理一次
    // 25.04.29 id=-1的行已废弃
    if (record.id !== '-1') {
      Object.assign(
        props.dataSource.filter(
          (item) => Number(item.id) === Number(record.id),
        )[0],
        {
          mapval: [value, name],
        },
      );
    }

    // 更新 dataSource
    props.dropRow(props.dataSource);
  },
};

const DropBodyRow = DropTarget(Types.card, targetSpec, (connect, monitor) => {
  return {
    // 由 collect 函数返回的对象中的API，可以在组件中利用 this.props 获取
    connectDropTarget: connect.dropTarget(),
    isOver: monitor.isOver(),
    canDrop: monitor.canDrop(),
  };
})(DropBodyRowItem);

// DropTarget
// eslint-disable-next-line
// @DragDropContext(HTML5Backend)
@Form.create()
export default class MapFieldsTable extends PureComponent {
  components = {
    body: {
      row: DropBodyRow,
    },
  };

  // TODO 调用上层组件更新 state.dataSource
  dropRow = (data) => this.props.updateDropData(data);

  deleteMapField = (val, record) => {
    const { dataSource } = this.props;
    Object.assign(dataSource[record.id], {
      mapval: [],
    });
    this.dropRow(dataSource);
    this.forceUpdate();
    // resetSaveStatus();
  };

  /**
   * TODO 预览版，编辑代码表时的状态管理。编辑操作置于上层组件中，便于操作dataSource
   * 思路：
   *    1. 点编辑后，向表格数据追加一个标志位字段，用来区别当前是否处于编辑状态
   *    2. 失焦后，改变标志位值，从而改变编辑状态
   *    3. 保存时，再从表格数据中删除该标志位，以不影响保存接口的参数结构
   */
  changeAIEdit = (record) => this.props.getAITableEdit(record);

  // #15839(#11894)由于可追加行的代码表存在一非数据行，条数统计值需要特殊处理
  manualModifyCount = () => {
    const { dataSource } = this.props;
    const pageCountEle = document.getElementsByClassName(
      'ant-pagination-total-text',
    )?.[1];

    if (pageCountEle) {
      if (dataSource[dataSource.length - 1]?.id === -1) {
        pageCountEle.textContent = pageCountEle.textContent.replace(
          /\d+/g,
          dataSource.length - 1,
        );
      }
    }
  };

  componentDidUpdate() {
    this.manualModifyCount();
  }

  render() {
    const {
      allCanEdit,
      dataSource,
      currentAITableName,
      loading,
      form: { getFieldDecorator, getFieldValue, getFieldsValue },
      search,
      aiMapFinish,
      aiMapLoading,
      getInputAITableChange,
      getInputAITableFocus,
      addEditableItemRow,
      getAIAutoMapped,
      // resetSaveStatus,
    } = this.props;

    const columns = [
      {
        title: (
          <div className={styles.mapTitle}>
            <div>映射字段枚举值</div>
            <IconFont
              type="icon-plus-circle"
              onClick={addEditableItemRow}
              style={{ color: '#1980ff', curspor: 'pointer' }}
            />
          </div>
        ),
        dataIndex: 'value',
        skipCheckEmpty: true,
        width: '45%',
        render: (val, record) => {
          return (
            <div>
              {record.iseditall === 1 ? (
                <div className={styles.aiEditGroup}>
                  {getFieldDecorator(`${record.id}-pre`, {
                    initialValue: record.value,
                  })(
                    <Input
                      size="small"
                      style={{
                        width: 100,
                        borderRadius: 4,
                        marginRight: 5,
                      }}
                      placeholder="代码值"
                      autoFocus={record.iseditall}
                      onFocus={() => getInputAITableFocus(record, 0)}
                      onBlur={() =>
                        getInputAITableChange(
                          record,
                          record.id,
                          getFieldsValue(),
                        )
                      }
                    />,
                  )}
                </div>
              ) : (
                <div className={styles.stdCodeTbVal}>
                  <Ellipsis
                    tooltip={!isEmpty(val)}
                    lines={1}
                    isAuto
                    style={{ maxWidth: 80 }}
                  >
                    {isEmpty(val) ? '--' : val}
                  </Ellipsis>
                  {/* 已选择推荐代码表后，不支持删除编辑操作；只有新增加行的代码表才可以删除编辑（意即空值的行也不可以删除编辑） */}
                  {!currentAITableName && record?.isextra /* &&
                  record.value === '' &&
                  record.name === '' */ ? (
                    <div className={styles.aiBtnGroup}>
                      <IconFont
                        type="icon-edit"
                        onClick={() => this.changeAIEdit(record)}
                      />
                      <IconFont
                        type="icon-delete"
                        onClick={() => getInputAITableChange(record)}
                      />
                    </div>
                  ) : null}
                </div>
              )}
            </div>
          );
        },
      },
      {
        title: (
          <div className={styles.mapTitle}>
            <div>映射的代码名称 | 代码值</div>
            <Tooltip title="请从右侧选择代码名称|代码值！">
              <IconFont type="icon-question-circle" />
            </Tooltip>
          </div>
        ),
        dataIndex: 'mapval',
        skipCheckEmpty: true,
        // width: '38%',
        render: (val, record) => {
          const obj = {
            children: (
              <div className={styles.stdCodeTbStd}>
                <Ellipsis
                  tooltip={val[1] && val[0]}
                  lines={1}
                  isAuto
                  style={{ maxWidth: 60 }}
                >
                  {val[1] || '--'}
                </Ellipsis>
                <span> | </span>
                <Ellipsis
                  tooltip={val[1] && val[0]}
                  lines={1}
                  isAuto
                  style={{ maxWidth: 60 }}
                >
                  {val[0] || '--'}
                </Ellipsis>
                {allCanEdit &&
                val &&
                val.length &&
                val[0] !== '' &&
                val[1] !== '' ? (
                  <IconFont
                    type="icon-close-circle"
                    style={{
                      color: '#DB3E02',
                      cursor: 'pointer',
                      marginLeft: 8,
                    }}
                    onClick={() => this.deleteMapField(val, record)}
                  />
                ) : null}
              </div>
            ),
            props: {},
          };
          // if (Number(record.id) === -1) {
          // obj.props.colSpan = 0;
          // }
          return obj;
        },
      },
    ];

    return (
      <Col style={{ width: 445, marginRight: 24 }}>
        <Row>
          <Col span={12} style={{ marginBottom: 8 }}>
            <InputSearch
              onSearch={search}
              allowClear
              // onChange={search} // InputSearch废弃该API
              style={{ width: '100%' }}
              placeholder="请输入搜索内容"
            />
          </Col>
          <Col span={12} style={{ textAlign: 'right', marginBottom: 8 }}>
            {aiMapFinish ? (
              <IconFont
                type="icon-check-circle"
                style={{ color: '#08B156', marginRight: 8 }}
              />
            ) : null}
            <Button
              type="primary"
              onClick={getAIAutoMapped}
              loading={aiMapLoading}
              disabled={getFieldValue('codesetid') === undefined}
            >
              智能映射
            </Button>
          </Col>
        </Row>
        <Row>
          <DSTable
            // key="table1"
            key={dataSource.length}
            // rowKey="id"
            loading={loading || aiMapLoading}
            columns={columns}
            scroll={{ y: 192 }}
            style={{ width: '100%', minHeight: 288 }}
            className={styles.mapFieldsTable}
            dataSource={dataSource}
            components={this.components}
            onRow={() => ({
              // 复写行属性
              dataSource,
              dropRow: this.dropRow,
            })}
            rowSelection={null}
            pagination={{
              pageSize: 4,
              style: {
                marginTop: dataSource?.length > STDCODETABLE_MAXLENGTH ? 0 : 16,
              },
              // total: dataSource?.filter(({ id }) => id >= 0)?.length,
              total: dataSource.length,
              showSizeChanger: false,
            }}
            footer={() =>
              dataSource?.length > STDCODETABLE_MAXLENGTH ? (
                <IconFont
                  type="icon-exclamation-circle"
                  text="当前选择字段代码值过多，有可能不是代码字段。"
                  style={{
                    fontSize: 12,
                    lineHeight: '16px',
                    color: '#ea1e1e',
                  }}
                />
              ) : null
            }
          />
        </Row>
      </Col>
    );
  }
}
