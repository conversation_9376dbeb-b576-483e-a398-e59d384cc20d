import React, { useState } from 'react';
import { Form, Checkbox, Tooltip, notification } from 'antd';
import { useRequest } from '@/utils';
import { RadioButton } from '@dana/ui';
import { cloneDeep, isEmpty, assign, map } from 'lodash';

import { ContentModal, DSIcon } from '@/components';
import { IPLISTTYPES } from '@/pages/DataService/ServiceManage/Service/API/SecureConfig';
import IPTable from '@/pages/DataService/ServiceManage/Service/API/IPTable';
// import styles1 from '@/pages/DataService/ServiceManage/Service/API/index.less';

import styles from './index.less';

const { Item } = Form;

interface RuleModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  handleRefresh: () => void;
  setSelectedRows: (param) => void;
  selectedRows: any[];
}

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 15 },
  },
};

const STAModal = (props: RuleModalProps) => {
  const { visible, selectedRows } = props;
  const [form] = Form.useForm();
  const [ipDataList, setIpDataList] = useState<any>([]);

  const { run: setSTA, loading } = useRequest('service/apiaccess/set', {
    onSuccess: (data) => {
      if (data.code === 200) {
        handleCancel();
        notification.success({ message: '设置黑白名单成功' });
        props.handleRefresh();
        props.setSelectedRows([]);
      } else {
        notification.error({
          message: `设置黑白名单失败`,
        });
      }
    },
  });

  const onBlur = (r) => {
    form
      ?.validateFields([`ip_${r?.idx}`])
      .then((val) => {
        const ipList = cloneDeep(ipDataList);
        Object.assign(ipList[r.idx - 1], {
          ...ipList[r.idx - 1],
          ip: val[`ip_${r?.idx}`],
          edited: true,
          default: false,
        });
        setIpDataList(ipList);
        // updateStateToProps({ ipDataList: ipList });
      })
      .catch((errorInfo) => {
        const ipList = cloneDeep(ipDataList);
        Object.assign(ipList[r.idx - 1], {
          ...ipList[r.idx - 1],
          ip: errorInfo.values[`ip_${r?.idx}`],
          createtime: '',
          edited: false,
          default: false,
        });
        setIpDataList(ipList);
        // updateStateToProps({ ipDataList: ipList });
      });

    setTimeout(() => {
      const ipValues = form?.getFieldsValue(
        ipDataList.map((x) => `ip_${x.idx}`),
      );
      // 校验其他字段
      let f = false;
      form?.validateFields(
        Object.keys(ipValues).filter((x) => {
          // 找到参数列表中第一个与当前参数重名的参数（自己除外）
          if (
            ipValues[x] === ipValues[`ip_${r.idx}`] &&
            x !== `ip_${r?.idx}` &&
            !f
          ) {
            f = true;
            return false;
          } else {
            return !isEmpty(ipValues[x]);
          }
        }),
      );
    }, 0);
  };

  const getInitIp = (idx?, isDefault?) => ({
    idx: idx ?? 1,
    ip: undefined,
    createtime: '',
    edited: false, // 是否已编辑完
    default: isDefault ?? false, // 是否是默认第一个
  });

  const addIpList = () => {
    const temp = [...ipDataList, getInitIp(ipDataList.length + 1, false)];
    setIpDataList(temp);
    // updateStateToProps({ ipDataList: temp });
    form?.setFieldsValue({
      [`ip_${ipDataList.length + 1}`]: undefined,
    });
  };

  const deleteIpList = (r) => {
    const list = cloneDeep(ipDataList);
    const newList = list
      .filter((v) => v.idx !== r.idx)
      .map((v, i) => ({ ...v, idx: i + 1 }));
    setIpDataList(newList);
    // updateStateToProps({ ipDataList: newList });
    const fields = newList.map((v) => ({
      [`ip_${v.idx}`]: v.ip,
    }));
    form?.setFieldsValue(assign(...map(fields, (obj) => obj)));
  };

  const handleOK = () => {
    form?.validateFields().then((values) => {
      const apiList = selectedRows.filter((v) => v.serviceType === 'API共享');
      // console.log(11, selectedRows, apiList);
      const idList = apiList.map((v) => v.id);
      const payload = {
        ...values,
        serviceids: idList,
        iplist: ipDataList.filter((v) => v.ip).map((v) => v.ip),
        addappip: values.addappip || false,
      };
      // console.log(payload);
      setSTA(payload);
    });
  };

  function handleCancel() {
    form.resetFields();
    setIpDataList([]);
    props.setVisible(false);
  }

  const handleChangeType = () => {
    form.setFieldsValue({
      addappip: false,
    });
  };

  return (
    <ContentModal
      title="黑白名单"
      visible={visible}
      onCancel={handleCancel}
      onOk={handleOK}
      loading={loading}
      modalSize="middle"
    >
      <div className={styles.staModal}>
        <Form form={form} {...formItemLayout}>
          <Item
            name="apiiptype"
            label="黑/白名单"
            initialValue={0}
            style={{ marginTop: 16 }}
            // rules={[{ required: allowEdit }]}
          >
            <RadioButton options={IPLISTTYPES} onChange={handleChangeType} />
          </Item>
          <Form.Item
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.apiiptype !== currentValues.apiiptype
            }
            noStyle
          >
            {({ getFieldValue }) => {
              const notSet = getFieldValue('apiiptype') === 0;
              const isWhite = getFieldValue('apiiptype') === 2;
              if (notSet) {
                return null;
              }

              return (
                <div style={{ position: 'relative' }}>
                  {isWhite && (
                    <Form.Item noStyle>
                      <Form.Item
                        name="addappip"
                        initialValue={false}
                        valuePropName="checked"
                        label={
                          <span style={{ color: 'transparent' }}>复选框</span>
                        }
                        colon={false}
                      >
                        <Checkbox>将审核通过的应用服务IP加入到白名单</Checkbox>
                      </Form.Item>
                      <Tooltip title="勾选该配置项，审核通过的应用服务IP会加入到白名单">
                        <DSIcon
                          type="icon-question-circle"
                          style={{ position: 'absolute', top: 5, right: 350 }}
                        />
                      </Tooltip>
                    </Form.Item>
                  )}
                  <div className={styles.apiDetailContentWrapper}>
                    <IPTable
                      id="ip_table"
                      ipListType={getFieldValue('apiiptype')}
                      dataSource={ipDataList}
                      onBlur={onBlur}
                      addIpList={addIpList}
                      deleteIpList={deleteIpList}
                      showCreatetime={false}
                    />
                  </div>
                </div>
              );
            }}
          </Form.Item>
        </Form>
      </div>
    </ContentModal>
  );
};

export default STAModal;
