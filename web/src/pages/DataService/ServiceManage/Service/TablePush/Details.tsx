/* eslint-disable @typescript-eslint/naming-convention */
/** ************** 库表推送详情 ***************** */
import { Ellipsis, FieldMap, FormBundle, Table, TagsAdd } from '@dana/ui';
import { Form, Tooltip } from 'antd';
import { RunningSetting, DepJobSetting } from '@/components';
import { fieldTableColumns, PAGINATION, TIME_FORMAT_AUTO } from '../../conf';
import { ellipsisStyle } from '../../utils/dealData';
import { BasicView } from '../BasicInfo';
import { DataSourceView } from '../DataSource';
import { Block, ViewCol, ViewRow } from '../Layout';
import AdvancedSetup from './AdvancedSetup';
import styles from './index.less';
import { checkDisabledPrimkeyFields } from '../../utils';

const { resToInitialValue: resToTimeout } = FormBundle.timeoutStrategy;
const { resToInitialValue } = FormBundle.exeStrategy;
// const getPreTaskNameById = (treeList, id = '') => {
//   for (const list of treeList) {
//     if (list.value === id) {
//       return list.title;
//     }
//     if (list.children) {
//       const result = getPreTaskNameById(list.children, id);
//       if (result) {
//         return result;
//       }
//     }
//   }
//   return '';
// };

const FORM_LAYOUT = {
  labelCol: { span: 2 },
  wrapperCol: { span: 22 },
};

const Details = ({ data }) => {
  const pushDetail = data.pushserver;
  const advancedconfig = data?.pushserver?.advancedconfig;
  const [form] = Form.useForm();
  const isRan = pushDetail.ranstatus; // 是否运行过

  const leftData = pushDetail.leftDataSource?.map((v: any) => ({
    ...v,
    isPrim: v?.isprim,
    isUnique: v?.isunique,
    isPartition: v?.ispartition,
    partitionNumber: v?.partitionnumber,
  }));
  const rightData = pushDetail.rightDataSource
    ?.filter((v) => v.fieldName !== '')
    .map((v) => ({
      ...v,
      disabled: checkDisabledPrimkeyFields(pushDetail.dbtype, v.fieldType),
      isPrim: v?.isprim,
      isUnique: v?.isunique,
      isPartition: v?.ispartition,
      partitionNumber: v?.partitionnumber,
    }));
  const {
    // ----
    assignnode: assignNode,
    priority,

    retry,
    retryInterval,
    retryDisable,
    retryinterval,
    retrynum,
  } = pushDetail;
  const {
    exectimes,
    scheduletype,
    scheduledetail,

    crossdep,
    crossforce,
    updep,
    depjobinfo,
    effectstart,
    effectend,
    timeoutstop,
    timeoutduration,
  } = pushDetail?.pushstrategy || {};
  const isMessage = data.sourcedatatype === 1;
  const schedule = isMessage
    ? null
    : resToInitialValue({
        schedule: scheduletype,
        scheduleDetail: scheduledetail,
        execTimes: exectimes,
        effectstart,
        effectend,
      });

  const timeoutStrategy = resToTimeout({
    timeoutstop,
    timeoutduration,
  });

  return (
    <>
      <Block title="基础信息">
        <BasicView data={data} />
      </Block>

      <Block title="数据来源">
        <DataSourceView data={data} />
      </Block>

      <Block title="服务配置">
        <ViewRow>
          <ViewCol title="服务类型">
            {
              {
                1: 'API共享',
                2: '库表推送',
                3: '离线文件',
              }[data.type]
            }
          </ViewCol>
        </ViewRow>
        <ViewRow>
          <ViewCol title="数据库">{pushDetail.dbname}</ViewCol>
          <ViewCol title="表名称">{pushDetail.tablename}</ViewCol>
          {/* {pushDetail?.describe && (
            <ViewCol title="表描述">{pushDetail.describe}</ViewCol>
          )} */}
        </ViewRow>
      </Block>
      <Block title="字段信息">
        <Table
          className="data-service-filed-table-view-row"
          columns={fieldTableColumns}
          dataSource={rightData}
          custom
          pagination={PAGINATION}
        />
      </Block>
      <Block title="字段映射" style={{ marginTop: 24 }}>
        <ViewRow className="data-service-fieldmap-row">
          <FieldMap
            status="view"
            viewsInfo={{
              columns: {
                left: [
                  {
                    title: '序号',
                    dataIndex: 'number',
                    width: 20,
                    render: (val) => {
                      return <div style={{ width: 20 }}>{Number(val)}</div>;
                    },
                  },
                  {
                    title: '数据源字段名',
                    dataIndex: 'fieldName',
                    // width: 106,
                    render: (_, record) => {
                      return (
                        <div
                          style={{
                            width: '106px',
                            ...ellipsisStyle,
                          }}
                        >
                          <FieldMap.RenderFieldIcons record={record} />
                        </div>
                      );
                    },
                  },
                  {
                    title: '字段类型',
                    dataIndex: 'fieldType',
                    // width: 106,
                    render: (text) => {
                      return (
                        <div
                          style={{
                            width: '106px',
                            ...ellipsisStyle,
                          }}
                        >
                          <Tooltip title={text}>{text}</Tooltip>
                        </div>
                      );
                    },
                  },
                  {
                    title: '字段注释',
                    dataIndex: 'comment',
                    // width: 106,
                    render: (text) => {
                      if (text) {
                        return (
                          <div
                            style={{
                              width: '106px',
                              ...ellipsisStyle,
                            }}
                          >
                            <Tooltip title={text}>{text}</Tooltip>
                          </div>
                        );
                      } else {
                        return '--';
                      }
                    },
                  },
                ],
                right: [
                  {
                    title: '序号',
                    dataIndex: 'number',
                    width: 20,
                    render: (val) => {
                      return <div style={{ width: '20px' }}>{Number(val)}</div>;
                    },
                  },
                  {
                    title: '目标端字段名',
                    dataIndex: 'fieldName',
                    // width: 106,
                    render: (_, record) => {
                      return (
                        <div
                          style={{
                            width: '106px',
                            ...ellipsisStyle,
                          }}
                        >
                          <FieldMap.RenderFieldIcons record={record} />
                        </div>
                      );
                    },
                  },
                  {
                    title: '字段类型',
                    dataIndex: 'fieldType',
                    // width: 106,
                    render: (text) => {
                      return (
                        <div
                          style={{
                            width: '106px',
                            ...ellipsisStyle,
                          }}
                        >
                          <Tooltip title={text}>{text}</Tooltip>
                        </div>
                      );
                    },
                  },
                  {
                    title: '字段注释',
                    dataIndex: 'comment',
                    // width: 106,
                    render: (text) => {
                      if (text) {
                        return (
                          <div
                            style={{
                              width: '106px',
                              ...ellipsisStyle,
                            }}
                          >
                            <Tooltip title={text}>{text}</Tooltip>
                          </div>
                        );
                      } else {
                        return '--';
                      }
                    },
                  },
                ],
              },
              tableRelation: {
                edges: pushDetail.tablerelation.edges,
                // 模拟保存的是非顺序关联
                ...(pushDetail?.issort && {
                  sortRules: {
                    left: leftData.map((v) => v.fieldName),
                    right: rightData?.map((v) => v.fieldName),
                  },
                }),
              },
              leftDataSource: leftData,
              rightDataSource: rightData,
              isShowSort: !pushDetail?.needcreate,
            }}
          />
        </ViewRow>
      </Block>

      <Block title="推送策略" style={{ marginTop: 24 }}>
        <ViewRow>
          <ViewCol title="更新方式" className="data-service-strategy-view-col">
            {{ 1: '全量', 2: '增量' }[pushDetail.updatestrategy]}
          </ViewCol>
          {pushDetail.updatestrategy === 2 && !isMessage ? (
            <>
              <ViewCol title="增量字段">
                {/* {pushDetail.updatefield.fieldname} */}
                <Ellipsis
                  tooltip
                  lines={1}
                  isResize
                  style={{ maxWidth: '200px' }}
                >
                  {pushDetail.updatefield.fieldname}
                </Ellipsis>
              </ViewCol>
              <ViewCol
                title={
                  pushDetail.updatefield.fieldtype.includes('yyyy')
                    ? '时间格式'
                    : '增量类型'
                }
              >
                {pushDetail.updatefield.fieldtype === TIME_FORMAT_AUTO
                  ? '自动识别'
                  : pushDetail.updatefield.fieldtype}
              </ViewCol>
              <ViewCol
                title="增量起始值"
                className="data-service-strategy-view-col"
              >
                {isRan || pushDetail?.syncsourcedata === 2
                  ? pushDetail?.updatefield?.startvalue
                  : '全部历史数据'}
              </ViewCol>
              <ViewCol title="过滤条件">{pushDetail.wherecontent}</ViewCol>
              <ViewCol title="数据策略">
                {
                  { 1: isMessage ? '更新' : '覆盖', 2: '追加', 3: '更新' }[
                    pushDetail.datastrategy
                  ]
                }
              </ViewCol>
              {pushDetail.datastrategy === 1 && !isMessage && (
                <div style={{ marginBottom: 12 }}>
                  <ViewCol title="覆盖策略">
                    <span>{pushDetail.sql ? '条件覆盖' : '全表覆盖'}</span>
                  </ViewCol>
                  {pushDetail.sql && (
                    <ViewCol title=" ">
                      <span>{pushDetail.sql}</span>
                    </ViewCol>
                  )}
                </div>
              )}
              {pushDetail.datastrategy === 2 && (
                <ViewCol title="存储表历史数据">
                  {isRan
                    ? '不清空'
                    : { 1: '清空', 2: '不清空' }[pushDetail.clearstoredata]}
                </ViewCol>
              )}
              {pushDetail.datastrategy === 3 && (
                <ViewCol title="更新键">
                  <TagsAdd
                    tags={
                      pushDetail?.serviceprimkey?.length
                        ? pushDetail?.serviceprimkey
                        : pushDetail?.rightDataSource
                            ?.filter((v) => v?.isprim || v?.isunique)
                            ?.map((v) => v?.fieldName) ?? []
                    }
                    num={3}
                    limit={12}
                    style={{ marginBottom: 8 }}
                  />
                </ViewCol>
              )}
            </>
          ) : (
            <>
              {!isMessage && (
                <ViewCol title="过滤条件">{pushDetail?.wherecontent}</ViewCol>
              )}
              <ViewCol title="数据策略">
                {
                  { 1: isMessage ? '更新' : '覆盖', 2: '追加', 3: '更新' }[
                    pushDetail.datastrategy
                  ]
                }
              </ViewCol>
              {pushDetail.datastrategy === 1 && !isMessage && (
                <div style={{ width: '33.3%% !important' }}>
                  <ViewCol title="覆盖策略" span={24}>
                    <span>{pushDetail.sql ? '条件覆盖' : '全表覆盖'}</span>
                  </ViewCol>
                  {pushDetail.sql && (
                    <ViewCol title=" " span={24}>
                      <span>{pushDetail.sql}</span>
                    </ViewCol>
                  )}
                </div>
              )}
              {pushDetail.datastrategy === 3 && !isMessage && (
                <ViewCol title="更新键">
                  <TagsAdd
                    tags={
                      pushDetail?.serviceprimkey?.length
                        ? pushDetail?.serviceprimkey
                        : pushDetail?.rightDataSource
                            ?.filter((v) => v?.isprim || v?.isunique)
                            ?.map((v) => v?.fieldName) ?? []
                    }
                    num={3}
                    limit={12}
                    style={{ marginBottom: 8 }}
                  />
                </ViewCol>
              )}
            </>
          )}
        </ViewRow>
      </Block>
      {/* <Block title="调度策略" >
        <ScheduleViews data={pushDetail} />
      </Block> */}
      <Form {...FORM_LAYOUT}>
        {!isMessage && (
          <>
            <Block title="调度策略配置" className={styles.scheduleDetails}>
              <FormBundle
                scene="exeStrategy"
                status="view"
                viewInfos={schedule}
                sceneConfig={{ startType: 'effectTime' }}
              />
            </Block>
            <Block title="调度依赖配置" style={{ marginTop: 12 }}>
              <div>
                <DepJobSetting
                  form={form}
                  ref={null}
                  canEdit={false}
                  curJob={{ id: data?.id }}
                  initialValues={{
                    crossdep,
                    crossforce,
                    updep,
                    depjobinfo: data?.depjoblist || depjobinfo,
                  }}
                />
              </div>
            </Block>
          </>
        )}
        <Block title="运行配置" style={{ marginTop: 12 }}>
          <RunningSetting
            status="view"
            isMessage={isMessage}
            viewInfos={{
              priority,
              node: assignNode,
              retry: isMessage
                ? {
                    retry: retrynum,
                    retryInterval: retryinterval,
                  }
                : { retry, retryInterval, retryDisable },
              timeoutStrategy,
            }}
          />
        </Block>
        {!isMessage && (
          <AdvancedSetup
            type="detail"
            advancedconfig={advancedconfig}
            dbtype={data?.pushserver?.dbtype}
          />
        )}
      </Form>
    </>
  );
};

export default Details;
