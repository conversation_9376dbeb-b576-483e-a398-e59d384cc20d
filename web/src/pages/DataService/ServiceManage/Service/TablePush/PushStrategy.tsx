import { useContext, useEffect, useState } from 'react';
import { Form, Col, Tooltip, Input, notification, Select, Button } from 'antd';
import { useSelector, useDispatch } from 'umi';
import { ConnectState } from '@/models/connect.d';
import moment from 'moment';
import { RadioButton, IconFont, TagsAdd } from '@dana/ui';
import { FormRow } from '../Layout';
import { ServiceContext } from '../index';
import Increment from './Increment';
import {
  DATASTRTEGY_TIP,
  MESSAGE_DATASTRTEGY_TIPDATASTRTEGY_TIP,
  STORE_TIP,
  TIME_FORMAT_AUTO,
  TIME_FORMATE,
  WHERECONTENT_TIP,
  UPDATEWAY_TIP,
} from '../../conf';
import { checkDisabledPrimkeyFields } from '../../utils';
import type { FormValuesType } from '../../index.d';
import styles from './index.less';
import { useLoading } from '../../hooks';

const { Search } = Input;

// *** 推送策略 ***
const PushStrategy = () => {
  const Context = useContext(ServiceContext);
  const { form, isChange, detailInfo, isInitialized } = Context;
  const dispatch = useDispatch();
  const { sqlTestLoading } = useLoading();
  const [isWhereValid, setIsWhereValid] = useState(true);
  const [sql, setSql] = useState(detailInfo?.pushserver?.wherecontent || '');

  const {
    pushFiledsInfo,
    newTableSelectedKeys,
    curDB,
    existTbFieldsInfo,
    rightFieldInfo,
    newRightFieldInfo,
  } = useSelector((state: ConnectState) => ({
    pushFiledsInfo: state.serviceManage.pushFiledsInfo,
    newTableSelectedKeys: state.serviceManage.newTableSelectedKeys,
    curDB: state.serviceManage.curDB,
    rightFieldInfo: state.serviceManage.rightFieldInfo,
    existTbFieldsInfo: state.serviceManage.rightFieldInfo,
    newRightFieldInfo: state.serviceManage.newRightFieldInfo,
  }));

  const [increFields, setIncreFields] = useState<typeof pushFiledsInfo>([]);

  useEffect(() => {
    // if (form?.getFieldValue('dataObj') === 'old') {
    //   setIncreFields(pushFiledsInfo)
    // } else {
    //   setIncreFields(pushFiledsInfo.filter((item) => newTableSelectedKeys.includes(item.fieldName)))
    // }
    // *fix:17540 【SIT2】新建库表推送到已有表服务的增量下拉框字段未勾选的也进行了展示
    setIncreFields(
      pushFiledsInfo.filter((item) =>
        newTableSelectedKeys.includes(item.fieldName),
      ),
    );
  }, [form?.getFieldValue('dataObj'), newTableSelectedKeys, pushFiledsInfo]);

  // 获取数据库类型（是否支持设置更新键，1只展示，2可修改，0不支持）
  const getUpdateStrategyType = () => {
    const MYSQLTYPES = ['mysql', 'stork', 'postgres'];
    const ORACLETYPES = ['oracle', 'sqlserver', 'SQL server', 'dameng'];
    return MYSQLTYPES.includes(
      (curDB?.parent || curDB?.dataRef || curDB)?.dbtype,
    )
      ? 1
      : ORACLETYPES.includes((curDB?.parent || curDB?.dataRef)?.dbtype)
      ? 2
      : 0;
  };

  /** 全量默认的数据策略为覆盖，增量默认的数据策略为追加 */
  const changeUpdateWay = (e) => {
    const {
      target: { value },
    } = e;
    form?.setFieldsValue({ strategy: value });
  };

  function whereChange(e) {
    setSql(e.target.value);
    // *输入内容发生变化后，需要重新校验
    dispatch({
      type: 'serviceManage/updateState',
      payload: { whereHasChecked: false },
    });
  }
  // *sql合法性校验
  function getSqlTest(value) {
    dispatch({
      type: 'serviceManage/updateState',
      payload: { whereHasChecked: true },
    });

    const isSchema = !Object.prototype.hasOwnProperty.call(curDB, 'haveschema');

    if (value) {
      dispatch({
        type: 'serviceManage/getSqlTest',
        payload: {
          // dbname: form?.getFieldValue('database'),
          schema: isSchema ? curDB?.schema : '',
          // tbname: curTableName,
          sqltxt: value,
          sourcetbtype: form?.getFieldValue('sourcetbtype'),
          sourcedatatype: form?.getFieldValue('sourcedatatype'),
          sourcetbid: isChange
            ? form?.getFieldValue('sourceTableId')
            : detailInfo?.sourcetbid,
        },
        callback: (res) => {
          const { code, result } = res;
          if (code === 200 && result) {
            setIsWhereValid(true);
            notification.success({
              message: 'where语句检测通过',
            });
            form?.validateFields([['wherecontent']]);
          } else {
            setIsWhereValid(false);
            form?.validateFields([['wherecontent']]);
          }
        },
      });
    }
  }
  const validateWhere = (_, val) => {
    if (!isWhereValid && val) {
      return Promise.reject(new Error('where语句不合法'));
    } else {
      return Promise.resolve();
    }
  };

  // 统计已有表/新建表当前的主键字段
  const getPrimKeys = (): string[] => {
    const curTbtype = form?.getFieldValue('dataObj');

    if (curTbtype === 'old') {
      // return isInitialized
      // ? detailInfo?.pushserver?.serviceprimkey ?? []
      return existTbFieldsInfo
        ?.filter((v: any) => v?.isprim || v?.isunique)
        ?.map((v: any) => v?.fieldName);
    } else if (curTbtype === 'new') {
      return (
        newRightFieldInfo
          ?.filter((v: any) => v?.isPrim)
          ?.map((v: any) => v?.fieldName) ?? []
      );
    } else {
      return [];
    }
  };

  // 统计更新键字段
  const getUpdateKeys = (): string[] => {
    const curTbtype = form?.getFieldValue('dataObj');

    if (curTbtype === 'old') {
      const existPrimKeys = existTbFieldsInfo
        ?.filter(
          (v: any) =>
            v?.isprim || (getUpdateStrategyType() === 1 ? v?.isunique : false),
        )
        ?.map((v: any) => v?.fieldName);

      return isInitialized
        ? detailInfo?.pushserver?.serviceprimkey || existPrimKeys
        : existPrimKeys; // mysql系已有表(不支持修改主键及更新键)默认展示主键或唯一键
    } else if (curTbtype === 'new') {
      return (
        newRightFieldInfo
          ?.filter((v: any) => v?.isPrim)
          ?.map((v: any) => v?.fieldName) ?? []
      );
    } else {
      return [];
    }
  };

  // 可设置更新键的数据库的表的字段
  const getUpdateKeysOptions = () => {
    const curTbtype = form?.getFieldValue('dataObj');
    const curTbid = form?.getFieldValue('tableId');
    let fields = [];
    if (curTbtype === 'old' && curTbid) {
      fields = existTbFieldsInfo;
    } else if (curTbtype === 'new') {
      fields = newRightFieldInfo;
    }

    return (
      fields?.map((v: any) => ({
        label: v.fieldName,
        value: v.fieldName,
        disabled: checkDisabledPrimkeyFields(
          (curDB?.parent || curDB?.dataRef)?.dbtype,
          v.fieldType,
        ),
      })) ?? []
    );
  };

  // 主键更新后需同步更新更新键
  useEffect(() => {
    const curTbtype = form?.getFieldValue('dataObj');
    const curTbid = form?.getFieldValue('tableId');

    // 更新策略
    if (form?.getFieldValue('strategy') === 3) {
      // 只展示主键
      if (!getPrimKeys()?.length && getUpdateStrategyType() === 1) {
        form?.setFieldsValue({ strategy: form?.getFieldValue('updateWay') });
      }
      // 可修改主键
      if (
        getUpdateStrategyType() === 2 &&
        ((curTbtype === 'old' && curTbid) || curTbtype === 'new')
      ) {
        // const curkeys = form?.getFieldValue('updatekeys') ?? [];
        form?.setFieldsValue({
          // updatekeys: Array.from(new Set([...curkeys, ...getPrimKeys()])),
          // updatekeys: Array.from(new Set([...[], ...getPrimKeys()])),
        });
      }
    }
  }, [JSON.stringify(getPrimKeys())]); // 由于在Checkbox.onChange中更新了更新键，此处可考虑废弃

  useEffect(() => {
    if (isInitialized) return;

    const objType = form?.getFieldValue('dataObj');
    if (objType === 'old') {
      form?.setFieldsValue({
        updatekeys:
          existTbFieldsInfo
            ?.filter((v: any) => v?.isprim)
            ?.map((v: any) => v?.fieldName) || undefined,
      });
    }
  }, [
    form?.getFieldValue('database'),
    form?.getFieldValue('tableId'),
    existTbFieldsInfo,
  ]);

  useEffect(() => {
    if (isInitialized) return;

    const objType = form?.getFieldValue('dataObj');
    // !!!新建表更换数据库是否需要重置更新键有待商榷
    if (objType === 'new') {
      form?.setFieldsValue({
        updatekeys:
          newRightFieldInfo
            ?.filter((v: any) => v?.isPrim)
            ?.map((v: any) => v?.fieldName) || undefined,
      });

      if (!getPrimKeys()?.length && getUpdateStrategyType() === 1) {
        form?.setFieldsValue({ strategy: form?.getFieldValue('updateWay') });
      }
    }
  }, [form?.getFieldValue('database'), newRightFieldInfo]);

  useEffect(() => {
    // 切库时重置数据策略默认值
    const tableid = form?.getFieldValue('tableId');
    if (!tableid) {
      form?.setFieldsValue({ strategy: form?.getFieldValue('updateWay') });
    }
  }, [form?.getFieldValue('tableId')]);

  // 是否消息流
  const isMessage = form?.getFieldValue('sourcedatatype') === 1;
  const canAppend =
    isMessage &&
    detailInfo &&
    rightFieldInfo?.find(
      (item) => item.fieldName === 'op' || item.fieldName === 'op_ts',
    );
  return (
    <FormRow>
      <>
        <Col span={8} className={styles.formItemCol}>
          <Form.Item label="更新方式">
            <Form.Item name="updateWay" initialValue={1} noStyle>
              <RadioButton
                options={[
                  { label: '全量', value: 1 },
                  { label: '增量', value: 2 },
                ]}
                onChange={changeUpdateWay}
              />
            </Form.Item>
            {isMessage && (
              <Tooltip title={UPDATEWAY_TIP}>
                <IconFont
                  type="icon-question-circle"
                  style={{ marginLeft: 8 }}
                />
              </Tooltip>
            )}
          </Form.Item>
        </Col>
        {!isMessage && (
          <Form.Item
            noStyle
            shouldUpdate={(pre, cur) => pre.updateWay !== cur.updateWay}
          >
            {({ getFieldValue }) => {
              return (
                getFieldValue('updateWay') === 2 && (
                  <Increment
                    form={form}
                    fields={increFields}
                    ranstatus={detailInfo?.ranstatus}
                    startvalue={detailInfo?.pushserver?.updatefield?.startvalue}
                  />
                )
              );
            }}
          </Form.Item>
        )}
      </>
      {!isMessage && (
        <Col span={8}>
          <Form.Item label="过滤条件">
            <Form.Item
              noStyle
              name="wherecontent"
              rules={[{ validator: validateWhere }]}
            >
              <Search
                addonBefore="where"
                loading={sqlTestLoading}
                placeholder="请输入标准SQL中where关键字后的筛选条件(不包括where)，支持使用全局参数"
                enterButton={
                  <Button type="primary" disabled={!(sql || '').trim()}>
                    检测
                  </Button>
                }
                onChange={whereChange}
                onSearch={getSqlTest}
                className={styles.whereContent}
              />
            </Form.Item>
            <Tooltip title={WHERECONTENT_TIP}>
              <IconFont
                type="icon-question-circle"
                style={{
                  position: 'absolute',
                  zIndex: 3,
                  marginLeft: '8px',
                }}
              />
            </Tooltip>
          </Form.Item>
        </Col>
      )}
      <>
        <Col span={8} className={styles.formItemCol}>
          <Form.Item label="数据策略">
            <Form.Item name="strategy" initialValue={1} noStyle>
              <RadioButton
                buttonWidth={60}
                options={
                  getUpdateStrategyType() && !isMessage
                    ? [
                        { label: '覆盖', value: 1 },
                        { label: '追加', value: 2 },
                        {
                          label: '更新',
                          value: 3,
                          disabled:
                            getUpdateStrategyType() === 1 &&
                            !getPrimKeys()?.length,
                          toolTip: {
                            title:
                              getUpdateStrategyType() === 1
                                ? !getPrimKeys()?.length
                                  ? `${
                                      form?.getFieldValue('dataObj') === 'old'
                                        ? '已有表未设置主键与唯一键'
                                        : '新建表未设置主键'
                                    }，不支持配置更新策略`
                                  : ''
                                : '',
                          },
                        },
                      ]
                    : [
                        { label: isMessage ? '更新' : '覆盖', value: 1 },
                        // 已有表的情况下检测表是否有op和op_ts字段，否则无法切换成追加数据策略
                        {
                          label: '追加',
                          value: 2,
                          disabled: isMessage ? !canAppend : false,
                        },
                      ]
                }
              />
            </Form.Item>
            <Tooltip
              title={
                isMessage
                  ? MESSAGE_DATASTRTEGY_TIPDATASTRTEGY_TIP
                  : DATASTRTEGY_TIP
              }
            >
              <IconFont type="icon-question-circle" style={{ marginLeft: 8 }} />
            </Tooltip>
          </Form.Item>
        </Col>
        {/* 覆盖策略 */}
        {!isMessage && (
          <Col span={8} className={styles.formItemCol}>
            <Form.Item
              shouldUpdate={(pre, cur) =>
                pre.strategy !== cur.strategy ||
                pre.updateWay !== cur.updateWay ||
                pre.dataObj !== cur.dataObj ||
                pre.tableId !== cur.tableId
              }
              noStyle
            >
              {({ getFieldValue }) => {
                // const primKeys = getPrimKeys();
                const updateKeys = getUpdateKeys();
                const isIncrement = getFieldValue('updateWay') === 2;
                const isStore = isIncrement && getFieldValue('strategy') === 2;
                const isUpdatekey = getFieldValue('strategy') === 3;

                return isStore ? (
                  <Form.Item label="存储表历史数据">
                    <Form.Item name="clearStoreData" initialValue={2} noStyle>
                      <RadioButton
                        options={[
                          { label: '清空', value: 1 },
                          { label: '不清空', value: 2 },
                        ]}
                      />
                    </Form.Item>
                    <Tooltip title={STORE_TIP}>
                      <IconFont
                        type="icon-question-circle"
                        style={{ marginLeft: 8 }}
                      />
                    </Tooltip>
                  </Form.Item>
                ) : isUpdatekey ? (
                  <Form.Item
                    label="更新键"
                    required={getUpdateStrategyType() === 2}
                  >
                    {getUpdateStrategyType() === 1 && updateKeys?.length ? (
                      <Form.Item name="updatekeys" noStyle>
                        <TagsAdd
                          tags={updateKeys}
                          num={3}
                          limit={12}
                          // collapsible
                        />
                      </Form.Item>
                    ) : getUpdateStrategyType() === 2 ? (
                      <Form.Item
                        name="updatekeys"
                        rules={[{ required: true, message: '请选择更新键' }]}
                        noStyle
                      >
                        <Select
                          mode="tags"
                          showSearch
                          allowClear
                          placeholder="请选择更新键"
                          style={{ width: 'calc(100% - 36px)' }}
                        >
                          {getUpdateKeysOptions().map((v) => (
                            <Select.Option
                              key={v.value}
                              value={v.value}
                              disabled={v?.disabled}
                            >
                              <div style={{ display: 'flex' }}>
                                <div
                                  style={{
                                    width: v?.disabled
                                      ? `calc(100% - ${32}px)`
                                      : '100%',
                                    overflow: 'hidden',
                                    whiteSpace: 'nowrap',
                                    textOverflow: 'ellipsis',
                                  }}
                                >
                                  {v?.label}
                                </div>
                                {v?.disabled && (
                                  <span
                                    style={{ position: 'absolute', right: 8 }}
                                  >
                                    不支持
                                  </span>
                                )}
                              </div>
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    ) : null}
                    <Tooltip
                      title={
                        getUpdateStrategyType() === 1
                          ? '新建表、已有表的逻辑主键不支持修改'
                          : getUpdateStrategyType() === 2
                          ? '此处设置的更新键为仅供库表推送使用的业务主键，不会落地到表'
                          : ''
                      }
                    >
                      <IconFont
                        type="icon-question-circle"
                        style={{ marginLeft: 8 }}
                      />
                    </Tooltip>
                  </Form.Item>
                ) : null;
              }}
            </Form.Item>
          </Col>
        )}
      </>
    </FormRow>
  );
};
export default PushStrategy;

const getFieldType = (fields, curField) => {
  const target = fields.find((item) => item.fieldName === curField);
  return target?.fieldType || '';
};

/**
 * @param values 表单值
 * @param fields 字段值，为获取字段的类型
 * @returns 如果不是字符串类型，返回字段本身的类，否则返回时间格式选择的类型
 */
const getPayloadFieldType = (values, fields) => {
  if (!values.timeFormat) {
    return getFieldType(fields, values?.increField);
  }
  return values.timeFormat;
};

export const getStartValue = (values) => {
  const isSync = values.syncSourceData === 1;
  if (isSync) return '';
  // 1. 整型
  if (values.increValue) {
    return String(values.increValue);
  }
  // 2. 字符型 - 自动识别
  if (values.timeFormat === TIME_FORMAT_AUTO) {
    return values.autoValue;
  }
  // 2. 字符型 - 其他时间格式
  return moment(values.increDate).format(values.increDate._f);
};

export const pushStrategyFormValueToRes = (values: FormValuesType, fields) => {
  // updatestrategy（更新方式）1 全量 2 增量
  // syncsourcedata（增量起始值） 1 全部历史数据 2 设置起始值
  // clearstoredata（存储表历史数据） 1 清空 2 不清空
  // datastrategy（数据策略） 1 覆盖 2 追加
  // 是否消息流
  const isMessage = values.sourcedatatype === 1;
  const isIncre = values.updateWay === 2;
  const params = {
    updatestrategy: values.updateWay,
    datastrategy: values.strategy,
  };
  if (isIncre) {
    return {
      ...params,
      ...(isMessage
        ? {}
        : {
            updatefield: {
              fieldname: values?.increField,
              fieldtype: getPayloadFieldType(values, fields),
              startvalue: getStartValue(values),
            },
          }),
      syncsourcedata: values.syncSourceData,
      clearstoredata: values.clearStoreData,
    };
  }
  return params;
};

export const resToPushStrategyFormValue = (res) => {
  const getIncreDate = () => {
    const increIdentify = res.updatefield.startvalue || res?.maxtime;
    if (!increIdentify) return undefined;
    // yyyy- 格式的需要格式化处理
    return moment(increIdentify, TIME_FORMATE[res.updatefield.fieldtype]);
  };

  const common = {
    updateWay: res.updatestrategy,
    strategy: res.datastrategy || 2, // 默认为追加
    wherecontent: res.wherecontent,
  };

  const isIncre = res.updatestrategy === 2;

  if (isIncre) {
    // 判断字段类型是否为整型
    const isInt = res.updatefield.fieldtype.includes('int');
    // 增量起始值
    const increIdentify = res.updatefield.startvalue || res?.maxtime;
    // 时间格式选择的是「自动识别」
    const isAuto = res.updatefield.fieldtype === TIME_FORMAT_AUTO;

    return {
      ...common,
      increField: res.updatefield.fieldname,
      timeFormat: res.updatefield.fieldtype,
      /** 增量起始值 */
      syncSourceData: res.ranstatus ? 2 : res.syncsourcedata || 1,
      /** 存储表历史数据 */
      clearStoreData: res.ranstatus ? 2 : res.clearstoredata || 2,
      // 增量起始值
      ...(isInt && {
        increValue: increIdentify ? Number(increIdentify) : undefined,
      }),
      ...(!isInt && {
        increDate: getIncreDate(),
      }),
      ...(isAuto && {
        autoValue: increIdentify || undefined,
      }),
    };
  }
  return common;
};
