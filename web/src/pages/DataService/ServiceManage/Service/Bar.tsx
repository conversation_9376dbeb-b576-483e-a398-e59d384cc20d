import { useContext, useMemo, useState, useEffect } from 'react';
import { routerRedux, Prompt } from 'dva/router';
import { Button, Row, Col, notification, Modal } from 'antd';
import { useSelector, useDispatch, useModel } from 'umi';
import {
  IconFont,
  Filter,
  FormBundle,
  setPageParam,
  RegulateURL,
} from '@dana/ui';
import LoadingButton from '@/pages/DataCollect/LoadingButton';
import { ConnectState, Dispatch, AnyAction } from '@/models/connect.d';
import TimeParamsModal from '@/components/TimeParamsModal';
import { pushStrategyFormValueToRes } from './TablePush/PushStrategy';
import { SaveAlertModal } from '../Modal';
import { ServiceContext } from './index';
import { useLoading } from '../hooks';
import { IDENTIFIER, RELEASED, SEPARATOR_COMMENT } from '../conf';
import type { PushRef, FormValuesType } from '../index.d';
import { checkTableFields, formateQueryparam } from '../utils';

const dateFormat = 'YYYY-MM-DD';
const timeFormat = 'YYYY-MM-DD HH:mm:ss';

const { formatToReq: formatToTimeOutReq } = FormBundle.timeoutStrategy;

// *** 基础信息 ***
/** 基础信息保存参数 */
const getBasicParams = (
  values: FormValuesType,
  { tags = [] as { id: string; dirid: string; name: string; color: string }[] },
) => {
  const tagsParams = values.tags
    ? tags
        .filter((v) => values.tags.includes(v.id))
        .map((v) => ({
          id: v.id,
          name: v.name,
          dirid: v.dirid,
          color: v.color,
        }))
    : [];
  return {
    name: values.serviceName,
    catalogid: values.folder,
    tags: tagsParams,
    describe: values.description,
  };
};

// *** 数据来源 ***
export const getDataSourceParams = (
  values: FormValuesType,
  { tables, sourceTableName },
) => {
  return {
    sourcetbtype: values.sourcetbtype,
    sourcedatatype: values.sourcedatatype,
    sourcecatalogid: values.sourceCatalogId, // 所属类目
    sourcetbid: values.sourceTableId, // 数据名称表id
    sourcetbname:
      tables.find((item) => item.id === values.sourceTableId)?.tbname ||
      sourceTableName ||
      '', // 数据名称表名
  };
};

// *** API共享 ***
/** API 服务配置参数 */
export const getApiServerParams = (
  values: FormValuesType,
  { fields },
  queryparam,
  othersParam,
) => {
  return {
    usewhere: values.usewhere,
    wheresql: values.wheresql,
    apiserver: {
      accessurl: values.path,
      // 黑白名单
      apiiptype: values.apiipType,
      addappip: values.addappip,
      openflow: values.flow,
      querylimit: values.querylimit,
      queryparam: formateQueryparam(queryparam),
      ...(values.flow && {
        flowinfo: {
          interval: values?.counttype === 1 ? 1 : values.interval, // 统计方式为自然周期则单位时间固定值为1
          intervalunit: values.intervalunit,
          apiaccessmax: values?.limitForApi?.checked
            ? values.limitForApi.number
            : -1,
          ipaccessmax: values?.limitForIp?.checked
            ? values.limitForIp.number
            : -1,
          useraccessmax: values?.limitForUser?.checked
            ? values.limitForUser.number
            : -1,
          counttype: values.counttype ?? 0,
        },
      }),
    },
    fields: fields.map((v) => ({
      selected: v.selected,
      name: v.fieldName,
      type: v.fieldType,
      comment: !values.paramComment
        ? v.comment
        : values.paramComment[`${v.fieldName}${SEPARATOR_COMMENT}`] || '',
      ruleinfo: v.ruleinfo,
    })),

    ...((values.apiipType === 1 || values.apiipType === 2) && {
      iplist: othersParam?.iplist?.map(({ ip }) => ip),
    }),
  };
};

/** 判断流量管控是否错误 */
const checkFlowIsError = (values: FormValuesType, form) => {
  const error =
    values.flow &&
    !values.limitForApi?.checked &&
    !values.limitForIp?.checked &&
    !values.limitForUser?.checked;
  if (error) {
    form.scrollToField('flow', { block: 'center' });
  }
  return error;
};

/** 检测返回字段数有无错误 */
const checkParamsFieldsIsError = (fields, form) => {
  const error = fields.every((item) => !item.selected);
  if (error) {
    form.scrollToField('return_fields', { block: 'center' });
  }
  return error;
};
const getNumber0 = (value) => {
  if (value === 0) {
    return value;
  }
  return value || '';
};
// *** 筛选条件 ***
/** 组装单个filters (value1: xxx, value2: xxx) */
export const getFilterItemValue = (item) => {
  return {
    fieldname: item.name,
    name: item.name,
    label: item?.label,
    option: item.condition,
    value1: Array.isArray(item.value) ? item.value[0] : getNumber0(item.value),
    value2: Array.isArray(item.value) ? item.value[1] : '',
  };
};

export const getFilterParams = (values) => {
  const newInfo = Filter.formValuesToRes(values.filters.filter((v) => v));
  const temp = newInfo.reduce((acc, cur) => {
    const newCur = getFilterItemValue(cur);
    acc[newCur.name] = acc[newCur.name] || [];
    acc[newCur.name].push(newCur);
    return acc;
  }, {});
  const data = Object.entries(temp).map(([key, value]) => {
    return {
      fieldname: key,
      condition: {
        type: values.conditionType,
        item: value,
      },
    };
  });
  return data;
};

export const getTileFilters = (filters, isTime?: boolean) => {
  const newFilters = Filter.formValuesToRes(
    filters.filter((v) => v),
    { format: isTime ? timeFormat : dateFormat },
  );
  return newFilters.map((item) => getFilterItemValue(item));
};

// *** 库表推送 ***
const getTablePushParams = (
  values: FormValuesType,
  { fields, extraParams },
) => {
  const {
    dataObj,
    tableId,
    newTbName,
    preTask,
    assignnode,
    retry,
    retryInterval,
    retryDisable,
    ram,
    kbps,
    kbpsValue,
    splitkey,
    concurrency,
    errorinfo,
    isrecorderr,
    describe, // *表描述
    wherecontent, // *条件过滤
    prioritymode,
  } = values;

  if (!extraParams) return;

  const { curDB, tables, graph, isSort, curTableName, getDepJob } = extraParams;
  const getPayloadTimeOut = (fieldsValue) => {
    const params = formatToTimeOutReq(fieldsValue);
    // timeoutstop: fieldsValue.isInterrupt, //* 超时中断
    // timeoutduration: fieldsValue.timeoutInterval, //* 超时时长  超时中断为true时，传参如[1,1]//数组长度为2，分别为：时间大小、时间类型(1-分，2-小时，3-天，以此类推)
    return {
      timeoutstop: params?.timeoutstop,
      timeoutduration: params?.timeoutduration,
    };
  };
  const getSchedule = (fieldValues) => {
    const { formatToReq } = FormBundle.exeStrategy;
    const payload = formatToReq(fieldValues);
    return {
      // * 策略
      scheduletype: payload?.scheduletype,
      scheduledetail: payload?.scheduledetail,
      exectimes: payload?.exectimes,
      // * 新加的参数
      // * 生效开始时间
      effectstart: payload?.effectstart,
      // 兼容用
      firststarttime: payload?.effectstart,
      // * 生效结束时间
      effectend: payload?.effectend,
      ...getPayloadTimeOut(fieldValues),
      crossdep: fieldValues.crossdep, //* 跨周期自依赖
      crossforce: fieldValues.crossforce, //* 强制自依赖
      updep: fieldValues.updep, //* 上游依赖任务
      depjobinfo: getDepJob ? getDepJob()?.depJobInfo : {}, //* 依赖任务
      joblevel: fieldValues.priority, //* 任务优先级 priority  高中低
    };
  };

  const sliceEdges = (op) => {
    return op.slice(0, op.length - 2);
  };

  const getTableRelation = () => {
    // *已有表和新建表这块处理都一样

    const nodes = graph.getNodes().map((item) => {
      const model = item.getModel();
      return {
        fieldname: model.fieldName,
        fieldtype: model.fieldType,
        comment: model.comment,
        position: model.position,
        //* 字段标识
        ispartition: item.isPartition || model?.ispartition,
        isprim: item.isPrim || model.isPrim || model?.isprim,
        isunique: item.isUnique || model.isUnique || model?.isunique,
        partitionnumber: item.partitionNumber || item?.partitionnumber,
      };
    });
    const edges = graph
      .getEdges()
      .filter((item) => {
        const model = item.getModel();
        return (
          typeof model.source === 'string' && typeof model.target === 'string'
        );
      })
      .map((item) => {
        const { source, target } = item.getModel();
        return {
          source: sliceEdges(source),
          target: sliceEdges(target),
        };
      });
    const leftNode = nodes.filter((item) => item.position === 'left');
    const selectedLeftNode = leftNode.filter((item) =>
      edges.find((it) => it.source === item.fieldname),
    );
    const rankedEdges = selectedLeftNode.map((item) => {
      return {
        source: item.fieldname,
        target: edges.find((it) => it.source === item.fieldname)?.target,
      };
    });
    return { nodes, edges: rankedEdges };
  };

  const dbInfo = curDB.parent?.id ? curDB.parent : curDB;

  return {
    dbid: dbInfo.id,
    dbname: dbInfo.dbname,
    dbtype: curDB?.dbtype || curDB?.parent?.dbtype,
    schemaid: curDB.schemaid || '',
    schema: curDB.schema || '',
    needcreate: dataObj === 'new', // 是否为新建表
    tableid: tableId,
    describe,
    wherecontent,
    tablename:
      newTbName ||
      tables.find((item) => item.tableid === tableId)?.tablename ||
      curTableName,
    tablerelation: getTableRelation(),
    issort: isSort,
    /** 推送策略 */
    pushstrategy: getSchedule(values),
    /** 前置任务 */
    prejobs: !values.preTask ? [] : [preTask],
    /** 运行节点 */
    assignnode: assignnode === '自由分配' ? '' : assignnode,
    /** 是否重试 */
    isretry: retry !== 0,
    /** 重试次数 */
    retrynum: retry,
    /** 重试间隔 */
    retryinterval: retryInterval,
    /** 失败禁用 */
    isban: retryDisable,
    /** 更多高级设置 */
    advancedconfig: {
      ram,
      kbps: kbps === -1 ? kbps : kbpsValue,
      splitkey,
      concurrency,
      errorinfo,
      isrecorderr,
      /** 数据入库模式 */
      prioritymode,
    },
    ...pushStrategyFormValueToRes(values, fields),
  };
};
const checkFieldMapIsError = (info, form) => {
  // 主键校验在后面进行
  const error = Object.keys(info?.fieldError || {})
    .filter((item) => item !== 'noMapPKeys')
    .some((item) => {
      return (info?.fieldError || {})[item];
    });
  if (error) {
    form.scrollToField('fieldmap_error', { block: 'nearest' });
  }
  return error;
};

const checkFieldPrimaryKey = (info, form) => {
  const error = (info?.fieldError || {}).noMapPKeys;
  if (error) {
    form.scrollToField('fieldmap_error', { block: 'nearest' });
  }
  return error;
};

async function checkFieldsIsError(extraParams, form, setPage) {
  const { curDB, graph } = extraParams;
  const rightTableData: any[] = [];
  graph.getNodes().forEach((item) => {
    const model = item.getModel();
    if (model.position === 'right') {
      const obj: any = {
        fieldName: model.fieldName,
        comment: model.comment,
        position: model.position,
        //* 字段标识
        ispartition: item.isPartition,
        isprim: item.isPrim,
        isunique: item.isUnique,
        partitionnumber: item.partitionNumber,
      };
      rightTableData.push(obj);
    }
  });
  /** 当前选择的数据库类型 */
  const dbType = (
    curDB?.parent ? curDB?.parent?.dbtype : curDB?.dbtype
  ) as string;
  // ? 已有表为什么要校验表格字段，已改为不校验
  if (form.getFieldValue('dataObj') !== 'old') {
    const checkFieldsRes = await checkTableFields(rightTableData, dbType, 10);
    if (typeof checkFieldsRes === 'number') {
      if (setPage) {
        await setPage(checkFieldsRes);
      }
      form.validateFields().catch((errData) => {
        form.scrollToField(errData.errorFields[0].name, {
          block: 'nearest',
        });
      });
      return true;
    }
  }
  return false;
}

// *** 离线文件 ***
const getOfflineServerParams = (values: FormValuesType, { fields }) => {
  return {
    usewhere: values.usewhere,
    wheresql: values.wheresql,
    downloadserver: {
      filetype: values.fileType,
      codingformat: values.codingFormat,
      numberdata: values.count,
      isshowchinese: values.isshowchinese,
      ...(values.fileType === 'Excel' && {
        watermarktype: values.watermarkType,
        watermark: values.watermark || '',
        passwordtype: values.passwordtype,
        passwords: values.passwords || values.randomPwd || '',
      }),
    },

    fields: fields.map((v) => ({
      selected: v.selected,
      name: v.fieldName,
      type: v.fieldType,
      comment: v.comment,
      ruleinfo: v.ruleinfo,
    })),
  };
};
/** 检测字段数有无错误 */
const checkOfflineFieldsIsError = (fields, form) => {
  const error = fields.every((item) => !item.selected);
  if (error) {
    form.scrollToField('offline_fields', { block: 'center' });
  }
  return error;
};

const Bar = ({
  pushRef,
  isView = false,
  auditStatus = -1,
  isChange,
  isFromAssetPublish,
}: {
  isView?: boolean;
  pushRef?: PushRef;
  auditStatus?: number;
  isChange?: boolean;
  isFromAssetPublish?: boolean;
}) => {
  const { form } = useContext(ServiceContext);

  const dispatch = useDispatch<Dispatch<AnyAction>>();

  const [alertVisible, setAlertVisible] = useState(false);
  const [timeVarShow, setTimeVarShow] = useState(false);
  const [timeVars, setTimeVars] = useState([]);
  const [leaveLocation, setLeaveLocation] = useState({ pathname: '/' });
  const { queryparam } = useModel('DataService.ServiceManage.apiConfig');

  // @ts-ignore
  const { hashParams } = new RegulateURL() as {
    id: string;
    auditStatus: number;
  };

  const {
    tileTags,
    apiFieldsInfo,
    offlineFieldsInfo,
    rowRule,
    sourceFieldsInfo,
    sourceTable,
    canLeave,
    newTableSelectedKeys,
    sourceTableName,
    detailInfo: details,
    setPage,
    curDB,
    existTbFieldsInfo,
    newRightFieldInfo,
    ipDataList,
  } = useSelector((state: ConnectState) => ({
    tileTags: state.serviceManage.tileTags,
    apiFieldsInfo: state.serviceManage.apiFieldsInfo,
    offlineFieldsInfo: state.serviceManage.offlineFieldsInfo,
    rowRule: state.serviceManage.rowRule,
    sourceFieldsInfo: state.serviceManage.sourceFieldsInfo,
    sourceTable: state.serviceManage.sourceTable,
    canLeave: state.serviceManage.canLeave,
    newTableSelectedKeys: state.serviceManage.newTableSelectedKeys,
    sourceTableName: state.serviceManage.sourceTableName,
    detailInfo: state.serviceManage.detailInfo,
    whereHasChecked: state.serviceManage.whereHasChecked,
    setPage: state.serviceManage.setPage,
    curDB: state.serviceManage.curDB,
    existTbFieldsInfo: state.serviceManage.rightFieldInfo,
    newRightFieldInfo: state.serviceManage.newRightFieldInfo,
    ipDataList: state.serviceManage.ipDataList,
  }));

  useEffect(() => {
    setTimeVars(details?.globalvars?.timevars || []);
    localStorage.setItem(
      'serviceManage_TimeVars',
      JSON.stringify(details?.globalvars?.timevars || []),
    );
  }, [details?.globalvars?.timevars]);

  /** 更新model层数据 */
  const updateStateFromProps = (values) => {
    dispatch({
      type: 'serviceManage/updateState',
      payload: values,
    });
  };

  // *sql合法性校验
  function getSqlTest(values, callBack) {
    const isSchema = !Object.prototype.hasOwnProperty.call(curDB, 'haveschema');
    if (values.wherecontent) {
      dispatch({
        type: 'serviceManage/getSqlTest',
        payload: {
          schema: isSchema ? curDB?.schema : '',
          sqltxt: values.wherecontent,
          sourcetbtype: values.sourcetbtype,
          sourcedatatype: values.sourcedatatype,
          sourcetbid: values.sourceTableId,
        },
        callback: (res) => {
          if (res?.code === 200 && res?.result) {
            callBack();
            form?.setFields([
              {
                value: values.wherecontent,
                name: 'wherecontent',
                errors: [],
              },
            ]);
          } else {
            form?.setFields([
              {
                value: values.wherecontent,
                name: 'wherecontent',
                errors: ['where语句不合法'],
              },
            ]);
            form?.scrollToField('wherecontent', {
              block: 'center',
            });
          }
        },
      });
    }
  }

  const checkSql = (callBack) => {
    const { serviceType, wheresql, wherecontent } =
      form?.getFieldsValue() || {};

    form
      ?.validateFields()
      .then((values: FormValuesType) => {
        if (serviceType === 2 && wherecontent) {
          getSqlTest(values, callBack);
        } else if (wheresql) {
          (
            dispatch({
              type: 'serviceManage/testAPI',
              payload: {
                sourcetbtype: values.sourcetbtype,
                sourcedatatype: values.sourcedatatype,
                sourcecatalogid: values.sourceCatalogId,
                sourcetbid: values.sourceTableId,
                sourcetbname: sourceTable.find(
                  (item) => item.id === values.sourceTableId,
                )?.tbname,
                type: values.serviceType,
                ...(serviceType === 3
                  ? {
                      fields: offlineFieldsInfo.map((v) => ({
                        selected: v.selected,
                        name: v.fieldName,
                        type: v.fieldType,
                        comment: !values.paramComment
                          ? v.comment
                          : values.paramComment[
                              `${v.fieldName}${SEPARATOR_COMMENT}`
                            ] || '',
                        ruleinfo: v.ruleinfo,
                      })),
                    }
                  : getApiServerParams(
                      values,
                      { fields: apiFieldsInfo },
                      queryparam,
                      {
                        iplist: ipDataList,
                      },
                    )),
                usewhere: values.usewhere,
                wheresql: values.wheresql,
              },
            }) as any
          ).then((res) => {
            if (res?.code === 200) {
              callBack();
              form.setFields([
                {
                  value: values.wheresql,
                  name: 'wheresql',
                  errors: [],
                },
              ]);
            } else {
              form.setFields([
                {
                  value: values.wheresql,
                  name: 'wheresql',
                  errors: ['where语句不合法'],
                },
              ]);
            }
          });
        } else {
          callBack();
        }
      })
      .catch((err) => {
        if (err && err.errorFields) {
          const { errorFields } = err;
          form.scrollToField(errorFields[0].name, {
            block: 'center',
          });
        }
      });
  };

  const saveServiceRequest = (params, values) => {
    (
      dispatch({
        type: 'serviceManage/saveService',
        payload: params,
      }) as any
    ).then((data) => {
      if (data.code === 200) {
        notification.success({
          message: hashParams?.id
            ? `编辑 ${values.serviceName} 服务成功`
            : `新建 ${values.serviceName} 服务成功`,
        });
        updateStateFromProps({ canLeave: true });
        setTimeout(() => {
          setPageParam({
            pathname: '/dataService/management/service/detail',
            keepData: null,
            query: { id: data.result.id || hashParams?.id },
          });
        }, 10);
      } else if (data.code === 409) {
        form.setFields([
          {
            name: ['newTbName'],
            value: values.newTbName,
            errors: ['名称已存在, 请重命名'],
          },
        ]);
      } else if (data.code === 10016) {
        notification.error({
          message: data.msg,
        });
      } else {
        notification.error({
          message: hashParams?.id
            ? `编辑 ${values.serviceName} 服务失败`
            : `新建 ${values.serviceName} 服务失败`,
        });
      }
    });
  };

  const handleSave = () => {
    let allFormNames = Object.keys(form?.getFieldsValue());
    // 如果是新建表，需要单独处理(不校验未勾选的表单项)
    if (
      form?.getFieldValue('serviceType') === 2 &&
      form.getFieldValue('dataObj') === 'new'
    ) {
      allFormNames = Object.keys(form?.getFieldsValue()).filter((item) => {
        return (
          !item.includes(IDENTIFIER) ||
          (item.includes(IDENTIFIER) &&
            newTableSelectedKeys.includes(
              item.slice(0, item.indexOf(IDENTIFIER)),
            ))
        );
      });
    }

    form
      ?.validateFields(allFormNames)
      .then(async (values: FormValuesType) => {
        // *判断数据名称是否发生变化
        if (!isChange) {
          // *未改变时，从详情中获取
          values.sourceTableId = details?.sourcetbid;
        }

        // 是否消息流
        const isMessage = values.sourcedatatype === 1;
        const apiError =
          values.serviceType === 1 &&
          ((!isMessage && checkParamsFieldsIsError(apiFieldsInfo, form)) ||
            checkFlowIsError(values, form));

        const pushError =
          values.serviceType === 2 &&
          (checkFieldMapIsError(pushRef?.current, form) ||
            (await checkFieldsIsError(pushRef?.current, form, setPage)));
        const offlineError =
          values.serviceType === 3 &&
          checkOfflineFieldsIsError(offlineFieldsInfo, form);

        if (apiError || pushError || offlineError) return;

        const basicInfo = getBasicParams(values, { tags: tileTags });
        const dataSourceInfo = getDataSourceParams(values, {
          tables: sourceTable,
          sourceTableName,
        });

        // 

        const params = {
          ...(hashParams?.id && { id: hashParams?.id }),
          ...basicInfo,
          ...dataSourceInfo,
          type: values.serviceType,
          ...(values.serviceType === 1 && {
            ...getApiServerParams(
              values,
              { fields: apiFieldsInfo },
              queryparam,
              { iplist: ipDataList },
            ),
            orderfield: (values?.orderfield || []).map((item, index) => ({
              ...item,
              index,
            })),
          }),
          ...(values.serviceType === 2 && {
            pushserver: {
              ...getTablePushParams(values, {
                fields: sourceFieldsInfo,
                extraParams: pushRef?.current,
              }),

              // 新建表主键
              ...(values.dataObj === 'new' && {
                primkey:
                  newRightFieldInfo
                    ?.filter((v: any) => v?.isPrim)
                    ?.map((v: any) => v?.fieldName) ?? [],
              }),

              // 可编辑的更新键取自表单/不可编辑的更新键取自映射表左表
              serviceprimkey: values?.updatekeys?.length
                ? values.updatekeys
                : existTbFieldsInfo
                    ?.filter((v: any) => v?.isprim)
                    ?.map((v: any) => v?.fieldName) ?? [],
            },
          }),
          ...(values.serviceType === 3 && {
            ...getOfflineServerParams(values, {
              fields: offlineFieldsInfo,
            }),
          }),
          // 1为公开，2为受限
          permission: isMessage ? 2 : values.permission,
          ...(values.filters && {
            paramfilter: getFilterParams(values),
            conditemss: getTileFilters(values.filters),
          }),

          globalvars: {
            crontype: '1', // 默认必传
            timevars: timeVars,
          },

          rowrule: rowRule,
        };
        const pushPrimaryKeyError =
          values.serviceType === 2 &&
          checkFieldPrimaryKey(pushRef?.current, form);
        if (pushPrimaryKeyError) {
          Modal.confirm({
            icon: (
              <IconFont
                type="icon-exclamation-circle"
                style={{ color: '#FFC446', marginRight: 8 }}
              />
            ),
            title: '保存提醒',
            content: '主键字段未作映射，可能会导致数据推送失败，确定要保存吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              saveServiceRequest(params, values);
            },
          });
        } else {
          saveServiceRequest(params, values);
        }
      })
      .catch((err) => {
        console.log(err, err.errorFields);
        if (err && err.errorFields) {
          const { errorFields } = err;
          form.scrollToField(errorFields[0].name, {
            block: 'center',
          });
        }
      });
  };

  const handleBack = () => {
    dispatch(
      routerRedux.push({
        pathname: `/dataService/management`,
      }),
    );
    // 延迟清除数据，不要影响跳转前的渲染
    if (canLeave) {
      setTimeout(() => {
        updateStateFromProps({ detailInfo: null, fileType: 'CSV' });
        // *清空model
        dispatch({
          type: 'serviceManage/reset',
        });
        form?.resetFields();
      }, 0);
    }
  };

  // FIXME setPageParam方法只针对一层跳转保存数据有用，此处先侵入一下setPageParam内部使用的值browseRecord，来达到夸页面的跳转数据保存
  const handleEdit = () => {
    const browseRecord = localStorage.getItem('browseRecord');
    const formatBroswers = browseRecord ? JSON.parse(browseRecord) : [];
    const currentBroswer = formatBroswers[formatBroswers.length - 1];
    setPageParam({
      pathname: '/dataService/management/service',
      keepData: currentBroswer?.keepData,
      query: { id: hashParams?.id },
    });
    localStorage.setItem(
      'browseRecord',
      JSON.stringify([
        { ...currentBroswer, prevPath: '/dataService/management' },
      ]),
    );
  };

  const onForce = () => {
    updateStateFromProps({ canLeave: true });

    setTimeout(() => {
      dispatch(
        routerRedux.push({
          pathname: leaveLocation.pathname,
        }),
      );
      setTimeout(() => {
        // *跳转后清空
        dispatch({
          type: 'serviceManage/reset',
        });
      }, 0);
    }, 0);
  };

  const handlePrompt = (location) => {
    if (!canLeave) {
      setAlertVisible(true);
      setLeaveLocation(location);
      return false;
    }
    return true;
  };

  const onParamsConfirm = (params) => {
    setTimeVars(params);
    setTimeVarShow(false);
    localStorage.setItem('serviceManage_TimeVars', JSON.stringify(params));
  };

  const onParamsCancel = () => {
    setTimeVarShow(false);
  };

  const { saveLoading, detailLoading, sqlTestLoading } = useLoading();
  // 2 - 发布审核中
  const disabled = useMemo(
    () => [2].concat(RELEASED).includes(auditStatus),
    [],
  );

  return (
    <>
      <Row style={{ marginBottom: 12 }}>
        <Col span={12}>
          <LoadingButton
            loading={saveLoading || detailLoading || sqlTestLoading}
            disabled={disabled || isFromAssetPublish}
            tooltip={{
              isShow: disabled || !!isFromAssetPublish,
              title: '服务已发布，无法编辑',
            }}
            icon="icon-save"
            text={isView ? '编辑' : '保存'}
            onClick={() => {
              if (isView) {
                handleEdit();
              } else {
                checkSql(handleSave);
              }
            }}
          />
        </Col>
        <Col span={12} style={{ textAlign: 'right' }}>
          <Button
            disabled={saveLoading || detailLoading}
            onClick={() => setTimeVarShow(true)}
          >
            全局参数
          </Button>
          <Button
            onClick={handleBack}
            disabled={saveLoading || detailLoading}
            style={{ marginLeft: 12 }}
          >
            <IconFont type="icon-rollback" text="返回" />
          </Button>
        </Col>
      </Row>
      <Prompt message={handlePrompt} />
      <SaveAlertModal
        visible={alertVisible}
        close={() => setAlertVisible(false)}
        onForce={onForce}
        onSave={handleSave}
        saveLoading={saveLoading}
      />

      <TimeParamsModal
        visible={timeVarShow}
        params={timeVars}
        canEdit={!isView}
        onConfirm={onParamsConfirm}
        onCancel={onParamsCancel}
      />
    </>
  );
};

export default Bar;
