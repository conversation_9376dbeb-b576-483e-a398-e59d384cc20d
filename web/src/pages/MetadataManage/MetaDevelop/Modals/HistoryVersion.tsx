import { memo, useCallback, useMemo, useState } from 'react';
import { useSelector, useDispatch } from 'umi';
import { Form, Tooltip } from 'antd';
import { ConnectState } from '@/models/connect.d';
import {
  ContentModal,
  Table,
  VersionCompareSelect,
  IconFont,
} from '@/components';
import { formatDate } from '@/utils';
import commonStyles from '@/components/VersionCompareSelect/versionCompare.less';
import VersionCompareModal from './VersionCompareModal';
import { useMetaGlobal } from '../MetaDevelopContext';
import { uuid } from '../utils';

type HistoryVersionProps = {
  // tableInfo?: any;
  visible: boolean;
  onCancel: () => void;
};

const HistoryVersion = (props: HistoryVersionProps) => {
  const [form] = Form.useForm();
  const { visible, onCancel } = props;
  const { dispatch: globalDispatch } = useMetaGlobal();
  const { curHistoryList, curVersionId } = useSelector(
    (state: ConnectState) => state.metaDevelopTable,
  );
  const loading = useSelector((state: ConnectState) => state.loading);
  const historyLoading = loading.effects['metaDevelopTable/getHistoryDetail'];

  const [versionCompareMdVisible, setVersionCompareMdVisible] = useState(false);
  const [showNickname, setShowNickname] = useState(false);

  const dispatch = useDispatch();

  const showCurHistoryPane = useCallback(
    (record) => {
      const { name, history, id } = record;
      onCancel();
      globalDispatch({
        type: 'ADD_TAB',
        payload: {
          data: {
            tableId: id,
            tableName: name,
            history,
          },
          type: 'history',
          tab: `${history}-${name}`,
          key: uuid(),
        },
      });
      dispatch({
        type: 'metaDevelopTable/getHistoryDetail',
        payload: {
          id,
        },
      });
    },
    [onCancel],
  );

  const handleCompare = () => {
    setVersionCompareMdVisible(true);
  };

  const columns = useMemo(
    () => [
      {
        title: '版本号',
        dataIndex: 'history',
        render(text, record) {
          return (
            <a onClick={() => showCurHistoryPane(record)}>
              <span>{text}</span>
            </a>
          );
        },
      },
      {
        title: '表名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '表描述',
        dataIndex: 'describe',
        ellipsis: true,
      },
      {
        title: (
          <div>
            创建人
            <Tooltip title={showNickname ? '展示为用户账号' : '展示为用户名'}>
              <IconFont
                type="icon-UpdateKey"
                className={commonStyles.changeNameIcon}
                onClick={() => setShowNickname((preData) => !preData)}
              />
            </Tooltip>
          </div>
        ),
        dataIndex: 'username',
        key: 'username',
        ellipsis: true,
        render(text, record) {
          return <span>{(showNickname ? record.nickname : text) || '--'}</span>;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'creatTime',
        sorter: true,
        render(text) {
          return formatDate(text);
        },
      },
    ],
    [showCurHistoryPane, showNickname],
  );

  const curVersion = [{ name: '当前版本', id: curVersionId }];
  const versionList = curVersion.concat(
    curHistoryList.map((v) => {
      return {
        name: v.history,
        time: v.creatTime,
        id: v.id,
      };
    }),
  );

  const handleCancelModal = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <ContentModal
      modalSize="middle"
      title="历史版本"
      footer={null}
      visible={visible}
      onCancel={handleCancelModal}
    >
      <Form form={form}>
        <div style={{ marginBottom: 12 }}>
          <VersionCompareSelect
            handleCompare={handleCompare}
            optionList={versionList.map((v) => v.name)}
            form={form}
          />
        </div>
        <Table
          loading={historyLoading}
          style={{ height: 480 }}
          // pagination={false}
          columns={columns}
          dataSource={curHistoryList}
        />
        <VersionCompareModal
          visible={versionCompareMdVisible}
          form={form}
          handleCancel={() => setVersionCompareMdVisible(false)}
          versionList={versionList}
        />
      </Form>
    </ContentModal>
  );
};

export default memo(HistoryVersion);
