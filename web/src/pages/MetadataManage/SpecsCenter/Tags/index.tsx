/* eslint-disable @typescript-eslint/naming-convention */
import React, { useState, useCallback, useEffect } from 'react';
import { connect } from 'dva';
import { debounce } from 'lodash';
import Ellipsis from 'ant-design-pro/lib/Ellipsis';
import { ButtonType } from 'antd/lib/button/button.d';
import { Row, Col, Button, Divider, Modal } from 'antd';
import {
  LayoutHelper,
  ResizePanel,
  IconFont,
  TextButton,
  InputSearch,
} from '@/components';
import { formatDate } from '@/utils';
import Table from '@/components/Antd/Table';
import MoveFileModal from './TagForms/MoveFileModal';
import TagsModal from './TagForms';
import TreeMenu from './TreeMenu';
import styles from './index.less';

const { confirm } = Modal;

interface rowSelectionType {
  selectedRowKeys: Array<any>;
  selectedRows: Array<any>;
  onChange: (rowKeys: Array<any>, rows: Array<any>) => void;
}
interface BaseButtonProps {
  name: string;
  icon: string;
  key: string;
  type?: ButtonType;
}
// 操作按钮
const operationButton: Array<BaseButtonProps> = [
  {
    name: '新增标签',
    icon: 'icon-plus',
    key: 'add',
    type: 'primary',
  },
  // {
  //   name: '移动',
  //   key: 'move',
  //   icon: 'icon-move',
  // },
  {
    name: '刷新',
    key: 'reload',
    icon: 'icon-reload',
  },
  {
    name: '删除',
    key: 'delete',
    icon: 'icon-delete',
  },
];

interface TagsProps {
  dispatch: any;
  tags: any;
  user: any;
  listtagLoading: boolean;
  allTagTotal: number;
}
interface sortcontentType {
  name: string;
  value: string;
}
const Tags = connect<{}, {}, {}, { tags: any; user: any; loading: any }>(
  ({ tags, user, loading }) => ({
    tags,
    user,
    listtagLoading: loading.effects['tags/listtag'],
  }),
)((props: TagsProps) => {
  const {
    tags: {
      tagsList,
      tagsTotal,
      treeDataList,
      allTagTotal,
      searchTreeData,
      classifyArr,
      listleveldirloading,
    },
    user: { currentPathAuth },
    dispatch,
    listtagLoading,
  } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState([]); // 选中的数据的key值
  const [selectedRows, setSelectedRows] = useState<Array<any>>([]); // 选中的数据的所有数据
  const [currentPage, setCurrentPage] = useState(1); // 当前页码
  const [currentPageSize, setCurrentPageSize] = useState(15); // 当前每页条数
  const [searchcontent, setSearchcontent] = useState<Array<sortcontentType>>(
    [],
  ); // 当前搜索内容
  const [sortcontent, setSortcontent] = useState({}); // 当前排序内容
  const [tagFormVisible, setTagFormVisible] = useState(false);
  const [moveFileModalVisible, setMoveFileModalVisible] = useState(false);
  const [tagItemData, setTagItemData] = useState({});
  const { Sider, Content, InnerBox, StrechBox, HeightContext } = LayoutHelper;

  function getDirectoryTreeData() {
    dispatch({
      type: 'tags/getDirectoryTreeData',
      param: {},
    });
  }

  function listtag() {
    dispatch({
      type: 'tags/listtag',
      param: {
        searchcontent,
        sortcontent,
        page: currentPage,
        rows: currentPageSize,
      },
    }).then(() => {
      setSelectedRowKeys([]);
      setSelectedRows([]);
    });
  }

  useEffect(() => {
    listtag();
  }, [sortcontent, currentPage, currentPageSize, searchcontent]);

  useEffect(() => {
    getDirectoryTreeData();
  }, []);

  useEffect(() => {
    if (tagFormVisible === false && Object.keys(tagItemData).length > 0) {
      setTagItemData({});
    }
  }, [tagFormVisible]);
  function onSelectChange(rowKeys, rows) {
    setSelectedRowKeys(rowKeys);
    setSelectedRows(rows);
  }
  function showDetail(record) {
    const { dirid, name, id, dirname, topid } = record;
    setTagItemData({
      dirid,
      dirname,
      name,
      id,
      topid,
    });
    setTagFormVisible(true);
  }

  function checkPage(length) {
    const newCurrentPage = currentPage === 1 ? 1 : currentPage - 1;
    if (tagsList.length === length && newCurrentPage !== currentPage) {
      setCurrentPage(newCurrentPage);
    } else {
      listtag();
    }
  }

  function deltagPost(record) {
    const { id, name } = record;
    confirm({
      icon: (
        <IconFont
          type="icon-exclamation-circle"
          style={{ color: '#ffc446', marginRight: 16 }}
        />
      ),
      title: '删除提醒',
      content: `您要删除 ${name} 标签吗?`,
      okText: '删除',
      cancelText: '取消',
      onOk() {
        return new Promise((resolve) => {
          dispatch({
            type: 'tags/deltag',
            param: {
              ids: Array.of(id),
              names: Array.of(name),
            },
          }).then(() => {
            getDirectoryTreeData();
            resolve();
            checkPage(1);
          });
        });
      },
    });
  }

  function deltag(record, e) {
    e.stopPropagation();
    deltagPost(record);
  }

  const rowSelection: rowSelectionType = {
    selectedRowKeys,
    selectedRows,
    onChange: onSelectChange,
  };

  const columns: Array<any> = [
    {
      title: '标签名 ',
      key: 'name',
      dataIndex: 'name',
      sorter: true,
      render: (val) => {
        return (
          <Ellipsis fullWidthRecognition tooltip lines={1}>
            {val}
          </Ellipsis>
        );
      },
    },
    {
      title: '标签类目',
      key: 'property',
      dataIndex: 'property',
      sorter: true,
      render: (val) => {
        return (
          <Ellipsis fullWidthRecognition tooltip lines={1}>
            {val}
          </Ellipsis>
        );
      },
    },
    {
      title: '引用次数',
      key: 'citations',
      dataIndex: 'citations',
      sorter: true,
    },
    {
      title: '创建时间',
      key: 'created',
      width: 165,
      dataIndex: 'created',
      sorter: true,
      render: (created) => formatDate(created),
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      render: (record) => {
        return (
          <>
            <TextButton onClick={() => showDetail(record)}>编辑</TextButton>
            <Divider type="vertical" />
            <TextButton onClick={(e) => deltag(record, e)}>删除</TextButton>
          </>
        );
      },
    },
  ];

  function batchDeltag() {
    const batchDeltagIds = selectedRows.map((v: any) => v.id);
    const batchDeltagNames = selectedRows.map((v: any) => v.name);
    const isOne = selectedRows.length === 1;
    confirm({
      icon: (
        <IconFont
          type="icon-exclamation-circle"
          style={{ color: '#ffc446', marginRight: 16 }}
        />
      ),
      title: '删除提醒',

      content: isOne
        ? `您要删除 ${selectedRows[0].name} 标签吗?`
        : `您要删除已选择的${batchDeltagIds.length}个标签吗?`,
      okText: '删除',
      cancelText: '取消',
      onOk() {
        return new Promise((resolve) => {
          dispatch({
            type: 'tags/deltag',
            param: {
              ids: batchDeltagIds,
              names: batchDeltagNames,
              type: 'batch',
            },
          }).then(() => {
            getDirectoryTreeData();
            resolve();
            checkPage(batchDeltagIds.length);
          });
        });
      },
    });
  }
  function reload() {
    listtag();
    getDirectoryTreeData();
  }
  // 3个按钮判断
  function openDialog(key) {
    switch (key) {
      case 'reload':
        reload();
        break;
      case 'delete':
        batchDeltag();
        break;
      case 'add':
        setTagFormVisible(true);
        break;
      case 'move':
        setMoveFileModalVisible(true);
        break;
      default:
        break;
    }
  }

  function handleTableChange(...param) {
    const { order, field } = param[2];
    if (order === undefined) {
      setSortcontent({});
    } else {
      setSortcontent({
        number: order === 'descend' ? 1 : 2, //  倒序:1,正序:2
        name: field,
      });
    }
  }

  function computedCurrentPage(size) {
    const min = (currentPage - 1) * size;
    if (!(tagsTotal > min)) {
      setCurrentPage(1);
    }
  }
  const newOperationButton =
    currentPathAuth === '1'
      ? operationButton.filter((v) => v.key === 'reload')
      : operationButton;

  const newColumns =
    currentPathAuth === '1' ? columns.slice(0, columns.length - 1) : columns;
  const delayedQuery = useCallback(
    debounce((value: string) => {
      const newSearchcontent: Array<any> = [];
      const nameArr = searchcontent.filter((item) => item.name === 'tagdirid');
      if (value !== '') {
        newSearchcontent.push({
          name: 'tagname',
          value,
        });
      }
      setSearchcontent([...nameArr, ...newSearchcontent]);
      setCurrentPage(1);
    }, 500),
    [searchcontent],
  );
  const tagFormProps = {
    tagFormVisible,
    setTagFormVisible,
    classifyArr,
    tagItemData,
    setTagItemData,
    getDirectoryTreeData,
    listtag,
  };
  const moveFileModalProps = {
    moveFileModalVisible,
    setMoveFileModalVisible,
    classifyArr,
    getDirectoryTreeData,
    listtag,
    selectedRowKeys,
    selectedRows,
  };
  return (
    <LayoutHelper>
      <Sider>
        <ResizePanel
          content={
            // @ts-ignore
            <TreeMenu
              classifyArr={treeDataList}
              getDirectoryTreeData={getDirectoryTreeData}
              listtag={listtag}
              searchcontent={searchcontent}
              setSearchcontent={setSearchcontent}
              allTagTotal={allTagTotal}
              searchTreeData={searchTreeData}
              currentPathAuth={currentPathAuth}
              listleveldirloading={listleveldirloading}
              setCurrentPage={setCurrentPage}
            />
          }
        />
      </Sider>
      <Content loading={listtagLoading}>
        <InnerBox>
          <Row gutter={8}>
            <Col span={24} className={styles.rightButton}>
              <>
                {newOperationButton.map((item) => {
                  return (
                    <Button
                      disabled={
                        !(item.key === 'add' || item.key === 'reload')
                          ? selectedRowKeys.length === 0
                          : false
                      }
                      type={item.type}
                      key={item.key}
                      danger={item.key === 'delete'}
                      onClick={() => openDialog(item.key)}
                    >
                      <IconFont type={item.icon} text={item.name} />
                    </Button>
                  );
                })}
                <InputSearch
                  className={styles.inputSearch}
                  allowClear
                  onChange={(e) => delayedQuery(e.target.value)}
                  placeholder="请输入标签名称搜索"
                />
              </>
            </Col>
          </Row>
        </InnerBox>
        <StrechBox>
          <HeightContext.Consumer>
            {(height) => {
              return (
                <Table
                  style={{ height }}
                  rowSelection={rowSelection}
                  columns={newColumns}
                  dataSource={tagsList}
                  rowKey="id"
                  pagination={{
                    current: currentPage,
                    total: tagsTotal,
                    pageSize: currentPageSize,
                    onChange: (page) => setCurrentPage(page),
                    onShowSizeChange: (_, size) => {
                      computedCurrentPage(size);
                      setCurrentPageSize(size);
                    },
                    showSizeChanger: true,
                    hideOnSinglePage: true,
                  }}
                  onChange={handleTableChange}
                />
              );
            }}
          </HeightContext.Consumer>
        </StrechBox>
      </Content>
      <TagsModal {...tagFormProps} />
      <MoveFileModal {...moveFileModalProps} />
    </LayoutHelper>
  );
});
export default Tags;
