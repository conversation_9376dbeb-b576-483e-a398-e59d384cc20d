import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { useDispatch, useSelector } from 'umi';
import { notification } from 'antd';
import { ConnectState, Dispatch, AnyAction } from '@/models/connect.d';
import { formatDate } from '@/utils';
import { TagsAdd, LayoutHelper, Table } from '@/components';
import { setPageParam, getPageParam } from '@dana/ui';
import { useUpdateTable } from './hooks';
import { TYPE_FILTERS, SUBMIT_FILTERS, DEFAULT_SELECTEDKEYS } from '../conf';
import Bar from './Bar';
import SwitchComp from './SwitchComp';
import OperateComp from './OperateComp';
import TaskNameComp from './TaskNameComp';
import { taskTypeMap } from './utils';
import { ThenType } from '../index.d';
import type { TreeRef } from '../index';

import styles from '../index.less';

const { Content, InnerBox, StrechBox } = LayoutHelper;
interface OwnProps {
  treeRef: TreeRef;
}
export type HomeRef = {
  setSearchOptions: (param) => void;
  resetSelectdRows: () => void;
  setTabList: (p: any[]) => void;
};

export type VisibleFuncType = (p: boolean) => void;
export type SelectedRowsType = { [propName: string]: any }[];
export type TaskType = {
  notename: string;
  [propName: string]: any;
};

const resolveQueryParams = (options) => {
  const params: any = {
    page: options.page || 1,
    pageSize: options.pageSize || 15,
    searchValue: options.searchValue || '',
    searchType: options.searchType || '任务名称',
    folderId: options.folderId,
    sortList: options.sorter ?? [],
    filterList: options.filters ?? [],
  };

  return params;
};

const Home = (props: OwnProps, ref) => {
  const { treeRef } = props;
  const keepData = getPageParam();

  const dispatch = useDispatch<Dispatch<AnyAction>>();
  const realtimeSync = useSelector((state: ConnectState) => state.realtimeSync);
  const loading = useSelector((state: ConnectState) => state.loading);

  const tableLoading =
    loading.effects['realtimeSync/getTaskDetail'] ||
    loading.effects['realtimeSync/getTableListNew'];

  //* state
  const [selectedRows, setSelectedRows] = useState<{ id: string }[]>([]);
  const [hasTableChange, sethasTableChange] = useState(false); // 是否改动了table，用于处理table选中状态
  const [searchValue, setSearchValue] = useState('');
  const [searchType, setSearchType] = useState('任务名称');

  // 表格状态（从任务详情中返回后需要保持原有的状态）
  const [tableStatus, setTableStatus] = useState(
    Object.keys(keepData).length === 0 || !keepData?.page
      ? {
          page: 1,
          pageSize: 15,
          sorter: null,
          filters: null,
          searchValue: '',
          searchType: '任务名称',
        }
      : {
          page: keepData.page,
          pageSize: keepData.pageSize,
          // searchValue: keepData.searchValue,
          // sorter: keepData.sortList,
          // filters: keepData.filterList,
        },
  );

  const { updateTableList } = useUpdateTable();

  const { newTableList, total } = realtimeSync;

  useEffect(() => {
    getTaskList();
    handleKeepData();
  }, []);

  function handleKeepData() {
    const folderId = keepData?.folderId || DEFAULT_SELECTEDKEYS;

    // console.log('keepData', keepData);
    if (keepData) {
      const newSearchType = keepData.searchType || '任务名称';
      setSearchValue(keepData.searchValue);
      setSearchType(newSearchType);
      // 因为seatchvalue赋值会调一遍onsearch导致页码变成1，所以加了个定时器
      setTimeout(() => {
        setSearchOptions({
          page: keepData.page,
          pageSize: keepData.pageSize,
          folderId: folderId === DEFAULT_SELECTEDKEYS ? '' : keepData.folderId,
          searchValue: keepData.searchValue,
          searchType: newSearchType,
          sorter: keepData.sortList,
          filters: keepData.filterList,
        });
      }, 10);
    } else {
      setSearchOptions({ folderId });
    }
  }

  function setSearchOptions(param) {
    const result = {};
    Object.keys(param).forEach((key) => {
      result[key] = param[key];
    });
    const newOptions = {
      ...tableStatus,
      ...result,
    };
    // console.log('options', newOptions);
    // console.log('result', result);
    setTableStatus(newOptions);
    getTableData(resolveQueryParams(newOptions));
  }

  function getTableData(payload) {
    dispatch({
      type: 'realtimeSync/getTableListNew',
      payload,
    }).then((data) => {
      if (data.code !== 200) {
        notification.error({ message: `列举任务失败，${data.result}!` });
      } else if (data.result?.result?.length === 0 && data.result.total > 0) {
        setSearchOptions({ page: 1 });
      }
    });
  }

  // 获取所有任务名，用于处理移动重名校验
  function getTaskList(dirid = '') {
    // const { dispatch, form, isEdit, editSource } = this.props;
    dispatch({
      type: 'realtimeSync/getTaskList',
      payload: {
        dirid,
        sorttype: 1,
        tasktype: -1,
        sort: 1,
        listall: true,
      },
    });
  }

  //* 清空选中状态
  const resetSelectdRows = () => {
    setSelectedRows([]);
  };

  //* 同时更新左侧树数据和表格数据
  const refreshData = () => {
    getTaskList();
    // 刷新左侧的树
    treeRef.current?.getFolderList();
    setSearchOptions({});
    resetSelectdRows();
  };

  //* 删除任务的方法(批量and单个)
  const handleDelete = (_e, ids) => {
    return new Promise((resolve, reject) => {
      (
        dispatch({
          type: 'realtimeSync/deleteTask',
          payload: {
            ids,
            realmessagetype: 1,
          },
        }) as unknown as Promise<ThenType<{ result: string }>>
      ).then((data) => {
        if (data.code === 200) {
          notification.success({
            message: '删除成功',
          });
          refreshData();
          resolve('success');
        } else {
          notification.error({ message: `删除任务失败，${data.result}!` });
          reject();
        }
      });
    });
  };

  //* 为了新增isSkip属性
  const getDeleteData = (rows) => {
    const selectedKeys = rows.map((row) => row.id);
    const selectedKeysInfo = newTableList.filter((item) =>
      selectedKeys.includes(item.id),
    );

    const newRows = selectedKeysInfo.map((item) => {
      return {
        ...item,
        isSkip: item.submitted || item.taskstatus === 0 || !!item.dependtask,
      };
    });
    return newRows;
  };

  //* ------- 表格相关 --------
  //* 表格多选切换
  const onSelectChange = (_, rows) => {
    setSelectedRows(rows);
  };

  const handleTableChange = (pagination, filters, sorter) => {
    const { pageSize, current } = pagination;
    let newSortList: { field: string; order: string }[] = [];
    if (Object.keys(sorter).length !== 0) {
      if (sorter.order) {
        const { field, order } = sorter;
        newSortList.push({ field, order });
      } else {
        newSortList = [];
      }
    }

    setSearchOptions({
      page: current,
      pageSize,
      sorter: newSortList,
      filters,
    });
    sethasTableChange(true);
  };

  const rowSelection = {
    selectedRowKeys: selectedRows.map((item) => item.id),
    onChange: onSelectChange,
  };
  // console.log('treeRef', treeRef, treeRef.current);

  const goToDetail = (record) => {
    // console.log(record);
    const path = record.tasktype === 4 ? 'tableSync' : 'pushSync';
    // console.log('treeRef', treeRef, treeRef.current);
    const folderId = treeRef.current.state.selectedKeys[0];

    // console.log('goToDetail', tableStatus.page);
    const { page, pageSize, sorter, filters } = tableStatus;
    // /collection/realtime/pushSync/detail
    setPageParam({
      pathname: `/collection/realtime/detail`,
      query: {
        taskId: record.id,
        type: path,
        // pageType: 'view', \
        // status,
      },
      keepData: {
        page,
        pageSize,
        searchValue,
        searchType,
        folderId,
        sortList: sorter,
        filterList: filters,
      },
    });
  };

  const getColumns = () => {
    return [
      {
        title: '任务名称',
        key: 'notename',
        dataIndex: 'notename',
        sorter: true,
        ...(keepData?.sortList && !hasTableChange
          ? {
              sortOrder:
                keepData?.sortList?.find((v) => v.field === 'notename')
                  ?.order || null,
            }
          : {}),
        className: styles.noteNameTd,
        render: (_text, record) => {
          return <TaskNameComp record={record} goToDetail={goToDetail} />;
        },
      },
      {
        title: '类型',
        key: 'notetype',
        dataIndex: 'notetype',
        width: 92,
        filters: TYPE_FILTERS,
        filterMultiple: true,
        onFilter: () => true,
        ...(keepData?.filterList && !hasTableChange
          ? {
              filteredValue: keepData?.filterList?.notetype || null,
            }
          : {}),
        className: `${styles.noteType}`,
        render({ tasktype }) {
          return <span>{taskTypeMap[tasktype]}</span>;
        },
      },
      {
        title: '上线状态',
        key: 'submitted',
        dataIndex: 'submitted',
        width: 100,
        filters: SUBMIT_FILTERS,
        filterMultiple: true,
        ...(keepData?.filterList && !hasTableChange
          ? {
              filteredValue: keepData?.filterList?.submitted || null,
            }
          : {}),
        onFilter: () => true,
        render: (_, record) => {
          return (
            <SwitchComp
              record={record}
              setSearchOptions={setSearchOptions}
              updateTableList={updateTableList}
            />
          );
        },
      },
      {
        title: '标签',
        key: 'tags',
        dataIndex: 'tags',
        render(tag) {
          const content = { tags: tag, isBoolen: true };
          return tag.length > 0 ? <TagsAdd {...content} /> : '--';
        },
      },
      {
        title: '任务描述',
        key: 'description',
        dataIndex: 'description',
        ellipsis: true,
      },
      {
        title: '创建时间',
        key: 'datecreated',
        dataIndex: 'datecreated',
        width: 150,
        sorter: true,
        ...(keepData?.sortList && !hasTableChange
          ? {
              sortOrder:
                keepData?.sortList?.find((v) => v.field === 'datecreated')
                  ?.order || null,
            }
          : {}),
        render: (text) => formatDate(text),
      },
      {
        title: '更新时间',
        key: 'updatetime',
        dataIndex: 'updatetime',
        width: 150,
        sorter: true,
        ...(keepData?.sortList && !hasTableChange
          ? {
              sortOrder:
                keepData?.sortList?.find((v) => v.field === 'updatetime')
                  ?.order || null,
            }
          : {}),
        render: (text) => formatDate(text),
      },
      {
        title: '操作',
        width: 85,
        render: (record) => {
          return (
            <OperateComp
              record={record}
              handleDelete={handleDelete}
              getDeleteData={getDeleteData}
              refreshData={refreshData}
            />
          );
        },
      },
    ];
  };

  //* 搜索
  const handleSearch = (value) => {
    // console.log('handleSearch', 1);
    setSearchValue(value);
    setSearchOptions({
      // categoryId,
      searchValue: value,
      page: 1,
    });
  };

  const handleChangeSearchType = (value) => {
    // console.log('handleSearch', 1);
    setSearchType(value);
    setSearchOptions({
      searchType: value,
      page: 1,
    });
  };

  useImperativeHandle(ref, () => {
    return {
      setSearchOptions,
      resetSelectdRows,
    };
  });
  const columns = getColumns();
  return (
    <Content className={styles.homeWrapped}>
      <InnerBox>
        <Bar
          tableList={newTableList}
          searchType={searchType}
          refreshData={refreshData}
          resetSelectdRows={resetSelectdRows}
          handleDelete={handleDelete}
          selectedRows={selectedRows}
          getDeleteData={getDeleteData}
          handleSearch={handleSearch}
          handleChangeSearchType={handleChangeSearchType}
          setSearchOptions={setSearchOptions}
          updateTableList={updateTableList}
        />
      </InnerBox>
      <StrechBox>
        {(height) => {
          return (
            <Table
              className={styles.wrappedTable}
              rowSelection={rowSelection}
              // @ts-ignore
              columns={columns}
              dataSource={newTableList}
              onChange={handleTableChange}
              rowClassName={(record) => {
                //* 如果是批量，设置自己的行样式
                if (record.tasktype === 3) {
                  return 'batchCell';
                }
                return 'commonCell';
              }}
              pagination={{
                pageSize: tableStatus.pageSize,
                current: tableStatus.page,
                total,
                showSizeChanger: true,
              }}
              style={{ height }}
              loading={tableLoading}
            />
          );
        }}
      </StrechBox>
    </Content>
  );
};

export default forwardRef(Home);
