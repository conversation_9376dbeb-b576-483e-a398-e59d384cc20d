.storeInfo {
  // 存储源选择已有表还是新建表
  .storeRadio {
    text-align: end;
    margin: 0 48px 12px;
  }

  .basicInfo {
    display: flex;
    justify-content: space-between;
    flex-flow: row wrap;

    :global {
      .ant-form-item {
        width: 50% !important;
        padding-right: 48px !important;
      }
    }

    .coverStartegyWrapper {
      width: 50% !important;
      margin-bottom: 12px;

      :global {
        .ant-form-item {
          width: 100% !important;
        }
      }
    }
  }

  .newStoreTitle {
    margin-left: 16px;
    margin-top: 4px;
    padding-left: 8px;
    border-left: 4px solid #1980ff;
    font-size: 14px;
    color: #1980ff;
  }

  // 表格样式
  .tableItem {
    margin: 0 0 12px -6px;
    padding-right: 48px;

    :global {
      .ant-form-item {
        width: 100% !important;
        padding-right: 0 !important;
      }

      .ant-table-body {
        .ant-empty-normal {
          margin: 27px 0;
        }
      }
    }
  }
}
