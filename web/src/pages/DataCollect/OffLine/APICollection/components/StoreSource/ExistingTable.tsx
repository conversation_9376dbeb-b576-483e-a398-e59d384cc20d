import React, {
  useState,
  useEffect,
  useMemo,
  useContext,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Form, Tooltip, Input } from 'antd';
import { useDispatch, useSelector } from 'umi';
import { fromJS } from 'immutable';
import _ from 'lodash';
import {
  historyTipText,
  strategyTipText,
} from '@/pages/DataCollect/OffLine/BatchExtractCollection/Single/StoreConfigs/Basic';
import { ConnectState, Dispatch, AnyAction } from '@/models/connect.d';
import { IconFont, SearchTreeSelect, FetchSelect } from '@/components';
import { STORE_WAY_TYPE } from '@/pages/DataCollect/OffLine/conf';
import { isHive } from '@/pages/DataCollect/OffLine/utils';
import { RadioButton, TagsAdd, Ellipsis } from '@dana/ui';
// import { checkDistributeKeyIsValid } from './include/Distribution';
import { Block, SettingView } from '../Views';
import type { TbDetailType } from '../Views/StoreSourceView';
import { getStoreTreeData, findTreeNode, getCatalogsStr } from '../../utils';
import { INITIAL_SOURCE } from '../../conf';
import { CommonItemContext } from '../../index';

import otherStyles from '../Views/index.less';

const storeTable = {
  tbid: '',
  tbname: '',
};
type ExistingTableProps = {
  setStoreParam: (params: any) => void;
};
type ExistingTableRef = {
  setStoreSource: (p) => void;
  setTbDetail: (p) => void;
  resetTable: (callback: () => void) => void;
};

//* 通用的表基本信息
const BasicView = ({ tbDetail, sourcedb, form }) => {
  const [strategy, setStrategy] = useState(sourcedb?.strategy);
  const [coverStrategy, setCoverStrategy] = useState(
    _.isEmpty(sourcedb?.conditioncover) ? 'whole' : 'partial',
  ); // 覆盖策略，默认全表覆盖

  return (
    <>
      <Form.Item label="所属类目">
        <span>
          {tbDetail ? getCatalogsStr(tbDetail?.catalogs || []) : '--'}
        </span>
      </Form.Item>
      <Form.Item label="数据策略" style={{ marginBottom: 0 }}>
        <Form.Item
          name={['sourcedb', 'strategy']}
          style={{ width: '100% !important' }}
          initialValue={sourcedb?.strategy}
          className={otherStyles.strategyBtn}
        >
          <RadioButton
            options={[
              { label: '覆盖', value: 0 },
              { label: '追加', value: 1 },
            ]}
            onChange={(e) => {
              setStrategy(e.target.value);
              setCoverStrategy('whole');
              form.setFieldsValue({
                sourcedb: {
                  coverStrategy: 'whole',
                  sql: undefined,
                },
              });
            }}
          />
        </Form.Item>
        <Tooltip title={strategyTipText}>
          <IconFont
            type="icon-question-circle"
            style={{
              position: 'absolute',
              top: 3,
              left: 167,
            }}
          />
        </Tooltip>
      </Form.Item>
      {strategy === 0 && (
        <div className={otherStyles.coverStartegyWrapper}>
          <Form.Item label="覆盖策略" style={{ width: '100% !important' }}>
            <Form.Item
              name={['sourcedb', 'coverStrategy']}
              // initialValue={
              //   !sourcedb.strateggy && !_.isEmpty(sourcedb.conditioncover)
              //     ? 'partial'
              //     : 'whole'
              // }
              noStyle
            >
              <RadioButton
                options={[
                  { label: '全表覆盖', value: 'whole' },
                  { label: '条件覆盖', value: 'partial' },
                ]}
                onChange={(e) => {
                  setCoverStrategy(e.target.value);
                  form.setFieldsValue({
                    sourcedb: {
                      sql: undefined,
                    },
                  });
                }}
              />
            </Form.Item>
          </Form.Item>
          {coverStrategy === 'partial' && (
            <Form.Item
              label=" "
              colon={false}
              className={otherStyles.tipIconBox}
              style={{ width: '100% !important' }}
            >
              <Form.Item
                name={['sourcedb', 'sql']}
                requiredMark
                // initialValue={
                //   _.isEmpty(sourcedb?.conditioncover)
                //     ? undefined
                //     : sourcedb.conditioncover
                // }
                rules={[{ required: true, message: '请输入条件' }]}
                noStyle
              >
                <Input
                  style={{ width: 210 }}
                  addonBefore="where"
                  placeholder="请输入条件"
                />
              </Form.Item>
              <Tooltip
                placement="right"
                title={
                  <>
                    <div>
                      1.
                      请输入标准SQL中where关键字后的筛选条件(不包括where)，支持使用全局参数
                    </div>
                    <div>2. Hive仅支持指定分区覆盖</div>
                  </>
                }
              >
                <IconFont
                  type="icon-question-circle"
                  style={{
                    width: 16,
                    left: 220,
                    position: 'absolute',
                    top: 2,
                  }}
                />
              </Tooltip>
            </Form.Item>
          )}
        </div>
      )}
      {/* 已有表+追加策略，才有存储表历史数据配置 */}
      {strategy === 1 && (
        <Form.Item label="存储表历史数据" style={{ marginBottom: 0 }}>
          <Form.Item
            name={['sourcedb', 'history']}
            style={{ width: '100% !important' }}
            className={otherStyles.strategyBtn}
          >
            <RadioButton
              options={[
                {
                  label: '清空',
                  value: true,
                },
                { label: '不清空', value: false },
              ]}
            />
          </Form.Item>
          <Tooltip title={historyTipText}>
            <IconFont
              type="icon-question-circle"
              style={{
                position: 'absolute',
                top: 3,
                left: 167,
              }}
            />
          </Tooltip>
        </Form.Item>
      )}
      <Form.Item label="表标签">
        {tbDetail?.tagList?.length ? (
          <TagsAdd tags={tbDetail?.tagList} collapsible />
        ) : (
          '--'
        )}
      </Form.Item>
      <Form.Item label="表描述">
        <Ellipsis showCenter={false} tooltip lines={1}>
          {tbDetail?.describe || '--'}
        </Ellipsis>
      </Form.Item>
      {STORE_WAY_TYPE.includes(tbDetail?.dbtype) && (
        <Form.Item label="压缩格式">
          <span>{tbDetail?.compresstype || '--'}</span>
        </Form.Item>
      )}
      {isHive(tbDetail?.dbtype) && tbDetail?.fileformat && (
        <Form.Item label="文件格式">
          <span>
            {tbDetail?.fileformat === 'text' ? 'Textfile' : 'Orcfile'}
          </span>
        </Form.Item>
      )}
    </>
  );
};

const ExistingTable = (
  props: ExistingTableProps,
  ref: React.Ref<ExistingTableRef>,
) => {
  const { form, setActiveKey, mappingRef, editSource, isEdit } =
    useContext(CommonItemContext);
  const dispatch = useDispatch<Dispatch<AnyAction>>();
  const {
    loading,
    apiCollect: { haveLayerlist, engineList },
  } = useSelector((state: ConnectState) => state);

  const [storeSource, setStoreSource] = useState(INITIAL_SOURCE);
  const [tablePayload, setTablePayload] = useState<{
    sourceid: string;
  }>({
    sourceid: editSource?.sourcedb?.sourceid,
  });

  // 初始化时设置部分表单值
  useEffect(() => {
    form?.setFieldsValue({
      sourcedb: {
        coverStrategy:
          !editSource?.sourcedb?.strategy &&
          !_.isEmpty(editSource?.sourcedb?.conditioncover)
            ? 'partial'
            : 'whole',
        sql:
          isEdit && !_.isEmpty(editSource?.sourcedb?.conditioncover)
            ? editSource?.sourcedb?.conditioncover
            : undefined,
      },
    });
  }, []);

  //* 表详情
  const [tbDetail, setTbDetail] = useState<TbDetailType | null>(
    isEdit ? editSource.tbDetail : null,
  );
  const [dataxFieldInfo, setDataxFieldInfo] = useState<any[]>(
    isEdit ? editSource?.sourcedb?.fieldinfo : null,
  );

  const tableListLoading = loading.effects['apiCollect/listStoreTable'];

  const treeData = useMemo(() => {
    // @ts-ignore
    return getStoreTreeData(fromJS(haveLayerlist).toJS(), engineList);
  }, [haveLayerlist]);

  // 切换存储源，主键应该跟着存储源表走，校验放到主键的文件里处理
  function onChangeDatabase(val) {
    const data = findTreeNode(treeData, (node) => node.value === val);
    setStoreSource(data);
    form?.setFieldsValue({
      sourcedb: {
        haveTable: undefined,
      },
    });

    setActiveKey(['basic', 'extract', 'storage']);
    // 清空表下拉框选项、表单、以及table的datasource
    setTbDetail(null);
    setDataxFieldInfo([]);
    const { sourceid } = data;
    setTablePayload({
      sourceid,
    });
    props.setStoreParam({
      database: data,
      table: storeTable,
      dataSource: [],
    });
  }

  const fetchOptions = (payload) => {
    const { searchValue = '', pageSize, key = '', ...restPayload } = payload;
    return (
      dispatch({
        type: 'apiCollect/listStoreTable',
        payload: {
          perpage: pageSize,
          reqtype: 2,
          filterds: [
            {
              filtertype: 1,
              filterfield: 'sourceid',
              filtercontent: [payload.sourceid],
            },
            key && {
              filtertype: 2,
              filterfield: 'tbname',
              filtercontent: [key],
            },
            searchValue && {
              filtertype: 0,
              filterfield: 'tbname',
              filtercontent: [searchValue],
            },
          ].filter((item) => item),
          ...restPayload,
        },
      }) as unknown as Promise<any>
    ).then(({ code, result, total }) => {
      if (code === 200) {
        const newResult = result || [];
        // const newResult = sortTables(result || []);
        const optionList = newResult.map((item) => {
          const isColumn =
            STORE_WAY_TYPE.includes(item.dbtype) &&
            item.orientation === 'column';
          const isPaimon = item.fileformat?.toLocaleLowerCase() === 'paimon';
          const isDisabled = isColumn || isPaimon;

          return {
            ...item,
            value: item.id,
            label: item.tbname,
            disabled: isDisabled,
            extraRender: isDisabled ? (
              <span className={otherStyles.disabledLabel}>
                {isColumn ? '（不支持）' : '（不支持paimon表）'}
              </span>
            ) : undefined,
          };
        });

        return {
          optionList,
          total,
        };
      }
      return {
        optionList: [],
        total: 0,
      };
    });
  };

  function onChangeTable(val, option) {
    //* 需要通知字段映射组件，是否切换了表
    mappingRef?.current?.setIsChangeTable(true);
    mappingRef?.current?.steIsRefresh(!mappingRef?.current?.isRefresh);

    const currentTable = option;
    (
      dispatch({
        type: 'apiCollect/storeTbDetail',
        payload: { id: currentTable.id },
      }) as Promise<any>
    ).then((res = {}) => {
      const {
        fieldInfo = [],
        fileformat = 'orc',
        partitionfield,
        partitioninfo,
        distributionBond,
      } = res;
      form?.setFieldsValue({ sourcedb: { fileformat } });
      setTbDetail(res);
      setDataxFieldInfo([]);
      const { children, ...tableData } = currentTable;

      props.setStoreParam({
        database: storeSource,
        table: tableData,
        dataSource: fieldInfo,
        partitionField: partitionfield,
        partitionInfoFields: partitioninfo ? partitioninfo?.fields : null,
        // 分布键
        distributionBond,
      });
      // checkUxDistributeKey(res);
      setTimeout(() => {
        setActiveKey([
          'basic',
          'extract',
          'storage',
          'mapping',
          'config',
          'advanced',
        ]);
      }, 0);
    });
  }

  // 每次切换到新建表页面时，已有表页面的数据需要清理重置
  function resetTable(callback) {
    setStoreSource(INITIAL_SOURCE);
    setTbDetail(null);
    setDataxFieldInfo([]);
    form?.setFieldsValue({
      sourcedb: {
        haveSource: undefined,
        haveTable: undefined,

        sql: undefined,
      },
    });
    callback();
  }

  useImperativeHandle(ref, () => ({
    resetTable,
    setStoreSource,
    setTbDetail,
    setDataxFieldInfo,
  }));
  return (
    <>
      <Block title="基础信息" style={{ width: '100%' }}>
        <div className={otherStyles.overviewItem}>
          <Form.Item
            label="存储源"
            name={['sourcedb', 'haveSource']}
            rules={[{ required: true, message: '请选择或搜索存储源' }]}
          >
            <SearchTreeSelect
              treeIcon
              showSearch
              expandOnClick
              showEmptyInner
              treeData={treeData}
              placeholder="请选择或搜索存储源"
              onChange={onChangeDatabase}
            />
          </Form.Item>
          <Form.Item
            label="表名称"
            name={['sourcedb', 'haveTable']}
            rules={[{ required: true, message: '请选择或搜索表' }]}
          >
            <FetchSelect
              placeholder="请选择或搜索表"
              optionFilterProp="label"
              fetchOptions={fetchOptions}
              style={{ width: '100%' }}
              fetchParams={tablePayload}
              loading={tableListLoading}
              onChange={onChangeTable}
              icon={
                <IconFont
                  type="icon-table"
                  style={{ color: '#1980ff', marginRight: 8 }}
                />
              }
            />
          </Form.Item>
          {tbDetail && (
            <BasicView
              tbDetail={tbDetail}
              sourcedb={editSource?.sourcedb}
              form={form}
            />
          )}
        </div>
      </Block>
      {tbDetail && (
        <SettingView tbDetail={tbDetail} dataxFieldInfo={dataxFieldInfo} />
      )}
    </>
  );
};

export default forwardRef(ExistingTable);
