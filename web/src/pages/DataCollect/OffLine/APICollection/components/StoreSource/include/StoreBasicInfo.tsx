import React, {
  useState,
  useMemo,
  useCallback,
  useContext,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Form, Input, Tooltip } from 'antd';
import { debounce } from 'lodash';
import { fromJS } from 'immutable';
import { useDispatch, useSelector, useModel } from 'umi';
import { ConnectState, Dispatch, AnyAction } from '@/models/connect.d';
import {
  IconFont,
  RadioButton,
  SearchTreeSelect,
  ColorTagTreeSelect,
} from '@/components';
import { getStandardRules, REGEXP } from '@/utils';
import { isHive } from '@/pages/DataCollect/OffLine/utils';
import {
  SUPPORT_DISTRIBUTE_TYPE,
  STORE_WAY_TYPE,
} from '@/pages/DataCollect/OffLine/conf';

import { CommonItemContext } from '../../../index';
import { getStoreTreeData, getCatalist, findTreeNode } from '../../../utils';
import { INITIAL_SOURCE } from '../../../conf';

import styles from '../index.less';

type StoreBasicRef = {
  storeSource: typeof INITIAL_SOURCE;
};
interface BasicProps {
  setStoreParam: (param: any) => void;
  getFieldTrans: () => void;
  getDistribution: (p: string) => void;
}
const StoreBasicInfo = (props: BasicProps, ref: React.Ref<StoreBasicRef>) => {
  const { setPrimKeys, setStoreMode, storeMode } = useModel(
    'DataCollect.OffLine.apiCollect',
  );
  const { getFieldTrans, getDistribution, setStoreParam } = props;
  const { form, tabKey } = useContext(CommonItemContext);
  const dispatch = useDispatch<Dispatch<AnyAction>>();

  const {
    apiCollect: {
      store,
      // extract,
      layerlist,
      cataloglist,
      tableTags,
      engineList,
    },
  } = useSelector((state: ConnectState) => state);

  const [storeSource, setStoreSource] = useState(INITIAL_SOURCE);
  const [tbNameArr, setTbNameArr] = useState<Array<string>>([]);

  const [strategy, setStrategy] = useState(0); // 数据策略，默认覆盖
  const [coverStrategy, setCoverStrategy] = useState('whole'); // 覆盖策略，默认全表覆盖

  // const extractItem = useMemo(() => {
  //   return extract.find((v) => v.key === tabKey);
  // }, [extract]);

  const sourceTree = useMemo(() => {
    const data = getStoreTreeData(
      (fromJS(layerlist) as any).toJS(),
      engineList,
    );
    return data;
  }, [layerlist]);

  const catalogTree = useMemo(
    () =>
      storeSource.layerid
        ? getCatalist((fromJS(cataloglist) as any).toJS(), storeSource.layerid)
        : [],
    [storeSource.layerid],
  );

  const setStoreTbname = useCallback(
    debounce((e) => {
      const item = store.find((v) => v.key === tabKey);
      setStoreParam({ ...item, table: { tbname: e.target.value, tbid: '' } });
    }, 1000),
    [store],
  );

  /* 
    选择存储源后，需要：
    1.设置当前的存储源数据storeSource和文件类型
    2.检测或重置表名称状态和重置所属类目信息
    3.如果是teryx存储源，获取分布键相关
    4.调用字段转换接口重置表格数据
  */
  async function onChangeDatabase(val) {
    const data = findTreeNode(sourceTree, (node) => node.value === val);
    const newstoragetype = isHive(data.sourcetype);
    if (newstoragetype) {
      setPrimKeys([]);
    }
    form?.setFieldsValue({
      sourcedb: {
        catalog: undefined,
        newstoragetype,
      },
    });
    await setStoreSource(data);
    const tbname = form?.getFieldValue(['sourcedb', 'newTable']);
    await validateTbname(tbname, data);

    setTimeout(() => {
      getFieldTrans();
      if (SUPPORT_DISTRIBUTE_TYPE.includes(data.sourcetype)) {
        getDistribution(data.sourcetype);
      }
    }, 500);
  }

  // 新建表名称校验
  function validateTbname(name: string, data: Partial<typeof INITIAL_SOURCE>) {
    const { engineid, sourcename, dbname } = data;
    (
      dispatch({
        type: 'apiCollect/getCurrentTables',
        payload: { engineid, sourcename, dbname },
      }) as Promise<Array<string>>
    ).then((tables) => {
      setTbNameArr(tables);
      const isRepeat = name && tables.find((v) => v === name);
      if (isRepeat) {
        form?.setFields([
          {
            name: ['sourcedb', 'newTable'],
            value: name,
            errors: ['名称已存在, 请重命名'],
          },
        ]);
      }
      // eslint-disable-next-line
      name && form?.validateFields([['sourcedb', 'newTable']]);
    });
  }

  useImperativeHandle(ref, () => ({ storeSource }));

  return (
    <>
      <Form.Item
        label="存储源"
        name={['sourcedb', 'newSource']}
        rules={[{ required: true, message: '请选择存储源' }]}
      >
        <SearchTreeSelect
          treeIcon
          showSearch
          expandOnClick
          showEmptyInner
          treeData={sourceTree}
          placeholder="请选择或搜索存储源"
          onChange={onChangeDatabase}
        />
      </Form.Item>
      <Form.Item
        label="表名称"
        name={['sourcedb', 'newTable']}
        rules={getStandardRules(
          '请输入表名称',
          ['', tbNameArr],
          'tableField',
          storeSource.sourcetype,
          'table',
        )}
      >
        <Input placeholder="请输入表名称" onChange={setStoreTbname} />
      </Form.Item>
      <Form.Item label="所属类目" name={['sourcedb', 'catalog']}>
        <SearchTreeSelect
          treeIcon
          showSearch
          expandOnClick
          treeData={catalogTree}
          placeholder="请选择或搜索类目信息"
        />
      </Form.Item>
      <Form.Item label="数据策略">
        <Form.Item
          noStyle
          name={['sourcedb', 'strategy']}
          initialValue={strategy}
        >
          <RadioButton
            options={[
              { label: '覆盖', value: 0 },
              { label: '追加', value: 1 },
            ]}
            onChange={(e) => {
              setStrategy(e.target.value);
              setCoverStrategy('whole');
              form?.setFieldsValue({
                sourcedb: {
                  coverStrategy: 'whole',
                  sql: undefined,
                },
              });
            }}
          />
        </Form.Item>
        <Tooltip
          title={
            <>
              <div>覆盖：先清空存储表中数据</div>
              <div style={{ marginLeft: 36 }}>再将采集的数据插入存储表</div>
              <div>追加：将采集的数据追加至存储表</div>
            </>
          }
        >
          <IconFont
            type="icon-question-circle"
            style={{
              position: 'absolute',
              marginLeft: 8,
            }}
          />
        </Tooltip>
      </Form.Item>
      {strategy === 0 && (
        <div className={styles.coverStartegyWrapper}>
          <Form.Item label="覆盖策略" style={{ width: '100% !important' }}>
            <Form.Item
              name={['sourcedb', 'coverStrategy']}
              initialValue={coverStrategy}
              noStyle
            >
              <RadioButton
                options={[
                  { label: '全表覆盖', value: 'whole' },
                  { label: '条件覆盖', value: 'partial' },
                ]}
                onChange={(e) => {
                  setCoverStrategy(e.target.value);
                  form?.setFieldsValue({
                    sourcedb: {
                      sql: undefined,
                    },
                  });
                }}
              />
            </Form.Item>
          </Form.Item>
          {coverStrategy === 'partial' && (
            <Form.Item
              label=" "
              colon={false}
              className={styles.tipIconBox}
              style={{ width: '100% !important' }}
            >
              <Form.Item
                name={['sourcedb', 'sql']}
                requiredMark
                initialValue={undefined}
                rules={[{ required: true, message: '请输入条件' }]}
                noStyle
              >
                <Input
                  style={{ width: 210 }}
                  addonBefore="where"
                  placeholder="请输入条件"
                />
              </Form.Item>
              <Tooltip
                placement="right"
                title={
                  <>
                    <div>
                      1.
                      请输入标准SQL中where关键字后的筛选条件(不包括where)，支持使用全局参数
                    </div>
                    <div>2. Hive仅支持指定分区覆盖</div>
                  </>
                }
              >
                <IconFont
                  type="icon-question-circle"
                  style={{
                    width: 16,
                    left: 220,
                    position: 'absolute',
                    top: 2,
                  }}
                />
              </Tooltip>
            </Form.Item>
          )}
        </div>
      )}
      <Form.Item label="表标签" name={['sourcedb', 'tags']}>
        <ColorTagTreeSelect
          placeholder="请选择或搜索表标签"
          tagList={tableTags}
        />
      </Form.Item>
      <Form.Item
        label="表描述"
        name={['sourcedb', 'describe']}
        rules={[
          {
            required: false,
            pattern: REGEXP.fieldComment.pattern,
            message: REGEXP.fieldComment.message,
          },
        ]}
      >
        <Input placeholder="请输入表描述信息" />
      </Form.Item>
      {STORE_WAY_TYPE.includes(storeSource.sourcetype) && (
        <>
          <Form.Item label="存储方式">
            <Form.Item
              noStyle
              name={['sourcedb', 'orientation']}
              initialValue="column"
            >
              <RadioButton
                options={[
                  {
                    label: '行存',
                    value: 'row',
                  },
                  {
                    label: '列存',
                    value: 'column',
                  },
                ]}
                onChange={(e) => setStoreMode(e.target.value)}
              />
            </Form.Item>
            <Tooltip
              overlayStyle={{ maxWidth: '397px' }}
              title={
                <div>
                  <div>行存储适用于OLTP任务类的表，对更新删除较方便</div>
                  <div>但占用存储空间大；列存适合OLAP任务类的表，支</div>
                  <div>持压缩存储，对数据分析、计算、聚合、多表JOIN</div>
                  <div>等很友好，但不支持UPDATE、Delete操作。</div>
                </div>
              }
            >
              <IconFont
                type="icon-question-circle"
                style={{ position: 'absolute', marginLeft: 8 }}
              />
            </Tooltip>
          </Form.Item>
          {storeMode === 'column' && (
            <Form.Item label="压缩格式">
              <span>zlib</span>
            </Form.Item>
          )}
        </>
      )}

      {isHive(storeSource.sourcetype) && (
        <Form.Item
          label="文件格式"
          name={['sourcedb', 'fileformat']}
          style={{ marginBottom: '12px' }}
        >
          <RadioButton
            options={[
              { label: 'Orcfile', value: 'orc' },
              { label: 'Textfile', value: 'text' },
            ]}
          />
        </Form.Item>
      )}
    </>
  );
};

export default forwardRef(StoreBasicInfo);
