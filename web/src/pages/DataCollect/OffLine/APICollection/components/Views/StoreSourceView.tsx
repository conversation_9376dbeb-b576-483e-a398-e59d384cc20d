import { useEffect, useState } from 'react';
import { Form, Divider } from 'antd';
// import { useModel } from 'umi';

import { Table, TagsAdd, Ellipsis, FormBundle } from '@dana/ui';
// import FormBundle from '@/components/FormBundle';

import { isHive } from '@/pages/DataCollect/OffLine/utils';
import RenderFieldIcons, {
  FieldRecord,
} from '@/pages/DataService/DwsPublish/RenderFieldIcons';
import {
  SUPPORT_DISTRIBUTE_TYPE,
  STORE_WAY_TYPE,
} from '@/pages/DataCollect/OffLine/conf';
import _ from 'lodash';

import { PAGINATION, TABLE_LAYOUT } from '../../conf';
import { getCatalogsStr, renderEllipsis } from '../../utils';

import styles from './index.less';

//* 块分割线
export const Block = ({ title, children, ...rest }) => {
  const { className, style } = rest;
  return (
    <div className={className} style={{ ...style, width: '100%' }}>
      <div className={styles.blockTitle}>{title}</div>
      <Divider
        style={{
          margin: '3px 0 16px 16px',
          minWidth: 'calc(100% - 16px)',
          width: 'calc(100% - 16px)',
        }}
      />
      {children}
    </div>
  );
};

//* 分区信息详情组件
const PartitionView = ({ partitionField }) => {
  return (
    <>
      <Form.Item
        label="表分区"
        name={['store', 'partition']}
        style={{ marginBottom: 8 }}
      >
        <span>{partitionField.length > 0 ? '分区' : '不分区'}</span>
      </Form.Item>
      ;
      {partitionField.length > 0 && (
        <FormBundle<'hivePartitionSql'>
          scene="hivePartitionSql"
          status="view"
          viewInfos={{
            hivePartition:
              partitionField.map((item) => {
                if (item.ispreset === 'notpreset') {
                  return {
                    pattern: 'field',
                    field: item.name,
                  };
                } else if (item.ispreset === 'customize') {
                  return {
                    pattern: 'custom',
                    customField: item.name,
                  };
                } else {
                  return {
                    pattern: 'system',
                    type: item.name || item.field,
                    field: item.name || item.field,
                  };
                }
              }) || [],
          }}
        />
      )}
    </>
  );
};

// *分区信息、分布键
export const SettingView = ({ tbDetail, dataxFieldInfo = [] }) => {
  const [datasource, setDatasource] = useState<any[]>([]);

  const {
    dbtype: sourceType,
    partitionField,
    distributionMode,
    distributionBond,
    fieldInfo = [],
  } = tbDetail;

  // 这里处理初始化+切换存储源表的时候，重新设置主键，并进行校验
  useEffect(() => {
    // 业务主键
    const dataxPrimKeys = dataxFieldInfo
      ?.filter((v) => v.isprim)
      .map((v) => v.fieldname);

    const curData = fieldInfo.map((v, idx) => ({
      ...v,
      isPrim: v.isPrim || dataxPrimKeys.includes(v.fieldName),
      fieldIndex: idx,
    }));
    setDatasource(curData);
  }, [JSON.stringify(fieldInfo), JSON.stringify(dataxFieldInfo)]);

  const getTableColumns = () => {
    const res: any[] = [
      {
        title: '字段名',
        dataIndex: 'fieldName',
        render: (text, record: FieldRecord) => {
          return <RenderFieldIcons record={record} />;
        },
      },
      { title: '字段类型', dataIndex: 'fieldType', render: renderEllipsis },
      { title: '字段注释', dataIndex: 'comment', render: renderEllipsis },
    ];

    return res;
  };

  return (
    <>
      <Block title="字段信息">
        <div className={styles.tableItem}>
          <Form.Item {...TABLE_LAYOUT}>
            <Table
              columns={getTableColumns()}
              dataSource={datasource}
              custom
              pagination={PAGINATION}
            />
          </Form.Item>
        </div>
      </Block>
      {isHive(sourceType) && (
        <Block title="分区信息">
          <div className={styles.overviewItem}>
            <PartitionView partitionField={partitionField} />
          </div>
        </Block>
      )}

      {SUPPORT_DISTRIBUTE_TYPE.includes(sourceType) && (
        <Block title="分布信息">
          <div className={styles.overviewItem}>
            <Form.Item label="分布方式">{distributionMode || '--'}</Form.Item>
            {distributionMode === 'HASH' && (
              <Form.Item label="分布键">
                {distributionBond.length !== 0 ? (
                  <TagsAdd
                    tags={distributionBond.map((item) => ({
                      name: item,
                      color: '#1980FF',
                    }))}
                  />
                ) : (
                  '--'
                )}
              </Form.Item>
            )}
          </div>
        </Block>
      )}
    </>
  );
};

//* 通用的表基本信息
export const BasicView = ({ tbDetail, sourcedb }) => {
  return (
    <>
      <Form.Item label="所属类目">
        <span>
          {tbDetail ? getCatalogsStr(tbDetail?.catalogs || []) : '--'}
        </span>
      </Form.Item>
      <Form.Item label="数据策略">
        <span>{sourcedb?.strategy === 1 ? '追加' : '覆盖'}</span>
      </Form.Item>
      {sourcedb?.strategy === 0 && (
        <div className={styles.strategyViewWrapper}>
          <Form.Item label="覆盖策略">
            <span>
              {!_.isEmpty(sourcedb.conditioncover) ? '条件覆盖' : '全表覆盖'}
            </span>
          </Form.Item>
          {!_.isEmpty(sourcedb.conditioncover) && (
            <Form.Item label=" " colon={false}>
              <span>{sourcedb.conditioncover}</span>
            </Form.Item>
          )}
        </div>
      )}
      {/* 追加策略，才有存储表历史数据配置 */}
      {sourcedb?.strategy === 1 && (
        <Form.Item label="存储表历史数据">
          <span>{sourcedb?.history ? '清空' : '不清空'}</span>
        </Form.Item>
      )}
      <Form.Item label="表标签">
        {tbDetail?.tagList?.length ? (
          <TagsAdd tags={tbDetail?.tagList} collapsible />
        ) : (
          '--'
        )}
      </Form.Item>
      <Form.Item label="表描述">
        <Ellipsis showCenter={false} tooltip lines={1}>
          {tbDetail?.describe || '--'}
        </Ellipsis>
      </Form.Item>
      {STORE_WAY_TYPE.includes(tbDetail?.dbtype) && (
        <Form.Item label="压缩格式">
          <span>{tbDetail?.compresstype || '--'}</span>
        </Form.Item>
      )}
      {isHive(tbDetail?.dbtype) && tbDetail?.fileformat && (
        <Form.Item label="文件格式">
          <span>
            {tbDetail?.fileformat === 'text' ? 'Textfile' : 'Orcfile'}
          </span>
        </Form.Item>
      )}
    </>
  );
};

export type TbDetailType = {
  tagList: Array<any>;
  describe: string;
  tbName: string;
  dbName: string;
  layerName: string;
  catalogs: Array<any>;
  fieldInfo: Array<FieldRecord>;
};

const StoreSourceView = ({ editSource }) => {
  const tbDetail = editSource?.tbDetail;
  const dataxFieldInfo = editSource?.sourcedb?.fieldinfo || [];

  return (
    <>
      <Block title="基础信息">
        <div className={styles.overviewItem}>
          <Form.Item label="存储源">
            <span>{`${tbDetail?.layerName} / ${tbDetail?.storage}`}</span>
          </Form.Item>
          <Form.Item label="表名称">
            <span>{tbDetail?.tbName || '--'}</span>
          </Form.Item>
          <BasicView tbDetail={tbDetail} sourcedb={editSource?.sourcedb} />
        </div>
      </Block>

      {tbDetail && (
        <SettingView tbDetail={tbDetail} dataxFieldInfo={dataxFieldInfo} />
      )}
    </>
  );
};

export default StoreSourceView;
