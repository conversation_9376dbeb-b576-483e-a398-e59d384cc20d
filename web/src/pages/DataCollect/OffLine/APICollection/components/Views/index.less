@import '~themes/default';

.overviewItem {
  display: flex;
  justify-content: space-between;
  flex-flow: row wrap;

  .viewItem {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :global {
    .ant-form-item {
      width: 50% !important;
      padding-right: 48px !important;
    }
  }

  .strategyBtn {
    width: 60% !important;
  }

  .coverStartegyWrapper {
    width: 50% !important;
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;

    :global {
      .ant-form-item {
        width: 100% !important;
        padding-right: 48px !important;
      }
    }
  }

  .strategyViewWrapper {
    width: 50% !important;
    margin-bottom: 12px;

    :global {
      .ant-form-item {
        width: 100% !important;
      }
    }
  }
}

.disabledLabel {
  color: #999;
  position: absolute;
  right: 8px;
  top: 5px;
}

// 表格样式
.tableItem {
  margin: 0 0 12px -6px;
  padding-right: 48px;

  :global {
    .ant-form-item {
      width: 100% !important;
    }

    .ant-table-body {
      .ant-empty-normal {
        margin: 27px 0;
      }
    }
  }
}

.blockTitle {
  margin-left: 16px;
  margin-top: 4px;
  padding-left: 8px;
  border-left: 2px solid @primary-color;
  font-size: 14px;
  color: @primary-color;
}

.extractConfig {
  :global {
    .contentBlock-block {
      padding: 0 16px;
    }

    .ant-form-item-label {
      max-width: 110px;
    }

    .ant-col-18 {
      max-width: 357px;
    }
  }
}
