import React, {
  useRef,
  useState,
  useEffect,
  createContext,
  useMemo,
} from 'react';
import { useDispatch, useSelector, useModel } from 'umi';
import { debounce, xor } from 'lodash';
import {
  Spin,
  Form,
  Collapse,
  Space,
  Button,
  Tooltip,
  notification,
} from 'antd';
import { FormBundle } from '@dana/ui';
import { Dispatch, AnyAction, ConnectState } from '@/models/connect.d';
import {
  formatJson,
  formatJsonToEditForm,
} from '@/pages/MetadataManage/DataSource/component/JsonSchemaEditor/utils';
import {
  isHive,
  getTagsArr,
  // getSystemFields,
} from '@/pages/DataCollect/OffLine/utils';
import {
  // HAVE_FIELD_IDENTIFY,
  SUPPORT_DISTRIBUTE_TYPE,
} from '@/pages/DataCollect/OffLine/conf';
import LoadingButton from '@/pages/DataCollect/LoadingButton';
import checkTableFields from '@/pages/Workflow/DataProcess/component/utils/tableFieldsValidator';
import { scrollToPageItem } from '@/utils';
import {
  getPathNameByPathId,
  formatStringToArrayWithoutRoot,
  getTableRelation,
} from '@/pages/DataCollect/OffLine/APICollection/utils';
import { getStoreModeParam } from '@/pages/DataCollect/OffLine/BatchExtractCollection/utils';
import TimeParamsModal from '@/components/TimeParamsModal';
import { CommonItemProps } from './utils.d';
import {
  TypeDeleteTab,
  NoParamsVoid,
  TypeChangeTabName,
  TypeChangeEditStatus,
} from '../index.d';
import { FORM_LAYOUT } from './conf';
import {
  // DataSourceView,
  StoreSourceView,
  FieldMappingView,
  ConfigOverview,
} from './components/Views';
import BasicInfo from './components/BasicInfo';
import DataSource from './components/DataSource/index';
import StoreSource from './components/StoreSource';
import FieldMapping, { MappingRef } from './components/FieldMapping';
import ConfigInfo from './components/ConfigInfo';
import Advanced from './components/Advanced';
import Overview from './components/Overview';
import IDEDrawer from './components/IDEDrawer/index';
import styles from './index.less';

const commonAttr = { notetype: 'api', tasktype: 15 };
const { formatToReq, resToInitialValue } = FormBundle.exeStrategy;

// eslint-disable-next-line @typescript-eslint/naming-convention
const CommonItemContext = createContext<CommonItemProps>({
  tabKey: '',
  form: null,
  isEditing: false,
  editSource: {},
  isEdit: false,
  activeKey: [],
  treeRef: null,
  setActiveKey: () => {},
  resetStoreValue: () => {},
});

//* 上线运行过后又下线的任务只允许编辑基本信息和高级配置
const bundleList = {
  basic: (ref) => <BasicInfo ref={ref} />,
  extract: (ref, editSource) => {
    return editSource.hadOnline ? (
      <DataSource viewSource={editSource} />
    ) : (
      <DataSource ref={ref} />
    );
  },
  storage: (ref, editSource, ...params) => {
    const mappingRef = params && params[0] ? params[0].mappingRef : null;
    return editSource.hadOnline ? (
      <StoreSourceView editSource={editSource} />
    ) : (
      <StoreSource ref={ref} mappingRef={mappingRef} />
    );
  },
  config: (ref, editSource, _, form) => {
    return editSource.hadOnline ? (
      <ConfigOverview editSource={editSource} form={form} depJobRef={ref} />
    ) : (
      <ConfigInfo depJobRef={ref} />
    );
  },
  mapping: (ref, editSource, ...params) => {
    return editSource.hadOnline ? (
      <FieldMappingView editSource={editSource} {...params[0]} />
    ) : (
      <FieldMapping ref={ref} {...params[0]} />
    );
  },
  advanced: (ref) => {
    return <Advanced ref={ref} />;
  },
};

const panelList = [
  { name: 'basic', title: '填写基本信息' },
  { name: 'extract', title: '选择数据源' },
  { name: 'storage', title: '选择存储源' },
  { name: 'mapping', title: '字段映射' },
  { name: 'config', title: '调度策略' },
  { name: 'advanced', title: '高级配置' },
];

const ALL_KEYS = panelList.map((item) => item.name);

interface RealtimeProps {
  task: any;
  isOnLine: boolean;
  deleteTab: TypeDeleteTab;
  renderAlert: any;
  tabList: any;
  refreshTreeData: NoParamsVoid;
  changeTabName: TypeChangeTabName;
  changeEditStatus: TypeChangeEditStatus;
  treeRef: any;
}
// eslint-disable-next-line @typescript-eslint/naming-convention
const APICollection: React.FC<RealtimeProps & CommonItemProps> = (props) => {
  const {
    datasource: fieldTableData,
    tableSourceType,
    setPage,
    primKeys,
    apiInfo,
    init,
    scriptParam,
    content,
    ramValue,
    isLock,
    pypath,
    tabSize,
    setScriptParam,
    setContent,
    setIsLock,
    setRamValue,
    setTabSize,
    setPypath,
    getItemById,
    getReturnDataByPath,
    formatTableChildrenData,
    formatTreeChildrenData,
    setApiInfo,
  } = useModel('DataCollect.OffLine.apiCollect');

  const {
    task,
    tabKey,
    isEdit,
    isOnLine,
    renderAlert,
    changeEditStatus,
    changeTabName,
    refreshTreeData,
    treeRef,
  } = props;
  const [form] = Form.useForm();
  const dispatch = useDispatch<Dispatch<AnyAction>>();

  const [warpLoading, setWarpLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [activeKey, setActiveKey] = useState<string | string[]>(['basic']);
  const [highlightKeys, setHighlightKeys] = useState<string[]>(['basic']);
  const [overviewSource, setOverviewSource] = useState<any>(task);

  const [detailLoading, setDetailLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const [timeVarShow, setTimeVarShow] = useState<boolean>(false);
  const [timeVars, setTimeVars] = useState<any[]>([]);

  // 所有自定义分区字段及其分区规则
  const [cusPartiFieldRules, setCusPartiFieldRules] = useState([]);

  const {
    loading,
    dataSync: { allDbList, folderList },
    user: { canCurrentPageEdit },
    apiCollect: { extract, store, taglist, tableTags },
  } = useSelector((state: ConnectState) => state);

  const taskLoading = loading.effects['dataSync/getTaskDetail'] || false;
  const dbLoading = isEdit
    ? loading.effects['apiCollect/listExtractSource'] || false
    : false;

  const saveRef = useRef({
    resolveFun: () => {},
    rejectFun: () => {},
  });

  const apiRefForEditShow = useRef({});

  const basicRef = useRef<React.ElementRef<typeof BasicInfo>>(null);
  const dataSourceRef = useRef<React.ElementRef<typeof DataSource>>(null);
  const storeSourceRef = useRef<React.ElementRef<typeof StoreSource>>(null);
  const mappingRef = useRef<MappingRef>(null);
  const advancedRef = useRef<React.ElementRef<typeof Advanced>>(null);
  const depJobRef = useRef<any>(null);

  // 传入子组件的props
  const itemProps = {
    form,
    isEdit,
    tabKey,
    isEditing,
    editSource: overviewSource,
    activeKey,
    setActiveKey,
    changeEditStatus,
    resetStoreValue,
    treeRef,
    mappingRef,

    cusPartiFieldRules,
    setCusPartiFieldRules,
  };

  // 重新选择完数据源，存储源的数据要重置
  function resetStoreValue() {
    storeSourceRef.current?.resetStoreValue();
  }

  function getNamePath() {
    const item = store.find((v) => v.key === tabKey);
    const namepath =
      item && item.radio === 'creat'
        ? [
            ['sourcedb', 'newSource'],
            ['sourcedb', 'newTable'],
          ]
        : [
            ['sourcedb', 'haveSource'],
            ['sourcedb', 'haveTable'],
          ];
    return namepath;
  }

  // 根据当前打开的activeKey来设置标题栏的高亮，未打开但是通过校验的块也要高亮
  function getHighlightkeys() {
    const xorArr = xor(ALL_KEYS, activeKey);
    const tempArr: any = [];
    const basicVal = form.getFieldsValue(['name', 'dirid']);
    const extractVal = form.getFieldsValue([
      ['extractdb', 'dbname'],
      ['extractdb', 'tbname'],
      ['extractdb', 'parsepath'],
    ]);
    const namepath = getNamePath();
    const storageVal = form.getFieldsValue(namepath);
    for (let i = 0; i < xorArr.length; i++) {
      if (xorArr[i] === 'basic' && !Object.values(basicVal).some((v) => !v)) {
        tempArr.push('basic');
      } else if (
        xorArr[i] === 'extract' &&
        !Object.values(extractVal.extractdb).some((v) => !v)
      ) {
        tempArr.push('extract');
      } else if (
        xorArr[i] === 'storage' &&
        !Object.values(storageVal.sourcedb).some((v) => !v)
      ) {
        tempArr.push('storage');
      }
    }
    const newTempArr = [...new Set([...activeKey, ...tempArr])];
    if (
      newTempArr.length === 3 &&
      !newTempArr.includes('mapping') &&
      !Object.values(storageVal.sourcedb).some((v) => !v)
    ) {
      newTempArr.push('mapping');
    }
    setHighlightKeys(newTempArr);
  }

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    getHighlightkeys();
  }, [activeKey]);

  function onActiveKeyChange(val) {
    // 当前状态是准备打开还是关闭
    const isOpening = val.length > activeKey.length;
    // 当前panel只有一个是打开的情况下，点击关闭， 关闭所有的panel
    if (!val.length || !isOpening) {
      // console.log(1);
      setActiveKey(val);
      return;
    }
    // 如果是val.length === 1的情况下，需要多加一层判断是减少关闭还是打开
    // 当前点击的panel
    const currentKey = xor(val, activeKey)[0];
    const basicVal = form.getFieldsValue(['name', 'dirid']);
    const extractVal = form.getFieldsValue([
      ['extractdb', 'dbname'],
      ['extractdb', 'tbname'],
      ['extractdb', 'parsepath'],
    ]);
    const storageVal = form.getFieldsValue(getNamePath());
    const isBasic = Object.values(basicVal).some((v) => !v);
    const isExtract = Object.values(extractVal.extractdb).some((v) => !v);
    const isStorage = Object.values(storageVal.sourcedb).some((v) => !v);

    // 根据当前选中的标题栏开始校验， 第一个选择基础信息的可以不用校验打开
    if (currentKey === 'basic') {
      // console.log(2);
      setActiveKey(val);
      // 如果当前点击的是数据源标题栏，先看一下数据源页面有没有通过校验，如果通过了，直接打开，否则，校验基础信息后打开
      // 如果高亮的highlightKeys中包含，说明是通过校验的，有值的模块。
    } else if (currentKey === 'extract') {
      if (highlightKeys.includes('extract')) {
        // console.log(3);
        setActiveKey(val);
      } else {
        form.validateFields(['name', 'dirid']).then(() => {
          // console.log(4);
          setActiveKey(val);
        });
      }
      // 如果当前点击的是存储源标题栏，
      // 1.先查看当前模块是否通过校验了，通过了直接打开
      // 2.如果未通过，需要校验
    } else if (currentKey === 'storage') {
      if (highlightKeys.includes('storage')) {
        // console.log(5);
        setActiveKey(val);
      } else if (isBasic) {
        setValidateKey('basic');
      } else if (isExtract) {
        setValidateKey('extract');
      } else {
        // console.log(6);
        setActiveKey(val);
      }
    } else if (currentKey === 'mapping') {
      // 如果点击的是字段映射标题栏，
      // 1.先看高亮的数组中是否包含前三个，如果包含，说明前三个都已经通过校验了，这时候直接打开
      // 2.如果不包含，这时候查找出前三个模块中哪个没有被校验，然后打开未被校验的模块，触发该模块的校验
      if (!isBasic && !isExtract && !isStorage) {
        // console.log(7);
        setActiveKey(val);
      } else if (isBasic) {
        setValidateKey('basic');
      } else if (isExtract) {
        setValidateKey('extract');
      } else if (isStorage) {
        setValidateKey('storage');
      }
    } else if (currentKey === 'advanced' || currentKey === 'config') {
      if (!isBasic && !isExtract && !isStorage) {
        // console.log(8);
        setActiveKey(val);
      } else if (isBasic) {
        setValidateKey('basic');
      } else if (isExtract) {
        setValidateKey('extract');
      } else if (isStorage) {
        setValidateKey('storage');
      }
    } else {
      // console.log(9);
      setActiveKey(val);
    }
  }

  function setValidateKey(type) {
    if (type === 'basic') {
      form.validateFields(['name', 'dirid']);
      // console.log(10);
      setActiveKey([...new Set([...activeKey, 'basic'])]);
    } else if (type === 'extract') {
      form.validateFields([
        ['extractdb', 'dbname'],
        ['extractdb', 'tbname'],
        ['extractdb', 'parsepath'],
      ]);
      // console.log(11);
      setActiveKey([...new Set([...activeKey, 'extract'])]);
    } else if (type === 'storage') {
      form.validateFields(getNamePath());
      // console.log(12);
      setActiveKey([...new Set([...activeKey, 'storage'])]);
    }
  }

  // @ts-ignore
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function onSave(_key, { resolve, reject }) {
    saveRef.current.resolveFun = resolve;
    saveRef.current.rejectFun = reject;
    onCheckAndSave();
  }

  const getDirName = (dir) => {
    const isNewFolder = typeof dir !== 'string';
    const targetFolder = folderList.find((item) => item.id === dir);
    return isNewFolder ? dir.title : targetFolder?.name || '';
  };

  const checkHasOtherError = () => {
    //! 1. 检测字段映射是否有误，若有误则不允许保存
    const isFieldError = Object.values(
      mappingRef.current?.fieldError as any,
    ).some((item) => item);
    if (isFieldError) {
      form.scrollToField(`fieldMap`, { block: 'nearest' });
      return true;
    }
    return false;
  };

  //* 查询存储源表详情
  const fetchTableDetail = (taskDetail) => {
    if (!taskDetail?.sourcedb?.tableid) {
      notification.error({ message: '获取存储表详情失败' });
      console.log('未获取到详情里的sourcedb.tableid');
      return;
    }
    (
      dispatch({
        type: 'apiCollect/storeTbDetail',
        payload: { id: taskDetail?.sourcedb?.tableid },
      }) as unknown as Promise<any>
    ).then((result) => {
      const data = {
        ...taskDetail,
        tbDetail: { ...result, layerName: taskDetail.sourcedb?.layername },
      };
      // console.log('data', data);
      setOverviewSource(data);
    });
  };

  useEffect(() => {
    const { extractdb } = overviewSource;
    if (extractdb) {
      const newScriptParam = extractdb.pyparams?.map((v) => ({
        id: v,
        name: v,
      }));
      setScriptParam(newScriptParam);
      setContent(extractdb.pycontent);
      setIsLock(extractdb.pylock);
      setRamValue(extractdb.pyrunram);
      setTabSize(extractdb.pycode);
      setPypath(extractdb.pypath);
    }
  }, [overviewSource]);

  const fetchDetail = (id) => {
    //* 请求详情数据
    setDetailLoading(true);
    (
      dispatch({
        type: 'dataSync/getTaskDetail',
        payload: { id },
      }) as any
    ).then(({ code, result }) => {
      if (code === 200) {
        result.tablerelation = {
          nodes: result.tablerelation.nodes || [],
          edges: result.tablerelation.edges || [],
        };
        const targetDataSource = (allDbList as any).find(
          (v) => v.id === result.extractdb.id,
        );
        if (targetDataSource) {
          result.extractdb.dbname = targetDataSource.dbname;
        }
        result.hadOnline = result?.realinfo?.offlinerecord;
        fetchTableDetail(result);
        setDetailLoading(false);
        setTimeVars(result?.globalvars?.timevars || []);

        // *详情接口获取自定义分区字段的分区规则数据
        setCusPartiFieldRules(
          result?.sourcedb?.partitionfield
            ?.filter((v) => v.ispreset === 'customize')
            ?.map((v) => ({
              ...v,
              customField: v.name,
              customizeRule: {
                sourceField: v.customizerule?.fieldname ?? '',
                formatSql: v.customizerule?.formatsql ?? '',
              },
            })),
        );
      }
    });
  };
  function getAdvancedValue(values) {
    const { timeout, errlogcount, isrecorderr } = values;
    // console.log(1, values)
    const advanced = {
      timeout,
      errorinfo: { errlogcount },
      isrecorderr,
    };
    return advanced;
  }

  async function onCheckAndSave() {
    setWarpLoading(true);
    form
      .validateFields()
      .then(async (values) => {
        // .then((values) => {
        // if (overviewSource?.hadOnline) {
        //   console.log(1);
        //   updateBasicAdvancedInfo(values);
        //   return;
        // }
        //* 检测是否有其他的报错
        const isError = checkHasOtherError();
        if (isError) {
          console.log(2);
          setWarpLoading(false);
          return;
        }
        // 处理新建表的跨页校验
        const storageItem = store.find((v) => v.key === tabKey);
        if (!isEdit && storageItem.radio === 'creat') {
          const pageSize = 10;
          const errorPage = await checkTableFields(
            fieldTableData,
            tableSourceType,
            pageSize,
          );
          if (typeof errorPage === 'number') {
            await setPage(errorPage);
            form.validateFields().catch((errData) => {
              form.scrollToField(errData.errorFields[0].name, {
                block: 'nearest',
              });
            });
            setWarpLoading(false);
            console.log(3);
            return;
          }
        }

        const validateAllFields = storeSourceRef.current?.validateAllFields;
        // *新建表字段的校验
        const tableFieldIsError =
          validateAllFields &&
          (await (validateAllFields as any)(
            storageItem.dataSource,
            storageItem.database.sourcetype,
          )) === 'error';

        if (tableFieldIsError) {
          saveRef.current.rejectFun();
          setWarpLoading(false);
          console.log(4);
          return;
        }

        // v592hive存储源表设置条件覆盖时若未配置分区字段则不允许保存
        const isPartialCoverInValid =
          storageItem?.database?.sourcetype === 'hive' &&
          !(values?.hivePartition ?? storageItem?.partitionField)?.length &&
          values?.sourcedb?.strategy === 0 &&
          values?.sourcedb?.coverStrategy === 'partial';

        form.setFields([
          {
            name: ['sourcedb', 'sql'],
            errors: isPartialCoverInValid
              ? ['Hive仅支持指定分区覆盖，当前无分区字段，请重新配置']
              : '',
          },
        ]);
        if (isPartialCoverInValid) {
          setWarpLoading(false);
          return;
        }

        const extractItem = extract.find((v) => v.key === tabKey);

        const {
          name,
          description = '',
          tags = [],
          extractdb,
          sourcedb,
        } = values;
        // console.log(1, values)
        const advancedconfig = getAdvancedValue(values);

        const dirname = getDirName(values.dirid);
        const dirid = values.dirid.title ? '' : values.dirid;
        const fileformat = form.getFieldValue(['sourcedb', 'fileformat']);
        const allTags = getTagsArr(taglist, []);
        const taskParams = {
          ...commonAttr,
          name,
          dirname,
          dirid,
          filetype: fileformat,
          description,
          tags: allTags.filter((item) => tags.includes(item.id)),
        };

        // 2.检测是否是新建表。如果是，配置新建表参数，完成建表请求
        // 3.完成新建或更新操作
        if (storageItem.radio === 'creat') {
          // *分区字段信息
          // *新建可编辑分区时取自values.hivePartition，否则取自storageItem.partitionField
          const partitionField = (values.hivePartition || []).map((item) => {
            const curCustomField = cusPartiFieldRules.find(
              (v: any) => v.customField === item?.customField,
            );

            return {
              name: item.pattern === 'custom' ? item.customField : item.field,
              ispreset:
                item.pattern === 'system'
                  ? item.type
                  : item.pattern === 'custom'
                  ? 'customize'
                  : 'notpreset',
              fieldtype: 'string',
              annitation: '',

              ...(item.pattern === 'custom' &&
                curCustomField && {
                  customizerule: {
                    fieldname: curCustomField?.customizeRule?.sourceField ?? '',
                    formatsql: curCustomField?.customizeRule?.formatSql ?? '',
                  },
                }),
            };
          });

          creatStoreTable(
            taskParams,
            extractItem,
            { ...storageItem, partitionField },
            sourcedb,
            extractdb,
            advancedconfig,
            values,
          );
        } else {
          // *分区字段信息
          // *新建可编辑分区时取自values.hivePartition，否则取自storageItem.partitionField
          const partitionField = (storageItem?.partitionField || []).map(
            (item) => {
              const curCustomField = cusPartiFieldRules.find(
                (v: any) => v.name === item?.name,
              );

              return {
                ...item,
                customizerule: {
                  fieldname: curCustomField?.customizeRule?.sourceField ?? '',
                  formatsql: curCustomField?.customizeRule?.formatSql ?? '',
                },
              };
            },
          );

          newOrUpdateTask(
            taskParams,
            extractItem,
            {
              ...storageItem,
              partitionField,
            },
            undefined,
            sourcedb,
            advancedconfig,
            values,
          );
        }
      })
      .catch((err) => {
        console.log('err', err);
        saveRef.current.rejectFun();
        setWarpLoading(false);
        if (err && err.errorFields?.length > 0) {
          // const { errorFields } = err;
          // form.scrollToField(errorFields[0].name, {
          //   block: 'nearest',
          // });
          // 用上面的有时候没跳过去
          scrollToPageItem('.ant-form-item-explain-error');
        } else {
          console.log('出现没有报错的项');
          onCheckAndSave();
        }
      });
  }

  // 新建表
  function creatStoreTable(
    params,
    extractItem,
    storageItem,
    sourceValue,
    extractValue,
    advancedconfig,
    values,
  ) {
    const {
      catalog = '',
      describe = '',
      tags = [],
      distributionmode = 'HASH',
      distributionbond = [],
      compresstype,
      orientation,
    } = sourceValue;
    const { dataSource, database, table } = storageItem;

    const fieldinfo = dataSource.map((v) => ({
      number: v.key,
      name: v.fieldName,
      fieldtype: v.fieldType,
      annotation: v.comment,
      isprim: primKeys.includes(v.fieldName),
    }));

    const addTableParam = {
      // 固定参数
      id: '',
      tbtype: '未分类',
      // 从modal层数据获取
      engineid: database.engineid,
      layername: database.layername,
      layerid: database.layerid,
      sourceid: database.sourceid,
      dbname: database.dbname,
      sourcedb: database.sourcename,
      dbtype: database.sourcetype,
      schema: isHive(database.sourcetype) ? '' : database.sourcename,
      tbname: table.tbname,
      fileformat: params.filetype,
      fieldinfo,
      extracttype: 'api',
      // 表单获取的参数
      catalogid: catalog,
      describe,
      distributionmode: SUPPORT_DISTRIBUTE_TYPE.includes(database.sourcetype)
        ? distributionmode
        : '',
      distributionbond: Array.isArray(distributionbond)
        ? distributionbond
        : Array.of(distributionbond),
      orientation,
      ...(orientation === 'column' && { compresstype }),
      taglist: getTagsArr(tableTags, []).filter((item) =>
        tags.includes(item.id),
      ),
      // *分区新增的
      partitionfield: storageItem.partitionField || [],
    };

    // 如果之前新建任务失败，但是表已新建，这时需要查询是否有表，有表就跳过直接新建任务
    (
      dispatch({
        type: 'apiCollect/getCurrentTables',
        payload: {
          engineid: database.engineid,
          sourcename: database.sourcename,
          dbname: database.dbname,
        },
      }) as Promise<Array<string>>
    ).then((tables) => {
      const isCreatedTable = !tables.find((v) => v === table.tbname);
      (
        dispatch({
          type: 'apiCollect/creatStoreTable',
          payload: isCreatedTable ? addTableParam : {},
        }) as Promise<string>
      )
        .then((res) => {
          // res === 'new' 说明新建表失败, 否则返回新建表id
          if (res === 'new') {
            saveRef.current.rejectFun();
            setWarpLoading(false);
            console.log(6);
            return;
          }

          saveRef.current.resolveFun();
          newOrUpdateTask(
            params,
            extractItem,
            storageItem,
            res, // tabldid
            sourceValue,
            advancedconfig,
            values,
          );
        })
        .catch((err) => {
          console.log('err', err);
          saveRef.current.rejectFun();
          setWarpLoading(false);
        });
    });
  }

  const getSchedule = (fieldValues) => {
    const payload = formatToReq(fieldValues);
    return {
      scheduletype: payload.scheduletype,
      scheduledetail: payload.scheduledetail,
      exectimes: payload.exectimes,
      effectstart: payload.effectstart,
      firststarttime: payload.effectstart,
      effectend: payload.effectend,
    };
  };

  const getTimeout = (fieldValues) => {
    const payload = FormBundle.timeoutStrategy.formatToReq(fieldValues);
    return {
      timeoutstop: payload.timeoutstop,
      timeoutduration: payload.timeoutduration,
    };
  };

  // 调用新建或更新接口
  function newOrUpdateTask(
    params,
    extractItem,
    storageItem,
    tbid,
    sourceValue,
    advancedconfig,
    values,
  ) {
    // 配置extractdb, sourcedb

    const extractdbValue = values.extractdb || {};
    const extractdb = {
      id: extractItem.database?.dbid,
      // schemaid: extractItem.database?.schemaid,
      // schema: extractItem.database?.schema,
      // table: extractItem.table?.tbname,
      field: extractItem.dataSource.map((v) => v.fieldName),
      fieldtype: extractItem.dataSource.map((v) => v.fieldType),
      fieldcomment: extractItem.dataSource.map((v) => v.comment),
      // wherecontent:,
      // createfield
      mainkey: primKeys,
      dbname: extractdbValue.dbname, // 数据源
      apiname: apiInfo.apiname,
      apiid: apiInfo.apiid,
      httpmethod: apiInfo.method,
      encoding: extractdbValue.encoding || apiInfo.encodingformat,
      authmethod: extractdbValue.authmethod,
      authbasic: extractdbValue.authbasic,
      authapikey: extractdbValue.authapikey,
      authbearertoken: extractdbValue.authbearertoken,
      authjwt: extractdbValue.authjwt,
      authoauth: extractdbValue.authoauth,
      incrementmethod: extractdbValue.incrementmethod,
      ispage: extractdbValue.ispage,
      incrementfield: extractdbValue.incrementfield,
      timeformat: extractdbValue.timeformat,

      pageendrule: extractdbValue.pageendrule,
      pageendpath: formatStringToArrayWithoutRoot(
        getPathNameByPathId(apiInfo.endOptionData, extractdbValue.pageendpath),
      ),
      pageendpathid: formatStringToArrayWithoutRoot(extractdbValue.pageendpath),

      parsepath: formatStringToArrayWithoutRoot(
        getPathNameByPathId(apiInfo.endOptionData, extractdbValue.parsepath),
      ),
      parsepathid: formatStringToArrayWithoutRoot(extractdbValue.parsepath),

      headerparam: formatJson(values.headerparam),
      requestparam: formatJson(
        values.requestparam,
        apiInfo.endOptionData,
        values,
      ),
      fieldinfo: extractItem.dataSource?.map((item) => ({
        fieldname: item.fieldName,
        fieldtype: item.fieldType,
        fieldid: item.paramid,
        fieldcomment: item.comment,
        ispartition: item.isPartition,
        isprim: item.isPrim,
        isunique: item.isUnique,
        partitionnumber: item.partitionNumber,
      })),
      pyparams: scriptParam.map((v) => v.name),
      pycontent: content,
      pyrunram: ramValue,
      pycode: tabSize,
      pyversion: 'python3',
      // pypath 编辑的时候要传
      pylock: isLock,
      pypath,
    };

    // console.log('params', params);
    const isCreat = storageItem.radio === 'creat';

    const storeModeParam = getStoreModeParam(values.sourcedb.orientation);

    const sourcedb = {
      engineid: storageItem.database.engineid,
      layerid: storageItem.database.layerid,
      layername: storageItem.database.layername,
      sourcename: storageItem.database.sourcename,
      sourceid: storageItem.database.sourceid,
      sourcetype: storageItem.database.sourcetype,

      dbname: storageItem.database.dbname,
      dbtype: storageItem.database.sourcetype,
      odsdatabase: storageItem.database.dbname,
      schema: isHive(storageItem.database.sourcetype)
        ? ''
        : storageItem.database.sourcename,
      table: storageItem.table.tbname,
      tableid: isCreat ? tbid : storageItem.table.tbid || storageItem.table.id,

      newcreate: isCreat,
      fileformat: params.filetype,
      strategy: sourceValue.strategy,

      conditioncover:
        !sourceValue.strategy && sourceValue.coverStrategy === 'partial'
          ? sourceValue.sql
          : '', // v592覆盖策略，为空表示全表覆盖，否则为条件覆盖

      history: sourceValue.history,

      field: storageItem.dataSource.map((v) => v.fieldName),
      fieldtype: storageItem.dataSource.map((v) => v.fieldType),
      fieldcomment: storageItem.dataSource.map((v) => v.comment),
      partitionfield: storageItem.partitionField || [],
      ...storeModeParam,
      fieldinfo: storageItem.dataSource.map((item) => ({
        fieldname: item.fieldName,
        fieldtype: item.fieldType,
        fieldcomment: item.comment,
        ispartition: item.isPartition || false,
        isprim: item.isPrim || false,
        isunique: item.isUnique || false,
        partitionnumber: item.partitionNumber || 0,
        ranknum: item.rankNum || 0,
        rankname: item.rankName || '',
      })),
      orientation: sourceValue.orientation,
    };

    const {
      config: { assignnode: assignNode, joblevel },
      retry,
      retryInterval,
      retryDisable,
    } = values;

    const tablerelation = getTableRelation(mappingRef.current?.graph);

    const schedulePayload = getSchedule(values);

    const globalsubmitinfo = {
      assignnode: assignNode === '自由分配' ? '' : assignNode,
      joblevel,
      // extractonlinestrategy: strategy, // api放sourcedb里
      ...schedulePayload,
      ...getTimeout(values),

      crossdep: values?.crossdep,
      crossforce: values?.crossforce,
      updep: values?.updep,
      depjobinfo: depJobRef?.current?.depJobInfo ?? [],
    };

    const payload = {
      ...params,
      extractdb,
      sourcedb,
      advancedconfig,
      tablerelation,
      globalsubmitinfo,
      retrynum: retry,
      retryinterval: retryInterval,
      isban: retryDisable,

      // v592新增全局参数(时间参数)
      globalvars: {
        crontype: '1', // 默认必传
        timevars: timeVars,
      },
    };

    const isUpdate = isEdit;
    if (isUpdate) {
      payload.id = task.id;
    }

    (
      dispatch({
        type: isUpdate ? 'apiCollect/updateTask' : 'apiCollect/creatTask',
        payload,
      }) as Promise<any>
    )
      .then((res) => {
        const { code, result } = res;
        if (code === 200) {
          const dirid = !isUpdate ? result.dirid : res.dirid;
          const id = result.id || '';
          const newTask = isUpdate
            ? { ...payload, notename: payload.name, dirid }
            : { ...payload, notename: payload.name, dirid, id };
          if (isUpdate) {
            fetchDetail(task.id);
          }
          // setOverviewSource(newTask);
          setTimeout(() => {
            changeEditingModel(newTask);
          }, 500);
          saveRef.current.resolveFun();
          changeEditStatus(tabKey, false);
          setWarpLoading(false);
        } else {
          saveRef.current.rejectFun();
          console.log(8);
          setWarpLoading(false);
        }
      })
      .catch((err) => {
        console.log(99, err);
        saveRef.current.rejectFun();
        setWarpLoading(false);
      });
  }

  // 新建成功后转成预览模式
  async function changeEditingModel(payload) {
    await setIsEditing(false);
    const { name } = payload;
    const mewName = `${name}`;
    const newTask = {
      ...payload,
      tdcload: payload.tdcload || '',
      content: {},
    };
    setTimeout(() => {
      changeTabName(tabKey, mewName, newTask);
      refreshTreeData();
    }, 1000);
  }

  // 预览模式下点击编辑按钮，开始编辑已有任务
  async function onChangeEditing() {
    setIsEditing(true);
    setWarpLoading(true);

    // Promise.all([
    //   fetchSourceData(),
    //   fetchConfigInfo(),
    // ])
    //   .then(() => {
    //     // setEditingLoading(false);
    //   })
    //   .catch(() => {
    //     // setIsEditing(false);
    //     notification.error({ message: '接口请求失败' });
    //   });

    await fetchSourceData();
    await fetchConfigInfo();
    await fetchApiInfo();

    setTimeout(() => {
      setEditingSource();
    }, 500);
  }
  // 点击编辑按钮后设置表单页面状态的值
  async function setEditingSource() {
    const {
      notename,
      dirid,
      dirname,
      tags,
      description,
      extractdb,
      sourcedb,
      advancedconfig,
    } = overviewSource;

    const delList = [];
    const addList = [];

    // curReturnData 是完整的返回数据，不是根据解析字段根路径筛选之后的
    const curReturnData = apiRefForEditShow?.current?.returnOptionData;
    // console.log('curReturnData', curReturnData);
    const preFieldIdList = (extractdb?.fieldInfo || []).map((v) => v.fieldid);

    // 处理被删除的字段
    // console.log('extractdb?.fieldInfo', extractdb?.fieldInfo);
    (extractdb?.fieldInfo || []).forEach((v) => {
      const curItem = getItemById(curReturnData, v.fieldid, 'paramid');
      // console.log(v, curItem);
      const hasDel = curItem?.isdel || !curItem;
      if (hasDel) {
        delList.push(v.fieldName);
      }
    });

    // 新建的字段也不要连线
    const acutalReturnData =
      extractdb.parsepathid?.length > 0
        ? getReturnDataByPath(curReturnData, extractdb.parsepathid)
        : curReturnData;
    acutalReturnData.forEach((v) => {
      if (!preFieldIdList.includes(v.paramid)) {
        addList.push(v.paramname);
      }
    });

    const edges = overviewSource?.tablerelation?.edges?.filter(
      (v) => !delList.includes(v.source) && !addList.includes(v.source),
    );

    const parseId =
      extractdb.parsepathid?.length > 0
        ? `根节点.${extractdb.parsepathid?.join('.')}`
        : '根节点';

    const returnItem = getItemById(curReturnData, parseId);
    // console.log('returnItem', returnItem);
    const leftDatasource = returnItem.children?.map((v) => {
      return {
        key: v.paramname,
        fieldName: v.paramname,
        fieldType: v.type,
        paramid: v.paramid,
        fieldcomment: v.describe,
        comment: v.describe,
      };
    });
    // console.log('leftDatasource', leftDatasource);
    const currentExtract: any = {
      key: tabKey,
      database: {
        dbtype: 'api',
        dbname: extractdb?.dbname,
        dbid: extractdb?.id,
        schema: extractdb?.schema,
        schemaid: extractdb?.schemaid,
      },
      table: { tbid: extractdb.tableid || '', tbname: extractdb.table },
      dataSource: leftDatasource || [],
      // dataSource: extractdb?.fieldInfo || [],
      edges: edges || [],
    };

    const currentStore = {
      radio: 'have',
      key: tabKey,
      table: {
        tbid: sourcedb.tableid,
        tbname: sourcedb.table,
      },
      database: {
        layerid: sourcedb.layerid,
        layername: sourcedb.layername,
        engineid: sourcedb.engineid,
        sourceid: sourcedb.sourceid,
        sourcename: isHive(sourcedb.dbtype)
          ? sourcedb.odsdatabase
          : sourcedb.schema || '',
        sourcetype: sourcedb.dbtype,
        dbname: sourcedb.odsdatabase,
      },
      //* 新建表后 要将系统预设字段展示
      dataSource: overviewSource?.tbDetail?.fieldInfo,
      // FIXME 删除分区信息
      partitionField: sourcedb?.partitionfield || [],
      // 分布键
      distributionBond: overviewSource?.tbDetail?.distributionBond,
    };
    await dataSourceRef.current?.setExtractParam(currentExtract);
    await storeSourceRef.current?.setStoreParam(currentStore);
    // dataSourceRef.current?.setExtractSource();
    storeSourceRef.current?.setStoreSource(currentStore);
    // console.log(13);
    await setActiveKey([
      'basic',
      'extract',
      'storage',
      'mapping',
      'config',
      'advanced',
    ]);
    setTimeout(() => {
      const endId =
        extractdb.ispage && extractdb.pageendrule === 1
          ? extractdb.pageendpathid?.length > 0
            ? `根节点.${extractdb.pageendpathid?.join('.')}`
            : '根节点'
          : undefined;

      const hasDelParseId = getItemById(
        overviewSource.extractdb.responseparam,
        parseId,
      )?.isdel;
      const hasDelEndId = getItemById(
        overviewSource.extractdb.responseparam,
        endId,
      )?.isdel;

      // 需要等待后面的setExtractParam和setStoreParam两个方法执行结束才能拿到连线的数据
      // 然后打开字段映射组件，开始渲染
      form.setFieldsValue({
        dirid: dirid || dirname,
        description,
        name: notename,
        tags: tags.map((v) => v.id),
        headerparam: formatJsonToEditForm(extractdb.headerparam),
        requestparam: formatJsonToEditForm(
          extractdb.requestparam,
          '',
          overviewSource,
        ),
        extractdb: {
          ...extractdb,
          timeformat: extractdb.timeformat || undefined,
          dbname: extractdb.id,
          tbname: extractdb.apiid,
          parsepath: hasDelParseId ? undefined : parseId,
          pageendpath: hasDelEndId ? undefined : endId,
        },
        sourcedb: {
          ...sourcedb,
          fileformat: sourcedb.filetype,
          haveSource: sourcedb.sourceid,
          haveTable: sourcedb.table,
        },
        timeout: advancedconfig.timeout,
        errlogcount: advancedconfig?.errorinfo?.errlogcount,
        isrecorderr: advancedconfig.isrecorderr,
      });
    }, 200);
  }

  // 预先加载基础信息和数据源模块的相关接口
  function getExtractAndTag() {
    if (!isEdit) {
      dispatch({
        type: 'apiCollect/listNewStoreInfo', // 接口：listlayerinfo 用于存储源的选择
        payload: { tagtype: '所有' },
      });
    }
    const hadOnline = overviewSource?.hadOnline;

    const promiseArr = [
      isEdit &&
        !hadOnline &&
        dispatch({
          type: 'apiCollect/listNewStoreInfo', // 接口：listlayerinfo 用于存储源的选择
          payload: { tagtype: '所有' },
        }),
      dispatch({
        type: 'apiCollect/listMetaTags',
        payload: {},
      }),
      !hadOnline &&
        dispatch({
          type: 'apiCollect/listExtractSource',
          payload: {},
        }),
    ].filter((item) => item);

    Promise.all(promiseArr)
      .then(() => {
        setWarpLoading(false);
      })
      .catch(() => setWarpLoading(false));
  }
  // 预先加载引擎信息
  function fetchEngineData() {
    dispatch({
      type: 'apiCollect/listEngineInfo',
    });
  }

  // 任务配置信息
  async function fetchConfigInfo() {
    await dispatch({
      type: 'apiCollect/getNode',
      payload: { listall: true, module: 'access' },
    });
  }

  // api详情
  async function fetchApiInfo() {
    await dispatch({
      type: 'apiCollect/listExtractTable',
      payload: {
        perpage: 5000,
        page: 1,
        apidatabaseid: overviewSource.extractdb.id,
      },
    }).then((data) => {
      if (data?.code === 200) {
        const apiListData = data.extractTable;

        const apiId = overviewSource?.extractdb?.apiid;
        const preApiInfo = apiListData.find((v) => v.apiid === apiId) || {
          headerparam: [],
          requestparam: [],
          responseparam: [],
        };

        const headerparam = formatTableChildrenData(preApiInfo.headerparam);
        const requestparam = formatTableChildrenData(preApiInfo.requestparam);
        const returnOptionData = formatTreeChildrenData(
          preApiInfo.responseparam,
          undefined,
          undefined,
          'hasChildren',
        );
        const endOptionData = formatTreeChildrenData(
          preApiInfo.responseparam,
          undefined,
          undefined,
          'isNumber',
        );
        const increOptionData = formatTreeChildrenData(
          preApiInfo.responseparam,
          undefined,
          undefined,
          'notObj',
        );

        setApiInfo({
          ...preApiInfo,
          headerparam,
          requestparam,
          endOptionData,
          returnOptionData,
          increOptionData,
        });

        apiRefForEditShow.current = {
          ...preApiInfo,
          headerparam,
          requestparam,
          endOptionData,
          returnOptionData,
          increOptionData,
        };
      }
      // console.log(data);
    });
  }

  async function fetchSourceData() {
    await fetchEngineData();
    await getExtractAndTag();
  }

  useEffect(() => {
    if (!isEdit) {
      setWarpLoading(true);
      fetchSourceData();
      fetchConfigInfo();
    } else {
      fetchDetail(overviewSource?.id);
    }
  }, []);

  function getRef(name: string) {
    switch (name) {
      case 'basic':
        return basicRef;
      case 'extract':
        return dataSourceRef;
      case 'storage':
        return storeSourceRef;
      case 'mapping':
        return mappingRef;
      case 'config':
        return depJobRef;
      case 'advanced':
        return advancedRef;
      default:
        return undefined;
    }
  }

  const onParamsConfirm = (params) => {
    setTimeVars(params);
    setTimeVarShow(false);
  };

  const onParamsCancel = () => {
    setTimeVarShow(false);
  };

  // console.log('activeKey', activeKey)
  const hadOnline = overviewSource?.hadOnline;

  const scheduleInit = useMemo(() => {
    if (isEdit && overviewSource?.globalsubmitinfo) {
      const {
        scheduletype,
        scheduledetail,
        exectimes,
        firststarttime,
        effectstart,
        effectend,
        timeoutstop,
        timeoutduration,
      } = overviewSource.globalsubmitinfo;
      const schedule = resToInitialValue({
        schedule: scheduletype,
        scheduleDetail: scheduledetail,
        execTimes: exectimes || -1,
        // startMode: isstartnow,
        startMode: firststarttime,
        effectstart,
        effectend,
      });
      const timeoutData = FormBundle.timeoutStrategy.resToInitialValue({
        timeoutstop,
        timeoutduration,
      });
      return { ...schedule, ...timeoutData };
    } else {
      return {};
    }
  }, [overviewSource]);

  // console.log(isEdit, isEditing);
  const isView = isEdit && !isEditing;

  // 获取标签信息
  return (
    <div className={styles.apiCollect}>
      <div className={styles.operate}>
        {canCurrentPageEdit ? (
          <Space className={styles.btnGroup}>
            <div>
              {isEdit && !isEditing ? (
                !task.submitted && (
                  <LoadingButton
                    icon="icon-edit"
                    text="编辑"
                    loading={warpLoading || taskLoading}
                    onClick={onChangeEditing}
                    disabled={!!isOnLine || task.submitted}
                  />
                )
              ) : (
                <LoadingButton
                  icon="icon-save"
                  text="保存"
                  loading={warpLoading || taskLoading}
                  onClick={onCheckAndSave}
                  disabled={!!isOnLine || task.submitted}
                />
              )}
              <Tooltip title="在API任务执行前会优先执行脚本内容。可通过预执行脚本实现复杂的接口鉴权操作或通过在脚本中定义动态参数，在任务参数设置中引用动态参数，从而实现接口参数的动态传入">
                <Button
                  onClick={() => setDrawerVisible(true)}
                  disabled={warpLoading || taskLoading}
                >
                  预执行脚本
                </Button>
              </Tooltip>
            </div>
            <Button
              disabled={warpLoading || taskLoading}
              onClick={() => setTimeVarShow(true)}
            >
              全局参数
            </Button>
          </Space>
        ) : (
          <Tooltip title="在API任务执行前会优先执行脚本内容。可通过预执行脚本实现复杂的接口鉴权操作或通过在脚本中定义动态参数，在任务参数设置中引用动态参数，从而实现接口参数的动态传入">
            <Button
              onClick={() => setDrawerVisible(true)}
              disabled={warpLoading || taskLoading}
            >
              预执行脚本
            </Button>
          </Tooltip>
        )}
      </div>

      {isEdit && !isEditing ? (
        <div className={styles.overviewFormWarp}>
          <Overview
            editSource={overviewSource}
            tabKey={tabKey}
            taskLoading={detailLoading}
            form={form}
          />
        </div>
      ) : (
        <div className={styles.formWarp} id="formWarp">
          <Spin spinning={warpLoading || taskLoading || dbLoading}>
            <Form
              {...FORM_LAYOUT}
              form={form}
              name={tabKey}
              initialValues={{
                sourcedb: {
                  filetype: 'orc',
                  strategy: 0,
                  distributionmode: 'HASH',
                  orientation: 'column', // 默认列存
                },
                retry: isEditing ? overviewSource.retrynum : 0,
                retryInterval: overviewSource.retryinterval || undefined,
                retryDisable: overviewSource.isban,
                config: {
                  assignnode:
                    overviewSource?.globalsubmitinfo?.assignnode || '自由分配',
                  joblevel: overviewSource?.globalsubmitinfo?.joblevel || '中',
                },
                // 执行策略+相关初始值
                ...scheduleInit,
              }}
              onValuesChange={debounce(() => {
                changeEditStatus(tabKey, true);
              }, 1000)}
            >
              <Collapse
                ghost
                activeKey={activeKey}
                expandIconPosition="right"
                onChange={onActiveKeyChange}
              >
                {panelList.map((v, k) => (
                  <Collapse.Panel
                    header={
                      <div
                        className={styles.panelHeader}
                        style={{
                          opacity: highlightKeys.includes(v.name) ? 1 : 0.5,
                        }}
                      >
                        <span className={styles.key}>{k + 1}</span>
                        <span className={styles.title}>{v.title}</span>
                      </div>
                    }
                    forceRender
                    key={v.name}
                  >
                    <CommonItemContext.Provider value={itemProps}>
                      {bundleList[v.name](
                        getRef(v.name),
                        overviewSource,
                        itemProps,
                        form,
                      )}
                    </CommonItemContext.Provider>
                  </Collapse.Panel>
                ))}
              </Collapse>
            </Form>
          </Spin>
        </div>
      )}
      <IDEDrawer
        visible={drawerVisible}
        onCancel={() => setDrawerVisible(false)}
        hadOnline={hadOnline}
        canEdit={canCurrentPageEdit && !isView}
      />

      <TimeParamsModal
        visible={timeVarShow}
        params={timeVars}
        canEdit={canCurrentPageEdit && !isView}
        onConfirm={onParamsConfirm}
        onCancel={onParamsCancel}
      />

      {renderAlert && renderAlert(onSave)}
    </div>
  );
};

export default APICollection;
export { CommonItemContext };
