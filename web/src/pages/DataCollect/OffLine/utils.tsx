import { Modal } from 'antd';
import { routerRedux } from 'dva/router';
import { IconFont } from '@/components';
import { Dispatch, AnyAction } from '@/models/connect.d';
import { DispatchCore } from './index.d';
import {
  HAVE_FIELD_IDENTIFY,
  SUPPORT_CAPITAL_DBTYPE,
  NOT_SUPPORT_RANDOMLY,
  DISTRIBUTION_OPTIONS,
} from './conf';

export const AddTipsConfirmModal = ({
  moduleName,
  dispatch,
  content = '',
}: {
  /** moduleName 模块名称 */
  moduleName: string;
  /** content 自定义提示内容 */
  content?: string;
  /** dispatch  */
  dispatch: Dispatch<AnyAction> | DispatchCore;
}) => {
  Modal.confirm({
    icon: (
      <IconFont
        type="icon-exclamation-circle"
        style={{ color: '#1980FF', marginRight: 16 }}
      />
    ),
    title: '新建提醒',
    content:
      content ||
      `分层管理中没有适用于${moduleName}的存储引擎，请先前往元数据开发-分层管理中添加存储引擎`,
    okText: '前往',
    cancelText: '取消',
    onOk: () => {
      dispatch(
        routerRedux.push({
          pathname: '/metadataManage/metaDevelop/layerManage',
          state: { isLayer: true },
        }),
      );
    },
    onCancel: () => {},
  });
};

export const OnlineTipsConfirmModal = ({
  content = '',
  dispatch,
}: {
  /** content 自定义提示内容 */
  content?: string;
  /** dispatch  */
  dispatch: Dispatch<AnyAction> | DispatchCore;
}) => {
  Modal.confirm({
    icon: (
      <IconFont
        type="icon-exclamation-circle"
        style={{ color: '#1980FF', marginRight: 16 }}
      />
    ),
    title: '上线提醒',
    content:
      content ||
      `推送接口已过期无法上线，如需上线请在元数据-数据源中更新该推送接口的有效期`,
    okText: '前往',
    cancelText: '取消',
    onOk: () => {
      dispatch(
        routerRedux.push({
          pathname: '/metadataManage/datasource',
        }),
      );
    },
    onCancel: () => {},
  });
};

//* 判断是否为hive
export const isHive = (type) => type === 'hive';

//* 判断是否为odps
export const isOdps = (type) => type === 'odps';

//* 获取标签值 [{ id: '', name: '', color: '', dirid: '' }]
export const getTagsArr = (dataSource, arr) => {
  dataSource.forEach((item) => {
    const { name, color, id, children, dirid } = item;
    if (color) {
      arr.push({
        name,
        color,
        id,
        dirid,
      });
    }
    if (children) {
      getTagsArr(children, arr);
    }
  });
  return arr;
};

// v592 ispreset = 'day | month | year | costomize' 均视为系统预置字段
export const getSystemFields = (partitionField) => {
  return (partitionField || [])?.filter(
    (item) => item.ispreset !== HAVE_FIELD_IDENTIFY,
  );
};

export const arrayIncludes = (arr1, arr2) => {
  const newArr1 = arr1.map((item) => item.field);
  const newArr2 = arr2.map((item) => item.field);
  const temp = new Set([...newArr1, ...newArr2]);
  return arr1.length === temp.size;
};
//* 判断是否为推送接口
export const isPush = (type) => type === '推送接口';

//* 判断是否为优炫
export const isUx = (type) => type === 'uxdb';

//* 对象数组去重
export const unionWith = (tabList) => {
  const obj = {};
  return tabList.reduce((current, next) => {
    // eslint-disable-next-line
    obj[next.key] ? '' : (obj[next.key] = true && current.push(next));
    return current;
  }, []);
};

//* 将后端数据转换为前端所需数据(数据源表)
export const getTableSourceForDb = (oldFields, partitionFields) => {
  return oldFields.map((v, k) => ({
    key: k,
    num: k,
    field: v.fieldName,
    fieldtype: v.fieldtype,
    fieldcomment: v.comment,
    //* 字段标识 v5.0
    name: v.fieldName,
    isPartition: v.ispartition,
    isPrim: v.index === 2 || v.bothprimandunique,
    isUnique: v.index === 3 || v.bothprimandunique,
    partitionNumber:
      // eslint-disable-next-line
      partitionFields?.findIndex((p) => p === v.fieldName) + 1 || 0,
  }));
};

//* 将后端数据转换为前端所需数据(存储源表)
export const getTableSourceForStore = (oldFields, partitionFields = []) => {
  return oldFields.map((v) => ({
    key: v.number,
    num: v.number,
    field: v.name,
    fieldtype: v.fieldtype,
    fieldcomment: v.annotation,
    //* 字段标识 v5.0
    name: v.name,
    isPartition: v.ispartition,
    isPrim: v.isprim,
    isUnique: v.isunique,
    // eslint-disable-next-line
    partitionNumber: partitionFields?.findIndex((p) => p === v.name) + 1 || 0,
    rankName: v.rankname,
    rankNum: v.ranknum,
  }));
};

export const transDataForFieldInfo = ({
  field,
  fieldtype,
  fieldcomment = [],
  fieldinfo,
}) => {
  return field.map((v, k) => {
    const targetField = ((fieldinfo || []) as any).find(
      (item) => item.fieldname === v,
    );
    return {
      key: k,
      num: k,
      field: v,
      fieldname: v,
      fieldtype: fieldtype[k],
      fieldcomment: fieldcomment[k],
      //* v5.0
      name: v,
      isPartition: targetField?.ispartition || false,
      isPrim: targetField?.isprim || false,
      isUnique: targetField?.isunique || false,
      partitionNumber: targetField?.partitionnumber || 0,
      rankName: targetField?.rankname || '',
      rankNum: targetField?.ranknum || 0,
    };
  });
};

//* 判断是否为支持大写的数据库
export const isSupportCapital = (dbType) => {
  return SUPPORT_CAPITAL_DBTYPE.includes(dbType);
};

export const formatType = (type) => {
  const index = type.indexOf('(');
  if (index > -1) {
    return type.slice(0, index);
  }
  return type;
};

//* 分布方式 选项
/**
 *
 * @param storeType 存储源类型
 * @param hasKeys 是否有主键，默认无主键
 * @returns 1. 某些存储源类型，不支持「随机」分布，需要区分
 * @returns 2. 有主键 不支持「随机」分布，需要增加tooltip提示
 */
export const getDistributionOptions = (storeType, hasKeys = false) => {
  if (NOT_SUPPORT_RANDOMLY.includes(storeType)) {
    return DISTRIBUTION_OPTIONS.filter((item) => item.value !== 'RANDOMLY');
  }

  return DISTRIBUTION_OPTIONS.map((item) => {
    if (item.value !== 'RANDOMLY') return item;
    return {
      ...item,
      disabled: hasKeys,
      ...(hasKeys && {
        toolTip: {
          title: '该表存在主键，不支持RANDOMLY分布',
        },
      }),
    };
  });
};
