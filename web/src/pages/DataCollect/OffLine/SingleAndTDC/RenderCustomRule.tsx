import { useEffect, useMemo } from 'react';
import { Form, Select, Button } from 'antd';
import AceEditor from 'react-ace';
import { ContentModal } from '@/components';

// import 'ace-builds/src-noconflict/mode-mysql';
// import 'ace-builds/src-noconflict/theme-monokai';
// import 'ace-builds/src-noconflict/ext-language_tools';

const { Item } = Form;
const { Option } = Select;

function getPresetDetail(data) {
  const title =
    data?.type === 'day'
      ? '按日分区'
      : data?.type === 'month'
      ? '按月分区'
      : data?.type === 'year'
      ? '按年分区'
      : '--';

  return title;
}

// * 分区、主键、唯一键、敏感等级（v5.0）
const RenderCustomRule = (props) => {
  const {
    isView = false,
    visible,
    form = null,
    record,
    leftTableData = [],
    setRuleVisible,
    formatSql,
    setFormatSql,
    onConfirm,
  } = props;

  const { isPartitionSystem = false, isPartitionCustom = false } = record;

  useEffect(() => {
    if (visible) {
      form?.setFieldsValue({
        sourceField: record?.customizeRule?.sourceField ?? undefined,
      });
      setFormatSql(record?.customizeRule?.formatSql ?? '');
    }
  }, [visible]);

  const sourceFields = useMemo(() => {
    return leftTableData?.map((item) => item.fieldName) ?? [];
  }, [leftTableData]);

  return (
    <ContentModal
      visible={visible}
      title="分区规则"
      okText="确定"
      cancelText="取消"
      modalSize={isPartitionSystem ? 'small' : 'middle'}
      destroyOnClose
      maskClosable={isPartitionSystem || isView}
      getContainer={false}
      onCancel={() => setRuleVisible(false)}
      className="custom-rule-modal"
      footer={
        isPartitionCustom && !isView
          ? [
              <Button onClick={() => setRuleVisible(false)}>取消</Button>,
              <Button onClick={onConfirm} type="primary">
                确定
              </Button>,
            ]
          : null
      }
    >
      {isPartitionSystem ? (
        <div style={{ marginBottom: 28 }}>
          系统预置：
          {getPresetDetail({ type: record?.pattern })}
        </div>
      ) : (
        <Form form={form}>
          <Item
            label="来源字段"
            name="sourceField"
            initialValue={record?.customizeRule?.sourceField ?? undefined}
            rules={[{ required: !isView, message: '请选择或搜索字段名' }]}
          >
            {isView ? (
              <span>{record?.customizeRule?.sourceField || '--'}</span>
            ) : (
              <Select
                showSearch
                allowClear
                style={{ maxWidth: 250 }}
                placeholder="请选择或搜索字段名"
              >
                {sourceFields.map((v) => (
                  <Option value={v} title={v}>
                    {v}
                  </Option>
                ))}
              </Select>
            )}
          </Item>
        </Form>
      )}
      {isPartitionCustom && (
        <AceEditor
          showGutter
          mode="mysql"
          width="100%"
          fontSize={14}
          height="180px"
          theme="monokai"
          readOnly={!!isView}
          highlightActiveLine
          showPrintMargin={false}
          style={{ marginTop: 12, marginBottom: isView ? 28 : 0 }}
          name="partitionCustomSql"
          onChange={(v) => setFormatSql(v)}
          value={formatSql}
          placeholder={`--请输入自定义SQL表达式，示例：
--date_format(to_date(timetest), 'yyyyMM')`}
          setOptions={{
            enableBasicAutocompletion: true,
            enableLiveAutocompletion: true,
            enableSnippets: true,
            showLineNumbers: true,
          }}
        />
      )}
    </ContentModal>
  );
};

export default RenderCustomRule;
