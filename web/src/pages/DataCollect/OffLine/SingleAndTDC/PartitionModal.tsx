import React, {
  useImperativeHandle,
  useState,
  useMemo,
  useEffect,
} from 'react';
import { Form, Button } from 'antd';
import { ContentModal, FormBundle } from '@/components';
import {
  SUPPORT_PARTITION_TYPE,
  HAVE_FIELD_IDENTIFY,
} from '@/pages/DataCollect/OffLine/conf';
import { isOdps } from '@/pages/DataCollect/OffLine/utils';
import { getPartitionField } from './index';
import styles from './partition.less';
import { getBinaryFieldOfSource } from './StoreSource';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
type PartitionField = {
  name: string;
  fieldtype: string;
  annotation: string;
  ispreset: string;
}[];
interface PartitionProps {
  /** 表格数据 实时更新 */
  dataSource: { name: string; type: string }[];
  /** 已有的分区设置 */
  partitionField: PartitionField;
  sourceType: 'hive' | 'odps';
  updateStoreData: (p: PartitionField) => void;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const PartitionModal = React.forwardRef((props: PartitionProps, ref) => {
  const [form] = Form.useForm();
  const {
    dataSource,
    partitionField = [],
    updateStoreData,
    sourceType,
  } = props;
  const [visible, setVisible] = useState(false);

  const showPartitionModal = () => {
    setVisible(true);
  };
  const hidePartitionModal = () => {
    form.resetFields(['hivePartition']);
    updateStoreData([]);
    setVisible(false);
  };
  const handleCancel = () => {
    const haveFields = (form?.getFieldValue('hivePartition') || []).filter(
      (item) => item.pattern !== 'system' && item.pattern !== 'custom',
    );
    const newData = partitionField.map((item) => {
      const target = haveFields.find((_item) => _item.field === item.name);
      if (item.ispreset === HAVE_FIELD_IDENTIFY && !target)
        return { ...item, name: '' };
      return item;
    });
    updateStoreData(newData as any);
    setVisible(false);
  };

  const onFinish = (values) => {
    const newData = getPartitionField(values.hivePartition, dataSource) || [];
    updateStoreData(newData as any);
    setVisible(false);
  };

  const binaryField = getBinaryFieldOfSource(sourceType);
  const existedFields = useMemo(() => {
    const partitionType = isOdps(sourceType)
      ? SUPPORT_PARTITION_TYPE.filter((item) => item !== 'date')
      : SUPPORT_PARTITION_TYPE;
    return dataSource.map((item) => ({
      text: item.name,
      support: partitionType.includes(item.type) && binaryField !== item.type,
    }));
  }, [dataSource]);

  // *已经设置过分区，再次编辑时复原表单
  useEffect(() => {
    if (!visible) return;
    if (partitionField.length !== 0) {
      const newFields = partitionField.map((item) => {
        if (item.ispreset === HAVE_FIELD_IDENTIFY) {
          return {
            pattern: 'field',
            field: item.name,
          };
        } else if (item.ispreset === 'customize') {
          return {
            pattern: 'custom',
            customField: item.name,
          };
        }
        return {
          pattern: 'system',
          type: item.ispreset,
          field: item.name,
        };
      });
      form.setFieldsValue({
        hivePartition: newFields,
      });
    }
  }, [visible]);

  useImperativeHandle(ref, () => {
    return {
      showPartitionModal,
      handleCancel: hidePartitionModal,
      partitionForm: form,
    };
  });

  return (
    <ContentModal
      title="分区设置"
      visible={visible}
      modalSize="middle"
      maskClosable={false}
      wrapClassName={styles.partitionModal}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          handleKeyBind
          htmlType="submit"
          onClick={() => {
            form.submit();
          }}
        >
          确定
        </Button>,
      ]}
    >
      <Form
        name="publish"
        form={form}
        scrollToFirstError
        onFinish={onFinish}
        {...formItemLayout}
      >
        <FormBundle<'hivePartitionSql'>
          existedFields={existedFields}
          scene="hivePartitionSql"
        />
      </Form>
    </ContentModal>
  );
});

export default PartitionModal;
