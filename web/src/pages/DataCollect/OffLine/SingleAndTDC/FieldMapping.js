/* eslint-disable class-methods-use-this */
import React from 'react';
import { connect } from 'dva';
import _ from 'lodash';
import { Radio, Select, Tooltip } from 'antd';
import { IconFont, Ellipsis, TextButton } from '@/components';
import { toChineseNumeral } from '@/components/VersionCompareSelect/utils';
import { HAVE_FIELD_IDENTIFY } from '../conf';
import RenderCustomRule from './RenderCustomRule';
import styles from './index.less';

const { Option } = Select;

const getItemLabel = (index) => {
  return `${toChineseNumeral(index)}级分区`;
};

@connect(({ processData }) => ({ processData }))
class ModelFieldMapping extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      ruleVisible: false, // 分区规则弹窗
      relevance: 'order',
      extractList: [],
      storeList: [],
      lineList: [],
      systemFields: [],

      curRecord: {},
      formatSql: '',

    };
  }

  // 页面加载开始前获取
  componentDidMount() {
    this.orderFieldSort();
    this.updateSystemFields();
    const { setMappingRef } = this.props;
    if (setMappingRef && typeof setMappingRef === 'function') {
      setMappingRef(this);
    }
  }

  componentDidUpdate() {
    const { setMappingRef } = this.props;
    if (setMappingRef) setMappingRef(this);
  }

  // *更新系统预置字段
  updateSystemFields = () => {
    const {
      isEdit,
      tabKey,
      editSource,
      processData: { storeData },
    } = this.props;
    const store = storeData.find((v) => v.key === tabKey);
    // ???可以用isEdit 来代替store?.partitionField
    const fields = !isEdit
      ? store?.partitionField
      : editSource?.sourcedb?.partitionfield;
    this.setState({
      systemFields: (fields || [])
        .map((v, i) => ({
          ...v,
          partitionnumber: i + 1,
        }))
        .filter((item) => item.ispreset !== HAVE_FIELD_IDENTIFY),
    });
  };

  // *编辑状态下判断存储源勾选状态是否发生改变
  // eslint-disable-next-line
  isChangeSelectedRows = (editSource, selectdRows) => {
    const fields = editSource?.sourcedb?.field || [];
    const selectRowKeys = selectdRows.map((item) => item.field);
    return !_.isEqual(fields, selectRowKeys);
  };

  getHaveStoreSource = (store, editSource) => {
    const isNotEdit = Object.keys(editSource).length === 0;
    if (isNotEdit) return store.selectedRows;
    const isChanged = this.isChangeSelectedRows(editSource, store.selectedRows);

    if (isChanged) return store.selectedRows;
    const fields = editSource?.sourcedb?.field || [];
    return fields
      .map((item) => {
        const target = store.selectedRows.find((_item) => _item.field === item);
        if (target) return target;
        return null;
      })
      .filter((item) => item);
  };

  // 顺序关联
  orderFieldSort = () => {
    const {
      isEdit,
      tabKey,
      editSource,
      processData: { mappingData, extractData, storeData },
    } = this.props;
    const mapping = mappingData.find((v) => v.key === tabKey);
    const store = storeData.find(
      (v) => v.key === tabKey && v.origin === 'mapping',
    );
    const partiFields = store?.partitionField ?? [];

    const extract = extractData.find((v) => v.key === tabKey);
    // 第一次进入字段映射页面的时候
    if (!mapping) {
      const { extractdb, sourcedb } = editSource;
      let extractList = [];
      let storeList = [];
      let lineList = [];
      if (isEdit) {
        extractList = extractdb.field.map((v, k) => ({
          key: k,
          field: v,
          fieldtype: extractdb.fieldtype[k],
          fieldcomment: extractdb.fieldcomment[k],
        }));
        storeList = sourcedb.field.map((v, k) => ({
          key: k,
          status: true,
          color: '#1980ff',
          field: sourcedb.field[k],
          fieldtype: sourcedb.fieldtype[k],
          fieldcomment: sourcedb.fieldcomment[k],
          // ispartition: '', // 是否是分区字段（已有字段）
          // partitionnumber: '',
        }));
        lineList = extractdb.field.map((v, k) => ({
          key: k,
          color: '#1980ff',
          border: '1px solid #1980ff',
        }));
      } else {
        const storeSource =
          store?.radio === 'have'
            ? store.selectedRows
            : store.values.list
                .filter((v) => v)
                .map((v, k) => ({
                  key: k,
                  field: v.name,
                  isprim: v.pKey,
                  fieldtype: v.type,
                  fieldcomment: v.describe,

                  ispartition: !!partiFields?.filter((t) => t.name === v.name)
                    ?.length, // 是否是分区字段（已有字段）
                  partitionnumber:
                    partiFields.findIndex((t) => t.name === v.name) + 1,
                }));
        extractList = extract.selectedRows.map((v, k) => ({
          ...v,
          key: k,
        }));
        storeList = storeSource.map((v, k) => ({
          ...v,
          key: k,
          status: true,
          color: '#1980ff',
        }));
        lineList = storeSource.map((v, k) => ({
          key: k,
          color: '#1980ff',
          border: '1px solid #1980ff',
        }));
      }
      this.setState({ extractList, storeList, lineList });
    } else if (mapping.origin === 'save') {
      // 编辑和新建状态下从第四页进入
      this.setState({ ...mapping });
    } else {
      /* 
        1.新建状态下从第二页进入
        2.编辑状态下从第二页进入
          2.1.只进入第二页编辑了，但是没进入第一页
          2.2.第一页和第二页都进入了
       */
      const storeSource =
        store?.radio === 'have'
          ? this.getHaveStoreSource(store, editSource)
          : store.values.list
              .filter((v) => v)
              .map((v, k) => ({
                key: k,
                field: v.name,
                isprim: v.pKey,
                fieldtype: v.type,
                fieldcomment: v.describe,

                ispartition: !!partiFields?.filter((t) => t.name === v.name)
                  ?.length, // 是否是分区字段（已有字段）
                partitionnumber:
                  partiFields.findIndex((t) => t.name === v.name) + 1,
              }));
      const storeList = storeSource.map((v, k) => ({
        ...v,
        key: k,
        status: true,
        color: '#1980ff',
      }));
      const extractList = extract
        ? extract.selectedRows.map((v, k) => ({
            ...v,
            key: k,
          }))
        : mapping.extractList;
      const lineList = storeSource.map((v, k) => ({
        key: k,
        color: '#1980ff',
        border: '1px solid #1980ff',
      }));
      this.setState({ extractList, storeList, lineList });
    }
  };

  // 同名关联
  sameFieldSort = () => {
    const { storeList, extractList } = this.state;
    const sameExtract = [];
    const sameStore = [];
    for (let i = 0; i < storeList.length; i++) {
      for (let j = 0; j < extractList.length; j++) {
        const isExistStore = sameStore.find(
          (item) => item.field === storeList[i].field,
        );
        const isExistExtract = sameExtract.find(
          (item) => item.field === extractList[j].field,
        );
        if (
          storeList[i].field.toUpperCase() ===
            extractList[j].field.toUpperCase() &&
          !isExistStore &&
          !isExistExtract
        ) {
          sameStore.push(storeList[i]);
          sameExtract.push(extractList[j]);
        }
      }
    }

    const diffExtract = _.differenceBy(extractList, sameExtract, 'field');
    const diffStore = _.differenceBy(storeList, sameStore, 'field');
    const tempStore = [
      ...sameStore,
      ...diffStore.map((v) => ({ ...v, status: false, color: '#999999' })),
    ].map((v, k) => ({ ...v, key: k }));
    const tempExtract = [...sameExtract, ...diffExtract].map((v, k) => ({
      ...v,
      key: k,
    }));

    const tempLine = [];
    tempStore.forEach((v, k) => {
      if (v.status) {
        tempLine.push({
          key: k,
          color: '#1980ff',
          border: '1px solid #1980ff',
        });
      } else {
        tempLine.push({
          key: k,
          color: '#999999',
          border: '1px dashed #999999',
        });
      }
    });

    this.setState({
      lineList: tempLine,
      storeList: tempStore,
      extractList: tempExtract,
    });
  };

  // 数组排序
  // eslint-disable-next-line
  onSort = (arr) =>
    arr
      .sort((a, b) => (a.field > b.field ? 1 : -1))
      .filter((v, k) => v.key === k);

  // 获取颜色
  // eslint-disable-next-line
  getColorBorder = (color, border) => {
    if (color) {
      return color === '#1980ff' ? '#999999' : '#1980ff';
    } else {
      return border === '1px solid #1980ff'
        ? '1px dashed #999999'
        : '1px solid #1980ff';
    }
  };

  // 锁定已经选好的做好映射的字段
  onLock = (item) => {
    const { lineList, storeList } = this.state;
    const tempLine = lineList.map((v, k) =>
      item.key === k
        ? {
            ...v,
            color: this.getColorBorder(v.color),
            border: this.getColorBorder('', v.border),
          }
        : v,
    );
    const tempStore = storeList.map((v, k) =>
      item.key === k
        ? { ...v, status: !v.status, color: this.getColorBorder(v.color) }
        : v,
    );
    this.setState({ lineList: tempLine, storeList: tempStore }, () =>
      this.props.editExitDetection(),
    );
  };

  // 选择表字段
  handleNameChange = (index, value) => {
    this.props.editExitDetection();
    const { extractList, lineList } = this.state;
    const extract = [...extractList];
    const line = [...lineList];
    const extractListItem = extractList[index];
    const { key, field } = JSON.parse(value);
    if (field === extractListItem.field) return;
    const lineItem = line[index];
    extract[index] = extract[key];
    extract[key] = extractListItem;
    line[index] = line[key];
    line[key] = lineItem;

    const tempExtract = extract.map((v, k) => ({ ...v, key: k }));
    this.setState({ extractList: tempExtract, lineList: line });
  };

  // 选择同名关联还是顺序关联，默认同名关联,对数据做出改变
  handleRelevanceChange = (e) => {
    this.props.editExitDetection();
    this.setState({ relevance: e.target.value });
    // 如果是顺序关联，需要将存储数据按照抽取数组字段名称保持一致
    if (e.target.value === 'name') {
      this.sameFieldSort();
    } else {
      this.orderFieldSort();
    }
  };

  calcNameWidth = (item) => {
    const iconW = 16;
    const ruleW = 48;
    return `calc(100% - ${
      item?.ispreset
        ? item.ispreset !== 'notpreset'
          ? iconW + ruleW
          : iconW
        : 0
    }px)`;
  };

  setRuleVisible = (v) => {
    this.setState({
      ruleVisible: v,
    });
  };

  renderFieldIcon = (item) => {
    return (
      <>
        <Tooltip
          title={
            typeof item?.partitionnumber === 'number'
              ? `${getItemLabel(item.partitionnumber)}`
              : ''
          }
        >
          <IconFont
            type="icon-partition"
            style={{ color: theme['blue-6'], cursor: 'pointer' }}
          />
        </Tooltip>
        {item?.ispreset && item?.ispreset !== 'notpreset' && (
          <TextButton
            style={{ color: theme['blue-6'], cursor: 'pointer' }}
            onClick={() => this.setRuleVisible(true)}
          >
            分区规则
          </TextButton>
        )}
      </>
    );
  };

  render() {
    const {
      relevance,
      lineList,
      storeList,
      extractList,
      systemFields,
      ruleVisible,
    } = this.state;
    return (
      <div className={styles.mapping}>
        <Radio.Group value={relevance} onChange={this.handleRelevanceChange}>
          <Radio.Button value="name">同名关联</Radio.Button>
          <Radio.Button value="order">顺序关联</Radio.Button>
        </Radio.Group>
        <div className={styles.mappingContainer}>
          <div className={styles.backgroundTitle}>
            <div className={styles.extractTitle}>
              <div style={{ padding: '0 16px' }}>
                <div className={styles.titles}>
                  <span>字段</span>
                  <span>字段类型</span>
                  <span>字段注释</span>
                </div>
              </div>
            </div>
            <div className={styles.storeTitle}>
              <div style={{ padding: '0 14px' }}>
                <div className={styles.titles}>
                  <span style={{ width: '17%' }} />
                  <span style={{ width: '29%' }}>字段</span>
                  <span>字段类型</span>
                  <span>字段注释</span>
                  <span>操作</span>
                </div>
              </div>
            </div>
          </div>
          <div className={styles.connect}>
            <div className={styles.extractTable}>
              {extractList.map((item, index) => {
                const key = index + 123;
                return (
                  <div key={key} className={styles.items}>
                    <span title={item?.field} className={styles.item}>
                      {item?.field}
                    </span>
                    <span title={item?.fieldtype} className={styles.item}>
                      {item?.fieldtype}
                    </span>
                    <span title={item?.fieldcomment} className={styles.item}>
                      {item?.fieldcomment || '--'}
                    </span>
                  </div>
                );
              })}
            </div>
            <div className={styles.line}>
              {lineList.map((item, index) => {
                const key = index + 123;
                return (
                  <div key={key} className={styles.items}>
                    <div
                      className={styles.leftCircle}
                      style={{ background: item.color }}
                    />
                    <div
                      className={styles.connectLine}
                      style={{ borderBottom: item.border }}
                    />
                    <div
                      className={styles.rightCircle}
                      style={{ background: item.color }}
                    />
                  </div>
                );
              })}
            </div>
            <div className={styles.storeTable}>
              {storeList.map((item, index) => {
                const key = index + 123;
                return (
                  <div key={key} className={styles.items}>
                    <span
                      className={styles.item}
                      onClick={this.onLock.bind(this, item)}
                    >
                      <IconFont
                        type={item.status ? 'icon-lock' : 'icon-unlock'}
                        style={{ color: item.color }}
                      />
                    </span>
                    <span
                      title={item.field}
                      className={styles.item}
                      style={{ width: '29%' }}
                    >
                      <Ellipsis style={{ maxWidth: '75%' }}>
                        {item.field}
                      </Ellipsis>
                      {item?.ispartition && (
                        <Tooltip
                          title={
                            typeof item?.partitionnumber === 'number'
                              ? `${getItemLabel(item.partitionnumber)}`
                              : ''
                          }
                        >
                          <IconFont
                            type="icon-partition"
                            style={{
                              color: theme['blue-6'],
                              cursor: 'pointer',
                            }}
                          />
                        </Tooltip>
                      )}
                    </span>
                    <span
                      title={item.fieldtype}
                      className={styles.item}
                      style={{ width: '20.5%' }}
                    >
                      {item.fieldtype}
                    </span>
                    <span
                      title={item.fieldcomment}
                      className={styles.item}
                      style={{ width: '21%' }}
                    >
                      {item.fieldcomment || '--'}
                    </span>
                    <span className={styles.item}>
                      <Select
                        size="small"
                        disabled={item.status}
                        style={{ width: '90%', margin: '0 auto' }}
                        placeholder="选择字段"
                        value={
                          item.status
                            ? undefined
                            : JSON.stringify(extractList[index])
                        }
                        defaultValue={
                          item.status
                            ? undefined
                            : JSON.stringify(extractList[index])
                        }
                        onChange={(value) =>
                          this.handleNameChange(index, value)
                        }
                      >
                        {extractList.map((d, k) => (
                          <Option
                            key={d?.key}
                            disabled={storeList[k]?.status}
                            value={JSON.stringify(d)}
                          >
                            {d.field}
                          </Option>
                        ))}
                      </Select>
                    </span>
                  </div>
                );
              })}
              {systemFields.map((item) => {
                return (
                  <div key={item.name} className={styles.items}>
                    <span
                      className={styles.item}
                      style={{ visibility: 'hidden' }}
                    >
                      <IconFont
                        type={item.status ? 'icon-lock' : 'icon-unlock'}
                        style={{ color: item.color }}
                      />
                    </span>
                    <span
                      title={item.name}
                      className={styles.item}
                      style={{ width: '29%' }}
                    >
                      <Ellipsis width={this.calcNameWidth(item)}>
                        {item.name}
                      </Ellipsis>
                      {this.renderFieldIcon(item)}
                    </span>
                    <span title={item.fieldtype} className={styles.item}>
                      {item.fieldtype}
                    </span>
                    <span title={item.annotation} className={styles.item}>
                      {item.annotation || '--'}
                    </span>
                    <span className={styles.item}>--</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* {ruleVisible && (
          <RenderCustomRule
            form={rulesForm}
            visible={ruleVisible}
            record={curRecord}
            formatSql={formatSql}
            setFormatSql={setFormatSql}
            leftTableData={leftTableData}
            rightTableData={rightTableData}
            setRuleVisible={this.setRuleVisible}
            // operateFieldRules={operateFieldRules}
            onConfirm={ruleSetConfirm}
          />
        )} */}
      </div>
    );
  }
}

export default ModelFieldMapping;
