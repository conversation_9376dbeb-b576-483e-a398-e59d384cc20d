import React from 'react';
import { connect } from 'dva';
import { Steps, Modal, Button, notification, Spin } from 'antd';
import AceEditor from 'react-ace';
// eslint-disable-next-line
import 'brace/mode/json';
// eslint-disable-next-line import/no-extraneous-dependencies
import 'brace/mode/python';
// eslint-disable-next-line
import 'brace/ext/language_tools';
// eslint-disable-next-line
import 'brace/theme/monokai';
import { IconFont } from '@/components';

import TimeParamsModal from '@/components/TimeParamsModal';

import { getTaglistArr } from './utils';
import ExtractSource from './ExtractSource';
import StoreSource from './StoreSource';
import CheckAndSave from './CheckAndSave';
import FieldMapping from './FieldMapping';

import {
  SUPPORT_PARTITION_TYPE,
  HAVE_FIELD_IDENTIFY,
  SUPPORT_FILE_FORMAT,
} from '../conf';
import { getSystemFields } from '../utils';
import styles from './index.less';

// eslint-disable-next-line
const { Step } = Steps;
const { confirm } = Modal;

const steps = [
  { title: '选择数据源', content: 'extract' },
  { title: '选择存储源', content: 'store' },
  { title: '表字段映射', content: 'mapping' },
  { title: '检查与保存', content: 'svae' },
];

// *处理分区字段
// eslint-disable-next-line @typescript-eslint/default-param-last
export const getPartitionField = (partition = [], dataSource) => {
  const newData = partition
    .map((item) => {
      if (item.pattern === 'system') {
        return {
          name: item.field,
          fieldtype: 'string',
          annotation: '',
          ispreset: item.type,
        };
      } else if (item.pattern === 'custom') {
        return {
          name: item.customField,
          fieldtype: 'string',
          annotation: '',
          ispreset: 'customize',
        };
      } else {
        const targetItem = dataSource.find(
          (_item) => item.field === _item.name,
        );
        if (!targetItem || !SUPPORT_PARTITION_TYPE.includes(targetItem?.type))
          return null;
        return {
          name: item.field,
          fieldtype: targetItem?.type,
          annotation: targetItem?.describe,
          ispreset: HAVE_FIELD_IDENTIFY,
        };
      }
    })
    .filter((item) => item);
  return newData;
};

@connect(
  ({
    single,
    user,
    singleAndTdc,
    processData,
    extraction,
    dataSync,
    loading,
  }) => ({
    single,
    user,
    singleAndTdc,
    dataSync,
    processData,
    extraction,
    loading,
    checkTbnameRepeatLoading: loading.effects['singleAndTdc/getCurrentTables'],
    updateSingleLoading: loading.effects['singleAndTdc/updateSingleTask'],
    distributionLoading: loading.effects['singleAndTdc/getDistributionKey'],
    transformLoading: loading.effects['single/singleToCustomTransform'],
    fieldTransLoading: loading.effects['singleAndTdc/listFieldTrans'],
    newStoreLoading: loading.effects['singleAndTdc/creatStoreTable'],
    newSingleLoading: loading.effects['singleAndTdc/creatSingTask'],
    folderLoading: loading.effects['singleAndTdc/getFolderList'],
    detailLoading: loading.effects['dataSync/getTaskDetail'],
  }),
)
class SingleAndTDC extends React.Component {
  constructor(props) {
    super(props);
    const { isEdit } = props;
    this.state = {
      isEditing: false,
      isCode: false,
      current: isEdit ? 3 : 0,
      alterVisible: false,
      saveDisabled: false,
      taskInfo: null,
      paramVisible: false,

      timeVars: [], // v592全局参数(时间参数)
    };
  }

  static getDerivedStateFromProps(nextProps) {
    const { isOnLine } = nextProps;
    if (isOnLine) {
      return {
        isEditing: false,
        current: 3,
        isCode: false,
      };
    }
  }

  componentDidMount() {
    const { dispatch, task, isEdit } = this.props;

    if (!isEdit) return;

    dispatch({
      type: 'dataSync/getTaskDetail',
      payload: { id: task.id },
    }).then((res) => {
      const { code, result } = res;
      if (code === 200) {
        //* 数据源名称修改后，同步页面数据
        const { allDbList } = this.props.dataSync;
        const datasource = allDbList.find((v) => v.id === result.extractdb.id);
        if (datasource && datasource.id) {
          result.extractdb.dbname = datasource.dbname;
        }
        this.setState({
          taskInfo: result,
          timeVars: result?.globalvars?.timevars || [],
        });
      }
    });
  }

  onStepsChange = (order) => {
    const { current } = this.state;
    const stepNum = Math.abs(order - current);
    if (stepNum > 1 || stepNum === 0) {
      notification.warning({ message: '只允许每次跳过一个步骤！' });
      return;
    }
    // 这里需要判断是上一步还是下一步
    switch (current) {
      case 0:
        this.getExtractSource(1);
        break;
      case 1:
        if (order > 1) {
          this.getStoreSourceNext(2);
        } else {
          this.getStoreSourcePrev(0);
        }
        break;
      case 2:
        if (order > 2) {
          this.getFieldMappingNext(3);
        } else {
          this.getFieldMappingPrev(1);
        }
        break;
      case 3: // 完成按钮
        if (order > 3) {
          this.getCheckAndSaveNext();
        } else {
          this.getCheckAndSavePrev(2);
        }
        break;
      default:
        break;
    }
  };

  onParamsConfirm = (params) => {
    // console.log(params, 'onConfirm...');
    this.setState({ timeVars: params, paramVisible: false });
  };

  onParamsCancel = () => {
    this.setState({ paramVisible: false });
  };

  // 抽取源页面点击下一步
  getExtractSource = async (current) => {
    const isSuccess = await this.extractRef.validateTimeFormat();
    if (!isSuccess) return;

    const { state, checkWhere } = this.extractRef;
    const {
      dispatch,
      tabKey,
      processData: { extractData },
    } = this.props;
    const { selectedRows, dataSource, currentAdded, currentWhere } = state;
    state.dataSource = dataSource.map((v, k) => ({ ...v, key: k }));
    const { checked, text } = currentWhere;
    const isSelectCondition = currentAdded !== 'error';
    // * 需要确保where条件语句检测通过 或者是 where条件语句为空
    // * checked  不论text有没有值  text没有值 可能是从来没输入检测过 也可能是输入检测后再删除了
    const isWhere = checked || (!text && !checked);

    const payload = state;
    let temp = [];
    if (
      dataSource.length &&
      selectedRows.length &&
      isWhere &&
      isSelectCondition
    ) {
      if (
        extractData.length === 0 ||
        extractData.some((v) => v.key !== tabKey)
      ) {
        extractData.push({ ...payload, key: tabKey });
        temp = extractData;
      } else {
        temp = extractData.map((v) => {
          return v.key === tabKey ? { ...payload, key: tabKey } : v;
        });
      }
      dispatch({
        type: 'processData/getExtractData',
        payload: temp,
      }).then(() => {
        this.setState({ current });
      });
    } else if (isWhere) {
      const message = '必须选择了字段才能进行下一步！';
      // const message = isWhere
      //   ? '必须选择了字段才能进行下一步！'
      //   : '如果输入where过滤条件，必须保证where语句经过合法检测才能下一步！,请点击按钮进行检测';
      notification.warning({ message });
    } else {
      // * 手动检测一下where条件
      checkWhere();
    }
  };

  // 存储源页面点击上一步
  getStoreSourcePrev = (current) => {
    const { state } = this.storeRef;
    const {
      tabKey,
      dispatch,
      processData: { storeData },
    } = this.props;
    const { form } = this.storeRef.props;
    const values = form.getFieldsValue();
    let temp = [];
    const dataItem = { ...state, values, key: tabKey, origin: 'extract' };
    if (storeData.length === 0 || storeData.some((v) => v.key !== tabKey)) {
      storeData.push(dataItem);
      temp = storeData;
    } else {
      temp = storeData.map((v) => (v.key === tabKey ? dataItem : v));
    }
    dispatch({
      type: 'processData/getStoreData',
      payload: temp,
    }).then(() => this.setState({ current }));
  };

  //* 已有表更新 存储源模块 的 selectedRows selectedRowKeys (去除系统预设字段)
  // eslint-disable-next-line
  updateSelectedRowsOrKeys = (state, partitionFields) => {
    const systemFieldNames = getSystemFields(partitionFields).map(
      (item) => item.name,
    );
    const selectedRows = state.selectedRows.filter(
      (item) => !systemFieldNames.includes(item.field),
    );
    return {
      ...state,
      selectedRows,
      selectedRowKeys: selectedRows.map((item) => item.key),
    };
  };
  // eslint-disable-next-line
  checkPartitionFieldIsError = ({ partitionField, newDataSource }) => {
    // *如果修改字段后，直接点下一步，需要获取新的partitionField
    const newPartitionField = partitionField.map((item) => {
      const target = newDataSource.find((_item) => _item.name === item.name);
      if (item.ispreset === HAVE_FIELD_IDENTIFY && !target)
        return { ...item, name: '' };
      return item;
    });
    return !newPartitionField.every((item) => item.name);
  };

  // 存储源页面点击下一步
  getStoreSourceNext = (current) => {
    const { state } = this.storeRef;
    const { tabKey } = this.props;
    const {
      radio,
      dataLength,
      storeLength,
      newDataSource,
      havePartitionField,
      currentSource,
    } = state;
    if (storeLength !== dataLength) {
      notification.warning({
        message:
          '抽取源和存储源字段数必须一致，请修改存储源字段个数或返回上一页修改抽取源字段个数',
      });
      return;
    }
    const isError = this.checkPartitionFieldIsError(state);

    if (isError && radio !== 'have') {
      notification.warning({
        message: '分区设置有误，请先重新设置',
      });
      return;
    }
    // 已有模型
    if (radio === 'have') {
      const newState = this.updateSelectedRowsOrKeys(
        state,
        havePartitionField?.partitionField || [],
      );
      const dataItem = {
        ...newState,
        values: {},
        key: tabKey,
        origin: 'mapping',
        partitionField: havePartitionField?.partitionField,
      };
      this.processStoreData(dataItem, current);
    } else {
      const { form } = this.storeRef.props;
      form.validateFieldsAndScroll(
        { scroll: { alignWithTop: true } },
        async (err, values) => {
          if (err) return;

          //* 由于用了虚拟表格所以在点击「下一步」的时候要手动校验全部字段
          const isFieldResult = await this.storeRef.validateAllFields(
            newDataSource,
            currentSource?.sourcetype,
          );
          if (isFieldResult === 'error') return;

          values.list = newDataSource;
          const dataItem = {
            ...state,
            values,
            key: tabKey,
            origin: 'mapping',
          };
          this.processStoreData(dataItem, current);
        },
      );
    }
  };

  processStoreData = (dataItem, current) => {
    const {
      tabKey,
      dispatch,
      processData: { storeData },
    } = this.props;
    let temp = [];
    const isSame =
      storeData.length === 0 || storeData.some((v) => v.key !== tabKey);
    if (isSame) {
      storeData.push(dataItem);
      temp = storeData;
    } else {
      temp = storeData.map((v) => (v.key === tabKey ? dataItem : v));
    }
    dispatch({
      type: 'processData/getStoreData',
      payload: temp,
    }).then(() => this.setState({ current }));
  };

  // 在字段映射页面点击上一步
  getFieldMappingPrev = (current) => {
    const {
      tabKey,
      dispatch,
      processData: { mappingData },
    } = this.props;
    const { state } = this.fieldMappingRef;
    let temp = [];
    if (mappingData.length === 0 || mappingData.some((v) => v.key !== tabKey)) {
      mappingData.push({ ...state, key: tabKey, origin: 'source' });
      temp = mappingData;
    } else {
      temp = mappingData.map((v) => {
        return v.key === tabKey
          ? { ...state, key: tabKey, origin: 'source' }
          : v;
      });
    }
    dispatch({
      type: 'processData/getMappingData',
      payload: temp,
    }).then(() => this.setState({ current }));
  };

  // 在字段映射页面点击下一步
  getFieldMappingNext = (current) => {
    const {
      tabKey,
      dispatch,
      processData: { mappingData },
    } = this.props;
    const { state } = this.fieldMappingRef;
    const { lineList, storeList, extractList, relevance } = state;
    const isMappingArr = storeList.filter((v) => v.status);
    // 检测映射条件，如果是没有映射，不让通过
    if (!isMappingArr.length) {
      notification.error({ message: '请确保至少有一个字段映射！' });
      return;
    }
    const data = {
      lineList,
      storeList,
      extractList,
      relevance,
    };
    let temp = [];
    if (mappingData.length === 0 || mappingData.some((v) => v.key !== tabKey)) {
      mappingData.push({ ...data, key: tabKey, origin: 'save' });
      temp = mappingData;
    } else {
      temp = mappingData.map((v) => {
        return v.key === tabKey ? { ...data, key: tabKey, origin: 'save' } : v;
      });
    }
    dispatch({
      type: 'processData/getMappingData',
      payload: temp,
    }).then(() => this.setState({ current }));
  };

  // 保存页面点击上一步
  getCheckAndSavePrev = (current) => {
    const { state, props } = this.saveRef;
    const {
      dispatch,
      tabKey,
      processData: { saveData },
    } = this.props;
    const values = this.getDirName(props.form.getFieldsValue());
    let temp = [];
    if (saveData.length === 0 || saveData.some((v) => v.key !== tabKey)) {
      saveData.push({ ...state, params: values, key: tabKey });
      temp = saveData;
    } else {
      temp = saveData.map((v) =>
        v.key === tabKey ? { ...state, params: values, key: tabKey } : v,
      );
    }
    dispatch({
      type: 'processData/getSaveData',
      payload: temp,
    }).then(() => this.setState({ current, saveDisabled: false }));
  };
  // eslint-disable-next-line
  onSave = (tagertKey, { resolve, reject }) => {
    this.getCheckAndSaveNext(resolve, reject);
  };

  setSaveButton = (disabled) => {
    this.setState({ saveDisabled: disabled });
  };

  updateIncreField = (newField) => {
    // *  更新抽取源里的增量字段 后应该要更新源抽取数据里的增量字段
    // editSource.extractdb.createfield;
    const task = this.state.taskInfo;
    const { isEdit } = this.props;
    // const { isEditing } = this.state;
    // console.info('updateIncreField ---->', task, isEdit, isEditing);
    if (isEdit || !!task) {
      //* 编辑或者是有editSource的时候 才处理下述步骤
      const target = JSON.parse(JSON.stringify(task));
      target.extractdb.createfield = newField;
      this.setState({
        taskInfo: target,
      });
    }
  };

  updateEditSource = (tempSource) => {
    this.setState({
      taskInfo: tempSource,
    });
  };

  getDirName = (values) => {
    //* 兼容外部文件夹名称修改后，需要获取最新的文件夹名称 lina
    const { folderList } = this.props.dataSync;
    const isNewFolder = typeof values.dirid !== 'string';
    const targetFolder = folderList.find((item) => item.id === values.dirid);
    values.dirname = isNewFolder
      ? values.dirid?.title
      : targetFolder?.name || '';

    return values;
  };

  // 点击保存提交代码
  getCheckAndSaveNext = (resolve = () => {}, reject = () => {}) => {
    const {
      tabKey,
      isEdit,
      dispatch,
      user: { projectId },
      singleAndTdc: { tableTaglist },
      processData: { extractData, storeData, mappingData },
    } = this.props;
    const { props, createTask, updateTask } = this.saveRef;
    props.form.validateFields((err, values) => {
      if (err) {
        reject();
        this.toggleAlterVisible(false);
        return;
      }
      if (isEdit) {
        //* 兼容外部文件夹名称修改后，需要获取最新的文件夹名称 lina
        const newValues = this.getDirName(values);
        updateTask(newValues, (id, payload, dirid) => {
          this.changeEditingModel(id, payload, dirid, resolve);
        });
        return;
      }

      const store = storeData.find((v) => v.key === tabKey) || {};
      const extract = extractData.find((v) => v.key === tabKey) || {};
      const mapping = mappingData.find((v) => v.key === tabKey) || {};
      const {
        currentLayer: { layername, layerid, engineid },
        currentSource: { sourcename, sourceid, dbname, sourcetype },
        values: {
          catalogid = '',
          tbname,
          fileformat = 'Orcfile',
          describe = '',
          tags,
          distributionmode = 'HASH',
          distributionbond = [],
          orientation,
          compresstype,
        },
      } = store;
      const fieldinfo = [];
      mapping.storeList.forEach((v, k) => {
        if (v.status) {
          fieldinfo.push({
            number: k,
            name: v.field,
            fieldtype: v.fieldtype,
            annotation: v.fieldcomment,
            isprim: v.isprim || false,
          });
        }
      });
      const newStoreParam = {
        id: '',
        isrt: false,
        tbtype: '未分类',
        fieldinfo,
        catalogid,
        tbname,
        describe,
        fileformat: SUPPORT_FILE_FORMAT.includes(sourcetype) ? fileformat : '',
        layername,
        layerid,
        engineid,
        sourceid,
        dbname,
        distributionmode,
        distributionbond,
        orientation,
        ...(orientation === 'column' && { compresstype }),
        dbtype: sourcetype,
        sourcedb: sourcename,
        projectid: projectId,
        extracttype: extract.currentDatabase.dbtype,
        schema: sourcetype === 'hive' ? '' : sourcename,
        taglist: getTaglistArr(tableTaglist, tags || [], []),
        partitionfield: store?.partitionField || [],
      };


      dispatch({
        type: 'singleAndTdc/getCurrentTables',
        payload: { engineid, sourcename, dbname },
      }).then(() => {
        const { currentTables } = this.props.singleAndTdc;
        // 如果之前任务新建不成功，但是存储表已经新建了，这时不需要再新建表了
        const isCreatedTable =
          store.radio === 'new' &&
          !currentTables.find((v) => v.tablename === tbname);
        dispatch({
          type: 'singleAndTdc/creatStoreTable',
          payload: isCreatedTable
            ? { ...newStoreParam, singlecollection: true }
            : {},
        }).then((data) => {
          if (data === 'new') return;
          // 如果表单中的文件夹名称在外面被修改了，需要对比id，将最新的文件夹名称作为参数
          const newValues = this.getDirName(values);
          createTask({ ...newValues, tableid: data }, (id, payload, dirid) => {
            this.changeEditingModel(id, payload, dirid, resolve);
          });
        });
      });
    });
  };

  // 新建或者修改单例抽取成功后，获取最新的任务列表，筛选出刚才提交的那条任务，然后转编辑模式
  changeEditingModel = (id, payload, dirid, resolve) => {
    const { dispatch, tabKey, changeTabName, refreshTreeData } = this.props;
    const originEditSource = JSON.parse(JSON.stringify(this.state.taskInfo));
    dispatch({ type: 'dataSync/getSingleTaskContent', payload: { id } })
      .then(() => {
        const { taskContent } = this.props.dataSync;
        const { name } = payload;
        const mewName = `${name}`;
        const newTask = {
          ...originEditSource, //* 结构原来的数据 让编辑的时候 保存下来继续可用
          ...payload,
          tdcload: payload.tdcload || '',
          content: taskContent || {},
          notename: name,
          dirid,
          id,
        };
        // 如果是新建任务，需要把processData中的四个对象的key置为新的key
        this.setState({ isEditing: false, taskInfo: newTask }, () => {
          changeTabName(tabKey, mewName, newTask);
          refreshTreeData();
        });
      })
      .then(() => {
        // eslint-disable-next-line
        resolve && resolve();
        this.toggleAlterVisible(false);
      });
  };

  // 切换代码模式
  changeToCode = (status) => this.setState({ isCode: status });

  editExitDetection = () => {
    const { changeEditStatus, tabKey } = this.props;
    changeEditStatus(tabKey, true);
  };

  // 转为低代码采集
  changeCustom = () => {
    const {
      // task,
      dispatch,
      changeTabName,
      tabKey,
      refreshTreeData,
      transformLoading,
    } = this.props;
    const task = this.state.taskInfo;
    const transform = () => {
      dispatch({
        type: 'single/singleToCustomTransform',
        payload: { id: task?.id },
      }).then(() => {
        const { code } = this.props.single.transformCustom;
        if (code && code === 200) {
          const newTask = { ...task, tasktype: 0 };
          changeTabName(tabKey, `${task?.notename}`, newTask);
          refreshTreeData();
        }
      });
    };
    confirm({
      title: '转换提醒',
      content: '转化为低代码采集任务后无法返回，是否继续？',
      okText: '确定',
      confirmLoading: transformLoading,
      cancelText: '取消',
      onOk() {
        transform();
      },
      onCancel() {},
    });
  };

  // 点击开始编辑已有任务
  onChangeEditing = () => {
    // const { task } = this.props;
    const task = this.state.taskInfo;
    const {
      dirname,
      dirid,
      tags,
      notename,
      description,
      retrynum,
      advancedconfig = {},
    } = task;
    const {
      ram,
      kbps,
      splitkey,
      concurrency,
      errorinfo,
      isrecorderr,
      prioritymode,
    } = advancedconfig;
    const taglist = tags.map((v) => v.id);
    this.setState({ isEditing: true }, () => {
      const { form } = this.saveRef.props;
      form.resetFields();
      form.setFieldsValue({
        tasktype: 1,
        name: notename,
        dirid: dirid || dirname,
        tags: taglist,
        retrynum,
        description,
        ram,
        kbps,
        splitkey: splitkey || undefined,
        concurrency,
        errorinfo,
        isrecorderr,
        prioritymode,
      });
    });
  };

  toggleAlterVisible = (visible) => {
    this.setState({ alterVisible: visible });
  };

  onCanaleLeave = () => {
    const { deleteTab, tabList, tabKey } = this.props;
    const targetTab = tabList.find((item) => item.key === tabKey);
    if (!targetTab) return;
    if (targetTab.isEdit) {
      this.toggleAlterVisible(true);
    } else {
      deleteTab(tabKey);
    }
  };
  // eslint-disable-next-line
  isJsonTest = (str) => {
    if (typeof str === 'string') {
      try {
        const obj = JSON.parse(str);
        return obj;
      } catch (e) {
        return false;
      }
    }
  };

  setSaveRef = (ref) => {
    this.saveRef = ref;
  };

  setStoreRef = (ref) => {
    this.storeRef = ref;
  };

  setMappingRef = (ref) => {
    this.fieldMappingRef = ref;
  };

  setExtractRef = (ref) => {
    this.extractRef = ref;
  };

  changeState = (stateObj) => {
    this.setState(stateObj);
  };

  render() {
    const { isEditing, current, isCode, saveDisabled, paramVisible, timeVars } =
      this.state;
    const {
      isEdit,
      tabKey,
      // task,
      type,
      isOnLine,
      renderAlert,
      folderLoading,
      newStoreLoading,
      updateSingleLoading,
      newSingleLoading,
      fieldTransLoading,
      distributionLoading,
      checkTbnameRepeatLoading,
      user: { canCurrentPageEdit },
    } = this.props;
    const task = this.state.taskInfo;
    const isSubmit = !!isOnLine || task?.submitted;
    const extractType =
      isEdit && task?.tdcload && task?.tdcload === '落地抽取'
        ? 'tdc'
        : type === 1 || type === 'single'
        ? 'single'
        : 'tdc';

    const heightStyle = {
      height:
        isEdit && !isEditing ? 'calc(100vh - 158px)' : 'calc(100vh - 200px)',
    };
    const commonProps = {
      extractType,
      isEdit,
      tabKey,
      isEditing,
      heightStyle,
      editSource: task || {},
      timeVars,
      changeState: this.changeState,
      editExitDetection: this.editExitDetection,
      setSaveButton: this.setSaveButton,
      updateIncreField: this.updateIncreField,
      updateEditSource: this.updateEditSource,
    };
    if (isEdit && !task)
      return (
        <Spin spinning={this.props.detailLoading}>
          <div className={styles.SingleExtractWarp} />
        </Spin>
      );
    return (
      <div className={styles.SingleExtractWarp}>
        {isEdit && !isEditing ? (
          <div className={styles.editOperate}>
            <span>
              {!isCode && canCurrentPageEdit && (
                <IconFont
                  onClick={this.onChangeEditing}
                  disabled={isSubmit}
                  type="icon-edit"
                  style={{
                    marginRight: 16,
                    cursor: isSubmit ? 'not-allowed' : 'pointer',
                  }}
                  text="编辑"
                />
              )}
              {extractType === 'single' && canCurrentPageEdit && (
                <IconFont
                  onClick={this.changeCustom}
                  type="icon-code"
                  disabled={isSubmit}
                  style={{
                    marginRight: 16,
                    cursor: isSubmit ? 'not-allowed' : 'pointer',
                  }}
                  text="转化为低代码任务"
                />
              )}
            </span>
            <span style={{ display: 'flex', alignItems: 'center' }}>
              {isCode ? (
                <IconFont
                  onClick={() => this.changeToCode(false)}
                  type="icon-rollback"
                  text="返回"
                  style={{ marginLeft: 16 }}
                />
              ) : (
                <>
                  <Button
                    onClick={() => this.setState({ paramVisible: true })}
                    style={{ borderColor: '#1980ff' }}
                  >
                    全局参数
                  </Button>
                  <IconFont
                    onClick={() => this.changeToCode(true)}
                    type="icon-file-search"
                    text="查看代码"
                    style={{
                      marginLeft: 16,
                      display: extractType === 'single' ? 'block' : 'none',
                    }}
                  />
                </>
              )}
            </span>
          </div>
        ) : (
          <div className={styles.singleSteps}>
            <Steps
              className={styles.steps}
              size="small"
              current={current}
              onChange={this.onStepsChange}
            >
              {steps.map((item) => (
                <Step key={item.title} title={item.title} />
              ))}
            </Steps>
            <Button onClick={() => this.setState({ paramVisible: true })}>
              全局参数
            </Button>
          </div>
        )}
        {!isCode ? (
          <div className={styles.singleContent}>
            {current === 0 && (
              <ExtractSource
                {...commonProps}
                setExtractRef={this.setExtractRef}
              />
            )}
            {current === 1 ? (
              <StoreSource {...commonProps} setStoreRef={this.setStoreRef} />
            ) : null}
            {current === 2 && (
              <FieldMapping
                {...commonProps}
                setMappingRef={this.setMappingRef}
              />
            )}

            {current === 3 && (
              <CheckAndSave {...commonProps} setSaveRef={this.setSaveRef} />
            )}
          </div>
        ) : (
          <AceEditor
            mode={this.isJsonTest(task.content) ? 'json' : 'python'}
            theme="monokai"
            width="100%"
            height="calc(100vh - 160px)"
            value={
              this.isJsonTest(task.content)
                ? JSON.stringify(JSON.parse(task.content), null, 4)
                : task.content
            }
            readOnly
            showPrintMargin={false}
            name="UNIQUE_ID_OF_DIV"
            debounceChangePeriod={60}
            highlightActiveLine={false}
            editorProps={{ $blockScrolling: true }}
          />
        )}
        {isEdit && !isEditing ? null : (
          <div className={styles.singleAction}>
            <Button
              style={{ marginRight: 8 }}
              onClick={() => this.onCanaleLeave()}
            >
              取消
            </Button>
            {current > 0 && (
              <Button
                type="primary"
                style={{ marginRight: 8 }}
                disabled={
                  !!(newStoreLoading || newSingleLoading || updateSingleLoading)
                }
                onClick={() => {
                  this.onStepsChange(current - 1);
                  this.setSaveButton(false);
                }}
              >
                上一步
              </Button>
            )}
            {current <= steps.length - 1 && (
              <Button
                type="primary"
                loading={
                  !!newStoreLoading ||
                  !!updateSingleLoading ||
                  !!newSingleLoading ||
                  !!folderLoading ||
                  !!checkTbnameRepeatLoading ||
                  !!fieldTransLoading ||
                  !!distributionLoading
                }
                disabled={saveDisabled}
                onClick={() => this.onStepsChange(current + 1)}
              >
                {current === 3 ? '完成' : '下一步'}
              </Button>
            )}
          </div>
        )}

        {renderAlert && renderAlert()}
        {renderAlert &&
          renderAlert(undefined, {
            visible: this.state.alterVisible,
            onCancel: () => this.toggleAlterVisible(false),
          })}

        <TimeParamsModal
          visible={paramVisible}
          params={timeVars}
          canEdit={!(isEdit && !isEditing)}
          onConfirm={this.onParamsConfirm}
          onCancel={this.onParamsCancel}
        />
      </div>
    );
  }
}

export default SingleAndTDC;
