@import '../../../../utils/utils.less';

.SingleExtractWarp {
  padding: 0 16px;
  height: ~'calc(100vh - 100px)';

  .editOperate {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 10px 0;
    border-bottom: 1px solid #e1e1e1;

    span {
      color: #1980ff;
    }
  }

  .singleSteps {
    // width: 65%;
    margin: 16px 0;
    display: flex;

    :global {
      .ant-steps {
        width: 60%;
        margin: 0 auto;
      }
    }
  }

  .singleContent {
    width: 100%;
    height: ~'calc(100vh - 200px)';
  }

  .singleAction {
    margin: 16px;
    text-align: center;
  }
}

/* 选择数据源 */
.dataSourceWarp {
  height: ~'calc(100vh - 200px)' !important;

  .displayNone {
    display: none;
  }

  .displayInline {
    display: block;
  }

  .emptySearch {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;

    i {
      width: auto;
      height: auto;
      font-size: 80px;
    }

    span {
      color: #999;
    }
  }

  .dataTree {
    height: ~'calc(100vh - 200px)';

    .treewarp {
      height: ~'calc(100vh - 200px)';
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      border: 1px solid rgba(220, 220, 220, 1);

      :global {
        .ant-card-body {
          padding: 0;
        }
      }

      .tree {
        height: 270px;
      }

      .table {
        height: ~'calc(100% - 270px)';
      }
    }

    .serach {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 100%;
      height: 50px;
      background: rgba(248, 248, 248, 1);

      .inputSearch {
        width: 85%;
        height: 26px;

        :global {
          .ant-input-sm {
            border-radius: 12px;
          }
        }
      }
    }

    .allWidth {
      :global {
        .ant-tree-switcher,
        .ant-tree-switcher_open {
          color: #333 !important;
          display: inline-block;
        }

        .ant-tree-node-content-wrapper {
          width: 100%;

          .ant-tree-title {
            width: 85%;
          }
        }
      }
    }
  }

  .dataProcess {
    height: ~'calc(100vh - 200px)';

    .extractSourceSetting {
      .timeFormat::before {
        display: inline-block;
        color: #ff4d4f;
        font-size: 14px;
        line-height: 1;
        content: '*';
      }

      .increField::before {
        display: inline-block;
        color: #ff4d4f;
        font-size: 14px;
        line-height: 1;
        content: '*';
        visibility: hidden;
      }

      :global {
        .ant-input-search-button {
          background: #fff;
          color: @primary-color;
        }
        // // * 报错提示的时候 不要前面的红字提示
        // .ant-form-item-has-error .ant-input-group-addon {
        //   color: inherit !important;
        //   border-color: transparent !important;
        // }
      }
    }

    .extractTable {
      :global {
        .ant-empty,
        .ant-empty-normal {
          height: ~'calc(100vh - 384px)';
        }
      }
    }

    .storeTable {
      :global {
        .ant-empty,
        .ant-empty-normal {
          height: ~'calc(100vh - 375px)';
        }
      }
    }
  }

  .text_search__match {
    color: #1980ff;
  }
}

/* 选择存储源 */
.storeSource {
  height: 100%;

  .storeHeader {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    margin-bottom: 16px;
  }

  .storeContent {
    height: ~'calc(100vh - 250px)';
    display: flex;
    // justify-content: space-between;
    // flex-flow: row nowrap;

    .treeWarp {
      width: 240px;
      height: ~'calc(100vh - 250px)';
      overflow-y: auto;
      border: 1px solid #e1e1e1;
      border-radius: 4px;

      .stroeForm {
        margin: 12px 8px;

        :global {
          .ant-input-affix-wrapper-textarea-with-clear-btn {
            border: 1px solid #e1e1e1 !important;
          }
        }

        .primaryFlag {
          color: #999;
          position: absolute;
          right: 8px;
          top: 5px;
        }

        .unsupportedLabel {
          color: #999;
          position: absolute;
          right: 8px;
          top: 5px;
        }
      }
    }

    .tableWarp {
      width: ~'calc(100% - 240px)';
      box-sizing: border-box;
      padding-left: 14px;

      // 新建表
      .newTable {
        :global {
          .ant-table-body {
            min-height: ~'calc(100vh - 345px)';
          }

          .ant-table-tbody > tr > td {
            padding: 10px 8px 14px 8px;
          }
        }
      }

      // 已有表
      .haveData {
        :global {
          .ant-table-tbody > tr > td {
            white-space: nowrap;
          }

          .ant-table-body {
            min-height: ~'calc(100vh - 300px)';
          }
        }
      }

      .primary {
        margin-bottom: 16px;
      }

      .tableBtn {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-top: 1px solid #e1e1e1;
        border-bottom: 1px solid #e1e1e1;
      }

      // 表格为空的情况下高度适配
      :global {
        .ant-empty,
        .ant-empty-normal {
          height: ~'calc(100vh - 390px)';
        }

        .ant-table-row,
        .ant-table-row-level-0 {
          vertical-align: top;
        }

        .ant-legacy-form-explain {
          // word-break: break-word;
          // white-space: normal;
          position: absolute;
          top: 26px;
          flex-wrap: wrap;
          z-index: 1000;
        }

        .ant-table-tbody > tr.hideCheckBox td .ant-checkbox-wrapper {
          display: none;
        }

        .ant-table-tbody > tr.hideCheckBox td {
          background: #fff;
        }
      }
    }
  }
}

/* 模型字段映射 */
.mapping {
  height: 100%;

  // 字段映射
  .mappingContainer {
    position: relative;
    margin: 16px 0;

    .backgroundTitle {
      display: flex;
      justify-content: space-between;
      flex-flow: row nowrap;
      height: ~'calc(100vh - 245px)';
      width: 100%;

      .extractTitle {
        width: 35%;
        border-radius: 4px;
        box-sizing: border-box;
        border: 1px solid rgba(225, 225, 225, 1);
        height: 100%;

        .titles {
          width: 100%;
          text-align: center;

          span {
            display: inline-block;
            width: 33%;
            padding: 14px 0;
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            line-height: 22px;
          }
        }
      }

      .storeTitle {
        width: 45%;
        border-radius: 4px;
        box-sizing: border-box;
        border: 1px solid rgba(225, 225, 225, 1);
        height: 100%;

        .titles {
          width: 100%;
          text-align: center;

          span {
            display: inline-block;
            width: 22%;
            padding: 14px 0;
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            line-height: 22px;
          }

          span:nth-child(1) {
            width: 8%;
          }
        }
      }
    }

    .connect {
      position: absolute;
      top: 50px;
      left: 0;
      z-index: 990;
      height: ~'calc(100vh - 310px)';
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-flow: row nowrap;
      padding: 0 16px;
      overflow: auto;
      background-color: transparent;

      .extractTable {
        width: ~'calc(35% - 16px)';
        height: 100%;
        box-sizing: border-box;

        .items {
          display: flex;
          justify-content: space-between;
          width: 100%;
          background: rgba(249, 249, 249, 1);
          border-radius: 4px;
          border: 1px solid rgba(223, 223, 223, 1);
          margin-bottom: 14px;
          text-align: center;

          .item {
            width: 33.3%;
            display: inline-block;
            line-height: 40px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .storeTable {
        width: ~'calc(45% - 16px)';
        height: 100%;
        box-sizing: border-box;

        .items {
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          width: 100%;
          background: rgba(249, 249, 249, 1);
          border-radius: 4px;
          border: 1px solid rgba(223, 223, 223, 1);
          margin-bottom: 14px;
          text-align: center;

          .item {
            width: 22.5%;
            // display: inline-block;
            line-height: 40px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            :global {
              .ellipsis {
                width: auto;
              }
            }
          }

          .item:nth-child(1) {
            width: 8%;
            cursor: pointer;
          }

          .item:nth-last-child(1) {
            overflow: inherit;
          }
        }
      }

      .line {
        width: ~'calc(20% + 32px)';
        height: 100%;
        box-sizing: border-box;

        .items {
          position: relative;
          width: 100%;
          height: 42px;
          margin-bottom: 14px;
          overflow: hidden;

          .leftCircle {
            position: absolute;
            left: -4px;
            top: 17px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
          }

          .rightCircle {
            position: absolute;
            right: -4px;
            top: 17px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
          }

          .connectLine {
            position: absolute;
            top: 20.5px;
            width: 100%;
            height: 0;
          }
        }
      }
    }
  }
}

/* 检查与保存 */
.checkAndSave {
  height: ~'calc(100vh - 200px)';
  display: flex;
  justify-content: space-between;

  .leftWarp {
    width: 400px;

    .param {
      border-radius: 4px;
      overflow: auto;
      border: 1px solid #e1e1e1;
      padding-top: 14px;

      .description {
        display: inline-block;
        width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &::-webkit-scrollbar {
        width: 0;
      }

      .strategy {
        :global {
          .ant-form-item-control {
            min-width: 292px;
            max-width: 292px;
          }
        }
      }

      .dataStrategy {
        :global {
          .ant-legacy-form-item-label,
          .ant-legacy-form-item-control-wrapper {
            height: 28px;
          }
        }
      }

      .sqlContainer {
        :global {
          div.ant-legacy-form-item {
            margin-bottom: 0;
          }

          div.ant-legacy-form-item-label {
            width: 100px;
            height: 28px;
          }

          .ant-legacy-form-item-children {
            // height: 28px;
            display: flex;
            gap: 16px;
          }
        }
      }
    }

    .viewParam {
      padding: 16px;

      .viewItem {
        line-height: 28px;
        margin-bottom: 12px;
        display: flex;
        align-items: baseline;

        .itemLeft {
          display: inline-block;
          width: 25%;
          text-align: right;
        }

        .itemRight {
          display: inline-block;
          width: 75%;
          padding-left: 8px;
          word-wrap: break-word;
          word-break: break-all;
          overflow: hidden;
        }
      }
    }
  }

  .rightWarp {
    width: ~'calc(100% - 416px)';
    // width: ~"calc(100% - 336px)";

    .overview {
      border-radius: 4px;
      border: 1px solid #e1e1e1;
      padding: 14px;
      overflow: auto;

      .titles {
        display: flex;
        justify-content: space-between;
        padding: 0 14px;

        .titleItem {
          width: 40%;
          box-sizing: border-box;

          p {
            display: flex;
            align-items: flex-start;
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.65);
            margin-bottom: 0;
            line-height: 22px;

            span {
              display: inline-block;
              text-align: left;
              overflow-wrap: break-word;
            }

            span:nth-child(1) {
              width: 40%;
            }

            span:nth-child(2) {
              width: 60%;
              color: rgba(0, 0, 0, 0.65);
            }
          }
        }
      }

      .mappingView {
        position: relative;
        display: flex;
        justify-content: space-between;
        margin: 14px;
        margin-bottom: 0;

        :global {
          .ant-card-body {
            padding: 16px;
          }
        }

        .mappingTitle {
          color: #1990ff;
          font-size: 14px;
          font-weight: 500;
        }

        .tableHead {
          display: flex;
          justify-content: space-between;
          text-align: center;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 0;

          span {
            display: inline-block;
            width: 50%;
            line-height: 20px;
          }
        }

        .tableContent {
          .items {
            display: flex;
            box-sizing: border-box;
            height: 30px;
            justify-content: space-between;
            width: 100%;
            background: rgba(249, 249, 249, 1);
            border-radius: 4px;
            border: 1px solid rgba(223, 223, 223, 1);
            margin-top: 14px;
            text-align: center;

            .item {
              width: 40%;
              display: inline-block;
              line-height: 28px;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.65);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}
