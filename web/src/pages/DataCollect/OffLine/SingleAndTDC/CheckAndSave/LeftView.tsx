import { useState, useRef } from 'react';
import { Button, Form, Tooltip } from 'antd';
import {
  Ellipsis,
  IconFont,
  // RadioButton,
  TagsAdd,
  ContentModal,
} from '@dana/ui';
import _ from 'lodash';
import OnlineTipTable from '@/pages/DataCollect/OffLine/Home/Modal/OnlineTipTable';
import { SUPPORT_FILE_FORMAT, STORE_WAY_TYPE } from '../../conf';
// import { RetryView } from './RetryForm';
import ScheduleView from './ScheduleView';
// import ScheduleConfig from '@/pages/DataCollect/OffLine/SingleAndTDC/CheckAndSave/ScheduleConfig';
import styles from '../index.less';

export type EditSourceType = {
  notename: string;
  dirname: string;

  retrynum: number;
  retryinterval: number;
  isban: boolean;

  filetype: string;
  tags: Array<{ name: string; color: string }>;
  description: string;
  advancedconfig: {
    ram: number;
    kbps: number;
    splitkey: string;
    concurrency: number;
    isrecorderr: boolean;
    prioritymode: number;
  };
  sourcedb: {
    /** 存储方式，column-行存，row-列存 */
    orientation: string;
    /** 压缩格式 */
    compresstype: string;
  };
  // * 是否已设置调度策略  兼容旧数据
  isok?: boolean;

  [key: string]: any;
};

interface Props {
  editSource: EditSourceType;
  storeType: string;
  extracTtype: string;
  errorinfo: any;
  formItemLayout: any;
  isPgOrGp: boolean;
}

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const SCHEDULE_MAP = {
  0: '覆盖',
  1: '追加',
} as const;
const COVER_MAP = [
  { label: '全表覆盖', value: 'whole' },
  { label: '条件覆盖', value: 'partial' },
] as const;

const LeftView = (props: Props) => {
  const { editSource, storeType, extracTtype, errorinfo, isPgOrGp } = props;
  const { errlogcount, errlogper } = errorinfo;
  const [expand, setExpand] = useState(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const depRef = useRef();
  const isCSV = extracTtype === 'csv';
  // const retry = {
  //   retrynum: editSource.retrynum,
  //   retryinterval: editSource.retryinterval,
  //   isbane: editSource.isban,
  // };

  // const prioritymode = '容错率优先';
  const getScheduleModal = () => {
    setModalVisible(true);
  };

  const currIncreData = editSource?.accincrestatus?.currincredata;

  return (
    <>
      <Form name="leftView" {...formItemLayout}>
        <Form.Item label="任务类型">单表采集</Form.Item>
        <Form.Item label="任务名称">
          <Ellipsis tooltip showCenter={false} lines={1}>
            {editSource.notename}
          </Ellipsis>
        </Form.Item>
        <Form.Item label="任务目录">{editSource.dirname}</Form.Item>
        {/* <RetryView retry={retry} /> */}
        {STORE_WAY_TYPE.includes(storeType) && (
          <>
            <Form.Item label="存储方式">
              {{ row: '行存', column: '列存' }[editSource.sourcedb.orientation]}
            </Form.Item>
            {editSource.sourcedb.orientation === 'column' && (
              <Form.Item label="压缩格式">
                {editSource.sourcedb.compresstype}
              </Form.Item>
            )}
          </>
        )}
        {SUPPORT_FILE_FORMAT.includes(storeType) && (
          <Form.Item label="文件类型">{editSource.filetype}</Form.Item>
        )}
        <Form.Item label="标签">
          {editSource.tags.length ? (
            <span>
              <TagsAdd tags={editSource.tags || []} collapsible />
            </span>
          ) : (
            '--'
          )}
        </Form.Item>
        <Form.Item label="描述">
          <Ellipsis tooltip showCenter={false} lines={1}>
            {editSource.description || '--'}
          </Ellipsis>
        </Form.Item>

        {/* DS V5.8.0 单表新增调度策略配置  👇 */}
        {editSource?.extractdb?.createfield ? (
          <>
            <Form.Item label="数据策略">
              <div style={{ display: 'flex' }}>
                <div style={{ width: 28, minWidth: 28 }}>追加</div>
                {currIncreData && (
                  <Ellipsis
                    lines={1}
                    tooltip
                    style={{
                      marginLeft: 8,
                      maxWidth: 248,
                      color: '#999',
                    }}
                  >
                    {`上次增量采集到${editSource?.extractdb?.createfield}=${currIncreData}`}
                  </Ellipsis>
                )}
              </div>
            </Form.Item>
            <Form.Item label="源表历史数据">
              {!currIncreData ? (
                <span>同步</span>
              ) : (
                <Form.Item noStyle name="isSyncHistory" initialValue={false}>
                  {/* <RadioButton
                    disabled
                    options={[
                      { label: '同步', value: true },
                      { label: '不同步', value: false },
                    ]}
                   
                  /> */}
                  {editSource?.accincrestatus?.issynchistory === false
                    ? '不同步'
                    : '同步'}
                </Form.Item>
              )}

              <Tooltip
                title={<OnlineTipTable />}
                overlayStyle={{ maxWidth: '660px' }}
                // overlayClassName={styles.onlineTips}
              >
                <IconFont
                  type="icon-question-circle"
                  style={{
                    position: 'absolute',
                    marginLeft: 8,
                  }}
                />
              </Tooltip>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(pre, cur) =>
                pre.isSyncHistory !== cur.isSyncHistory
              }
            >
              {() => {
                //  {({ getFieldValue }) => {
                return (
                  <Form.Item
                    label="存储表已有数据"
                    name="isDeleteSoutb"
                    initialValue={false}
                  >
                    {editSource?.accincrestatus?.isdeletesoutb === false
                      ? '不清空'
                      : '清空'}
                  </Form.Item>
                );
              }}
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item
              label="数据策略"
              rules={[{ required: true, message: '请选择数据策略' }]}
            >
              {/* 后端补充 可根据isok 进行判断，isok为true，配置过调度策略和数据策略，正常展示，isok为false，
            未配置调度信息（包括上线再下线和从未上线），增量和全量的数据策略统一为追加 */}
              {!editSource.isok
                ? '追加'
                : SCHEDULE_MAP[
                    editSource?.globalsubmitinfo?.extractonlinestrategy
                  ]}
            </Form.Item>
            {editSource.isok &&
              editSource?.globalsubmitinfo?.extractonlinestrategy === 0 && (
                <>
                  <Form.Item label="覆盖策略">
                    {
                      COVER_MAP[
                        _.isEmpty(editSource?.globalsubmitinfo?.conditioncover)
                          ? 0
                          : 1
                      ].label
                    }
                  </Form.Item>
                  {editSource?.globalsubmitinfo?.conditioncover && (
                    <Form.Item label=" " colon={false}>
                      {editSource?.globalsubmitinfo?.conditioncover ?? '--'}
                    </Form.Item>
                  )}
                </>
              )}
          </>
        )}

        <Form.Item label="策略调度">
          {editSource.isok ? (
            <Button
              disabled={!editSource.isok}
              onClick={() => getScheduleModal()}
            >
              点击查看
            </Button>
          ) : (
            '未设置'
          )}
        </Form.Item>
        {/* DS V5.8.0 单表新增调度策略配置  👆 */}

        <>
          <div className={styles.viewItem}>
            <span className={styles.itemLeft} />
            <span className={styles.itemRight}>
              <a onClick={() => setExpand(!expand)}>
                高级设置 &nbsp;
                <IconFont type={expand ? 'icon-up' : 'icon-down'} />
              </a>
            </span>
          </div>
          {expand && (
            <>
              <Form.Item label="运行内存">
                {editSource.advancedconfig?.ram || '--'}G
              </Form.Item>
              <Form.Item label="速率上限">
                {editSource.advancedconfig?.kbps !== -1
                  ? `${editSource.advancedconfig?.kbps}MB/S`
                  : '不限流'}
              </Form.Item>
              {!isCSV && (
                <>
                  <Form.Item label="切分键">
                    {editSource.advancedconfig?.splitkey || '--'}
                  </Form.Item>
                  {editSource.advancedconfig?.splitkey && (
                    <Form.Item label="并发数">
                      {editSource.advancedconfig?.concurrency !== -1
                        ? editSource.advancedconfig?.concurrency
                        : '无限制'}
                    </Form.Item>
                  )}
                </>
              )}

              {isPgOrGp && (
                <Form.Item label="数据入库模式">
                  {editSource.advancedconfig?.prioritymode
                    ? editSource.advancedconfig?.prioritymode === 1
                      ? '容错率优先'
                      : '速率优先'
                    : '--'}
                </Form.Item>
              )}

              {/* 容错率优先 才展示下面的容错及错误数据 */}
              {(editSource.advancedconfig?.prioritymode === 1 || !isPgOrGp) && (
                <>
                  <Form.Item label="容错">
                    {errlogcount !== -1 ? (
                      <span
                        style={{
                          maxWidth: 210,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        <Tooltip
                          title={`错误记录超过${errlogcount}条任务自动结束`}
                        >
                          {`错误记录超过${errlogcount}条任务自动结束`}
                        </Tooltip>
                      </span>
                    ) : (
                      <span
                        style={{
                          maxWidth: 210,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        <Tooltip
                          title={`错误记录超过${errlogper}%任务自动结束`}
                        >
                          {`错误记录超过${errlogper}%任务自动结束`}
                        </Tooltip>
                      </span>
                    )}
                  </Form.Item>
                  <Form.Item label="错误数据">
                    {editSource.advancedconfig?.isrecorderr ? '记录' : '不记录'}
                  </Form.Item>
                </>
              )}
            </>
          )}
        </>
      </Form>

      {/* 调度策略弹窗 */}
      <ContentModal
        title="调度策略"
        visible={modalVisible}
        destroyOnClose
        onCancel={() => setModalVisible(false)}
        footer={null}
        modalSize="large"
      >
        <ScheduleView ref={depRef} editSource={editSource} />
      </ContentModal>
    </>
  );
};

export default LeftView;
