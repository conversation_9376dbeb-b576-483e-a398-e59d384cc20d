import React from 'react';
import { connect } from 'dva';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Spin, Input, notification, Tooltip, Button } from 'antd';
import { ContentModal, FormBundle } from '@dana/ui';
import moment from 'moment';
import {
  SearchTreeSelect,
  ColorTagTreeSelect,
  IconFont,
  Ellipsis,
  RadioButton,
} from '@/components';
import _, { cloneDeep } from 'lodash';
import { getStandardRules } from '@/utils';
import OnlineTipTable from '@/pages/DataCollect/OffLine/Home/Modal/OnlineTipTable';
import { listToTree, getTaglistArr } from '../utils';
import Overview from './Overview.js';
import AdvancedSettingBundle from './AdvancedSettingBundle';
import LeftView from './LeftView';
import { SUPPORT_FILE_FORMAT } from '../../conf';
import styles from '../index.less';
import ScheduleConfig from './ScheduleConfig';

const { TextArea } = Input;
const {
  formatToReq: formatToStrategyReq,
  // @ts-ignore
  // resToInitialValue: resToInitialStrategyValue,
} = FormBundle.exeStrategy;
const {
  formatToReq: formatToTimeOutReq,
  // @ts-ignore
  // resToInitialValue: resToInitialTimeOutStrategy,
} = FormBundle.timeoutStrategy;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};

export const startTimeDefault = moment().format('YYYY-MM-DD HH:mm:ss');
export const endTimeDefault = '3006-01-02 15:04:05';

const expandLayout = {
  wrapperCol: { offset: 6, span: 15 },
};

// 默认速率优先
const GP_SYSTEM = ['teryx', 'greenplum', 'gaussdba'];
// 默认容错率优先
const PG_SYSTEM = [
  'postgresql',
  'stork',
  'tdsql',
  'gaussdb',
  'postgres',
  'drealdb',
]; // 历史遗留问题  其实PG的 postgresql 和 postgres 是一样的

@connect(({ user, singleAndTdc, processData, single, dataSync, loading }) => ({
  user,
  singleAndTdc,
  processData,
  dataSync,
  single,
  loading,
  tagLoading: loading.effects['singleAndTdc/listTaskMetaTags'],
  taskListLoading: loading.effects['singleAndTdc/getAllTaskList'],
  folderLoading: loading.effects['singleAndTdc/getFolderList'],
  createLoading: loading.effects['singleAndTdc/creatSingTask'],
  updateLoading: loading.effects['singleAndTdc/updateSingleTask'],
  buildLoading: loading.effects['singleAndTdc/creatStoreTable'],
}))
class CheckAndSaveWarp extends React.Component {
  // retryRef = React.createRef();
  scheduleRef = React.createRef();

  constructor(props) {
    super(props);
    const {
      isEdit,
      tabKey,
      editSource,
      // timeVars,
      processData: { saveData },
      dataSync: { folderTreeData },
    } = props;
    const save = saveData.find((v) => v.key === tabKey) || {};
    const dirid = save.dirid ? save.dirid : isEdit ? editSource.dirid : '';
    const taskList = save.taskList || [];
    const folders = folderTreeData || save.folders;
    const expand = save.expand || false;
    this.state = {
      dirid,
      taskList,
      expand,
      folders,
      //* 并发数-由于Form用的旧版，所以只能将并发数的数据提置父组件，然后进行数据复原
      concurrency: save.concurrency || 0,
      modalVisible: false, //* 调度策略弹窗
      isClear: false, //*  是否清空存储表已有数据
      // isDeleteSoutbDisabled: false, //* 存储表已有数据是否禁用
      /** 调度策略的表单信息  因为只能用ref取 DepJobSettings 但是弹窗一关闭，dom就没了 所以用一个state来保存调度策略的全部信息 */
      scheduleConfigInfo: {
        formValues: undefined,
        depJobInfo: undefined,
      },
      haveClickedScheduleModal: false, //* 是否点开了调度弹窗
    };
  }

  componentDidMount() {
    this.getDataSource();
    this.setFormValue();
    const { setSaveRef } = this.props;
    if (setSaveRef && typeof setSaveRef === 'function') setSaveRef(this);
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    // 该方法内禁止访问this
    if (nextProps.dataSync.folderTreeData !== prevState.folders) {
      // 通过对比nextProps和prevState，返回一个用于更新状态的对象
      return {
        ...prevState,
        folders: nextProps.dataSync.folderTreeData,
      };
    }
    return null;
  }

  componentDidUpdate() {
    const { setSaveRef } = this.props;
    if (setSaveRef && typeof setSaveRef === 'function') setSaveRef(this);
  }

  setFormValue = () => {
    const {
      form,
      tabKey,
      processData: { saveData },
    } = this.props;
    const save = saveData.find((v) => v.key === tabKey);
    if (save) {
      form.setFieldsValue({
        tasktype: 1,
        name: save.params.name,
        dirid: save.params.dirid,
        tags: save.params.tags,
        retrynum: save.params.retrynum,
        description: save.params.description,
        ram: save.params.ram,
        kbps: save.params.kbps,
        splitkey: save.params.splitkey,
        concurrency: save.params.concurrency,
        errorinfo: save.params.errorinfo,
        isrecorderr: save.params.isrecorderr,
        prioritymode: save.params.prioritymode, // 不受编辑 存储源更改影响
      });
    }
  };

  // 请求文件夹列表和任务列表
  getDataSource = () => {
    const {
      dispatch,
      user: { projectId },
    } = this.props;
    const { dirid } = this.state;
    if (dirid) {
      this.getTaskList(dirid);
    }
    dispatch({
      type: 'singleAndTdc/listTaskMetaTags',
      payload: { projectid: projectId, tagtype: '任务' },
    });
  };

  // 获取当前选择的目录下的所有任务
  getTaskList = (dirid = '') => {
    const { dispatch, form, isEdit, editSource } = this.props;
    dispatch({
      type: 'singleAndTdc/getAllTaskList',
      payload: {
        dirid,
        sorttype: 1,
        tasktype: -1,
        sort: 1,
        listall: true,
      },
    }).then(() => {
      const { taskList } = this.props.singleAndTdc;
      // 如果是编辑状态下必须保证当前的名称在当前文件夹下不会被重名校验
      const newTaskNameArr = isEdit
        ? taskList.filter((v) =>
            editSource.dirid === dirid ? v !== editSource.notename : v,
          )
        : taskList;
      this.setState({ dirid, taskList: newTaskNameArr }, () => {
        form.validateFields(['name'], { force: true });
      });
    });
  };

  // 选择文件夹后获取该文件夹下的所有任务列表
  onChangeFolder = (val) => {
    //* 重名校验需要请求文件夹下的任务列表
    if (val && typeof val === 'string') {
      this.getTaskList(val);
    } else {
      this.setState({ taskList: [] }, () => {
        this.props.form.validateFields(['name'], { force: true });
      });
    }
  };

  getCheckAndSaveFormValues = (values, type) => {
    const params = {};
    const {
      tabKey,
      extractType,
      editSource,
      singleAndTdc: { taskTaglist },
      processData: { storeData },
    } = this.props;
    const store = storeData.find((v) => v.key === tabKey) || {};
    const retryPayload = this.getRetryPayload();
    const globalSubmitPayload = this.getGlobalSubmitInfoPayload();
    //  ???  再取一遍
    const currIncreDataEditing = editSource?.accincrestatus?.currincredata;
    const {
      name,
      tags,
      description,
      retrynum,
      ram,
      kbps,
      splitkey = '',
      concurrency = -1,
      isrecorderr,
      errorinfo,
      prioritymode,
      dataStrategy, // * 数据策略 0 覆盖 1 追加
      coverStrategy, // * 覆盖策略
      sql,
    } = values;
    // 配置并行参数
    params.advancedconfig = {
      ram,
      kbps,
      splitkey,
      concurrency,
      errorinfo,
      isrecorderr,
      prioritymode,
    };
    params.notetype = 'datax';
    params.name = name;
    params.dirname = values?.dirname;
    params.dirid = values.dirid?.title ? '' : values?.dirid;
    params.tasktype = 1;
    params.retrynum = retrynum;
    params.description = description || '';
    params.tdcload = extractType === 'tdc' ? '落地抽取' : '';
    params.tags = getTaglistArr(taskTaglist, tags || [], []);
    params.filetype = type === 'creat' ? store.fileformat : editSource.filetype;
    const targetParams = {
      ...params,

      // !   此处有以下场景
      // ! 1.纯新建 一定会设置调度设置 即便不打开弹窗  前端手动会给默认值传给后端
      // ! 2.编辑  不一定打开调度设置   若是打开设置了 用表单值 若是没打开 用详情返回的调度设置值

      ...retryPayload,
      globalsubmitinfo: {
        ...globalSubmitPayload,
        extractonlinestrategy:
          dataStrategy !== undefined && dataStrategy !== null
            ? dataStrategy
            : editSource?.globalsubmitinfo?.extractonlinestrategy, // * 数据策略 0 覆盖 1 追加
        //! 需要分情况考虑如果表单没有 dataStrategy 说明是固定的覆盖或者是追加 这个时候 直接 传 原来编辑数据的该值即可

        conditioncover:
          dataStrategy === 0 && coverStrategy === 'partial'
            ? sql
            : // : editSource?.globalsubmitinfo?.conditioncover
              '', // * 条件覆盖时sql语句，为空表示全表覆盖 | 追加
      },

      //! 增量策略状态  为了兼容旧数据 放在外面(后端说的)
      // ! 只有追加1才有 追加分为Radio选项和固定展示[追加]文案  accincrestatus 调整为下述代码逻辑
      ...(dataStrategy === 1 ||
        (!dataStrategy && {
          accincrestatus: {
            //* 采集到哪个节点
            currincredata: currIncreDataEditing || '',
            //* 若是采集了某个具体节点 后续继续采集 用用户选择的是否同步 不然的话 追加默认同步
            issynchistory: currIncreDataEditing ? values.isSyncHistory : true,
            // * 至于清空和不清空  可以由用户选择  此处默认不清空 === 接着上次的采集
            isdeletesoutb: values.isDeleteSoutb,
          },
        })),
    };

    return targetParams;
  };

  getRetryPayload = () => {
    const { scheduleConfigInfo, haveClickedScheduleModal } = this.state;
    const { editSource } = this.props;
    const retryFormValues = scheduleConfigInfo.formValues;

    if (retryFormValues && haveClickedScheduleModal) {
      // * 新建或者是编辑  点开了弹窗设置
      return {
        isretry: retryFormValues?.retry !== 0,
        retrynum: retryFormValues?.retry,
        retryinterval: retryFormValues?.retryInterval,
        isban: retryFormValues?.retryDisable,
      };
    } else if (!haveClickedScheduleModal && editSource.isok) {
      // * 编辑 已设置了调度信息  但没点开弹窗设置  只是改外面的基础信息
      return {
        isretry: editSource.isretry,
        retrynum: editSource.retrynum,
        retryinterval: editSource.retryinterval,
        isban: editSource.isban,
      };
    } else {
      //! 没有点开调度设置 且 未设置调度信息 !isok  的话 给的默认值
      return {
        isretry: false,
        retrynum: 0,
        retryinterval: 0,
        isban: false,
      };
    }
  };

  getGlobalSubmitInfoPayload = () => {
    const { scheduleConfigInfo, haveClickedScheduleModal } = this.state;
    const { editSource } = this.props;
    const scheduleFormValues = scheduleConfigInfo.formValues;

    if (scheduleFormValues && haveClickedScheduleModal) {
      // * 新建或者是编辑  点开了弹窗设置
      // const fieldsValue = scheduleForm?.getFieldsValue();
      const payload = formatToStrategyReq(scheduleFormValues);
      const params = formatToTimeOutReq(scheduleFormValues);
      return {
        // * 策略设置 👇
        scheduletype: payload?.scheduletype,
        scheduledetail: payload?.scheduledetail,
        exectimes: payload?.exectimes,
        //! 配合#24081 后端调整 恢复firststarttime 传参 值为生效开始时间
        firststarttime: payload?.effectstart,
        // * 新加的参数
        // * 生效开始时间
        effectstart: payload?.effectstart,
        // * 生效结束时间
        effectend: payload?.effectend,

        // 👇 超时设置
        timeoutstop: params?.timeoutstop,
        timeoutduration: params?.timeoutduration,

        // 👇 其他设置
        assignnode:
          scheduleFormValues.assignnode === '自由分配'
            ? ''
            : scheduleFormValues.assignnode,
        crossdep: scheduleFormValues.crossdep, //* 跨周期自依赖
        crossforce: scheduleFormValues.crossforce, //* 跨周期强制依赖
        updep: scheduleFormValues.updep, //* 上游依赖任务
        depjobinfo: scheduleConfigInfo?.depJobInfo, //* 依赖任务
        joblevel: scheduleFormValues.priority, //* 任务优先级 priority  高中低
        extractonlinestrategy: scheduleFormValues.dataStrategy, //* 数据策略的 覆盖 0 还是追加1
        conditioncover: scheduleFormValues?.sql, //* 条件覆盖的sql语句，为空表示 全表覆盖 | 追加
      };
    } else if (!haveClickedScheduleModal && editSource.isok) {
      // * 编辑 已设置了调度信息  但没点开弹窗设置  只是改外面的基础信息
      return {
        // * 策略设置 👇
        scheduletype: editSource.globalsubmitinfo.scheduletype,
        scheduledetail: editSource.globalsubmitinfo.scheduledetail,
        exectimes: editSource.globalsubmitinfo.exectimes,
        //! 配合#24081 后端调整 恢复firststarttime 传参 值为生效开始时间
        firststarttime: editSource.globalsubmitinfo.effectstart,
        // * 新加的参数
        // * 生效开始时间
        effectstart: editSource.globalsubmitinfo.effectstart,
        // * 生效结束时间
        effectend: editSource.globalsubmitinfo.effectend,

        // 👇 超时设置
        timeoutstop: editSource.globalsubmitinfo.timeoutstop,
        timeoutduration: editSource.globalsubmitinfo.timeoutduration,

        // 👇 其他设置
        assignnode: editSource.globalsubmitinfo.assignnode,
        crossdep: editSource.globalsubmitinfo.crossdep, //* 跨周期自依赖
        crossforce: editSource.globalsubmitinfo.crossforce, //* 跨周期强制依赖
        updep: editSource.globalsubmitinfo.updep, //* 上游依赖任务
        depjobinfo: editSource.depjoblist, //* 依赖任务
        joblevel: editSource.globalsubmitinfo.joblevel, //* 任务优先级 priority  高中低
        extractonlinestrategy:
          editSource.globalsubmitinfo.extractonlinestrategy, //* 数据策略的 覆盖 0 还是追加1
        conditioncover: editSource.globalsubmitinfo.conditioncover, //* 条件覆盖的sql语句，为空表示 全表覆盖 | 追加
      };
    } else {
      //! 没有点开调度设置 且 未设置调度信息 !isok  的话 给的默认值

      return {
        // * 策略设置 👇
        scheduletype: 1,
        scheduledetail: '00:00',
        exectimes: -1,
        //! 配合#24081 后端调整 恢复firststarttime 传参 值为生效开始时间
        firststarttime: startTimeDefault,
        // * 新加的参数
        // * 生效开始时间  默认为当前时间
        effectstart: startTimeDefault,
        // * 生效结束时间 默认为永久时间
        effectend: endTimeDefault,
        // 👇 超时设置
        timeoutstop: false,
        timeoutduration: [], //* 或者不传
        // 👇 其他设置
        assignnode: '',
        crossdep: true, //* 跨周期自依赖
        crossforce: false, //* 跨周期强制依赖
        updep: false, //* 上游依赖任务
        depjobinfo: [], //* 依赖任务
        joblevel: '中', //* 任务优先级 priority 012 高中低
      };
    }
  };

  // 新建任务
  // eslint-disable-next-line
  createTask = (values, callback) => {
    const {
      tabKey,
      dispatch,
      extractType,
      timeVars,
      processData: { extractData, storeData, mappingData },
    } = this.props;

    const store = storeData.find((v) => v.key === tabKey) || {};
    const extract = extractData.find((v) => v.key === tabKey) || {};
    const mapping = mappingData.find((v) => v.key === tabKey) || {};
    // 新建单例抽取的部分参数
    const params = this.getCheckAndSaveFormValues(values, 'creat');
    const { tableid } = values;

    const dbtype = store.currentSource.sourcetype;
    //* 不支持「文件格式」的filetype 传值为 空
    params.filetype = SUPPORT_FILE_FORMAT.includes(dbtype)
      ? store.fileformat
      : '';

    // 抽取源字段列表和字段类型、注释列表。从模型字段映射获取
    params.extractdb = {
      id: extract.currentDatabase.dbid,
      dbname: extract.currentDatabase.dbname,
      dbtype: extract.currentDatabase.dbtype,
      schema: extract.currentSchema.schema,
      schemaid: extract.currentSchema.schemaid,
      table: extract.currentTable.tbname,
      tableid: extract.currentTable.tbid,
      field: [],
      fieldtype: [],
      fieldcomment: [],
      wherecontent: extract.currentWhere.text,
      createfield: extract.currentAdded,
      supportdirtydata: extract.supportDirtyData,
      ...(extract.iconType && {
        iconType: extract.iconType,
      }),
      ...(extract.increFieldType && {
        timetype: extract.increFieldType,
      }),
    };
    // 存储源中的除了表字段相关的相关参数（表字段参下一步再修改）

    params.sourcedb = {
      engineid: store.currentLayer.engineid,
      layerid: store.currentLayer.layerid,
      layername: store.currentLayer.layername,
      odsdatabase: store.currentSource.dbname,
      dbtype,
      distributionmode:
        store.radio === 'new' ? store.values.distributionmode : '',
      dbname: store.currentSource.dbname,
      schema: dbtype === 'hive' ? '' : store.currentSource.sourcename,
      table: store.currentTable.tablename,
      tableid: store.radio === 'new' ? tableid : store.currentTable.tableid,
      newcreate: store.radio === 'new',
      field: [],
      fieldtype: [],
      fieldcomment: [],
      partitionfield: store?.partitionField,
      partitioninfofields: store?.havePartitionField?.partitionFieldArr,
      orientation: store?.orientation,
      ...(store?.orientation === 'column' && {
        compresstype: store?.compresstype,
      }),
    };
    // 如果抽取源是CSV 或者是本地文件的CSV，需要添加csvfilename和csvnum两个参数
    if (
      extract.currentDatabase.dbtype === 'csv' ||
      extract.currentDatabase.dbtype === 'localdb'
    ) {
      params.extractdb.wherecontent = '';
      params.extractdb.createfield = '';
      params.csvnum = [];
      params.csvfilename = extract.currentTable.tbname;
      mapping.extractList.forEach((v, k) => {
        if (mapping.storeList[k].status) params.csvnum.push(v.num);
      });
    }
    // 这里映射的字段只有status为true才能push
    // 先获取存储源的映射字段
    mapping.storeList.forEach((v) => {
      if (v.status) {
        params.sourcedb.field.push(v.field);
        params.sourcedb.fieldtype.push(v.fieldtype);
        params.sourcedb.fieldcomment.push(v.fieldcomment);
      }
    });
    // 抽取源映射字段
    mapping.extractList.forEach((v, k) => {
      if (mapping.storeList[k].status) {
        params.extractdb.field.push(v.field);
        params.extractdb.fieldtype.push(v.fieldtype);
        params.extractdb.fieldcomment.push(v.fieldcomment);
      }
    });

    // v592新增全局参数(时间参数)
    params.globalvars = {
      crontype: '1', // 默认必传
      timevars: timeVars,
    };

    // 检查条件覆盖策略是否符合条件
    const isPartialCoverInValid =
      params.sourcedb.dbtype === 'hive' &&
      !params.sourcedb.partitionfield?.length &&
      // params.isok && // 新建无isok属性
      params.globalsubmitinfo.extractonlinestrategy === 0 &&
      !_.isEmpty(params.globalsubmitinfo.conditioncover);

    this.props.form.setFields({
      sql: {
        value: params.globalsubmitinfo.conditioncover,
        errors: isPartialCoverInValid
          ? [new Error('Hive仅支持指定分区覆盖，当前无分区字段，请重新配置')]
          : '',
      },
    });
    if (isPartialCoverInValid) {
      return;
    }

    dispatch({
      type: 'singleAndTdc/creatSingTask', // newdatax
      payload: params,
    }).then(() => {
      const {
        creatSingle: { code, result },
      } = this.props.singleAndTdc;
      if (code === 200) {
        callback(result.id, params, result.dirid);
        const text = extractType === 'tdc' ? '落地采集' : '单表采集';
        notification.success({ message: `新建${text}任务成功!` });
        // * 重置弹窗是否被点击的状态
        this.setState({
          haveClickedScheduleModal: false,
        });
      } else if (code && code === 409) {
        this.props.form.setFields({
          name: {
            value: values.name,
            errors: [new Error('名称已存在，请重命名')],
          },
        });
      } else {
        notification.error({ message: '新建单表采集任务失败！' });
      }
    });
  };

  // 修改任务
  // eslint-disable-next-line
  updateTask = (values, callback) => {
    const {
      tabKey,
      dispatch,
      extractType,
      editSource,
      timeVars,
      changeState,
      processData: { extractData, storeData, mappingData },
    } = this.props;
    const store = storeData.find((v) => v.key === tabKey) || {};
    const extract = extractData.find((v) => v.key === tabKey) || {};
    //* 要从origin: save中拿到的数据
    const mapping =
      mappingData.find((v) => v.key === tabKey && v.origin === 'save') || {};
    // 配置并行参数和基础参数
    const params = this.getCheckAndSaveFormValues(values, 'edit');

    // 默认没有修改
    params.id = editSource.id;
    params.sourcedb = editSource.sourcedb;
    params.extractdb = editSource.extractdb;
    // * 是否配置调度策略也不会被改 因为表单验证不通过时不能保存 恒为true
    params.isok = true;
    // 一步步查看有没有修改,如果进入了模型字段映射页面
    if (Object.keys(mapping).length) {
      params.sourcedb.field = [];
      params.sourcedb.fieldtype = [];
      params.sourcedb.fieldcomment = [];
      params.extractdb.field = [];
      params.extractdb.fieldtype = [];
      params.extractdb.fieldcomment = [];
      mapping.storeList.forEach((v) => {
        if (v.status) {
          params.sourcedb.field.push(v.field);
          params.sourcedb.fieldtype.push(v.fieldtype);
          params.sourcedb.fieldcomment.push(v.fieldcomment);
        }
      });
      // 抽取源映射字段
      mapping.extractList.forEach((v, k) => {
        if (mapping.storeList[k].status) {
          params.extractdb.field.push(v.field);
          params.extractdb.fieldtype.push(v.fieldtype);
          params.extractdb.fieldcomment.push(v.fieldcomment);
        }
      });
    }
    // 如果进入到选择抽取源页面
    if (Object.keys(extract).length) {
      params.extractdb.id = extract.currentDatabase.dbid;
      params.extractdb.dbname = extract.currentDatabase.dbname;
      params.extractdb.dbtype = extract.currentDatabase.dbtype;
      params.extractdb.schema = extract.currentSchema.schema;
      params.extractdb.schemaid = extract.currentSchema.schemaid;
      params.extractdb.table = extract.currentTable.tbname;
      params.extractdb.tableid = extract.currentTable.tbid;
      params.extractdb.wherecontent = extract.currentWhere.text;
      params.extractdb.createfield = extract.currentAdded;
      params.extractdb.timetype = extract.increFieldType;
      params.extractdb.supportdirtydata = extract.supportDirtyData;
    }
    // 如果进入到选择存储源页面
    if (Object.keys(store).length) {
      const dbtype = store.currentSource.sourcetype;
      params.sourcedb.dbtype = dbtype;
      params.sourcedb.engineid = store.currentLayer.engineid;
      params.sourcedb.layerid = store.currentLayer.layerid;
      params.sourcedb.layername = store.currentLayer.layername;
      params.sourcedb.odsdatabase = store.currentSource.dbname;
      params.sourcedb.dbname = store.currentSource.dbname;
      params.sourcedb.schema =
        dbtype === 'hive' ? '' : store.currentSource.sourcename;
      params.sourcedb.table = store.currentTable.tablename;
      params.sourcedb.tableid = store.currentTable.tableid;
      params.sourcedb.newcreate = false;
      params.sourcedb.partitionfield =
        store?.havePartitionField?.partitionField;
      params.sourcedb.partitioninfofields =
        store?.havePartitionField?.partitionFieldArr;
    }

    // 判断是否是csv类型的修改
    // 分三种情况，1.未进入修改抽取源页面 2.进入修改抽取源页面但是没有做任何修改 3.进入且修改
    const isCsv = editSource.extractdb.table.includes('.csv');
    // 未进入抽取源页面
    if (isCsv) {
      params.extractdb.wherecontent = '';
      params.extractdb.createfield = '';
      if (!Object.keys(extract).length || !extract.currentDatabase.dbtype) {
        params.csvnum = editSource.csvnum;
        params.csvfilename = editSource.extractdb.table;
      } else {
        params.csvnum = [];
        params.csvfilename = extract.currentTable.tbname;
        mapping.extractList.forEach((v, k) => {
          if (mapping.storeList[k].status) params.csvnum.push(v.num);
        });
      }
    }

    // v592新增全局参数(时间参数)
    params.globalvars = {
      crontype: '1', // 默认必传
      timevars: timeVars,
    };

    const isPartialCoverValid =
      params.sourcedb.dbtype === 'hive' &&
      !params.sourcedb.partitionfield?.length &&
      params.isok &&
      params.globalsubmitinfo.extractonlinestrategy === 0 &&
      !_.isEmpty(params.globalsubmitinfo.conditioncover);

    this.props.form.setFields({
      sql: {
        value: params.globalsubmitinfo.conditioncover,
        errors: isPartialCoverValid
          ? [new Error('Hive仅支持指定分区覆盖，当前无分区字段，请重新配置')]
          : '',
      },
    });
    if (isPartialCoverValid) {
      return;
    }

    dispatch({
      type: 'singleAndTdc/updateSingleTask',
      payload: params,
    }).then(() => {
      const { updateSingle } = this.props.singleAndTdc;
      const { code, dirid } = updateSingle;
      if (code === 200) {
        const text = extractType === 'tdc' ? '落地采集' : '单表采集';
        notification.success({ message: `修改${text}任务成功！` });
        // * 重置弹窗是否被点击的状态
        this.setState({
          haveClickedScheduleModal: false,
        });
        callback(params.id, params, dirid);

        changeState({
          timeVars: params?.globalvars?.timevars ?? [],
        });
      } else if (code === 409) {
        this.props.form.setFields({
          name: {
            value: values.name,
            errors: [new Error('名称已存在，请重命名')],
          },
        });
      } else {
        notification.error({ message: '修改单表采集任务失败！' });
      }
    });
  };

  onChangeConcurrency = (value) => {
    this.setState({
      concurrency: value,
    });
  };

  getScheduleModal = () => {
    this.setState({
      modalVisible: true,
      haveClickedScheduleModal: true,
    });
  };

  handleDeleteSoutb = (e) => {
    const {
      target: { value },
    } = e;
    this.setState({
      isClear: value,
    });
  };

  render() {
    const { folders, expand, taskList, isClear } = this.state;
    const {
      isEdit,
      tabKey,
      isEditing,
      editSource,
      extractType,
      heightStyle,
      createLoading,
      updateLoading,
      buildLoading,
      tagLoading,
      folderLoading,
      taskListLoading,
      singleAndTdc: { taskTaglist },
      processData: { extractData, storeData, mappingData },
      form: { getFieldValue, setFieldsValue },
    } = this.props;

    // const {
    //   form: { getFieldValue },
    // } = this.props;
    const tagTree = listToTree(taskTaglist, 'tag')[0]?.children || [];
    const { getFieldDecorator } = this.props.form;
    const spinning =
      !!createLoading ||
      !!updateLoading ||
      !!buildLoading ||
      !!folderLoading ||
      !!taskListLoading ||
      !!tagLoading;
    const store = storeData.find((v) => v.key === tabKey) || {};
    const extract = extractData.find((v) => v.key === tabKey) || {};
    const filetype = isEdit ? editSource.filetype : store.fileformat;
    const storeType = isEdit
      ? editSource.sourcedb.dbtype
      : store.currentSource.sourcetype;

    const extracTtype = isEdit
      ? editSource.extractdb.dbtype
      : extract.currentDatabase.dbtype;

    const tagInitialValue = isEdit
      ? editSource.tags.map((v) => v.id)
      : undefined;

    // * 数据策略 是追加还是覆盖 0覆盖 1 追加  全量默认覆盖 增量默认追加
    //! 后端补充  可根据isok 进行判断，isok为true，配置过调度策略和数据策略，正常展示，
    //! isok为false，未配置调度信息（包括上线再下线和从未上线），增量和全量的数据策略统一为追加

    // console.info('isEdit', editSource);
    const dataStrategyInitialValue = isEdit
      ? editSource.isok
        ? editSource?.globalsubmitinfo.extractonlinestrategy
        : 1
      : 0;

    // 覆盖策略默认值
    const coverStrategyInitialValue = isEdit
      ? _.isEmpty(editSource?.globalsubmitinfo?.conditioncover)
        ? 'whole'
        : 'partial'
      : 'whole'; // 新建默认值为全表覆盖
    const sqlInitialValue = isEdit
      ? editSource?.globalsubmitinfo?.conditioncover ?? undefined
      : '';

    // 判断是否是PG或者是GP系存储源
    // const isPgOrGp = GP_SYSTEM.concat(PG_SYSTEM).indexOf(storeType) > -1;

    const isPG = PG_SYSTEM.indexOf(storeType) > -1;

    const isGP = GP_SYSTEM.indexOf(storeType) > -1;

    // 公用属性
    const commonProps = {
      isEdit,
      tabKey,
      mappingData,
      extractData,
      editSource,
    };
    const overviewProps = {
      heightStyle,
      storeData,
      ...commonProps,
    };
    const advancedProps = {
      form: this.props.form,
      formItemLayout,
      concurrency: this.state.concurrency,
      onChangeConcurrency: this.onChangeConcurrency,
      ...commonProps,
    };

    const errorinfo = isEdit ? editSource.advancedconfig?.errorinfo || {} : {};
    /**
       {
    "currincredata": "2022/12/03",
    "issynchistory": true,
    "isdeletesoutb": false
    }  
    展示为  数据策略 追加  上次增量采集到gender=2022/12/13
            源表历史数据  RadioButton 同步/不同步  点亮不同步  + 图标
            存储表已有数据  RadioButton 清空/不清空   点亮不清空  disabled清空



            {
    "currincredata": "",
    "issynchistory": true,
    "isdeletesoutb": false
}
      展示为   数据策略  追加(文本展示)
              源表历史数据  同步(文本展示)+图标
              存储表已有数据 RadioButton 清空/不清空  点亮不清空
     */
    const currIncreData = editSource?.accincrestatus?.currincredata;
    // const currIncreData = {
    //   currincredata: '2022/12/03',
    //   issynchistory: true,
    //   isdeletesoutb: false,
    // }.currincredata;

    const isSyncHistoryValue = getFieldValue('isSyncHistory');

    // * 如果有增量字段  是增量采集 单独处理
    const pageCreateField = isEdit
      ? editSource.extractdb.createfield
      : extract.currentAdded;

    // ! 源表历史数据 isSyncHistory

    // const issynchistory = isEdit
    //   ? editSource?.accincrestatus?.issynchistory
    //   : false;
    return (
      <Spin spinning={spinning}>
        <div className={styles.checkAndSave} id="saveWrap">
          <div className={styles.leftWarp}>
            {isEdit && !isEditing ? (
              <div
                className={`${styles.param} ${styles.viewParam}`}
                style={heightStyle}
              >
                <LeftView
                  editSource={editSource}
                  errorinfo={errorinfo}
                  extracTtype={extracTtype}
                  storeType={storeType}
                  formItemLayout={formItemLayout}
                  // 是否是这两个类型中的一个 其余按照原有逻辑处理 即 展示容错xx 错误xxx
                  isPgOrGp={isPG || isGP}
                />
              </div>
            ) : (
              <Form className={styles.param} style={heightStyle}>
                <Form.Item {...formItemLayout} label="任务类型">
                  {getFieldDecorator('tasktype', {
                    initialValue: '1',
                    rules: [{ required: true }],
                  })(
                    <span>
                      {extractType === 'tdc' ? '落地采集' : '单表采集'}
                    </span>,
                  )}
                </Form.Item>
                <Form.Item {...formItemLayout} label="任务名称">
                  {getFieldDecorator('name', {
                    rules: getStandardRules('请输入任务名称', ['', taskList]),
                  })(<Input placeholder="请输入任务名称" />)}
                </Form.Item>
                <Form.Item {...formItemLayout} label="任务目录">
                  {getFieldDecorator('dirid', {
                    rules: [{ required: true, message: '请选择任务目录' }],
                  })(
                    <SearchTreeSelect
                      treeData={folders}
                      allowNew
                      treeIcon
                      placeholder="请选择任务目录"
                      onChange={this.onChangeFolder}
                    />,
                  )}
                </Form.Item>
                {/* <RetryForm
                  ref={this.retryRef}
                  editSource={editSource}
                  formItemLayout={formItemLayout}
                /> */}
                {SUPPORT_FILE_FORMAT.includes(storeType) && (
                  <Form.Item {...formItemLayout} label="文件类型">
                    {getFieldDecorator('filetype', {
                      initialValue: filetype,
                    })(<span>{filetype}</span>)}
                  </Form.Item>
                )}
                <Form.Item {...formItemLayout} label="标签">
                  {getFieldDecorator('tags', {
                    initialValue: tagInitialValue,
                  })(
                    <ColorTagTreeSelect
                      placeholder="请选择标签"
                      tagList={tagTree}
                    />,
                  )}
                </Form.Item>
                <Form.Item {...formItemLayout} label="描述">
                  {getFieldDecorator('description')(
                    <TextArea rows={2} placeholder="请输入描述信息" />,
                  )}
                </Form.Item>
                {/* DS V5.8.0 单表新增调度策略配置  👇 */}
                {pageCreateField ? (
                  <>
                    <Form.Item
                      {...formItemLayout}
                      label="数据策略"
                      // className={styles.strategy}
                    >
                      <div style={{ display: 'flex' }}>
                        <div style={{ width: 28, minWidth: 28 }}>追加</div>
                        {currIncreData && (
                          <Ellipsis
                            lines={1}
                            tooltip
                            style={{
                              marginLeft: 8,
                              maxWidth: 248,
                              color: '#999',
                            }}
                          >
                            {`上次增量采集到${editSource.extractdb.createfield}=${currIncreData}`}
                          </Ellipsis>
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item {...formItemLayout} label="源表历史数据">
                      {!currIncreData ? (
                        <span>同步</span>
                      ) : (
                        <Form.Item noStyle>
                          {getFieldDecorator('isSyncHistory', {
                            initialValue: false, //* 默认值恒为false,
                          })(
                            <RadioButton
                              options={[
                                { label: '同步', value: true },
                                {
                                  label: '不同步',
                                  value: false,
                                  disabled: isClear,
                                },
                              ]}
                            />,
                          )}
                        </Form.Item>
                      )}

                      <Tooltip
                        title={<OnlineTipTable />}
                        overlayStyle={{ maxWidth: '660px' }}
                        getPopupContainer={() =>
                          document.getElementById('saveWrap')
                        }
                      >
                        <IconFont
                          type="icon-question-circle"
                          style={{
                            position: 'absolute',
                            marginLeft: 8,
                            top: !currIncreData ? -4 : 2,
                            left: !currIncreData ? 24 : 170,
                          }}
                        />
                      </Tooltip>
                    </Form.Item>

                    <Form.Item {...formItemLayout} label="存储表已有数据">
                      {getFieldDecorator('isDeleteSoutb', {
                        initialValue: false, //* 默认值恒为false
                      })(
                        <RadioButton
                          options={[
                            {
                              label: '清空',
                              value: true,
                              disabled:
                                Boolean(currIncreData) && !isSyncHistoryValue,
                            },
                            { label: '不清空', value: false },
                          ]}
                          onChange={this.handleDeleteSoutb}
                        />,
                      )}
                    </Form.Item>
                  </>
                ) : (
                  <>
                    <Form.Item
                      label="数据策略"
                      rules={[{ required: true, message: '请选择数据策略' }]}
                      {...formItemLayout}
                      className={styles.dataStrategy}
                    >
                      <Form.Item noStyle name="dataStrategy">
                        {getFieldDecorator('dataStrategy', {
                          initialValue: dataStrategyInitialValue, //* 全量采集 默认值覆盖0   增量采集 默认值追加1  这里一定是全量采集 所以写为覆盖
                          rules: [
                            { required: true, message: '请选择数据策略' },
                          ],
                        })(
                          <RadioButton
                            options={[
                              { label: '覆盖', value: 0 },
                              { label: '追加', value: 1 },
                            ]}
                          />,
                        )}
                      </Form.Item>
                      <Tooltip
                        title={
                          <>
                            <div>覆盖：先清空存储表中数据</div>
                            <div style={{ marginLeft: 36 }}>
                              再将采集的数据插入存储表
                            </div>
                            <div>追加：将采集的数据追加至存储表</div>
                          </>
                        }
                      >
                        <IconFont
                          type="icon-question-circle"
                          style={{
                            position: 'absolute',
                            marginLeft: 8,
                            top: 2,
                            left: 170,
                          }}
                        />
                      </Tooltip>
                    </Form.Item>

                    {getFieldValue('dataStrategy') === 0 && (
                      <>
                        <Form.Item label="覆盖策略" {...formItemLayout}>
                          <Form.Item noStyle name="coverStrategy">
                            {getFieldDecorator('coverStrategy', {
                              initialValue: coverStrategyInitialValue, // * 默认全表覆盖
                            })(
                              <RadioButton
                                options={[
                                  { label: '全表覆盖', value: 'whole' },
                                  { label: '条件覆盖', value: 'partial' },
                                ]}
                                onChange={() => {
                                  setFieldsValue({
                                    sql: undefined,
                                  });
                                  this.props.updateEditSource({
                                    ...editSource,
                                    globalsubmitinfo: {
                                      ...editSource.globalsubmitinfo,
                                      conditioncover: '',
                                    },
                                  });
                                }}
                              />,
                            )}
                          </Form.Item>
                        </Form.Item>
                        {getFieldValue('coverStrategy') === 'partial' && (
                          <Form.Item
                            label=" "
                            colon={false}
                            className={styles.sqlContainer}
                          >
                            <Form.Item noStyle name="sql" requiredMark>
                              {getFieldDecorator('sql', {
                                initialValue: sqlInitialValue,
                                rules: [
                                  {
                                    required: true,
                                    message: '请输入条件',
                                  },
                                ],
                              })(
                                <Input
                                  style={{ width: 210 }}
                                  addonBefore="where"
                                  placeholder="请输入条件"
                                />,
                              )}
                            </Form.Item>
                            <Tooltip
                              placement="right"
                              title={
                                <>
                                  <div>
                                    1.
                                    请输入标准SQL中where关键字后的筛选条件(不包括where)，支持使用全局参数
                                  </div>
                                  <div>2. Hive仅支持指定分区覆盖</div>
                                </>
                              }
                            >
                              <IconFont
                                type="icon-question-circle"
                                style={{ width: 16, left: 220 }}
                              />
                            </Tooltip>
                          </Form.Item>
                        )}
                      </>
                    )}
                  </>
                )}
                <Form.Item label="调度策略" {...formItemLayout}>
                  <Button onClick={() => this.getScheduleModal()}>
                    点击设置
                  </Button>
                </Form.Item>
                {/* DS V5.8.0 单表新增调度策略配置  👆 */}
                <>
                  <Form.Item
                    {...expandLayout}
                    style={{ marginTop: '-12px !important' }}
                  >
                    <a onClick={() => this.setState({ expand: !expand })}>
                      高级设置 &nbsp;
                      <IconFont type={expand ? 'icon-up' : 'icon-down'} />
                    </a>
                  </Form.Item>
                  <span style={{ display: expand ? 'block' : 'none' }}>
                    <AdvancedSettingBundle
                      {...advancedProps}
                      isPG={isPG}
                      isGP={isGP}
                      extracTtype={extracTtype}
                    />
                  </span>
                </>
              </Form>
            )}
          </div>
          <div className={styles.rightWarp}>
            <Overview {...overviewProps} />
          </div>
        </div>

        {/* 调度策略弹窗 */}
        {this.state.modalVisible ? (
          <ContentModal
            title="调度策略"
            visible={this.state.modalVisible}
            destroyOnClose
            onCancel={() => {
              this.setState({
                modalVisible: false,
              });
            }}
            footer={[
              <Button
                key="back"
                onClick={() => {
                  this.setState({
                    modalVisible: false,
                  });
                }}
              >
                取消
              </Button>,
              <Button
                key="submit"
                type="primary"
                handleKeyBind
                onClick={() => {
                  const target = cloneDeep(this.scheduleRef);
                  const scheduleConfigForm = target.current?.scheduleConfigForm;
                  const formValuesTemp = scheduleConfigForm.getFieldsValue();
                  scheduleConfigForm.validateFields().then((formValues) => {
                    // console.info('formValues', formValues);

                    this.setState(
                      {
                        scheduleConfigInfo: {
                          formValues: scheduleConfigForm
                            ? formValues
                            : undefined,
                          depJobInfo: target?.current
                            ? target?.current.depJobRef?.current?.depJobInfo
                            : undefined,
                        },
                      },
                      () => {
                        // ! editSource 已经在上面从 this.props里结构出来了
                        const tempSource = JSON.parse(
                          JSON.stringify(editSource),
                        );
                        const retryPayload = this.getRetryPayload();
                        const globalSubmitPayload =
                          this.getGlobalSubmitInfoPayload();

                        // !还需要单独存一下 globalsubmitinfo 当前新建时操作的调度信息
                        const targetTemp = {
                          globalsubmitinfo: {
                            ...globalSubmitPayload,
                            extractonlinestrategy:
                              formValuesTemp.dataStrategy ||
                              editSource?.globalsubmitinfo
                                ?.extractonlinestrategy, // * 数据策略 0 覆盖 1 追加
                            conditioncover: _.isEmpty(formValuesTemp?.sql)
                              ? ''
                              : formValuesTemp.sql,
                          },
                        };
                        //* 强制给 editSource 赋值
                        tempSource.globalsubmitinfo =
                          targetTemp.globalsubmitinfo;
                        tempSource.depjoblist = target?.current
                          ? target?.current.depJobRef?.current?.depJobInfo
                          : undefined;

                        // * 失败重试 表单相关
                        tempSource.retrynum = retryPayload.retrynum;
                        tempSource.isban = retryPayload.isban;
                        tempSource.retryinterval = retryPayload.retryinterval;
                        tempSource.isok = true; //! 编辑时   是否设置采集策略 恒为true

                        const tempTarget = {
                          ...editSource,
                          ...tempSource,
                        };
                        this.props.updateEditSource(tempTarget);
                        this.setState({
                          modalVisible: false,
                        });
                      },
                    );
                  });
                }}
              >
                确定
              </Button>,
            ]}
            modalSize="large"
          >
            <ScheduleConfig
              ref={this.scheduleRef}
              editSource={this.props.editSource}
            />
          </ContentModal>
        ) : null}
      </Spin>
    );
  }
}
// eslint-disable-next-line
const CheckAndSave = Form.create({
  onValuesChange(props, changedValues) {
    const { tags } = changedValues;
    if (!tags) props.editExitDetection();
  },
})(CheckAndSaveWarp);
export default CheckAndSave;
