import React from 'react';
import { connect } from 'dva';
import _ from 'lodash';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import Schema from 'async-validator';
import {
  Spin,
  Input,
  Radio,
  Select,
  Tooltip,
  Button,
  Divider,
  Checkbox,
  notification,
  Row,
  Col,
  Space,
  Dropdown,
  Menu,
} from 'antd';
import {
  SearchTree,
  IconFont,
  SearchTreeSelect,
  ColorTagTreeSelect,
  RadioButton,
  TextButton,
} from '@/components';
import { getStandardRules, DBUTILS, turnEveryLetterIntoPinyin } from '@/utils';
import CustomSelect from '@/pages/DataCollect/CustomSelect';
import {
  getSystemFields,
  isSupportCapital,
} from '@/pages/DataCollect/OffLine/utils';
import VirtualTable from './VirtualTable';
import PartitionModal from './PartitionModal';

import {
  HAVE_FIELD_IDENTIFY,
  SUPPORT_PARTITION_DB_TYPE,
  SUPPORT_FILE_FORMAT,
  SUPPORT_DISTRIBUTE_TYPE as DISTRIBUTION_DBTYPES,
  NOT_SUPPORT_RANDOMLY,
  STORE_WAY_TYPE,
} from '../conf';

import {
  FORM_LAYOYT,
  DISTRIBUTION_MODE_TIP,
  DISTRIBUTION_KEY_TIP,
  ORIENTATION_TIP,
  FIELD_PRIMARYKEY_TIP,
  FIELD_TYPE,
} from './conf';

import {
  tableIcon,
  databaseIcon,
  columns,
  listToTree,
  getCatalist,
  getStoreDatabase,
  getTitle,
} from './utils';
import styles from './index.less';

const { Option } = Select;

const DEFAULT_FIELD = {
  id: 0,
  name: '',
  type: '',
  pKey: false,
  describe: '',
  origintype: '',
  originname: '',
  origindescribe: '',
};
const LAYER = { layerid: '', layername: '', engineid: '' };
const SOURCE = { sourceid: '', sourcename: '', sourcetype: '', dbname: '' };
const TABLE = { tableid: '', tablename: '' };
const ORIENTATAION = 'column';
const COMPRESS_TYPE = 'zlib';

const strfindIndex = (str) => {
  const prefixIndex = str.indexOf('[');
  const surfixIndex = str.indexOf(']');

  return str.slice(prefixIndex + 1, surfixIndex);
};

@Form.create()
class StoreSource extends React.Component {
  storeFormRef = React.createRef();

  tableRef = React.createRef();

  partitionRef = React.createRef();

  constructor(props) {
    super(props);
    const { isEdit, tabKey, editSource, processData } = props;
    const { extractData, storeData } = processData;
    let dataLength = 0;
    let storeLength = 0;
    let radio = 'new';
    const tbNameArr = [];
    const selectedKeys = [];
    let newDataSource = [];
    const haveDataSource = [];
    const selectedRows = [];
    const selectedRowKeys = [];
    let currentLayer = LAYER;
    let currentSource = SOURCE;
    let currentTable = TABLE;
    let description;
    const fileformat = 'Orcfile';
    const orientation = ORIENTATAION;
    const compresstype = COMPRESS_TYPE;
    // 分布方式
    const distributionmode = 'HASH';
    // 选择的分布键
    const distributionbond = [];
    // 分布键选项
    const distributionbondOptions = [];
    const store = storeData.find((v) => v.key === tabKey);
    const extract = extractData.find((v) => v.key === tabKey);
    // 编辑下的进入
    if (isEdit && !store) {
      const {
        sourcedb: {
          field,
          layerid,
          engineid,
          layername,
          odsdatabase,
          dbtype,
          schema,
          sourceid,
          tableid,
          table,
        },
      } = editSource;
      // 如果是编辑时第一次进来，所有的数据从editSource拿取
      radio = 'have';
      dataLength = field.length;
      storeLength = field.length;
      currentTable = { tableid, tablename: table };
      currentLayer = { layerid, layername, engineid };
      currentSource = {
        sourceid,
        dbname: odsdatabase,
        sourcename: dbtype === 'hive' ? odsdatabase : schema,
        sourcetype: dbtype,
      };
    } else if (isEdit) {
      store.dataLength = extract
        ? extract.selectedRows.length
        : editSource.sourcedb.field.length;
    }
    // 新建状态下的进入
    if (!isEdit && store) {
      // 这里需要知道是是从上一步进入还是下一步进入
      if (store.origin === 'extract') {
        const support = isSupportCapital(store?.currentSource?.sourcetype);
        store.newDataSource = !extract.isChangedTable
          ? store.newDataSource
          : extract.selectedRows.map((v, k) => ({
              id: k,
              name: support ? v.field : v.field.toLowerCase(),
              type: v.fieldtype.toLowerCase(),
              describe: v.fieldcomment,
              pKey: false,
              origintype: v.fieldtype.toLowerCase(),
              originname: support ? v.field : v.field.toLowerCase(),
              origindescribe: v.fieldcomment,
            }));
        store.dataLength = extract.selectedRows.length;
        store.description = !extract.isChangedTable
          ? store.description
          : extract.description;
        store.values.describe = !extract.isChangedTable
          ? store.values.describe
          : extract.description;
        if (store.radio === 'new') {
          store.storeLength = store.dataLength;
        }
        //* 点击上一步，然后回到下一步的数据在这要更新store里的partitionField
        store.partitionField = store?.partitionField
          .map((item) => {
            const target = extract?.selectedRows?.find(
              (_item) => _item.field === item.name,
            );
            if (item.ispreset === HAVE_FIELD_IDENTIFY && !target) {
              return { ...item, name: '' };
              // return null;
            }
            return item;
          })
          .filter((item) => item);
        // 新建状态下 选择上一步重新选择表后也要重新更新现在的默认表名称
        currentTable.tablename = extract?.currentTable.tbname;
      } else {
        const support = isSupportCapital(store?.currentSource?.sourcetype);
        store.newDataSource = store.newDataSource.map((v) => ({
          ...v,
          originname: support ? v.originname : v.originname.toLowerCase(),
        }));
      }
    } else if (!isEdit) {
      // 新建时第一次进入
      newDataSource = extract.selectedRows.map((v, k) => ({
        id: k,
        name: v.field,
        type: v.fieldtype.toLowerCase(),
        describe: v.fieldcomment,
        originname: v.field,
        origintype: v.fieldtype.toLowerCase(),
        origindescribe: v.fieldcomment,
        pKey: false,
      }));
      dataLength = extract.selectedRows.length;
      storeLength = dataLength;
      description = extract.description;

      // 需要配置表名称默认项
      currentTable.tablename = extract.currentTable.tbname;
    }

    // haveChangedStore用来标记 是否是切换了存储源
    this.state = store
      ? { ...store, haveChangedStore: false }
      : {
          tbNameArr,
          fileformat,
          orientation,
          compresstype,
          description,
          currentLayer,
          currentSource,
          currentTable,
          newDataSource,
          haveDataSource,
          radio,
          treeData: [],
          dataLength,
          storeLength,
          selectedKeys,
          selectedRows,
          selectedRowKeys,
          distributionmode,
          distributionbond,
          distributionbondOptions,
          // *分区
          partitionField: [],
          // *已有表分区
          havePartitionField: null,
          // 已有表loading
          treeLoading: false,
          // 用来标记 是否是切换了存储源
          haveChangedStore: false,
        };

    this.onTableChange = _.debounce(this.onTableChange, 200);
  }

  componentDidMount() {
    const { setStoreRef, isEdit } = this.props;
    if (setStoreRef && typeof setStoreRef === 'function') {
      setStoreRef(this);
    }
    const { radio, treeData, selectedKeys, currentSource, distributionmode } =
      this.state;
    // 防止重复加载树组件的treedata
    if (!treeData.length) {
      this.getStoreSource();
    }
    // 如果之前进入过新建表页面，需要重新更新分布键相关信息，并且需要重置字段类型
    if (!isEdit && radio === 'new' && currentSource.sourcetype) {
      const isDistribution =
        DISTRIBUTION_DBTYPES.includes(currentSource.sourcetype) &&
        distributionmode;
      this.getFieldTrans('', isDistribution);
    }
    if (radio !== 'new') {
      this.setState({ selectedKeys: [...selectedKeys] });
    }
  }

  componentDidUpdate() {
    const { setStoreRef } = this.props;
    if (setStoreRef && typeof setStoreRef === 'function') setStoreRef(this);
  }

  getStoreSource = () => {
    const {
      dispatch,
      user: { projectId },
      setSaveButton,
    } = this.props;
    setSaveButton(true);

    // 取引擎数据 判断存储源引擎状态
    dispatch({
      type: 'singleAndTdc/listEngineInfo',
    });
    dispatch({
      type: 'singleAndTdc/listTableMetaTags',
      payload: { projectid: projectId, tagtype: '表' },
    });
    dispatch({
      type: 'singleAndTdc/listNewStoreInfo',
      payload: { projectid: projectId, tagtype: '表' },
    });
    dispatch({
      type: 'singleAndTdc/listHaveStoreInfo',
      payload: { projectid: projectId, tagtype: '表', showdb: true },
    }).then(() => this.getTreeData());
  };

  // eslint-disable-next-line
  sortTableData = (tableData, partitionField) => {
    const systemFields = getSystemFields(partitionField);
    const originData = tableData.filter(
      (item) => !systemFields.map((_item) => _item.name).includes(item.name),
    );
    const otherData = tableData.filter((item) =>
      systemFields.map((_item) => _item.name).includes(item.name),
    );
    return [...originData, ...otherData];
  };

  // eslint-disable-next-line
  getStoreFieldData = (fields, { threerecord, partitionFields, isCapital }) => {
    return (fields || []).map((v, k) => ({
      key: k,
      num: k,
      field: isCapital ? v.name : v.name.toLowerCase(),
      fieldtype: v.fieldtype,
      fieldcomment: v.annotation,
      first: threerecord[0] ? threerecord[0][k] : '',
      second: threerecord[1] ? threerecord[1][k] : '',
      three: threerecord[2] ? threerecord[2][k] : '',
      //* 字段标识 v5.0
      fieldName: isCapital ? v.name : v.name.toLowerCase(),
      fieldType: v.fieldtype,
      isPartition: v.ispartition,
      isPartitionSystem: '',
      isPartitionCuston: '',
      pattern: '', // 为系统预置字段时的预置类型，'day | month | year'
      isPrim: v.isprim,
      isUnique: v.isunique,
      partitionNumber:
        (partitionFields || []).findIndex((p) => p === v.name) + 1 || 0,
      rankName: v.rankname,
      rankNum: v.ranknum,
    }));
  };

  // 获取树组件的数据，只有在选择已有表的情况下执行
  getTreeData = async () => {
    if (this.state.radio === 'new') {
      this.props.setSaveButton(false);
      return;
    }
    const {
      isEdit,
      dispatch,
      singleAndTdc: { haveLayerlist, engineList },
    } = this.props;
    const tempData = haveLayerlist.filter((v) => v.isgather && v.engineid);
    const treeData = await Promise.all(
      tempData.map(async (v) => {
        // 补充引擎状态信息
        const isDiabledFather =
          engineList.find((it) => it?.engineid === v.engineid)?.enginestatus !==
          'GREEN';
        const item = {
          ...v,
          key: v.layername,
          title: v.layername,
          type: 'layer',
          icon: databaseIcon(v.enginename),
          sourcedb: v.sourcedb || [],
          extra: isDiabledFather ? (
            <span style={{ marginRight: 8 }}>(故障)</span>
          ) : (
            ''
          ),
          disabled: isDiabledFather,
        };
        if (v.sourcedb && v.sourcedb.length) {
          item.children = await this.getSourceList(v, isDiabledFather);
        } else {
          item.disabled = true || isDiabledFather;
        }
        return item;
      }),
    );
    this.setState({ treeData, treeLoading: false }, () => {
      if (isEdit) {
        // 这里需要请求出表详情，然后渲染table
        // 如果是新建表，这里拿不到tableid，需要从treeData中筛选出来tableid
        // tablename;
        const { currentTable, currentLayer, currentSource } = this.state;
        const layer = treeData.find((v) => v.layerid === currentLayer.layerid);
        if (!layer) {
          this.matchSource('数据层');
          return;
        }
        const source = layer.children
          ? layer.children.find(
              (v) => v.sourcename === currentSource.sourcename,
            )
          : undefined;
        if (!source) {
          this.matchSource('存储层');
          return;
        }
        const tb = source.children
          ? source.children.find((v) => v.tablename === currentTable.tablename)
          : undefined;
        if (!tb) {
          this.matchSource('表');
          return;
        }

        dispatch({
          type: 'singleAndTdc/listStoreTableDetail',
          payload: { id: currentTable.tableid || tb.tableid, status: 1 },
        }).then(() => {
          // 已有表的表详情，组成datasource
          const {
            editSource: { sourcedb },
            singleAndTdc: {
              storeTableDetail: {
                fieldinfo,
                fielddata,
                fileformat = 'Orcfile',
                partitionfield,
                partitioninfofields,
              },
            },
          } = this.props;

          // fieldinfo、fielddata可能会为null，使用解构不能赋初始值
          const threerecord = fielddata || [];
          const tempFieldinfo = fieldinfo || [];
          const sortData = this.sortTableData(tempFieldinfo, partitionfield);
          const tableData = this.getStoreFieldData(sortData, {
            threerecord,
            partitionFields: partitioninfofields,
            isCapital: isSupportCapital(sourcedb?.dbtype),
          });
          const haveDataSource = DBUTILS[sourcedb?.dbtype].fieldShow(tableData);

          const selectedRows = [];
          const selectedRowKeys = [];
          haveDataSource.forEach((v, k) => {
            if (sourcedb.field.includes(v.field)) {
              selectedRows.push(v);
              selectedRowKeys.push(k);
            }
          });
          const selectedKeys = [
            `${currentLayer.layername}${currentSource.sourcename}${currentTable.tablename}`,
          ];
          this.setState(
            {
              fileformat,
              selectedRows,
              selectedRowKeys,
              storeLength: selectedRows.length,
              selectedKeys,
              haveDataSource,
              havePartitionField: {
                partitionField: partitionfield || [],
                //* 后端借前端做中转，获取到值后，再重新传给后端
                partitionFieldArr: partitioninfofields,
              },
            },
            () => this.props.setSaveButton(false),
          );
        });
      } else {
        this.props.setSaveButton(false);
      }
    });
  };

  matchSource = (type) => {
    const message = `未找到对应的${type}`;
    notification.error({ message });
    this.setState(
      {
        fileformat: 'Orcfile',
        selectedRows: [],
        selectedRowKeys: [],
        storeLength: 0,
        selectedKeys: [],
        haveDataSource: [],
      },
      () => this.props.setSaveButton(false),
    );
  };

  // 已有表，树组件treedata
  getSourceList = (item, isDiabledFather) => {
    const sources = item.sourcedb || [];
    this.setState({ treeLoading: true });
    return new Promise((resolve) => {
      const sourceList = Promise.all(
        sources.map(async (v) => {
          const { sourcename } = v;
          const listItem = {
            ...v,
            ...item,
            key: `${item.layername}${sourcename}`,
            title: sourcename,
            icon: databaseIcon(),
            type: 'database',
            disabled: isDiabledFather, // 引擎故障 下面的表全部故障不让选
            extra: isDiabledFather ? (
              <span style={{ marginRight: 8 }}>(故障)</span>
            ) : (
              ''
            ),
          };
          listItem.children = await this.getTable(listItem, isDiabledFather);
          if (!listItem.children || !listItem.children.length) {
            listItem.disabled = true;
          }
          return listItem;
        }),
      );
      resolve(sourceList);
    });
  };

  // 已有表的时候获取全部的表
  getTable = (item, isDiabledFather) => {
    const { dispatch } = this.props;
    const { id } = item;
    return new Promise((resolve) => {
      dispatch({
        type: 'singleAndTdc/listStoreTable',
        // payload: { sourceid: id, isrt: false },
        payload: {
          perpage: -1,
          page: 1,
          reqtype: 2, // 筛选
          filterds: [
            {
              filtertype: 1,
              filterfield: 'sourceid',
              filtercontent: [id],
            },
          ],
        },
      }).then(async () => {
        const { storeTable } = this.props.singleAndTdc;
        // 对存储表先做去重操作
        const obj = {};
        const tempTableArr = storeTable.reduce((current, next) => {
          // eslint-disable-next-line
          obj[next.tablename]
            ? ''
            : (obj[next.tablename] = true && current.push(next));
          return current;
        }, []);
        // 再排序操作
        const sortArr = _.sortBy(tempTableArr, (v) => v.tablename);
        // 最后组成children后resolve
        const tableData = sortArr.map((v) => {
          const { tablename } = v;
          const isPaimon = v.fileformat?.toLocaleLowerCase() === 'paimon';

          return {
            ...v,
            ...item,
            type: 'table',
            icon: tableIcon,
            title: getTitle(tablename),
            disabled: isDiabledFather || isPaimon, // 引擎故障全部不让选
            key: `${item.layername}${item.sourcename}${tablename}`,
            extra: isDiabledFather ? (
              <span style={{ marginRight: 8 }}>(故障)</span>
            ) : isPaimon ? (
              <span style={{ marginRight: 8 }}>(不支持paimon表)</span>
            ) : (
              ''
            ),
          };
        });
        resolve(tableData);
      });
    });
  };

  // 已有表的时候选表获取表详情
  onSelectTree = (nodeKeys, { node }) => {
    this.setState({ selectedKeys: nodeKeys });
    if (node.type === 'table') {
      const {
        layername,
        layerid,
        engineid,
        sourceid,
        dbname,
        sourcename,
        sourcetype,
        tableid,
        tablename,
      } = node;
      const { dispatch } = this.props;
      dispatch({
        type: 'singleAndTdc/listStoreTableDetail',
        payload: { id: tableid, status: 1 },
      }).then(() => {
        // 已有表的表详情，组成datasource
        const {
          storeTableDetail: {
            fieldinfo,
            fielddata,
            fileformat = 'Orcfile',
            partitionfield,
            partitioninfofields,
          },
        } = this.props.singleAndTdc;
        // fieldinfo、fielddata可能会为null，使用解构不能赋初始值
        const threerecord = fielddata || [];
        const tableData = this.getStoreFieldData(fieldinfo, {
          threerecord,
          partitionFields: partitioninfofields,
          isCapital: isSupportCapital(sourcetype),
        });

        const haveDataSource = DBUTILS[sourcetype].fieldShow(tableData);
        this.setState({
          fileformat,
          currentLayer: {
            layername,
            layerid,
            engineid,
          },
          currentSource: {
            dbname,
            sourceid,
            sourcename,
            sourcetype,
          },
          currentTable: {
            tableid,
            tablename,
          },
          haveDataSource,
          storeLength: 0,
          selectedRows: [],
          selectedRowKeys: [],
          havePartitionField: {
            partitionField: partitionfield || [],
            //* 后端借前端做中转，获取到值后，再重新传给后端
            partitionFieldArr: partitioninfofields,
          },
        });
        this.setState({
          orientation: node.orientation,
          // eslint-disable-next-line react/no-unused-state
          compresstype: node.compresstype,
        });
      });
    }
  };

  // 新建表的时候选选择当前存储源
  onDbnameChange = (val) => {
    const { form, singleAndTdc } = this.props;
    const { layerlist } = singleAndTdc;
    const { newDataSource } = this.state;
    const tbnameErr = form.getFieldsError(['tbname']);
    if (tbnameErr?.tbname) {
      form.resetFields(['tbname']);
    }
    // 切换存储源后应该清除当前所属类目
    form.resetFields(['catalogid']);
    //! 强制清掉  从FieldMapping回来的时候 表单有initialValue  reset会回到initialValue
    //!  但本质上需要的是undefined之类的空值
    form.setFieldsValue({
      catalogid: undefined,
    });
    let currentLayer = {};
    let currentSource = {};
    for (let i = 0; i < layerlist.length; i++) {
      const item = layerlist[i];
      const source = item.sourcedb || [];
      for (let j = 0; j < source.length; j++) {
        if (source[j].id === val) {
          currentLayer = {
            layerid: item.layerid,
            layername: item.layername,
            engineid: item.engineid,
          };
          currentSource = {
            sourceid: source[j].id,
            dbname: source[j].dbname,
            sourcename: source[j].sourcename,
            sourcetype: source[j].sourcetype,
          };
          break;
        }
      }
    }

    const support = isSupportCapital(currentSource?.sourcetype);
    // 重新进行字段是否支持大小写名称校验
    const tempDataSource = newDataSource.map((v) => ({
      ...v,
      name: support ? v.name : v.name.toLowerCase(),
      originname: support ? v.originname : v.originname.toLowerCase(),
    }));

    this.setState(
      {
        currentLayer,
        currentSource,
        newDataSource: tempDataSource,
        haveChangedStore: true,
      },
      () => {
        // 获取存储源下的所有表名称，做表名称重名校验用，如果当前已有表名称，需要出发自动校验
        this.validateTbname();
        this.validateNewFields();
        // 选择具有分布键存储源的时候，需要在进行字段转换 后获取分布键相关信息
        const isDistribution = DISTRIBUTION_DBTYPES.includes(
          currentSource.sourcetype,
        );
        // 重新进行字段转换
        this.getFieldTrans('', isDistribution);
      },
    );
  };

  validateNewFields = () => {
    const { form } = this.props;
    const { newDataSource } = this.state;
    const listNameArr = newDataSource.map((v) => [
      `list[${v.id}].name`,
      `list[${v.id}].type`,
      `list[${v.id}].describe`,
      `list[${v.id}].pKey`,
    ]);
    form.validateFields(_.flatten(listNameArr), { force: true });
  };

  // 新建表时获取存储源下的所有表
  validateTbname = () => {
    const { form, dispatch } = this.props;
    const {
      currentTable: { tablename },
      currentLayer: { engineid },
      currentSource: { sourcename, dbname },
    } = this.state;
    dispatch({
      type: 'singleAndTdc/getCurrentTables',
      payload: { engineid, sourcename, dbname },
    }).then(() => {
      const { currentTables } = this.props.singleAndTdc;
      this.setState({ tbNameArr: currentTables.map((v) => v.tablename) });
      const isRepeat = currentTables.find((v) => v.tablename === tablename);
      if (tablename && isRepeat) {
        form.setFields({
          tbname: { tablename, errors: [new Error('名称已存在，请重命名')] },
        });
      }
      // eslint-disable-next-line
      tablename && form?.validateFields(['tbname']);
    });
  };

  // 输入表名称
  onTbnameChange = (e) => {
    this.setState({ currentTable: { tableid: '', tablename: e.target.value } });
  };

  // 文件类型选择
  filetypeChange = (val) => {
    this.setState({ fileformat: val });
  };

  orientationChange = (val) => {
    this.setState((prevState) => ({
      orientation: val,
      newDataSource: prevState.newDataSource.map((item) => ({
        ...item,
        pKey: false,
      })),
    }));
  };

  // 选择分布方式
  distributionChange = (val) => this.setState({ distributionmode: val });

  // 分布键表单项校验
  validateDistributionbond = (val = [], datasource = []) => {
    // 判断选中的分布键是否包含主键
    const primaryFields = datasource.filter((v) => v.pKey).map((v) => v.name);
    // 检测所选的分布键是不是主键，如果不是，触发表单报错
    const isIncludes =
      !val.length ||
      (primaryFields.length &&
        val.filter((v) => !primaryFields.includes(v)).length);
    if (isIncludes) {
      this.props.form.setFields({
        distributionbond: {
          value: val,
          errors: [
            new Error(
              val.length
                ? '当前表中包含主键，分布键只能在主键中选择'
                : '分布键不能为空',
            ),
          ],
        },
      });
    } else {
      this.props.form.setFieldsValue({ distributionbond: val });
    }
  };

  // 选择分布键
  distributionbondChange = (val) => {
    const {
      newDataSource,
      currentSource: { sourcetype },
    } = this.state;
    const newVal = sourcetype === 'uxdb' ? [val] : val;
    this.setState({ distributionbond: newVal }, () =>
      this.validateDistributionbond(newVal, newDataSource),
    );
  };

  // 选择teryx存储源时获取分布键相关信息
  getDistribution = (extract, sourcetype) => {
    const { dispatch } = this.props;
    // const { orientation } = this.state;
    const support = isSupportCapital(sourcetype);
    const extractFileds = extract.selectedRows.map((v) =>
      support ? v.field : v.field.toLowerCase(),
    );
    dispatch({
      type: 'singleAndTdc/getDistributionKey',
      payload: {
        isrealtime: false,
        extractid: extract.currentDatabase.dbid,
        schemaid: extract.currentSchema.schemaid,
        tableid: extract.currentTable.tbid,
        tbname: extract.currentTable.tbname,
        fieldnames: extractFileds,
      },
    }).then(() => {
      const {
        recdistributionmode,
        recdistributionbond = [],
        uxdistributionmode,
        uxdistributionbond = [],
        dmdistributionmode,
        dmdistributionbond = [],
      } = this.props.singleAndTdc.distributionKey;
      let mode;
      let tempBonds;
      switch (sourcetype) {
        case 'uxdb':
          mode = uxdistributionmode;
          tempBonds = uxdistributionbond;
          break;
        case 'dameng':
          mode = dmdistributionmode;
          tempBonds = dmdistributionbond;
          break;
        // ??? 不明白原作者为什么要自己判断此处的逻辑，我认为该智能推荐的逻辑应该在后端接口中处理会更好
        case 'gaussdb':
          mode =
            recdistributionmode === 'RANDOMLY' ? 'HASH' : recdistributionmode;
          tempBonds = recdistributionbond;
          break;
        case 'gaussdba':
          mode =
            recdistributionmode === 'RANDOMLY' ? 'HASH' : recdistributionmode;
          tempBonds = recdistributionbond;
          break;
        default:
          mode = recdistributionmode;
          tempBonds = recdistributionbond;
          break;
      }
      const bonds = tempBonds.filter((v) => extractFileds.includes(v));
      this.setState(
        {
          distributionmode: mode || 'HASH',
          distributionbond: bonds,
          // 重置完分布键后把标识符重置
          haveChangedStore: false,
        },
        () => {
          this.props.form.setFieldsValue({
            distributionmode: mode || 'HASH',
            distributionbond: bonds.length ? bonds : undefined,
          });
          setTimeout(() => {
            this.resetTableForm();
          }, 500);
        },
      );
    });
  };

  // 切换已有表和新建表
  onRadioChange = (e) => {
    const { value } = e.target;
    // 切换radio后选择的当前数据需要置空
    // 选择已有表
    if (value !== 'new') {
      this.setState(
        {
          radio: value,
          haveDataSource: [],
          storeLength: 0,
          selectedRows: [],
          selectedRowKeys: [],
          currentLayer: LAYER,
          currentSource: SOURCE,
          currentTable: TABLE,
        },
        () => {
          this.getTreeData();
        },
      );
    } else {
      const { newDataSource } = this.state;
      this.setState(
        {
          radio: value,

          currentLayer: LAYER,
          currentSource: SOURCE,
          currentTable: TABLE,
          storeLength: newDataSource.length,
          orientation: ORIENTATAION,
          // eslint-disable-next-line react/no-unused-state
          compresstype: COMPRESS_TYPE,
          newDataSource: newDataSource.map((v) => ({
            ...v,
            pKey: false,
            type: v.origintype.toLowerCase(),
            name: v.originname,
            describe: v.origindescribe,
          })),
        },
        () => {
          // 清空跟存储源有关的表单项
          this.props.form.resetFields([
            'tbname',
            'catalogid',
            'sourceid',
            'distributionmode',
            'distributionbond',
          ]);
        },
      );
    }
  };

  // 获取选取的表选项
  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.props.editExitDetection();
    const { havePartitionField } = this.state;
    const systemFieldsArr = getSystemFields(
      havePartitionField?.partitionField || [],
    ).map((item) => item.name);
    const newRows = selectedRows.filter(
      (item) => !systemFieldsArr.includes(item.field),
    );

    this.setState({
      storeLength: newRows.length,
      selectedRowKeys,
      selectedRows,
    });
  };

  // 清空字段
  handleClear = () => {
    this.setState({ newDataSource: [DEFAULT_FIELD], storeLength: 1 }, () => {
      this.resetTableForm();
      notification.success({ message: '已清空当前所有字段' });
    });
  };

  // 字段信息升降
  upOrDownGrade = (record, type) => {
    const { newDataSource } = this.state;
    const index = record.id;
    const end = newDataSource.length - 1;
    const tempData = [...newDataSource];
    if (type === 'up' && index !== 0) {
      // eslint-disable-next-line
      tempData[index] = tempData.splice(index - 1, 1, tempData[index])[0];
    } else if (type === 'up' && index === 0) {
      notification.info({ message: '当前行已经是第一行了！' });
    } else if (type === 'down' && index !== end) {
      // eslint-disable-next-line
      tempData[index] = tempData.splice(index + 1, 1, tempData[index])[0];
    } else if (type === 'down' && index === end) {
      notification.info({ message: '当前行已经是最后一行了！' });
    }
    const datasource = tempData.map((v, k) => ({
      ...v,
      id: k,
    }));
    this.setState({ newDataSource: [...datasource] }, () => {
      this.resetTableForm();
    });
  };

  // 重置table中的表单的默认值，并重新进行校验
  resetTableForm = () => {
    const { form } = this.props;
    const { newDataSource } = this.state;
    const listNameArr = newDataSource.map((v) => [
      `list[${v.id}].name`,
      `list[${v.id}].type`,
      `list[${v.id}].describe`,
      `list[${v.id}].pKey`,
    ]);
    form.resetFields(_.flatten(listNameArr));
    setTimeout(() => {
      form.validateFields(_.flatten(listNameArr), { force: true });
    }, 500);
  };

  // 删除字段
  delField = (record) => {
    const { newDataSource, storeLength } = this.state;
    const tempData = [...newDataSource];
    const index = record.id;
    if (newDataSource.length === 1) {
      this.handleClear();
      return;
    }
    tempData.splice(index, 1);
    this.setState(
      {
        newDataSource: [...tempData].map((v, k) => ({ ...v, id: k })),
        storeLength: storeLength - 1,
      },
      () => this.resetTableForm(),
    );
  };

  // 添加字段
  addField = () => {
    const { newDataSource, storeLength, currentSource } = this.state;
    if (!currentSource.sourcetype) {
      notification.warning({ message: '请先选择存储源！' });
      return;
    }
    const count = newDataSource.length;
    const tempData = [...newDataSource];
    tempData.push({
      id: count,
      name: '',
      type: '',
      originname: '',
      origintype: '',
      origindescribe: '',
      pKey: false,
      describe: '',
    });
    this.setState({ newDataSource: tempData, storeLength: storeLength + 1 });
  };

  // eslint-disable-next-line
  getOriginDataSource = (extractItem, sourcetype) => {
    const support = isSupportCapital(sourcetype);
    const extractSelectRows = extractItem?.selectedRows || [];
    const newData = extractSelectRows.map((item, index) => {
      return {
        id: index,
        describe: item?.fieldcomment,
        name: support ? item?.field : item?.field.toLowerCase(),
        origindescribe: item?.fieldcomment,
        originname: support ? item?.field : item?.field.toLowerCase(),
        origintype: item?.fieldtype.toLowerCase(),
        type: item?.fieldtype.toLowerCase(),
        pKey: false,
      };
    });
    return newData;
  };

  // 字段类型转换
  getFieldTrans = (formType, isDistribution = false) => {
    const {
      newDataSource,
      currentSource: { sourcetype },
      haveChangedStore,
    } = this.state;

    const {
      tabKey,
      dispatch,
      processData: { extractData, storeData },
    } = this.props;
    const store = storeData.find((v) => v.key === tabKey);
    const extract = extractData.find((v) => v.key === tabKey);
    const tempArr = ['extract', 'mapping'];
    const newSourceData = !tempArr.includes(store?.origin)
      ? this.getOriginDataSource(extract, sourcetype)
      : newDataSource;
    // 如果没有选择存储源，无法进行类型转换
    if (!sourcetype) {
      this.setState(
        {
          newDataSource: [...newSourceData].map((v) => ({
            ...v,
            pKey: false,
            name: v.originname,
            type: v.origintype.toLowerCase(),
            describe: v.origindescribe,
          })),
          storeLength: newDataSource.length,
        },
        () => this.resetTableForm(),
      );
      notification.success({ message: '重置源字段成功！' });
      return;
    }

    const extrtype = extract.currentDatabase.dbtype;

    // const fieldtype = newSourceData.map((v) => v.origintype);
    // const fieldtypeprecision = extract.dataSource.map(
    //   (item) => item.fieldtypeprecision,
    // );
    // const fieldtypescale = extract.dataSource.map(
    //   (item) => item.fieldtypescale,
    // );
    const fieldtype = newSourceData.map((v) => v.origintype);

    // 构建映射表，将 extract.dataSource 转为 { fieldname: { precision, scale } }
    const dataSourceMap = Object.fromEntries(
      extract.dataSource.map((item) => [
        item.fieldName.toUpperCase(),
        {
          fieldtypeprecision: item.fieldtypeprecision,
          fieldtypescale: item.fieldtypescale,
        },
      ]),
    );
    // 根据 newSourceData 提取结果
    const tempMatchResult = newSourceData.map((v) => {
      //! 全部转成大写匹配
      const searchName = v.originname.toUpperCase();
      const match = dataSourceMap[searchName];
      return {
        fieldtypeprecision: match.fieldtypeprecision || 0,
        fieldtypescale: match.fieldtypescale || 0,
      };
    });

    // 提取独立数组
    const fieldtypeprecisionArray = tempMatchResult.map(
      (item) => item.fieldtypeprecision,
    );
    const fieldtypescaleArray = tempMatchResult.map(
      (item) => item.fieldtypescale,
    );

    dispatch({
      type: 'singleAndTdc/listFieldTrans',
      payload: {
        extrtype,
        sourcetype,
        fieldtype,
        fieldtypeprecision: fieldtypeprecisionArray,
        fieldtypescale: fieldtypescaleArray,
      },
    }).then(() => {
      const { fieldTrans } = this.props.singleAndTdc;
      let newFieldSource = newSourceData.map((v, k) => ({
        ...v,
        pKey: false,
        type: fieldTrans[k] || v.type,
        // name: v.originname,
        // describe: v.origindescribe,
      }));
      // 如果是点击加载源字段按钮进行转换的，需要提示，否则为隐式转换
      if (formType === 'origin') {
        // * 只要是点击【重置为源字段】  直接全部重置  即便是从【mapping】回来也一样
        newFieldSource = this.getOriginDataSource(extract, sourcetype).map(
          (inner, idx) => {
            return {
              ...inner,
              pKey: false,
              type: fieldTrans[idx] || inner.type,
            };
          },
        );
        notification.success({ message: '重置源字段成功！' });
      }
      this.setState(
        {
          newDataSource: [...newFieldSource],
          storeLength: newFieldSource.length,
        },
        () => {
          // if (isDistribution && tempArr.includes(store?.origin)) {
          //! 即便是从后面点击上一步回来的时候  保存原分布相关数据
          //! 但若切换了存储源 需要更换为最新存储源匹配的分布数据
          if (
            isDistribution &&
            (!tempArr.includes(store?.origin) || haveChangedStore)
          ) {
            this.getDistribution(extract, sourcetype);
          } else {
            this.resetTableForm();
          }
        },
      );
    });
  };

  // 表格中的输入框onchange事件
  handelOnChange = (e, record, type) => {
    if (e.persist) e.persist();
    const {
      currentSource: { sourcetype },
    } = this.state;
    const support = isSupportCapital(sourcetype);
    const value = e.target
      ? type === 'pKey'
        ? e.target.checked
        : support
        ? e.target.value
        : e.target.value.toLowerCase()
      : e;
    this.onTableChange(value, record, type);
  };

  setDistributionBond = (bond) => {
    this.props.form.setFieldsValue({
      distributionbond: bond.length ? bond : undefined,
    });
  };

  /** 如果字段做了修改，需要同步分布键
   *  1. 修改字段名称
   *  2. 修改字段类型(达梦不支持text)
   */
  updateDistributionKey = (type) => {
    const { newDataSource, distributionbond } = this.state;
    let newBond = distributionbond ? [...distributionbond] : [];
    if (type === 'name') {
      // 如果编辑了字段名称，且该字段名称恰好时选中的分布键，需要清除该分布键
      const names = newDataSource.map((v) => v.name);
      newBond = _.intersection(names, distributionbond || []);
      this.setDistributionBond(newBond);
      return newBond;
    }
    return newBond;
  };

  // table里面的输入框事件
  onTableChange = (value, record, type) => {
    const {
      newDataSource,
      currentSource,
      distributionbond,
      currentSource: { sourcetype },
    } = this.state;

    const index = record.id;
    const tempdata = [...newDataSource];
    const item = tempdata[index];
    let newDistributeKeys = distributionbond ? [...distributionbond] : [];

    switch (type) {
      case 'name':
        item.name = value;
        break;
      case 'type':
        item.type = value;
        // 二进制字段不得作为主键
        if (this.constructor.getBinaryFieldOfSource(sourcetype) === value) {
          item.pKey = false;
        }
        break;
      case 'describe':
        item.describe = value;
        break;
      case 'pKey':
        item.pKey = value;
        break;
      default:
        break;
    }
    tempdata[index] = item;
    if (DISTRIBUTION_DBTYPES.includes(currentSource.sourcetype)) {
      newDistributeKeys = this.updateDistributionKey(type);
    }

    this.setState(
      { newDataSource: [...tempdata], distributionbond: newDistributeKeys },
      () => {
        const { distributionmode } = this.state;
        // this.resetTableForm();
        // 如果选择了主键的话校验分布键
        if (
          type === 'pKey' &&
          DISTRIBUTION_DBTYPES.includes(currentSource.sourcetype)
        ) {
          if (distributionmode === 'RANDOMLY') {
            notification.warning({
              message: '已设置主键，无法使用RANDOMLY分布',
            });
            this.setState(
              { distributionmode: 'HASH', distributionbond: undefined },
              () => {
                this.props.form.setFieldsValue({
                  distributionmode: 'HASH',
                  distributionbond: undefined,
                });
                this.validateDistributionbond([], [...tempdata]);
              },
            );
          } else {
            this.validateDistributionbond(distributionbond, [...tempdata]);
          }
        }
      },
    );
  };

  // table中的form校验需要定位滚动到具体报错的td位置
  positonTableError = (index = 0) => {
    const { newDataSource } = this.state;
    const tableScroll = this.tableRef.current.querySelector(`.ant-table-body`);
    tableScroll.scrollTop = index * 53;
    const fieldArr = _.flatten(
      [...newDataSource].map((v) => [
        `list[${v.id}].name`,
        `list[${v.id}].describe`,
      ]),
    );
    setTimeout(() => {
      this.props.form.validateFields(_.flatten(fieldArr), { force: true });
    }, 100);
  };

  // 新建表，获取表格中字段类型下拉框选项
  getTypeOptions = (type) => {
    const rawTypeOptions = this.constructor.getRawTypeOptions(type);
    return rawTypeOptions.map((field) =>
      /**
       * 字段类型中，可能存在某一种类型的字段，在各个db内部的名称不一致
       * 如二进制可能名为binary、BINARY等等
       * 业务上需要判断是否属于这种类型，所以字段也有可能是一个{value,isXXX}的对象
       */
      typeof field === 'string' ? field : field.value,
    );
  };

  static getRawTypeOptions = (type) => {
    if (!type) return [];
    switch (type) {
      case 'hive':
        return FIELD_TYPE.hive;
      case 'odps':
        return FIELD_TYPE.hive;
      case 'tdh_inceptor':
        return FIELD_TYPE.hive;
      case 'uxdb':
        return FIELD_TYPE.ux;
      case 'dameng':
        return FIELD_TYPE.dameng;
      case 'drealdb':
        return FIELD_TYPE.drealdb;
      default:
        // tdsql和stork 类型都参考stork
        return FIELD_TYPE.stork;
    }
  };

  static getBinaryFieldOfSource = (type) =>
    this.getRawTypeOptions(type).find((item) => item.isBinary)?.value;

  getNewColumns = () => {
    const {
      newDataSource,
      currentSource: { sourcetype },
      partitionField,
      orientation,
    } = this.state;
    const {
      form: { getFieldDecorator },
    } = this.props;
    const typeOptions = this.getTypeOptions(sourcetype);
    const disabled = newDataSource.length === 1;
    const systemFieldsArr = getSystemFields(partitionField).map(
      (item) => item.name,
    );
    const binaryField = this.constructor.getBinaryFieldOfSource(sourcetype);
    return [
      {
        title: (
          <span>
            <i style={{ color: 'red' }}>*</i>
            字段名
            <Space style={{ float: 'right' }}>
              <Dropdown overlay={this.fieldnameMenu()}>
                <IconFont type="icon-setting" style={{ color: '#1980ff' }} />
              </Dropdown>
            </Space>
          </span>
        ),
        key: 'name',
        dataIndex: 'name',
        skipCheckEmpty: true,
        render: (val, record) => {
          const { id } = record;
          const nameArrs = _.remove(
            newDataSource.map((v) => v.name),
            (v, k) => k !== id,
          ).filter((item) => item);
          return (
            <Form.Item>
              {getFieldDecorator(`list[${id}].name`, {
                initialValue: val || undefined,
                rules: getStandardRules(
                  '请填写字段',
                  ['', nameArrs.concat(systemFieldsArr)],
                  'tableField',
                  sourcetype,
                  'field',
                ),
              })(
                <Input
                  onChange={(e) => this.handelOnChange(e, record, 'name')}
                  placeholder="请输入字段名"
                />,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: '字段类型',
        key: 'type',
        dataIndex: 'type',
        skipCheckEmpty: true,
        render: (val, record) => {
          const { id } = record;
          return getFieldDecorator(`list[${id}].type`, {
            initialValue: val || undefined,
          })(
            <Select
              onClick={() => {
                if (!sourcetype) {
                  notification.warning({ message: '请先选择存储源！' });
                }
              }}
              onChange={(e) => this.handelOnChange(e, record, 'type')}
              style={{ width: '100%' }}
              getPopupContainer={() => document.body}
              placeholder="请选择字段类型"
            >
              {typeOptions.map((v) => (
                <Option key={v} value={v}>
                  {v}
                </Option>
              ))}
            </Select>,
          );
        },
      },
      {
        title: (
          <div>
            字段注释
            <Space style={{ float: 'right' }}>
              <Dropdown overlay={this.commentMenu()}>
                <IconFont type="icon-setting" style={{ color: '#1980ff' }} />
              </Dropdown>
            </Space>
          </div>
        ),
        key: 'describe',
        dataIndex: 'describe',
        skipCheckEmpty: true,
        render: (val, record) => {
          const { id } = record;
          return (
            <Form.Item>
              {getFieldDecorator(`list[${id}].describe`, {
                initialValue: val || undefined,
                rules: getStandardRules('', [], 'comment', sourcetype),
              })(
                <Input
                  onChange={(e) => this.handelOnChange(e, record, 'describe')}
                  placeholder="请填写字段注释"
                />,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            主键
            <Tooltip
              overlayStyle={{ maxWidth: '295px' }}
              title={FIELD_PRIMARYKEY_TIP}
            >
              <IconFont type="icon-question-circle" style={{ marginLeft: 8 }} />
            </Tooltip>
          </span>
        ),
        key: 'pKey',
        dataIndex: 'pKey',
        width: 100,
        skipCheckEmpty: true,
        render: (val, record) => {
          const { id, pKey, type } = record;
          const isDisplay =
            ['dameng', 'uxdb'].includes(sourcetype) && type === 'text';
          const disabledPKey =
            STORE_WAY_TYPE.includes(sourcetype) && orientation === 'column';
          const checkboxElement = (
            <Checkbox
              checked={pKey}
              onChange={(e) => this.handelOnChange(e, record, 'pKey')}
              disabled={disabledPKey}
            />
          );
          return isDisplay || binaryField === type
            ? null
            : getFieldDecorator(`list[${id}].pKey`, {
                initialValue: pKey,
              })(
                disabledPKey ? (
                  <Tooltip title="列存储方式不支持设置主键">
                    {checkboxElement}
                  </Tooltip>
                ) : (
                  checkboxElement
                ),
              );
        },
      },
      {
        title: '操作',
        key: 'action',
        width: 107,
        render: (text, record) => (
          <>
            <a
              disabled={disabled}
              onClick={() => this.upOrDownGrade(record, 'up')}
            >
              <IconFont type="icon-arrow-up" />
            </a>
            <Divider type="vertical" />
            <a
              disabled={disabled}
              onClick={() => this.upOrDownGrade(record, 'down')}
            >
              <IconFont type="icon-arrow-down" />
            </a>
            <Divider type="vertical" />
            <a disabled={disabled} onClick={() => this.delField(record)}>
              <IconFont type="icon-delete" />
            </a>
          </>
        ),
      },
    ];
  };

  // 处理一键将字段名转为全大写/全小写
  transferFieldName = (type) => {
    const { newDataSource, currentSource, distributionbond } = this.state;
    let newDistributeKeys = [...distributionbond];
    const tempDataSource = newDataSource.map((v) => {
      let { name } = v;
      if (type === 'upper') {
        name = name.toLocaleUpperCase();
      } else if (type === 'lower') {
        name = name.toLocaleLowerCase();
      } else {
        name = turnEveryLetterIntoPinyin(name);
      }

      this.props.form.setFieldsValue({
        [`list[${v.id}].name`]: name,
      });

      return {
        ...v,
        name,
      };
    });

    this.setState(
      {
        newDataSource: tempDataSource,
      },
      () => {
        if (DISTRIBUTION_DBTYPES.includes(currentSource.sourcetype)) {
          newDistributeKeys = this.updateDistributionKey('name');
        }
        //* 编辑了字段名称 直接清空分布键 参考原有逻辑

        this.setState({
          distributionbond: newDistributeKeys,
        });
      },
    );
  };

  handleChangeFieldname = ({ key }) => {
    this.transferFieldName(key);
  };

  handleChangeComment = ({ key }) => {
    if (key === 'clearComment') {
      // * 清空全部注释
      const { newDataSource } = this.state;

      const tempDataSource = newDataSource.map((v) => {
        this.props.form.setFieldsValue({
          [`list[${v.id}].describe`]: undefined,
        });
        return {
          ...v,
          describe: undefined,
        };
      });

      this.setState({
        newDataSource: tempDataSource,
      });
    }
    // * 加载源注释
    if (key === 'toOriginComment') {
      const { newDataSource } = this.state;
      const {
        currentSource: { sourcetype },
      } = this.state;
      const {
        tabKey,
        processData: { extractData },
      } = this.props;
      const extract = extractData.find((v) => v.key === tabKey);
      const newFieldSource = this.getOriginDataSource(extract, sourcetype);
      const tempDataSource = newDataSource.map((v, idx) => {
        // * 一刀切  顺序展示
        const targetDescription = newFieldSource[idx].origindescribe;

        //* 挨个设置值
        this.props.form.setFieldsValue({
          [`list[${v.id}].describe`]: targetDescription,
        });

        return {
          ...v,
          describe: targetDescription,
        };
      });

      this.setState({
        newDataSource: tempDataSource,
      });
    }
    if (key === 'toField') {
      // * 加载字段名当作注释
      const { newDataSource } = this.state;
      const tempDataSource = newDataSource.map((v) => {
        const { name } = v;
        this.props.form.setFieldsValue({
          [`list[${v.id}].describe`]: name,
        });

        return {
          ...v,
          describe: name,
        };
      });

      this.setState({
        newDataSource: tempDataSource,
      });
    }
  };

  fieldnameMenu = () => {
    const {
      currentSource: { sourcetype },
    } = this.state;
    return (
      <Menu onClick={this.handleChangeFieldname}>
        <Menu.Item key="pinyin" title="转拼音首字母">
          转拼音首字母
        </Menu.Item>
        {isSupportCapital(sourcetype) && (
          <Menu.Item key="upper" title="转大写">
            转大写
          </Menu.Item>
        )}
        <Menu.Item key="lower" title="转小写">
          转小写
        </Menu.Item>
      </Menu>
    );
  };

  commentMenu = () => {
    return (
      <Menu onClick={this.handleChangeComment}>
        <Menu.Item key="toField" title="加载字段名">
          加载字段名
        </Menu.Item>
        <Menu.Item key="toOriginComment" title="加载源注释">
          加载源注释
        </Menu.Item>
        <Menu.Item key="clearComment" title="清空全部注释">
          清空全部注释
        </Menu.Item>
      </Menu>
    );
  };

  //* 切换 不分区/分区
  handleChangePartition = (e) => {
    const { value } = e.target;
    if (value) {
      this.partitionRef?.current?.showPartitionModal();
    } else {
      this.partitionRef?.current?.handleCancel();
    }
  };

  //* 重新编辑分区
  handleEditPartition = () => {
    this.partitionRef?.current?.showPartitionModal();
  };

  //* 点击分区弹窗的确定后，要更新storeData数据
  updateStoreData = (partition) => {
    // 存在state里
    this.setState({ partitionField: partition });
  };

  //* 校验所有的数据
  // eslint-disable-next-line
  validateAllFields = (data, sourceType) => {
    const listNameArr = data.map((v) => {
      const obj = {};
      obj[`list[${v.id}].name`] = v.name;
      obj[`list[${v.id}].describe`] = v.describe;
      return obj;
    });
    // 所有待校验的数据
    const validateData = listNameArr.reduce(
      (pre, cur) => ({ ...pre, ...cur }),
      0,
    );
    const { partitionField } = this.state;
    const systemFieldsArr = getSystemFields(partitionField).map(
      (item) => item.name,
    );
    //* 校验规则
    const allRules = data.reduce((cur, pre) => {
      const existedFields = data.reduce((res, curr, index) => {
        if (index === pre.id) return res;
        res.push(curr.name);
        return res;
      }, []);
      cur[`list[${pre.id}].name`] = [
        ...getStandardRules(
          '请填写字段',
          ['', existedFields.concat(systemFieldsArr)],
          'tableField',
          sourceType,
          'field',
        ),
      ];
      cur[`list[${pre.id}].describe`] = getStandardRules('', [], 'comment');
      return cur;
    }, {});

    const validator = new Schema(allRules);

    return new Promise((resolve) => {
      validator
        .validate(validateData)
        .then(() => resolve('success'))
        .catch(({ fields }) => {
          const errorFields = Object.keys(fields);
          const firstErrorField = errorFields[0];
          const index = Number(strfindIndex(firstErrorField));
          this.positonTableError(index);

          resolve('error');
        });
    });
  };

  onScroll = _.debounce(() => {
    const { newDataSource } = this.state;
    const formTableValue = newDataSource;
    // const formTableValue = this.props.form.getFieldsValue().list;
    if (!formTableValue) return;

    const validateData = formTableValue
      .map((v, i) => ({ ...v, id: i }))
      .filter((item) => item);

    const fieldArr = _.flatten(
      [...validateData].map((v) => [
        `list[${v.id}].name`,
        `list[${v.id}].describe`,
      ]),
    );
    this.props.form.validateFields(fieldArr);
  }, 300);

  render() {
    const {
      radio,
      treeData,
      tbNameArr,
      newDataSource,
      haveDataSource,
      dataLength,
      storeLength,
      selectedKeys,
      // 已有表的表格选择
      selectedRows,
      selectedRowKeys,
      // 当前数据源
      currentLayer,
      currentSource,
      currentTable,
      orientation,
      fileformat,
      description,
      distributionmode,
      distributionbond,
      partitionField,
      // 已有表分区字段(从元数据那边继承而来)
      havePartitionField,
    } = this.state;
    const { sourcetype } = currentSource;
    const { layerid } = currentLayer;
    const { tablename } = currentTable;
    const color = dataLength === storeLength ? '#1980ff' : '#ea1e1e';

    const {
      form,
      tabKey,
      isEdit,
      tableLoading,
      sourceLoading,
      fieldTransLoading,
      tableInfoLoading,
      testTbaleNmaeLoading,
      distributionLoading,
      processData: { storeData },
      singleAndTdc: { tableTaglist, layerlist, cataloglist, engineList },
    } = this.props;
    const { getFieldDecorator } = form;
    const store = storeData.find((v) => v.key === tabKey);
    const catalogTree = layerid ? getCatalist(cataloglist, layerid) : [];
    const tagTree = listToTree(tableTaglist, 'tag')[0]?.children || [];
    const warpSpinning = !!(sourceLoading || tableLoading);
    const formSpring = !!(testTbaleNmaeLoading || distributionLoading);
    // 表格配置项
    const rowSelection = {
      selectedRows,
      selectedRowKeys,
      onChange: this.onSelectChange,
    };

    const binaryField = this.constructor.getBinaryFieldOfSource(sourcetype);
    return (
      <div className={styles.storeSource}>
        <Spin spinning={warpSpinning}>
          <div className={styles.storeHeader}>
            <Radio.Group value={radio} onChange={this.onRadioChange}>
              <Radio.Button value="have">已有表</Radio.Button>
              <Radio.Button value="new" disabled={isEdit}>
                新建表
              </Radio.Button>
            </Radio.Group>
            <span style={{ padding: '0 16px' }}>
              数据源字段数：
              <span style={{ color: '#1980ff' }}>{dataLength}</span>
            </span>
            <span style={{ paddingRight: 16 }}>
              存储源字段数：
              <span style={{ color }}>{storeLength}</span>
            </span>
            <Tooltip title="数据源和存储源字段数必须一致，否则无法下一步!">
              <IconFont type="icon-question-circle" />
            </Tooltip>
          </div>
          <div className={styles.storeContent}>
            <div className={styles.treeWarp}>
              {radio === 'new' ? (
                <div className={styles.stroeForm}>
                  <Spin spinning={formSpring}>
                    <Form {...FORM_LAYOYT} ref={this.storeFormRef}>
                      <Form.Item label="存储源">
                        {getFieldDecorator('sourceid', {
                          initialValue: store
                            ? store.values.sourceid
                            : undefined,
                          rules: [{ required: true, message: '请选择存储源' }],
                        })(
                          <SearchTreeSelect
                            // * 支持isgather为true的所有存储源
                            treeData={getStoreDatabase(layerlist, engineList)}
                            onChange={this.onDbnameChange}
                            placeholder="请选择存储源"
                            expandOnClick
                            showSearch
                            treeIcon
                          />,
                        )}
                      </Form.Item>
                      <Form.Item label="所属类目">
                        {getFieldDecorator('catalogid', {
                          initialValue: store
                            ? store.values.catalogid
                            : undefined,
                        })(
                          <SearchTreeSelect
                            placeholder="请选择类目"
                            disabled={!layerid}
                            treeData={catalogTree}
                            expandOnClick
                            showSearch
                            allowClear
                            treeIcon
                          />,
                        )}
                      </Form.Item>
                      <Form.Item label="表名称">
                        {getFieldDecorator('tbname', {
                          initialValue: tablename || undefined,
                          rules: getStandardRules(
                            '请输入表名称',
                            ['', tbNameArr],
                            'tableField',
                            sourcetype,
                          ),
                        })(
                          <Input
                            placeholder="请输入表名称"
                            onChange={this.onTbnameChange}
                          />,
                        )}
                      </Form.Item>

                      {SUPPORT_PARTITION_DB_TYPE.includes(sourcetype) && (
                        <>
                          <Row
                            style={{
                              marginBottom: 12,
                              fontSize: 12,
                              lineHeight: `28px`,
                            }}
                          >
                            <Col span={8} style={{ textAlign: 'right' }}>
                              表分区：
                            </Col>
                            <Col span={16}>
                              <RadioButton
                                options={[
                                  { label: '不分区', value: false },
                                  { label: '分区', value: true },
                                ]}
                                buttonWidth={60}
                                value={!!(partitionField.length !== 0)}
                                onChange={this.handleChangePartition}
                              />
                            </Col>
                          </Row>
                          <PartitionModal
                            ref={this.partitionRef}
                            dataSource={newDataSource}
                            partitionField={
                              partitionField || store?.partitionField
                            }
                            sourceType={sourcetype}
                            updateStoreData={this.updateStoreData}
                          />
                          {partitionField.length !== 0 && (
                            <TextButton
                              onClick={this.handleEditPartition}
                              style={{
                                display: 'block',
                                margin: '0 0 8px 75px',
                              }}
                            >
                              编辑分区
                            </TextButton>
                          )}
                        </>
                      )}

                      {DISTRIBUTION_DBTYPES.includes(sourcetype) && (
                        <Form.Item label="分布方式">
                          {getFieldDecorator('distributionmode', {
                            initialValue: store
                              ? store.values.distributionmode
                              : distributionmode,
                          })(
                            sourcetype === 'uxdb' ? (
                              <span>HASH</span>
                            ) : (
                              <Select
                                placeholder="请选择分布方式"
                                onChange={this.distributionChange}
                                style={{ width: 'calc(100% - 28px)' }}
                              >
                                <Option value="HASH">HASH</Option>
                                <Option value="REPLICATED">REPLICATION</Option>
                                {!NOT_SUPPORT_RANDOMLY.includes(sourcetype) && (
                                  <Option
                                    value="RANDOMLY"
                                    disabled={newDataSource.some((v) => v.pKey)}
                                  >
                                    {newDataSource.some((v) => v.pKey) ? (
                                      <Tooltip title="该表存在主键，不支持RANDOMLY分布">
                                        RANDOMLY
                                      </Tooltip>
                                    ) : (
                                      'RANDOMLY'
                                    )}
                                  </Option>
                                )}
                              </Select>
                            ),
                          )}
                          {sourcetype !== 'uxdb' && (
                            <Tooltip
                              overlayStyle={{ maxWidth: '437px' }}
                              title={DISTRIBUTION_MODE_TIP}
                            >
                              <IconFont
                                type="icon-question-circle"
                                style={{ marginLeft: 8 }}
                              />
                            </Tooltip>
                          )}
                        </Form.Item>
                      )}
                      {DISTRIBUTION_DBTYPES.includes(sourcetype) &&
                        (distributionmode === 'HASH' ||
                          distributionmode === 'APPEND') && (
                          <Form.Item label="分布键">
                            {getFieldDecorator('distributionbond', {
                              initialValue: store
                                ? distributionbond.length
                                  ? distributionbond
                                  : store.values.distributionbond
                                : distributionbond,
                              rules: [
                                { required: true, message: '分布键不能为空' },
                              ],
                            })(
                              <CustomSelect
                                showSearch
                                mode={
                                  sourcetype === 'uxdb' ? undefined : 'multiple'
                                }
                                placeholder="请选择分布键"
                                optionLabelProp="value"
                                menuItemSelectedIcon={null}
                                style={{ width: 'calc(100% - 28px)' }}
                                onChange={this.distributionbondChange}
                                optionList={newDataSource.map((item) => {
                                  return {
                                    value: item.name,
                                    disabled:
                                      binaryField === item.type ||
                                      (sourcetype === 'dameng' &&
                                        item.type === 'text'),
                                    isPrim: item.pKey,
                                  };
                                })}
                              />,
                            )}
                            <Tooltip
                              overlayStyle={{ maxWidth: '376px' }}
                              title={DISTRIBUTION_KEY_TIP}
                            >
                              <IconFont
                                type="icon-question-circle"
                                style={{ marginLeft: 8 }}
                              />
                            </Tooltip>
                          </Form.Item>
                        )}
                      {/* 存储方式选择 */}
                      {STORE_WAY_TYPE.includes(sourcetype) && (
                        <>
                          <Form.Item label="存储方式" required>
                            {getFieldDecorator('orientation', {
                              initialValue: store
                                ? store.values.orientation || orientation
                                : orientation,
                            })(
                              <Select
                                onChange={this.orientationChange}
                                style={{ width: 'calc(100% - 28px)' }}
                              >
                                <Option value="column">列存</Option>
                                <Option value="row">行存</Option>
                              </Select>,
                            )}
                            <Tooltip
                              overlayStyle={{ maxWidth: '376px' }}
                              title={ORIENTATION_TIP}
                            >
                              <IconFont
                                type="icon-question-circle"
                                style={{ marginLeft: 8 }}
                              />
                            </Tooltip>
                          </Form.Item>
                          {orientation === 'column' && (
                            <Form.Item label="压缩方式">
                              {getFieldDecorator('compresstype', {
                                initialValue: 'zlib',
                              })(<span>zlib</span>)}
                            </Form.Item>
                          )}
                        </>
                      )}

                      {/* 文件格式选择 */}
                      {SUPPORT_FILE_FORMAT.includes(sourcetype) && (
                        <Form.Item label="文件格式">
                          {getFieldDecorator('fileformat', {
                            initialValue: store
                              ? store.values.fileformat || fileformat
                              : fileformat,
                            rules: [
                              { required: true, message: '请选择文件格式' },
                            ],
                          })(
                            <Select
                              placeholder="请选择文件格式"
                              onChange={this.filetypeChange}
                            >
                              <Option value="Orcfile">Orcfile</Option>
                              <Option value="Textfile">Textfile</Option>
                            </Select>,
                          )}
                        </Form.Item>
                      )}
                      <Form.Item label="标签">
                        {getFieldDecorator('tags', {
                          initialValue: store ? store.values.tags : undefined,
                        })(
                          <ColorTagTreeSelect
                            placeholder="请选择标签"
                            tagList={tagTree}
                          />,
                        )}
                      </Form.Item>
                      <Form.Item label="描述">
                        {getFieldDecorator('describe', {
                          initialValue: store
                            ? store.values.describe
                            : description,
                          rules: getStandardRules(
                            '',
                            [],
                            'comment',
                            sourcetype,
                          ),
                        })(
                          <Input.TextArea
                            allowClear
                            placeholder="请输入描述信息"
                            autoSize={{ minRows: 4, maxRows: 6 }}
                          />,
                        )}
                      </Form.Item>
                    </Form>
                  </Spin>
                </div>
              ) : (
                <SearchTree
                  isVirtual
                  onSelect={this.onSelectTree}
                  selectedKeys={selectedKeys}
                  treeData={treeData}
                  dirLoading={this.state.treeLoading}
                />
              )}
            </div>
            <div className={styles.tableWarp}>
              {radio === 'new' ? (
                <>
                  <div ref={this.tableRef} className={styles.newTable}>
                    <VirtualTable
                      pagination={false}
                      loading={!!fieldTransLoading}
                      columns={this.getNewColumns()}
                      dataSource={newDataSource.map((item) => ({
                        ...item,
                        key: item.id,
                      }))}
                      onScroll={this.onScroll}
                      scroll={{ y: 'calc(100vh - 345px)' }}
                    />
                  </div>

                  <div className={styles.tableBtn}>
                    <Button type="primary" onClick={this.addField}>
                      <IconFont type="icon-plus" text="添加字段" />
                    </Button>
                    <div>
                      <Button onClick={() => this.getFieldTrans('origin')}>
                        加载源字段
                      </Button>
                      <Button
                        onClick={this.handleClear}
                        style={{ marginLeft: 8 }}
                        type="primary"
                      >
                        清空
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className={styles.haveData}>
                  <VirtualTable
                    pagination={false}
                    columns={columns}
                    loading={!!tableInfoLoading}
                    dataSource={haveDataSource}
                    scroll={{ y: 'calc(100vh - 300px)' }}
                    rowSelection={rowSelection}
                    rowClassName={(record) => {
                      const data = havePartitionField?.partitionField?.filter(
                        (item) => item.ispreset !== HAVE_FIELD_IDENTIFY,
                      );
                      const target = data.find(
                        (item) => item.name === record.field,
                      );
                      return target ? 'hideCheckBox' : '';
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </Spin>
      </div>
    );
  }
}

export default connect(({ user, processData, singleAndTdc, loading }) => ({
  user,
  processData,
  singleAndTdc,
  sourceLoading: loading.effects['singleAndTdc/listHaveStoreInfo'],
  tableLoading: loading.effects['singleAndTdc/listStoreTable'],
  testTbaleNmaeLoading: loading.effects['singleAndTdc/getCurrentTables'],
  tableInfoLoading: loading.effects['singleAndTdc/listStoreTableDetail'],
  fieldTransLoading: loading.effects['singleAndTdc/listFieldTrans'],
  distributionLoading: loading.effects['singleAndTdc/getDistributionKey'],
}))(StoreSource);

const { getBinaryFieldOfSource } = StoreSource;
export { getBinaryFieldOfSource };
