import { Form, Tag } from 'antd';
import { connect } from 'dva';
import CS from 'classnames';
import { FormBundle } from '@dana/ui';
import { ConnectState } from '@/models/connect.d';
import _ from 'lodash';
import styles from '../Batch/batch.less';
import { TERYX, HIVE } from '../config';
import { BatchDetail } from '../index.d';

export interface SourceInfoType {
  globalSourcedb: BatchDetail['globalsourcedb'];
  policyType: number;
  prefix: string;
  suffix: string;
  submitInfo: BatchDetail['globalsubmitinfo'];
}

const SourceOverview = (props: SourceInfoType & StateToProps) => {
  const {
    globalSourcedb: {
      layername: layerName,
      schema: sourceSchema,
      filetype: fileType,
      catalogid,
      taglist,
      compresstype: compressType,
      odsenginename: storeType,
      partitionfield,
    },
    policyType,
    prefix,
    suffix,
    submitInfo: { extractonlinestrategy, conditioncover },
  } = props;
  const { plainCatalogList, plainTagList } = props;

  function getContent(type, pre, suf) {
    switch (type) {
      case 0:
        return <span>使用源表名</span>;
      case 1:
        return (
          <div>
            <span>表存在则加前缀或后缀</span>
            <span style={{ marginLeft: '16px' }}>
              前缀:
              {pre}
            </span>
            <span style={{ marginLeft: '16px' }}>
              后缀:
              {suf}
            </span>
          </div>
        );
      case 2:
        return (
          <div>
            <span>所有表名加前缀或后缀</span>
            <span style={{ marginLeft: '16px' }}>
              前缀:
              {pre}
            </span>
            <span style={{ marginLeft: '16px' }}>
              后缀:
              {suf}
            </span>
          </div>
        );
      default:
        break;
    }
  }

  function getCatalog() {
    let catalogName = '--';
    plainCatalogList.forEach((item) => {
      if (item.id === catalogid) {
        catalogName = item.name;
      }
    });

    const isDel = catalogid && catalogName === '--';
    return (
      <>
        <span>{catalogName}</span>
        {isDel && <span style={{ color: '#fa1e1e' }}>（已删除）</span>}
      </>
    );
  }
  const policyContent = getContent(policyType, prefix, suffix);

  const hasPartition = !!partitionfield && partitionfield.length > 0;

  const allTagIdList = plainTagList.map((v) => v.id);
  const curTagList = taglist.filter((v) => allTagIdList.includes(v.dirid));

  const getPartitionValue = () => {
    let res = [];
    if (hasPartition) {
      res = partitionfield.map((item) => {
        const { name, ispreset } = item;
        const data = {
          pattern: 'system',
          type: ispreset,
          field: name,
        };
        return data;
      });
    }

    return res;
  };
  const viewsInfo = { hivePartition: getPartitionValue() };

  return (
    <>
      <div
        className={CS({
          [styles.info]: true,
          [styles.storeSource]: true,
          [styles.storeSourceView]: true,
        })}
      >
        <Form.Item label="存储源">
          <span>
            {layerName}/{sourceSchema}
          </span>
        </Form.Item>
        <Form.Item label="所属类目">
          <span>{getCatalog()}</span>
        </Form.Item>
        <Form.Item label="数据策略">
          <span>{extractonlinestrategy === 0 ? '覆盖' : '追加'}</span>
        </Form.Item>

        {extractonlinestrategy === 0 && (
          <div className={styles.strategyViewWrapper}>
            <Form.Item label="覆盖策略">
              <span>
                {!_.isEmpty(conditioncover) ? '条件覆盖' : '全表覆盖'}
              </span>
            </Form.Item>
            {!_.isEmpty(conditioncover) && (
              <Form.Item label=" " colon={false}>
                <span>{conditioncover}</span>
              </Form.Item>
            )}
          </div>
        )}

        <Form.Item label="表标签">
          {curTagList.length ? (
            curTagList.map((item) => {
              return (
                <Tag key={item.name} closable={false} color={item.color}>
                  {item.name}
                </Tag>
              );
            })
          ) : (
            <span>--</span>
          )}
        </Form.Item>
        {TERYX.includes(storeType) && (
          <Form.Item label="压缩格式">
            <span>{compressType || '--'}</span>
          </Form.Item>
        )}

        {fileType && (
          <Form.Item label="文件格式">
            <span>{fileType === 'orc' ? 'Orcfile' : 'Textfile'}</span>
          </Form.Item>
        )}
        {HIVE.includes(storeType) && (
          <>
            <Form.Item label="默认表分区">
              <span>{hasPartition ? '系统预置分区' : '不分区'}</span>
            </Form.Item>
            {hasPartition && (
              <FormBundle<'hivePartitionSystem'>
                status="view"
                viewInfos={viewsInfo}
                scene="hivePartitionSystem"
              />
            )}
          </>
        )}
      </div>
      <Form.Item
        label="目标表策略"
        style={{ width: '50%', paddingRight: '48px' }}
      >
        <span>{policyContent}</span>
      </Form.Item>
    </>
  );
};

const SourceOverviewStateToProps = ({
  batchNew: { plainCatalogList, plainTagList },
}: ConnectState) => {
  return { plainCatalogList, plainTagList };
};
type StateToProps = ReturnType<typeof SourceOverviewStateToProps>;

export default connect<StateToProps, {}, {}, ConnectState>(
  SourceOverviewStateToProps,
)(SourceOverview);
