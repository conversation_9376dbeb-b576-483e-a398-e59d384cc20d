import {
  useState,
  useMemo,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useRef,
} from 'react';
import { Form, Radio, Space, Input, Tooltip, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form';
import CS from 'classnames';
import { useModel } from 'umi';
import { connect } from 'dva';
import { FormBundle } from '@dana/ui';
import _ from 'lodash';
import {
  SearchTreeSelect,
  RadioButton,
  ColorTagTreeSelect,
  DatabaseIcon,
  IconFont,
} from '@/components';
import { REGEXP } from '@/utils';
import { ConnectState } from '@/models/connect.d';
import { SUPPORT_CAPITAL_DBTYPE as CAPITAL_DB } from '@/pages/DataCollect/OffLine/conf';
import { HIVE, TERYX } from '../config';
import { AutoExpand, GetValidatePath, SingleCustom } from '../index';
import { BatchDetail } from '../index.d';

import styles from './batch.less';

interface Store {
  form: FormInstance;
  autoExpandNext: AutoExpand;
  getValidatePath: GetValidatePath;
  overviewData: BatchDetail;
  isEditing: boolean;
  // singlesPayload: SingleCustom;
  setSinglesPayload: (singles: SingleCustom) => void;
}

type Dispatch = ({ type, payload }: { type: string; payload?: {} }) => void;

const StoreSource = forwardRef(
  (props: Store & StateToProps & { dispatch: Dispatch }, ref) => {
    const { getStoreTbList, hasNoteFenQu, setHasNoteFenQu } = useModel(
      'DataCollect.OffLine.batchCollect',
    );
    const {
      form,
      autoExpandNext,
      getValidatePath,
      overviewData,
      // singlesPayload,
      setSinglesPayload,
      isEditing,
    } = props;
    const { layerList, catalogList, tableTag, singleSetRefresh, dispatch } =
      props;
    const [sourceType, setSourceType] = useState('');
    const [sourceLayerId, setSourceLayerId] = useState('');
    const [policyType, setPolicyType] = useState(0);
    const [storeMode, setStoreMode] = useState('column');
    const [partition, setPartition] = useState(0);

    const [strategy, setStrategy] = useState(0); // 数据策略
    const [coverStrategy, setCoverStrategy] = useState('whole'); // 覆盖策略，默认全表覆盖

    const sourceIdCache = useRef();
    const preFenQu = overviewData?.globalsourcedb?.partitionfield || [];

    useEffect(() => {
      setPolicyType(overviewData.policytype);
      setSourceLayerId(overviewData.globalsourcedb.layerid);
      setPartition(
        overviewData?.globalsourcedb?.partitionfield?.length > 0 ? 1 : 0,
      );

      setStrategy(overviewData?.globalsourcedb?.strategy);
      setCoverStrategy(
        !overviewData?.globalsourcedb?.strategy &&
          !_.isEmpty(overviewData?.globalsourcedb?.conditioncover)
          ? 'partial'
          : 'whole',
      );
      form.setFieldsValue({
        store: {
          coverStrategy:
            !overviewData?.globalsourcedb?.strategy &&
            !_.isEmpty(overviewData?.globalsourcedb?.conditioncover)
              ? 'partial'
              : 'whole',
          sql: overviewData?.globalsourcedb?.conditioncover,
        },
      });
    }, [overviewData]);

    useEffect(() => {
      form.validateFields([
        ['store', 'existPrefix'],
        ['store', 'allPrefix'],
        ['store', 'existSuffix'],
        ['store', 'allSuffix'],
      ]);
    }, [sourceType]);
    const sourceDb = useMemo(() => {
      return layerList.map((item) => {
        const newChildren = item.children.map((v) => {
          return {
            title: v.title,
            value: v.value,
            icon: <IconFont type="icon-database" />,
          };
        });
        return {
          title: item.title,
          value: item.value,
          icon: <DatabaseIcon type={item.engineName as any} />,
          children: newChildren,
        };
      });
    }, [layerList]);
    const isCAPITAL = useMemo(() => {
      return CAPITAL_DB.includes(sourceType);
    }, [sourceType]);

    const sourceCatalog = useMemo(() => {
      const cataLog =
        catalogList.find((item) => item.id === sourceLayerId) || catalogList;
      return Array.isArray(cataLog) ? cataLog : [cataLog];
    }, [catalogList, sourceLayerId]);

    const changeStoreMode = (e) => {
      setStoreMode(e.target.value);
    };

    useImperativeHandle(
      ref,
      () => {
        return { sourceType };
      },
      [sourceType],
    );

    useEffect(() => {
      if (overviewData.globalsourcedb) {
        setSourceType(overviewData.globalsourcedb?.odsenginename);
        setStoreMode(overviewData.globalsourcedb.orientation || 'column');
      }
    }, [overviewData]);

    // 2024/3/25 切换存储源时清空自定义配置
    const changeStore = (v) => {
      getStoreTbList({ sourceid: v });
      autoExpandNext(getValidatePath('store'), [
        'basic',
        'extract',
        'store',
        'config',
        'singleSet',
      ]);
      let curType = '';
      let curLayerId = '';
      layerList.forEach((layer) => {
        if (layer.children) {
          layer.children.forEach((db) => {
            if (db.value === v) {
              curType = layer.engineName;
              curLayerId = layer.value;
            }
          });
        }
      });
      if (v !== sourceIdCache.current) {
        // const payloadV = Object.values(singlesPayload);
        // const noChangeData = payloadV.filter(
        //   (item) => !item.sourceinfo?.tableid,
        // );
        // const newPayload = {};
        // noChangeData.forEach((data) => {
        //   const key = data.extracttable;
        //   newPayload[key] = data;
        // });
        // console.log('newPayload', newPayload);
        setSinglesPayload({});
        // setSinglesPayload(newPayload);
      }
      form.setFieldsValue({ store: { catalogid: undefined } });
      setSourceType(curType);
      setSourceLayerId(curLayerId);
      sourceIdCache.current = v;
    };

    const changePrefix = () => {
      dispatch({
        type: 'batchNew/save',
        payload: {
          singleSetRefresh: !singleSetRefresh,
        },
      });
    };

    const handleChangeDefaultPartition = (e) => {
      const { value } = e.target;
      setPartition(value);
      if (value === 0) {
        form.setFieldsValue({
          hivePartition: undefined,
        });
      } else {
        form.setFieldsValue({
          hivePartition: [
            {
              type: 'day',
              field: 'ds_day',
            },
          ],
        });
      }
      if (
        ((preFenQu.length > 0 && value === 0) ||
          (preFenQu.length === 0 && value === 1)) &&
        !hasNoteFenQu &&
        isEditing
      ) {
        Modal.confirm({
          title: '分区提醒',
          content: (
            <p>
              修改默认表分区后，已有表会删除重建，同时数据会清空，请谨慎修改！个性化设置已经单独修改保存过的子任务的已有表不受影响。
            </p>
          ),
          onOk: () => {
            setHasNoteFenQu(true);
          },
          onCancel: () => {
            const newPartition = preFenQu.length > 0 ? 1 : 0;
            setPartition(newPartition);
            form.setFieldsValue({
              store: {
                defaultPartition: newPartition,
              },
              hivePartition:
                preFenQu.length > 0
                  ? preFenQu.map((v) => ({
                      pattern: 'system',
                      type: v.ispreset,
                      field: v.name,
                    }))
                  : undefined,
            });
          },
          cancelText: '取消',
          okText: '确定',
        });
      }
    };
    const handleChangePartition = () => {
      if (!hasNoteFenQu && isEditing) {
        Modal.confirm({
          title: '分区提醒',
          content: (
            <p>
              修改默认表分区后，已有表会删除重建，同时数据会清空，请谨慎修改！个性化设置已经单独修改保存过的子任务的已有表不受影响。
            </p>
          ),
          onOk: () => {
            setHasNoteFenQu(true);
          },
          onCancel: () => {
            form.setFieldsValue({
              hivePartition:
                preFenQu.length > 0
                  ? preFenQu.map((v) => ({
                      pattern: 'system',
                      type: v.ispreset,
                      field: v.name,
                    }))
                  : undefined,
            });
          },
          cancelText: '取消',
          okText: '确定',
        });
      }
    };
    return (
      <>
        <div
          className={CS({
            [styles.info]: true,
            [styles.storeSource]: true,
          })}
        >
          <Form.Item label="存储源" required>
            <Form.Item
              noStyle
              name={['store', 'sourceName']}
              rules={[{ required: true, message: '请选择或搜索存储源' }]}
            >
              <SearchTreeSelect
                treeIcon
                showSearch
                expandOnClick
                treeData={sourceDb}
                showEmptyInner
                placeholder="请选择或搜索存储源"
                onChange={changeStore}
              />
            </Form.Item>

            <Tooltip
              overlayStyle={{ maxWidth: '370px' }}
              title={
                '若数据源中表描述及字段注释中含有部分特殊字符（单引号“’”、分号“;”、双短横线“--”、换行符“\\n或\\n\\r”、反引号“`”、反斜杠“\\”)且存储源中不存在同名表时，上述特殊字符将会在存储源被转换为空格存储，以防止存储源建表错误。'
              }
            >
              <IconFont
                type="icon-question-circle"
                style={{
                  position: 'absolute',
                  marginLeft: 8,
                }}
              />
            </Tooltip>
          </Form.Item>
          <Form.Item label="所属类目" name={['store', 'catalogid']}>
            <SearchTreeSelect
              treeIcon
              showSearch
              expandOnClick
              treeData={sourceCatalog}
              showEmptyInner
              placeholder="请选择或搜索类目信息"
            />
          </Form.Item>
          <Form.Item label="数据策略">
            <Form.Item noStyle name={['store', 'strategy']}>
              <RadioButton
                options={[
                  { label: '覆盖', value: 0 },
                  { label: '追加', value: 1 },
                ]}
                onChange={(e) => {
                  setStrategy(e.target.value);
                  setCoverStrategy('whole');
                  form.setFieldsValue({
                    store: {
                      coverStrategy: 'whole',
                      sql: undefined,
                    },
                  });
                }}
              />
            </Form.Item>
            <Tooltip
              title={
                <>
                  <div>覆盖：先清空存储表中数据</div>
                  <div style={{ marginLeft: 36 }}>再将采集的数据插入存储表</div>
                  <div>追加：将采集的数据追加至存储表</div>
                </>
              }
            >
              <IconFont
                type="icon-question-circle"
                style={{
                  position: 'absolute',
                  marginLeft: 8,
                }}
              />
            </Tooltip>
          </Form.Item>

          {strategy === 0 && (
            <div className={styles.coverStartegyWrapper}>
              <Form.Item label="覆盖策略">
                <Form.Item name={['store', 'coverStrategy']} noStyle>
                  <RadioButton
                    options={[
                      { label: '全表覆盖', value: 'whole' },
                      { label: '条件覆盖', value: 'partial' },
                    ]}
                    onChange={(e) => {
                      setCoverStrategy(e.target.value);
                      form.setFieldsValue({
                        store: {
                          sql: undefined,
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Form.Item>
              {coverStrategy === 'partial' && (
                <Form.Item
                  label=" "
                  colon={false}
                  className={styles.tipIconBox}
                >
                  <Form.Item
                    name={['store', 'sql']}
                    requiredMark
                    rules={[{ required: true, message: '请输入条件' }]}
                    noStyle
                  >
                    <Input
                      style={{ width: 210 }}
                      addonBefore="where"
                      placeholder="请输入条件"
                    />
                  </Form.Item>
                  <Tooltip
                    placement="right"
                    title={
                      <>
                        <div>
                          1.
                          请输入标准SQL中where关键字后的筛选条件(不包括where)，支持使用全局参数
                        </div>
                        <div>2. Hive仅支持指定分区覆盖</div>
                      </>
                    }
                  >
                    <IconFont
                      type="icon-question-circle"
                      style={{
                        width: 16,
                        left: 220,
                        position: 'absolute',
                        top: 2,
                      }}
                    />
                  </Tooltip>
                </Form.Item>
              )}
            </div>
          )}
          <Form.Item label="表标签" name={['store', 'tags']}>
            <ColorTagTreeSelect
              placeholder="请选择或搜索表标签"
              tagList={tableTag}
            />
          </Form.Item>
          {TERYX.includes(sourceType) && (
            <>
              <Form.Item label="存储方式">
                <Form.Item noStyle name={['store', 'mode']}>
                  <RadioButton
                    options={[
                      { label: '行存', value: 'row' },
                      { label: '列存', value: 'column' },
                    ]}
                    onChange={changeStoreMode}
                  />
                </Form.Item>
                <Tooltip
                  title={
                    <div>
                      行存储适用于OLTP任务类的表，对更新删除较方便
                      但占用存储空间大；列存适合OLAP任务类的表，支
                      持压缩存储，对数据分析、计算、聚合、多表JOIN
                      等很友好，但不支持UPDATE、Delete操作。
                    </div>
                  }
                >
                  <IconFont
                    type="icon-question-circle"
                    style={{
                      position: 'absolute',
                      marginLeft: 8,
                    }}
                  />
                </Tooltip>
              </Form.Item>
              {storeMode === 'column' && (
                <Form.Item label="压缩格式">
                  <span>zlib</span>
                </Form.Item>
              )}
            </>
          )}
          {HIVE.includes(sourceType) && (
            <Form.Item
              label="文件格式"
              name={['store', 'fileType']}
              style={{ marginBottom: '12px' }}
            >
              <RadioButton
                options={[
                  { label: 'Orcfile', value: 'orc' },
                  { label: 'Textfile', value: 'text' },
                ]}
              />
            </Form.Item>
          )}
          {HIVE.includes(sourceType) && (
            <>
              <Form.Item label="默认表分区">
                <Form.Item name={['store', 'defaultPartition']} noStyle>
                  <RadioButton
                    options={[
                      { label: '不分区', value: 0 },
                      { label: '系统预置分区', value: 1 },
                    ]}
                    onChange={handleChangeDefaultPartition}
                  />
                </Form.Item>
                <Tooltip
                  // overlayStyle={{ maxWidth: '370px' }}
                  title={
                    <>
                      <div>
                        1、系统预置分区：系统默认给存储表增加分区字段，其值为周期任务每次调度的起始时间或实时任务监测源端数据变化的时间
                      </div>
                      <div>
                        2、未在个性化设置修改过分区配置的表适用默认分区规则，已经单独修改过分区规则的表始终以个性化设置为准
                      </div>
                    </>
                  }
                >
                  <IconFont
                    type="icon-question-circle"
                    style={{
                      position: 'absolute',
                      marginLeft: 8,
                    }}
                  />
                </Tooltip>
              </Form.Item>
              {partition === 1 && (
                <Form.Item noStyle>
                  <FormBundle<'hivePartitionSystem'>
                    scene="hivePartitionSystem"
                    onValuesChange={handleChangePartition}
                  />
                </Form.Item>
              )}
            </>
          )}
        </div>
        <div className={styles.namePolicy}>
          <Form.Item
            label="目标表策略"
            name={['store', 'policyType']}
            style={{ width: '50%', paddingRight: '48px' }}
          >
            <Radio.Group
              onChange={(e) => {
                const { value } = e.target;
                setPolicyType(value);
              }}
            >
              <Space direction="vertical">
                <Radio value={0}>使用源表名</Radio>
                <Radio value={1}>
                  <div className={styles.content}>
                    <span>表存在则加前缀或后缀</span>
                    <Form.Item
                      style={{
                        display: policyType === 1 ? 'inline-flex' : 'none',
                      }}
                      label="前缀"
                      name={['store', 'existPrefix']}
                      rules={[
                        {
                          pattern: isCAPITAL
                            ? REGEXP.capitalTbName.pattern
                            : REGEXP.tbName.pattern,
                          message: isCAPITAL
                            ? REGEXP.capitalTbName.message
                            : REGEXP.tbName.message,
                        },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const suffix = getFieldValue([
                              'store',
                              'existSuffix',
                            ]);
                            if (policyType === 1 && !value && !suffix) {
                              return Promise.reject(
                                new Error('前缀和后缀不可同时为空'),
                              );
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                      dependencies={[['store', 'existSuffix']]}
                    >
                      <Input style={{ width: '160px' }} />
                    </Form.Item>
                    <Form.Item
                      style={{
                        display: policyType === 1 ? 'inline-flex' : 'none',
                      }}
                      label="后缀"
                      name={['store', 'existSuffix']}
                      rules={[
                        {
                          pattern: isCAPITAL
                            ? /^[0-9a-zA-Z_]*$/g
                            : /^[0-9a-z_]*$/g,
                          message: isCAPITAL
                            ? '只支持字母、数字、下划线'
                            : '只支持小写字母、数字、下划线',
                        },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const prefix = getFieldValue([
                              'store',
                              'existPrefix',
                            ]);
                            if (policyType === 1 && !value && !prefix) {
                              return Promise.reject(
                                new Error('前缀和后缀不可同时为空'),
                              );
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                      dependencies={[['store', 'existPrefix']]}
                    >
                      <Input style={{ width: '160px' }} />
                    </Form.Item>
                  </div>
                </Radio>
                <Radio value={2}>
                  <div className={styles.content}>
                    <span>所有表名加前缀或后缀</span>
                    <Form.Item
                      style={{
                        display: policyType === 2 ? 'inline-flex' : 'none',
                      }}
                      label="前缀"
                      name={['store', 'allPrefix']}
                      rules={[
                        {
                          pattern: isCAPITAL
                            ? REGEXP.capitalTbName.pattern
                            : REGEXP.tbName.pattern,
                          message: isCAPITAL
                            ? REGEXP.capitalTbName.message
                            : REGEXP.tbName.message,
                        },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const suffix = getFieldValue([
                              'store',
                              'allSuffix',
                            ]);
                            if (policyType === 2 && !value && !suffix) {
                              return Promise.reject(
                                new Error('前缀和后缀不可同时为空'),
                              );
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                      dependencies={[['store', 'allSuffix']]}
                    >
                      <Input
                        style={{ width: '160px' }}
                        onChange={changePrefix}
                      />
                    </Form.Item>
                    <Form.Item
                      style={{
                        display: policyType === 2 ? 'inline-flex' : 'none',
                      }}
                      label="后缀"
                      name={['store', 'allSuffix']}
                      rules={[
                        {
                          pattern: isCAPITAL
                            ? /^[0-9a-zA-Z_]*$/g
                            : /^[0-9a-z_]*$/g,
                          message: isCAPITAL
                            ? '只支持字母、数字、下划线'
                            : '只支持小写字母、数字、下划线',
                        },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const prefix = getFieldValue([
                              'store',
                              'allPrefix',
                            ]);
                            if (policyType === 2 && !value && !prefix) {
                              return Promise.reject(
                                new Error('前缀和后缀不可同时为空'),
                              );
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                      dependencies={[['store', 'allPrefix']]}
                    >
                      <Input
                        style={{ width: '160px' }}
                        onChange={changePrefix}
                      />
                    </Form.Item>
                  </div>
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
        </div>
      </>
    );
  },
);

const StoreStateToProps = ({
  batchNew: { layerList, catalogList, tableTag, singleSetRefresh },
}: ConnectState) => {
  return { layerList, catalogList, tableTag, singleSetRefresh };
};

type StateToProps = ReturnType<typeof StoreStateToProps>;

export default connect<StateToProps, {}, {}, ConnectState>(
  StoreStateToProps,
  null,
  null,
  { forwardRef: true },
)(StoreSource);
