@import (reference) '~@/theme/color.less';

.info {
  display: flex;
  justify-content: space-between;
  flex-flow: row wrap;

  :global {
    .ant-form-item {
      &:last-child {
        margin-bottom: 12px;
      }

      width: 50% !important;
      padding-right: 48px !important;
    }
  }
}

.storeSource {
  :global {
    .dana-form-bundle-item {
      width: 100% !important;
    }
  }

  .coverStartegyWrapper {
    width: 50% !important;

    :global {
      .ant-form-item {
        width: 100% !important;
      }
    }
  }
}

.storeSourceView {
  :global {
    .ant-col-12 {
      .ant-form-item {
        width: 100% !important;
      }
    }
  }

  .strategyViewWrapper {
    width: 50% !important;

    :global {
      .ant-form-item {
        width: 100% !important;
      }
    }
  }
}

.extractConfig {
  :global {
    .contentBlock-block {
      padding: 0 16px;
    }

    .ant-form-item-label {
      max-width: 110px;
    }

    .ant-col-18 {
      max-width: 357px;
    }
  }
}

.summary {
  margin-bottom: 16px;
  float: right;
}

.namePolicy {
  :global {
    .ant-space-item {
      white-space: nowrap;
    }
  }

  .content {
    line-height: 28px;

    & > span,
    & > div {
      margin-right: 24px;
    }

    :global {
      .ant-form-item-explain {
        width: 160px;
        white-space: normal;
      }
    }
  }
}

.treeContainer {
  margin-top: 12px;

  :global {
    .ant-form-item {
      width: 50% !important;
      padding-right: 48px !important;
    }
  }

  .wrapper {
    width: 100%;
    height: 440px;
    display: inline-block;

    &:first-child {
      margin-left: -12px;
    }

    .treeTitle {
      height: 40px;
      line-height: 40px;
      border: 1px solid #e1e1e1;
      border-bottom: 0;

      .checkNode {
        color: #333;
        margin-left: 8px;
      }

      .checkMessage {
        color: #999;
        margin-left: 8px;
      }

      .checkError {
        color: #ea1e1e;
        margin-left: 16px;
      }

      .tip {
        color: #999;
        float: right;
        margin-right: 8px;
      }
    }

    .tree {
      height: 400px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(225, 225, 225, 1);

      :global {
        .ant-tree-iconEle.ant-tree-icon__customize {
          svg {
            height: 28px;
          }
        }
      }

      .selectedTree {
        :global {
          span.ant-tree-switcher {
            flex: 0;
          }

          .ant-tree span.ant-tree-node-content-wrapper span.ant-tree-title {
            overflow: unset;
          }

          .ant-tree-node-content-wrapper {
            padding-left: 8px;

            .ant-tree-iconEle {
              min-width: 58px;
            }
          }
        }
      }
    }
  }
}

.primary {
  color: @blue-6;
}
