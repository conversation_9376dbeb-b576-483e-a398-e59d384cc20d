import { useEffect, useState, useMemo } from 'react';
import { Form, Input, Tooltip, Tag } from 'antd';
import { connect } from 'dva';
import { FormInstance } from 'antd/lib/form';
import { getStandardRules, getNameList } from '@/utils';
import { ConnectState } from '@/models/connect.d';
import { useModel } from 'umi';
import {
  SearchTreeSelect,
  RadioButton,
  ColorTagTreeSelect,
  FetchSelect,
  IconFont,
} from '@/components';
import { SUPPORT_CAPITAL_DBTYPE as CAPITAL_DB } from '@/pages/DataCollect/OffLine/conf';
import otherStyles from '@/pages/DataCollect/OffLine/APICollection/components/Views/index.less';
import {
  SingleExtractTable,
  SingleDetail,
  BatchDetail,
  ExistTableDetail,
} from '../../index.d';
import { HIVE, TERYX } from '../../config';

type OverViewBasic = {
  sourceName: string;
  tableName: string;
  catalogId: string;
  strategy: number;
  tagList: {
    id: string;
    name: string;
    color: string;
  }[];
  des: string;
};

export const strategyTipText = (
  <>
    <div>
      <span style={{ color: '#1980ff' }}>覆盖：</span>
      <span>先清空存储表中数据</span>
    </div>
    <div style={{ marginLeft: 36 }}>再将采集的数据插入存储表</div>
    <div>
      <span style={{ color: '#1980ff' }}>追加：</span>
      <span>将采集的数据追加至存储表</span>
    </div>
  </>
);
const strategyTooltip = (
  <Tooltip title={strategyTipText}>
    <IconFont
      type="icon-question-circle"
      style={{
        marginLeft: 8,
      }}
    />
  </Tooltip>
);

export const historyTipText = (
  <>
    <div>
      <span style={{ color: '#1980ff' }}>清空：</span>
      <span>上线后首次运行，会先清空存储表中已有数据再将采集数据入库</span>
    </div>
    <div>
      <span style={{ color: '#1980ff' }}>不清空：</span>
      <span>上线后首次运行，不会清空存储表中的历史数据</span>
    </div>
  </>
);

export const historyTooltip = (
  <Tooltip title={historyTipText}>
    <IconFont
      type="icon-question-circle"
      style={{
        marginLeft: 8,
      }}
    />
  </Tooltip>
);

const textType = ['text', 'Textfile'];
type NamePayload = {
  dbname: string;
  engineid: string;
  sourcename: string;
};

type DispatchCore = ({ type, payload }: { type: string; payload?: {} }) => void;
type PropsFromState<T> = T & {
  dispatch: DispatchCore;
};

interface BasicProps {
  batchForm: FormInstance | undefined;
  singleForm: FormInstance;
  isEdit: boolean;
  isEditing: boolean;
  storeType: 'new' | 'exist';
  overviewData: BatchDetail;
  singleData: SingleExtractTable;
  singleCustomize: SingleDetail;
  existTableDetail: ExistTableDetail;
  extractHistory: boolean;
  extractMode: SingleDetail['gathermethod'];
  changeExistTable: (tbId: string) => void;
}

const Basic = (props: PropsFromState<StateProps> & BasicProps) => {
  const { storeMode, setStoreMode } = useModel(
    'DataCollect.OffLine.batchCollect',
  );
  const {
    storeType,
    batchForm,
    singleForm,
    changeExistTable,
    extractMode,
    extractHistory,
    isEdit,
    isEditing,
    overviewData,
    singleData,
    singleCustomize,
    existTableDetail,
  } = props;
  const {
    layerList,
    catalogList,
    categoryList,
    tableTag,
    storeTableNames,
    dispatch,
    fieldMapFresh,
  } = props;
  const batchStoreData = batchForm?.getFieldValue(['store']) || {};
  const [layerDbName, setLayerDbName] = useState('');
  const [sourceType, setSourceType] = useState('');
  const [sourceLayerId, setSourceLayerId] = useState('');
  const [tbNamePayload, setTbNamePayload] = useState<NamePayload | {}>({});
  const [existStrategy, setExistStrategy] = useState(0);
  const [nameChanged, setNameChanged] = useState(false);
  const [newStrategy, setNewStrategy] = useState(0);
  // 新建表的覆盖策略状态
  const [newCoverStrategy, setNewCoverStrategy] = useState('whole');
  // 已有表的覆盖策略状态
  const [existCoverStrategy, setExistCoverStrategy] = useState('whole');
  useEffect(() => {
    const { dbName, dbType, namePayload, layerId } = getLayerDbName();
    setLayerDbName(dbName);
    setSourceType(dbType);
    setSourceLayerId(layerId);
    setTbNamePayload(namePayload);
  }, [layerList, overviewData]);
  const existStrategyFormData =
    singleForm.getFieldValue(['store', 'exist', 'strategy']) || 0;
  const newStoreMode =
    singleForm.getFieldValue(['store', 'new', 'mode']) || 'column';
  // const newStrategyFormData =
  // singleForm.getFieldValue(['store', 'new', 'strategy']) || 0;
  const newCoverStrategyFormData =
    singleForm.getFieldValue(['store', 'new', 'coverStrategy']) ??
    singleCustomize.conditioncover
      ? 'partial'
      : 'whole';
  const existCoverStrategyFormData =
    singleForm.getFieldValue(['store', 'exist', 'coverStrategy']) ??
    singleCustomize.conditioncover
      ? 'partial'
      : 'whole';

  useEffect(() => {
    setExistStrategy(existStrategyFormData);
    setStoreMode(newStoreMode);
    setNewCoverStrategy(newCoverStrategyFormData);
    setExistCoverStrategy(existCoverStrategyFormData);
  }, [
    existStrategyFormData,
    newStoreMode,
    newCoverStrategyFormData,
    existCoverStrategyFormData,
  ]);

  useEffect(() => {
    if (Object.keys(tbNamePayload).length > 0) {
      dispatch({
        type: 'batchNew/getStoreTableNames',
        payload: tbNamePayload,
      });
    }
  }, [tbNamePayload]);
  useEffect(() => {
    if (isEdit && !isEditing) {
      const { sourceTableName } = singleData;
      if (sourceTableName) {
        dispatch({
          type: 'batchNew/listStoreTable',
          payload: {
            page: 1,
            reqtype: 2,
            perpage: 5000,
            sourceid: overviewData.globalsourcedb.sourceid,
            filterds: [
              {
                filtertype: 1,
                filterfield: 'sourceid',
                filtercontent: [overviewData.globalsourcedb.sourceid],
              },
              {
                filtertype: 2,
                filterfield: 'tbname',
                filtercontent: [sourceTableName],
              },
            ],
            isonlyshowtb: true,
          },
        });
      }
    }
  }, [isEditing, isEdit, singleData.sourceTableName]);

  useEffect(() => {
    singleForm.validateFields([['store', 'new', 'tableName']]);
  }, [
    singleForm.getFieldValue(['store', 'new', 'tableName']),
    storeType,
    nameChanged,
  ]);
  const sourceCatalog = useMemo(() => {
    const cataLog =
      catalogList.find((item) => item.id === sourceLayerId) || catalogList;
    return Array.isArray(cataLog) ? cataLog : [cataLog];
  }, [catalogList, sourceLayerId]);

  function getLayerDbName() {
    let dbName = '';
    let dbType = '';
    let layerId = '';
    let namePayload = {};
    const sourceId =
      batchStoreData.sourceName || overviewData.globalsourcedb.sourceid;
    layerList.forEach((layer) => {
      if (layer.children) {
        layer.children.forEach((db) => {
          if (db.value === sourceId) {
            dbName = `${layer.title}/${db.title}`;
            dbType = layer.engineName;
            layerId = layer.value;
            namePayload = {
              dbname: db.sourcedbRef?.dbname,
              engineid: layer.layerRef?.engineid,
              sourcename: db.sourcedbRef?.sourcename,
            };
          }
        });
      }
    });
    return { dbName, dbType, layerId, namePayload };
  }

  function getCatalog(catalogId?: string) {
    let catalogName = '其他';
    const nameList = [];
    if (categoryList && categoryList.length > 0) {
      getNameList(categoryList, nameList);
      catalogName = nameList.join('/');
    }
    return catalogName;
  }

  function getTagListView(tagList) {
    const tagsView = tagList.length ? (
      tagList.map((item) => {
        return (
          <Tag key={item.name} closable={false} color={item.color}>
            {item.name}
          </Tag>
        );
      })
    ) : (
      <span>--</span>
    );
    return tagsView;
  }

  const fetchOptions = (payload) => {
    const { searchValue = '', pageSize, key = '', ...restPayload } = payload;
    return (
      dispatch({
        type: 'batchNew/listStoreTable',
        payload: {
          perpage: pageSize,
          reqtype: 2,
          filterds: [
            {
              filtertype: 1,
              filterfield: 'sourceid',
              filtercontent: [payload.sourceid],
            },

            key && {
              filtertype: 2,
              filterfield: 'tbname',
              filtercontent: [key],
            },
            searchValue && {
              filtertype: 0,
              filterfield: 'tbname',
              filtercontent: [searchValue],
            },
          ].filter((item) => item),
          isonlyshowtb: true,
          ...restPayload,
        },
      }) as unknown as Promise<any>
    ).then(({ code, result, total }) => {
      if (code === 200) {
        const newResult = result || [];
        if (newResult.length === 0 && !searchValue) {
          return {
            optionList: [],
            total: 0,
          };
        }

        const optionList = newResult.map((item) => {
          const isPaimon = item.fileformat?.toLocaleLowerCase() === 'paimon';

          return {
            ...item,
            value: item.tbname,
            label: item.tbname,
            disabled: isPaimon,
            extraRender: isPaimon ? (
              <span className={otherStyles.disabledLabel}>
                （不支持paimon表）
              </span>
            ) : undefined,
          };
        });
        return {
          optionList,
          total,
        };
      }
      return {
        optionList: [],
        total: 0,
      };
    });
  };

  const changeExistStrategy = (e) => {
    const { value } = e.target;
    setExistStrategy(value);
  };

  const changeNewCoverStrategy = (e) => {
    const { value } = e.target;
    setNewCoverStrategy(value);
    singleForm?.setFieldsValue({
      store: {
        new: {
          sql: undefined,
        },
      },
    });
  };

  const changeExistCoverStrategy = (e) => {
    const { value } = e.target;
    setExistCoverStrategy(value);
    singleForm?.setFieldsValue({
      store: {
        exist: {
          sql: undefined,
        },
      },
    });
  };

  const changeTable = (_, option) => {
    dispatch({
      type: 'batchNew/save',
      payload: {
        fieldMapFresh: !fieldMapFresh,
      },
    });
    const { id } = option || { id: '' };
    changeExistTable(id);
  };

  const changeStoreMode = (e) => {
    setStoreMode(e.target.value);
  };

  const overviewBasic: OverViewBasic = useMemo(() => {
    let sourceName = '';
    let tableName = '';
    let catalogId = '';
    let strategy = 0;
    let tagList = [];
    let des = '';
    let sql = '';
    sourceName = `${overviewData.globalsourcedb.layername}/${overviewData.globalsourcedb.schema}`;
    tableName = singleCustomize.sourcetable || singleData.sourceTableName;
    catalogId =
      existTableDetail.catalogId ||
      singleCustomize.sourceinfo.catalogid ||
      overviewData.globalsourcedb.catalogid ||
      '';
    strategy =
      singleCustomize.extractonlinestrategy !== undefined
        ? singleCustomize.extractonlinestrategy
        : overviewData.globalsubmitinfo?.extractonlinestrategy;
    tagList = (existTableDetail.tagList ||
      singleCustomize.sourceinfo?.taglist ||
      overviewData.globalsourcedb.taglist ||
      []) as never;
    des =
      existTableDetail.describe ||
      singleCustomize.sourceinfo?.tablecomment ||
      '--';

    sql =
      singleCustomize?.conditioncover ??
      overviewData.globalsubmitinfo?.conditioncover ??
      undefined;

    return { sourceName, tableName, catalogId, strategy, tagList, des, sql };
  }, [overviewData, singleCustomize, existTableDetail]);

  const existTableFileType =
    existTableDetail.fileFormat ||
    singleCustomize.sourceinfo?.filetype ||
    overviewData.globalsourcedb.filetype;
  const existConfigShow = useMemo(() => {
    if (
      storeType === 'exist' &&
      !singleForm.getFieldValue(['store', 'exist', 'tableName'])
    ) {
      return false;
    } else {
      return true;
    }
  }, [storeType, singleForm.getFieldValue(['store', 'exist', 'tableName'])]);
  const changeNewTbName = () => {
    setNameChanged(true);
  };
  return (
    <div>
      {storeType === 'new' ? (
        // *新建表
        <>
          <Form.Item label="存储源">
            <span>
              {isEdit && !isEditing ? overviewBasic.sourceName : layerDbName}
            </span>
          </Form.Item>
          <Form.Item
            label="目标表名"
            name={['store', 'new', 'tableName']}
            rules={
              isEdit && !isEditing
                ? []
                : getStandardRules(
                    '目标表名不能为空',
                    ['', storeTableNames.map((item) => item.tablename)],
                    'tableField',
                    sourceType,
                  )
            }
          >
            <Input placeholder="源表名支持修改" onChange={changeNewTbName} />
          </Form.Item>
          <Form.Item label="所属类目" name={['store', 'new', 'catalogId']}>
            {isEdit && !isEditing ? (
              <span>{getCatalog(overviewBasic.catalogId)}</span>
            ) : (
              <SearchTreeSelect
                treeIcon
                showSearch
                expandOnClick
                treeData={sourceCatalog}
                placeholder="请选择或搜索类目信息"
              />
            )}
          </Form.Item>
          {TERYX.includes(sourceType) && (
            <>
              <Form.Item label="存储方式">
                <Form.Item noStyle name={['store', 'new', 'mode']}>
                  <RadioButton
                    options={[
                      { label: '行存', value: 'row' },
                      { label: '列存', value: 'column' },
                    ]}
                    onChange={changeStoreMode}
                  />
                </Form.Item>
                <Tooltip
                  title={
                    <div>
                      行存储适用于OLTP任务类的表，对更新删除较方便
                      但占用存储空间大；列存适合OLAP任务类的表，支
                      持压缩存储，对数据分析、计算、聚合、多表JOIN
                      等很友好，但不支持UPDATE、Delete操作。
                    </div>
                  }
                >
                  <IconFont
                    type="icon-question-circle"
                    style={{
                      position: 'absolute',
                      marginLeft: 8,
                    }}
                  />
                </Tooltip>
              </Form.Item>
              {storeMode === 'column' && (
                <Form.Item label="压缩格式">
                  <span>zlib</span>
                </Form.Item>
              )}
            </>
          )}
          {HIVE.includes(sourceType) && (
            <Form.Item label="文件格式" name={['store', 'new', 'fileType']}>
              <RadioButton
                options={[
                  { label: 'Orcfile', value: 'orc' },
                  { label: 'Textfile', value: 'text' },
                ]}
              />
            </Form.Item>
          )}
          {isEdit && !isEditing ? (
            <>
              <Form.Item label="数据策略">
                <span>{overviewBasic.strategy > 0 ? '追加' : '覆盖'}</span>
              </Form.Item>
              {overviewBasic.strategy === 0 && (
                <div style={{ marginBottom: 12 }}>
                  <Form.Item label="覆盖策略">
                    <span>
                      {singleForm.getFieldValue(['store', 'new', 'sql'])
                        ? '条件覆盖'
                        : '全表覆盖'}
                    </span>
                  </Form.Item>
                  {singleForm.getFieldValue(['store', 'new', 'sql']) && (
                    <Form.Item label=" " colon={false}>
                      <span>
                        {singleForm.getFieldValue(['store', 'new', 'sql'])}
                      </span>
                    </Form.Item>
                  )}
                </div>
              )}
            </>
          ) : (
            <>
              <Form.Item label="数据策略">
                <Form.Item noStyle name={['store', 'new', 'strategy']}>
                  <RadioButton
                    options={[
                      {
                        label: '覆盖',
                        value: 0,
                        disabled: extractMode === 2,
                        toolTip:
                          extractMode === 2
                            ? {
                                title: (
                                  <>
                                    <div>数据源采集方式为增量时</div>
                                    <div>数据策略不支持覆盖</div>
                                  </>
                                ),
                              }
                            : undefined,
                      },
                      { label: '追加', value: 1 },
                    ]}
                    onChange={({ target: { value } }) => {
                      setNewStrategy(value);
                      setNewCoverStrategy('whole');
                      singleForm?.setFieldsValue({
                        store: {
                          new: {
                            coverStrategy: 'whole',
                            sql: undefined,
                          },
                        },
                      });
                    }}
                  />
                </Form.Item>
                {strategyTooltip}
              </Form.Item>
              {/* 新建/编辑 - 子任务-新建表 */}
              {newStrategy === 0 && (
                <div style={{ marginBottom: 12 }}>
                  <Form.Item
                    label="覆盖策略"
                    style={{ width: '100% !important' }}
                  >
                    <Form.Item
                      name={['store', 'new', 'coverStrategy']}
                      initialValue={newCoverStrategy}
                      noStyle
                    >
                      <RadioButton
                        options={[
                          { label: '全表覆盖', value: 'whole' },
                          { label: '条件覆盖', value: 'partial' },
                        ]}
                        onChange={changeNewCoverStrategy}
                      />
                    </Form.Item>
                  </Form.Item>
                  {newCoverStrategy === 'partial' && (
                    <Form.Item
                      label=" "
                      colon={false}
                      style={{ width: '100% !important' }}
                    >
                      <Form.Item
                        name={['store', 'new', 'sql']}
                        requiredMark
                        initialValue={
                          singleCustomize.conditioncover ?? undefined
                        }
                        rules={[{ required: true, message: '请输入条件' }]}
                        noStyle
                      >
                        <Input
                          style={{ width: 210 }}
                          addonBefore="where"
                          placeholder="请输入条件"
                        />
                      </Form.Item>
                      <Tooltip
                        placement="right"
                        title={
                          <>
                            <div>
                              1.
                              请输入标准SQL中where关键字后的筛选条件(不包括where)，支持使用全局参数
                            </div>
                            <div>2. Hive仅支持指定分区覆盖</div>
                          </>
                        }
                      >
                        <IconFont
                          type="icon-question-circle"
                          style={{
                            width: 16,
                            left: 220,
                            position: 'absolute',
                            top: 2,
                          }}
                        />
                      </Tooltip>
                    </Form.Item>
                  )}
                </div>
              )}
            </>
          )}

          <Form.Item label="表标签" name={['store', 'new', 'tags']}>
            {isEdit && !isEditing ? (
              getTagListView(overviewBasic.tagList)
            ) : (
              <ColorTagTreeSelect
                placeholder="请选择或搜索表标签"
                tagList={tableTag}
              />
            )}
          </Form.Item>
          <Form.Item label="表描述" name={['store', 'new', 'description']}>
            {isEdit && !isEditing ? (
              <span>{overviewBasic.des}</span>
            ) : (
              <Input placeholder="请输入表描述信息" />
            )}
          </Form.Item>
        </>
      ) : (
        // *已有表
        <>
          <Form.Item label="存储源">
            <span>
              {isEdit && !isEditing ? overviewBasic.sourceName : layerDbName}
            </span>
          </Form.Item>
          <Form.Item
            label="目标表名"
            name={['store', 'exist', 'tableName']}
            rules={
              isEdit && !isEditing
                ? []
                : [{ required: true, message: '请选择或搜索表' }]
            }
          >
            {isEdit && !isEditing ? (
              <span>
                {CAPITAL_DB.includes(sourceType)
                  ? overviewBasic.tableName
                  : overviewBasic.tableName?.toLocaleLowerCase()}
              </span>
            ) : (
              <FetchSelect
                placeholder="请选择或搜索表"
                fetchOptions={fetchOptions}
                fetchParams={{
                  sourceid:
                    overviewData.globalsourcedb.sourceid ||
                    batchStoreData.sourceName,
                }}
                icon={
                  <IconFont
                    type="icon-table"
                    style={{ color: '#1980ff', marginRight: 8 }}
                  />
                }
                onChange={changeTable}
              />
            )}
          </Form.Item>
          {existConfigShow && (
            <Form.Item label="所属类目">
              <span>{getCatalog(overviewBasic.catalogId)}</span>
            </Form.Item>
          )}
          {TERYX.includes(sourceType) && existConfigShow && (
            <Form.Item label="压缩格式">
              <span>{existTableDetail.compressType || '--'}</span>
            </Form.Item>
          )}
          {HIVE.includes(sourceType) && existConfigShow && (
            <Form.Item label="文件格式">
              <span>
                {textType.includes(existTableFileType) ? 'Textfile' : 'Orcfile'}
              </span>
            </Form.Item>
          )}
          {!(isEdit && !isEditing) && existConfigShow && (
            <>
              <Form.Item label="数据策略">
                <Form.Item noStyle name={['store', 'exist', 'strategy']}>
                  <RadioButton
                    options={[
                      {
                        label: '覆盖',
                        value: 0,
                        disabled: extractMode === 2,
                        toolTip:
                          extractMode === 2
                            ? {
                                title: (
                                  <>
                                    <div>数据源采集方式为增量时</div>
                                    <div>数据策略不支持覆盖</div>
                                  </>
                                ),
                              }
                            : undefined,
                      },
                      { label: '追加', value: 1 },
                    ]}
                    onChange={(e) => {
                      changeExistStrategy(e);
                      if (e.target.value === 0) {
                        setExistCoverStrategy('whole');
                        singleForm?.setFieldsValue({
                          store: {
                            exist: {
                              coverStrategy: 'whole',
                              sql: undefined,
                            },
                          },
                        });
                      }
                    }}
                  />
                </Form.Item>
                {strategyTooltip}
              </Form.Item>
              {/* 新建/编辑 - 子任务-已有表 */}
              {existStrategy === 0 && (
                <div style={{ marginBottom: 12 }}>
                  <Form.Item
                    label="覆盖策略"
                    style={{ width: '100% !important' }}
                  >
                    <Form.Item
                      name={['store', 'exist', 'coverStrategy']}
                      initialValue={existCoverStrategy}
                      noStyle
                    >
                      <RadioButton
                        options={[
                          { label: '全表覆盖', value: 'whole' },
                          { label: '条件覆盖', value: 'partial' },
                        ]}
                        onChange={changeExistCoverStrategy}
                      />
                    </Form.Item>
                  </Form.Item>
                  {existCoverStrategy === 'partial' && (
                    <Form.Item
                      label=" "
                      colon={false}
                      style={{ width: '100% !important' }}
                    >
                      <Form.Item
                        name={['store', 'exist', 'sql']}
                        requiredMark
                        initialValue={
                          singleCustomize.conditioncover ?? undefined
                        }
                        rules={[{ required: true, message: '请输入条件' }]}
                        noStyle
                      >
                        <Input
                          style={{ width: 210 }}
                          addonBefore="where"
                          placeholder="请输入条件"
                        />
                      </Form.Item>
                      <Tooltip
                        placement="right"
                        title={
                          <>
                            <div>
                              1.
                              请输入标准SQL中where关键字后的筛选条件(不包括where)，支持使用全局参数
                            </div>
                            <div>2. Hive仅支持指定分区覆盖</div>
                          </>
                        }
                      >
                        <IconFont
                          type="icon-question-circle"
                          style={{
                            width: 16,
                            left: 220,
                            position: 'absolute',
                            top: 2,
                          }}
                        />
                      </Tooltip>
                    </Form.Item>
                  )}
                </div>
              )}
            </>
          )}
          {isEdit && !isEditing && (
            <>
              <Form.Item label="数据策略">
                <span>{overviewBasic.strategy > 0 ? '追加' : '覆盖'}</span>
              </Form.Item>
              {/* 查看 - 子任务-详情(已有表) */}
              {overviewBasic.strategy === 0 && (
                <div style={{ marginBottom: 12 }}>
                  <Form.Item label="覆盖策略">
                    <span>
                      {singleForm.getFieldValue(['store', 'exist', 'sql'])
                        ? '条件覆盖'
                        : '全表覆盖'}
                    </span>
                  </Form.Item>
                  {singleForm.getFieldValue(['store', 'exist', 'sql']) && (
                    <Form.Item label=" " colon={false}>
                      <span>
                        {singleForm.getFieldValue(['store', 'exist', 'sql'])}
                      </span>
                    </Form.Item>
                  )}
                </div>
              )}
            </>
          )}
          {existStrategy === 1 &&
            !(isEdit && !isEditing) &&
            existConfigShow && (
              <Form.Item label="存储表历史数据">
                <Form.Item noStyle name={['store', 'exist', 'history']}>
                  <RadioButton
                    options={[
                      {
                        label: '清空',
                        value: true,
                        disabled: extractMode === 2 && !extractHistory,
                        toolTip:
                          extractMode === 2 && !extractHistory
                            ? {
                                title: (
                                  <>
                                    <div>源表历史数据为不同步</div>
                                    <div>则不支持清空存储表历史数据</div>
                                  </>
                                ),
                              }
                            : undefined,
                      },
                      { label: '不清空', value: false },
                    ]}
                  />
                </Form.Item>
                {historyTooltip}
              </Form.Item>
            )}
          {existStrategy === 1 && isEdit && !isEditing && (
            <Form.Item label="存储表历史数据">
              <span>{singleCustomize.historydata ? '清空' : '不清空'}</span>
            </Form.Item>
          )}
          {existConfigShow && (
            <Form.Item label="表标签">
              <sapn>{getTagListView(overviewBasic.tagList)}</sapn>
            </Form.Item>
          )}
          {existConfigShow && (
            <Form.Item label="表描述">
              <span>{overviewBasic.des}</span>
            </Form.Item>
          )}
        </>
      )}
    </div>
  );
};

const BasicStateToProps = ({
  batchNew: {
    layerList,
    catalogList,
    categoryList,
    tableTag,
    storeTableNames,
    fieldMapFresh,
  },
}: ConnectState) => {
  return {
    layerList,
    catalogList,
    tableTag,
    storeTableNames,
    fieldMapFresh,
    categoryList,
  };
};

type StateProps = ReturnType<typeof BasicStateToProps>;
export default connect<StateProps, {}, {}, ConnectState>(BasicStateToProps)(
  Basic,
);
