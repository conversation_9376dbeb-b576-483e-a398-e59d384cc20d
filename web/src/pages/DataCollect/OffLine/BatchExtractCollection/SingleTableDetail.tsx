import { useEffect, useMemo, useState, useRef } from 'react';
import {
  Form,
  Collapse,
  Button,
  notification,
  Drawer,
  Space,
  Spin,
} from 'antd';
import { connect } from 'dva';
import { FormInstance } from 'antd/lib/form';
import { Graph as GraphType } from '@antv/g6';
import { ConnectState } from '@/models/connect.d';
import _ from 'lodash';
import { SUPPORT_CAPITAL_DBTYPE as CAPITAL_DB } from '@/pages/DataCollect/OffLine/conf';
import Extract from './Single/Extract';
import Store from './Single/Store';
import FieldMap from './Single/FieldMap';
import Advanced from './Single/Advanced';
import { DM, UX, MAP_FIELD_LIMIT, DISTRIBUTE, GP_DB } from './config';

import {
  checkTableFields,
  getStoreModeParam,
  getPartitionField,
  transCSVName,
} from './utils';
import { SingleCustom } from './index';
import {
  BatchTask,
  BatchDetail,
  SingleDetail,
  SingleExtractTable,
  DBDistribute,
  ExistTableDetail,
  DistributionField,
  DistributionMode,
  FileType,
  PartionField,
} from './index.d';

import styles from './index.less';

const FORM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type Pattern = 'system' | 'field';

export type SingleDistributeInfo = {
  recdistributionbond: string[];
  recdistributionmode: DistributionMode;
  originalfieldinfo: DistributionField[];
  dmdistributionbond: string[];
  dmdistributionmode: DistributionMode;
  dmfieldinfo: DistributionField[];
  uxdistributionbond: string[];
  uxdistributionmode: DistributionMode;
  uxdbfieldinfo: DistributionField[];
} | null;

const sliceEdges = (op) => {
  return op.slice(0, op.length - 2);
};

const getTableRelation = (graph) => {
  const nodes = graph?.getNodes().map((item) => item?.getModel());
  const curEdges = graph?.getEdges().map((item) => {
    const { source, target } = item.getModel();
    return {
      source: sliceEdges(source),
      target: sliceEdges(target),
    };
  });
  const tableRelation = {
    nodes: nodes.map((item) => ({
      fieldname: item.fieldName,
      fieldtype: item.fieldType,
    })),
    edges: curEdges,
  };
  return tableRelation;
};

type Dispatch = ({ type, payload }: { type: string; payload?: {} }) => void;

interface SinigleList {
  visible: boolean;
  isEdit: boolean;
  isEditing: boolean;
  overviewData: BatchDetail;
  closeSingleTable: () => void;
  taskData: BatchTask;
  singleData: SingleExtractTable;
  singleDistribute: SingleDistributeInfo;
  batchForm?: FormInstance;
  singlesPayload: SingleCustom;
  changeSinglesPayload?: (single: SingleCustom) => void;
  taskInfo?: {};
  setSingleData?: (taskInfo: any) => void;
  setSingleDistribute?: (distirbute: any) => void;
  isOuter?: boolean;
  subTaskFetch?: () => void;
}

interface StoreRef {
  storeType: string;
  setPage: (page: number) => void;
  pageSize: number | undefined;
  curExistTable: { id: string };
}

type MapRef = {
  graph: GraphType;
  fieldError: {
    noMapField: boolean;
    noConnectMainKeys: boolean;
    noIncreField: boolean;
  };
};

const SingleTableDetail = (
  props: SinigleList & StateToProps & { dispatch: Dispatch },
) => {
  const {
    singlesPayload,
    changeSinglesPayload,
    isEdit,
    isEditing,
    overviewData,
    taskData,
    taskInfo,
    setSingleData,
    setSingleDistribute,
    isOuter,
  } = props;
  const { visible, closeSingleTable, singleData, singleDistribute, batchForm } =
    props;
  const {
    layerList,
    extractTableField,
    extractTableDetail,
    tagList,
    storeTable,
    dispatch,
  } = props;
  const initCustom = {
    advancedconfig: {
      kbps: -1,
    },
    extracttable: '',
    sourcetable: '',
    gathermethod: 1,
    extractonlinestrategy: undefined,
    conditioncover: undefined,
    historydata: false,
    extractinfo: {},
    sourceinfo: {},
    tablerelation: null,
  };
  const initExistTable = {
    fileFormat: '' as FileType,
    partitionField: null,
    orientation: '',
    catalogId: '',
    compressType: '',
    distributionMode: '' as DistributionMode,
    distributionBond: null,
    tagList: null,
    describe: '',
  };

  const defaultKey = ['extract', 'store'];
  const isOverView = isEdit && !isEditing;
  const storeRef: React.RefObject<StoreRef> = useRef(null);
  const mappingRef = useRef<MapRef>(null);
  const [singleForm] = Form.useForm();
  const [activeKey, setActiveKey] = useState(defaultKey);
  const [sourceDbType, setSourceDbType] = useState('');
  const [existDataInMap, setExistDataInMap] = useState([]);
  const [existTableDetail, setExistTableDetail] =
    useState<ExistTableDetail>(initExistTable);
  const [newDataInMap, setNewDataInMap] = useState([]);
  const [extractMode, setExtractMode] = useState<1 | 2>(1);
  const [extractHistory, setExtractHistory] = useState(true);
  const [whereContent, setWhereContent] = useState('');
  // 编辑时候的单表数据
  const [singleCustomize, setSingleCustomize] = useState<SingleDetail>(
    initCustom as SingleDetail,
  );

  const [increKey, setIncreKey] = useState('');
  const [mapShow, setMapShow] = useState(true);

  const [isWhereValid, setIsWhereValid] = useState(true);

  const defaultPartition = batchForm?.getFieldValue(['hivePartition']) || [];

  useEffect(() => {
    // *curCustomize(单表设置):编辑时候从接口中获取, 新建时从已配置的数据中获取
    const curCustomize = singlesPayload[singleData.tablename] || initCustom;
    setSingleCustomize(curCustomize);
  }, [singlesPayload, singleData]);
  const dbDistribute: DBDistribute = useMemo(() => {
    const {
      recdistributionbond = [],
      recdistributionmode = '',
      originalfieldinfo = [],
      dmdistributionbond = [],
      dmdistributionmode = '',
      dmfieldinfo = [],
      uxdistributionbond = [],
      uxdistributionmode = '',
      uxdbfieldinfo = [],
    } = singleDistribute || {};
    if (DM.includes(sourceDbType)) {
      return {
        distributionMode: dmdistributionmode,
        distributionBond: dmdistributionbond,
        distributionField: dmfieldinfo,
      };
    } else if (UX.includes(sourceDbType)) {
      return {
        distributionMode: uxdistributionmode,
        distributionBond: uxdistributionbond,
        distributionField: uxdbfieldinfo,
      };
    } else {
      return {
        distributionMode: recdistributionmode,
        distributionBond: recdistributionbond,
        distributionField: originalfieldinfo,
      };
    }
  }, [singleDistribute, sourceDbType]);

  useEffect(() => {
    if (taskInfo && Object.keys(taskInfo).length > 0 && setSingleData) {
      setSingleData(taskInfo);
      getSingleDistribution(taskInfo);
    }
  }, [taskInfo]);

  async function getSingleDistribution(single) {
    const { dbid, schemaid, tablename, tableid } = single;
    const sourceType = overviewData.globalsourcedb.odsenginename;
    if (DISTRIBUTE.includes(sourceType)) {
      const result = await dispatch({
        type: 'batchNew/getDistributionKey',
        payload: {
          extracttabsinfo: [
            {
              extractid: dbid,
              schemaid,
              tableid: [tableid],
              tablename: [tablename],
            },
          ],
        },
      });
      if (setSingleDistribute) setSingleDistribute(result);
    }
  }

  function closeTableSet() {
    setSourceDbType('');
    setExistDataInMap([]);
    dispatch({
      type: 'batchNew/save',
      payload: {
        extractTableField: [],
        storeTable: [],
        fieldMapFresh: false,
        singleSetRefresh: false,
      },
    });
    setActiveKey(defaultKey);
    setExistTableDetail(initExistTable);
    setNewDataInMap([]);
    setExtractMode(1);
    setWhereContent('');
    setSingleCustomize(initCustom as SingleDetail);
    closeSingleTable();
  }

  function changeIncreKey(value) {
    setIncreKey(value);
  }

  useEffect(() => {
    const value = singleCustomize.extractinfo?.createfield || '';
    const gatherMode = singleCustomize.gathermethod || 1;
    const isSynchistory =
      singleCustomize.extractinfo?.accincrestatus?.issynchistory;
    const history = isSynchistory !== undefined ? isSynchistory : true;
    setIncreKey(value);
    setExtractMode(gatherMode);
    setExtractHistory(history);
  }, [
    singleCustomize.extractinfo?.createfield,
    singleCustomize.extractinfo?.accincrestatus?.issynchistory,
    singleCustomize.gathermethod,
  ]);

  async function checkWhere({ value, showSuccessTip = false }) {
    const whereValid = (await dispatch({
      type: 'batchNew/checkWhere',
      payload: {
        id: singleData.dbid,
        dbname: singleData.dbname,
        schema: singleData.schemaname,
        table: singleData.tablename,
        wherecontent: value,
        showSuccessTip,
      },
    })) as unknown as boolean;
    return whereValid;
  }

  const extractField = useMemo(() => {
    return extractTableField.map((item) => {
      return {
        ...item,
        isIncre: item.fieldName === increKey,
      };
    });
  }, [extractTableField, increKey]);

  const partitionInitValue: PartionField[] | undefined = useMemo(() => {
    const value =
      storeRef.current?.storeType === 'new'
        ? singleCustomize.sourceinfo?.partitionfield
        : existTableDetail.partitionField ||
          singleCustomize.sourceinfo?.partitionfield;
    let partition = value
      ? value.map((item) => {
          const { name, ispreset } = item;
          const data =
            ispreset === 'notpreset'
              ? { pattern: 'field' as Pattern, field: name }
              : {
                  pattern: 'system' as Pattern,
                  type: ispreset,
                  field: name,
                };
          return data;
        })
      : undefined;
    if (!partition && defaultPartition?.length > 0) {
      partition = defaultPartition.map((item) => {
        return {
          pattern: 'system',
          type: item.type,
          field: item.field,
        };
      });
    }
    return partition;
  }, [
    existTableDetail.partitionField,
    singleCustomize.sourceinfo?.partitionfield,
    defaultPartition,
  ]);

  const SINGLE_SETTING = [
    {
      name: 'extract',
      title: '选择数据源',
      render: (
        <Extract
          singleForm={singleForm}
          isEdit={isEdit}
          isEditing={isEditing}
          singleData={singleData}
          overviewData={overviewData}
          singleCustomize={singleCustomize}
          changeExtractMode={changeExtractMode}
          changeExtractHistory={changeExtractHistory}
          changeWhereContent={changeWhereContent}
          whereContent={whereContent}
          changeIncreKey={changeIncreKey}
          taskData={taskData}
          checkWhere={checkWhere}
          isWhereValid={isWhereValid}
          setIsWhereValid={setIsWhereValid}
        />
      ),
      required: [],
    },
    {
      name: 'store',
      title: '选择存储源',
      render: (
        <Store
          ref={storeRef}
          isEdit={isEdit}
          isEditing={isEditing}
          overviewData={overviewData}
          singleCustomize={singleCustomize}
          batchForm={batchForm}
          singleForm={singleForm}
          singleData={singleData}
          sourceDbType={sourceDbType}
          dbDistribute={dbDistribute}
          refreshExistDataInMap={refreshExistDataInMap}
          refreshExistTableDetail={refreshExistTableDetail}
          refreshNewDataInMap={refreshNewDataInMap}
          refreshMapShow={refreshMapShow}
          extractMode={extractMode}
          extractHistory={extractHistory}
          isOuter={isOuter}
        />
      ),
      required: [],
    },
    {
      name: 'map',
      title: '表字段映射',
      render: (
        <FieldMap
          ref={mappingRef}
          isEdit={isEdit}
          isEditing={isEditing}
          extractField={extractField}
          storeField={
            storeRef.current?.storeType === 'new' && !isOverView
              ? newDataInMap
              : existDataInMap
          }
          singleCustomize={singleCustomize}
          partitionFields={partitionInitValue}
        />
      ),
      required: [],
    },
    {
      name: 'advanced',
      title: '高级配置',
      render: (
        <Advanced
          form={singleForm}
          extractField={extractField}
          isEdit={isEdit}
          isEditing={isEditing}
          singleCustomize={singleCustomize}
          singleData={singleData}
          sourceDbType={sourceDbType}
        />
      ),
    },
  ];
  function refreshExistDataInMap(data) {
    setExistDataInMap(data);
  }

  function refreshExistTableDetail(val) {
    setExistTableDetail(val);
  }

  function refreshNewDataInMap(data) {
    setNewDataInMap(data);
  }

  function refreshMapShow(value) {
    setMapShow(value);
  }
  function changeExtractMode(mode) {
    setExtractMode(mode);
    if (mode === 2) {
      singleForm.setFieldsValue({ store: { new: { strategy: 1 } } });
      singleForm.setFieldsValue({ store: { exist: { strategy: 1 } } });
    }
  }

  function changeExtractHistory(value) {
    setExtractHistory(value);
  }

  function changeWhereContent(value) {
    setWhereContent(value);
  }

  useEffect(() => {
    const dbType = getSourceType();
    setSourceDbType(dbType);
    // 初始化单表存储源数据
    initSingleFormData();
  }, [visible, dbDistribute, singleCustomize, singleData]);

  const extractHistoryEdit = useMemo(() => {
    if (taskData.customize) {
      const curCustomize = taskData.customize?.find(
        (item) => item.extracttable === singleData.tablename,
      );
      if (curCustomize) {
        const increData =
          curCustomize.extractinfo?.accincrestatus?.currincredata;
        return !!increData;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }, [singleData, taskData]);

  function getSourceType() {
    const batchStoreData = batchForm?.getFieldValue(['store']) || {};
    let curType = '';
    layerList.forEach((layer) => {
      if (layer.children) {
        layer.children.forEach((db) => {
          if (
            db.value ===
            (batchStoreData.sourceName ||
              overviewData?.globalsourcedb?.sourceid)
          ) {
            curType = layer.engineName;
          }
        });
      }
    });
    return curType;
  }

  const getSourceTableName = () => {
    let sourceName = singleData.sourceTableName;
    if (isEdit && !isEditing) {
      sourceName = singleCustomize.sourceinfo.tableid
        ? singleCustomize.sourcetable
        : singleData.sourceTableName;
    }
    if (isEditing) {
      sourceName = singleCustomize.sourcetable || singleData.sourceTableName;
    }
    return sourceName;
  };

  const getCurPartition = () => {
    if (
      singleCustomize.sourceinfo?.partitionfield?.length > 0 ||
      (singleCustomize.sourceinfo?.partitionfield === undefined &&
        defaultPartition?.length > 0)
    ) {
      return 1;
    }

    return 0;
  };
  function initSingleFormData() {
    const batchStoreData = batchForm?.getFieldValue(['store']) || {
      strategy: overviewData.globalsubmitinfo.extractonlinestrategy,
      coverStrategy: _.isEmpty(overviewData.globalsubmitinfo?.conditioncover)
        ? 'whole'
        : 'partial',
      sql: overviewData.globalsubmitinfo?.conditioncover ?? undefined,
    };
    const { catalogid, strategy, coverStrategy, sql, tags, fileType, mode } =
      batchStoreData;
    const { distributionMode, distributionBond } = dbDistribute;
    if (visible) {
      const singleStrategy =
        singleCustomize.extractonlinestrategy !== undefined
          ? singleCustomize.extractonlinestrategy
          : strategy;

      const singleSql =
        singleCustomize.extractonlinestrategy !== undefined
          ? singleCustomize?.conditioncover ?? undefined
          : sql; // 仿照singleStrategy取值逻辑，定义过子任务的策略则取定义过的值，否则取详情接口的值

      const newTbName =
        singleCustomize.sourcetable || singleData.sourceTableName || '';

      const tableName = transCSVName(singleData.dbtype, newTbName);
      const initPriorityMode = GP_DB.includes(sourceDbType) ? 2 : 1;

      // 此处处理编辑时切换存储源，依然展示上次保存的，用的overviewData数据处理
      let { prioritymode } = singleCustomize.advancedconfig;
      const overviewCustomize = overviewData.customize?.find(
        (item) => item.sourcetable === newTbName,
      );
      if (!singleCustomize.advancedconfig.prioritymode && overviewCustomize) {
        prioritymode = overviewCustomize.advancedconfig.prioritymode;
      }

      let errorInfo = {
        type: 'number',
        count: singleCustomize.advancedconfig.errorinfo?.errlogcount || 1000,
        percent: 2,
      };

      if (singleCustomize.advancedconfig.errorinfo?.errlogcount === -1) {
        errorInfo = {
          type: 'percent',
          count: 1000,
          percent: singleCustomize.advancedconfig.errorinfo?.errlogper || 2,
        };
      } else if (
        singleCustomize.advancedconfig.errorinfo?.errlogcount === undefined &&
        overviewCustomize
      ) {
        const overviewErrLogcount =
          overviewCustomize.advancedconfig.errorinfo.errlogcount;
        errorInfo = {
          type: overviewErrLogcount === -1 ? 'percent' : 'number',
          count:
            overviewErrLogcount === -1
              ? 1000
              : overviewCustomize.advancedconfig.errorinfo.errlogcount,
          percent: overviewCustomize.advancedconfig.errorinfo?.errlogper || 2,
        };
      }

      let ram = 1;
      if (singleCustomize.advancedconfig.ram) {
        ram = singleCustomize.advancedconfig.ram;
      } else if (!singleCustomize.advancedconfig.ram && overviewCustomize) {
        ram = overviewCustomize.advancedconfig.ram;
      }

      let speedLimit = {
        limit: true,
        speed: singleCustomize.advancedconfig.kbps || 1,
      };

      if (singleCustomize.advancedconfig.kbps === -1) {
        speedLimit = { limit: false, speed: -1 };
      } else if (
        singleCustomize.advancedconfig.kbps === undefined &&
        overviewCustomize
      ) {
        const overviewKps = overviewCustomize.advancedconfig.kbps;
        speedLimit = {
          limit: overviewKps !== -1,
          speed: overviewKps === -1 ? -1 : 1,
        };
      }

      let errorRecord = true;
      if (
        singleCustomize.advancedconfig.isrecorderr === undefined &&
        overviewCustomize
      ) {
        errorRecord = overviewCustomize.advancedconfig.isrecorderr || true;
      } else {
        errorRecord = singleCustomize.advancedconfig.isrecorderr;
      }

      const filterData = singleCustomize.extractinfo?.wherecontent || '';

      setWhereContent(filterData);
      setIsWhereValid(true);

      singleForm.setFieldsValue({
        extract: {
          mode: singleCustomize.gathermethod || 1,
          history: !extractHistoryEdit,
          increField: singleCustomize.extractinfo?.createfield || undefined,
          abnormal: singleCustomize.extractinfo?.supportdirtydata || false,
          filter: filterData,
        },
        store: {
          new: {
            // 表名:1自定义设置,2继承批量的设置
            tableName: singleCustomize.sourceinfo?.tableid
              ? undefined
              : CAPITAL_DB.includes(sourceDbType)
              ? tableName
              : tableName.toLocaleLowerCase(),

            catalogId: singleCustomize.sourceinfo?.catalogid || catalogid,
            // 数据策略
            strategy: !singleStrategy ? 0 : 1,
            coverStrategy: 'whole', // 覆盖策略默认全表覆盖
            sql: undefined,
            fileType: singleCustomize.sourceinfo?.filetype || fileType,
            tags:
              singleCustomize.sourceinfo?.taglist?.map((item) => item.id) ||
              tags,
            description:
              singleCustomize.sourceinfo?.tableCommentForNew ||
              singleData.extractComment,
            mode: singleCustomize.sourceinfo?.orientation || mode,
          },
          exist: {
            // 数据策略
            strategy: !singleStrategy ? 0 : 1,
            coverStrategy: !_.isEmpty(singleCustomize?.conditioncover)
              ? 'partial'
              : 'whole',
            sql: singleSql,
            // 存储表历史数据
            history: singleCustomize.historydata || false,
            tableName: getSourceTableName(),
          },
          partition: getCurPartition(),
          distributionMode:
            singleCustomize.sourceinfo?.distributionmode || distributionMode,
          distributionBond:
            singleCustomize.sourceinfo?.distributionbond || distributionBond,
        },
        hivePartition:
          partitionInitValue?.length === 0 ? undefined : partitionInitValue,
        advanced: {
          ram,
          speedLimit,
          splitKey:
            singleCustomize.advancedconfig.splitkey ||
            overviewCustomize?.advancedconfig?.splitkey,
          concurrency:
            singleCustomize.advancedconfig.concurrency ||
            overviewCustomize?.advancedconfig?.concurrency ||
            1,
          prioritymode: prioritymode || initPriorityMode,
          errorInfo,
          errorRecord: !!errorRecord,
        },
      });
    }
  }

  function getExtractFieldInfo() {
    const { field } = extractTableDetail;
    const formatField = field.map((item) => {
      const {
        fieldName,
        fieldtype,
        ispartition,
        index,
        comment,
        bothprimandunique,
      } = item;
      return {
        fieldname: fieldName,
        fieldtype,
        fieldcomment: comment,
        index,
        bothprimandunique,
        ispartition,
      };
    });
    return formatField;
  }

  function getSourceFieldInfo() {
    const tableInfo =
      storeRef.current?.storeType === 'new' ? newDataInMap : existDataInMap;
    const formatField = tableInfo.map((item) => {
      const {
        fieldName,
        fieldType,
        ispartition,
        index,
        comment,
        bothprimandunique,
      } = item;
      return {
        fieldname: fieldName,
        fieldtype: fieldType,
        fieldcomment: comment,
        index,
        bothprimandunique,
        ispartition,
      };
    });
    return formatField;
  }

  const checkFieldMapIsError = () => {
    const fieldError = mappingRef.current?.fieldError || {};
    return Object.values(fieldError).some((item) => item);
  };

  function getSourTbName(id) {
    const data = storeTable.find((item) => item.id === id) || {
      tbname: '',
    };
    return data.tbname;
  }

  const updateSubTask = (subTask) => {
    const newTaskData = {
      ...overviewData,
      name: overviewData.notename,
      customize: [subTask],
    };
    (
      dispatch({ type: 'batchNew/updateSubTask', payload: newTaskData }) as any
    ).then(({ code }) => {
      if (code === 200) {
        closeTableSet();
        //* 14090 lina
        if (props.subTaskFetch) {
          props?.subTaskFetch();
        }
      }
      closeTableSet();
    });
  };

  async function handleOk() {
    if (storeRef.current?.storeType === 'new') {
      const pageSize = storeRef.current?.pageSize || 10;
      const checkFieldsRes = await checkTableFields(
        newDataInMap,
        sourceDbType,
        pageSize,
      );
      if (typeof checkFieldsRes === 'number') {
        await storeRef.current?.setPage(checkFieldsRes);
        singleForm.scrollToField('newField', { block: 'nearest' });
        return;
      }
    }
    if (!mappingRef.current) {
      notification.warning({ message: '请先展开表字段映射配置' });
      return;
    }
    const isError = checkFieldMapIsError();
    if (isError) {
      singleForm.scrollToField('batchMap', { block: 'nearest' });
      return;
    }

    // 判断where 语句
    if (whereContent) {
      const whereValid = await checkWhere({ value: whereContent });
      await setIsWhereValid(whereValid);
      singleForm.validateFields([['extract', 'filter']]);
      if (!whereValid) {
        singleForm.scrollToField(['extract', 'filter'], { block: 'nearest' });
        return;
      }
    }
    const tableRelation = getTableRelation(mappingRef.current?.graph);

    const extractFieldInfo = getExtractFieldInfo();
    const sourceFieldInfo = getSourceFieldInfo();
    const sourceTable =
      storeRef.current?.storeType === 'new' ? newDataInMap : existDataInMap;

    singleForm
      .validateFields()
      .then((val) => {
        const { storeType, curExistTable } = storeRef.current || {
          curExistTable: { id: '' },
          storeType: 'new',
        };
        const { sourceTableName } = singleData;
        const curSourceTable = storeTable.find(
          (item) => item.tbname === sourceTableName,
        );
        const existTableId = curExistTable.id || curSourceTable?.id;
        const {
          extract: {
            mode,
            increField,
            timeFormat,
            abnormal,
            filter,
            // 源表历史数据
            history: isSynchistory,
          },
          store: {
            [storeType]: {
              strategy,
              catalogId,
              fileType,
              description,
              history: storeHistory,
              tableName,
              tags,
              mode: storeMode,
              coverStrategy,
              sql,
            },
            distributionMode,
            distributionBond,
          },
          hivePartition,
          advanced: {
            ram,
            prioritymode,
            speedLimit: { limit, speed },
            splitKey,
            concurrency,
            errorInfo,
            errorRecord = true,
          },
        } = val;

        const modeType =
          storeType === 'new' ? storeMode : existTableDetail.orientation;
        const storeModeParam = getStoreModeParam(modeType);

        const disBond =
          storeType === 'new'
            ? distributionBond
            : existTableDetail.distributionBond;

        const existPartition =
          singleCustomize.sourceinfo?.partitionfield ||
          defaultPartition.map((v) => ({
            name: v.field,
            ispreset: v.type,
            fieldtype: 'string',
            annitation: '',
          }));
        // || existTableDetail.partitionField;

        const customize = {
          advancedconfig: {
            ram,
            splitkey: splitKey,
            concurrency,
            prioritymode,
            kbps: limit ? speed : -1,
            ...(errorInfo
              ? {
                  errorinfo: {
                    errlogcount:
                      errorInfo.type === 'number' ? errorInfo.count : -1,
                    errlogper:
                      errorInfo.type === 'number' ? -1 : errorInfo.percent,
                  },
                }
              : {}),
            isrecorderr: errorRecord,
          },
          extracttable: extractTableDetail.tablename,
          // TODO 区分已有表和新建表
          sourcetable:
            storeType === 'new' ? tableName : getSourTbName(existTableId),
          gathermethod: mode, // 1 | 2
          extractonlinestrategy: mode === 2 ? 2 : strategy, // 0 | 1 | 2
          conditioncover:
            strategy === 0 && coverStrategy === 'partial' ? sql : '', // * 条件覆盖sql语句，为空表示 全表覆盖|追加
          // 存储表历史数据
          historydata: storeHistory,
          extractinfo: {
            accincrestatus: {
              currincredata: '',
              // 源表历史数据
              issynchistory: isSynchistory,
            },
            createfield: increField,
            timetype: timeFormat,
            supportdirtydata: abnormal,
            tableid: extractTableDetail.tableid,
            firstrecord: extractTableDetail.threerecord,
            fieldinfo: extractFieldInfo,
            wherecontent: filter,
          },
          sourceinfo: {
            tableCommentForNew: storeType === 'new' ? description : undefined, // 存一份新建表时填写的表描述
            catalogid:
              storeType === 'new' ? catalogId : existTableDetail.catalogId,
            filetype:
              storeType === 'new' ? fileType : existTableDetail.fileFormat,
            tablecomment:
              storeType === 'new' ? description : existTableDetail.describe,
            taglist:
              storeType === 'new'
                ? (tags || []).map((item) => {
                    const data = tagList.find((list) => list.id === item) || {};
                    return data;
                  })
                : existTableDetail.tagList,
            // * 已有表,要传tableid
            tableid: storeType === 'new' ? '' : existTableId,
            fieldinfo: sourceFieldInfo,
            distributionmode:
              storeType === 'new'
                ? distributionMode
                : existTableDetail.distributionMode,
            distributionbond: typeof disBond === 'string' ? [disBond] : disBond,
            partitionfield:
              storeType === 'new'
                ? getPartitionField(hivePartition, sourceTable)
                : existPartition,
            ...storeModeParam,
          },
          tablerelation: tableRelation,
        };
        const singlePayload = {
          [extractTableDetail.tablename]: customize,
        };

        /**
         * v592检查覆盖策略是否满足要求（hive存储源表未设置分区字段时无法设置条件覆盖）
         */
        const partitionField =
          storeType === 'new'
            ? getPartitionField(hivePartition, sourceTable)
            : existPartition;
        const isPartialCoverInValid =
          sourceDbType === 'hive' &&
          !partitionField?.length &&
          strategy === 0 &&
          coverStrategy === 'partial';

        singleForm.setFields([
          {
            name: ['store', storeType, 'sql'],
            errors: isPartialCoverInValid
              ? ['Hive仅支持指定分区覆盖，当前无分区字段，请重新配置']
              : [],
          },
        ]);
        if (isPartialCoverInValid) {
          return;
        }

        if (isOuter) {
          updateSubTask(customize);
          return;
        }
        if (changeSinglesPayload) {
          changeSinglesPayload(singlePayload);
        }
        singleForm.resetFields();
        closeTableSet();
      })
      .catch((err) => {
        console.log('err', err);
        const { errorFields } = err;
        if (errorFields) {
          singleForm.scrollToField(errorFields[0].name, { block: 'center' });
        }
      });
  }

  useEffect(() => {
    if (
      extractTableField.length &&
      extractTableField.length <= MAP_FIELD_LIMIT
    ) {
      if (!activeKey.includes('map')) setActiveKey(activeKey.concat('map'));
    }
  }, [extractTableField]);

  useEffect(() => {
    if (!mapShow) {
      if (activeKey.includes('map')) {
        setActiveKey(activeKey.filter((item) => item !== 'map'));
      }
    } else if (
      mapShow &&
      !activeKey.includes('map') &&
      extractTableField?.length <= MAP_FIELD_LIMIT
    ) {
      setActiveKey(activeKey.concat('map'));
    }
  }, [mapShow]);

  function changeActiveKey(key) {
    setActiveKey(key);
  }

  const pageLoading = useMemo(() => {
    return (
      props.extractTableLoading ||
      props.existTableLoading ||
      props.distributeLoading ||
      props.fieldTransLoading ||
      props.sameTbNamesLoading ||
      false
    );
  }, [
    props.extractTableLoading,
    props.existTableLoading,
    props.distributeLoading,
    props.fieldTransLoading,
    props.sameTbNamesLoading,
  ]);

  return (
    <Drawer
      className={styles.singleTableWrap}
      title={
        singleData.subTaskName ? (
          <div
            style={{
              width: '810px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
            title={singleData.subTaskName}
          >
            {singleData.subTaskName}
          </div>
        ) : (
          '个性化设置'
        )
      }
      visible={visible}
      width="960px"
      closable={!!isOverView}
      maskClosable={isOverView}
      onClose={closeTableSet}
      destroyOnClose
      footer={
        isOverView ? (
          []
        ) : (
          <Space>
            <Button onClick={closeTableSet}>取消</Button>
            <Button type="primary" onClick={handleOk} loading={pageLoading}>
              确定
            </Button>
          </Space>
        )
      }
    >
      <Spin spinning={pageLoading}>
        <Form {...FORM_LAYOUT} form={singleForm}>
          <Collapse
            ghost
            activeKey={activeKey}
            onChange={changeActiveKey}
            expandIconPosition="right"
          >
            {SINGLE_SETTING.map((v, i) => {
              return (
                <Collapse.Panel
                  key={v.name}
                  header={
                    <div className={styles.panelHeader}>
                      <span className={styles.number}>{i + 1}</span>
                      <span className={styles.title}>{v.title}</span>
                    </div>
                  }
                  forceRender={v.name === 'advanced'}
                >
                  {v.render}
                </Collapse.Panel>
              );
            })}
          </Collapse>
        </Form>
      </Spin>
    </Drawer>
  );
};

const SingleStateToProps = ({
  batchNew: {
    layerList,
    extractTableField,
    extractTableDetail,
    tagList,
    storeTable,
  },
  loading,
}: ConnectState) => {
  return {
    layerList,
    extractTableField,
    extractTableDetail,
    tagList,
    storeTable,
    extractTableLoading: loading.effects['batchNew/getTableInfo'],
    fieldTransLoading: loading.effects['batchNew/getTransType'],
    existTableLoading: loading.effects['batchNew/getSourceTableDetail'],
    distributeLoading: loading.effects['batchNew/getDistributionKey'],
    sameTbNamesLoading: loading.effects['batchNew/getStoreTableNames'],
  };
};

type StateToProps = ReturnType<typeof SingleStateToProps>;
export default connect<StateToProps, {}, {}, ConnectState>(SingleStateToProps)(
  SingleTableDetail,
);
