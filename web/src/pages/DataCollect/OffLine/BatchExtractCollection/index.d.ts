import { ExtractTableField } from '@/models/Fusion/batchNew';

export type DistributionMode = 'HASH' | 'REPLICATED' | 'RANDOMLY' | '';
export type FileType = 'text' | 'orc' | '';
export type Policytype = 0 | 1 | 2;
export type ScheduleType = 1 | 2 | 3 | 4 | 5 | 6 | 7;
export type StrategyType = 0 | 1 | 2;

type Tag = {
  id: string;
  dirid: string;
  name: string;
  color: string;
};

type CodeContent = {
  [propName: string]: string;
};

export type OuterTaskInfo = {
  isPreview: boolean;
  overviewData: BatchDetail;
  parentRecord: {
    taskId: string;
  };
  customize: SingleDetail[];
  singles: any;
};

export type DistributionField = {
  name: string;
  number: number;
  rankname: string;
  ranknum: number;
  fieldtype: string;
  ispartition: boolean;
  isprim: boolean;
  isunique: boolean;
};

type PartitionField = {
  name: string; // 字段名称
  annotation: string; // 注释
  fieldtype: string; // 字段类型
  ispreset: 'day' | 'month' | 'year' | 'notpreset' | 'customize'; // notpreset: 非预设
};

type FieldInfo = {
  fieldname: string; // 字段名称
  fieldtype: string; // 字段类型
  fieldcomment: string; // 字段注释
  index: 0 | 1 | 2 | 3; // 0:非索引 1:普通索引 2:主键 3:唯一键
  bothprimandunique: boolean; // 是否同时为主键和唯一键
  ispartition: boolean; // 是否是分区
};

type SourceFieldInfo = {
  name: string;
  fieldName: string;
  fieldType: string;
  comment: string;
  firstData: string | undefined;
  secondData: string | undefined;
  thirdData: string | undefined;
  ispartition: boolean;
  partitionNumber: number;
  isprim: boolean;
  isPrim: boolean;
  isunique: boolean;
  rankname: string; // 敏感等级名称
  ranknum: number; // 等级数
};

type NewTableFieldRender =
  | 'fieldName'
  | 'fieldType'
  | 'comment'
  | 'index'
  | 'bothPrimAndUnique';
type NewTableFiledInfo = Pick<ExtractTableField, NewTableFieldRender> & {
  key: number;
};

interface BatchTask {
  id: string;
  customize: SingleDetail[];
}

interface SubTaskInfo {
  subtaskid: string;
  subtaskname: string;
  extracttbname: string;
  status: 0 | 1 | 2;
}

type TaskStatus = {
  taskstatus: 0 | 1 | 2;
  subsuccess: number;
  subfail: number;
  subcreating: number;
};

interface BatchDetail {
  id: string; // 任务id
  notename: string; // 任务名称
  name: string; // 同notename
  dirid: string; // 任务目录id
  dirname: string; // 任务目录名称
  contents: CodeContent | null;
  description: string; // 任务描述
  filetype: FileType; // hive存储源文件格式
  tags: Tag[]; // 任务标签
  prefix: string; // 表前缀
  suffix: string; // 表后缀
  policytype: Policytype; // 前后缀策略
  retrynum: number; // 重试次数
  retryinterval: number; // 重试间隔时长，单位为分钟
  isban: boolean; // 重试失败后是否禁用
  submitted: boolean; // 是否上线
  isbatchtask: boolean;
  globalextractdb: {
    id: string; // 数据源id
    dbname: string; // 数据源名称
    dbtype: string; // 数据源类型
    schema: string; // schema名称
    schemaid: string; // shcema id
    batchtables: string[]; // 批量采集-表列表
    batchtablesextractdbid: string[]; // 批量采集-数据源表id
    tablesinfo: {
      tablename: string; // 表名
      status: 0 | 1; // 表是否为空, 0:空表 1:非空表
    }[];
    samenametbs: {
      tbname: string; // 表名
      way: 0 | 1; // 同名表策略,0:不采集该表 1:使用已有同名表
    }[];
    filtertablenull: boolean; // 是否过滤空表
  };
  globalsourcedb: {
    layerid: string; // 分层id
    layername: string; // 分层名称
    odsenginename: string; // 引擎类型
    sourceid: string; // 存储源id
    schema: string; // schema名称
    orientation: string; // 存储方式, column:列存 row:行存
    catalogid: string | undefined; // 类目id
    taglist: Tag[]; // 表标签
    filetype: FileType; // hive存储源文件格式
    compresstype?: string; // 压缩格式
    partitionfield?: []; // 默认分区方式
  };
  globalsubmitinfo: {
    extractonlinestrategy: StrategyType; // 全局数据策略 0:覆盖 1:全量追加 2:增量追加
    scheduletype: ScheduleType; // 执行策略
    scheduledetail: string; // 策略详情，执行时间
    exectimes: number; // 执行次数 -1:不限
    // isstartnow: boolean; // 开始时间
    firststarttime?: string; // 首次开始时间
    effectstart?: string; // 生效时间
    effectend?: string;
    assignnode: string; // 运行节点ip
    joblevel: string; // 任务优先级
    preid: string[]; // 前置任务id列表
    // 失败重试
    timeoutstop?: boolean;
    timeoutduration?: any[];
  };
  customize: SingleDetail[] | null;
}

type ExistTableDetail = {
  catalogId: string;
  fileFormat: FileType;
  partitionField: PartitionField[] | null;
  distributionMode: DistributionMode;
  distributionBond: string[] | null;
  orientation: string;
  compressType: string;
  tagList: Tag[] | null;
  describe: string;
};

type SingleDetail = {
  advancedconfig: {
    ram: number;
    kbps: number;
    splitkey: string;
    concurrency: number;
    errorinfo: {
      errlogcount: number;
      errlogper: number;
    };
    isrecorderr: boolean;
    prioritymode: number;
  };
  extracttable: string; // 源表名
  sourcetable: string; // 目标表名
  gathermethod: 1 | 2; // 采集方式 1:全量，2:增量
  extractonlinestrategy: StrategyType | undefined; // 单表数据策略 0:覆盖存储数据 1:全量追加 2:增量追加
  historydata: boolean; // 清空存储表历史数据
  extractinfo: {
    tableid: string; // 数据源表id
    createfield: string; // 增量字段
    timetype: string; // 时间增量类型
    supportdirtydata: boolean; // 支持抽取异常(脏)数据
    wherecontent: string; // 过滤条件
    accincrestatus: {
      currincredata: string; // 当前增量记录时间
      issynchistory: boolean; // 同步源表历史数据
    };
    firstrecord: [][]; // 前三条数据
    fieldinfo: FieldInfo[]; // 字段信息
  };
  sourceinfo: {
    tableid: string; // 已有表id
    catalogid: string | undefined; // 类目信息
    filetype: string; // hive存储源文件格式
    tablecomment: string; // 表描述
    tableCommentForNew?: string; // 用于前端记录新建时的表描述
    taglist: Tag[]; // 表描述
    distributionmode: DistributionMode; // 分布方式
    distributionbond: string[]; // 分布键
    partitionfield: PartitionField[]; // 分区字段
    orientation: string; // 存储方式
    compresstype?: string; // 压缩格式
    compresslevel: number; // 压缩等级
    fieldinfo: FieldInfo[]; // 字段信息
  };
  tablerelation: {
    edges: {
      source: string;
      target: string;
    }[];
  } | null;
};

type SingleExtractTable = {
  dbid: string;
  dbname: string;
  dbtype: string;
  schemaid: string;
  schemaname: string;
  tableid: string;
  tablename: string;
  sourceTableName: string;
  subTaskName: string;
  hasCustomize?: boolean;
  extractComment?: string;
};

type DBDistribute = {
  distributionMode: DistributionMode;
  distributionBond: string[];
  distributionField: DistributionField[];
};

export type PartionField = {
  field: string;
  pattern: 'system' | 'field';
  type?: 'day' | 'month' | 'year';
};

export {
  BatchDetail,
  SingleDetail,
  SingleExtractTable,
  BatchTask,
  SubTaskInfo,
  TaskStatus,
  DBDistribute,
  ExistTableDetail,
  SourceFieldInfo,
  NewTableFiledInfo,
  PartitionField,
};
