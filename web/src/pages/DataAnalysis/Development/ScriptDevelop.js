/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-shadow */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-return-assign */
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { ResizePanel, LayoutHelper as Layout } from '@/components';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
// import {Layout} from '@/components/LayoutHelper';
import styles from './scriptDevelop.less';
import { FileUploadForm, AddScriptForm } from './ScriptDevelop/Modals';
// import ScriptModals from './ScriptDevelop/Modals';
import TreeMenu from './ScriptDevelop/TreeMenu';
import ScriptTable from './ScriptDevelop/ScriptTable';
import ScriptPane from './ScriptDevelop/ScriptPane';
import EditBody from './EditBody';
// eslint-disable-next-line
import 'moment/locale/zh-cn';
// eslint-disable-next-line
moment.locale('zh-cn');
// const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',');
const { Sider, Content } = Layout;

@connect(({ develop, editbody, psNotification }) => ({
  develop,
  editbody,
  psNotification, // 引擎相关
}))
export default class ScriptDevelop extends PureComponent {
  constructor(props) {
    super(props);
    this.inputSelect = React.createRef();
  }

  state = {
    // 表格与搜索
    selectedRowKeys: [],
    selectedRows: [],
    // listall: true, // 我自己 所有人
    sortMenu: ['默认排序（创建时间倒序）', '按更新时间排序', '按任务类型排序'],
    sortmenuText: '',
    // sorttype: {
    //   sorttype: 0,
    //   sort: 1,
    // },
    filterTitle: '', // 当前搜索框内的搜索内容

    // 树
    selectDir: 'listAll', // 当前选中的文件夹
    selectedKeys: ['listAll'], // 当前选中的树节点
    isReal: false,

    // tab编辑页
    panes: [],
    panesSplit: [],
    newActiveKey: '',
    fullscreen: false,
    // editorHeight: 527,
    editorWidth: '100%',
    classDiv: styles.notFullScreenDiv,
    saveArr: [], // 当前打开的编辑页保存状态

    // 模态框
    modalVisible: false,
    modalName: '',
    curDir: '', // 移动到的文件夹id

    // 其他状态
    curEngine: '', // 当前引擎
  };

  componentDidMount() {
    // const { listall } = this.state;
    const {
      dispatch,
      location: { state },
    } = this.props;
    dispatch({
      type: 'develop/searchList',
      payload: {
        // search: [],
        // sorttype,
        listall: true,
        selectDir: '',
        page: 1,
        perpage: 10000,
      },
    });
    // 获取sql脚本数据库信息
    dispatch({
      type: 'editbody/listNewDataBase',
    });
    // 这个是旧版获取sql脚本数据库的接口
    // this.props.dispatch({
    //   type: 'editbody/listDataBase',
    //   payload: {},
    // });

    this.getlistDir();
    // this.refreshTagList();
    if (state) {
      const {
        editId: { langtype, id, name, submitted },
      } = state;
      const record = {
        id,
        notename: name,
        notetype: langtype,
        submitted,
        ban: false,
        star: false,
      };
      this.gotoEditBody(record);
      // this.detailTab(name, id, langtype, submitted);
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { selectDir } = this.state;
    if (prevState.selectDir !== selectDir) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({
        selectedRows: [],
        selectedRowKeys: [],
      });
    }
  }

  // 刷新表格区数据
  refresh = () => {
    this.setState({
      selectedRowKeys: [],
      selectedRows: [],
      // filterTitle: '',
      sortmenuText: '默认排序（按创建时间排序）',
      // currentPage: 1,
    });
    const { selectDir, filterTitle } = this.state;
    const { dispatch } = this.props;
    dispatch({
      type: 'develop/searchList',
      payload: {
        // sorttype,
        listall: true,
        search: filterTitle,
        selectDir: selectDir === 'listAll' ? '' : selectDir,
        page: 1,
        perpage: 10000,
      },
    });
    this.getlistDir();
    // eslint-disable-next-line no-unused-expressions
    if (this.inputSelect) {
      const { current } = this.inputSelect;
      current && current.clearContent();
    }
    // this.inputSelect && this.inputSelect.clearContent();
  };

  /*
   * 更新树形菜单列表数据、更新模态框文件夹列表数据
   */
  getlistDir = () => {
    const { dispatch } = this.props;
    // 获取树形菜单
    dispatch({
      type: 'develop/dirSearch',
      payload: {
        listall: true,
      },
    });
  };

  setSaveArr = (saveArr) => this.setState({ saveArr });

  // 模态框隐藏
  handleModalVisible = (flag, form = null) => {
    this.setState(
      {
        modalVisible: !!flag,
        modalName: '',
        // modelRecord: {},
      },
      () => {
        form && form.resetFields();
      },
    );
  };

  // 进入脚本编辑（树+表格+新建）
  gotoEditBody = (record) => {
    const { id, notename, notetype, ban, star, submitted, dirid } = record;
    // star 表示是否被锁定，true：锁定
    const { panes, panesSplit, saveArr } = this.state;
    const curPane =
      panes.find((pane) => pane.key === id) ||
      panesSplit.find((pane) => pane.key === id);
    const newPanes = curPane
      ? panes
      : [
          ...panes,
          {
            title: notename,
            dirid,
            star,
            submitted,
            ban,
            // content
            notetype,
            disabled: !ban || star || notetype.indexOf('-') !== -1,
            key: id,
            runResult: '',
          },
        ];
    // console.log({ curPane }, { record });
    this.setState({
      panes: newPanes,
      newActiveKey: curPane ? id : '',
      // selectDir: id,
      selectedKeys: [id], // 只更新选中的树节点，保留文件夹
      saveArr: curPane
        ? saveArr
        : [
            ...saveArr,
            {
              save: true,
              key: id,
            },
          ],
    });
  };

  gotoHistoryScript = (record) => {
    const { id, notename, notetype, ban, star, submitted, dirid, version } =
      record;
    // star 表示是否被锁定，true：锁定
    const { panes, panesSplit, saveArr } = this.state;
    const curPane =
      panes.find((pane) => pane.key === id) ||
      panesSplit.find((pane) => pane.key === id);
    const newPanes = curPane
      ? panes
      : [
          ...panes,
          {
            title: `版本${version}-${notename}`,
            dirid,
            star,
            submitted,
            ban,
            // content
            notetype,
            disabled: true,
            key: id,
            runResult: '',
            isHistory: true,
            scriptDetail: record,
          },
        ];
    this.setState({
      panes: newPanes,
      newActiveKey: curPane ? id : '',
      // selectDir: id,
      selectedKeys: [id], // 只更新选中的树节点，保留文件夹
      saveArr: curPane
        ? saveArr
        : [
            ...saveArr,
            {
              save: true,
              key: id,
            },
          ],
    });
  };

  freshAll = (form) => {
    const { selectDir, filterTitle } = this.state;
    const { dispatch } = this.props;
    this.setState({
      modalVisible: false,
      selectedRows: [],
      selectedRowKeys: [],
    });
    form && form.resetFields();
    dispatch({
      type: 'develop/searchList',
      payload: {
        listall: true,
        selectDir: selectDir === 'listAll' ? '' : selectDir,
        search: filterTitle,
      },
    });
    this.getlistDir();
  };

  // 进入编辑页后在生命周期 componentDidUpdate 中调用，传入脚本的id
  // 即在 onChange 时会调用
  // handleChangeContent = key => {
  //   this.changeContect.push({
  //     key,
  //   });
  //   // debugger
  //   /*
  //    * @param:
  //    * key:             当前脚本id，
  //    * onChangeContect: [{key: ''}, {key: ''}, {key: ''}, {key: ''}]，
  //    *     其中，数组项总是比当前已打开并且已编辑过的tabs数多一个，多出的一个表示当前激活的tabs。
  //    *     当tabs被打开但未被编辑过时，不会出现在 onChangeContect 中。
  //    * */
  //   // console.log(key, this.changeContect, '进入编辑页时的handleChangeContent');
  //   const his = {};
  //   this.changeContect = this.changeContect.reduce((item, val) => {
  //     // console.log(item ,val, '每一项');
  //     const keymap = val.key;
  //     // eslint-disable-next-line
  //     his[keymap] ? '' : (his[keymap] = true && item.push(val));
  //     return item;
  //   }, []);
  // };

  setMyState = (obj, callback) => {
    this.setState(obj, callback);
  };

  setRunResult = (newResult, paneKey) => {
    const { panes, panesSplit } = this.state;
    panes.forEach((pane) => {
      if (pane.key === paneKey) {
        pane.runResult = newResult;
      }
    });
    panesSplit.forEach((pane) => {
      if (pane.key === paneKey) {
        pane.runResult = newResult;
      }
    });
    this.setState({
      panes,
      panesSplit,
    });
  };

  renderEditBody = (pane, renderAlert) => {
    // console.log({ pane });
    const popoverContent = [
      {
        title: '时间参数',
        notetype: 'sql',
      },
    ];
    if (pane.notetype !== 'sql')
      popoverContent.push({
        title: '全局参数',
        notetype: 'shell',
      });

    const { isHistory } = pane;
    return (
      <EditBody
        isHistory={isHistory}
        fullscreen={this.state.fullscreen}
        editorHeight={this.state.editorHeight}
        saveArr={this.state.saveArr}
        editorWidth={this.state.editorWidth}
        renderAlert={renderAlert}
        refresh={this.refresh}
        setSaveArr={this.setSaveArr}
        setRunResult={this.setRunResult}
        keyMap={pane.title}
        id={pane.key}
        notetype={pane.notetype}
        star={pane.star}
        submitted={pane.submitted}
        ban={pane.ban}
        runResult={pane.runResult}
        popoverContent={popoverContent}
        scriptDetail={pane.scriptDetail}
      />
    );
  };

  renderTable = () => {
    return (
      <ScriptTable
        filterTitle={this.state.filterTitle}
        panes={this.state.panes}
        // listall={this.state.listall}
        selectedRows={this.state.selectedRows}
        selectedRowKeys={this.state.selectedRowKeys}
        sortMenu={this.state.sortMenu}
        isReal={this.state.isReal}
        selectDir={this.state.selectDir}
        inputSelect={this.inputSelect} // ref
        //* 方法列表
        freshAll={this.freshAll}
        setFaState={this.setMyState}
        gotoEditBody={this.gotoEditBody}
        gotoHistoryScript={this.gotoHistoryScript}
        refresh={this.refresh}
        getlistDir={this.getlistDir}
      />
    );
  };

  modalProps = (modalName) => {
    const {
      // deprecated myDirs代表我自己的文件夹，旧需求
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      develop: { dirlist, tags, myDirs },
    } = this.props;
    const { selectedRows, curDir, curEngine } = this.state;
    const isMove = modalName === 'moveTo';
    const isNew = modalName === 'newScript';
    const isUpload = modalName === 'fileUpload';
    const payload = {
      handleModalVisible: this.handleModalVisible,
      setFaState: this.setMyState,
      freshAll: this.freshAll,
      dirList: isMove && dirlist,
      selectedRows: isMove && selectedRows,
      curDir: isMove && curDir,
      // modalVisible: isMove && modalVisible,
      curEngine: isNew && curEngine,
      gotoEditBody: isNew && this.gotoEditBody,
      dirlist: (isNew || isUpload) && dirlist,
      // dirlist: (isNew || isUpload) && myDirs, // deprecated
      allTags: isNew && tags,
    };
    return payload;
  };

  render() {
    const {
      selectDir,
      selectedKeys,
      // selectedRows,
      // selectedRowKeys,
      sortMenu,
      sortmenuText,
      filterTitle,
      panes,
      panesSplit,
      fullscreen,
      newActiveKey,
      classDiv,
      // curDir,
      isReal,
      modalVisible,
      // modelRecord,
      modalName,
      // listall,
      saveArr,
      // curEngine,
    } = this.state;

    const breadcrumbList = [
      {
        title: '工作流',
        href: '/danastudio/#/customProcess/scriptdevelopment/info',
      },
      {
        title: '脚本开发',
      },
    ];

    return (
      <PageHeaderLayout breadcrumbList={breadcrumbList}>
        <Layout>
          <Sider>
            <ResizePanel
              content={
                <TreeMenu
                  // listall={listall}
                  selectDir={selectDir}
                  selectedKeys={selectedKeys}
                  sortMenu={sortMenu}
                  sortmenuText={sortmenuText}
                  filterTitle={filterTitle}
                  isReal={isReal}
                  panes={panes}
                  setFaState={this.setMyState}
                  gotoEditBody={this.gotoEditBody}
                  freshAll={this.freshAll}
                />
              }
            />
          </Sider>
          <Content style={{ padding: 0 }} className={classDiv}>
            <ScriptPane
              filterTitle={this.state.filterTitle}
              selectedRows={this.state.selectedRows}
              selectedRowKeys={this.state.selectedRowKeys}
              // listall={listall}
              isReal={isReal}
              selectDir={selectDir}
              fullscreen={fullscreen}
              classDiv={classDiv}
              newActiveKey={newActiveKey}
              panes={panes}
              panesSplit={panesSplit}
              saveArr={saveArr}
              setFaState={this.setMyState}
              renderEditBody={this.renderEditBody}
              renderTable={this.renderTable}
            />
          </Content>
        </Layout>
        <FileUploadForm
          {...this.modalProps('fileUpload')}
          modalVisible={modalVisible && modalName === 'fileUpload'}
        />
        <AddScriptForm
          {...this.modalProps('newScript')}
          modalVisible={modalVisible && modalName === 'newScript'}
        />
      </PageHeaderLayout>
    );
  }
}
