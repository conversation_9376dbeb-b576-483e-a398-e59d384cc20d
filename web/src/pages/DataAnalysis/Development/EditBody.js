/* eslint-disable class-methods-use-this */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable react/sort-comp */
/* eslint-disable no-shadow */
/* eslint-disable react/no-find-dom-node */
/* eslint-disable react/no-string-refs */
/* eslint-disable jsx-a11y/no-noninteractive-tabindex */
import React, { PureComponent } from 'react';
import { connect } from 'dva';
// import ReactDOM from 'react-dom';
import AceEditor from 'react-ace';
import { routerRedux } from 'dva/router';
import 'brace/ext/language_tools';
import 'brace/ext/searchbox';
import { Form } from '@ant-design/compatible';
import ResizeObserver from 'rc-resize-observer';
import '@ant-design/compatible/assets/index.css';
import { Select, notification, Spin, Space, Tooltip } from 'antd';
import { encode } from 'js-base64';
// import ScrollArea from 'react-scrollbar/dist/no-css';
import { TextButton, DSIcon, IconFont } from '@/components';
import { correctName } from '@/utils';
import {
  optionLanguages,
  mockSQL,
} from '@/pages/DataAnalysis/Development/sqlUtils';
import { renderStopTooltip } from '@/pages/Workflow/utils';
import DataBaseTree from '@/pages/DataAnalysis/ModelManage/components/DataBaseTreeSelect';
import ParamSet from './include/SideBar';
import styles from './editbody.less';
import EditorResult from './include/EditorResult';

// import CascaderMenu from './CascaderMenu';
import AutoSave from './AutoSave';

const { Option } = Select;

const languages = ['sh', 'python', 'php', 'sql'];

const themes = [
  'monokai',
  'github',
  'tomorrow',
  'kuroir',
  'twilight',
  'xcode',
  'textmate',
  'solarized_dark',
  'solarized_light',
  'terminal',
];
// const tipsData = [
//   {
//     title: '时间参数',
//     message: [
//       '该脚本如有动态时间参数需要注册，需要注意时间参数会替换为 实际执行的时间。且其中每个参数可以指定特定的时间格式，格 式大致分为日期和时间两种。',
//       '输出内容实例',
//       'VariableTime = yyyy-MM-dd hh:mm:ss',
//       'VariableDate = yyyy-MM-dd',
//     ],
//   },
//   {
//     title: '全局参数',
//     message: [
//       '该脚本如有变量需要传递，必须保持前后组件中变量名称一致， 请在右侧参数列表中填写变量名称，并以标准输出逐行输出变量 名称和变量值。',
//       '输出内容实例',
//       'VariableName1 = value1',
//       'VariableName2 = value2',
//     ],
//   },
// ];
languages.forEach((lang) => {
  require(`brace/mode/${lang}`);
  require(`brace/snippets/${lang}`);
});

themes.forEach((theme) => {
  require(`brace/theme/${theme}`);
});

@Form.create()
@connect(({ develop, editbody, user, loading }) => ({
  develop,
  editbody,
  user,
  addLoading: loading.effects['editbody/addScript'],
  saveLoading: loading.effects['editbody/saveScript'],
  runLoading: loading.effects['editbody/runScript'],
  getRunResultLoading: loading.effects['editbody/getRunscript'],
  stopLoading: loading.effects['editbody/stopScript'],
  contentLoading: loading.effects['editbody/fetchContent'],
  lockLoading: loading.effects['develop/clickStar'],
  versionLoading: loading.effects['editbody/getVersion'],
}))
export default class EditBody extends PureComponent {
  constructor(props) {
    super(props);
    this.changeContect = false;
    // this.$$itemRef = React.createRef();
    this.state = {
      theme: 'monokai',
      // language:
      //   (this.props.notetype === 'shell' ? 'sh' : this.props.notetype) || 'sh',
      isEdit: false,
      // classDiv: this.props.classDiv,
      // modalVisible: false,

      // exchange: false,
      // panelHeight: 'inherit',

      delaySaveTime: 0, // 延迟保存的时间
      content: '',
      memorylimit: 0, // 运行内存
      sqlname: '',
      enginename: '',
      // variables: [],

      // tags: [],
      // dirlist: [],
      // isRunningId: '',

      // isSave: false, // 是否保存
      // isRun: false, // 是否运行
      isStop: false, // 是否停止

      isLoadingRunScript: false,
      isSaveScript: false,
      // resetCheck: false,

      // 是否被锁定
      isLock: this.props.editbody.star,
      // 是否可编辑（即是否未上线或上线但处于未被禁用状态），新建的脚本默认可编辑，即 ban = true
      // ban: this.props.editbody.ban || true,
      ban: this.props.ban,
      isKettle: this.props.notetype.indexOf('-') !== -1,
      isSubmitted: this.props.submitted || false, // 是否上线
      // iconVisible: true, // 前往运维
      // identify: true,
      // isOnline: !this.props.submitted || true, // 运行上线
      // modelRecord: {},
      // runResult: this.props.runResult || '',
      pid: '',
      showData: false,
      // 新增参数表格数据源
      dataSource: [
        {
          key: 0,
          value: '',
          type: 'yyyy-MM-dd HH:mm:ss',
        },
        {
          key: 1,
          value: '',
          type: 'yyyy-MM-dd HH:mm:ss',
        },
        {
          key: 2,
          value: '',
          type: 'yyyy-MM-dd HH:mm:ss',
        },
      ],
      shellDataSource: [
        {
          key: 0,
          value: '',
        },
        {
          key: 1,
          value: '',
        },
        {
          key: 2,
          value: '',
        },
      ],
      // 输出结果
      // flag: 1,
      // // editorHeight: '100%',
      // funcinfoHeight: 184,
      display: 'flex',

      // timeInter·val: '',
      addType: '',
      aceHeight: 339,
      openStatus: 0,

      curNoteType: '',

      dbname: '',
      sqlData: {
        engineid: '',
        initdb: '',
        layerid: '',
      },
    };
  }

  componentDidMount() {
    document.body.classList.add('CascaderMenu');
    const {
      id,
      notetype,
      develop: { names },
      isHistory,
      scriptDetail,
    } = this.props;
    // console.log({ names });
    if (id && notetype && !isHistory) {
      this.fetchContent(names);
      if ((names && names.length === 0) || !names) {
        console.error('参数names存在问题，其值为:', names);
      }
    }
    if (isHistory) {
      this.setState({
        // sqlname: engchanged ? '' : res.name,
        enginename: scriptDetail.enginename,
        showData: scriptDetail.notetype === 'sql',
        curNoteType: scriptDetail.notetype,
        content: scriptDetail.content || '',
        dbname: scriptDetail.sourcedbname,
        sqlData: {
          engineid: scriptDetail.engineid,
          initdb: scriptDetail.initdb,
          layerid: scriptDetail.layerid,
        },
        memorylimit: scriptDetail.memorylimit || 0,
      });
    }
  }

  /*
   * TODO 生命周期
   * 调用父组件的 onChangeContent，传入当前组件的id，或者未保存的该文件的时间戳
   * */
  componentDidUpdate() {
    // deprecated onChangeContent在父组件中已废弃，用途不明，应该和未保存的脚本删除有关，此需求已有其他逻辑处理
    const { onChangeContent, keyMap, id } = this.props;
    const { addType } = this.state;
    if (onChangeContent && this.changeContect === true) {
      onChangeContent(id || keyMap);
    }
    this.scrollTable(addType, id);
  }

  databaseErr = () => {
    notification.warning({
      message: '导入脚本数据库已变更，请重新选择运行数据库',
    });
  };

  dealEditStatus = (flag) => {
    const { id, saveArr, setSaveArr } = this.props;
    setSaveArr(
      saveArr.map((item) => {
        return {
          ...item,
          save: item.key === id ? !flag : item.save,
        };
      }),
    );
    this.setState({
      isEdit: flag,
    });
  };

  fetchContent = (names) => {
    const { dispatch, id, notetype } = this.props;
    dispatch({
      type: 'editbody/fetchContent', // 获取实验脚本内容接口
      payload: {
        id,
        names,
      },
    }).then((res) => {
      // const { content, sqlname = '', enginename = '', variables, identify, ban } = this.props.editbody;
      const {
        content,
        initdb = '',
        // sqlname = '',
        engineid = '',
        enginename = '',
        variables,
        timevars,
        // identify,
        ban,
        memorylimit = 0,
        finish,
        engchanged,
        star: lock,
        actualnotetype,
        submitted,
      } = res;

      if (notetype === 'sql') {
        if (engchanged) {
          this.dealEditStatus(true);
          this.databaseErr();
        }
        let curNoteType = actualnotetype;
        // 这里处理从模型管理过来的sql脚本，没有actualnotetype，但是有enginename
        if (!actualnotetype && enginename) {
          curNoteType = mockSQL(enginename, correctName(notetype));
        }
        this.setState({
          sqlname: engchanged ? '' : res.name,
          enginename: engchanged ? '' : enginename,
          showData: notetype === 'sql',
          curNoteType: curNoteType || notetype,
          // collapsed: false,
          content,
          ban,
          // isSave: lock ? true : false,
          // isRun: !!lock,
          isStop: !!lock,
          isSubmitted: submitted,
          isLock: lock,
          // iconVisible: false,
          // identify,
          dbname: res.sourcedbname,
          sqlData: {
            engineid: engchanged ? '' : engineid,
            initdb: engchanged ? '' : initdb,
            layerid: res.layerid,
          },
        });
      } else {
        // 全局参数
        const { shellDataSource } = this.state;
        // const newShellDataSource = [];
        // let timevars;
        // const newarr = [];
        if (variables) {
          const newVariables =
            variables.length === 0
              ? shellDataSource
              : variables.map((item, index) => {
                  return {
                    key: index,
                    value: item,
                  };
                });
          this.setState({
            shellDataSource: newVariables,
          });
        }
        this.setState({
          content,
          // isSave: !!lock,
          // isRun: !!lock,
          memorylimit,
          isStop: !!lock,
          isSubmitted: submitted,
          isLock: lock,
          // iconVisible: false,
          ban,
          // identify,
        });
      }
      // 时间参数
      const { dataSource } = this.state;
      if (timevars) {
        const newTimevars =
          timevars.length === 0
            ? dataSource
            : timevars.map(({ name, type }, index) => {
                return {
                  key: index,
                  value: name,
                  type,
                };
              });
        this.setState({
          dataSource: newTimevars,
        });
      }

      // #21939
      if (!finish) {
        this.loopRunScript();
      }
    });
  };

  onLoad = (ace) => {
    const that = this;
    ace.commands.addCommand({
      name: 'fScreen',
      bindKey: { win: 'Ctrl-Enter', mac: 'Command-Enter' },
      exec(editor) {
        if (!that.state.fullscreen) {
          that.setState({
            fullscreen: true,
            editorHeight: `${document.documentElement.clientHeight}px`,
          });
          that.refs.fullscreenDiv.style.position = 'fixed';
          that.refs.fullscreenDiv.style.top = 0;
          that.refs.fullscreenDiv.style.left = 0;
          that.refs.fullscreenDiv.style.width = '100%';
          editor.resize();
        } else {
          that.refs.fullscreenDiv.style.position = 'relative';
          document.getElementById('scriptDevelop').style.position = 'relative';
          that.setState({
            fullscreen: false,
            editorHeight: '380px',
            // editorWidth: '100%',
          });
          editor.resize();
        }
      },
    });
  };

  /* TODO 编辑内容实时触发 */
  onChange = (newValue) => {
    this.setState(
      {
        content: newValue,
      },
      () => {
        this.changeContect = true;
        this.dealEditStatus(true);
        // 实时保存。第二个参数表示是否为实时保存
        // this.handleModalVisible(true, true);
      },
    );
  };

  scrollTable = (addType, id) => {
    const tableBody = document.querySelectorAll(
      `#${addType}_${id} .ant-table-tbody`,
    )[0];
    const tableScroll = document.querySelectorAll(
      `#${addType}_${id} .ant-table-body`,
    )[0];
    if (tableScroll && tableBody) {
      const { scrollTop } = tableScroll;
      const { offsetHeight } = tableBody;
      const { clientHeight } = tableScroll;
      const { scrollHeight } = tableScroll;
      const isBottom = scrollTop + clientHeight === scrollHeight;
      if (clientHeight !== offsetHeight && !isBottom) {
        tableScroll.scrollTop = scrollHeight;
      }
    }
  };

  hoverSelect = (item) => {
    this.setState({
      theme: item,
    });
  };

  onSave = (key, { resolve, reject }) => {
    this.handleModalVisible(true, false, { resolve, reject });
  };

  autoSave = async () => {};

  getVersion = async () => {
    this.handleModalVisible(true, false, undefined, () => {
      const { dispatch, id } = this.props;
      // 调用生成版本接口
      dispatch({
        type: 'editbody/getVersion', // 获取实验脚本内容接口
        payload: {
          id,
        },
      }).then((res) => {
        if (res.code === 200) {
          notification.success({ message: '版本生成成功' });
        }
      });
    });
  };

  /* TODO 首次点击保存脚本后出现弹窗 */
  handleModalVisible = (
    flag,
    sync = false,
    param = {},
    callBack = undefined,
  ) => {
    // flag为true: 弹出弹框，保存事件转递给弹框中的确定按钮；false：执行保存操作
    const { resolve = () => {}, reject = () => {} } = param;
    if (flag) {
      const {
        dispatch,
        id,
        form,
        notetype,
        develop: { names },
      } = this.props;
      // console.log(this.props, '是否是首次点击');
      if (id) {
        const {
          content,
          memorylimit,
          dataSource,
          shellDataSource,
          enginename,
          curNoteType,
          sqlData,
          dbname,
        } = this.state;
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const param = {};
        param.id = id;
        param.content = encode(content);
        param.encode = 'base64';
        // 保存参数设置表单值
        form.validateFields((err) => {
          if (err) {
            reject();
            return;
          }
          const shellArr = shellDataSource
            .map((v) => v.value)
            .filter((item) => item !== '');
          const timevars = dataSource.map((item) => item.value);
          const timevarsIsInclude = timevars.filter(
            (v) => !content.includes(v),
          );
          if (timevarsIsInclude.length > 0 && !sync) {
            notification.warning({
              message: `${timevarsIsInclude.join(
                '、',
              )}在脚本中没有出现，请检查编辑！`,
            });
            reject();
            return;
          }
          param.timevars = dataSource
            .map(({ value, type }) => {
              return {
                name: value,
                type,
              };
            })
            .filter((v) => v.name !== '' && v.type !== '');
          if (notetype === 'sql') {
            if (enginename === '' || !sqlData) {
              this.databaseErr();
              reject();
              return;
            }
            param.sqlid = sqlData.engineid;
            param.sourcedbname = dbname;
            param.initdb = sqlData.initdb;
            param.layerid = sqlData.layerid;
            param.actualnotetype = curNoteType;
          } else if (notetype.includes('python')) {
            param.memorylimit = memorylimit;
          } else {
            param.variables = shellArr;
          }
          this.setState(
            {
              isSaveScript: true,
            },
            () => {
              dispatch({
                type: 'editbody/saveScript',
                payload: { ...param, names },
              }).then((data) => {
                this.setState({
                  isSaveScript: false,
                });
                if (callBack) {
                  callBack();
                }
                if (!sync) {
                  if (data === 'success') {
                    notification.success({
                      message: '保存脚本成功',
                    });
                    this.dealEditStatus(false);
                    this.refresh();
                    resolve();
                  } else {
                    notification.error({
                      message: '保存脚本失败',
                    });
                    reject();
                  }
                }
              });
            },
          );
        });
        this.changeContect = false;
      } else {
        if (!sync) {
          dispatch({
            type: 'editbody/listDir',
            payload: {
              module: 'develop',
            },
          }).then(() => {
            // this.setState({
            //   dirlist: this.props.editbody.dirlist,
            // });
            this.dealEditStatus(false);
          });
        }
        dispatch({
          type: 'editbody/getTagslist',
          payload: {
            // listall,
          },
        }).then(() => {
          resolve();
          // this.setState({
          //   tags: this.props.editbody.tags,
          // });
        });
        // 第一次保存时出现模态框
        // this.setState({
        //   modalVisible: flag,
        // });
      }
    } else {
      // this.setState({
      //   modalVisible: flag,
      // });
    }
  };

  refresh = () => {
    if (this.props.refresh) {
      this.props.refresh();
    }
  };

  runErrHandle = (data) => {
    notification.error({
      message: '脚本运行失败',
      description: data,
    });
    this.setState({
      isLoadingRunScript: false,
      isSaveScript: false,
    });
  };

  // 运行脚本
  runScript = (sync = false) => {
    const {
      dispatch,
      id,
      form,
      notetype,
      develop: { names },
    } = this.props;
    // console.log({ names }, 'run');
    const {
      content,
      // timeInterval,
      memorylimit,
      dataSource,
      shellDataSource,
      curNoteType,
      sqlData,
      dbname,
    } = this.state;
    const param = {};
    param.id = id;
    param.content = encode(content);
    param.encode = 'base64';
    // 保存参数设置表单值
    form.validateFields((err) => {
      if (err) return;
      const shellArr = shellDataSource
        .map((v) => v.value)
        .filter((item) => item !== '');
      const timevars = dataSource.map((item) => item.value);
      const timevarsIsInclude = timevars.filter((v) => !content.includes(v));
      if (timevarsIsInclude.length > 0 && !sync) {
        notification.warning({
          message: `${timevarsIsInclude.join(
            '、',
          )}在脚本中没有出现，请检查编辑！`,
        });
        return;
      }
      this.setState({
        openStatus: 1,
      });
      param.timevars = dataSource
        .map(({ value, type }) => {
          return {
            name: value,
            type,
          };
        })
        .filter((v) => v.name !== '' && v.type !== '');
      if (notetype === 'sql') {
        param.sqlid = sqlData.engineid;
        param.sourcedbname = dbname;
        param.initdb = sqlData.initdb;
        param.layerid = sqlData.layerid;
        param.actualnotetype = curNoteType;
      } else if (notetype.includes('python')) {
        param.memorylimit = memorylimit;
      } else {
        param.variables = shellArr;
      }
      notification.success({
        message: '脚本开始运行',
        duration: 1,
      });
      dispatch({
        type: 'editbody/cleanLog',
        payload: {
          id,
        },
      });
      this.setState(
        {
          isLoadingRunScript: true,
          isSaveScript: true,
        },
        () => {
          dispatch({
            type: 'editbody/saveScript',
            payload: { ...param, names },
          }).then((data) => {
            if (data === 'success') {
              this.dealEditStatus(false);
              this.setState({ isSaveScript: false });
              dispatch({
                type: 'editbody/runScript',
                payload: {
                  id,
                  content: encode(content),
                  names,
                  encode: 'base64',
                },
              })
                // eslint-disable-next-line @typescript-eslint/no-shadow
                .then((data) => {
                  if (data === 'success') {
                    document.body.scrollTop = document.body.scrollHeight;
                    this.setState(
                      {
                        pid: this.props.editbody.pid,
                        // isRun: true,
                        isStop: false,
                        // isOnline: false,
                        // isLoadingRunScript: false,
                        // 使用定时器循环调用比直接循环性能更高，且可手动停止，无需被动停止。
                        // timeInterval: setInterval(this.loopRunScript, 1000),
                      },
                      () => {
                        this.refresh();
                        this.loopRunScript();
                      },
                    );
                  } else {
                    this.runErrHandle(data);
                  }
                })
                .catch(() => {
                  this.runErrHandle();
                });
            } else {
              notification.error({
                message: '保存脚本失败',
              });
              this.setState({
                isLoadingRunScript: false,
                isSaveScript: false,
              });
            }
          });
        },
      );
    });
    this.changeContect = false;
  };

  // 之所以要循环调用，是因为后端无法一次性返回，需要通过runFinish字段判断是否取得结果
  loopRunScript = async () => {
    const {
      dispatch,
      id,
      editbody: { pid },
    } = this.props;
    const { isStop, isLoadingRunScript, openStatus } = this.state;
    if (isStop) {
      return;
    }

    if (!isLoadingRunScript) {
      this.setState({ isLoadingRunScript: true, pid });
    }

    if (openStatus !== 1) {
      this.setState({ openStatus: 1 });
    }

    await dispatch({
      type: 'editbody/getRunscript',
      payload: {
        id,
      },
    }).then((res) => {
      const { runFinish } = res;
      const {
        editbody: { sqlname },
      } = this.props;
      // setRunResult(runResult, id);
      // this.setState({
      //   runResult,
      // });
      if (runFinish) {
        this.setState({
          // isRun: false,
          isStop: true,
          // isOnline: true,
          isLoadingRunScript: false,
          // timeInterval: '',
          sqlname,
        });
      } else {
        setTimeout(() => {
          this.loopRunScript();
        }, 1000);
      }
    });
  };

  // 停止运行脚本
  stopScript = () => {
    const { dispatch, id } = this.props;
    const { pid } = this.state;
    if (pid) {
      // clearInterval(timeInterval);
      dispatch({
        type: 'editbody/stopScript',
        payload: {
          id,
        },
      }).then((data) => {
        if (data === 'success') {
          // const newRes = {
          //   ...runResult,
          //   finish: true,
          // };
          // setRunResult(newRes, id);
          this.setState(
            {
              // isRun: false,
              isStop: true,
              isLoadingRunScript: false,
              // runResult: newRes,
              // isOnline: true,
              // timeInterval: '',
            },
            () => {
              this.refresh();
            },
          );
          notification.success({
            message: '停止脚本成功',
          });
        } else {
          notification.error({
            message: '停止脚本失败',
          });
        }
      });
    } else {
      notification.error({
        message: '还未运行脚本！',
      });
    }
  };

  stopUnmoutScript = () => {
    const { dispatch, id } = this.props;
    const { pid } = this.state;
    if (pid) {
      // clearInterval(timeInterval);
      dispatch({
        type: 'editbody/stopScript',
        payload: {
          id,
        },
      });
      // this.setState({
      //   timeInterval: '',
      // });
    }
  };

  goOperation = (id) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'editbody/goOperation',
      payload: {
        ids: [id],
      },
    }).then(() => {
      const { goOperationList } = this.props.editbody;
      if (goOperationList.length < 1) {
        notification.error({
          message: '无上线数据，不可前往运维！',
        });
        return;
      }
      dispatch(
        routerRedux.push({
          pathname: '/operations/job/cycle/task',
          state: { cycleIds: goOperationList },
        }),
      );
    });
  };

  resetAddType = () => this.setState({ addType: '' });

  /* TODO 点击(保存)按钮后弹出弹窗
   * hasInfo: 是否有提示信息
   * */
  targetA = (value, id, hasInfo) => {
    this.resetAddType();
    switch (value) {
      case 'save':
        this.handleModalVisible(true, hasInfo);
        break;
      case 'run':
        this.runScript();
        break;
      case 'stop':
        this.stopScript();
        break;
      case 'goto':
        this.goOperation(id);
        break;
      case 'lock':
        this.scriptLocked();
        break;
      case 'version':
        this.getVersion(id);
        break;
      default:
        break;
    }
  };

  // 级联菜单数据选择时
  // onChangeCascaderMenu = (list) => {
  //   // console.log(list, 'list');
  //   const enginename = list[0];
  //   const sqlid = list[1];
  //   const initdb = list[2];
  //   this.setState(
  //     {
  //       enginename,
  //       sqlid,
  //       initdb,
  //     },
  //     () => {
  //       // sql脚本在重新选择数据库参数后调用实时保存
  //       this.targetA('save');
  //     },
  //   );
  // };

  /*
   * TODO 编辑器锁定
   * */
  scriptLocked = () => {
    const {
      dispatch,
      id,
      develop: { names },
    } = this.props;
    dispatch({
      type: 'develop/clickStar',
      payload: {
        id,
      },
    }).then((res) => {
      if (res === 'success') {
        dispatch({
          type: 'editbody/fetchContent', // 获取实验脚本内容接口
          payload: {
            id,
            names,
          },
        }).then((response) => {
          this.setState({
            isLock: response.star,
          });
        });
      }
    });
  };

  setOpenStatus = (openStatus) => {
    this.setState({
      openStatus,
    });
  };

  getACEmode = () => {
    const { notetype } = this.props;
    switch (notetype) {
      case 'shell':
        return 'sh';
      case 'python3':
        return 'python';
      default:
        return notetype;
    }
  };

  showGide = (language) => {
    switch (language) {
      case 'EN':
        window.open('https://prestodb.io/docs/current/index.html');
        break;
      case 'CH':
        window.open('http://prestodb.jd.com/docs/current/index.html');
        break;
      case undefined:
        window.open('https://spark.apache.org/docs/2.3.2/api/sql/index.html');
        break;
      default:
    }
  };

  getEngine = (str) => {
    if (str === 'spark') {
      return 'Spark';
    }
    return 'Presto';
  };

  componentWillUnmount() {
    const { id } = this.props;
    if (id) {
      this.stopUnmoutScript();
    }
    document.body.classList.remove('CascaderMenu');
  }

  globalVarJudge = (type) => {
    const {
      develop: { globalVars },
    } = this.props;
    const { workflows, dynamicVars, constVars, timeVars } = globalVars;
    // 6、针对所有类型，静态参数（常量&时间参数）：
    //     1、未配置静态参数，允许单独运行
    //     2、脚本被导入多个工作流，但凡设置了静态参数，不可点击运行
    // 针对 Python 和 SQL 类型，动态参数（动态参数只对 Python 和 SQL 生效）：
    //     1、未配置动态参数时，允许单独运行
    //     2、配置动态参数时，不允许单独运行
    if (workflows.length > 1) {
      if (constVars.length + timeVars.length > 0) {
        return true;
      }
    }
    if (type === 'python' || type === 'python3' || type === 'sql') {
      if (dynamicVars.length > 0) {
        // 存在动态参数不运行
        return true;
      }
    }
    return false;
  };

  onTypeChange = (val) => {
    this.setState({
      curNoteType: val,
      isEdit: true,
      sqlData: undefined,
    });
    // dealLoadingArr(id, true);
  };

  onMemoryChange = (v) => {
    this.setState({
      memorylimit: v,
      isEdit: true,
    });
  };

  onSelectDataBase = (value, item) => {
    if (item.title) {
      this.setState(
        {
          isEdit: true,
          dbname: item.title,
          enginename: item.enginename,
          sqlData: {
            engineid: item.engineid,
            initdb: item.initdb,
            layerid: item.layerid,
          },
        },
        () => {
          // sql脚本在重新选择数据库参数后调用实时保存
          this.targetA('save');
        },
      );
    }
  };

  getDefaultValue = (option, layerId, treeNodeTitle) => {
    let initdbId;
    option.forEach((item) => {
      // console.log(item, value[0]);
      // engineID不具备唯一性，要用layerID判断
      if (item.id === layerId) {
        item.children?.forEach((child) => {
          // console.log(child.sourcename, treeNodeTitle);
          if (child.sourcename === treeNodeTitle && child) {
            initdbId = child.id;
          }
        });
      }
    });
    return initdbId;
  };

  render() {
    const {
      // addLoading,
      // saveLoading, // 分屏状态下来自dva的props会共享
      // runLoading, // 分屏状态下来自dva的props会共享
      // getRunResultLoading,
      stopLoading,
      lockLoading,
      // fullscreen,
      // editorHeight,
      // editorWidth,
      // classDiv,
      id,
      notetype,
      editbody: { curEngine, newDataBaseOption },
      user: { canCurrentPageEdit: canEditProps },
      // form,
      // popoverContent,
      renderAlert,
      contentLoading,
      keyMap,
      versionLoading,
      isHistory,
    } = this.props;
    const canEdit = canEditProps && !isHistory;
    // console.log(workflows, 'workflows');
    const {
      theme,
      // language,
      delaySaveTime,
      content,
      memorylimit,
      ban,
      isKettle,

      // exchange,
      // panelHeight,

      // isRunningId,

      // isRun,
      isEdit,
      isStop,
      isLock,
      isSubmitted,

      isLoadingRunScript,
      isSaveScript,

      showData,

      sqlname,
      enginename,
      // runResult,
      // identify,
      // collapsed,
      // dataSource,
      // shellDataSource,

      // flag,
      // contentHeight,
      // funcinfoHeight,
      display,
      openStatus,
      // addType,
      sqlData,
      dbname,
    } = this.state;

    // TODO 绑定快捷键
    const handleScriptKeyDown = (event) => {
      const { ctrlKey, shiftKey, altKey, metaKey, keyCode } = event;
      // Ctrl S
      if (keyCode === 83 && ctrlKey && !shiftKey && !altKey && !metaKey) {
        event.preventDefault();
        this.targetA('save', null, true);
      }
    };
    // const tips = (
    //   <div style={{ width: 400 }}>
    //     {tipsData.map((item) => {
    //       return (
    //         <div
    //           className={styles.tipsBox}
    //           style={{
    //             display:
    //               item.title === '全局参数' && notetype === 'sql'
    //                 ? 'none'
    //                 : 'block',
    //           }}
    //         >
    //           <p className={styles.tipsTitle}>{item.title}</p>
    //           {item.message.map((v) => {
    //             return <p className={styles.tipsList}>{v}</p>;
    //           })}
    //         </div>
    //       );
    //     })}
    //     <Checkbox
    //       defaultChecked={!!window.localStorage.getItem('workdevscr')}
    //       style={{ marginTop: 8 }}
    //       onChange={(event) => {
    //         window.localStorage.setItem(
    //           'workdevscr',
    //           event.target.checked ? '1' : '',
    //         );
    //       }}
    //     >
    //       不再自动弹出该提示
    //     </Checkbox>
    //   </div>
    // );
    // 这里使用notetype是因为 发现python类型，curNoteType拿不到数据
    const curNoteType = this.state.curNoteType || notetype;

    const stopDisabled =
      !ban ||
      isStop ||
      isLock ||
      isKettle ||
      !isLoadingRunScript ||
      isSubmitted;

    let defaultSql;
    // 兼容旧数据
    if (curNoteType === 'sql') {
      defaultSql =
        enginename === ''
          ? undefined
          : `${correctName(enginename)}/${sqlname}/${sqlData.initdb}`;
    } else if (sqlData && dbname) {
      defaultSql = this.getDefaultValue(
        newDataBaseOption,
        sqlData.layerid,
        dbname,
      );
    }

    // console.log('curNoteType', curNoteType, dbname, defaultSql);

    return (
      <EditorResult
        notetype={notetype}
        // runResult={runResult}
        id={id}
        openStatus={openStatus}
        setOpenStatus={this.setOpenStatus}
        notename={keyMap}
        enginename={enginename}
        // loading={isLoadingRunScript}
      >
        {/* TODO 编辑区菜单栏 */}
        <div className={styles.tools}>
          {/* <Row type="flex"> */}
          <div className={styles.left}>
            {/* deprecated identify */}
            {canEdit ? (
              <>
                <TextButton
                  style={{ marginRight: 12 }}
                  onClick={() => this.targetA('save')}
                  disabled={!ban || isLock || !isEdit || isSubmitted}
                >
                  <IconFont
                    type="icon-save"
                    loading={isSaveScript}
                    text="保存"
                  />
                </TextButton>
                <TextButton
                  style={{ marginRight: 12 }}
                  onClick={() => this.targetA('run')}
                  disabled={
                    !ban ||
                    isLock ||
                    isKettle ||
                    isSubmitted ||
                    isLoadingRunScript ||
                    this.globalVarJudge(notetype)
                  }
                  id={id}
                >
                  <IconFont
                    type="icon-play-circle"
                    loading={isLoadingRunScript}
                    text="运行"
                  />
                </TextButton>
                {renderStopTooltip(stopDisabled)(
                  <TextButton
                    style={{ marginRight: 12 }}
                    disabled={stopDisabled}
                    onClick={() => this.targetA('stop')}
                    /* disabled={!runLoading} */
                  >
                    <IconFont
                      loading={stopLoading}
                      type="icon-pause-circle"
                      text="停止"
                    />
                  </TextButton>,
                )}

                <TextButton
                  style={{ marginRight: 12 }}
                  onClick={() => this.targetA('lock')}
                  disabled={!ban || isSubmitted}
                >
                  <IconFont
                    loading={lockLoading}
                    type={!isLock ? 'icon-lock' : 'icon-unlock'}
                    text={!isLock ? '锁定' : '解锁'}
                  />
                </TextButton>
                <TextButton
                  style={{ marginRight: 12 }}
                  onClick={() => this.targetA('version')}
                  disabled={!ban || isLock || isSubmitted}
                >
                  <DSIcon
                    type="icon-versionmanage"
                    style={{ color: '#1980ff' }}
                    loading={isSaveScript || versionLoading}
                    text="生成版本"
                  />
                </TextButton>
              </>
            ) : null}
          </div>
          <div size="large" className={styles.rightButton}>
            {showData && enginename === 'hive' && curEngine && (
              <div>
                <span>引擎：</span>
                {this.getEngine(curEngine)}
                <span style={{ color: '#aaa' }}>
                  （请使用
                  {this.getEngine(curEngine)}
                  语法）
                </span>
                <Tooltip
                  title={
                    <div style={{ textAlign: 'center' }}>
                      {this.getEngine(curEngine) === 'Spark' ? (
                        <TextButton onClick={() => this.showGide()}>
                          Spark函数使用指导手册
                        </TextButton>
                      ) : (
                        <>
                          <div>Presto语法手册</div>
                          <Space>
                            <TextButton onClick={() => this.showGide('EN')}>
                              英文版
                            </TextButton>
                            <TextButton onClick={() => this.showGide('CH')}>
                              中文版
                            </TextButton>
                          </Space>
                        </>
                      )}
                    </div>
                  }
                >
                  <IconFont
                    type="icon-question-circle"
                    isTip
                    // className={`${styles.icon} ${styles.iconfont}`}
                  />
                </Tooltip>
              </div>
            )}
            {notetype.includes('python') && (
              <div>
                <span>运行内存：</span>
                {canEdit ? (
                  <>
                    <Select
                      size="small"
                      value={memorylimit}
                      style={{ width: 80 }}
                      disabled={isSubmitted}
                      onChange={this.onMemoryChange}
                    >
                      {[...new Array(33).keys()].map((v) => (
                        <Option key={v.toString()} value={v}>
                          {v ? `${v}G` : '无限制'}
                        </Option>
                      ))}
                    </Select>
                    <Tooltip title="当任务运行占用内存超过设定值时，任务会被强行终止以释放资源">
                      <span style={{ position: 'relative', top: 2 }}>
                        <IconFont
                          type="icon-question-circle"
                          style={{ color: '#333', marginLeft: 8 }}
                        />
                      </span>
                    </Tooltip>
                  </>
                ) : (
                  <div>{memorylimit ? `${memorylimit}G` : '无限制'}</div>
                )}
              </div>
            )}
            <div>
              <span>脚本类型：</span>
              {!canEdit ? (
                correctName(curNoteType)
              ) : notetype === 'sql' ? (
                <Select
                  size="small"
                  onChange={this.onTypeChange}
                  placeholder="请选择脚本类型"
                  style={{ width: 100 }}
                  // disabled={basicDisabled}
                  disabled={isSubmitted}
                  value={curNoteType}
                >
                  {optionLanguages
                    .filter((item) => item.key.includes('sql'))
                    .map((item) => {
                      return (
                        <Option
                          key={item.key}
                          value={item.key}
                          label={item.value}
                        >
                          {item.value}
                        </Option>
                      );
                    })}
                </Select>
              ) : (
                correctName(curNoteType)
              )}
            </div>
            {showData && (
              <div>
                <span>数据库：</span>
                {canEdit ? (
                  <DataBaseTree
                    value={defaultSql}
                    size="small"
                    placeholder="请选择数据库"
                    engineType={curNoteType}
                    onSelect={this.onSelectDataBase}
                    // disabled={basicDisabled}
                    disabled={isSubmitted}
                    style={{ width: '150px' }}
                  />
                ) : (
                  dbname
                )}

                {/* <CascaderMenu
                  detValue={
                    enginename === ''
                      ? undefined
                      : [enginename, sqlname, initdb]
                  }
                  placeholder="请选择数据库"
                  onChange={this.onChangeCascaderMenu}
                  disabled={!canEdit || isLock}
                  size="small"
                /> */}
              </div>
            )}
            <div>
              <span>代码风格：</span>
              <Select
                defaultValue={theme}
                style={{ width: 120 }}
                size="small"
                onChange={this.hoverSelect}
              >
                {themes.map((item) => {
                  return <Option key={item}>{item}</Option>;
                })}
              </Select>
            </div>
          </div>
          {/* </Row> */}
        </div>
        {/* TODO 编辑区 */}
        <ResizeObserver
          onResize={({ height }) => {
            this.setState({
              aceHeight: height,
            });
          }}
        >
          <div className={styles.aceContainer}>
            <Spin
              tip={contentLoading ? '加载中' : '运行中...'}
              spinning={isLoadingRunScript || !!contentLoading}
              delay={0}
              size="large"
            >
              <div
                style={{ display, height: '100%', position: 'relative' }}
                onKeyDown={handleScriptKeyDown}
                tabIndex={0}
                ref="ace_container"
              >
                {/* {console.log(language, 'language')} */}
                <AceEditor
                  mode={this.getACEmode()}
                  theme={theme}
                  name="scriptDevelop"
                  ref="aceEditorArea"
                  className="scriptDevelopEditor"
                  fontSize={15}
                  readOnly={!ban || isLock || !canEdit || isSubmitted}
                  // readOnly={!ban || isLock || !identify} // deprecated
                  showPrintMargin={false}
                  showGutter
                  highlightActiveLine
                  onLoad={this.onLoad}
                  onChange={this.onChange}
                  debounceChangePeriod={delaySaveTime}
                  value={content}
                  height={`${this.state.aceHeight}px`}
                  width="calc(100% - 36px)"
                  style={{ zIndex: 0, position: 'absolute' }}
                  setOptions={{
                    enableBasicAutocompletion: true,
                    enableLiveAutocompletion: true,
                    enableSnippets: true,
                    showLineNumbers: true,
                    tabSize: 2,
                  }}
                />
                {/* TODO 参数设置 */}
                {!isKettle && <ParamSet />}
                {/* <div className={styles.detail} hidden={isKettle}>
                <Popover
                  content={tips}
                  placement="left"
                  title="提示"
                  trigger="hover"
                  defaultVisible={!window.localStorage.getItem('workdevscr')}
                >
                  <IconFont type="icon-question-circle" />
                </Popover>
              </div> */}
              </div>
            </Spin>
          </div>
        </ResizeObserver>
        {renderAlert && renderAlert(this.onSave)}
        <AutoSave id={id} save={this.autoSave} isEdit={isEdit} />
      </EditorResult>
    );
  }
}
