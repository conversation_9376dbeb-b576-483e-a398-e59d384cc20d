/* eslint-disable no-shadow */
/* eslint-disable brace-style */
/* eslint-disable object-shorthand */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import {
  IconFont,
  LayoutHelper as Layout,
  EditTabs as Tabs,
} from '@/components';
import { getIcon } from '@/pages/DataAnalysis/Development/sqlUtils';
import { PaneProps } from './interface';
import styles from '../scriptDevelop.less';
import styless from '../editbody.less';

const { TabPane } = Tabs;
const { Content, InnerBox } = Layout;

const ScriptPane = connect<{}, {}, {}, { develop; editbody; mgm; loading }>(
  ({ develop, editbody, mgm, loading }) => ({
    develop,
    editbody,
    mgm,
    deleteLoading: loading.effects['develop/deleteScript'],
    fileExportLoading: loading.effects['develop/exportFile'],
  }),
)((props: PaneProps) => {
  const {
    // 下传的states
    // listall,
    selectDir,
    fullscreen,
    classDiv,
    newActiveKey,

    dispatch,
    develop: { names },
    renderEditBody,
    renderTable,
    panes,
    panesSplit,
    saveArr,
    setFaState,
  } = props;
  const [activeKey, setActiveKey] = useState<string>(
    panes[panes.length - 1]?.key || 'home',
  );
  const [activeKeySplit, setActiveKeySplit] = useState<string>('');
  const [isCancelSplit, setIsCancelSplit] = useState<boolean>(false); // TODO 是否是取消分屏操作，可优化？

  useEffect(() => {
    if (!isCancelSplit) {
      // 非取消分屏操作，激活面板为最新面板
      // console.log(panes, 'newPane');
      setActiveKey(panes[panes.length - 1]?.key);
    } else {
      // 取消分屏操作：激活面板为table，不另外设置激活面板
      setIsCancelSplit(false);
    }
  }, [panes.length]);
  useEffect(() => {
    if (activeKeySplit !== newActiveKey && newActiveKey !== '') {
      setActiveKey(newActiveKey);
    }
  }, [newActiveKey]);
  useEffect(() => {
    if (activeKey && activeKey !== 'home') {
      const activePane = panes.find((pane) => pane.key === activeKey);
      // console.log(activeKey, 'activeKey');
      dispatch({
        type: 'develop/paramsFetch',
        payload: {
          id: activePane?.isHistory
            ? activePane?.scriptDetail?.devid
            : activeKey,
        },
      });
      if (!activePane?.isHistory) {
        dispatch({
          type: 'editbody/fetchContent',
          payload: {
            id: activeKey,
            names,
          },
        });
      }
    }
  }, [activeKey]);

  const screenStyle: React.CSSProperties | undefined = panesSplit.length
    ? {
        height: '100%',
        width: '50%',
        float: 'left',
        borderRight: '1px solid #eee',
        boxSizing: 'border-box',
      }
    : undefined;

  const operations = (
    <div className={styles.screenOperation}>
      {(panes.length > 1 || (panes.length >= 1 && panesSplit.length >= 1)) &&
        !fullscreen && (
          <span onClick={toggleScreenSplit}>
            {activeKey === 'home' ? (
              ''
            ) : panesSplit.length ? (
              <IconFont type="icon-single-screen" text="单屏" />
            ) : (
              <IconFont type="icon-double-screen" text="分屏" />
            )}
          </span>
        )}
    </div>
  );

  function toggleScreenSplit() {
    // setIsScreenSplit(!isScreenSplit);
    if (panesSplit.length) {
      // 取消分屏
      setFaState({
        panes: [...panes, ...panesSplit],
        panesSplit: [],
      });
    } else {
      // 根据当前键分屏
      const newPanesSplit = panes.find((item) => item.key === activeKey) || {};
      // console.log({ panesSplit });
      setActiveKeySplit(activeKey);
      setFaState({
        panes: panes.filter((item) => item.key !== activeKey),
        panesSplit: [newPanesSplit],
      });
    }
  }

  /**
   * 设置树的选中节点
   * @param {string | undefined} paneKey 选中的tab页key值
   * @param {boolean} isCloseAll 是否来自关闭全部
   */
  function setSelectDir(paneKey, isCloseAll = false) {
    // console.log({ panes });
    if (!paneKey || paneKey === 'home' || isCloseAll) {
      // 关闭最后一个面板 || 不关闭面板回归主面板 || 关闭全部面板
      if (isCloseAll) {
        const activePane = panes.find((pane) => pane.key === activeKey);
        if (!activePane) {
          // 找不到匹配的激活面板，当前激活者为主表格，无需设置树节点、设置搜索
          return;
        }
      }
      setFaState({
        selectedKeys: [selectDir],
      });
    } else {
      setFaState({
        selectedKeys: [paneKey],
      });
    }
  }

  function resetPanes(targetKey) {
    const newSave = saveArr.filter((item) => item.key !== targetKey);
    if (panes.filter((item) => item.key !== targetKey).length === 0) {
      // 没有主面板了，分屏面板变为主面板
      setActiveKey((panesSplit[0] && panesSplit[0].key) || '');
      setActiveKeySplit('');
      setSelectDir(panesSplit[0] && panesSplit[0].key); // 改变树的选中状态
      setFaState({
        fullscreen: panesSplit.length === 0 ? false : fullscreen,
        classDiv: panesSplit.length === 0 ? styles.notFullScreenDiv : classDiv,
        panes: panesSplit,
        panesSplit: [],
        saveArr: newSave,
      });
      return;
    }
    const newPane = panes.filter((item) => item.key !== targetKey);
    setSelectDir(newPane[newPane.length - 1].key); // 改变树的选中状态
    setFaState({
      panes: newPane,
      panesSplit: panesSplit.filter((item) => item.key !== targetKey),
      saveArr: newSave,
    });
  }

  // 删除编辑脚本页签时的回调
  function onEditTab(targetKey) {
    dispatch({
      type: 'editbody/cleanLog',
      payload: {
        id: targetKey,
      },
    });
    resetPanes(targetKey);
  }
  function handleCancelSplit() {
    setActiveKey('home'); // 主面板激活为table
    setActiveKeySplit(''); // 分屏面板还原
    setSelectDir('home'); // 重置树的选中状态
    setIsCancelSplit(true);
    // setModalVisible(false);
    setFaState({
      panes: [...panes, ...panesSplit],
      panesSplit: [],
      // 当在全屏界面返回主界面时，默认退出全屏
      fullscreen: false,
      classDiv: `${styles.notFullScreenDiv} ${styles.notSliptScreenWidthAuto}`,
    });
  }
  // 切换面板
  function onChangeTab1(value) {
    if (value === 'home') {
      if (panesSplit.length) {
        handleCancelSplit();
        // setModalVisible(true);
        return;
      }
      setFaState({
        // 当在全屏界面返回主界面时，默认退出全屏
        fullscreen: false,
        classDiv: `${styles.notFullScreenDiv} ${styles.notSliptScreenWidthAuto}`,
      });
    }
    setSelectDir(value); // 重置树的选中状态
    setActiveKey(value);
  }
  function onChangeTab2(value) {
    setSelectDir(value);
    setActiveKeySplit(value);
  }

  return (
    <InnerBox className={styless.splitEditContainer}>
      <Tabs
        style={{ ...screenStyle, height: '100%' }}
        className={styless.originScreenTab}
        onChange={onChangeTab1}
        activeKey={activeKey}
        type="editable-card"
        onClose={onEditTab}
        // toggleFullScreen
        tabBarExtraContent={panesSplit.length ? null : operations}
        homeTab={
          <TabPane>
            <Layout style={{ height: '100%' }}>
              <Content>{renderTable()}</Content>
            </Layout>
          </TabPane>
        }
      >
        {panes.map((pane) => (
          <TabPane
            tab={pane.title}
            key={pane.key}
            icon={<IconFont type={getIcon(pane.notetype)} />}
            isEdit={!saveArr.filter((save) => save.key === pane.key)[0].save}
          >
            {(renderAlert) => renderEditBody(pane, renderAlert)}
          </TabPane>
        ))}
      </Tabs>
      {/* 分屏 */}
      {panesSplit.length ? (
        <Tabs
          style={screenStyle}
          className="splitScreenTab"
          onChange={onChangeTab2}
          activeKey={activeKeySplit}
          type="editable-card"
          onClose={onEditTab}
          // toggleFullScreen
          tabBarExtraContent={operations}
        >
          {panesSplit.map((pane) => (
            <TabPane
              tab={pane.title}
              key={pane.key}
              icon={<IconFont type={getIcon(pane.notetype)} />}
              isEdit={!saveArr.filter((save) => save.key === pane.key)[0].save}
            >
              {(renderAlert) => renderEditBody(pane, renderAlert)}
            </TabPane>
          ))}
        </Tabs>
      ) : (
        ''
      )}
    </InnerBox>
  );
});

export default ScriptPane;
