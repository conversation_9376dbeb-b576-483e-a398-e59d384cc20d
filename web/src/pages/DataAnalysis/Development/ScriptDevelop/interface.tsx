type selectedRow = {
  id: string;
  dirname: string;
  dirid: string;
  notename: string;
  notetype: string;
  username: string;
  identify: boolean;
};
type save = {
  key: string;
  save: boolean;
};
type pane = {
  key: string;
  ban: boolean;
  disabled: boolean;
  notetype: string;
  star: boolean;
  title: string;
  submitted: boolean;
  dirid: string;
  runResult: any;
};

interface faStates {
  listall: boolean;
  showAll: boolean;
  filterTitle: string;
  selectDir: string;
  selectedRows: Array<selectedRow>;
  selectedRowKeys: Array<string>;
  isReal: boolean;
  sortMenu: Array<string>;
  classDiv: string;
  fullscreen: boolean;
  newActiveKey: string;
}

export interface PaneProps extends faStates {
  dispatch: any;
  faStates: faStates;
  develop: { names: { scriptName: string; noteName: string }[] };
  editbody: any;
  newActiveKey: string;
  fullscreen: boolean;
  panes: Array<pane>;
  panesSplit: Array<pane>;
  saveArr: Array<save>;
  setFaState: any;
  renderEditBody;
  renderTable;
}

export interface TableProps extends faStates {
  dispatch: any;
  tableLoading: boolean;
  panes: Array<pane>;
  develop: { total; list; modelRecord; dirlist; tags; workflowList };
  user: { canCurrentPageEdit: boolean };
  faStates: faStates;
  deleteLoading: boolean;
  fileExportLoading: boolean;
  freshAll: (form?: any) => void;
  setFaState: (obj, callback?) => void;
  getlistDir: () => void;
  gotoEditBody: (record) => void;
  inputSelect;
  deleteScript: (e, ids) => void;
  refresh: () => void;
  gotoHistoryScript: (record) => void;
}
