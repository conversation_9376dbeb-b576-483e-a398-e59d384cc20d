import React, { useState, useEffect } from 'react';
import { Modal, Row, Col, Spin } from 'antd';
import { Ellipsis } from '@dana/ui';
import AceEditor from 'react-ace';
import CS from 'classnames';
import { useRequest, correctName } from '@/utils';
import { mockSQL } from '@/pages/DataAnalysis/Development/sqlUtils';
import commonStyles from '@/components/VersionCompareSelect/versionCompare.less';

import styles from './compareModal.less';
import ParamSet from '../../include/SideBar';

// import { leftDataSource, rightDataSource } from './mockData';

const CompareModal = (props) => {
  const { visible, selectedRows, isHistory } = props;
  const [leftContent, setLeftContent] = useState({});
  const [rightContent, setRightContent] = useState({});
  const [leftRecord, setLeftRecord] = useState({});
  const [rightRecord, setRightRecord] = useState({});
  const [leftMarkers, setLeftMarkers] = useState([]);
  const [rightMarkers, setRightMarkers] = useState([]);

  const { run: getScript1Data, loading: getData1Loading } = useRequest(
    'ants/develop/content',
  );
  const { run: getScript2Data, loading: getData2Loading } = useRequest(
    'ants/develop/content',
  );

  useEffect(() => {
    if (visible) {
      getCompareData();
    }
  }, [visible]);

  useEffect(() => {
    const leftPart = leftContent.content?.split('\n') || [];
    const rightPart = rightContent.content?.split('\n') || [];
    const newRightMarkers: any[] = [];
    const newLeftMarkers: any[] = [];

    // 右侧高亮展示新增/修改的行
    rightPart.forEach((text, index) => {
      if (!leftPart.includes(text) || leftPart[index] !== text) {
        newRightMarkers.push({
          startRow: index,
          endRow: index,
          startCol: 0,
          endCol: 1, // 关键：设置为1而不是0，确保空白行也能高亮
          className: 'modify-highlight',
          type: 'text',
        });
      }
    });
    setRightMarkers(newRightMarkers);

    // 左侧高亮展示删除的行
    // if (leftPart.length > rightPart.length) {
    //   const delIndexList = new Array(leftPart.length - rightPart.length).fill(
    //     'x',
    //   );
    //   delIndexList.forEach((_, index) => {
    //     const i = rightPart.length + index;
    //     newLeftMarkers.push({
    //       startRow: i,
    //       endRow: i,
    //       startCol: 0,
    //       endCol: 1, // 关键：设置为1而不是0，确保空白行也能高亮
    //       className: 'delete-highlight',
    //       type: 'text',
    //     });
    //   });
    // }

    // console.log('leftPart', leftPart);
    // console.log('rightPart', rightPart);
    leftPart.forEach((text, index) => {
      if (!rightPart.includes(text)) {
        newLeftMarkers.push({
          startRow: index,
          endRow: index,
          startCol: 0,
          endCol: 1, // 关键：设置为1而不是0，确保空白行也能高亮
          className: 'delete-highlight',
          type: 'text',
        });
      }
    });

    setLeftMarkers(newLeftMarkers);
  }, [leftContent.content, rightContent.content]);

  function getCompareData() {
    const time1 = selectedRows[0].updatetime;
    const time2 = selectedRows[1].updatetime;

    // 将字符串时间转换为 Date 对象
    const date1 = time1 ? new Date(time1?.replace(/-/g, '/')) : new Date();
    const date2 = time2 ? new Date(time2?.replace(/-/g, '/')) : new Date();

    // 获取时间戳
    const timestamp1 = date1.getTime();
    const timestamp2 = date2.getTime();

    const isTime2Later = timestamp2 > timestamp1;

    const leftRow = isTime2Later ? selectedRows[0] : selectedRows[1];
    const rightRow = isTime2Later ? selectedRows[1] : selectedRows[0];

    setLeftRecord(leftRow);
    setRightRecord(rightRow);

    if (!leftRow.isHistory) {
      getScript1Data({ id: leftRow.id }).then(({ result }) => {
        const { enginename = '', actualnotetype } = result;
        let curNoteType = actualnotetype;
        // 这里处理从模型管理过来的sql脚本，没有actualnotetype，但是有enginename
        if (!actualnotetype && enginename) {
          curNoteType = mockSQL(enginename, correctName(leftRow.notetype));
        }
        setLeftContent({
          ...result,
          curNoteType: curNoteType || leftRow.notetype,
          dbname: result.sourcedbname,
          showData: leftRow.notetype === 'sql',
          isKettle: leftRow.notetype?.indexOf('-') !== -1,
        });
      });
    } else {
      const scriptDetail = leftRow?.scriptDetail || {};
      const { notetype, actualnotetype, sourcedbname } = scriptDetail;
      const curNoteType = actualnotetype;
      setLeftContent({
        ...scriptDetail,
        curNoteType: curNoteType || notetype,
        dbname: sourcedbname,
        showData: notetype === 'sql',
        isKettle: notetype?.indexOf('-') !== -1,
      });
    }

    if (!rightRow.isHistory) {
      getScript2Data({ id: rightRow.id }).then(({ result }) => {
        const { enginename = '', actualnotetype } = result;
        let curNoteType = actualnotetype;
        // 这里处理从模型管理过来的sql脚本，没有actualnotetype，但是有enginename
        if (!actualnotetype && enginename) {
          curNoteType = mockSQL(enginename, correctName(rightRow.notetype));
        }

        setRightContent({
          ...result,
          curNoteType: curNoteType || rightRow.notetype,
          dbname: result.sourcedbname,
          showData: rightRow.notetype === 'sql',
          isKettle: rightRow.notetype?.indexOf('-') !== -1,
        });
      });
    } else {
      const scriptDetail = rightRow?.scriptDetail || {};
      const { notetype, actualnotetype, sourcedbname } = scriptDetail;
      const curNoteType = actualnotetype;
      setRightContent({
        ...scriptDetail,
        curNoteType: curNoteType || notetype,
        dbname: sourcedbname,
        showData: notetype === 'sql',
        isKettle: notetype?.indexOf('-') !== -1,
      });
    }
  }

  const getACEmode = (notetype) => {
    switch (notetype) {
      case 'shell':
        return 'sh';
      case 'python3':
        return 'python';
      default:
        return notetype;
    }
  };

  const theme = 'github';

  const paramSetContainer = document.querySelectorAll('.ant-modal-body')?.[0];
  const paramSetMaxHeight = 647;

  const handleCancel = () => {
    setLeftRecord({});
    setRightRecord({});
    setLeftContent({});
    setRightContent({});
    props.handleCancel();
  };
  return (
    <Modal
      title={isHistory ? '版本对比' : '脚本对比'}
      visible={visible}
      onOk={props.handleCancel}
      onCancel={handleCancel}
      className={styles.compareModal}
      width={1280}
      footer={null}
    >
      <Row className={styles.container} id="compareModal1">
        <Col span={12}>
          <div
            className={CS({
              [commonStyles.leftVersionName]: true,
              [styles.tools]: true,
            })}
          >
            <Ellipsis
              className={CS({
                [commonStyles.versionName]: true,
                [styles.left]: true,
              })}
              lines={1}
              tooltip
              style={{ maxWidth: 350 }}
            >
              {leftRecord.notename}
            </Ellipsis>
            <div className={styles.rightButton}>
              {leftRecord.notetype?.includes('python') && (
                <div>
                  <span>运行内存：</span>
                  <span>
                    {leftContent.memorylimit
                      ? `${leftContent.memorylimit}G`
                      : '无限制'}
                  </span>
                </div>
              )}
              <div>
                <span>脚本类型：</span>
                {correctName(leftContent.curNoteType)}
              </div>
              {leftContent.showData && (
                <div>
                  <span>数据库：</span>
                  {leftContent.dbname}
                </div>
              )}
              {/* <div>
                    <span>代码风格：</span>
                    <Select
                      defaultValue={theme}
                      style={{ width: 120 }}
                      size="small"
                      onChange={this.hoverSelect}
                    >
                      {themes.map((item) => {
                        return <Option key={item}>{item}</Option>;
                      })}
                    </Select>
                  </div> */}
            </div>
          </div>
          {/* TODO 编辑区 */}
          <div className={styles.aceContainer}>
            <Spin
              tip="加载中"
              spinning={getData1Loading}
              delay={0}
              size="large"
            >
              <div
                style={{
                  display: 'flex',
                  height: '100%',
                  position: 'relative',
                }}
              >
                <AceEditor
                  mode={getACEmode(leftRecord.notetype)}
                  theme={theme}
                  name="scriptDevelop"
                  className="scriptDevelopEditor"
                  fontSize={15}
                  readOnly
                  showPrintMargin={false}
                  showGutter
                  highlightActiveLine={false}
                  value={leftContent.content}
                  height="672px"
                  width="calc(100% - 36px)"
                  style={{ zIndex: 0, position: 'absolute' }}
                  setOptions={{
                    enableBasicAutocompletion: true,
                    enableLiveAutocompletion: true,
                    enableSnippets: true,
                    showLineNumbers: true,
                    tabSize: 2,
                  }}
                  markers={leftMarkers}
                />
                {/* TODO 参数设置 */}
                {!leftContent.isKettle && (
                  <ParamSet
                    popupContainer={paramSetContainer}
                    maxHeight={paramSetMaxHeight}
                  />
                )}
              </div>
            </Spin>
          </div>
        </Col>
        <Col span={12}>
          <div
            className={CS({
              [commonStyles.rightVersionName]: true,
              [styles.tools]: true,
            })}
          >
            <Ellipsis
              className={CS({
                [commonStyles.versionName]: true,
                [styles.left]: true,
              })}
              lines={1}
              tooltip
              style={{ maxWidth: 350 }}
            >
              {rightRecord.notename}
            </Ellipsis>
            <div className={styles.rightButton}>
              {rightRecord.notetype?.includes('python') && (
                <div>
                  <span>运行内存：</span>
                  <span>
                    {rightContent.memorylimit
                      ? `${rightContent.memorylimit}G`
                      : '无限制'}
                  </span>
                </div>
              )}
              <div>
                <span>脚本类型：</span>
                {correctName(rightContent.curNoteType)}
              </div>
              {rightContent.showData && (
                <div>
                  <span>数据库：</span>
                  {rightContent.dbname}
                </div>
              )}
              {/* <div>
                    <span>代码风格：</span>
                    <Select
                      defaultValue={theme}
                      style={{ width: 120 }}
                      size="small"
                      onChange={this.hoverSelect}
                    >
                      {themes.map((item) => {
                        return <Option key={item}>{item}</Option>;
                      })}
                    </Select>
                  </div> */}
            </div>
          </div>
          <div className={styles.aceContainer}>
            <Spin
              tip="加载中"
              spinning={getData2Loading}
              delay={0}
              size="large"
            >
              <div
                style={{
                  display: 'flex',
                  height: '100%',
                  position: 'relative',
                }}
              >
                <AceEditor
                  mode={getACEmode(rightRecord.notetype)}
                  theme={theme}
                  name="scriptDevelop2"
                  // ref="aceEditorArea"
                  className="scriptDevelopEditor"
                  fontSize={15}
                  readOnly
                  showPrintMargin={false}
                  showGutter
                  highlightActiveLine={false}
                  value={rightContent.content}
                  height="672px"
                  width="calc(100% - 36px)"
                  style={{ zIndex: 0, position: 'absolute' }}
                  setOptions={{
                    enableBasicAutocompletion: true,
                    enableLiveAutocompletion: true,
                    enableSnippets: true,
                    showLineNumbers: true,
                    tabSize: 2,
                  }}
                  markers={rightMarkers}
                />
                {/* TODO 参数设置 */}
                {!rightContent.isKettle && (
                  <ParamSet
                    popupContainer={paramSetContainer}
                    maxHeight={paramSetMaxHeight}
                  />
                )}
              </div>
            </Spin>
          </div>
        </Col>
      </Row>
    </Modal>
  );
};

export default CompareModal;
