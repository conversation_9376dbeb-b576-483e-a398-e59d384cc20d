import { memo, useCallback, useMemo, useState } from 'react';
import { useSelector } from 'umi';
import { Form } from 'antd';
import { ConnectState } from '@/models/connect.d';
import { ContentModal, Table, VersionCompareSelect } from '@/components';
import { formatDate } from '@/utils';
import CompareModal from './CompareModal';

type HistoryVersionProps = {
  // tableInfo?: any;
  visible: boolean;
  onCancel: () => void;
  gotoHistoryScript: (r: any) => void;
};

const HistoryVersion = (props: HistoryVersionProps) => {
  const [form] = Form.useForm();
  const { visible, onCancel, gotoHistoryScript } = props;
  const { curHistoryList, curVersion } = useSelector(
    (state: ConnectState) => state.develop,
  );
  // loading
  const loading = useSelector(
    (state) => state.loading.effects['develop/getScriptVersion'],
  );
  const [versionCompareMdVisible, setVersionCompareMdVisible] = useState(false);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const showCurHistoryPane = useCallback(
    (record) => {
      onCancel();
      gotoHistoryScript({ ...record });
    },
    [onCancel],
  );

  const curVersion1 = [
    {
      name: '当前版本',
      id: curVersion?.id,
      notename: '当前版本',
      updatetime: curVersion?.updatetime,
      notetype: curVersion?.notetype,
    },
  ];
  const versionList = curVersion1.concat(
    curHistoryList.map((v) => {
      return {
        name: `版本${v.version}`,
        notename: `版本${v.version}`,
        time: v.creatTime,
        id: v.id,
        updatetime: v.generatetime,
        isHistory: true,
        scriptDetail: v,
      };
    }),
  );

  const handleCompare = () => {
    setVersionCompareMdVisible(true);
    const leftVersion = form.getFieldValue('leftVersionName');
    const rightVersion = form.getFieldValue('rightVersionName');
    const rows = versionList.filter(
      (item) => item.name === leftVersion || item.name === rightVersion,
    );
    setSelectedRows(rows);
  };

  const columns = useMemo(
    () => [
      {
        title: '版本号',
        dataIndex: 'version',
        render(text, record) {
          return (
            <a onClick={() => showCurHistoryPane(record)}>
              <span>
                版本
                {text}
              </span>
            </a>
          );
        },
      },
      {
        title: '脚本名称',
        dataIndex: 'notename',
        ellipsis: true,
      },
      {
        title: '描述',
        dataIndex: 'description',
        ellipsis: true,
      },

      {
        title: '创建时间',
        dataIndex: 'generatetime',
        sorter: true,
        render(text) {
          return formatDate(text);
        },
      },
    ],
    [showCurHistoryPane],
  );

  const handleCancelModal = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <ContentModal
      loading={loading}
      modalSize="middle"
      title="历史版本"
      footer={null}
      visible={visible}
      onCancel={handleCancelModal}
    >
      <Form form={form}>
        <div style={{ marginBottom: 12 }}>
          <VersionCompareSelect
            handleCompare={handleCompare}
            optionList={versionList.map((v) => v.name)}
            form={form}
          />
        </div>
        <Table
          style={{ height: 480 }}
          pagination={false}
          columns={columns}
          dataSource={curHistoryList}
        />

        <CompareModal
          isHistory
          visible={versionCompareMdVisible}
          selectedRows={selectedRows}
          handleCancel={() => setVersionCompareMdVisible(false)}
        />
      </Form>
    </ContentModal>
  );
};

export default memo(HistoryVersion);
