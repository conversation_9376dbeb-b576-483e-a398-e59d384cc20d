/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-shadow */
/* eslint-disable brace-style */
/* eslint-disable object-shorthand */
import React, { useCallback, useState } from 'react';
import { Divider, notification, Modal, Dropdown, Menu } from 'antd';
import { connect } from 'dva';
// import Ellipsis from 'ant-design-pro/lib/Ellipsis';
import { ColumnProps } from 'antd/lib/table/Column';
import {
  RuleProvider,
  Ellipsis,
  IconFont,
  Table,
  LayoutHelper as Layout,
  TextButton,
  TagsAdd as TagAdd,
} from '@dana/ui';
import { formatDate, correctName } from '@/utils';
import { DeleteForm, CreateForm, CopyScriptForm } from './Modals';
import { TableProps } from './interface';
import { getIcon } from '../sqlUtils';
import TableHeader from './TableHeader';
import styles from '../scriptDevelop.less';
import HistoryVersionModal from './Modals/HistoryVersion';

const { StrechBox, InnerBox, HeightContext } = Layout;

const noteTypes = [
  'shell',
  'python',
  'python3',
  'php',
  'pgsql',
  'hivesql',
  'kettle-trans',
  'kettle-job',
];

const ScriptTable = connect<{}, {}, {}, { develop; loading; user }>(
  ({ develop, user, loading }) => ({
    develop,
    user,
    tableLoading: loading.effects['develop/searchList'],
    deleteLoading: loading.effects['develop/deleteScript'],
    fileExportLoading: loading.effects['develop/exportFile'],
  }),
)((props: TableProps) => {
  const {
    // 下传的states
    filterTitle,
    panes,
    // listall,
    selectedRows,
    selectedRowKeys,
    isReal,
    selectDir,

    dispatch,
    tableLoading,
    develop: { list, modelRecord, dirlist },
    user: { canCurrentPageEdit: canEdit },

    freshAll,
    setFaState,
    getlistDir,
    gotoEditBody,
    gotoHistoryScript,
  } = props;

  // const [modelDelete, setModelDelete] = useState([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalName, setModalName] = useState<string>('');
  const [ban, setBan] = useState<boolean>(false);
  const [record, setRecord] = useState<any>({});
  const [historyVisible, setHistoryVisible] = useState(false);

  // const [selectedKeys, setSelectedKeys] = useState<Array<string>>([]);

  // useEffect(() => {
  //   bindK();
  // }, [root, expandedKeys]);

  function getDeData(data) {
    const arr: Array<any> = [];
    data.forEach((item) => {
      arr.push({
        ...item,
        isSkip: !item.ban,
        // isSkip: item.dags && item.dags.length !== 0,
      });
    });
    return arr;
  }

  function freshAfterDelete() {
    getlistDir();
    setFaState({
      selectedRowKeys: [],
      selectedRows: [],
    });
    dispatch({
      type: 'develop/searchList',
      payload: {
        listall: true,
        selectDir: selectDir === 'listAll' ? '' : selectDir,
        search: filterTitle,
      },
    });
  }
  function deleteScript(e, ids) {
    const paneIds = panes.map((pane) => pane.key);
    let isScriptEdit = false;
    ids.forEach((id) => {
      if (paneIds.includes(id)) {
        isScriptEdit = true;
      }
    });
    if (isScriptEdit) {
      Modal.info({
        title: '删除提醒',
        content: '存在任务正在被查看\\编辑，请先关闭标签！',
      });
      return;
    }
    dispatch({
      type: 'develop/deleteScript',
      payload: {
        ids: ids,
        isdel: true,
      },
    }).then((data) => {
      freshAfterDelete();
      if (data === 'successN') {
        notification.success({
          message: '删除脚本成功',
        });
      }
      if (data === 'successY') {
        setModalVisible(true);
        setModalName('Delete');
      }
    });
  }
  function detailScript(id, banValue) {
    dispatch({
      type: 'develop/detailFetch',
      payload: {
        id,
      },
    }).then(() => {
      // const { modelRecord } = props.develop;
      setModalName('Detail');
      setModalVisible(true);
      setBan(ban);
      setFaState({
        // modalVisible: true,
        // modelName: 'detail',
        // modelRecord: { ...modelRecord },
        // 获取到当前脚本是否可被编辑，重置state
        downBan: banValue,
      });
    });
  }
  function handleCopy(recordVal) {
    setModalName('Copy');
    setModalVisible(true);
    setRecord(recordVal);
    // setFaState({
    //   modelRecord: record,
    // });
  }

  function onSelectChange(selectedRowKeys, selectedRows) {
    // console.log(selectedRowKeys, selectedRows, '选中行');
    setFaState({
      selectedRowKeys,
      selectedRows,
    });
  }

  const showHistory = useCallback((record) => {
    dispatch({
      type: 'develop/getHistoryVersion',
      payload: {
        id: record.id,
      },
    });
    dispatch({
      type: 'develop/save',
      payload: {
        curVersion: record,
      },
    });
    setHistoryVisible(true);
  }, []);

  const getMenu = (item) => {
    const { ban, id } = item;
    return (
      <Menu>
        {canEdit && (
          <Menu.Item
            disabled={
              // !identify ||
              !ban || panes.map((pane) => pane.key).includes(id)
            }
          >
            <RuleProvider
              data={getDeData([item])}
              nameAtt="notename"
              skipAtt="isSkip"
              nameStr="脚本文件"
              errMessage="正在被工作流使用，无法移动和删除"
              onClick={deleteScript}
              disabled={!ban || panes.map((pane) => pane.key).includes(id)}
            >
              <div>删除</div>
            </RuleProvider>
          </Menu.Item>
        )}
        <Menu.Item onClick={() => showHistory(item)}>历史版本</Menu.Item>
      </Menu>
    );
  };

  const columns: ColumnProps<any>[] = [
    {
      title: '脚本名称',
      // className: styles.nopaddingleftcolumns,
      dataIndex: isReal ? 'scriptname' : 'notename',
      // sorter: true,
      // width: 120,
      render: (val, record) => {
        const { notetype } = record;
        let type = notetype;
        type = type.includes('kett') ? 'kettle' : type;
        const icon = getIcon(type);
        return (
          <TextButton onClick={() => gotoEditBody(record)}>
            <Ellipsis tooltip lines={1} isResize>
              <IconFont
                type={icon}
                style={{
                  fontSize: 16,
                  marginRight: 5,
                }}
              />
              {val}
            </Ellipsis>
          </TextButton>
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'notetype',
      width: 80,
      // filterMultiple: false,
      filters: noteTypes.map((type) => {
        return {
          text: correctName(type),
          value: type,
        };
      }),
      onFilter: (val, record) =>
        (record.actualnotetype || record.notetype) === val,
      render: (val, record) => {
        return correctName(record.actualnotetype || val);
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: '20%',
      render: (val) => {
        const content = {
          tags: val && Array.isArray(val) ? val : [],
          isBoolen: true,
        };
        return val && val.length ? <TagAdd {...content} /> : '--';
      },
      // // mark to display a total number
      // needTotal: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      render: (val) => (
        <Ellipsis tooltip lines={1}>
          {val}
        </Ellipsis>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'datecreated',
      width: 130,
      sorter: true,
      render: (val) => formatDate(val),
    },
    {
      title: '更新时间',
      dataIndex: 'updatetime',
      width: 130,
      sorter: true,
      render: (val) => formatDate(val),
    },
    {
      title: '操作',
      align: 'center',
      width: 130,
      render: (record) => {
        const { id, ban } = record;
        // const { identify } = record;
        // console.log(record)
        return (
          <>
            <TextButton onClick={() => detailScript(id, ban)}>详情</TextButton>
            {/* {identify ? (
              <a onClick={() => detailScript(id, ban)}>详情</a>
            ) : (
              <>
                <Popover
                  content="您无权限修改，只能查看!"
                  placement="top"
                  trigger="hover"
                >
                  <TextButton onClick={() => detailScript(id, ban)}>
                    详情
                  </TextButton>
                </Popover>
              </>
            )} */}
            {canEdit && (
              <>
                <Divider type="vertical" />
                <TextButton onClick={() => handleCopy(record)}>复制</TextButton>
              </>
            )}
            <Divider type="vertical" />
            <Dropdown overlay={getMenu(record)} placement="bottomLeft">
              <TextButton>更多</TextButton>
            </Dropdown>
          </>
        );
      },
    },
  ];

  // 如果切换到“所有人”，则表格增加“所属者”一列
  // if (listall) {
  //   columns.splice(5, 0, {
  //     title: '所属者',
  //     dataIndex: 'username',
  //     // width: 80,
  //   });
  // }
  const rowSelection = {
    selectedRowKeys,
    selectedRows,
    onChange: onSelectChange,
    // getCheckboxProps: record => ({
    //     disabled: (record.dags.length > 0) || record.submitted,
    // }),
  };
  // console.log(dirlist, 'dirlist');
  return (
    // <div className={styles.tableList}>
    <>
      <InnerBox>
        <TableHeader {...props} deleteScript={deleteScript} />
      </InnerBox>
      {/* <div className={styles.tableListForm}>{renderForm()}</div> */}
      <StrechBox className={styles.tableList}>
        <HeightContext.Consumer>
          {(height) => {
            // console.log({ height });
            return (
              <Table
                loading={tableLoading}
                rowSelection={rowSelection}
                // @ts-ignore
                columns={columns}
                dataSource={list}
                rowKey="id"
                style={{ height }}
              />
            );
          }}
        </HeightContext.Consumer>
      </StrechBox>
      <DeleteForm
        // @ts-ignore
        deleteFresh={freshAfterDelete}
        handleModalVisible={setModalVisible}
        modalVisible={modalVisible && modalName === 'Delete'}
      />
      {/* {console.log({ modelRecord })} */}
      <CopyScriptForm
        // @ts-ignore
        freshAll={freshAll}
        handleModalVisible={setModalVisible}
        modelRecord={record}
        dirlist={dirlist}
        modalVisible={modalVisible && modalName === 'Copy'}
      />
      {modalVisible && modalName === 'Detail' && (
        <CreateForm
          // @ts-ignore
          freshAll={freshAll}
          handleModalVisible={setModalVisible}
          modelRecord={modelRecord}
          dirlist={dirlist}
          // allTags={tags}
          canEdit={canEdit}
          downBan={ban}
          modalVisible={modalVisible && modalName === 'Detail'}
        />
      )}
      <HistoryVersionModal
        onCancel={() => {
          setHistoryVisible(false);
        }}
        visible={historyVisible}
        gotoHistoryScript={gotoHistoryScript}
      />
    </>
    // </div>
  );
});

export default ScriptTable;
