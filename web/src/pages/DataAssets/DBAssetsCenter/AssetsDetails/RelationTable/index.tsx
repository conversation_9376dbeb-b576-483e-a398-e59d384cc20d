import { useRef, useEffect, useState } from 'react';
import { connect } from 'dva';
import { Select, Divider } from 'antd';
import { TextButton } from '@dana/ui';
import { ConnectState } from '@/models/connect.d';

import DagGraphCmp from './DagGraphCmp';
import styles from '../index.less';

const OPTIONS = [
  {
    label: '全链分析',
    value: 'all',
  },
  {
    label: '影响分析',
    value: 'next',
  },
  {
    label: '血缘分析',
    value: 'pre',
  },
];

const RelationGraph = (props: any) => {
  const childRef = useRef<any>(null);

  const [pres, setPres] = useState<number>(0);
  const [nexts, setNexts] = useState<number>(0);
  const [colla, setColla] = useState<boolean>(true);

  useEffect(() => {
    if (childRef.current) {
      childRef.current.onStateChange(({ collapse, counts }) => {
        setPres(counts.pres);
        setNexts(counts?.nexts);
        setColla(collapse);
      });
    }
  }, []);

  return (
    <div className={styles.relationTable}>
      <div className={styles.title}>
        <p style={{ color: '#1980ff' }}>血缘关系</p>
        <div className={styles.btnGroup}>
          <div className={styles.item}>
            <Select
              defaultValue={OPTIONS[0]}
              options={OPTIONS}
              bordered={false}
              onChange={(v) => childRef?.current?.handleDataSourceType?.(v)}
              style={{ width: 90 }}
            />
          </div>
          <Divider type="vertical" />
          <div className={styles.item}>
            <span>关联度分析：</span>
            <TextButton onClick={() => childRef?.current?.toggleNodeFields?.()}>
              {colla ? '开启' : '关闭'}
            </TextButton>
          </div>
          <Divider type="vertical" />
          <div className={styles.item}>
            上游表数：
            <i style={{ marginRight: 12 }}>{pres}</i>
            下游表数：
            <i>{nexts}</i>
          </div>
        </div>
      </div>

      <DagGraphCmp ref={childRef} {...props} />
    </div>
  );
};

export default connect(({ wMgm, loading }: ConnectState) => ({
  wMgm,
  loading,
  // listLoading: loading.effects['relationTable/listBlood'],
}))(RelationGraph);
