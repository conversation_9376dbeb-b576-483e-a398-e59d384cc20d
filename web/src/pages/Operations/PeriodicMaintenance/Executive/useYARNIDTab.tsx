import { Row, Tabs, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useRequest } from '@/utils';
import { IconFont } from '@dana/ui';
import styles from './index.less';

const { Text } = Typography;

const useYARNIDTab = (props) => {
  const { hiveSqlTasks = [], logType, recordid } = props;
  const [yarnids, setYarnids] = useState<
    { retryid: string; yarnids: string[] }[]
  >([]);

  const { run: getLog } = useRequest('dodox/job/record/log', {
    onSuccess: (res, params) => {
      if (res.code === 200) {
        setYarnids([
          { retryid: params?.[0]?.retryid, yarnids: res?.yarnids || [] },
        ]);
      }
    },
  });
  const panes = hiveSqlTasks.map((task) => {
    const curYarnids =
      yarnids.find((item) => item.retryid === task.retryid)?.yarnids || [];
    return {
      key: task.retryid,
      label: (
        <Text ellipsis={{ tooltip: task.name }}>
          <IconFont type="icon-sql" text={task.name} />
        </Text>
      ),
      children: (
        <div>
          {curYarnids
            ? curYarnids.map((item) => {
                return (
                  <Row key={item} style={{ marginBottom: 8 }}>
                    <Text ellipsis={{ tooltip: item }}>{item}</Text>
                  </Row>
                );
              })
            : '--'}
        </div>
      ),
    };
  });
  const [activeKey, setActiveKey] = useState(panes?.[0]?.key);
  useEffect(() => {
    setActiveKey(panes?.[0]?.key);
    setYarnids([]);
    const subid = hiveSqlTasks?.[0]?.subid;
    if (recordid) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      panes?.[0]?.key &&
        getLog({ recordid, retryid: panes?.[0]?.key, needyarn: true, subid });
    }
  }, [logType, recordid]);

  const tagChange = (e) => {
    setActiveKey(e);
    const subid = hiveSqlTasks.find((item) => item.retryid === e)?.subid;
    getLog({ recordid, retryid: e, subid, needyarn: true });
  };

  const yarnidTab = (
    <div className={styles.yarnidTab}>
      <div style={{ marginBottom: 12 }}>YARN任务id：</div>
      <Tabs
        onChange={(key) => {
          tagChange(key);
        }}
        activeKey={activeKey}
        tabPosition="left"
        items={panes}
      />
    </div>
  );

  return {
    yarnidTab,
  };
};
export default useYARNIDTab;
